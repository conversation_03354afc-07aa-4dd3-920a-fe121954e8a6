.ui-select-toggle {
  position: relative;
}

/* Fix caret going into new line in Firefox */
.ui-select-placeholder {
  float: left;
}

/* Fix Bootstrap dropdown position when inside a input-group */
.input-group {
  > .dropdown {
    position: static;
  }
}

.ui-select-match {
  > .btn {
    text-align: left !important;
  }

  > .caret {
    position: absolute;
    top: 45%;
    right: 15px;
  }
}

.ui-disabled {
  background-color: #eceeef;
  border-radius: 4px;
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 5;
  opacity: 0.6;
  top: 0;
  left: 0;
  cursor: not-allowed;
}

.ui-select-choices {
  width: 100%;
  height: auto;
  max-height: 200px;
  overflow-x: hidden;
  margin-top: 0;
}

.ui-select-multiple {
  .ui-select-choices {
    margin-top: 1px;
  }
}

.ui-select-choices-row {
  > a {
    display: block;
    padding: 3px 20px;
    clear: both;
    font-weight: 400;
    line-height: 1.42857143;
    color: #333;
    white-space: nowrap;
  }

  &.active {
    > a {
      color: #fff;
      text-decoration: none;
      outline: 0;
      background-color: #428bca;
    }
  }
}

.ui-select-multiple {
  height: auto;
  padding: 3px 3px 0 3px;

  input {
    &.ui-select-search {
      background-color: transparent !important; /* To prevent double background when disabled */
      border: none;
      outline: none;
      box-shadow: none;
      height: 1.6666em;
      padding: 0;
      margin-bottom: 3px;
    }
  }
}

.ui-select-match {
  .close {
    font-size: 1.6em;
    line-height: 0.75;
  }
}

.ui-select-multiple {
  .ui-select-match-item {
    outline: 0;
    margin: 0 3px 3px 0;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.ui-select-toggle {
  > .caret {
    position: absolute;
    height: 10px;
    top: 50%;
    right: 10px;
    margin-top: -2px;
  }
}
