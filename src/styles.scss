// @import '~@skywind-group/lib-swui/styles/themes/_theme.scss';
// @import '~@skywind-group/lib-swui/styles/fonts/fonts.scss';

.sw-grid-layout {
  display: flex;

  &__filter {
    flex-shrink: 0;
    flex-grow: 0;
    width: 0;
    overflow: hidden;
    transition: width 300ms ease;

    &.visible {
      width: 320px;
      padding-left: 32px;
    }
  }

  &__table {
    flex-grow: 1;
    width: calc(100% - 320px)
  }

  @media (max-width: 1024px) {
    flex-direction: column;

    &__filter {
      width: 0;
      padding-left: 0;
      transition: width 300ms ease;

      &.visible {
        width: 100%;
        padding-top: 32px;
      }
    }

    &__table {
      width: 100%;
    }
  }
}

.category {
  &__save-spin {
    svg {
      circle {
        stroke: #fff !important;
      }
    }

  }
}

.table-sticky {
  label {
    margin-bottom: 0 !important;
  }
}

.nowrap {
  white-space: nowrap;
}

.cdk-global-scrollblock {
  position: initial;
  overflow-y: hidden !important;
}

.sw-translations {
  .nav-tabs {
    margin-bottom: 0;
  }
}

@media print {

  *,
  *:before,
  *:after {
    background: transparent !important;
    color: #000 !important; // Black prints faster: h5bp.com/s
    box-shadow: none !important;
    text-shadow: none !important;
  }

  a,
  a:visited {
    text-decoration: underline;
  }

  a[href]:after {
    content: " (" attr(href) ")";
  }

  abbr[title]:after {
    content: " (" attr(title) ")";
  }

  // Don't show links that are fragment identifiers,
  // or use the `javascript:` pseudo protocol
  a[href^="#"]:after,
  a[href^="javascript:"]:after {
    content: "";
  }

  pre,
  blockquote {
    border: 1px solid #999;
    page-break-inside: avoid;
  }

  thead {
    display: table-header-group; // h5bp.com/t
  }

  tr,
  img {
    page-break-inside: avoid;
  }

  img {
    max-width: 100% !important;
  }

  p,
  h2,
  h3 {
    orphans: 3;
    widows: 3;
  }

  h2,
  h3 {
    page-break-after: avoid;
  }

  lib-swui-top-menu {
    display: none;
  }

  lib-swui-sidebar {
    display: none;
  }

  .cdk-overlay-container {
    display: none;
  }
}

.sec-block {

  &--whitelists,
  &--available-sites {
    .sw-grid {
      width: calc(100% + 2px);
      margin: 0 -1px;

      &__header {
        padding: 0 16px 0 12px;
      }

      &__body {
        border-radius: 0 !important;
      }
    }
  }
}

.game-groups-header {
  mat-form-field {
    .mat-form-field-wrapper {
      margin: 0;

      .mat-form-field-infix {
        height: 34px;
        padding: 0;
      }

      .mat-select-arrow-wrapper {
        transform: translateY(0);
      }
    }
  }
}

.game-group-filters {
  hints {
    .sw-banner {
      margin-bottom: 0 !important;
    }
  }
}

mat-header-row {
  z-index: 1 !important;
}

// @include attr-x('font-weight', 10, 10, '');
// @include attr-x('width', 10, 10, '%');

// @include attr-x('padding', 10, 4, 'px');
// @include attr-x('padding-top', 10, 4, 'px');
// @include attr-x('padding-left', 10, 4, 'px');
// @include attr-x('padding-bottom', 10, 4, 'px');

// @include attr-x('margin', 10, 4, 'px');
// @include attr-x('margin-top', 10, 4, 'px');
// @include attr-x('margin-left', 10, 4, 'px');
// @include attr-x('margin-right', 10, 4, 'px');
// @include attr-x('margin-bottom', 10, 4, 'px');


// close X button start
.close-button-x {
  float: right;
  top: -24px;
  right: -24px;
}

.close-icon:hover {
  transform: rotate(180deg);
}

::ng-deep .icon-outside .close-button-x {
  float: right;
  top: -52px;
  right: -52px;
}

::ng-deep .icon-outside .mat-dialog-container {
  overflow: unset
}

// close X button end

// severity levels start
.custom-error {
  padding: 6px 16px;
  font-size: 14px;
  border-radius: 4px;
  display: block;

  &--error {
    color: rgb(97, 26, 21);
    background-color: rgb(253, 236, 234);
  }

  &--warn {
    color: rgb(102, 60, 0);
    background-color: rgb(255, 244, 229);
  }

  &--info {
    color: rgb(13, 60, 97);
    background-color: rgb(232, 244, 253);
  }

  &--success {
    color: rgb(30, 70, 32);
    background-color: rgb(237, 247, 237);
  }
}

// severity levels end

@media print {
  button {
    visibility: hidden;
  }
}

.gh-iframe {
  width: 580px;
  min-height: 400px;
  border: none;
}

.no-borders {
  border: none;
}

.entity-rtp-grid {
  .sw-grid {
    &__actions {
      padding-bottom: 10px !important;
    }
  }
}

.live-limits-radio-button {
  .mat-radio-button:not(.mat-radio-disabled).cdk-program-focused .mat-radio-persistent-ripple {
    opacity: 0 !important;
  }
}

.td-radio-button-widget {
  .mat-radio-button:not(.mat-radio-disabled).cdk-program-focused .mat-radio-persistent-ripple {
    opacity: 0 !important;
  }
}

.mat-tab-group.nested {
  .mat-tab-header {
    .mat-tab-label {
      .mat-badge-content {
        font-size: 10px;
        width: 18px;
        height: 18px;
        line-height: 18px;
      }
    }
  }
}

.game-group__grid {
  .sw-grid {
    thead {
      tr {
        .mat-column-row-actions-column div {
          padding-left: 25px !important;
        }
      }
    }
  }
}


.sw-es-tab {
  &__header {
    display: flex;
    align-items: center;
    padding: 32px 32px 24px;
  }

  &__body {
    padding: 0 32px;
  }

  &__footer {
    display: flex;
    align-items: center;
    height: 84px;
    padding: 0 32px;
    border-top: 1px solid rgba(0, 0, 0, 0.12);

    button {
      min-width: auto;
    }
  }

  &__title {
    font-size: 24px;
    line-height: 1;
    font-weight: 700;
    letter-spacing: .15px;
  }
}

.report-item {
  &__toggles {
    border: none;
    height: 30px;

    .mat-button-toggle-label-content {
      line-height: 30px;
    }

    .mat-button-toggle {
      position: relative;
      border: 1px solid rgba(0, 0, 0, .12);

      &:not(:last-child) {
        margin-right: -1px;
      }

      &:first-child {
        border-top-left-radius: 4px;
        border-bottom-left-radius: 4px;
      }

      &:last-child {
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;
      }
    }

    .mat-button-toggle-checked {
      z-index: 1;
      border-color: #1468CF !important;
      color: #1468CF;
      background: rgba(20, 104, 207, 0.1) !important;
    }
  }
}

.live-limits-icons {
  .mat-button-focus-overlay {
    background-color: transparent !important;
  }
}

.entity-breadcrumbs {
  &__menu {
    margin-right: -10px !important;
  }
}

.dialog-actions {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 72px;
  margin: 0 -24px -24px;
  background: #EBEFF3;

  button {
    margin-right: 16px;

    &:last-child {
      margin-right: 0;
    }
  }
}

.link,
.widget-link {
  @extend .color-primary !optional;
  text-decoration: none;
}

.entity-test-players-grid {
  .sw-row-actions {
    justify-content: center !important;
  }
}

.history__grid {
  .sw-grid {
    lib-swui-grid-row-actions {
      .mat-button-disabled:hover {
        color: rgba(0, 0, 0, 0.26);
      }
    }
  }
}
