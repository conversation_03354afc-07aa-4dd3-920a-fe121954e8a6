import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { FlexLayoutModule } from '@angular/flex-layout';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatSelectModule } from '@angular/material/select';
import { MatDialogModule } from '@angular/material/dialog';
import { MatCardModule } from '@angular/material/card';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatButtonModule } from '@angular/material/button';
import { MatRadioModule } from '@angular/material/radio';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiChipsAutocompleteModule, SwuiControlMessagesModule, SwuiSelectModule } from '@skywind-group/lib-swui';
import { TrimInputValueModule } from '../../directives/trim-input-value/trim-input-value.module';

import { UserEditorService } from './user-editor.service';
import { UserFormComponent } from './user-form.component';
import { UserEditorDialogComponent } from './user-editor-dialog.component';
import { ControlMessagesModule } from '../control-messages/control-messages.module';
import { BoConfirmationModule } from '../bo-confirmation/bo-confirmation.module';


@NgModule({
    imports: [
        CommonModule,
        TranslateModule,
        ControlMessagesModule,
        ReactiveFormsModule,
        MatDialogModule,
        MatButtonModule,
        MatFormFieldModule,
        MatInputModule,
        FlexLayoutModule,
        MatProgressSpinnerModule,
        MatProgressBarModule,
        MatSelectModule,
        MatRadioModule,
        SwuiChipsAutocompleteModule,
        SwuiSelectModule,
        BoConfirmationModule,
        SwuiControlMessagesModule,
        MatCardModule,
        TrimInputValueModule,
    ],
  declarations: [
    UserFormComponent,
    UserEditorDialogComponent,
  ],
  providers: [
    UserEditorService,
  ],
})
export class MatUserEditorModule {
}
