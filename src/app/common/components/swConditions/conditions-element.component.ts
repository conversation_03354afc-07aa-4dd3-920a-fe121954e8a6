import { Component, EventEmitter, Input, Output } from '@angular/core';
import { Form<PERSON><PERSON>y, FormBuilder, FormGroup } from '@angular/forms';

export const HIGHLIGHT_CONTAINER_CLASS = 'condition__group';
export const HIGHLIGHT_CLASS = 'condition__group--highlight';

export class SelectOption {
  title?: string;
  value: any;
}

@Component({
  selector: '[sw-conditions-element]',
  templateUrl: './conditions-element.component.html',
  styleUrls: ['./conditions.component.scss'],
})
export class ConditionsElementComponent {
  @Input() public form: FormGroup;
  @Input() public valueFields: SelectOption[];
  @Input() public operators: SelectOption[];
  @Input() public disabled: boolean = false;

  @Output() public remove: EventEmitter<FormGroup> = new EventEmitter();

  constructor(private fb: FormBuilder) {

  }

  public isConditionGroup() {
    return this.form && this.form.controls.hasOwnProperty('group');
  }

  public isConditionItem() {
    return this.form && this.form.controls.hasOwnProperty('value');
  }

  public get conditionsGroup(): FormArray {
    return <FormArray>this.form.get('group');
  }


  public highlightGroup($event) {
    const target: HTMLElement = <HTMLElement>$event.target;
    const ancestor: HTMLElement = this.getAncestor(target, HIGHLIGHT_CONTAINER_CLASS);
    if (!ancestor) return;

    if (ancestor.classList.contains(HIGHLIGHT_CLASS)) {
      ancestor.classList.remove(HIGHLIGHT_CLASS);
    } else {
      ancestor.classList.add(HIGHLIGHT_CLASS);
    }
  }

  public removeItem() {
    this.remove.emit(this.form);
  }

  public removeGroup($event) {
    $event.preventDefault();
    this.remove.emit(this.form);
  }

  public onRemoveChildItem(item: FormGroup) {
    let itemIndex = this.conditionsGroup.controls.indexOf(item);
    this.conditionsGroup.removeAt(itemIndex);

    if (!this.conditionsGroup.controls.length) {
      this.remove.emit(this.form);
    }
  }

  public addConditionItem($event) {
    $event.preventDefault();
    this.conditionsGroup.push(this.createConditionItem());
  }

  public addConditionGroup($event) {
    $event.preventDefault();
    this.conditionsGroup.push(this.createConditionGroup());
  }

  private getAncestor(child: HTMLElement, cls): HTMLElement {
    let element = child;
    let found = false;

    while (element && !found) {
      element = element.parentElement;
      if (element) {
        found = element.classList.contains(cls);
      }
    }
    return found ? element : null;
  }

  private createConditionGroup(): FormGroup {
    return this.fb.group({
      groupType: 'and',
      group: this.fb.array([
        this.createConditionItem()
      ])
    });
  }

  private createConditionItem(): FormGroup {
    return this.fb.group({
      valueField: '',
      operator: '',
      value: '',
    });
  }
}
