.bo-select {
  position: relative;

  &__button {
    position: relative;
    width: 100%;
    text-align: left;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding-right: 40px;

    &:after {
      content: '\e9c5';
      font-family: Icomoon;
      display: inline-block;
      position: absolute;
      top: 50%;
      right: 12px;
      margin-top: -8px;
      font-size: 16px;
      line-height: 1;
      color: inherit;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      transition: transform 0.15s ease-in-out;
    }

    &:hover,
    &:focus {
      color: inherit !important;
    }
  }

  &__dropdown {
    max-width: 100%;
    width: 100%;
    padding: 10px;
  }

  &__search {
    margin-bottom: 10px;
  }

  &__list {
    overflow: auto;
  }

  &__item {
    padding: 5px 0;
    cursor: pointer;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    &:hover {
      background-color: #fafafa;
    }

    &.disabled {
      color: #999;
      font-weight: 300;
      background-color: inherit;
      cursor: auto;
    }
  }

  &__cancel {
    position: absolute;
    right: 25px;
    top: 12px;
    font-size: 11px;
  }

  &.open {
    .bo-select__button {
      &:not(.disabled) {
        &:after {
          transform: rotate(180deg);
        }
      }
    }
  }
}
