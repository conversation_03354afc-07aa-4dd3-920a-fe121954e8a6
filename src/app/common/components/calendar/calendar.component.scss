.sw-calendar {
  position: relative;

  .disabled {
    background: #fafafa;
    cursor: not-allowed;

    i {
      color: #9f9f9f;
    }
  }

  &_wrapper {
    left: inherit;
    right: 0;
    padding: 14px;
    max-width: 350px;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 3px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  &_table {
    width: 100%;
    color: black;
  }

  &_th,
  &_td {
    padding: 10px;
    line-height: 1;
    white-space: nowrap;
    text-align: center;

    &.today {
      background-color: lightgray;
    }

    &:hover {
      background-color: #f5f5f5;
    }

    &.selected {
      background-color: #26A69A;

      &:not(.today) {
        color: #fff;
      }
    }

    &.disabled {
      pointer-events: none;
      opacity: 0.4;
    }
  }

  &_td {
    cursor: pointer;
    border-radius: 3px;
  }

  &_th {
    color: #999;
    font-weight: normal;
    font-size: 12px;

    &.month {
      font-size: 15px;
      line-height: 1;
      color: #333;
      padding-top: 15px;
      padding-bottom: 15px;
      font-weight: 400;
    }

    &.prev,
    &.next {
      cursor: pointer;

      &:hover {
        color: #333;
      }
    }
  }

  &_input {
    &.form-control {
      display: inline-block;
      width: 70px;
    }

    &.select {
      -moz-text-align-last: center;
      text-align-last: center;
    }
  }
}
