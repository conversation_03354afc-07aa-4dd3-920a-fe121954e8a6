$color-border: #ddd;
$color-hover-bg: #e1e1e1;

:host {
  display: flex;
}

.bo-labels {
  position: relative;
  display: inline-block;
  min-height: 28px;
  min-width: 180px;
  box-sizing: border-box;

  &__selected {
    display: flex;
    align-items: center;
    height: 100%;
    width: 100%;
  }

  &__input {
    display: none;
    width: 100%;
    padding: 3px;
    box-shadow: none;
    border: none;
    border-radius: 3px;

    &:focus {
      outline: none !important;
      box-shadow: none !important;
    }

    &:disabled {
      background-color: #fff !important;
    }

    .show {
      display: block;
    }
  }

  &__btn {
    width: 38px;
    padding: 0;

    &--edit {
      display: none;
      position: absolute;
      top: 0;
      right: -1px;
      height: 100%;
    }

    &--select {
      position: absolute;
      top: 0;
      right: -1px;
      height: 100%;
    }

    &:focus {
      outline: none !important;
      box-shadow: none !important;
    }
  }

  &__selected-list {
    display: block;
    width: 100%;
    margin: 0;
    padding: 0;
  }

  &__nolabels {
    display: flex;
    align-items: center;
    width: 100%;
    height: 30px;
    margin: -1px 0;
    padding-left: 12px;
    border-radius: 3px;
  }

  &__noitems {
    padding-left: 3px;
  }

  &__dropdown {
    top: calc(100% - 3px);
    width: calc(100% + 1px);
    height: auto;
    overflow-y: auto;

    .dropdown-item {
      padding-left: 3px;
      padding-right: 3px;

      &:hover, &:focus {
        background-color: $color-hover-bg;
      }
    }
  }

  &__add {
    text-transform: uppercase;
    font-weight: 600;
    font-size: 10px;
    line-height: 21px;
    background-color: #e1e1e1;
  }

  &__actions {
    display: none;
    position: absolute;
    right: -1px;
    top: 100%;
    z-index: 2;
    margin-top: -1px;
    padding: 3px;
    border: 1px solid $color-border;
    border-radius: 3px;
    border-top-left-radius: 0;
    background-color: #fff;
  }

  &__inner {
    width: 100%;
    padding-right: 38px;
    border: 1px solid transparent;
    border-radius: 3px;

    &:not(.disabled) {
      &:hover {
        border-color: $color-border;

        .bo-labels__btn {
          &--edit {
            display: block;
          }
        }

        .bo-labels__nolabels {
          border-color: transparent;
        }
      }
    }
  }

  &__nomatches {
    padding-left: 3px;
    padding-right: 3px;
  }

  &--editable {
    display: block;
    width: 100%;

    .bo-labels__inner {
      border-color: $color-border;
    }

    .bo-labels__input {
      display: block;
    }

    .bo-labels__actions {
      display: block;
    }

    .bo-labels__nolabels {
      border-color: transparent;
    }

    .editable-item {
      &__remove {
        visibility: visible;
        margin-left: 0;

        i {
          top: 0;
        }
      }

      &:hover {
        box-shadow: 0 0 2px 1px rgba(38, 50, 56, 0.31);

        .editable-item__remove {
          color: #333;
        }
      }
    }
  }
}

.editable-item {
  display: flex;
  align-items: center;
  transition: box-shadow 0.3s ease-in-out;

  &__remove {
    visibility: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 22px;
    height: 22px;
    margin-left: -21px;
    color: #9b9b9b;
    background-color: #fafafa;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;

    i {
      font-size: 12px;
    }
  }

  &__label {
    position: relative;
    z-index: 1;
    font-size: 11px;
  }
}

.label-list {
  margin: 0;
  padding: 0;
  list-style: none;

  li {
    float: left;
    margin: 0;
    padding: 3px;
  }

  &:after {
    content: '';
    display: table;
    clear: both;
  }
}

:host-context(.grid-header__filter) {
  .bo-labels {
    &__input {
      min-height: 30px !important;
    }

    &__inner {
      border-color: #ddd;
    }
  }

  .label-list {
    li {
      padding-top: 4px;
      padding-bottom: 4px;
    }
  }
}
