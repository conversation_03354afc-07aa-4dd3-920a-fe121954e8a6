import { NgModule } from '@angular/core';
import { PreloadAllModules, RouterModule, Routes } from '@angular/router';


const routes: Routes = [
  { path: '', redirectTo: 'pages', pathMatch: 'full' },
  {
    path: 'pages',
    loadChildren: () => import('./pages/pages.module').then(m => m.PagesModule),
  },
  {
    path: 'empty',
    loadChildren: () => import('./pages/empty/empty.module').then(m => m.EmptyModule),
  },
  {
    path: 'integration-test-result',
    loadChildren: () =>
      import('./pages/integrations/integration-test-result/integration-test-result.module').then(m => m.IntegrationTestResultModule),
  },
  {
    path: 'gitbook',
    loadChildren: () => import('./gitbook/gitbook.module').then(m => m.GitbookModule),
  },
];

@NgModule({
  imports: [RouterModule.forRoot(routes, { preloadingStrategy: PreloadAllModules })],
  exports: [RouterModule]
})
export class AppRoutingModule {
}
