import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';

import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { StatusConfirmComponent } from './status-confirm.component';

export const matModules = [
  MatDialogModule,
  MatButtonModule,
];

@NgModule({
  imports: [
    CommonModule,
    TranslateModule,
    ...matModules,
  ],
  declarations: [StatusConfirmComponent],
  exports: [StatusConfirmComponent]
})
export class StatusConfirmModule {
}
