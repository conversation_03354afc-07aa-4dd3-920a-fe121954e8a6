import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { TrimInputValueModule } from '../../../../../../common/directives/trim-input-value/trim-input-value.module';


import { MatCountryDialogComponent } from './mat-country-dialog.component';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatTableModule } from '@angular/material/table';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDialogModule } from '@angular/material/dialog';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';

export const matModules = [
  MatTableModule,
  MatFormFieldModule,
  MatInputModule,
  MatButtonModule,
  MatDialogModule,
  MatCheckboxModule,
];

@NgModule({
    imports: [
        CommonModule,
        TranslateModule,
        FormsModule,
        ...matModules,
        TrimInputValueModule,
    ],
  declarations: [
    MatCountryDialogComponent
  ],
})
export class MatCountryDialogModule {
}
