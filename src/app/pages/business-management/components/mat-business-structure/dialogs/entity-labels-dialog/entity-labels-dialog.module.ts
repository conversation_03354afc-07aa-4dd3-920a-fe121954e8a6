import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexModule } from '@angular/flex-layout';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiChipsAutocompleteModule, SwuiSelectModule } from '@skywind-group/lib-swui';
import { EntityLabelsService } from '../../../../../../common/services/entity-labels.service';
import { LabelsService } from '../../../../../../common/services/labels.service';
import { EntityLabelsDialogComponent } from './entity-labels-dialog.component';

@NgModule({
  imports: [
    MatButtonModule,
    MatIconModule,
    MatDialogModule,
    TranslateModule,
    ReactiveFormsModule,
    FlexModule,
    MatFormFieldModule,
    SwuiSelectModule,
    SwuiChipsAutocompleteModule,
    CommonModule,
  ],
  declarations: [
    EntityLabelsDialogComponent,
  ],
  providers: [
    LabelsService,
    EntityLabelsService,
  ],
})
export class EntityLabelsDialogModule {
}
