import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { MatTabsModule } from '@angular/material/tabs';
import { TranslateModule } from '@ngx-translate/core';
import { ManageBalanceModule } from '../manage-balance/manage-balance.module';
import { MatRegionalDialogComponent } from './mat-regional-dialog.component';
import { MatRegionalItemModule } from './mat-regional-item/mat-regional-item.module';

export const matModules = [
  MatDialogModule,
  MatButtonModule,
  MatTabsModule,
  MatRegionalItemModule,
];


@NgModule({
  declarations: [
    MatRegionalDialogComponent,
  ],
  imports: [
    CommonModule,
    TranslateModule,
    ReactiveFormsModule,
    ...matModules,
    ManageBalanceModule,
  ]
})
export class MatRegionalDialogModule {
}
