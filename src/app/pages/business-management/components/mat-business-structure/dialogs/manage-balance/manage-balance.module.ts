import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { TranslateModule } from '@ngx-translate/core';
import { ControlMessagesModule } from '../../../../../../common/components/control-messages/control-messages.module';
import { TouchspinModule } from '../../../../../../common/components/touchspin/touchspin.module';
import { TrimInputValueModule } from '../../../../../../common/directives/trim-input-value/trim-input-value.module';
import { FormComponent } from './form.component';
import { ManageBalanceComponent } from './manage-balance.component';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatDialogModule } from '@angular/material/dialog';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';

export const matModules = [
  MatDialogModule,
  MatButtonModule,
  MatFormFieldModule,
  MatSelectModule,
  MatInputModule,
  MatProgressSpinnerModule,
];

@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        TranslateModule,
        TouchspinModule,
        ControlMessagesModule,
        ...matModules,
        TrimInputValueModule,

    ],
  declarations: [
    ManageBalanceComponent,
    FormComponent,
  ],
  exports: [
    FormComponent
  ],
})
export class ManageBalanceModule {
}
