import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TrimInputValueModule } from '../../../../../../common/directives/trim-input-value/trim-input-value.module';
import { MatLanguageDialogComponent } from './mat-language-dialog.component';
import { TranslateModule } from '@ngx-translate/core';
import { FormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatTableModule } from '@angular/material/table';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDialogModule } from '@angular/material/dialog';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';

export const matModules = [
  MatTableModule,
  MatFormFieldModule,
  MatInputModule,
  MatButtonModule,
  MatDialogModule,
  MatCheckboxModule,
];

@NgModule({
    imports: [
        CommonModule,
        TranslateModule,
        FormsModule,
        ...matModules,
        TrimInputValueModule,
    ],
  declarations: [
    MatLanguageDialogComponent,
  ],
})
export class MatLanguageDialogModule {
}
