import { NgModule } from '@angular/core';

import { MatJurisdictionsDialogComponent } from './mat-jurisdictions-dialog.component';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { FormsModule } from '@angular/forms';
import { MatTableModule } from '@angular/material/table';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { TrimInputValueModule } from '../../../../../../common/directives/trim-input-value/trim-input-value.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    TranslateModule,

    MatTableModule,
    MatDialogModule,
    MatButtonModule,
    MatCheckboxModule,
    MatFormFieldModule,
    MatInputModule,
    TrimInputValueModule,
  ],
  declarations: [
    MatJurisdictionsDialogComponent
  ],
  exports: [],
  providers: [],
})
export class MatJurisdictionsDialogModule {
}
