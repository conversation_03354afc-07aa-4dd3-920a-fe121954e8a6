<div class="domain">
  <div class="domain--info">
    <span>{{ 'ENTITY_SETUP.DOMAINS.static.tags' | translate }}:</span>
  </div>
  <div class="domain--controls">
    <mat-form-field appearance="outline" style="min-width: 320px; width: 100%; max-width: 560px;">
      <mat-label>{{ 'ENTITY_SETUP.DOMAINS.tags' | translate }}</mat-label>
      <mat-chip-set #chipList>
        <mat-chip *ngFor="let tag of tagsControl.value; let i = index" (removed)="removeTag(i)" [removable]="true">
          {{ tag }}
          <button matChipRemove>
            <mat-icon>cancel</mat-icon>
          </button>
        </mat-chip>
      </mat-chip-set>
      <input placeholder="{{ 'ENTITY_SETUP.DOMAINS.addTag' | translate }}" [matChipInputFor]="chipList"
        [matChipInputSeparatorKeyCodes]="separatorKeys" [matChipInputAddOnBlur]="true"
        (matChipInputTokenEnd)="onChipAdd($event)" />
      <mat-hint align="start">only domains containing those tags (case-insensitive substring match) are
        allowed</mat-hint>
    </mat-form-field>

    <button mat-icon-button (click)="setTags()" [matTooltip]="'ENTITY_SETUP.DOMAINS.saveTags' | translate">
      <mat-icon>save</mat-icon>
    </button>
    <button mat-icon-button (click)="resetTags()" [matTooltip]="'ENTITY_SETUP.DOMAINS.resetTags' | translate">
      <mat-icon>undo</mat-icon>
    </button>
  </div>
</div>
