import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexModule } from '@angular/flex-layout';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiControlMessagesModule, SwuiDatePickerModule, SwuiGridModule, SwuiSchemaTopFilterModule } from '@skywind-group/lib-swui';
import { ControlMessagesModule } from '../../../../../common/components/control-messages/control-messages.module';
import { TrimInputValueModule } from '../../../../../common/directives/trim-input-value/trim-input-value.module';
import { TestPlayersService } from '../../../../../common/services/test-players.service';
import { TabTestPlayersComponent } from './tab-test-players.component';
import { TabTestPlayersRoutingModule } from './tab-test-players.routing';
import { TestPlayersDialogComponent } from './test-players-dialog/test-players-dialog.component';
import { TestPlayersMerchantComponent } from './test-players-merchant.component';
import { TestPlayersComponent } from './test-players.component';

@NgModule({
    imports: [
        TranslateModule.forChild(),
        TabTestPlayersRoutingModule,
        SwuiGridModule,
        MatButtonModule,
        SwuiSchemaTopFilterModule,
        CommonModule,
        MatDialogModule,
        ReactiveFormsModule,
        FlexModule,
        MatFormFieldModule,
        MatInputModule,
        SwuiControlMessagesModule,
        SwuiDatePickerModule,
        MatIconModule,
        MatCardModule,
        MatSlideToggleModule,
        ControlMessagesModule,
        MatTooltipModule,
        TrimInputValueModule,
    ],
  declarations: [
    TabTestPlayersComponent,
    TestPlayersComponent,
    TestPlayersMerchantComponent,
    TestPlayersDialogComponent,
  ],
  providers: [
    TestPlayersService,
  ],
})
export class TabTestPlayersModule {
}
