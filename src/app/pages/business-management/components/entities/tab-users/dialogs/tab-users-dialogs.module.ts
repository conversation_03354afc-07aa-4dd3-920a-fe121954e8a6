import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDialogModule } from '@angular/material/dialog';
import { MatListModule } from '@angular/material/list';
import { TranslateModule } from '@ngx-translate/core';
import { DeleteUserDialogComponent } from './delete-user-dialog.component';
import { TwofaResetDialogComponent } from './twofa-reset-dialog.component';
import { UnblockUserDialogComponent } from './unblock-user-dialog.component';

@NgModule({
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    MatDialogModule,
    MatCheckboxModule,
    MatListModule,
    MatButtonModule,
    FormsModule,
  ],
  exports: [],
  declarations: [
    DeleteUserDialogComponent,
    TwofaResetDialogComponent,
    UnblockUserDialogComponent,
  ],
  providers: [],
})
export class TabUsersDialogsModule {
}
