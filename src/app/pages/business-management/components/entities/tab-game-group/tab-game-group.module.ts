import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexLayoutModule } from '@angular/flex-layout';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiGridModule, SwuiSelectModule } from '@skywind-group/lib-swui';
import { SwuiControlMessagesModule } from '@skywind-group/lib-swui';

import { ControlMessagesModule } from '../../../../../common/components/control-messages/control-messages.module';
import { BaIfAllowedModule } from '../../../../../common/directives/baIfAllowed/baIfAllowed.module';
import { TrimInputValueModule } from '../../../../../common/directives/trim-input-value/trim-input-value.module';
import { PipesModule } from '../../../../../common/pipes/pipes.module';
import { GameGroupService } from '../../../../../common/services/game-group.service';
import { GameGroupDeleteDialogComponent } from './game-group-delete-dialog/game-group-delete-dialog.component';
import { GameGroupDialogComponent } from './game-group-dialog/game-group-dialog.component';
import { GameGroupComponent } from './game-group.component';
import { TabGameGroupComponent } from './tab-game-group.component';
import { TabGameGroupRoutingModule } from './tab-game-group.routing';


@NgModule({
    imports: [
        CommonModule,
        TranslateModule.forChild(),
        ReactiveFormsModule,
        FormsModule,
        TabGameGroupRoutingModule,
        ControlMessagesModule,
        BaIfAllowedModule,
        PipesModule,

        MatButtonModule,
        MatFormFieldModule,
        MatIconModule,
        MatTooltipModule,
        SwuiSelectModule,
        MatDialogModule,
        MatInputModule,
        MatCheckboxModule,
        FlexLayoutModule,
        SwuiControlMessagesModule,
        SwuiGridModule,
        TrimInputValueModule,
    ],
  exports: [],
  declarations: [
    TabGameGroupComponent,
    GameGroupComponent,
    GameGroupDialogComponent,
    GameGroupDeleteDialogComponent,
  ],
  providers: [
    GameGroupService,
  ],
})
export class TabGameGroupModule {
}
