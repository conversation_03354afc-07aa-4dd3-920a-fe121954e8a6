import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiGridModule } from '@skywind-group/lib-swui';
import { ControlMessagesModule } from '../../../../../../common/components/control-messages/control-messages.module';

import { EntityIpWhitelistComponent } from './entity-ip-whitelist.component';
import { TrimInputValueModule } from '../../../../../../common/directives/trim-input-value/trim-input-value.module';
import { RemoveConfirmDialogComponent } from './dialogs/remove-confirm-dialog.component';
import { MatDialogModule } from '@angular/material/dialog';
import { MatListModule } from '@angular/material/list';
import { MatIconModule } from '@angular/material/icon';
import { RouterModule } from '@angular/router';
import { DownloadCsvModule } from '../../../../../../common/components/download-csv/download-csv.module';

@NgModule({
    imports: [
        CommonModule,
        TranslateModule.forChild(),
        ControlMessagesModule,
        ReactiveFormsModule,
        SwuiGridModule,
        MatButtonModule,
        MatFormFieldModule,
        MatInputModule,
        TrimInputValueModule,
        MatDialogModule,
        MatListModule,
        MatIconModule,
        RouterModule,
        DownloadCsvModule,
    ],
  declarations: [
    EntityIpWhitelistComponent,
    RemoveConfirmDialogComponent
  ],
  exports: [
    EntityIpWhitelistComponent
  ],
  providers: [],
})
export class EntityIpWhitelistModule {
}
