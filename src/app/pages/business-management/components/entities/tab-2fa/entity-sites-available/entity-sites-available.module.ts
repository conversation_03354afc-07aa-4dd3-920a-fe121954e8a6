import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatListModule } from '@angular/material/list';
import { MatMenuModule } from '@angular/material/menu';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSelectModule } from '@angular/material/select';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiGridModule } from '@skywind-group/lib-swui';
import { ControlMessagesModule } from '../../../../../../common/components/control-messages/control-messages.module';
import { TrimInputValueModule } from '../../../../../../common/directives/trim-input-value/trim-input-value.module';
import { EditSiteDialogComponent } from './dialogs/edit-site-dialog.component';
import { RemoveConfirmDialogComponent } from './dialogs/remove-confirm-dialog.component';

import { EntitySitesAvailableComponent } from './entity-sites-available.component';
import { WhitelistLevelsComponent } from './whitelist-levels.component';

@NgModule({
    imports: [
        CommonModule,
        ReactiveFormsModule,
        ControlMessagesModule,
        TranslateModule.forChild(),
        SwuiGridModule,
        MatButtonModule,
        MatMenuModule,
        MatIconModule,
        MatProgressSpinnerModule,
        MatTooltipModule,
        MatDialogModule,
        MatFormFieldModule,
        MatInputModule,
        MatSelectModule,
        MatListModule,
        MatCheckboxModule,
        TrimInputValueModule,
    ],
  declarations: [
    EntitySitesAvailableComponent,
    WhitelistLevelsComponent,
    EditSiteDialogComponent,
    RemoveConfirmDialogComponent,
  ],
  exports: [
    EntitySitesAvailableComponent,
    WhitelistLevelsComponent,
  ],
  providers: [],
})
export class EntitySitesAvailableModule {
}
