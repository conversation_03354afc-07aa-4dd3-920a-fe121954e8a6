import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexModule } from '@angular/flex-layout';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDialogModule } from '@angular/material/dialog';
import { TranslateModule } from '@ngx-translate/core';

import { ClipboardModule } from '../../../../../common/components/clipboard/clipboard.module';
import { ViewHistoryComponent } from './view-history.component';

@NgModule({
  declarations: [
    ViewHistoryComponent
  ],
  imports: [
    CommonModule,
    MatCardModule,
    ReactiveFormsModule,
    ClipboardModule,
    TranslateModule,
    FlexModule,
    MatButtonModule,
    MatDialogModule
  ]
})
export class ViewHistoryModule {
}
