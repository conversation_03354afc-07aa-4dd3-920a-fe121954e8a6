import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiPagePanelModule } from '@skywind-group/lib-swui';
import { ClipboardModule } from '../../common/components/clipboard/clipboard.module';
import { AuditService } from '../../common/services/audits.service';
import { CurrentUserResolver } from '../../common/services/resolvers/current-user.resolver';
import { RoleService } from '../../common/services/role.service';
import { UserService } from '../../common/services/user.service';
import { ActivityLogModule } from './components/activityLog/activityLog.module';
import { UsersListModule } from './components/list/user-list.module';
import { UsersComponent } from './users.component';
import { UsersRoutingModule } from './users.routing';

@NgModule({
  imports: [
    CommonModule,
    TranslateModule,
    UsersListModule,
    UsersRoutingModule,
    FormsModule,
    ReactiveFormsModule,
    ClipboardModule,
    SwuiPagePanelModule,
    ActivityLogModule
  ],
  declarations: [
    UsersComponent,
  ],
  providers: [
    AuditService,
    RoleService,
    CurrentUserResolver,
    UserService
  ]
})
export class UsersModule {
}
