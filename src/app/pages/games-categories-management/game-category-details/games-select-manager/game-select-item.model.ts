import { GameInfo, GameLabel, Label } from '../../../../common/typings';
import { GameCategoryItem } from '../../game-category.model';

export interface IntersectionData {
  games: GameSelectItem[];
}

export class CheckboxItem {
  checked: boolean;

  constructor() {
    this.checked = false;
  }

  toggleCheck() {
    this.checked = !this.checked;
  }
}

export interface GameSelectItemTypes {
  GAME: gameSelectItemType;
  PROVIDER: gameSelectItemType;
  LABEL: gameSelectItemType;
  INTERSECTION: gameSelectItemType;
  CORRUPTION: gameSelectItemType;
}

export type gameSelectItemType = 'game' | 'provider' | 'label' | 'intersection' | 'corruption';

export const gameSelectItemTypes: GameSelectItemTypes = {
  GAME: 'game',
  PROVIDER: 'provider',
  LABEL: 'label',
  INTERSECTION: 'intersection',
  CORRUPTION: 'corruption'
};

export const getUniqueLabelGames = (labels: GameSelectItem[]): GameSelectItem[] => {
  let codes = []; // array of codes is required for determining unique games

  return labels.reduce((games, label) => {
    let labelGames = label.items.filter(({ data }) => {
      let code = (data as GameInfo).code;
      return codes.indexOf(code) === -1;
    });
    codes = [...codes, ...labelGames.map(game => game.data['code'])];
    return [...games, ...labelGames];
  }, []);
};

export const getIntersectedGames = (labels: GameSelectItem[]): GameSelectItem[] => {
  let labelIds = labels.map(label => (label.data as Label).id);

  return getUniqueLabelGames(labels)
    .filter((game) => labelIds.every(id => game.data['labels'].map(l => l.id).indexOf(id) > -1));
};

export class GameSelectItem extends CheckboxItem {

  id?: string;
  type: gameSelectItemType;
  items?: GameSelectItem[]; // for labels and intersections
  data?: Object; // can be GameInfo, Label, Provider, etc...

  static createFromGameInfo(game: GameInfo): GameSelectItem {

    if (!game.labels.find(label => label.id === game.providerCode)) {
      game.labels.push({
        id: game.providerCode,
        title: game.providerTitle,
        group: 'provider',
      }); // adding provider as label
    }

    return new GameSelectItem({
      id: game.code,
      type: gameSelectItemTypes.GAME,
      data: game,
    });
  }

  static convertToGameInfo(item: GameSelectItem): GameInfo {
    return item.data as GameInfo;
  }

  static createFromLabel(label: Label): GameSelectItem {
    return new GameSelectItem({
      id: label.id,
      type: label.group === 'provider' ? gameSelectItemTypes.PROVIDER : gameSelectItemTypes.LABEL,
      data: label,
    });
  }

  static createLabelIntersection(items: GameSelectItem[]): GameSelectItem {
    const allowedIntersectionItems = [ // we're not supporting nested intersections, only labels and providers
      gameSelectItemTypes.PROVIDER,
      gameSelectItemTypes.LABEL
    ];

    let intersectionItems = items.filter((item) => allowedIntersectionItems.indexOf(item.type) > -1);

    return new GameSelectItem({
      type: gameSelectItemTypes.INTERSECTION,
      items: intersectionItems,
      data: {
        games: getIntersectedGames(intersectionItems)
      }
    });
  }

  constructor(obj) {
    super();

    if ('id' in obj) {
      this.id = obj.id;
    }

    this.type = obj.type;
    this.data = obj.data || {};

    if (this.type === gameSelectItemTypes.INTERSECTION && obj.items) {
      this.setIntersectionItems(obj.items);
    } else {
      this.items = [];
    }
  }

  public toCategoryItem(): GameCategoryItem {
    let allowedChildrenTypes = [
      gameSelectItemTypes.PROVIDER,
      gameSelectItemTypes.LABEL
    ];

    let item = {
      id: this.id,
      type: this.type
    };

    if (typeof item.id === 'undefined') {
      delete item.id;
    }

    if (this.items && this.items.length) {
      let items = this.items
        .filter(child => allowedChildrenTypes.indexOf(child.type) > -1)
        .map((child: GameSelectItem) => child.toCategoryItem()) as GameCategoryItem[];

      if (items.length) {
        item = Object.assign({}, item, { items });
      }
    }
    return <GameCategoryItem>item;
  }

  public addGameToLabel(game: GameSelectItem) {
    let typesWithGames = [
      gameSelectItemTypes.LABEL,
      gameSelectItemTypes.PROVIDER,
    ];

    if (typesWithGames.indexOf(this.type) > -1) {
      this.items.push(game);
    }
  }

  public getGameLabels(): GameLabel[] {
    if (this.type !== gameSelectItemTypes.GAME) return;

    return (this.data as GameInfo).labels;
  }

  public get title(): string {
    let title: string = '';
    if ('title' in this.data) {
      title = this.data['title'] as string;
    }
    return title;
  }

  private setIntersectionItems(items: GameSelectItem[]) {
    this.items = items;
  }

}

export const isLabel = (item: GameSelectItem): boolean => {
  return [
    gameSelectItemTypes.LABEL,
    gameSelectItemTypes.PROVIDER,
  ].indexOf(item.type) > -1;
};
