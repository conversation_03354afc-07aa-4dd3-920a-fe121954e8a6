import { NgModule } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { DragulaModule } from 'ng2-dragula';
import { TooltipModule } from 'ngx-bootstrap/tooltip';
import { TrimInputValueModule } from '../../../../common/directives/trim-input-value/trim-input-value.module';

import { GamesSelectManagerComponent } from './games-select-manager.component';
import { AvailableGamesComponent } from './available-games/available-games.component';
import { AvailableLabelsComponent } from './available-labels/available-labels.component';
import { GamesSelectManagerService } from './games-select-manager.service';
import { SelectedItemsComponent } from './selected-items/selected-items.component';
import { CoinValueColumnComponent } from './game-select-columns/coin-value-column.component';
import { GameCoeffColumnComponent } from './game-select-columns/game-coeff-column.component';
import { ExtraColumnChooserComponent } from './game-select-columns/extra-column-chooser.component';
import { FilteredPipe } from './filtered.pipe';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatButtonModule } from '@angular/material/button';
import { MatChipsModule } from '@angular/material/chips';

/**
 * @deprecated use lib-swui-games-select-manager
 */
@NgModule({
    imports: [
        CommonModule,
        TranslateModule,
        DragulaModule.forRoot(),
        TooltipModule.forRoot(),
        FormsModule,
        MatTabsModule,
        MatButtonModule,
        MatCheckboxModule,
        MatIconModule,
        MatFormFieldModule,
        MatInputModule,
        MatChipsModule,
        MatProgressSpinnerModule,
        TrimInputValueModule,
    ],
  exports: [
    GamesSelectManagerComponent,
  ],
  declarations: [
    GamesSelectManagerComponent,
    AvailableGamesComponent,
    AvailableLabelsComponent,
    SelectedItemsComponent,
    ExtraColumnChooserComponent,
    CoinValueColumnComponent,
    GameCoeffColumnComponent,
    FilteredPipe,
  ],
  providers: [
    GamesSelectManagerService
  ],
})
export class GamesSelectManagerModule {
}

