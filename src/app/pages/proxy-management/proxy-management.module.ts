import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexLayoutModule } from '@angular/flex-layout';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiGridModule, SwuiPagePanelModule } from '@skywind-group/lib-swui';
import { TrimInputValueModule } from '../../common/directives/trim-input-value/trim-input-value.module';
import { ProxyConfiramtionDialogComponent } from './proxy-confiramtion-dialog/proxy-confiramtion-dialog.component';
import { ProxyItemDialogComponent } from './proxy-item-dialog/proxy-item-dialog.component';

import { ProxyManagementComponent } from './proxy-management.component';


export const PROXY_MANAGEMENT_MODULES = [
  MatDialogModule,
  MatInputModule,
  MatFormFieldModule,
  MatButtonModule,
  FlexLayoutModule,
  ReactiveFormsModule,
  SwuiPagePanelModule,
  TranslateModule,
];

@NgModule({
  declarations: [
    ProxyManagementComponent,
    ProxyItemDialogComponent,
    ProxyConfiramtionDialogComponent
  ],
  exports: [ProxyManagementComponent],
    imports: [
        CommonModule,
        ...PROXY_MANAGEMENT_MODULES,
        SwuiGridModule,
        RouterModule.forChild([
            {
                path: '',
                pathMatch: 'full',
                component: ProxyManagementComponent,
                data: {
                    title: 'Proxy'
                }
            }
        ]),
        TrimInputValueModule
    ],
  providers: []
})
export class ProxyManagementModule {
}
