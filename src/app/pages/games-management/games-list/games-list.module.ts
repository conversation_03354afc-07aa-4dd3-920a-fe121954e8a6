import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatTooltipModule } from '@angular/material/tooltip';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import {
  SwuiControlMessagesModule, SwuiGridModule, SwuiNotificationsModule, SwuiPagePanelModule, SwuiSchemaTopFilterModule
} from '@skywind-group/lib-swui';
import { JackpotService } from 'src/app/common/services/jackpot.service';
import { ControlMessagesModule } from '../../../common/components/control-messages/control-messages.module';

import { BaIfAllowedModule } from '../../../common/directives/baIfAllowed/baIfAllowed.module';
import { GameProviderService } from '../../../common/services/game-provider.service';
import { GameService } from '../../../common/services/game.service';
import { GamesListComponent } from './games-list.component';
import { CloneGameComponent } from './modals/clone-game/clone-game.component';
import { SetJackpotComponent } from './modals/set-jackpot/set-jackpot.component';

@NgModule({
  imports: [
    BaIfAllowedModule,
    CommonModule,
    ControlMessagesModule,
    ReactiveFormsModule,
    RouterModule,
    TranslateModule,
    SwuiGridModule,
    SwuiPagePanelModule,
    MatIconModule,
    MatTooltipModule,
    MatButtonModule,
    SwuiSchemaTopFilterModule,
    MatDialogModule,
    MatFormFieldModule,
    SwuiControlMessagesModule,
    MatInputModule,
    SwuiNotificationsModule
  ],
  exports: [
    GamesListComponent
  ],
  declarations: [
    CloneGameComponent,
    GamesListComponent,
    SetJackpotComponent,
  ],
  providers: [
    GameProviderService,
    GameService,
    JackpotService
  ],
})
export class GamesListModule {
}
