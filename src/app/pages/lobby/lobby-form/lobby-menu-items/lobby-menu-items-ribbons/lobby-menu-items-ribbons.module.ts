import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexLayoutModule } from '@angular/flex-layout';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiControlMessagesModule } from '@skywind-group/lib-swui';
import { BsDropdownModule } from 'ngx-bootstrap/dropdown';
import { TooltipModule } from 'ngx-bootstrap/tooltip';
import { LobbyMenuItemsRibbonsModalComponent } from './lobby-menu-items-ribbons-modal/lobby-menu-items-ribbons-modal.component';

import { LobbyMenuItemsRibbonsComponent } from './lobby-menu-items-ribbons.component';


@NgModule({
  declarations: [
    LobbyMenuItemsRibbonsComponent,
    LobbyMenuItemsRibbonsModalComponent
  ],
  exports: [LobbyMenuItemsRibbonsComponent],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    BsDropdownModule.forRoot(),
    TooltipModule.forRoot(),
    TranslateModule,
    MatIconModule,
    MatButtonModule,
    MatInputModule,
    MatFormFieldModule,
    MatMenuModule,
    MatDialogModule,
    FlexLayoutModule,
    SwuiControlMessagesModule,
  ],
})
export class LobbyMenuItemsRibbonsModule {
}
