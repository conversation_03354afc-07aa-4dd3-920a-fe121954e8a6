<form [formGroup]="form" class="p-32">
  <mat-tab-group animationDuration="0ms" (selectedIndexChange)="onSelectedIndexChange($event)" [selectedIndex]="activeTabIndex">
    <mat-tab [label]="'LOBBY.FORM.settings' | translate">
      <mat-card class="mat-elevation-z0">
        <div fxLayout="column" style="max-width: 1000px">
          <mat-form-field appearance="outline">
            <mat-label>{{'LOBBY.FORM.title' | translate}}</mat-label>
            <input matInput trimValue type="text" formControlName="title">
            <mat-error>
              <lib-swui-control-messages [control]="form.get('title')" [force]="submitted"></lib-swui-control-messages>
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>{{'LOBBY.FORM.description' | translate}}</mat-label>
            <textarea matInput [cdkTextareaAutosize]="true" rows="5" cols="5" formControlName="description"></textarea>
            <mat-error>
              <lib-swui-control-messages [control]="form.get('description')" [force]="submitted"></lib-swui-control-messages>
            </mat-error>
          </mat-form-field>

          <lobby-settings [form]="form" [values]="lobbyOptions" [submitted]="submitted" name="settings"></lobby-settings>
        </div>
      </mat-card>
    </mat-tab>

    <mat-tab>
      <ng-template matTabLabel>
        {{'LOBBY.FORM.template' | translate}}
      </ng-template>
      <mat-card class="mat-elevation-z0">

        <mat-tab-group #tabsTemplates animationDuration="0ms" class="nested">
          <mat-tab>
            <ng-template matTabLabel>
              <mat-icon fontSet="material-icons-outline" class="tab-icon">settings</mat-icon> {{'LOBBY.FORM.templateSetup' | translate}}
            </ng-template>
            <div class="tpl-settings">
              <div *ngIf="theme" class="tpl-settings__header">
                <div class="tpl-settings__title">{{theme.name}}</div>
                <div class="tpl-settings__image">
                  <img [src]="theme.thumbnailUrl" [alt]="theme.name">
                </div>
              </div>
              <div class="tpl-settings__body">
                <div [themeTitle]="titleControl.value" [options]="theme?.options" [values]="themeOptions"
                     [submitted]="submitted" name="options" [template-settings]="form.get('theme')"></div>
              </div>
            </div>

          </mat-tab>

          <mat-tab>
            <ng-template matTabLabel>
              <mat-icon fontSet="material-icons-outline" class="tab-icon">important_devices</mat-icon> {{'LOBBY.FORM.templateList' | translate}}
            </ng-template>

            <div [templates-list]="themes" [active]="theme" (onActivate)="handleActivateTemplate($event)"></div>
          </mat-tab>
        </mat-tab-group>

      </mat-card>
    </mat-tab>

    <mat-tab [label]="'LOBBY.MENU_ITEMS.menuItems' | translate">
      <lobby-menu-items
        formControlName="menuItems"
        [themeKey]="theme.key"
        [widgets]="widgets"
        [submitted]="submitted">
      </lobby-menu-items>
    </mat-tab>
  </mat-tab-group>

  <div style="padding: 32px 0" fxLayout="row" fxLayoutAlign="end center">
    <button [routerLink]="['/pages/lobby/layouts']" mat-stroked-button color="primary" style="margin-right: 8px;">
      <mat-icon>keyboard_arrow_left</mat-icon>
      {{ 'LOBBY.FORM.back' | translate }}
    </button>
    <ng-content select=".submit-button"></ng-content>
  </div>
</form>
