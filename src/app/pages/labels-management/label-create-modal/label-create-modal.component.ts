import { Component, OnInit } from '@angular/core';
import { Form<PERSON><PERSON>er, FormControl, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef } from '@angular/material/dialog';
import { combineLatest, Subject } from 'rxjs';
import { debounceTime, filter, map, switchMap, takeUntil, tap } from 'rxjs/operators';
import { SelectOptionModel } from '../../../common/models/select-option.model';
import { LabelsService } from '../../../common/services/labels.service';
import { LabelGroupInfo } from '../../../common/typings/label';
import { SUPPORTED_LABEL_TYPES } from '../schema';

@Component({
  selector: 'label-create-modal',
  templateUrl: './label-create-modal.component.html',
})
export class LabelCreateModal implements OnInit {
  form: FormGroup;
  messageErrors: any;
  groupType: SelectOptionModel[] = SUPPORTED_LABEL_TYPES.map(type => ({ id: type.id, text: type.displayName }));
  groups: SelectOptionModel[] = [];
  groupTypeControl = new FormControl('');
  groupControl = new FormControl({ value: '', disabled: true });

  private readonly destroyed$ = new Subject<void>();

  constructor(
    private fb: FormBuilder,
    private readonly service: LabelsService,
    private dialogRef: MatDialogRef<LabelCreateModal>
  ) {
    this.initForm();
  }

  ngOnInit(): void {
    this.groupTypeControl.valueChanges
      .pipe(
        switchMap((type: string) => this.service.getLabelGroups(type)),
        tap(() => this.groupControl.reset()),
        map((groupInfo: LabelGroupInfo[]) => {
          return groupInfo.map((group: LabelGroupInfo) => ({ id: group.group, text: group.group }));
        }),
        takeUntil(this.destroyed$)
      ).subscribe((group: SelectOptionModel[]) => {
        this.groups = group;
        this.groups.length ? this.groupControl.enable() : this.groupControl.disable();
        this.form.get('groupId').reset();
      });

    combineLatest([
      this.groupTypeControl.valueChanges,
      this.groupControl.valueChanges
    ]).pipe(
      debounceTime(400),
      filter(([type, group]: [string, string]) => !!type && !!group),
      switchMap(([type, group]: [string, string]) => this.service.getLabelGroups(type, group)),
      takeUntil(this.destroyed$)
    ).subscribe((group: LabelGroupInfo[]) => {
      this.form.get('groupId').patchValue(group[0].id);
      this.form.get('title').enable();
    });
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  onNoClick() {
    this.dialogRef.close();
  }

  onConfirmClick() {
    this.dialogRef.close(this.form.getRawValue());
  }

  private initForm() {
    this.form = this.fb.group({
      title: [{ value: '', disabled: true }, Validators.required],
      groupId: [null, Validators.required]
    });
  }
}
