import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexModule } from '@angular/flex-layout';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { MatInputModule } from '@angular/material/input';
import { MissingTranslationHandler, TranslateModule } from '@ngx-translate/core';
import { ClipboardModule } from '../../../../../common/components/clipboard/clipboard.module';
import { PipesModule } from '../../../../../common/pipes/pipes.module';
import { GameService } from '../../../../../common/services/game.service';
import { GameHistoryService } from '../../../../../common/services/reports/gamehistory.service';
import { SpinBnsComponent } from '../spin-bns/spin-bns.component';
import { SpinDetailsComponent } from '../spin-details/spin-details.component';
import { SpinJackpotComponent } from '../spin-jackpot/spin-jackpot.component';
import { SpinListModule } from '../spin-list/spin-list.module';
import { SpinPhTournamentComponent } from '../spin-ph-tournament/spin-ph-tournament.component';
import { SpinPrizeDropComponent } from '../spin-prize-drop/spin-prize-drop.component';
import { SpinSharedJackpotPrizeComponent } from '../spin-shared-jackpot-prize/spin-shared-jackpot-prize.component';
import { SpinSrtCanvasComponent } from '../spin-srt-canvas/spin-srt-canvas.component';
import { SWMissingTranslationPoolHandler } from './missing-translation-pool.handler';

import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { RoundInfoComponent } from './round-info.component';


@NgModule({
  declarations: [
    RoundInfoComponent,
    SpinJackpotComponent,
    SpinBnsComponent,
    SpinSrtCanvasComponent,
    SpinDetailsComponent,
    SpinPhTournamentComponent,
    SpinPrizeDropComponent,
    SpinSharedJackpotPrizeComponent,
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    TranslateModule.forChild({
      missingTranslationHandler: { provide: MissingTranslationHandler, useClass: SWMissingTranslationPoolHandler },
    }),
    PipesModule,
    ClipboardModule,
    MatButtonModule,
    MatInputModule,
    MatDialogModule,
    FlexModule,
    SpinListModule,
    MatProgressSpinnerModule
  ],
  exports: [
    RoundInfoComponent,
  ],

  providers: [
    GameHistoryService,
    GameService,
  ]
})
export class RoundInfoModule {
}
