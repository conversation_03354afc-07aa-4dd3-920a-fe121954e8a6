import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexModule } from '@angular/flex-layout';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiGridModule, SwuiNotificationsModule, SwuiSchemaTopFilterModule } from '@skywind-group/lib-swui';
import { DownloadCsvModule } from '../../../../common/components/download-csv/download-csv.module';
import { GameService } from '../../../../common/services/game.service';
import { GameNotifyModule } from '../game-notify/game-notify.module';

import { IframeViewModalModule } from '../internal-game-history/modals/iframe-view-modal/iframe-view-modal.module';
import { RoundInfoViewModalModule } from '../internal-game-history/modals/round-info-view-modal.module';
import { GameHistoryGeneralComponent } from './game-history-general.component';


@NgModule({
  declarations: [
    GameHistoryGeneralComponent,
  ],
  exports: [
    GameHistoryGeneralComponent,
  ],
  imports: [
    CommonModule,
    FlexModule,
    TranslateModule,
    SwuiGridModule,
    SwuiNotificationsModule.forRoot(),
    RoundInfoViewModalModule,
    GameNotifyModule,
    MatTooltipModule,
    SwuiSchemaTopFilterModule,
    IframeViewModalModule,
    DownloadCsvModule,
  ],
  providers: [
    GameService,
  ],
})
export class GameHistoryGeneralModule {
}
