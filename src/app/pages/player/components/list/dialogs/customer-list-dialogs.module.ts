import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexLayoutModule } from '@angular/flex-layout';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatSelectModule } from '@angular/material/select';
import { TranslateModule } from '@ngx-translate/core';
import { PipesModule } from '../../../../../common/pipes/pipes.module';
import { SimplePromoService } from '../../../../../common/services/simple-promo.service';
import { MatPlayerFormModule } from '../../mat-player-form/player-form.module';
import { ApplySimpleFreebetDialogComponent } from './apply-simple-freebet-dialog.component';
import { CreatePlayerDialogComponent } from './create-player-dialog.component';


@NgModule({
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    MatPlayerFormModule,
    MatDialogModule,
    MatButtonModule,
    PipesModule,
    MatSelectModule,
    FlexLayoutModule,
    ReactiveFormsModule,
    MatProgressBarModule,
  ],
  declarations: [
    ApplySimpleFreebetDialogComponent,
    CreatePlayerDialogComponent,
  ],
  exports: [],
  providers: [
    SimplePromoService
  ],
})
export class CustomerListDialogsModule {
}
