import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { SwuiGridModule } from '@skywind-group/lib-swui';
import { DownloadCsvModule } from '../../../../../../common/components/download-csv/download-csv.module';
import { GameService } from '../../../../../../common/services/game.service';

import { IframeViewModalModule } from '../../../../../gamehistory/components/internal-game-history/modals/iframe-view-modal/iframe-view-modal.module';

import { CustomerGeneralGameHistoryComponent } from './customer-general-game-history.component';


@NgModule({
  declarations: [
    CustomerGeneralGameHistoryComponent
  ],
  exports: [
    CustomerGeneralGameHistoryComponent
  ],
  imports: [
    CommonModule,
    SwuiGridModule,
    IframeViewModalModule,
    MatButtonModule,
    MatTooltipModule,
    MatIconModule,
    DownloadCsvModule,
  ],
  providers: [
    GameService,
  ],
})
export class CustomerGeneralGameHistoryModule {
}
