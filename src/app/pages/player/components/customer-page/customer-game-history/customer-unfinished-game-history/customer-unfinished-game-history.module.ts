import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiChipsAutocompleteModule, SwuiGridModule } from '@skywind-group/lib-swui';
import { BoConfirmationModule } from 'src/app/common/components/bo-confirmation/bo-confirmation.module';
import { DownloadCsvModule } from '../../../../../../common/components/download-csv/download-csv.module';
import { BaIfAllowedModule } from '../../../../../../common/directives/baIfAllowed/baIfAllowed.module';
import { GameService } from '../../../../../../common/services/game.service';

import { IframeViewModalModule } from '../../../../../gamehistory/components/internal-game-history/modals/iframe-view-modal/iframe-view-modal.module';

import { CustomerUnfinishedGameHistoryComponent } from './customer-unfinished-game-history.component';
import { CustomerUnfinishedGameHistoryService } from './customer-unfinished-game-history.service';


@NgModule({
  declarations: [
    CustomerUnfinishedGameHistoryComponent
  ],
  providers: [
    CustomerUnfinishedGameHistoryService,
    GameService,
  ],
  exports: [
    CustomerUnfinishedGameHistoryComponent
  ],
  imports: [
    CommonModule,
    BoConfirmationModule,
    SwuiChipsAutocompleteModule,
    SwuiGridModule,
    IframeViewModalModule,
    BaIfAllowedModule,
    MatMenuModule,
    TranslateModule,
    MatButtonModule,
    MatTooltipModule,
    MatIconModule,
    DownloadCsvModule,
  ],
})
export class CustomerUnfinishedGameHistoryModule {
}
