import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexLayoutModule } from '@angular/flex-layout';
import { ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { ControlMessagesModule } from '../../../../../common/components/control-messages/control-messages.module';

import { ValidationService } from '../../../../../common/services/validation.service';
import { CustomerRespGamingComponent } from './customer-resp-gaming.component';
import { NoticeModule } from './notice/notice.module';
import { RgDepositLimitCasinoComponent } from './rg-deposit-limit/rg-deposit-limit-casino/rg-deposit-limit-casino.component';
import { RgDepositLimitDialogComponent } from './rg-deposit-limit/rg-deposit-limit-dialog/rg-deposit-limit-dialog.component';
import { RgDepositLimitComponent } from './rg-deposit-limit/rg-deposit-limit.component';
import { RgLossLimitCasinoComponent } from './rg-loss-limit/rg-loss-limit-casino/rg-loss-limit-casino.component';
import { RgLossLimitDialogComponent } from './rg-loss-limit/rg-loss-limit-dialog/rg-loss-limit-dialog.component';
import { RgLossLimitComponent } from './rg-loss-limit/rg-loss-limit.component';
import { RgRealityCheckCasinoComponent } from './rg-reality-check/rg-reality-check-casino/rg-reality-check-casino.component';
import { RgRealityCheckDialogComponent } from './rg-reality-check/rg-reality-check-dialog/rg-reality-check-dialog.component';
import { RgRealityCheckComponent } from './rg-reality-check/rg-reality-check.component';
import { RgSelfExclusionCasinoComponent } from './rg-self-exclusion/rg-self-exclusion-casino/rg-self-exclusion-casino.component';
import { RgSelfExclusionDialogComponent } from './rg-self-exclusion/rg-self-exclusion-dialog/rg-self-exclusion-dialog.component';
import { RgSelfExclusionComponent } from './rg-self-exclusion/rg-self-exclusion.component';
import { RgTimeOutCasinoComponent } from './rg-time-out/rg-time-out-casino/rg-time-out-casino.component';
import { RgTimeOutDialogComponent } from './rg-time-out/rg-time-out-dialog/rg-time-out-dialog.component';
import { RgTimeOutComponent } from './rg-time-out/rg-time-out.component';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatCardModule } from '@angular/material/card';
import { MatTabsModule } from '@angular/material/tabs';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatRadioModule } from '@angular/material/radio';

@NgModule({
  declarations: [
    CustomerRespGamingComponent,
    RgRealityCheckComponent,
    RgRealityCheckCasinoComponent,
    RgLossLimitComponent,
    RgLossLimitCasinoComponent,
    RgDepositLimitComponent,
    RgDepositLimitCasinoComponent,
    RgTimeOutComponent,
    RgTimeOutCasinoComponent,
    RgSelfExclusionComponent,
    RgSelfExclusionCasinoComponent,
    RgRealityCheckDialogComponent,
    RgLossLimitDialogComponent,
    RgDepositLimitDialogComponent,
    RgTimeOutDialogComponent,
    RgSelfExclusionDialogComponent,
  ],
  exports: [CustomerRespGamingComponent],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    ControlMessagesModule,
    NoticeModule,
    TranslateModule,

    MatCardModule,
    MatTabsModule,
    MatButtonModule,
    MatSelectModule,
    FlexLayoutModule,
    MatRadioModule,
    MatFormFieldModule,
    MatInputModule,
  ],
  providers: [
    ValidationService,
  ]
})

export class CustomerRespGamingModule {
}
