import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiPagePanelModule } from '@skywind-group/lib-swui';
import { BaCardModule } from '../../common/components/baCard/baCard.module';
import { GameService } from '../../common/services/game.service';
import { BriefResolver } from '../../common/services/resolvers/brief.resolver';
import { GamesShortInfoResolver } from '../../common/services/resolvers/gamesShortInfo.resolver';
import { MerchantBriefResolver } from '../../common/services/resolvers/merchant-brief.resolver';
import { ReportAuditLogComponent } from './components/audit-log/audit-log.component';
import { ReportCurrencyModule } from './components/currency/report-currency.module';
import { ReportFinancialModule } from './components/financial/report-financial.module';
import { ReportIndexComponent } from './components/index/index.component';
import { ReportKpiComponent } from './components/kpi';
import { ReportPlayerModule } from './components/player/report-player.module';
import { ReportRgModule } from './components/report-rg/report-rg.module';
import { ReportsComponent } from './reports.component';
import { ReportsRoutingModule } from './reports.routing';


@NgModule({
  imports: [
    CommonModule,
    TranslateModule,
    BaCardModule,
    ReportsRoutingModule,
    SwuiPagePanelModule,
    ReportPlayerModule,
    ReportCurrencyModule,
    ReportFinancialModule,
    ReportRgModule
  ],
  declarations: [
    ReportsComponent,
    ReportIndexComponent,
    ReportAuditLogComponent,
    ReportKpiComponent,
  ],
  providers: [
    BriefResolver,
    MerchantBriefResolver,
    GameService,
    GamesShortInfoResolver
  ]
})
export class ReportsSectionModule {
}
