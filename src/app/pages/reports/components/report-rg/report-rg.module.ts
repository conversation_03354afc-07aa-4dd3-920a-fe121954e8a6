import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexLayoutModule } from '@angular/flex-layout';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiGridModule, SwuiNotificationsModule, SwuiPagePanelModule, SwuiSchemaTopFilterModule } from '@skywind-group/lib-swui';

import { ReportRgService } from '../../../../common/services/reports/report-rg.service';
import { ReportRgComponent } from './report-rg.component';


@NgModule({
  imports: [
    CommonModule,
    SwuiPagePanelModule,
    SwuiGridModule,
    SwuiNotificationsModule.forRoot(),
    TranslateModule,
    FlexLayoutModule,
    MatTooltipModule,
    SwuiSchemaTopFilterModule
  ],
  declarations: [
    ReportRgComponent
  ],
  providers: [ReportRgService]
})
export class ReportRgModule {
}
