import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexLayoutModule } from '@angular/flex-layout';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiGridModule, SwuiNotificationsModule, SwuiPagePanelModule, SwuiSchemaTopFilterModule } from '@skywind-group/lib-swui';
import { ReportFinancialComponent } from './financial.component';


@NgModule({
  imports: [
    CommonModule,
    SwuiPagePanelModule,
    SwuiGridModule,
    SwuiNotificationsModule.forRoot(),
    TranslateModule,
    FlexLayoutModule,
    MatTooltipModule,
    SwuiSchemaTopFilterModule
  ],
  declarations: [
    ReportFinancialComponent
  ],
  providers: []
})
export class ReportFinancialModule {
}
