# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage

# Grunt intermediate storage (http://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Compiled binary addons (http://nodejs.org/api/addons.html)
build/Release

# Users Environment Variables
.lock-wscript

# OS generated files #
.DS_Store
ehthumbs.db
Icon?
Thumbs.db

# Node Files #
/node_modules/
/bower_components/
npm-debug.log
/npm-debug.log.*
/.npmrc

# Coverage #
/coverage/

# Typing #
/src/typings/tsd/
/typings/
/tsd_typings/

# Dist #
/dist
/public/__build__/
/src/*/__build__/
/__build__/**
/public/dist/
/src/*/dist/
/dist/**
/.awcache
.webpack.json
/compiled/
dll/

# Doc #
/doc/

# IDE #
.idea/
*.swp

# Angular #
*.ngfactory.ts
*.css.shim.ts
*.ngsummary.json
*.shim.ngstyle.ts
.angular/

# Development #
/src/app/pages/sandbox/

*.metadata.json

# These lines were probably already there
*.map.js

/src/environments/base.ts
.env
