{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { MatTooltip } from '@angular/material/tooltip';\nimport { RowAction } from '@skywind-group/lib-swui';\nimport { BehaviorSubject, fromEvent, Subject, Subscription, zip } from 'rxjs';\nimport { first, map, take, takeUntil } from 'rxjs/operators';\nimport { PERMISSIONS_LIST, PERMISSIONS_NAMES } from '../../../../../app.constants';\nimport { Entity, Entity as EntityModel } from '../../../../../common/models/entity.model';\nimport { bsDialogs } from '../business-structure.service';\nimport { EntityLabelsDialogComponent } from '../dialogs/entity-labels-dialog/entity-labels-dialog.component';\nimport { ShowLimitsModalComponent } from '../dialogs/show-limits-modal/show-limits-modal.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../business-structure.service\";\nimport * as i3 from \"@skywind-group/lib-swui\";\nimport * as i4 from \"../../../../../common/services/entity.service\";\nimport * as i5 from \"@angular/material/dialog\";\nimport * as i6 from \"../../../../../common/services/entity-labels.service\";\nimport * as i7 from \"../../../../../common/services/languages.service\";\nimport * as i8 from \"../../../../../common/services/country.service\";\nconst _c0 = [\"entity-item\", \"\"];\nconst _c1 = a0 => ({\n  \"disabled-cell\": a0\n});\nconst _c2 = a0 => ({\n  width: a0\n});\nconst _c3 = a0 => ({\n  \"disabled-tooltip\": a0\n});\nconst _c4 = () => [\"../entities/setup\", \"p\"];\nconst _c5 = a0 => [\"../entities/setup\", a0, \"p\"];\nfunction EntityItemComponent_mat_icon_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-icon\", 27);\n    i0.ɵɵlistener(\"click\", function EntityItemComponent_mat_icon_4_Template_mat_icon_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onItemClick(ctx_r1.entity));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.expanded ? \"expand_more\" : \"chevron_right\", \" \");\n  }\n}\nfunction EntityItemComponent_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 28);\n    i0.ɵɵtext(1, \"R\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EntityItemComponent_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 29);\n    i0.ɵɵtext(1, \"O\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EntityItemComponent_span_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 30);\n    i0.ɵɵtext(1, \"O\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EntityItemComponent_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 31);\n    i0.ɵɵtext(1, \"S\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EntityItemComponent_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 32);\n    i0.ɵɵtext(1, \"Test\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"ngClass\", \"sw-chip-default\");\n  }\n}\nfunction EntityItemComponent_ng_container_13_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function EntityItemComponent_ng_container_13_button_1_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.handleOpenEdit($event));\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\", 35);\n    i0.ɵɵtext(2, \"edit\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EntityItemComponent_ng_container_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, EntityItemComponent_ng_container_13_button_1_Template, 3, 0, \"button\", 33);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isEntityEditAllowed(ctx_r1.entity));\n  }\n}\nfunction EntityItemComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵelement(1, \"mat-progress-spinner\", 37);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EntityItemComponent_mat_icon_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-icon\", 38);\n    i0.ɵɵlistener(\"mouseenter\", function EntityItemComponent_mat_icon_15_Template_mat_icon_mouseenter_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      return i0.ɵɵresetView($event.stopImmediatePropagation());\n    })(\"mouseleave\", function EntityItemComponent_mat_icon_15_Template_mat_icon_mouseleave_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      return i0.ɵɵresetView($event.stopImmediatePropagation());\n    })(\"click\", function EntityItemComponent_mat_icon_15_Template_mat_icon_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onInfoClick());\n    });\n    i0.ɵɵtext(1, \"info_outline \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵpropertyInterpolate(\"matTooltip\", ctx_r1.infoText);\n  }\n}\nfunction EntityItemComponent_ng_container_20_ng_container_14_p_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 47);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r6 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\"\", item_r6.displayName, \" (\", item_r6.code, \")\");\n  }\n}\nfunction EntityItemComponent_ng_container_20_ng_container_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, EntityItemComponent_ng_container_20_ng_container_14_p_1_Template, 2, 2, \"p\", 46);\n    i0.ɵɵpipe(2, \"async\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(2, 1, ctx_r1.availableItems));\n  }\n}\nfunction EntityItemComponent_ng_container_20_ng_template_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48);\n    i0.ɵɵelement(1, \"mat-progress-bar\", 49);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EntityItemComponent_ng_container_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\", 5)(2, \"div\", 39)(3, \"div\", 40)(4, \"a\", 41);\n    i0.ɵɵlistener(\"click\", function EntityItemComponent_ng_container_20_Template_a_click_4_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.handleOpenItemsMenu($event));\n    });\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \" / \");\n    i0.ɵɵelementStart(7, \"a\", 42);\n    i0.ɵɵlistener(\"click\", function EntityItemComponent_ng_container_20_Template_a_click_7_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.handleOpenItemsMenu($event));\n    });\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(9, \" / \");\n    i0.ɵɵelementStart(10, \"a\", 43);\n    i0.ɵɵlistener(\"click\", function EntityItemComponent_ng_container_20_Template_a_click_10_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.handleOpenItemsMenu($event));\n    });\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"mat-menu\", 44, 2);\n    i0.ɵɵtemplate(14, EntityItemComponent_ng_container_20_ng_container_14_Template, 3, 3, \"ng-container\", 21)(15, EntityItemComponent_ng_container_20_ng_template_15_Template, 2, 0, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"button\", 45);\n    i0.ɵɵpipe(18, \"async\");\n    i0.ɵɵlistener(\"click\", function EntityItemComponent_ng_container_20_Template_button_click_17_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.handleOpenEditRegional($event));\n    });\n    i0.ɵɵelementStart(19, \"mat-icon\", 35);\n    i0.ɵɵtext(20, \"edit\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r7 = i0.ɵɵreference(13);\n    const spinner_r8 = i0.ɵɵreference(16);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(13, _c1, ctx_r1.isEntityRowDisabled));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"matMenuTriggerFor\", item_r7)(\"ngClass\", i0.ɵɵpureFunction1(15, _c3, ctx_r1.disabledCurrencyTooltips));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.entity.defaultCurrency);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"matMenuTriggerFor\", item_r7);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.entity.defaultCountry);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"matMenuTriggerFor\", item_r7);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.entity.defaultLanguage);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isAvailableItemsLoaded)(\"ngIfElse\", spinner_r8);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.entity.isRoot() || i0.ɵɵpipeBind1(18, 11, ctx_r1.editDisabled$));\n  }\n}\nfunction EntityItemComponent_ng_template_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 5)(1, \"div\", 39)(2, \"div\", 40)(3, \"span\", 50);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" / \");\n    i0.ɵɵelementStart(6, \"span\", 51);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8, \" / \");\n    i0.ɵɵelementStart(9, \"span\", 52);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 53)(12, \"mat-icon\", 35);\n    i0.ɵɵtext(13, \"edit\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c1, ctx_r1.isEntityRowDisabled));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.entity.defaultCurrency);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.entity.defaultCountry);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.entity.defaultLanguage);\n  }\n}\nfunction EntityItemComponent_ng_container_25_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 58);\n    i0.ɵɵlistener(\"click\", function EntityItemComponent_ng_container_25_div_8_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.handleOpenConfirmSetStatus($event, \"normal\"));\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\", 59);\n    i0.ɵɵtext(2, \"fiber_manual_record\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 1, \"BUSINESS_STRUCTURE.WIDGETS.active\"), \" \");\n  }\n}\nfunction EntityItemComponent_ng_container_25_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 58);\n    i0.ɵɵlistener(\"click\", function EntityItemComponent_ng_container_25_div_9_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.handleOpenConfirmSetStatus($event, \"suspended\"));\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\", 60);\n    i0.ɵɵtext(2, \"fiber_manual_record\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 1, \"BUSINESS_STRUCTURE.WIDGETS.inactive\"), \" \");\n  }\n}\nfunction EntityItemComponent_ng_container_25_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 58);\n    i0.ɵɵlistener(\"click\", function EntityItemComponent_ng_container_25_div_10_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.handleOpenConfirmSetStatus($event, \"maintenance\"));\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\", 61);\n    i0.ɵɵtext(2, \"fiber_manual_record\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 1, \"BUSINESS_STRUCTURE.WIDGETS.maintenance\"), \" \");\n  }\n}\nfunction EntityItemComponent_ng_container_25_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 58);\n    i0.ɵɵlistener(\"click\", function EntityItemComponent_ng_container_25_div_11_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.handleOpenConfirmSetStatus($event, \"blocked_by_admin\"));\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\", 62);\n    i0.ɵɵtext(2, \"fiber_manual_record\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 1, \"BUSINESS_STRUCTURE.WIDGETS.blocked\"), \" \");\n  }\n}\nfunction EntityItemComponent_ng_container_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 54);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementStart(4, \"mat-icon\", 55);\n    i0.ɵɵtext(5, \"arrow_drop_down\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"mat-menu\", 56, 4);\n    i0.ɵɵtemplate(8, EntityItemComponent_ng_container_25_div_8_Template, 5, 3, \"div\", 57)(9, EntityItemComponent_ng_container_25_div_9_Template, 5, 3, \"div\", 57)(10, EntityItemComponent_ng_container_25_div_10_Template, 5, 3, \"div\", 57)(11, EntityItemComponent_ng_container_25_div_11_Template, 5, 3, \"div\", 57);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const statusMenu_r13 = i0.ɵɵreference(7);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"sw-chip-green\", ctx_r1.entity.status === \"normal\")(\"sw-chip-default\", ctx_r1.entity.status === \"suspended\")(\"sw-chip-orange\", ctx_r1.entity.status === \"maintenance\")(\"sw-chip-red\", ctx_r1.entity.status === \"blocked_by_admin\")(\"sw-chip-blue\", ctx_r1.entity.status === \"test\");\n    i0.ɵɵproperty(\"matMenuTriggerFor\", statusMenu_r13);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.statusUpdating ? \"...\" : i0.ɵɵpipeBind1(3, 16, ctx_r1.entityItemStatusMap[ctx_r1.entity.status].title), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.entity.status !== \"normal\" && ctx_r1.canChangeState || ctx_r1.entity.status === \"test\" && ctx_r1.canChangeTest);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.entity.status !== \"suspended\" && ctx_r1.canChangeState);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.entity.status !== \"maintenance\" && ctx_r1.canChangeState);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.entity.status !== \"blocked_by_admin\" && ctx_r1.isSuperAdmin);\n  }\n}\nfunction EntityItemComponent_ng_template_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵpipe(1, \"translate\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"sw-chip-green\", ctx_r1.entity.status === \"normal\")(\"sw-chip-default\", ctx_r1.entity.status === \"suspended\")(\"sw-chip-orange\", ctx_r1.entity.status === \"maintenance\")(\"sw-chip-red\", ctx_r1.entity.status === \"blocked_by_admin\")(\"sw-chip-blue\", ctx_r1.entity.status === \"test\");\n    i0.ɵɵproperty(\"title\", ctx_r1.entity.status !== \"blocked_by_admin\" ? \"\" : i0.ɵɵpipeBind1(1, 12, \"BUSINESS_STRUCTURE.WIDGETS.blockedByAdmin\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 14, ctx_r1.entityItemStatusMap[ctx_r1.entity.status].title), \" \");\n  }\n}\nfunction EntityItemComponent_a_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 64);\n    i0.ɵɵpipe(1, \"translate\");\n    i0.ɵɵelementStart(2, \"mat-icon\", 35);\n    i0.ɵɵtext(3, \"settings\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵpropertyInterpolate(\"matTooltip\", i0.ɵɵpipeBind1(1, 3, \"BUSINESS_STRUCTURE.WIDGETS.settings\"));\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isEntityRowDisabled)(\"routerLink\", ctx_r1.entity.isRoot() ? i0.ɵɵpureFunction0(5, _c4) : i0.ɵɵpureFunction1(6, _c5, ctx_r1.entity.path));\n  }\n}\nexport class EntityItemComponent {\n  set entity(val) {\n    if (!val) {\n      return;\n    }\n    console.log(val);\n    this._entity = val;\n    this.isEntitySuspended = val.changeStatusState !== 'normal';\n    this.canChangeStatus = val.changeStatusState === 'blocked';\n    this.isEntityRowDisabled = this.isEntitySuspended && !val.isRoot();\n    this.initRowActions = this.setRowActions();\n  }\n  get entity() {\n    return this._entity;\n  }\n  set initRowActions(val) {\n    if (!val) {\n      return;\n    }\n    this._rowActions = val;\n  }\n  get rowActions() {\n    return this._rowActions;\n  }\n  set isEntitySuspended(val) {\n    this._isEntitySuspended = val;\n  }\n  get isEntitySuspended() {\n    return this._isEntitySuspended;\n  }\n  set canChangeStatus(val) {\n    this._canChangeStatus = val;\n  }\n  get canChangeStatus() {\n    return this._canChangeStatus;\n  }\n  constructor(route, bsService, authService, entityService, cd, dialog, entityLabelsService, languageService, countryService, settingsService, router) {\n    this.route = route;\n    this.bsService = bsService;\n    this.authService = authService;\n    this.entityService = entityService;\n    this.cd = cd;\n    this.dialog = dialog;\n    this.entityLabelsService = entityLabelsService;\n    this.languageService = languageService;\n    this.countryService = countryService;\n    this.settingsService = settingsService;\n    this.router = router;\n    this.expanded = false;\n    this.disabledCurrencyTooltips = false;\n    this.canChangeTest = false;\n    this.onClick = new EventEmitter();\n    this.entityItemStatusMap = {\n      'normal': {\n        class: 'success',\n        color: 'primary',\n        title: 'BUSINESS_STRUCTURE.WIDGETS.active'\n      },\n      'suspended': {\n        class: 'default',\n        color: 'accent',\n        title: 'BUSINESS_STRUCTURE.WIDGETS.inactive'\n      },\n      'maintenance': {\n        class: 'warning',\n        color: 'warn',\n        title: 'BUSINESS_STRUCTURE.WIDGETS.maintenance'\n      },\n      'blocked_by_admin': {\n        class: 'danger',\n        color: 'warn',\n        title: 'BUSINESS_STRUCTURE.WIDGETS.blocked'\n      },\n      'test': {\n        class: 'danger',\n        color: 'blue',\n        title: 'BUSINESS_STRUCTURE.WIDGETS.test'\n      }\n    };\n    this.isAvailableItemsLoaded = false;\n    this.merchantLoading = false;\n    this.balancesLoading = true;\n    this.statusUpdating = false;\n    this.entityUpdate = false;\n    this.infoText = '';\n    this.isEntityRowDisabled = false;\n    this.availableItems = new BehaviorSubject([]);\n    this.isSuperAdmin = false;\n    this.editDisabled$ = new BehaviorSubject(false);\n    this.canChangeState = false;\n    this.menuHasItems = false;\n    this._isEntitySuspended = false;\n    this._canChangeStatus = false;\n    this.destroyed$ = new Subject();\n    this.tooltipSubscription = new Subscription();\n    this.numberOfClicks = 0;\n    this._rowActions = [];\n    const {\n      brief\n    } = this.route.snapshot.data;\n    const briefEntity = new EntityModel(brief);\n    this.isBrand = briefEntity.type === Entity.TYPE_BRAND || briefEntity.type === Entity.TYPE_MERCHANT;\n    this.isSuperAdmin = this.authService.isSuperAdmin;\n  }\n  ngOnChanges({\n    entity\n  }) {\n    if (entity?.currentValue) {\n      const decoded = this.entity.decryptedBrand && `id (decoded): ${this.entity.decryptedBrand}\\n` || '';\n      this.infoText = `${decoded}id (encoded): ${this.entity.id}\\npath: ${this.entity.path}`;\n    }\n  }\n  ngOnInit() {\n    this.canChangeState = this.authService.allowedTo([PERMISSIONS_NAMES.ENTITY_CHANGESTATE]) && !this.entity.isRoot();\n    this.menuHasItems = (this.canChangeState && this.entity.status !== 'test' || ['test'].includes(this.entity.status) && this.canChangeTest && !this.entity.isReseller()) && this.allowedStatuses() && !this.entity.isRoot();\n  }\n  handleOpenItemsMenu(event) {\n    this.availableItems.next([]);\n    this.isAvailableItemsLoaded = false;\n    let source;\n    switch (event.target.id) {\n      case 'currency':\n        source = this.entityService.getBalances(this.entity.path).pipe(map(balances => {\n          return Object.keys(balances).reduce((acc, key) => {\n            const currencyFormat = this.settingsService.appSettings.currencyFormat;\n            const currencySymbol = new Intl.NumberFormat(currencyFormat, {\n              style: 'currency',\n              currency: key,\n              minimumFractionDigits: 0,\n              maximumFractionDigits: 0\n            }).format(0).replace(/\\d/g, '').trim();\n            const balanceFormat = new Intl.NumberFormat(currencyFormat, {\n              style: 'currency',\n              currency: key\n            }).format(balances[key].main).replace(currencySymbol, '').replace(/\\s+/g, '');\n            acc.push({\n              code: balanceFormat,\n              displayName: key\n            });\n            return acc;\n          }, []);\n        }));\n        break;\n      case 'country':\n        source = this.countryService.getList('', this.entity.path);\n        break;\n      case 'language':\n        source = this.languageService.getList('', this.entity.path).pipe(map(languages => {\n          return languages.map(language => ({\n            code: language.code,\n            displayName: language.name\n          }));\n        }));\n        break;\n      default:\n        break;\n    }\n    source.pipe(take(1)).subscribe(items => {\n      this.availableItems.next(items);\n      this.isAvailableItemsLoaded = true;\n      this.cd.detectChanges();\n    });\n  }\n  onInfoClick() {\n    if (!this.tooltip) {\n      return;\n    }\n    this.tooltipSubscription.unsubscribe();\n    this.tooltip.show();\n    const tooltip = document.getElementsByTagName('mat-tooltip-component')[0];\n    this.tooltipSubscription = fromEvent(tooltip, 'click').pipe(takeUntil(this.destroyed$)).subscribe(e => {\n      e.stopPropagation();\n    });\n  }\n  ngOnDestroy() {\n    this.destroyed$.next();\n    this.destroyed$.complete();\n    this.tooltipSubscription.unsubscribe();\n  }\n  isAddCascadeGamesAllowed() {\n    return this.authService.areGranted(PERMISSIONS_LIST.GAMES_CASCADE_ADD);\n  }\n  isEntityEditAllowed(entity) {\n    let allowed = false;\n    if (entity && !entity.isRoot()) {\n      allowed = this.authService.allowedTo([PERMISSIONS_NAMES.ENTITY_EDIT]);\n    }\n    return allowed;\n  }\n  isEntityInfoAllowed(entity) {\n    let allowed = false;\n    if (entity && !entity.isRoot()) {\n      allowed = this.authService.allowedTo([PERMISSIONS_NAMES.ENTITY_INFO]);\n    }\n    return allowed;\n  }\n  handleOpenEdit(event) {\n    event.preventDefault();\n    this.singleClick(() => this.bsService.openDialog(bsDialogs.ENTITY_EDIT, this.entity));\n  }\n  handleOpenEditRegional(event) {\n    event.preventDefault();\n    this.editDisabled$.next(true);\n    this.bsService.openDialog(bsDialogs.EDIT_REGIONAL, this.entity, event.target.id).pipe(take(1)).subscribe(() => {\n      this.editDisabled$.next(false);\n    });\n  }\n  handleOpenConfirmSetStatus(event, confirmStatus) {\n    event.preventDefault();\n    this.bsService.openDialog(bsDialogs.SET_STATUS_CONFIRM, this.entity, {\n      confirmStatus\n    });\n  }\n  allowedStatuses() {\n    if (!this.isSuperAdmin) {\n      if (this.entity.status === 'test') {\n        return this.canChangeTest;\n      } else {\n        return this.entity.status !== 'blocked_by_admin' && (this.canChangeStatus || !this.isEntitySuspended);\n      }\n    }\n    return this.isSuperAdmin;\n  }\n  fetchBalances(event) {\n    event.preventDefault();\n    const hasBalancePermission = this.authService.allowedTo([PERMISSIONS_NAMES.ENTITY_BALANCE]);\n    if (hasBalancePermission) {\n      this._fetchBalances();\n    } else {\n      this.balancesLoading = false;\n      this.entity.currencies.forEach(currency => {\n        this.entity.balances[currency] = {};\n      });\n    }\n  }\n  isCreditDebitAllowed() {\n    return this.authService.allowedTo([PERMISSIONS_NAMES.FINANCE_CREDIT, PERMISSIONS_NAMES.FINANCE_DEBIT]);\n  }\n  isCountryAddAllowed() {\n    return this.authService.allowedTo([PERMISSIONS_NAMES.COUNTRY_ADD]);\n  }\n  isShowGameLimitsAllowed() {\n    return this.authService.allowedTo(PERMISSIONS_LIST.GAME_GROUP_VIEW);\n  }\n  isLabelsManagementAllowed() {\n    return this.entity.isRoot() && this.authService.allowedTo([PERMISSIONS_NAMES.KEYENTITY_ENTITYLABELS_VIEW]) || !this.entity.isRoot() && this.authService.allowedTo([PERMISSIONS_NAMES.ENTITYLABELS_VIEW]);\n  }\n  onItemClick(entity) {\n    this.onClick.emit(entity.id);\n  }\n  isAddChildAllowed() {\n    return this.authService.allowedTo([PERMISSIONS_NAMES.ENTITY_CREATE]);\n  }\n  _fetchBalances() {\n    const parentHasBalances = !this.entity.isRoot() && this.entity.entityParent.hasDefinedBalances();\n    if (this.entity && this.entity.hasDefinedBalances() && (parentHasBalances || this.entity.isRoot())) {\n      this.balancesLoading = false;\n      return;\n    }\n    zip(this.entityService.getBalances(this.entity.path), this.entityService.getBalances(this.entity.entityParent.path)).pipe(first()).subscribe(([balances, parentBalances]) => {\n      this.entity.balances = balances;\n      if (this.entity.entityParent) {\n        this.entity.entityParent.balances = parentBalances;\n      }\n      this.balancesLoading = false;\n      this.cd.detectChanges();\n    }, err => {\n      console.error(err);\n    });\n  }\n  singleClick(callback) {\n    this.numberOfClicks++;\n    if (this.numberOfClicks <= 1) {\n      callback();\n      setTimeout(() => {\n        this.numberOfClicks = 0;\n      }, 2000);\n    }\n  }\n  setRowActions() {\n    const entityRowActions = [];\n    if (this.entity?.type === 'entity' && this.isAddChildAllowed()) {\n      const addChildren = new RowAction({\n        title: 'BUSINESS_STRUCTURE.WIDGETS.addChild',\n        icon: 'add_box',\n        fn: () => this.bsService.openDialog(bsDialogs.ENTITY_ADD, this.entity),\n        canActivateFn: () => true\n      });\n      entityRowActions.push(addChildren);\n    }\n    if (!this.entity?.isMaster() && !this.isBrand && this.authService.allowedTo(PERMISSIONS_LIST.ENTITY_MOVE)) {\n      const move = new RowAction({\n        title: 'BUSINESS_STRUCTURE.WIDGETS.move',\n        icon: 'format_line_spacing',\n        fn: () => this.router.navigate(['/pages/business-management/entities/move/move', this.entity.key]),\n        canActivateFn: () => true\n      });\n      entityRowActions.push(move);\n    }\n    if (!this.entity?.isMaster() && this.isAddCascadeGamesAllowed() && !this.isBrand && !this.entity?.isRoot()) {\n      const addCascade = new RowAction({\n        title: 'BUSINESS_STRUCTURE.WIDGETS.addCascade',\n        icon: 'library_add',\n        fn: () => this.router.navigate(['/pages/business-management/entities/cascade-games/add', this.entity.path]),\n        canActivateFn: () => true\n      });\n      entityRowActions.push(addCascade);\n    }\n    if (this.isShowGameLimitsAllowed()) {\n      const addCascade = new RowAction({\n        title: 'BUSINESS_STRUCTURE.WIDGETS.showGameLimits',\n        icon: 'attach_money',\n        fn: () => this.dialog.open(ShowLimitsModalComponent, {\n          width: '80vw',\n          panelClass: 'limits-dialog-body',\n          data: this.entity,\n          disableClose: true\n        }),\n        canActivateFn: () => true\n      });\n      entityRowActions.push(addCascade);\n    }\n    if (this.isLabelsManagementAllowed()) {\n      const labels = new RowAction({\n        title: 'BUSINESS_STRUCTURE.WIDGETS.labels',\n        icon: 'label',\n        fn: () => this.entityLabelsService.getLabelGroups('entity').pipe(take(1)).subscribe(groups => {\n          return this.dialog.open(EntityLabelsDialogComponent, {\n            width: '90vw',\n            disableClose: true,\n            data: {\n              entity: this.entity,\n              labelGroupsSelectOptions: groups.map(group => {\n                return {\n                  id: group.id,\n                  text: group.group\n                };\n              })\n            }\n          });\n        }),\n        canActivateFn: () => true\n      });\n      entityRowActions.push(labels);\n    }\n    return entityRowActions;\n  }\n  static {\n    this.ɵfac = function EntityItemComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || EntityItemComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.BusinessStructureService), i0.ɵɵdirectiveInject(i3.SwHubAuthService), i0.ɵɵdirectiveInject(i4.EntityService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i5.MatDialog), i0.ɵɵdirectiveInject(i6.EntityLabelsService), i0.ɵɵdirectiveInject(i7.LanguagesService), i0.ɵɵdirectiveInject(i8.CountryService), i0.ɵɵdirectiveInject(i3.SettingsService), i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: EntityItemComponent,\n      selectors: [[\"\", \"entity-item\", \"\"]],\n      viewQuery: function EntityItemComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(MatTooltip, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.tooltip = _t.first);\n        }\n      },\n      inputs: {\n        expanded: \"expanded\",\n        disabledCurrencyTooltips: \"disabledCurrencyTooltips\",\n        canChangeTest: \"canChangeTest\",\n        entity: \"entity\"\n      },\n      outputs: {\n        onClick: \"onClick\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature],\n      attrs: _c0,\n      decls: 32,\n      vars: 38,\n      consts: [[\"regionals\", \"\"], [\"readonlyState\", \"\"], [\"item\", \"\"], [\"spinner\", \"\"], [\"statusMenu\", \"matMenu\"], [3, \"ngClass\"], [1, \"name-wrapper\"], [1, \"bs-entity\"], [1, \"bs-entity__arrow\"], [3, \"click\", 4, \"ngIf\"], [3, \"ngSwitch\"], [\"class\", \"bs-entity__type bs-type bs-type--entity\", \"title\", \"Reseller\", 4, \"ngSwitchCase\"], [\"class\", \"bs-entity__type bs-type bs-type--brand\", \"title\", \"Operator\", 4, \"ngSwitchCase\"], [\"class\", \"bs-entity__type bs-type bs-type--merchant\", \"title\", \"Merchant\", 4, \"ngSwitchCase\"], [\"class\", \"bs-entity__type bs-type bs-type--studio\", \"title\", \"Live Studio\", 4, \"ngSwitchCase\"], [1, \"bs-entity__title\"], [\"class\", \"sw-chip\", 3, \"ngClass\", 4, \"ngIf\"], [4, \"ngIf\"], [\"class\", \"bs-entity__spinner\", 4, \"ngIf\"], [\"class\", \"info-link\", 3, \"matTooltip\", \"mouseenter\", \"mouseleave\", \"click\", 4, \"ngIf\"], [2, \"white-space\", \"nowrap\", 3, \"ngClass\"], [4, \"ngIf\", \"ngIfElse\"], [2, \"text-align\", \"center\", 3, \"ngClass\"], [1, \"bs-status\"], [2, \"padding-right\", \"24px\", \"text-align\", \"center\", \"display\", \"flex\", \"justify-content\", \"flex-end\"], [\"class\", \"menu-item-link\", \"mat-menu-item\", \"\", 3, \"matTooltip\", \"disabled\", \"routerLink\", 4, \"ngIf\"], [3, \"actions\", \"ignorePlainLink\"], [3, \"click\"], [\"title\", \"Reseller\", 1, \"bs-entity__type\", \"bs-type\", \"bs-type--entity\"], [\"title\", \"Operator\", 1, \"bs-entity__type\", \"bs-type\", \"bs-type--brand\"], [\"title\", \"Merchant\", 1, \"bs-entity__type\", \"bs-type\", \"bs-type--merchant\"], [\"title\", \"Live Studio\", 1, \"bs-entity__type\", \"bs-type\", \"bs-type--studio\"], [1, \"sw-chip\", 3, \"ngClass\"], [\"mat-icon-button\", \"\", \"class\", \"bs-entity__edit\", 3, \"click\", 4, \"ngIf\"], [\"mat-icon-button\", \"\", 1, \"bs-entity__edit\", 3, \"click\"], [\"fontSet\", \"material-icons-outline\"], [1, \"bs-entity__spinner\"], [\"diameter\", \"14\", \"mode\", \"indeterminate\"], [1, \"info-link\", 3, \"mouseenter\", \"mouseleave\", \"click\", \"matTooltip\"], [1, \"bs-regional\"], [1, \"bs-regional__val\"], [\"matTooltip\", \"Currency\", \"id\", \"currency\", 3, \"click\", \"matMenuTriggerFor\", \"ngClass\"], [\"matTooltip\", \"Country\", \"id\", \"country\", 3, \"click\", \"matMenuTriggerFor\"], [\"matTooltip\", \"Language\", \"id\", \"language\", 3, \"click\", \"matMenuTriggerFor\"], [1, \"regional-item\"], [\"mat-icon-button\", \"\", 1, \"editRegionBtn\", 3, \"click\", \"disabled\"], [\"mat-menu-item\", \"\", 4, \"ngFor\", \"ngForOf\"], [\"mat-menu-item\", \"\"], [1, \"bs-regional__spinner\"], [\"mode\", \"indeterminate\"], [\"matTooltip\", \"Currency\"], [\"matTooltip\", \"Country\"], [\"matTooltip\", \"Language\"], [\"disabled\", \"\", \"mat-icon-button\", \"\", 1, \"editRegionBtn\"], [1, \"bs-status__chip\", \"bs-status__chip--menu\", \"sw-chip\", 3, \"matMenuTriggerFor\"], [1, \"bs-status__chevron\"], [\"xPosition\", \"before\", 1, \"bs-status__menu\"], [\"mat-menu-item\", \"\", 3, \"click\", 4, \"ngIf\"], [\"mat-menu-item\", \"\", 3, \"click\"], [1, \"bs-status__circle\", \"sw-color-green\"], [1, \"bs-status__circle\", \"sw-color-grey\"], [1, \"bs-status__circle\", \"sw-color-orange\"], [1, \"bs-status__circle\", \"sw-color-deep-orange\"], [1, \"bs-status__chip\", \"sw-chip\", 3, \"title\"], [\"mat-menu-item\", \"\", 1, \"menu-item-link\", 3, \"matTooltip\", \"disabled\", \"routerLink\"]],\n      template: function EntityItemComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"td\", 5)(1, \"div\", 6)(2, \"div\", 7)(3, \"div\", 8);\n          i0.ɵɵtemplate(4, EntityItemComponent_mat_icon_4_Template, 2, 1, \"mat-icon\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementContainerStart(5, 10);\n          i0.ɵɵtemplate(6, EntityItemComponent_span_6_Template, 2, 0, \"span\", 11)(7, EntityItemComponent_span_7_Template, 2, 0, \"span\", 12)(8, EntityItemComponent_span_8_Template, 2, 0, \"span\", 13)(9, EntityItemComponent_span_9_Template, 2, 0, \"span\", 14);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementStart(10, \"span\", 15);\n          i0.ɵɵtext(11);\n          i0.ɵɵtemplate(12, EntityItemComponent_span_12_Template, 2, 1, \"span\", 16)(13, EntityItemComponent_ng_container_13_Template, 2, 1, \"ng-container\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(14, EntityItemComponent_div_14_Template, 2, 0, \"div\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(15, EntityItemComponent_mat_icon_15_Template, 2, 1, \"mat-icon\", 19);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"td\", 5);\n          i0.ɵɵtext(17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"td\", 20);\n          i0.ɵɵtext(19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(20, EntityItemComponent_ng_container_20_Template, 21, 17, \"ng-container\", 21)(21, EntityItemComponent_ng_template_21_Template, 14, 6, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementStart(23, \"td\", 22)(24, \"div\", 23);\n          i0.ɵɵtemplate(25, EntityItemComponent_ng_container_25_Template, 12, 18, \"ng-container\", 21)(26, EntityItemComponent_ng_template_26_Template, 4, 16, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(28, \"td\", 24)(29, \"div\");\n          i0.ɵɵtemplate(30, EntityItemComponent_a_30_Template, 4, 8, \"a\", 25);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(31, \"lib-swui-grid-row-actions\", 26);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          const regionals_r14 = i0.ɵɵreference(22);\n          const readonlyState_r15 = i0.ɵɵreference(27);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(28, _c1, ctx.isEntityRowDisabled));\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleProp(\"padding-left\", (ctx.entity.level + 1) * 20 + \"px\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.entity.children.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngSwitch\", ctx.entity.type);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngSwitchCase\", \"entity\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngSwitchCase\", \"brand\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngSwitchCase\", \"merchant\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngSwitchCase\", \"liveStudio\");\n          i0.ɵɵadvance();\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction1(30, _c2, ctx.bsService.maxLength - ctx.entity.level * 20 + \"px\"));\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", ctx.entity.title || ctx.entity.name, \" \");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.entity.isTest);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isEntitySuspended);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.merchantLoading || ctx.entityUpdate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isEntityInfoAllowed(ctx.entity));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(32, _c1, ctx.isEntityRowDisabled));\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", ctx.entity.name, \"\\n\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(34, _c1, ctx.isEntityRowDisabled));\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", ctx.entity.key, \"\\n\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isEntitySuspended || ctx.entity.isRoot())(\"ngIfElse\", regionals_r14);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(36, _c1, ctx.isEntitySuspended && !ctx.canChangeStatus && !ctx.entity.isRoot()));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.menuHasItems)(\"ngIfElse\", readonlyState_r15);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", !ctx.entity.isMaster() && !ctx.isBrand);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"actions\", ctx.rowActions)(\"ignorePlainLink\", true);\n        }\n      },\n      styles: [\".name-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n}\\n\\n.info-link[_ngcontent-%COMP%] {\\n  color: black;\\n  cursor: pointer;\\n  transition: opacity 0.25s;\\n  opacity: 0.5;\\n}\\n.info-link[_ngcontent-%COMP%]:hover {\\n  opacity: 1;\\n}\\n\\n  .mat-tooltip {\\n  white-space: pre-line;\\n  pointer-events: all;\\n  max-width: none !important;\\n}\\n\\ntd[_ngcontent-%COMP%] {\\n  border-bottom-color: rgba(0, 0, 0, 0.12);\\n  border-bottom-width: 1px;\\n  border-bottom-style: solid;\\n  text-align: left;\\n  padding: 0 8px;\\n  font-weight: 400;\\n  font-size: 14px;\\n  white-space: nowrap;\\n  height: 48px;\\n}\\ntd[_ngcontent-%COMP%]:first-child {\\n  padding-left: 0;\\n}\\ntd[_ngcontent-%COMP%]:first-child:hover   .edit-entity[_ngcontent-%COMP%] {\\n  display: inline-block;\\n}\\ntd[_ngcontent-%COMP%]:first-child   .toggle-expand-button[_ngcontent-%COMP%] {\\n  padding: 0;\\n  margin: 0;\\n  visibility: hidden;\\n}\\ntd[_ngcontent-%COMP%]:first-child   .expand-visible[_ngcontent-%COMP%] {\\n  visibility: visible;\\n}\\n\\n.bs-regional[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: flex-end;\\n  cursor: pointer;\\n}\\n.bs-regional__val[_ngcontent-%COMP%] {\\n  text-transform: uppercase;\\n  font-weight: 500;\\n}\\n.bs-regional[_ngcontent-%COMP%]:hover   .editRegionBtn[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n.bs-regional__text[_ngcontent-%COMP%] {\\n  margin-left: 4px;\\n}\\n.bs-regional__spinner[_ngcontent-%COMP%] {\\n  width: 200px;\\n  height: 50px;\\n  display: flex;\\n  align-items: center;\\n  padding: 0 16px;\\n}\\n\\n.bs-status__chip[_ngcontent-%COMP%] {\\n  min-width: 120px;\\n  font-size: 14px;\\n  justify-content: center;\\n}\\n.bs-status__chip--menu[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n}\\n.bs-status__chevron[_ngcontent-%COMP%] {\\n  width: 14px;\\n}\\n.bs-status__circle[_ngcontent-%COMP%] {\\n  margin-right: 4px;\\n  vertical-align: middle;\\n  width: 16px;\\n  height: 16px;\\n  font-size: 16px;\\n  margin-bottom: 2px;\\n}\\n\\n.bs-menu__add[_ngcontent-%COMP%] {\\n  position: relative;\\n  height: 48px;\\n  padding: 0 16px 0 12px;\\n  display: flex;\\n  align-items: center;\\n  width: 100%;\\n  margin-bottom: 0 !important;\\n  color: rgba(0, 0, 0, 0.87);\\n  cursor: pointer;\\n}\\n.bs-menu__add[_ngcontent-%COMP%]:after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  display: block;\\n  width: 100%;\\n  height: 1px;\\n  background-color: rgba(0, 0, 0, 0.12);\\n}\\n.bs-menu__icon[_ngcontent-%COMP%] {\\n  margin-right: 4px;\\n}\\n.bs-menu__dropdown[_ngcontent-%COMP%] {\\n  max-height: 240px;\\n  overflow: auto;\\n}\\n.bs-menu__item.default[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n}\\n\\n.bs-entity[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n.bs-entity__arrow[_ngcontent-%COMP%] {\\n  position: absolute;\\n  margin: 10px 0 0 -16px;\\n}\\n.bs-entity__arrow[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n}\\n.bs-entity__type[_ngcontent-%COMP%] {\\n  margin: 0 8px;\\n}\\n.bs-entity__edit[_ngcontent-%COMP%] {\\n  visibility: hidden;\\n  opacity: 0;\\n  width: 24px;\\n  height: 24px;\\n  margin-bottom: 2px;\\n  margin-left: 4px;\\n  color: rgba(0, 0, 0, 0.54);\\n  line-height: 24px;\\n  transition: all 0.15s ease-in-out;\\n}\\n.bs-entity__edit[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n  font-size: 20px;\\n}\\n.bs-entity__edit[_ngcontent-%COMP%]:hover {\\n  color: rgba(0, 0, 0, 0.92);\\n}\\n.bs-entity[_ngcontent-%COMP%]:hover   .bs-entity__edit[_ngcontent-%COMP%] {\\n  visibility: visible;\\n  opacity: 1;\\n}\\n.bs-entity__spinner[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-left: 4px;\\n  overflow: hidden;\\n}\\n\\n.bs-type[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 50%;\\n  width: 24px;\\n  height: 24px;\\n  color: #fff;\\n}\\n.bs-type--entity[_ngcontent-%COMP%] {\\n  background-color: #6b67bc;\\n}\\n.bs-type--brand[_ngcontent-%COMP%] {\\n  background-color: #bc67a3;\\n}\\n.bs-type--merchant[_ngcontent-%COMP%] {\\n  background-color: #67BCAB;\\n}\\n.bs-type--studio[_ngcontent-%COMP%] {\\n  background-color: #7CB342;\\n}\\n\\n.menu-item-link[_ngcontent-%COMP%] {\\n  padding-right: unset;\\n  text-transform: none;\\n  color: initial;\\n}\\n.menu-item-link[_ngcontent-%COMP%]:hover {\\n  color: initial;\\n}\\n\\n.limits-dialog-body[_ngcontent-%COMP%] {\\n  display: block;\\n  min-width: 400px;\\n  min-height: 200px;\\n  max-height: 80vh;\\n}\\n\\n.editRegionBtn[_ngcontent-%COMP%] {\\n  opacity: 0;\\n}\\n.editRegionBtn[_ngcontent-%COMP%]:hover {\\n  opacity: 1;\\n}\\n\\n.disabled-cell[_ngcontent-%COMP%] {\\n  opacity: 0.5;\\n}\\n\\n.disabled-tooltip[_ngcontent-%COMP%] {\\n  opacity: 0.5;\\n  pointer-events: none;\\n}\\n\\n  .regional-item {\\n  max-height: 300px;\\n  overflow-y: auto !important;\\n  display: flex;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      changeDetection: 0\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "MatTooltip", "RowAction", "BehaviorSubject", "fromEvent", "Subject", "Subscription", "zip", "first", "map", "take", "takeUntil", "PERMISSIONS_LIST", "PERMISSIONS_NAMES", "Entity", "EntityModel", "bsDialogs", "EntityLabelsDialogComponent", "ShowLimitsModalComponent", "i0", "ɵɵelementStart", "ɵɵlistener", "EntityItemComponent_mat_icon_4_Template_mat_icon_click_0_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onItemClick", "entity", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "expanded", "ɵɵproperty", "EntityItemComponent_ng_container_13_button_1_Template_button_click_0_listener", "$event", "_r3", "handleOpenEdit", "ɵɵelementContainerStart", "ɵɵtemplate", "EntityItemComponent_ng_container_13_button_1_Template", "isEntityEditAllowed", "ɵɵelement", "EntityItemComponent_mat_icon_15_Template_mat_icon_mouseenter_0_listener", "_r4", "stopImmediatePropagation", "EntityItemComponent_mat_icon_15_Template_mat_icon_mouseleave_0_listener", "EntityItemComponent_mat_icon_15_Template_mat_icon_click_0_listener", "onInfoClick", "ɵɵpropertyInterpolate", "infoText", "ɵɵtextInterpolate2", "item_r6", "displayName", "code", "EntityItemComponent_ng_container_20_ng_container_14_p_1_Template", "ɵɵpipeBind1", "availableItems", "EntityItemComponent_ng_container_20_Template_a_click_4_listener", "_r5", "handleOpenItemsMenu", "EntityItemComponent_ng_container_20_Template_a_click_7_listener", "EntityItemComponent_ng_container_20_Template_a_click_10_listener", "EntityItemComponent_ng_container_20_ng_container_14_Template", "EntityItemComponent_ng_container_20_ng_template_15_Template", "ɵɵtemplateRefExtractor", "EntityItemComponent_ng_container_20_Template_button_click_17_listener", "handleOpenEditRegional", "ɵɵpureFunction1", "_c1", "isEntityRowDisabled", "item_r7", "_c3", "disabledCurrencyTooltips", "ɵɵtextInterpolate", "defaultCurrency", "defaultCountry", "defaultLanguage", "isAvailableItemsLoaded", "spinner_r8", "isRoot", "editDisabled$", "EntityItemComponent_ng_container_25_div_8_Template_div_click_0_listener", "_r9", "handleOpenConfirmSetStatus", "EntityItemComponent_ng_container_25_div_9_Template_div_click_0_listener", "_r10", "EntityItemComponent_ng_container_25_div_10_Template_div_click_0_listener", "_r11", "EntityItemComponent_ng_container_25_div_11_Template_div_click_0_listener", "_r12", "EntityItemComponent_ng_container_25_div_8_Template", "EntityItemComponent_ng_container_25_div_9_Template", "EntityItemComponent_ng_container_25_div_10_Template", "EntityItemComponent_ng_container_25_div_11_Template", "ɵɵclassProp", "status", "statusMenu_r13", "statusUpdating", "entityItemStatusMap", "title", "canChangeState", "canChangeTest", "isSuperAdmin", "ɵɵpureFunction0", "_c4", "_c5", "path", "EntityItemComponent", "val", "console", "log", "_entity", "isEntitySuspended", "changeStatusState", "canChangeStatus", "initRowActions", "setRowActions", "_rowActions", "rowActions", "_isEntitySuspended", "_canChangeStatus", "constructor", "route", "bsService", "authService", "entityService", "cd", "dialog", "entityLabelsService", "languageService", "countryService", "settingsService", "router", "onClick", "class", "color", "merchantLoading", "balancesLoading", "entityUpdate", "menuHasItems", "destroyed$", "tooltipSubscription", "numberOfClicks", "brief", "snapshot", "data", "briefEntity", "isBrand", "type", "TYPE_BRAND", "TYPE_MERCHANT", "ngOnChanges", "currentValue", "decoded", "decry<PERSON><PERSON><PERSON>", "id", "ngOnInit", "allowedTo", "ENTITY_CHANGESTATE", "includes", "is<PERSON><PERSON>ller", "allowedStatuses", "event", "next", "source", "target", "getBalances", "pipe", "balances", "Object", "keys", "reduce", "acc", "key", "currencyFormat", "appSettings", "currencySymbol", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "maximumFractionDigits", "format", "replace", "trim", "balanceFormat", "main", "push", "getList", "languages", "language", "name", "subscribe", "items", "detectChanges", "tooltip", "unsubscribe", "show", "document", "getElementsByTagName", "e", "stopPropagation", "ngOnDestroy", "complete", "isAddCascadeGamesAllowed", "areGranted", "GAMES_CASCADE_ADD", "allowed", "ENTITY_EDIT", "isEntityInfoAllowed", "ENTITY_INFO", "preventDefault", "singleClick", "openDialog", "EDIT_REGIONAL", "confirmStatus", "SET_STATUS_CONFIRM", "fetchBalances", "hasBalancePermission", "ENTITY_BALANCE", "_fetchBalances", "currencies", "for<PERSON>ach", "isCreditDebitAllowed", "FINANCE_CREDIT", "FINANCE_DEBIT", "isCountryAddAllowed", "COUNTRY_ADD", "isShowGameLimitsAllowed", "GAME_GROUP_VIEW", "isLabelsManagementAllowed", "KEYENTITY_ENTITYLABELS_VIEW", "ENTITYLABELS_VIEW", "emit", "isAddChildAllowed", "ENTITY_CREATE", "parentHasBalances", "entityParent", "hasDefinedBalances", "parentBalances", "err", "error", "callback", "setTimeout", "entityRowActions", "add<PERSON><PERSON><PERSON><PERSON>", "icon", "fn", "ENTITY_ADD", "canActivateFn", "isMaster", "ENTITY_MOVE", "move", "navigate", "addCascade", "open", "width", "panelClass", "disableClose", "labels", "getLabelGroups", "groups", "labelGroupsSelectOptions", "group", "text", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "i2", "BusinessStructureService", "i3", "SwHubAuthService", "i4", "EntityService", "ChangeDetectorRef", "i5", "MatDialog", "i6", "EntityLabelsService", "i7", "LanguagesService", "i8", "CountryService", "SettingsService", "Router", "selectors", "viewQuery", "EntityItemComponent_Query", "rf", "ctx", "EntityItemComponent_mat_icon_4_Template", "EntityItemComponent_span_6_Template", "EntityItemComponent_span_7_Template", "EntityItemComponent_span_8_Template", "EntityItemComponent_span_9_Template", "EntityItemComponent_span_12_Template", "EntityItemComponent_ng_container_13_Template", "EntityItemComponent_div_14_Template", "EntityItemComponent_mat_icon_15_Template", "EntityItemComponent_ng_container_20_Template", "EntityItemComponent_ng_template_21_Template", "EntityItemComponent_ng_container_25_Template", "EntityItemComponent_ng_template_26_Template", "EntityItemComponent_a_30_Template", "ɵɵstyleProp", "level", "children", "length", "ɵɵstyleMap", "_c2", "max<PERSON><PERSON><PERSON>", "isTest", "regionals_r14", "readonlyState_r15"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/mat-business-structure/entity-item/entity-item.component.ts", "/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/mat-business-structure/entity-item/entity-item.component.html"], "sourcesContent": ["import {\n  ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges, ViewChild\n} from '@angular/core';\nimport { MatDialog } from '@angular/material/dialog';\nimport { MatTooltip } from '@angular/material/tooltip';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { RowAction, SettingsService, SwHubAuthService } from '@skywind-group/lib-swui';\nimport { BehaviorSubject, fromEvent, Subject, Subscription, zip } from 'rxjs';\nimport { first, map, take, takeUntil } from 'rxjs/operators';\n\nimport { PERMISSIONS_LIST, PERMISSIONS_NAMES } from '../../../../../app.constants';\nimport { Entity, Entity as EntityModel } from '../../../../../common/models/entity.model';\nimport { CountryService } from '../../../../../common/services/country.service';\nimport { EntityLabelsService } from '../../../../../common/services/entity-labels.service';\nimport { EntityService } from '../../../../../common/services/entity.service';\nimport { LanguagesService } from '../../../../../common/services/languages.service';\nimport { Balance, Country, Language } from '../../../../../common/typings';\nimport { LabelGroupInfo } from '../../../../../common/typings/label';\nimport { bsDialogs, BusinessStructureService } from '../business-structure.service';\nimport { EntityLabelsDialogComponent } from '../dialogs/entity-labels-dialog/entity-labels-dialog.component';\nimport { RegionalItem } from '../dialogs/mat-regional-dialog/mat-regional-item/mat-regional-item.component';\nimport { ShowLimitsModalComponent } from '../dialogs/show-limits-modal/show-limits-modal.component';\nimport { StructureEntityModel } from '../structure-entity.model';\n\n@Component({\n  selector: '[entity-item]',\n  templateUrl: './entity-item.component.html',\n  styleUrls: ['./entity-item.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class EntityItemComponent implements OnInit, OnChanges, OnDestroy {\n\n  @Input() expanded = false;\n\n  @Input() disabledCurrencyTooltips = false;\n\n  @Input() canChangeTest = false;\n\n  @Input() set entity( val: StructureEntityModel ) {\n    if (!val) {\n      return;\n    }\n    console.log(val);\n    this._entity = val;\n    this.isEntitySuspended = val.changeStatusState !== 'normal';\n    this.canChangeStatus = val.changeStatusState === 'blocked';\n    this.isEntityRowDisabled = this.isEntitySuspended && !val.isRoot();\n    this.initRowActions = this.setRowActions();\n  }\n\n  get entity() {\n    return this._entity;\n  }\n\n  set initRowActions( val: RowAction[] ) {\n    if (!val) {\n      return;\n    }\n    this._rowActions = val;\n  }\n\n  get rowActions() {\n    return this._rowActions;\n  }\n\n  set isEntitySuspended( val: boolean ) {\n    this._isEntitySuspended = val;\n  }\n\n  get isEntitySuspended(): boolean {\n    return this._isEntitySuspended;\n  }\n\n  set canChangeStatus( val: boolean ) {\n    this._canChangeStatus = val;\n  }\n\n  get canChangeStatus(): boolean {\n    return this._canChangeStatus;\n  }\n\n  @Output() onClick = new EventEmitter<string>();\n\n  @ViewChild(MatTooltip) tooltip: MatTooltip;\n\n  entityItemStatusMap = {\n    'normal': {\n      class: 'success',\n      color: 'primary',\n      title: 'BUSINESS_STRUCTURE.WIDGETS.active'\n    },\n    'suspended': {\n      class: 'default',\n      color: 'accent',\n      title: 'BUSINESS_STRUCTURE.WIDGETS.inactive'\n    },\n    'maintenance': {\n      class: 'warning',\n      color: 'warn',\n      title: 'BUSINESS_STRUCTURE.WIDGETS.maintenance'\n    },\n    'blocked_by_admin': {\n      class: 'danger',\n      color: 'warn',\n      title: 'BUSINESS_STRUCTURE.WIDGETS.blocked'\n    },\n    'test': {\n      class: 'danger',\n      color: 'blue',\n      title: 'BUSINESS_STRUCTURE.WIDGETS.test'\n    }\n  };\n  isAvailableItemsLoaded = false;\n  countriesHash: { [code: string]: Country };\n\n  merchantLoading = false;\n  balancesLoading = true;\n  statusUpdating = false;\n  entityUpdate = false;\n  infoText = '';\n  isEntityRowDisabled: boolean = false;\n  availableItems: BehaviorSubject<RegionalItem[]> = new BehaviorSubject([]);\n\n  isSuperAdmin = false;\n  editDisabled$ = new BehaviorSubject(false);\n  canChangeState = false;\n  menuHasItems = false;\n\n  readonly isBrand: boolean;\n  private _isEntitySuspended = false;\n  private _canChangeStatus = false;\n  private _entity: StructureEntityModel;\n  private readonly destroyed$ = new Subject<void>();\n  private tooltipSubscription = new Subscription();\n  private numberOfClicks = 0;\n  private _rowActions: RowAction[] = [];\n\n  constructor(\n    private route: ActivatedRoute,\n    public readonly bsService: BusinessStructureService,\n    private readonly authService: SwHubAuthService,\n    private readonly entityService: EntityService<StructureEntityModel>,\n    private cd: ChangeDetectorRef,\n    private dialog: MatDialog,\n    private entityLabelsService: EntityLabelsService,\n    private readonly languageService: LanguagesService<Language>,\n    private readonly countryService: CountryService<Country>,\n    private readonly settingsService: SettingsService,\n    private readonly router: Router,\n  ) {\n    const { brief } = this.route.snapshot.data;\n    const briefEntity = new EntityModel(brief);\n    this.isBrand = briefEntity.type === Entity.TYPE_BRAND || briefEntity.type === Entity.TYPE_MERCHANT;\n    this.isSuperAdmin = this.authService.isSuperAdmin;\n  }\n\n  ngOnChanges( { entity }: SimpleChanges ) {\n    if (entity?.currentValue) {\n      const decoded = this.entity.decryptedBrand && `id (decoded): ${this.entity.decryptedBrand}\\n` || '';\n\n      this.infoText = `${decoded}id (encoded): ${this.entity.id}\\npath: ${this.entity.path}`;\n    }\n  }\n\n  ngOnInit(): void {\n    this.canChangeState = this.authService.allowedTo([PERMISSIONS_NAMES.ENTITY_CHANGESTATE]) && !this.entity.isRoot();\n\n    this.menuHasItems = ((this.canChangeState && this.entity.status !== 'test')\n        || (['test'].includes(this.entity.status) && this.canChangeTest && !this.entity.isReseller()))\n      && this.allowedStatuses() &&  !this.entity.isRoot();\n  }\n\n  handleOpenItemsMenu( event ) {\n    this.availableItems.next([]);\n    this.isAvailableItemsLoaded = false;\n    let source;\n    switch (event.target.id) {\n      case 'currency':\n        source =\n          this.entityService.getBalances(this.entity.path)\n            .pipe(\n              map<Balance, RegionalItem[]>(( balances: Balance ) => {\n                return Object.keys(balances).reduce(( acc: RegionalItem[], key: string ) => {\n                  const currencyFormat = this.settingsService.appSettings.currencyFormat;\n                  const currencySymbol = new Intl.NumberFormat(currencyFormat, {\n                    style: 'currency',\n                    currency: key,\n                    minimumFractionDigits: 0,\n                    maximumFractionDigits: 0\n                  }).format(0).replace(/\\d/g, '').trim();\n\n                  const balanceFormat = new Intl.NumberFormat(currencyFormat, {\n                    style: 'currency',\n                    currency: key\n                  }).format(balances[key].main).replace(currencySymbol, '').replace(/\\s+/g, '');\n\n                  acc.push({\n                    code: balanceFormat,\n                    displayName: key\n                  });\n                  return acc;\n                }, []);\n              })\n            );\n        break;\n      case 'country':\n        source = this.countryService.getList('', this.entity.path);\n        break;\n      case 'language':\n        source = this.languageService.getList('', this.entity.path)\n          .pipe(\n            map(( languages: Language[] ) => {\n              return languages.map(language => ({\n                code: language.code,\n                displayName: language.name\n              }));\n            })\n          );\n        break;\n      default:\n        break;\n    }\n    source.pipe(take(1)).subscribe(( items ) => {\n      this.availableItems.next(items);\n      this.isAvailableItemsLoaded = true;\n      this.cd.detectChanges();\n    });\n  }\n\n  onInfoClick() {\n    if (!this.tooltip) {\n      return;\n    }\n    this.tooltipSubscription.unsubscribe();\n    this.tooltip.show();\n\n    const tooltip = document.getElementsByTagName('mat-tooltip-component')[0];\n\n    this.tooltipSubscription = fromEvent(tooltip, 'click')\n      .pipe(\n        takeUntil(this.destroyed$),\n      )\n      .subscribe(e => {\n        e.stopPropagation();\n      });\n  }\n\n  ngOnDestroy(): void {\n    this.destroyed$.next();\n    this.destroyed$.complete();\n    this.tooltipSubscription.unsubscribe();\n  }\n\n  isAddCascadeGamesAllowed(): boolean {\n    return this.authService.areGranted(PERMISSIONS_LIST.GAMES_CASCADE_ADD);\n  }\n\n  isEntityEditAllowed( entity: StructureEntityModel ): boolean {\n    let allowed = false;\n    if (entity && !entity.isRoot()) {\n      allowed = this.authService.allowedTo([PERMISSIONS_NAMES.ENTITY_EDIT]);\n    }\n    return allowed;\n  }\n\n  isEntityInfoAllowed( entity: StructureEntityModel ): boolean {\n    let allowed = false;\n    if (entity && !entity.isRoot()) {\n      allowed = this.authService.allowedTo([PERMISSIONS_NAMES.ENTITY_INFO]);\n    }\n    return allowed;\n  }\n\n  handleOpenEdit( event ) {\n    event.preventDefault();\n    this.singleClick(() => this.bsService.openDialog(bsDialogs.ENTITY_EDIT, this.entity));\n  }\n\n  handleOpenEditRegional( event ) {\n    event.preventDefault();\n    this.editDisabled$.next(true);\n    this.bsService.openDialog(bsDialogs.EDIT_REGIONAL, this.entity, event.target.id)\n      .pipe(take(1))\n      .subscribe(() => {\n        this.editDisabled$.next(false);\n      });\n  }\n\n  handleOpenConfirmSetStatus( event, confirmStatus ) {\n    event.preventDefault();\n    this.bsService.openDialog(bsDialogs.SET_STATUS_CONFIRM, this.entity, { confirmStatus });\n  }\n\n  allowedStatuses(): boolean {\n    if (!this.isSuperAdmin) {\n      if (this.entity.status === 'test') {\n        return this.canChangeTest;\n      } else {\n        return this.entity.status !== 'blocked_by_admin' && (this.canChangeStatus || !this.isEntitySuspended);\n      }\n    }\n    return this.isSuperAdmin;\n  }\n\n  fetchBalances( event ) {\n    event.preventDefault();\n\n    const hasBalancePermission = this.authService.allowedTo([PERMISSIONS_NAMES.ENTITY_BALANCE]);\n\n    if (hasBalancePermission) {\n      this._fetchBalances();\n    } else {\n      this.balancesLoading = false;\n      this.entity.currencies.forEach(currency => {\n        this.entity.balances[currency] = {};\n      });\n    }\n  }\n\n  isCreditDebitAllowed(): boolean {\n    return this.authService.allowedTo([PERMISSIONS_NAMES.FINANCE_CREDIT, PERMISSIONS_NAMES.FINANCE_DEBIT]);\n  }\n\n  isCountryAddAllowed(): boolean {\n    return this.authService.allowedTo([PERMISSIONS_NAMES.COUNTRY_ADD]);\n  }\n\n  isShowGameLimitsAllowed(): boolean {\n    return this.authService.allowedTo(PERMISSIONS_LIST.GAME_GROUP_VIEW);\n  }\n\n  isLabelsManagementAllowed(): boolean {\n    return (this.entity.isRoot() && this.authService.allowedTo([PERMISSIONS_NAMES.KEYENTITY_ENTITYLABELS_VIEW])) ||\n      (!this.entity.isRoot() && this.authService.allowedTo([PERMISSIONS_NAMES.ENTITYLABELS_VIEW]));\n  }\n\n  onItemClick( entity: Entity ) {\n    this.onClick.emit(entity.id);\n  }\n\n  isAddChildAllowed(): boolean {\n    return this.authService.allowedTo([PERMISSIONS_NAMES.ENTITY_CREATE]);\n  }\n\n  private _fetchBalances() {\n    const parentHasBalances = !this.entity.isRoot() && this.entity.entityParent.hasDefinedBalances();\n    if (this.entity && this.entity.hasDefinedBalances() && (parentHasBalances || this.entity.isRoot())) {\n      this.balancesLoading = false;\n      return;\n    }\n\n    zip(\n      this.entityService.getBalances(this.entity.path),\n      this.entityService.getBalances(this.entity.entityParent.path)\n    ).pipe(\n      first(),\n    ).subscribe(\n      ( [balances, parentBalances] ) => {\n        this.entity.balances = balances;\n        if (this.entity.entityParent) {\n          this.entity.entityParent.balances = parentBalances;\n        }\n        this.balancesLoading = false;\n\n        this.cd.detectChanges();\n      },\n      err => {\n        console.error(err);\n      }\n    );\n  }\n\n  private singleClick( callback ) {\n    this.numberOfClicks++;\n    if (this.numberOfClicks <= 1) {\n      callback();\n      setTimeout(() => {\n        this.numberOfClicks = 0;\n      }, 2000);\n    }\n  }\n\n  private setRowActions(): RowAction[] {\n    const entityRowActions: RowAction[] = [];\n    if (this.entity?.type === 'entity' && this.isAddChildAllowed()) {\n      const addChildren = new RowAction({\n        title: 'BUSINESS_STRUCTURE.WIDGETS.addChild',\n        icon: 'add_box',\n        fn: () => this.bsService.openDialog(bsDialogs.ENTITY_ADD, this.entity),\n        canActivateFn: () => true\n      });\n\n      entityRowActions.push(addChildren);\n    }\n    if (!this.entity?.isMaster() && !this.isBrand && this.authService.allowedTo(PERMISSIONS_LIST.ENTITY_MOVE)) {\n      const move = new RowAction({\n        title: 'BUSINESS_STRUCTURE.WIDGETS.move',\n        icon: 'format_line_spacing',\n        fn: () => this.router.navigate(['/pages/business-management/entities/move/move', this.entity.key]),\n        canActivateFn: () => true\n      });\n\n      entityRowActions.push(move);\n    }\n    if (!this.entity?.isMaster() && this.isAddCascadeGamesAllowed() && !this.isBrand && !this.entity?.isRoot()) {\n      const addCascade = new RowAction({\n        title: 'BUSINESS_STRUCTURE.WIDGETS.addCascade',\n        icon: 'library_add',\n        fn: () => this.router.navigate(['/pages/business-management/entities/cascade-games/add', this.entity.path]),\n        canActivateFn: () => true\n      });\n\n      entityRowActions.push(addCascade);\n    }\n    if (this.isShowGameLimitsAllowed()) {\n      const addCascade = new RowAction({\n        title: 'BUSINESS_STRUCTURE.WIDGETS.showGameLimits',\n        icon: 'attach_money',\n        fn: () => this.dialog.open(ShowLimitsModalComponent, {\n          width: '80vw',\n          panelClass: 'limits-dialog-body',\n          data: this.entity,\n          disableClose: true\n        }),\n        canActivateFn: () => true,\n      });\n\n      entityRowActions.push(addCascade);\n    }\n    if (this.isLabelsManagementAllowed()) {\n      const labels = new RowAction({\n        title: 'BUSINESS_STRUCTURE.WIDGETS.labels',\n        icon: 'label',\n        fn: () => this.entityLabelsService.getLabelGroups('entity')\n          .pipe(\n            take(1),\n          ).subscribe(( groups: LabelGroupInfo[] ) => {\n            return this.dialog.open(EntityLabelsDialogComponent, {\n              width: '90vw',\n              disableClose: true,\n              data: {\n                entity: this.entity,\n                labelGroupsSelectOptions: groups.map(group => {\n                  return { id: group.id, text: group.group };\n                }),\n              },\n            });\n          }),\n        canActivateFn: () => true\n      });\n\n      entityRowActions.push(labels);\n    }\n    return entityRowActions;\n  }\n}\n", "<td [ngClass]=\"{'disabled-cell': isEntityRowDisabled}\">\n  <div class=\"name-wrapper\">\n    <div class=\"bs-entity\" [style.padding-left]=\"(entity.level + 1) * 20 + 'px'\">\n      <div class=\"bs-entity__arrow\">\n        <mat-icon *ngIf=\"entity.children.length > 0\" (click)=\"onItemClick(entity)\">\n          {{ expanded ? 'expand_more' : 'chevron_right' }}\n        </mat-icon>\n      </div>\n\n      <ng-container [ngSwitch]=\"entity.type\">\n        <span class=\"bs-entity__type bs-type bs-type--entity\" *ngSwitchCase=\"'entity'\" title=\"Reseller\">R</span>\n        <span class=\"bs-entity__type bs-type bs-type--brand\" *ngSwitchCase=\"'brand'\" title=\"Operator\">O</span>\n        <span class=\"bs-entity__type bs-type bs-type--merchant\" *ngSwitchCase=\"'merchant'\" title=\"Merchant\">O</span>\n        <span class=\"bs-entity__type bs-type bs-type--studio\" *ngSwitchCase=\"'liveStudio'\" title=\"Live Studio\">S</span>\n      </ng-container>\n\n      <span class=\"bs-entity__title\" [style]=\"{width: (bsService.maxLength - (entity.level * 20)) + 'px'}\">\n        {{ entity.title || entity.name }}\n        <span *ngIf=\"entity.isTest\" class=\"sw-chip\" [ngClass]=\"'sw-chip-default'\">Test</span>\n        <ng-container *ngIf=\"!isEntitySuspended\">\n        <button\n          mat-icon-button\n          *ngIf=\"isEntityEditAllowed(entity)\"\n          class=\"bs-entity__edit\"\n          (click)=\"handleOpenEdit($event)\">\n        <mat-icon fontSet=\"material-icons-outline\">edit</mat-icon>\n      </button>\n        </ng-container>\n      </span>\n\n      <div class=\"bs-entity__spinner\" *ngIf=\"merchantLoading || entityUpdate\">\n        <mat-progress-spinner diameter=\"14\" mode=\"indeterminate\"></mat-progress-spinner>\n      </div>\n    </div>\n\n    <mat-icon\n      class=\"info-link\"\n      matTooltip=\"{{infoText}}\"\n      (mouseenter)=\"$event.stopImmediatePropagation()\"\n      (mouseleave)=\"$event.stopImmediatePropagation()\"\n      *ngIf=\"isEntityInfoAllowed(entity)\"\n      (click)=\"onInfoClick()\">info_outline\n    </mat-icon>\n  </div>\n</td>\n\n<td [ngClass]=\"{'disabled-cell': isEntityRowDisabled}\">\n  {{ entity.name }}\n</td>\n\n<td style=\"white-space: nowrap\"\n    [ngClass]=\"{'disabled-cell': isEntityRowDisabled}\">\n  {{ entity.key }}\n</td>\n\n<ng-container *ngIf=\"!isEntitySuspended || entity.isRoot(); else regionals\">\n  <td [ngClass]=\"{'disabled-cell': isEntityRowDisabled}\">\n    <div class=\"bs-regional\">\n      <div class=\"bs-regional__val\">\n        <a matTooltip=\"Currency\" id=\"currency\" [matMenuTriggerFor]=\"item\"\n           [ngClass]=\"{'disabled-tooltip': disabledCurrencyTooltips}\"\n           (click)=\"handleOpenItemsMenu($event)\">{{ entity.defaultCurrency }}</a> /\n        <a matTooltip=\"Country\" id=\"country\" [matMenuTriggerFor]=\"item\"\n           (click)=\"handleOpenItemsMenu($event)\">{{ entity.defaultCountry }}</a> /\n        <a matTooltip=\"Language\" id=\"language\" [matMenuTriggerFor]=\"item\"\n           (click)=\"handleOpenItemsMenu($event)\">{{ entity.defaultLanguage }}</a>\n        <mat-menu class=\"regional-item\" #item>\n          <ng-container *ngIf=\"isAvailableItemsLoaded; else spinner\">\n            <p *ngFor=\"let item of availableItems | async\" mat-menu-item>{{item.displayName }} ({{item.code}})</p>\n          </ng-container>\n          <ng-template #spinner>\n            <div class=\"bs-regional__spinner\">\n              <mat-progress-bar mode=\"indeterminate\"></mat-progress-bar>\n            </div>\n          </ng-template>\n        </mat-menu>\n        <button\n          mat-icon-button\n          class=\"editRegionBtn\"\n          [disabled]=\"entity.isRoot() || (editDisabled$ | async)\"\n          (click)=\"handleOpenEditRegional($event)\">\n          <mat-icon fontSet=\"material-icons-outline\">edit</mat-icon>\n        </button>\n      </div>\n    </div>\n  </td>\n</ng-container>\n<ng-template #regionals>\n  <td [ngClass]=\"{'disabled-cell': isEntityRowDisabled}\">\n    <div class=\"bs-regional\">\n      <div class=\"bs-regional__val\">\n        <span matTooltip=\"Currency\">{{ entity.defaultCurrency }}</span> /\n        <span matTooltip=\"Country\">{{ entity.defaultCountry }}</span> /\n        <span matTooltip=\"Language\">{{ entity.defaultLanguage }}</span>\n        <button\n          disabled\n          mat-icon-button\n          class=\"editRegionBtn\">\n          <mat-icon fontSet=\"material-icons-outline\">edit</mat-icon>\n        </button>\n      </div>\n    </div>\n  </td>\n</ng-template>\n\n\n<td style=\"text-align: center\"\n    [ngClass]=\"{'disabled-cell': isEntitySuspended && !canChangeStatus && !entity.isRoot() }\">\n  <div class=\"bs-status\">\n    <ng-container *ngIf=\"menuHasItems; else readonlyState\">\n      <div class=\"bs-status__chip bs-status__chip--menu sw-chip\"\n           [class.sw-chip-green]=\"entity.status === 'normal'\"\n           [class.sw-chip-default]=\"entity.status === 'suspended'\"\n           [class.sw-chip-orange]=\"entity.status === 'maintenance'\"\n           [class.sw-chip-red]=\"entity.status === 'blocked_by_admin'\"\n           [class.sw-chip-blue]=\"entity.status === 'test'\"\n           [matMenuTriggerFor]=\"statusMenu\">\n        {{ statusUpdating ? '...' : (entityItemStatusMap[entity.status].title | translate) }}\n        <mat-icon class=\"bs-status__chevron\">arrow_drop_down</mat-icon>\n      </div>\n\n      <mat-menu #statusMenu=\"matMenu\" xPosition=\"before\" class=\"bs-status__menu\">\n        <div\n          mat-menu-item\n          *ngIf=\"(entity.status !== 'normal' && canChangeState) || (entity.status === 'test' && canChangeTest)\"\n          (click)=\"handleOpenConfirmSetStatus($event, 'normal')\">\n\n          <mat-icon class=\"bs-status__circle sw-color-green\">fiber_manual_record</mat-icon>\n          {{'BUSINESS_STRUCTURE.WIDGETS.active' | translate}}\n        </div>\n\n        <div\n          mat-menu-item\n          *ngIf=\"entity.status !== 'suspended' && canChangeState\"\n          (click)=\"handleOpenConfirmSetStatus($event, 'suspended')\">\n\n          <mat-icon class=\"bs-status__circle sw-color-grey\">fiber_manual_record</mat-icon>\n          {{'BUSINESS_STRUCTURE.WIDGETS.inactive' | translate}}\n        </div>\n\n        <div\n          mat-menu-item\n          *ngIf=\"entity.status !== 'maintenance' && canChangeState\"\n          (click)=\"handleOpenConfirmSetStatus($event, 'maintenance')\">\n\n          <mat-icon class=\"bs-status__circle sw-color-orange\">fiber_manual_record</mat-icon>\n          {{'BUSINESS_STRUCTURE.WIDGETS.maintenance' | translate}}\n        </div>\n\n        <div\n          mat-menu-item\n          *ngIf=\"entity.status !== 'blocked_by_admin' && isSuperAdmin\"\n          (click)=\"handleOpenConfirmSetStatus($event, 'blocked_by_admin')\">\n\n          <mat-icon class=\"bs-status__circle sw-color-deep-orange\">fiber_manual_record</mat-icon>\n          {{'BUSINESS_STRUCTURE.WIDGETS.blocked' | translate}}\n        </div>\n      </mat-menu>\n    </ng-container>\n\n    <ng-template #readonlyState>\n      <div class=\"bs-status__chip sw-chip\"\n           [title]=\"entity.status !== 'blocked_by_admin' ? '' : 'BUSINESS_STRUCTURE.WIDGETS.blockedByAdmin' | translate\"\n           [class.sw-chip-green]=\"entity.status === 'normal'\"\n           [class.sw-chip-default]=\"entity.status === 'suspended'\"\n           [class.sw-chip-orange]=\"entity.status === 'maintenance'\"\n           [class.sw-chip-red]=\"entity.status === 'blocked_by_admin'\"\n           [class.sw-chip-blue]=\"entity.status === 'test'\">\n        {{entityItemStatusMap[entity.status].title | translate}}\n      </div>\n    </ng-template>\n  </div>\n</td>\n\n<td style=\"padding-right: 24px; text-align: center; display: flex; justify-content: flex-end;\">\n  <div>\n    <a class=\"menu-item-link\"\n       matTooltip=\"{{'BUSINESS_STRUCTURE.WIDGETS.settings' | translate}}\"\n       mat-menu-item *ngIf=\"!entity.isMaster() && !isBrand\"\n       [disabled]=\"isEntityRowDisabled\"\n       [routerLink]=\"entity.isRoot() ? ['../entities/setup', 'p'] : ['../entities/setup', entity.path, 'p']\">\n      <mat-icon fontSet=\"material-icons-outline\">settings</mat-icon>\n    </a>\n  </div>\n  <lib-swui-grid-row-actions\n    [actions]=\"rowActions\"\n    [ignorePlainLink]=\"true\">\n  </lib-swui-grid-row-actions>\n</td>\n"], "mappings": "AAAA,SACyDA,YAAY,QAC9D,eAAe;AAEtB,SAASC,UAAU,QAAQ,2BAA2B;AAEtD,SAASC,SAAS,QAA2C,yBAAyB;AACtF,SAASC,eAAe,EAAEC,SAAS,EAAEC,OAAO,EAAEC,YAAY,EAAEC,GAAG,QAAQ,MAAM;AAC7E,SAASC,KAAK,EAAEC,GAAG,EAAEC,IAAI,EAAEC,SAAS,QAAQ,gBAAgB;AAE5D,SAASC,gBAAgB,EAAEC,iBAAiB,QAAQ,8BAA8B;AAClF,SAASC,MAAM,EAAEA,MAAM,IAAIC,WAAW,QAAQ,2CAA2C;AAOzF,SAASC,SAAS,QAAkC,+BAA+B;AACnF,SAASC,2BAA2B,QAAQ,gEAAgE;AAE5G,SAASC,wBAAwB,QAAQ,0DAA0D;;;;;;;;;;;;;;;;;;;;;;;;;ICjB3FC,EAAA,CAAAC,cAAA,mBAA2E;IAA9BD,EAAA,CAAAE,UAAA,mBAAAC,kEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,WAAA,CAAAH,MAAA,CAAAI,MAAA,CAAmB;IAAA,EAAC;IACxEV,EAAA,CAAAW,MAAA,GACF;IAAAX,EAAA,CAAAY,YAAA,EAAW;;;;IADTZ,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAc,kBAAA,MAAAR,MAAA,CAAAS,QAAA,wCACF;;;;;IAIAf,EAAA,CAAAC,cAAA,eAAgG;IAAAD,EAAA,CAAAW,MAAA,QAAC;IAAAX,EAAA,CAAAY,YAAA,EAAO;;;;;IACxGZ,EAAA,CAAAC,cAAA,eAA8F;IAAAD,EAAA,CAAAW,MAAA,QAAC;IAAAX,EAAA,CAAAY,YAAA,EAAO;;;;;IACtGZ,EAAA,CAAAC,cAAA,eAAoG;IAAAD,EAAA,CAAAW,MAAA,QAAC;IAAAX,EAAA,CAAAY,YAAA,EAAO;;;;;IAC5GZ,EAAA,CAAAC,cAAA,eAAuG;IAAAD,EAAA,CAAAW,MAAA,QAAC;IAAAX,EAAA,CAAAY,YAAA,EAAO;;;;;IAK/GZ,EAAA,CAAAC,cAAA,eAA0E;IAAAD,EAAA,CAAAW,MAAA,WAAI;IAAAX,EAAA,CAAAY,YAAA,EAAO;;;IAAzCZ,EAAA,CAAAgB,UAAA,8BAA6B;;;;;;IAEzEhB,EAAA,CAAAC,cAAA,iBAImC;IAAjCD,EAAA,CAAAE,UAAA,mBAAAe,8EAAAC,MAAA;MAAAlB,EAAA,CAAAI,aAAA,CAAAe,GAAA;MAAA,MAAAb,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAc,cAAA,CAAAF,MAAA,CAAsB;IAAA,EAAC;IAClClB,EAAA,CAAAC,cAAA,mBAA2C;IAAAD,EAAA,CAAAW,MAAA,WAAI;IACjDX,EADiD,CAAAY,YAAA,EAAW,EACnD;;;;;IAPPZ,EAAA,CAAAqB,uBAAA,GAAyC;IACzCrB,EAAA,CAAAsB,UAAA,IAAAC,qDAAA,qBAImC;;;;;IAFhCvB,EAAA,CAAAa,SAAA,EAAiC;IAAjCb,EAAA,CAAAgB,UAAA,SAAAV,MAAA,CAAAkB,mBAAA,CAAAlB,MAAA,CAAAI,MAAA,EAAiC;;;;;IAQtCV,EAAA,CAAAC,cAAA,cAAwE;IACtED,EAAA,CAAAyB,SAAA,+BAAgF;IAClFzB,EAAA,CAAAY,YAAA,EAAM;;;;;;IAGRZ,EAAA,CAAAC,cAAA,mBAM0B;IAAxBD,EAHA,CAAAE,UAAA,wBAAAwB,wEAAAR,MAAA;MAAAlB,EAAA,CAAAI,aAAA,CAAAuB,GAAA;MAAA,OAAA3B,EAAA,CAAAQ,WAAA,CAAcU,MAAA,CAAAU,wBAAA,EAAiC;IAAA,EAAC,wBAAAC,wEAAAX,MAAA;MAAAlB,EAAA,CAAAI,aAAA,CAAAuB,GAAA;MAAA,OAAA3B,EAAA,CAAAQ,WAAA,CAClCU,MAAA,CAAAU,wBAAA,EAAiC;IAAA,EAAC,mBAAAE,mEAAA;MAAA9B,EAAA,CAAAI,aAAA,CAAAuB,GAAA;MAAA,MAAArB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAEvCF,MAAA,CAAAyB,WAAA,EAAa;IAAA,EAAC;IAAC/B,EAAA,CAAAW,MAAA,oBAC1B;IAAAX,EAAA,CAAAY,YAAA,EAAW;;;;IALTZ,EAAA,CAAAgC,qBAAA,eAAA1B,MAAA,CAAA2B,QAAA,CAAyB;;;;;IA+BnBjC,EAAA,CAAAC,cAAA,YAA6D;IAAAD,EAAA,CAAAW,MAAA,GAAqC;IAAAX,EAAA,CAAAY,YAAA,EAAI;;;;IAAzCZ,EAAA,CAAAa,SAAA,EAAqC;IAArCb,EAAA,CAAAkC,kBAAA,KAAAC,OAAA,CAAAC,WAAA,QAAAD,OAAA,CAAAE,IAAA,MAAqC;;;;;IADpGrC,EAAA,CAAAqB,uBAAA,GAA2D;IACzDrB,EAAA,CAAAsB,UAAA,IAAAgB,gEAAA,gBAA6D;;;;;;IAAzCtC,EAAA,CAAAa,SAAA,EAAyB;IAAzBb,EAAA,CAAAgB,UAAA,YAAAhB,EAAA,CAAAuC,WAAA,OAAAjC,MAAA,CAAAkC,cAAA,EAAyB;;;;;IAG7CxC,EAAA,CAAAC,cAAA,cAAkC;IAChCD,EAAA,CAAAyB,SAAA,2BAA0D;IAC5DzB,EAAA,CAAAY,YAAA,EAAM;;;;;;IAlBlBZ,EAAA,CAAAqB,uBAAA,GAA4E;IAIpErB,EAHN,CAAAC,cAAA,YAAuD,cAC5B,cACO,YAGa;IAAtCD,EAAA,CAAAE,UAAA,mBAAAuC,gEAAAvB,MAAA;MAAAlB,EAAA,CAAAI,aAAA,CAAAsC,GAAA;MAAA,MAAApC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAqC,mBAAA,CAAAzB,MAAA,CAA2B;IAAA,EAAC;IAAClB,EAAA,CAAAW,MAAA,GAA4B;IAAAX,EAAA,CAAAY,YAAA,EAAI;IAACZ,EAAA,CAAAW,MAAA,UAC1E;IAAAX,EAAA,CAAAC,cAAA,YACyC;IAAtCD,EAAA,CAAAE,UAAA,mBAAA0C,gEAAA1B,MAAA;MAAAlB,EAAA,CAAAI,aAAA,CAAAsC,GAAA;MAAA,MAAApC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAqC,mBAAA,CAAAzB,MAAA,CAA2B;IAAA,EAAC;IAAClB,EAAA,CAAAW,MAAA,GAA2B;IAAAX,EAAA,CAAAY,YAAA,EAAI;IAACZ,EAAA,CAAAW,MAAA,UACzE;IAAAX,EAAA,CAAAC,cAAA,aACyC;IAAtCD,EAAA,CAAAE,UAAA,mBAAA2C,iEAAA3B,MAAA;MAAAlB,EAAA,CAAAI,aAAA,CAAAsC,GAAA;MAAA,MAAApC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAqC,mBAAA,CAAAzB,MAAA,CAA2B;IAAA,EAAC;IAAClB,EAAA,CAAAW,MAAA,IAA4B;IAAAX,EAAA,CAAAY,YAAA,EAAI;IACzEZ,EAAA,CAAAC,cAAA,uBAAsC;IAIpCD,EAHA,CAAAsB,UAAA,KAAAwB,4DAAA,2BAA2D,KAAAC,2DAAA,gCAAA/C,EAAA,CAAAgD,sBAAA,CAGrC;IAKxBhD,EAAA,CAAAY,YAAA,EAAW;IACXZ,EAAA,CAAAC,cAAA,kBAI2C;;IAAzCD,EAAA,CAAAE,UAAA,mBAAA+C,sEAAA/B,MAAA;MAAAlB,EAAA,CAAAI,aAAA,CAAAsC,GAAA;MAAA,MAAApC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA4C,sBAAA,CAAAhC,MAAA,CAA8B;IAAA,EAAC;IACxClB,EAAA,CAAAC,cAAA,oBAA2C;IAAAD,EAAA,CAAAW,MAAA,YAAI;IAIvDX,EAJuD,CAAAY,YAAA,EAAW,EACnD,EACL,EACF,EACH;;;;;;;IA7BDZ,EAAA,CAAAa,SAAA,EAAkD;IAAlDb,EAAA,CAAAgB,UAAA,YAAAhB,EAAA,CAAAmD,eAAA,KAAAC,GAAA,EAAA9C,MAAA,CAAA+C,mBAAA,EAAkD;IAGTrD,EAAA,CAAAa,SAAA,GAA0B;IAC9Db,EADoC,CAAAgB,UAAA,sBAAAsC,OAAA,CAA0B,YAAAtD,EAAA,CAAAmD,eAAA,KAAAI,GAAA,EAAAjD,MAAA,CAAAkD,wBAAA,EACJ;IACpBxD,EAAA,CAAAa,SAAA,EAA4B;IAA5Bb,EAAA,CAAAyD,iBAAA,CAAAnD,MAAA,CAAAI,MAAA,CAAAgD,eAAA,CAA4B;IAChC1D,EAAA,CAAAa,SAAA,GAA0B;IAA1Bb,EAAA,CAAAgB,UAAA,sBAAAsC,OAAA,CAA0B;IACtBtD,EAAA,CAAAa,SAAA,EAA2B;IAA3Bb,EAAA,CAAAyD,iBAAA,CAAAnD,MAAA,CAAAI,MAAA,CAAAiD,cAAA,CAA2B;IAC7B3D,EAAA,CAAAa,SAAA,GAA0B;IAA1Bb,EAAA,CAAAgB,UAAA,sBAAAsC,OAAA,CAA0B;IACxBtD,EAAA,CAAAa,SAAA,EAA4B;IAA5Bb,EAAA,CAAAyD,iBAAA,CAAAnD,MAAA,CAAAI,MAAA,CAAAkD,eAAA,CAA4B;IAEpD5D,EAAA,CAAAa,SAAA,GAA8B;IAAAb,EAA9B,CAAAgB,UAAA,SAAAV,MAAA,CAAAuD,sBAAA,CAA8B,aAAAC,UAAA,CAAY;IAYzD9D,EAAA,CAAAa,SAAA,GAAuD;IAAvDb,EAAA,CAAAgB,UAAA,aAAAV,MAAA,CAAAI,MAAA,CAAAqD,MAAA,MAAA/D,EAAA,CAAAuC,WAAA,SAAAjC,MAAA,CAAA0D,aAAA,EAAuD;;;;;IAYzDhE,EAHN,CAAAC,cAAA,YAAuD,cAC5B,cACO,eACA;IAAAD,EAAA,CAAAW,MAAA,GAA4B;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAACZ,EAAA,CAAAW,MAAA,UAChE;IAAAX,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAW,MAAA,GAA2B;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAACZ,EAAA,CAAAW,MAAA,UAC9D;IAAAX,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAW,MAAA,IAA4B;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAK7DZ,EAJF,CAAAC,cAAA,kBAGwB,oBACqB;IAAAD,EAAA,CAAAW,MAAA,YAAI;IAIvDX,EAJuD,CAAAY,YAAA,EAAW,EACnD,EACL,EACF,EACH;;;;IAdDZ,EAAA,CAAAgB,UAAA,YAAAhB,EAAA,CAAAmD,eAAA,IAAAC,GAAA,EAAA9C,MAAA,CAAA+C,mBAAA,EAAkD;IAGpBrD,EAAA,CAAAa,SAAA,GAA4B;IAA5Bb,EAAA,CAAAyD,iBAAA,CAAAnD,MAAA,CAAAI,MAAA,CAAAgD,eAAA,CAA4B;IAC7B1D,EAAA,CAAAa,SAAA,GAA2B;IAA3Bb,EAAA,CAAAyD,iBAAA,CAAAnD,MAAA,CAAAI,MAAA,CAAAiD,cAAA,CAA2B;IAC1B3D,EAAA,CAAAa,SAAA,GAA4B;IAA5Bb,EAAA,CAAAyD,iBAAA,CAAAnD,MAAA,CAAAI,MAAA,CAAAkD,eAAA,CAA4B;;;;;;IA6BxD5D,EAAA,CAAAC,cAAA,cAGyD;IAAvDD,EAAA,CAAAE,UAAA,mBAAA+D,wEAAA/C,MAAA;MAAAlB,EAAA,CAAAI,aAAA,CAAA8D,GAAA;MAAA,MAAA5D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA6D,0BAAA,CAAAjD,MAAA,EAAmC,QAAQ,CAAC;IAAA,EAAC;IAEtDlB,EAAA,CAAAC,cAAA,mBAAmD;IAAAD,EAAA,CAAAW,MAAA,0BAAmB;IAAAX,EAAA,CAAAY,YAAA,EAAW;IACjFZ,EAAA,CAAAW,MAAA,GACF;;IAAAX,EAAA,CAAAY,YAAA,EAAM;;;IADJZ,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAAd,EAAA,CAAAuC,WAAA,iDACF;;;;;;IAEAvC,EAAA,CAAAC,cAAA,cAG4D;IAA1DD,EAAA,CAAAE,UAAA,mBAAAkE,wEAAAlD,MAAA;MAAAlB,EAAA,CAAAI,aAAA,CAAAiE,IAAA;MAAA,MAAA/D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA6D,0BAAA,CAAAjD,MAAA,EAAmC,WAAW,CAAC;IAAA,EAAC;IAEzDlB,EAAA,CAAAC,cAAA,mBAAkD;IAAAD,EAAA,CAAAW,MAAA,0BAAmB;IAAAX,EAAA,CAAAY,YAAA,EAAW;IAChFZ,EAAA,CAAAW,MAAA,GACF;;IAAAX,EAAA,CAAAY,YAAA,EAAM;;;IADJZ,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAAd,EAAA,CAAAuC,WAAA,mDACF;;;;;;IAEAvC,EAAA,CAAAC,cAAA,cAG8D;IAA5DD,EAAA,CAAAE,UAAA,mBAAAoE,yEAAApD,MAAA;MAAAlB,EAAA,CAAAI,aAAA,CAAAmE,IAAA;MAAA,MAAAjE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA6D,0BAAA,CAAAjD,MAAA,EAAmC,aAAa,CAAC;IAAA,EAAC;IAE3DlB,EAAA,CAAAC,cAAA,mBAAoD;IAAAD,EAAA,CAAAW,MAAA,0BAAmB;IAAAX,EAAA,CAAAY,YAAA,EAAW;IAClFZ,EAAA,CAAAW,MAAA,GACF;;IAAAX,EAAA,CAAAY,YAAA,EAAM;;;IADJZ,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAAd,EAAA,CAAAuC,WAAA,sDACF;;;;;;IAEAvC,EAAA,CAAAC,cAAA,cAGmE;IAAjED,EAAA,CAAAE,UAAA,mBAAAsE,yEAAAtD,MAAA;MAAAlB,EAAA,CAAAI,aAAA,CAAAqE,IAAA;MAAA,MAAAnE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA6D,0BAAA,CAAAjD,MAAA,EAAmC,kBAAkB,CAAC;IAAA,EAAC;IAEhElB,EAAA,CAAAC,cAAA,mBAAyD;IAAAD,EAAA,CAAAW,MAAA,0BAAmB;IAAAX,EAAA,CAAAY,YAAA,EAAW;IACvFZ,EAAA,CAAAW,MAAA,GACF;;IAAAX,EAAA,CAAAY,YAAA,EAAM;;;IADJZ,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAAd,EAAA,CAAAuC,WAAA,kDACF;;;;;IA/CJvC,EAAA,CAAAqB,uBAAA,GAAuD;IACrDrB,EAAA,CAAAC,cAAA,cAMsC;IACpCD,EAAA,CAAAW,MAAA,GACA;;IAAAX,EAAA,CAAAC,cAAA,mBAAqC;IAAAD,EAAA,CAAAW,MAAA,sBAAe;IACtDX,EADsD,CAAAY,YAAA,EAAW,EAC3D;IAENZ,EAAA,CAAAC,cAAA,sBAA2E;IA4BzED,EA3BA,CAAAsB,UAAA,IAAAoD,kDAAA,kBAGyD,IAAAC,kDAAA,kBASG,KAAAC,mDAAA,kBASE,KAAAC,mDAAA,kBASK;IAKrE7E,EAAA,CAAAY,YAAA,EAAW;;;;;;IA9CNZ,EAAA,CAAAa,SAAA,EAAkD;IAIlDb,EAJA,CAAA8E,WAAA,kBAAAxE,MAAA,CAAAI,MAAA,CAAAqE,MAAA,cAAkD,oBAAAzE,MAAA,CAAAI,MAAA,CAAAqE,MAAA,iBACK,mBAAAzE,MAAA,CAAAI,MAAA,CAAAqE,MAAA,mBACC,gBAAAzE,MAAA,CAAAI,MAAA,CAAAqE,MAAA,wBACE,iBAAAzE,MAAA,CAAAI,MAAA,CAAAqE,MAAA,YACX;IAC/C/E,EAAA,CAAAgB,UAAA,sBAAAgE,cAAA,CAAgC;IACnChF,EAAA,CAAAa,SAAA,EACA;IADAb,EAAA,CAAAc,kBAAA,MAAAR,MAAA,CAAA2E,cAAA,WAAAjF,EAAA,CAAAuC,WAAA,QAAAjC,MAAA,CAAA4E,mBAAA,CAAA5E,MAAA,CAAAI,MAAA,CAAAqE,MAAA,EAAAI,KAAA,OACA;IAMGnF,EAAA,CAAAa,SAAA,GAAmG;IAAnGb,EAAA,CAAAgB,UAAA,SAAAV,MAAA,CAAAI,MAAA,CAAAqE,MAAA,iBAAAzE,MAAA,CAAA8E,cAAA,IAAA9E,MAAA,CAAAI,MAAA,CAAAqE,MAAA,eAAAzE,MAAA,CAAA+E,aAAA,CAAmG;IASnGrF,EAAA,CAAAa,SAAA,EAAqD;IAArDb,EAAA,CAAAgB,UAAA,SAAAV,MAAA,CAAAI,MAAA,CAAAqE,MAAA,oBAAAzE,MAAA,CAAA8E,cAAA,CAAqD;IASrDpF,EAAA,CAAAa,SAAA,EAAuD;IAAvDb,EAAA,CAAAgB,UAAA,SAAAV,MAAA,CAAAI,MAAA,CAAAqE,MAAA,sBAAAzE,MAAA,CAAA8E,cAAA,CAAuD;IASvDpF,EAAA,CAAAa,SAAA,EAA0D;IAA1Db,EAAA,CAAAgB,UAAA,SAAAV,MAAA,CAAAI,MAAA,CAAAqE,MAAA,2BAAAzE,MAAA,CAAAgF,YAAA,CAA0D;;;;;IAU/DtF,EAAA,CAAAC,cAAA,cAMqD;;IACnDD,EAAA,CAAAW,MAAA,GACF;;IAAAX,EAAA,CAAAY,YAAA,EAAM;;;;IAFDZ,EAJA,CAAA8E,WAAA,kBAAAxE,MAAA,CAAAI,MAAA,CAAAqE,MAAA,cAAkD,oBAAAzE,MAAA,CAAAI,MAAA,CAAAqE,MAAA,iBACK,mBAAAzE,MAAA,CAAAI,MAAA,CAAAqE,MAAA,mBACC,gBAAAzE,MAAA,CAAAI,MAAA,CAAAqE,MAAA,wBACE,iBAAAzE,MAAA,CAAAI,MAAA,CAAAqE,MAAA,YACX;IAL/C/E,EAAA,CAAAgB,UAAA,UAAAV,MAAA,CAAAI,MAAA,CAAAqE,MAAA,+BAAA/E,EAAA,CAAAuC,WAAA,qDAA6G;IAMhHvC,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAAd,EAAA,CAAAuC,WAAA,QAAAjC,MAAA,CAAA4E,mBAAA,CAAA5E,MAAA,CAAAI,MAAA,CAAAqE,MAAA,EAAAI,KAAA,OACF;;;;;IAOFnF,EAAA,CAAAC,cAAA,YAIyG;;IACvGD,EAAA,CAAAC,cAAA,mBAA2C;IAAAD,EAAA,CAAAW,MAAA,eAAQ;IACrDX,EADqD,CAAAY,YAAA,EAAW,EAC5D;;;;IALDZ,EAAA,CAAAgC,qBAAA,eAAAhC,EAAA,CAAAuC,WAAA,8CAAkE;IAGlEvC,EADA,CAAAgB,UAAA,aAAAV,MAAA,CAAA+C,mBAAA,CAAgC,eAAA/C,MAAA,CAAAI,MAAA,CAAAqD,MAAA,KAAA/D,EAAA,CAAAuF,eAAA,IAAAC,GAAA,IAAAxF,EAAA,CAAAmD,eAAA,IAAAsC,GAAA,EAAAnF,MAAA,CAAAI,MAAA,CAAAgF,IAAA,EACqE;;;ADtJ5G,OAAM,MAAOC,mBAAmB;EAQ9B,IAAajF,MAAMA,CAAEkF,GAAyB;IAC5C,IAAI,CAACA,GAAG,EAAE;MACR;IACF;IACAC,OAAO,CAACC,GAAG,CAACF,GAAG,CAAC;IAChB,IAAI,CAACG,OAAO,GAAGH,GAAG;IAClB,IAAI,CAACI,iBAAiB,GAAGJ,GAAG,CAACK,iBAAiB,KAAK,QAAQ;IAC3D,IAAI,CAACC,eAAe,GAAGN,GAAG,CAACK,iBAAiB,KAAK,SAAS;IAC1D,IAAI,CAAC5C,mBAAmB,GAAG,IAAI,CAAC2C,iBAAiB,IAAI,CAACJ,GAAG,CAAC7B,MAAM,EAAE;IAClE,IAAI,CAACoC,cAAc,GAAG,IAAI,CAACC,aAAa,EAAE;EAC5C;EAEA,IAAI1F,MAAMA,CAAA;IACR,OAAO,IAAI,CAACqF,OAAO;EACrB;EAEA,IAAII,cAAcA,CAAEP,GAAgB;IAClC,IAAI,CAACA,GAAG,EAAE;MACR;IACF;IACA,IAAI,CAACS,WAAW,GAAGT,GAAG;EACxB;EAEA,IAAIU,UAAUA,CAAA;IACZ,OAAO,IAAI,CAACD,WAAW;EACzB;EAEA,IAAIL,iBAAiBA,CAAEJ,GAAY;IACjC,IAAI,CAACW,kBAAkB,GAAGX,GAAG;EAC/B;EAEA,IAAII,iBAAiBA,CAAA;IACnB,OAAO,IAAI,CAACO,kBAAkB;EAChC;EAEA,IAAIL,eAAeA,CAAEN,GAAY;IAC/B,IAAI,CAACY,gBAAgB,GAAGZ,GAAG;EAC7B;EAEA,IAAIM,eAAeA,CAAA;IACjB,OAAO,IAAI,CAACM,gBAAgB;EAC9B;EA0DAC,YACUC,KAAqB,EACbC,SAAmC,EAClCC,WAA6B,EAC7BC,aAAkD,EAC3DC,EAAqB,EACrBC,MAAiB,EACjBC,mBAAwC,EAC/BC,eAA2C,EAC3CC,cAAuC,EACvCC,eAAgC,EAChCC,MAAc;IAVvB,KAAAV,KAAK,GAALA,KAAK;IACG,KAAAC,SAAS,GAATA,SAAS;IACR,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,aAAa,GAAbA,aAAa;IACtB,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACV,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,MAAM,GAANA,MAAM;IApHhB,KAAArG,QAAQ,GAAG,KAAK;IAEhB,KAAAyC,wBAAwB,GAAG,KAAK;IAEhC,KAAA6B,aAAa,GAAG,KAAK;IA6CpB,KAAAgC,OAAO,GAAG,IAAIxI,YAAY,EAAU;IAI9C,KAAAqG,mBAAmB,GAAG;MACpB,QAAQ,EAAE;QACRoC,KAAK,EAAE,SAAS;QAChBC,KAAK,EAAE,SAAS;QAChBpC,KAAK,EAAE;OACR;MACD,WAAW,EAAE;QACXmC,KAAK,EAAE,SAAS;QAChBC,KAAK,EAAE,QAAQ;QACfpC,KAAK,EAAE;OACR;MACD,aAAa,EAAE;QACbmC,KAAK,EAAE,SAAS;QAChBC,KAAK,EAAE,MAAM;QACbpC,KAAK,EAAE;OACR;MACD,kBAAkB,EAAE;QAClBmC,KAAK,EAAE,QAAQ;QACfC,KAAK,EAAE,MAAM;QACbpC,KAAK,EAAE;OACR;MACD,MAAM,EAAE;QACNmC,KAAK,EAAE,QAAQ;QACfC,KAAK,EAAE,MAAM;QACbpC,KAAK,EAAE;;KAEV;IACD,KAAAtB,sBAAsB,GAAG,KAAK;IAG9B,KAAA2D,eAAe,GAAG,KAAK;IACvB,KAAAC,eAAe,GAAG,IAAI;IACtB,KAAAxC,cAAc,GAAG,KAAK;IACtB,KAAAyC,YAAY,GAAG,KAAK;IACpB,KAAAzF,QAAQ,GAAG,EAAE;IACb,KAAAoB,mBAAmB,GAAY,KAAK;IACpC,KAAAb,cAAc,GAAoC,IAAIxD,eAAe,CAAC,EAAE,CAAC;IAEzE,KAAAsG,YAAY,GAAG,KAAK;IACpB,KAAAtB,aAAa,GAAG,IAAIhF,eAAe,CAAC,KAAK,CAAC;IAC1C,KAAAoG,cAAc,GAAG,KAAK;IACtB,KAAAuC,YAAY,GAAG,KAAK;IAGZ,KAAApB,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,gBAAgB,GAAG,KAAK;IAEf,KAAAoB,UAAU,GAAG,IAAI1I,OAAO,EAAQ;IACzC,KAAA2I,mBAAmB,GAAG,IAAI1I,YAAY,EAAE;IACxC,KAAA2I,cAAc,GAAG,CAAC;IAClB,KAAAzB,WAAW,GAAgB,EAAE;IAenC,MAAM;MAAE0B;IAAK,CAAE,GAAG,IAAI,CAACrB,KAAK,CAACsB,QAAQ,CAACC,IAAI;IAC1C,MAAMC,WAAW,GAAG,IAAItI,WAAW,CAACmI,KAAK,CAAC;IAC1C,IAAI,CAACI,OAAO,GAAGD,WAAW,CAACE,IAAI,KAAKzI,MAAM,CAAC0I,UAAU,IAAIH,WAAW,CAACE,IAAI,KAAKzI,MAAM,CAAC2I,aAAa;IAClG,IAAI,CAAChD,YAAY,GAAG,IAAI,CAACsB,WAAW,CAACtB,YAAY;EACnD;EAEAiD,WAAWA,CAAE;IAAE7H;EAAM,CAAiB;IACpC,IAAIA,MAAM,EAAE8H,YAAY,EAAE;MACxB,MAAMC,OAAO,GAAG,IAAI,CAAC/H,MAAM,CAACgI,cAAc,IAAI,iBAAiB,IAAI,CAAChI,MAAM,CAACgI,cAAc,IAAI,IAAI,EAAE;MAEnG,IAAI,CAACzG,QAAQ,GAAG,GAAGwG,OAAO,iBAAiB,IAAI,CAAC/H,MAAM,CAACiI,EAAE,WAAW,IAAI,CAACjI,MAAM,CAACgF,IAAI,EAAE;IACxF;EACF;EAEAkD,QAAQA,CAAA;IACN,IAAI,CAACxD,cAAc,GAAG,IAAI,CAACwB,WAAW,CAACiC,SAAS,CAAC,CAACnJ,iBAAiB,CAACoJ,kBAAkB,CAAC,CAAC,IAAI,CAAC,IAAI,CAACpI,MAAM,CAACqD,MAAM,EAAE;IAEjH,IAAI,CAAC4D,YAAY,GAAG,CAAE,IAAI,CAACvC,cAAc,IAAI,IAAI,CAAC1E,MAAM,CAACqE,MAAM,KAAK,MAAM,IAClE,CAAC,MAAM,CAAC,CAACgE,QAAQ,CAAC,IAAI,CAACrI,MAAM,CAACqE,MAAM,CAAC,IAAI,IAAI,CAACM,aAAa,IAAI,CAAC,IAAI,CAAC3E,MAAM,CAACsI,UAAU,EAAG,KAC5F,IAAI,CAACC,eAAe,EAAE,IAAK,CAAC,IAAI,CAACvI,MAAM,CAACqD,MAAM,EAAE;EACvD;EAEApB,mBAAmBA,CAAEuG,KAAK;IACxB,IAAI,CAAC1G,cAAc,CAAC2G,IAAI,CAAC,EAAE,CAAC;IAC5B,IAAI,CAACtF,sBAAsB,GAAG,KAAK;IACnC,IAAIuF,MAAM;IACV,QAAQF,KAAK,CAACG,MAAM,CAACV,EAAE;MACrB,KAAK,UAAU;QACbS,MAAM,GACJ,IAAI,CAACvC,aAAa,CAACyC,WAAW,CAAC,IAAI,CAAC5I,MAAM,CAACgF,IAAI,CAAC,CAC7C6D,IAAI,CACHjK,GAAG,CAA4BkK,QAAiB,IAAK;UACnD,OAAOC,MAAM,CAACC,IAAI,CAACF,QAAQ,CAAC,CAACG,MAAM,CAAC,CAAEC,GAAmB,EAAEC,GAAW,KAAK;YACzE,MAAMC,cAAc,GAAG,IAAI,CAAC3C,eAAe,CAAC4C,WAAW,CAACD,cAAc;YACtE,MAAME,cAAc,GAAG,IAAIC,IAAI,CAACC,YAAY,CAACJ,cAAc,EAAE;cAC3DK,KAAK,EAAE,UAAU;cACjBC,QAAQ,EAAEP,GAAG;cACbQ,qBAAqB,EAAE,CAAC;cACxBC,qBAAqB,EAAE;aACxB,CAAC,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACC,IAAI,EAAE;YAEtC,MAAMC,aAAa,GAAG,IAAIT,IAAI,CAACC,YAAY,CAACJ,cAAc,EAAE;cAC1DK,KAAK,EAAE,UAAU;cACjBC,QAAQ,EAAEP;aACX,CAAC,CAACU,MAAM,CAACf,QAAQ,CAACK,GAAG,CAAC,CAACc,IAAI,CAAC,CAACH,OAAO,CAACR,cAAc,EAAE,EAAE,CAAC,CAACQ,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;YAE7EZ,GAAG,CAACgB,IAAI,CAAC;cACPvI,IAAI,EAAEqI,aAAa;cACnBtI,WAAW,EAAEyH;aACd,CAAC;YACF,OAAOD,GAAG;UACZ,CAAC,EAAE,EAAE,CAAC;QACR,CAAC,CAAC,CACH;QACL;MACF,KAAK,SAAS;QACZR,MAAM,GAAG,IAAI,CAAClC,cAAc,CAAC2D,OAAO,CAAC,EAAE,EAAE,IAAI,CAACnK,MAAM,CAACgF,IAAI,CAAC;QAC1D;MACF,KAAK,UAAU;QACb0D,MAAM,GAAG,IAAI,CAACnC,eAAe,CAAC4D,OAAO,CAAC,EAAE,EAAE,IAAI,CAACnK,MAAM,CAACgF,IAAI,CAAC,CACxD6D,IAAI,CACHjK,GAAG,CAAGwL,SAAqB,IAAK;UAC9B,OAAOA,SAAS,CAACxL,GAAG,CAACyL,QAAQ,KAAK;YAChC1I,IAAI,EAAE0I,QAAQ,CAAC1I,IAAI;YACnBD,WAAW,EAAE2I,QAAQ,CAACC;WACvB,CAAC,CAAC;QACL,CAAC,CAAC,CACH;QACH;MACF;QACE;IACJ;IACA5B,MAAM,CAACG,IAAI,CAAChK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC0L,SAAS,CAAGC,KAAK,IAAK;MACzC,IAAI,CAAC1I,cAAc,CAAC2G,IAAI,CAAC+B,KAAK,CAAC;MAC/B,IAAI,CAACrH,sBAAsB,GAAG,IAAI;MAClC,IAAI,CAACiD,EAAE,CAACqE,aAAa,EAAE;IACzB,CAAC,CAAC;EACJ;EAEApJ,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACqJ,OAAO,EAAE;MACjB;IACF;IACA,IAAI,CAACvD,mBAAmB,CAACwD,WAAW,EAAE;IACtC,IAAI,CAACD,OAAO,CAACE,IAAI,EAAE;IAEnB,MAAMF,OAAO,GAAGG,QAAQ,CAACC,oBAAoB,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC;IAEzE,IAAI,CAAC3D,mBAAmB,GAAG5I,SAAS,CAACmM,OAAO,EAAE,OAAO,CAAC,CACnD7B,IAAI,CACH/J,SAAS,CAAC,IAAI,CAACoI,UAAU,CAAC,CAC3B,CACAqD,SAAS,CAACQ,CAAC,IAAG;MACbA,CAAC,CAACC,eAAe,EAAE;IACrB,CAAC,CAAC;EACN;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAC/D,UAAU,CAACuB,IAAI,EAAE;IACtB,IAAI,CAACvB,UAAU,CAACgE,QAAQ,EAAE;IAC1B,IAAI,CAAC/D,mBAAmB,CAACwD,WAAW,EAAE;EACxC;EAEAQ,wBAAwBA,CAAA;IACtB,OAAO,IAAI,CAACjF,WAAW,CAACkF,UAAU,CAACrM,gBAAgB,CAACsM,iBAAiB,CAAC;EACxE;EAEAvK,mBAAmBA,CAAEd,MAA4B;IAC/C,IAAIsL,OAAO,GAAG,KAAK;IACnB,IAAItL,MAAM,IAAI,CAACA,MAAM,CAACqD,MAAM,EAAE,EAAE;MAC9BiI,OAAO,GAAG,IAAI,CAACpF,WAAW,CAACiC,SAAS,CAAC,CAACnJ,iBAAiB,CAACuM,WAAW,CAAC,CAAC;IACvE;IACA,OAAOD,OAAO;EAChB;EAEAE,mBAAmBA,CAAExL,MAA4B;IAC/C,IAAIsL,OAAO,GAAG,KAAK;IACnB,IAAItL,MAAM,IAAI,CAACA,MAAM,CAACqD,MAAM,EAAE,EAAE;MAC9BiI,OAAO,GAAG,IAAI,CAACpF,WAAW,CAACiC,SAAS,CAAC,CAACnJ,iBAAiB,CAACyM,WAAW,CAAC,CAAC;IACvE;IACA,OAAOH,OAAO;EAChB;EAEA5K,cAAcA,CAAE8H,KAAK;IACnBA,KAAK,CAACkD,cAAc,EAAE;IACtB,IAAI,CAACC,WAAW,CAAC,MAAM,IAAI,CAAC1F,SAAS,CAAC2F,UAAU,CAACzM,SAAS,CAACoM,WAAW,EAAE,IAAI,CAACvL,MAAM,CAAC,CAAC;EACvF;EAEAwC,sBAAsBA,CAAEgG,KAAK;IAC3BA,KAAK,CAACkD,cAAc,EAAE;IACtB,IAAI,CAACpI,aAAa,CAACmF,IAAI,CAAC,IAAI,CAAC;IAC7B,IAAI,CAACxC,SAAS,CAAC2F,UAAU,CAACzM,SAAS,CAAC0M,aAAa,EAAE,IAAI,CAAC7L,MAAM,EAAEwI,KAAK,CAACG,MAAM,CAACV,EAAE,CAAC,CAC7EY,IAAI,CAAChK,IAAI,CAAC,CAAC,CAAC,CAAC,CACb0L,SAAS,CAAC,MAAK;MACd,IAAI,CAACjH,aAAa,CAACmF,IAAI,CAAC,KAAK,CAAC;IAChC,CAAC,CAAC;EACN;EAEAhF,0BAA0BA,CAAE+E,KAAK,EAAEsD,aAAa;IAC9CtD,KAAK,CAACkD,cAAc,EAAE;IACtB,IAAI,CAACzF,SAAS,CAAC2F,UAAU,CAACzM,SAAS,CAAC4M,kBAAkB,EAAE,IAAI,CAAC/L,MAAM,EAAE;MAAE8L;IAAa,CAAE,CAAC;EACzF;EAEAvD,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAAC3D,YAAY,EAAE;MACtB,IAAI,IAAI,CAAC5E,MAAM,CAACqE,MAAM,KAAK,MAAM,EAAE;QACjC,OAAO,IAAI,CAACM,aAAa;MAC3B,CAAC,MAAM;QACL,OAAO,IAAI,CAAC3E,MAAM,CAACqE,MAAM,KAAK,kBAAkB,KAAK,IAAI,CAACmB,eAAe,IAAI,CAAC,IAAI,CAACF,iBAAiB,CAAC;MACvG;IACF;IACA,OAAO,IAAI,CAACV,YAAY;EAC1B;EAEAoH,aAAaA,CAAExD,KAAK;IAClBA,KAAK,CAACkD,cAAc,EAAE;IAEtB,MAAMO,oBAAoB,GAAG,IAAI,CAAC/F,WAAW,CAACiC,SAAS,CAAC,CAACnJ,iBAAiB,CAACkN,cAAc,CAAC,CAAC;IAE3F,IAAID,oBAAoB,EAAE;MACxB,IAAI,CAACE,cAAc,EAAE;IACvB,CAAC,MAAM;MACL,IAAI,CAACpF,eAAe,GAAG,KAAK;MAC5B,IAAI,CAAC/G,MAAM,CAACoM,UAAU,CAACC,OAAO,CAAC3C,QAAQ,IAAG;QACxC,IAAI,CAAC1J,MAAM,CAAC8I,QAAQ,CAACY,QAAQ,CAAC,GAAG,EAAE;MACrC,CAAC,CAAC;IACJ;EACF;EAEA4C,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAACpG,WAAW,CAACiC,SAAS,CAAC,CAACnJ,iBAAiB,CAACuN,cAAc,EAAEvN,iBAAiB,CAACwN,aAAa,CAAC,CAAC;EACxG;EAEAC,mBAAmBA,CAAA;IACjB,OAAO,IAAI,CAACvG,WAAW,CAACiC,SAAS,CAAC,CAACnJ,iBAAiB,CAAC0N,WAAW,CAAC,CAAC;EACpE;EAEAC,uBAAuBA,CAAA;IACrB,OAAO,IAAI,CAACzG,WAAW,CAACiC,SAAS,CAACpJ,gBAAgB,CAAC6N,eAAe,CAAC;EACrE;EAEAC,yBAAyBA,CAAA;IACvB,OAAQ,IAAI,CAAC7M,MAAM,CAACqD,MAAM,EAAE,IAAI,IAAI,CAAC6C,WAAW,CAACiC,SAAS,CAAC,CAACnJ,iBAAiB,CAAC8N,2BAA2B,CAAC,CAAC,IACxG,CAAC,IAAI,CAAC9M,MAAM,CAACqD,MAAM,EAAE,IAAI,IAAI,CAAC6C,WAAW,CAACiC,SAAS,CAAC,CAACnJ,iBAAiB,CAAC+N,iBAAiB,CAAC,CAAE;EAChG;EAEAhN,WAAWA,CAAEC,MAAc;IACzB,IAAI,CAAC2G,OAAO,CAACqG,IAAI,CAAChN,MAAM,CAACiI,EAAE,CAAC;EAC9B;EAEAgF,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAAC/G,WAAW,CAACiC,SAAS,CAAC,CAACnJ,iBAAiB,CAACkO,aAAa,CAAC,CAAC;EACtE;EAEQf,cAAcA,CAAA;IACpB,MAAMgB,iBAAiB,GAAG,CAAC,IAAI,CAACnN,MAAM,CAACqD,MAAM,EAAE,IAAI,IAAI,CAACrD,MAAM,CAACoN,YAAY,CAACC,kBAAkB,EAAE;IAChG,IAAI,IAAI,CAACrN,MAAM,IAAI,IAAI,CAACA,MAAM,CAACqN,kBAAkB,EAAE,KAAKF,iBAAiB,IAAI,IAAI,CAACnN,MAAM,CAACqD,MAAM,EAAE,CAAC,EAAE;MAClG,IAAI,CAAC0D,eAAe,GAAG,KAAK;MAC5B;IACF;IAEArI,GAAG,CACD,IAAI,CAACyH,aAAa,CAACyC,WAAW,CAAC,IAAI,CAAC5I,MAAM,CAACgF,IAAI,CAAC,EAChD,IAAI,CAACmB,aAAa,CAACyC,WAAW,CAAC,IAAI,CAAC5I,MAAM,CAACoN,YAAY,CAACpI,IAAI,CAAC,CAC9D,CAAC6D,IAAI,CACJlK,KAAK,EAAE,CACR,CAAC4L,SAAS,CACT,CAAE,CAACzB,QAAQ,EAAEwE,cAAc,CAAC,KAAK;MAC/B,IAAI,CAACtN,MAAM,CAAC8I,QAAQ,GAAGA,QAAQ;MAC/B,IAAI,IAAI,CAAC9I,MAAM,CAACoN,YAAY,EAAE;QAC5B,IAAI,CAACpN,MAAM,CAACoN,YAAY,CAACtE,QAAQ,GAAGwE,cAAc;MACpD;MACA,IAAI,CAACvG,eAAe,GAAG,KAAK;MAE5B,IAAI,CAACX,EAAE,CAACqE,aAAa,EAAE;IACzB,CAAC,EACD8C,GAAG,IAAG;MACJpI,OAAO,CAACqI,KAAK,CAACD,GAAG,CAAC;IACpB,CAAC,CACF;EACH;EAEQ5B,WAAWA,CAAE8B,QAAQ;IAC3B,IAAI,CAACrG,cAAc,EAAE;IACrB,IAAI,IAAI,CAACA,cAAc,IAAI,CAAC,EAAE;MAC5BqG,QAAQ,EAAE;MACVC,UAAU,CAAC,MAAK;QACd,IAAI,CAACtG,cAAc,GAAG,CAAC;MACzB,CAAC,EAAE,IAAI,CAAC;IACV;EACF;EAEQ1B,aAAaA,CAAA;IACnB,MAAMiI,gBAAgB,GAAgB,EAAE;IACxC,IAAI,IAAI,CAAC3N,MAAM,EAAE0H,IAAI,KAAK,QAAQ,IAAI,IAAI,CAACuF,iBAAiB,EAAE,EAAE;MAC9D,MAAMW,WAAW,GAAG,IAAIvP,SAAS,CAAC;QAChCoG,KAAK,EAAE,qCAAqC;QAC5CoJ,IAAI,EAAE,SAAS;QACfC,EAAE,EAAEA,CAAA,KAAM,IAAI,CAAC7H,SAAS,CAAC2F,UAAU,CAACzM,SAAS,CAAC4O,UAAU,EAAE,IAAI,CAAC/N,MAAM,CAAC;QACtEgO,aAAa,EAAEA,CAAA,KAAM;OACtB,CAAC;MAEFL,gBAAgB,CAACzD,IAAI,CAAC0D,WAAW,CAAC;IACpC;IACA,IAAI,CAAC,IAAI,CAAC5N,MAAM,EAAEiO,QAAQ,EAAE,IAAI,CAAC,IAAI,CAACxG,OAAO,IAAI,IAAI,CAACvB,WAAW,CAACiC,SAAS,CAACpJ,gBAAgB,CAACmP,WAAW,CAAC,EAAE;MACzG,MAAMC,IAAI,GAAG,IAAI9P,SAAS,CAAC;QACzBoG,KAAK,EAAE,iCAAiC;QACxCoJ,IAAI,EAAE,qBAAqB;QAC3BC,EAAE,EAAEA,CAAA,KAAM,IAAI,CAACpH,MAAM,CAAC0H,QAAQ,CAAC,CAAC,+CAA+C,EAAE,IAAI,CAACpO,MAAM,CAACmJ,GAAG,CAAC,CAAC;QAClG6E,aAAa,EAAEA,CAAA,KAAM;OACtB,CAAC;MAEFL,gBAAgB,CAACzD,IAAI,CAACiE,IAAI,CAAC;IAC7B;IACA,IAAI,CAAC,IAAI,CAACnO,MAAM,EAAEiO,QAAQ,EAAE,IAAI,IAAI,CAAC9C,wBAAwB,EAAE,IAAI,CAAC,IAAI,CAAC1D,OAAO,IAAI,CAAC,IAAI,CAACzH,MAAM,EAAEqD,MAAM,EAAE,EAAE;MAC1G,MAAMgL,UAAU,GAAG,IAAIhQ,SAAS,CAAC;QAC/BoG,KAAK,EAAE,uCAAuC;QAC9CoJ,IAAI,EAAE,aAAa;QACnBC,EAAE,EAAEA,CAAA,KAAM,IAAI,CAACpH,MAAM,CAAC0H,QAAQ,CAAC,CAAC,uDAAuD,EAAE,IAAI,CAACpO,MAAM,CAACgF,IAAI,CAAC,CAAC;QAC3GgJ,aAAa,EAAEA,CAAA,KAAM;OACtB,CAAC;MAEFL,gBAAgB,CAACzD,IAAI,CAACmE,UAAU,CAAC;IACnC;IACA,IAAI,IAAI,CAAC1B,uBAAuB,EAAE,EAAE;MAClC,MAAM0B,UAAU,GAAG,IAAIhQ,SAAS,CAAC;QAC/BoG,KAAK,EAAE,2CAA2C;QAClDoJ,IAAI,EAAE,cAAc;QACpBC,EAAE,EAAEA,CAAA,KAAM,IAAI,CAACzH,MAAM,CAACiI,IAAI,CAACjP,wBAAwB,EAAE;UACnDkP,KAAK,EAAE,MAAM;UACbC,UAAU,EAAE,oBAAoB;UAChCjH,IAAI,EAAE,IAAI,CAACvH,MAAM;UACjByO,YAAY,EAAE;SACf,CAAC;QACFT,aAAa,EAAEA,CAAA,KAAM;OACtB,CAAC;MAEFL,gBAAgB,CAACzD,IAAI,CAACmE,UAAU,CAAC;IACnC;IACA,IAAI,IAAI,CAACxB,yBAAyB,EAAE,EAAE;MACpC,MAAM6B,MAAM,GAAG,IAAIrQ,SAAS,CAAC;QAC3BoG,KAAK,EAAE,mCAAmC;QAC1CoJ,IAAI,EAAE,OAAO;QACbC,EAAE,EAAEA,CAAA,KAAM,IAAI,CAACxH,mBAAmB,CAACqI,cAAc,CAAC,QAAQ,CAAC,CACxD9F,IAAI,CACHhK,IAAI,CAAC,CAAC,CAAC,CACR,CAAC0L,SAAS,CAAGqE,MAAwB,IAAK;UACzC,OAAO,IAAI,CAACvI,MAAM,CAACiI,IAAI,CAAClP,2BAA2B,EAAE;YACnDmP,KAAK,EAAE,MAAM;YACbE,YAAY,EAAE,IAAI;YAClBlH,IAAI,EAAE;cACJvH,MAAM,EAAE,IAAI,CAACA,MAAM;cACnB6O,wBAAwB,EAAED,MAAM,CAAChQ,GAAG,CAACkQ,KAAK,IAAG;gBAC3C,OAAO;kBAAE7G,EAAE,EAAE6G,KAAK,CAAC7G,EAAE;kBAAE8G,IAAI,EAAED,KAAK,CAACA;gBAAK,CAAE;cAC5C,CAAC;;WAEJ,CAAC;QACJ,CAAC,CAAC;QACJd,aAAa,EAAEA,CAAA,KAAM;OACtB,CAAC;MAEFL,gBAAgB,CAACzD,IAAI,CAACwE,MAAM,CAAC;IAC/B;IACA,OAAOf,gBAAgB;EACzB;;;uCAxaW1I,mBAAmB,EAAA3F,EAAA,CAAA0P,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA5P,EAAA,CAAA0P,iBAAA,CAAAG,EAAA,CAAAC,wBAAA,GAAA9P,EAAA,CAAA0P,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAAhQ,EAAA,CAAA0P,iBAAA,CAAAO,EAAA,CAAAC,aAAA,GAAAlQ,EAAA,CAAA0P,iBAAA,CAAA1P,EAAA,CAAAmQ,iBAAA,GAAAnQ,EAAA,CAAA0P,iBAAA,CAAAU,EAAA,CAAAC,SAAA,GAAArQ,EAAA,CAAA0P,iBAAA,CAAAY,EAAA,CAAAC,mBAAA,GAAAvQ,EAAA,CAAA0P,iBAAA,CAAAc,EAAA,CAAAC,gBAAA,GAAAzQ,EAAA,CAAA0P,iBAAA,CAAAgB,EAAA,CAAAC,cAAA,GAAA3Q,EAAA,CAAA0P,iBAAA,CAAAK,EAAA,CAAAa,eAAA,GAAA5Q,EAAA,CAAA0P,iBAAA,CAAAC,EAAA,CAAAkB,MAAA;IAAA;EAAA;;;YAAnBlL,mBAAmB;MAAAmL,SAAA;MAAAC,SAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;yBAqDnBnS,UAAU;;;;;;;;;;;;;;;;;;;;;;;UChFjBkB,EAHN,CAAAC,cAAA,YAAuD,aAC3B,aACqD,aAC7C;UAC5BD,EAAA,CAAAsB,UAAA,IAAA6P,uCAAA,sBAA2E;UAG7EnR,EAAA,CAAAY,YAAA,EAAM;UAENZ,EAAA,CAAAqB,uBAAA,OAAuC;UAIrCrB,EAHA,CAAAsB,UAAA,IAAA8P,mCAAA,mBAAgG,IAAAC,mCAAA,mBACF,IAAAC,mCAAA,mBACM,IAAAC,mCAAA,mBACG;;UAGzGvR,EAAA,CAAAC,cAAA,gBAAqG;UACnGD,EAAA,CAAAW,MAAA,IACA;UACAX,EADA,CAAAsB,UAAA,KAAAkQ,oCAAA,mBAA0E,KAAAC,4CAAA,2BACjC;UAS3CzR,EAAA,CAAAY,YAAA,EAAO;UAEPZ,EAAA,CAAAsB,UAAA,KAAAoQ,mCAAA,kBAAwE;UAG1E1R,EAAA,CAAAY,YAAA,EAAM;UAENZ,EAAA,CAAAsB,UAAA,KAAAqQ,wCAAA,uBAM0B;UAG9B3R,EADE,CAAAY,YAAA,EAAM,EACH;UAELZ,EAAA,CAAAC,cAAA,aAAuD;UACrDD,EAAA,CAAAW,MAAA,IACF;UAAAX,EAAA,CAAAY,YAAA,EAAK;UAELZ,EAAA,CAAAC,cAAA,cACuD;UACrDD,EAAA,CAAAW,MAAA,IACF;UAAAX,EAAA,CAAAY,YAAA,EAAK;UAkCLZ,EAhCA,CAAAsB,UAAA,KAAAsQ,4CAAA,6BAA4E,KAAAC,2CAAA,iCAAA7R,EAAA,CAAAgD,sBAAA,CAgCpD;UAqBtBhD,EAFF,CAAAC,cAAA,cAC8F,eACrE;UAoDrBD,EAnDA,CAAAsB,UAAA,KAAAwQ,4CAAA,6BAAuD,KAAAC,2CAAA,iCAAA/R,EAAA,CAAAgD,sBAAA,CAmD3B;UAYhChD,EADE,CAAAY,YAAA,EAAM,EACH;UAGHZ,EADF,CAAAC,cAAA,cAA+F,WACxF;UACHD,EAAA,CAAAsB,UAAA,KAAA0Q,iCAAA,gBAIyG;UAG3GhS,EAAA,CAAAY,YAAA,EAAM;UACNZ,EAAA,CAAAyB,SAAA,qCAG4B;UAC9BzB,EAAA,CAAAY,YAAA,EAAK;;;;;UA5LDZ,EAAA,CAAAgB,UAAA,YAAAhB,EAAA,CAAAmD,eAAA,KAAAC,GAAA,EAAA8N,GAAA,CAAA7N,mBAAA,EAAkD;UAE3BrD,EAAA,CAAAa,SAAA,GAAqD;UAArDb,EAAA,CAAAiS,WAAA,kBAAAf,GAAA,CAAAxQ,MAAA,CAAAwR,KAAA,kBAAqD;UAE7DlS,EAAA,CAAAa,SAAA,GAAgC;UAAhCb,EAAA,CAAAgB,UAAA,SAAAkQ,GAAA,CAAAxQ,MAAA,CAAAyR,QAAA,CAAAC,MAAA,KAAgC;UAK/BpS,EAAA,CAAAa,SAAA,EAAwB;UAAxBb,EAAA,CAAAgB,UAAA,aAAAkQ,GAAA,CAAAxQ,MAAA,CAAA0H,IAAA,CAAwB;UACmBpI,EAAA,CAAAa,SAAA,EAAsB;UAAtBb,EAAA,CAAAgB,UAAA,0BAAsB;UACvBhB,EAAA,CAAAa,SAAA,EAAqB;UAArBb,EAAA,CAAAgB,UAAA,yBAAqB;UAClBhB,EAAA,CAAAa,SAAA,EAAwB;UAAxBb,EAAA,CAAAgB,UAAA,4BAAwB;UAC1BhB,EAAA,CAAAa,SAAA,EAA0B;UAA1Bb,EAAA,CAAAgB,UAAA,8BAA0B;UAGpDhB,EAAA,CAAAa,SAAA,EAAqE;UAArEb,EAAA,CAAAqS,UAAA,CAAArS,EAAA,CAAAmD,eAAA,KAAAmP,GAAA,EAAApB,GAAA,CAAAvK,SAAA,CAAA4L,SAAA,GAAArB,GAAA,CAAAxQ,MAAA,CAAAwR,KAAA,cAAqE;UAClGlS,EAAA,CAAAa,SAAA,EACA;UADAb,EAAA,CAAAc,kBAAA,MAAAoQ,GAAA,CAAAxQ,MAAA,CAAAyE,KAAA,IAAA+L,GAAA,CAAAxQ,MAAA,CAAAsK,IAAA,MACA;UAAOhL,EAAA,CAAAa,SAAA,EAAmB;UAAnBb,EAAA,CAAAgB,UAAA,SAAAkQ,GAAA,CAAAxQ,MAAA,CAAA8R,MAAA,CAAmB;UACXxS,EAAA,CAAAa,SAAA,EAAwB;UAAxBb,EAAA,CAAAgB,UAAA,UAAAkQ,GAAA,CAAAlL,iBAAA,CAAwB;UAWRhG,EAAA,CAAAa,SAAA,EAAqC;UAArCb,EAAA,CAAAgB,UAAA,SAAAkQ,GAAA,CAAA1J,eAAA,IAAA0J,GAAA,CAAAxJ,YAAA,CAAqC;UAUrE1H,EAAA,CAAAa,SAAA,EAAiC;UAAjCb,EAAA,CAAAgB,UAAA,SAAAkQ,GAAA,CAAAhF,mBAAA,CAAAgF,GAAA,CAAAxQ,MAAA,EAAiC;UAMpCV,EAAA,CAAAa,SAAA,EAAkD;UAAlDb,EAAA,CAAAgB,UAAA,YAAAhB,EAAA,CAAAmD,eAAA,KAAAC,GAAA,EAAA8N,GAAA,CAAA7N,mBAAA,EAAkD;UACpDrD,EAAA,CAAAa,SAAA,EACF;UADEb,EAAA,CAAAc,kBAAA,MAAAoQ,GAAA,CAAAxQ,MAAA,CAAAsK,IAAA,OACF;UAGIhL,EAAA,CAAAa,SAAA,EAAkD;UAAlDb,EAAA,CAAAgB,UAAA,YAAAhB,EAAA,CAAAmD,eAAA,KAAAC,GAAA,EAAA8N,GAAA,CAAA7N,mBAAA,EAAkD;UACpDrD,EAAA,CAAAa,SAAA,EACF;UADEb,EAAA,CAAAc,kBAAA,MAAAoQ,GAAA,CAAAxQ,MAAA,CAAAmJ,GAAA,OACF;UAEe7J,EAAA,CAAAa,SAAA,EAA6C;UAAAb,EAA7C,CAAAgB,UAAA,UAAAkQ,GAAA,CAAAlL,iBAAA,IAAAkL,GAAA,CAAAxQ,MAAA,CAAAqD,MAAA,GAA6C,aAAA0O,aAAA,CAAc;UAoDtEzS,EAAA,CAAAa,SAAA,GAAyF;UAAzFb,EAAA,CAAAgB,UAAA,YAAAhB,EAAA,CAAAmD,eAAA,KAAAC,GAAA,EAAA8N,GAAA,CAAAlL,iBAAA,KAAAkL,GAAA,CAAAhL,eAAA,KAAAgL,GAAA,CAAAxQ,MAAA,CAAAqD,MAAA,IAAyF;UAE1E/D,EAAA,CAAAa,SAAA,GAAoB;UAAAb,EAApB,CAAAgB,UAAA,SAAAkQ,GAAA,CAAAvJ,YAAA,CAAoB,aAAA+K,iBAAA,CAAkB;UAqEnC1S,EAAA,CAAAa,SAAA,GAAoC;UAApCb,EAAA,CAAAgB,UAAA,UAAAkQ,GAAA,CAAAxQ,MAAA,CAAAiO,QAAA,OAAAuC,GAAA,CAAA/I,OAAA,CAAoC;UAOtDnI,EAAA,CAAAa,SAAA,EAAsB;UACtBb,EADA,CAAAgB,UAAA,YAAAkQ,GAAA,CAAA5K,UAAA,CAAsB,yBACE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}