{"ast": null, "code": "import { SchemaFilterMatchEnum } from '@skywind-group/lib-swui';\nimport { gameStatusClassMap, providerCodeClassMap, tagClassMap } from '../../../../../app.constants';\nimport { DISPLAY_GAME_STATUS_LIST, GAME_STATUS_LIST, LABEL_DISPLAY_TEXT } from './general-games-info/games.schema';\nimport { componentGameTypes } from '../../../../games-management/games-create/games-create.component';\nconst SCHEMA = [{\n  field: 'providerTitle',\n  title: 'ENTITY_SETUP.GAMES.MANAGE_GRID.providerTitle',\n  type: 'string',\n  isList: true,\n  isViewable: false,\n  isSortable: false,\n  isFilterable: false,\n  td: {\n    type: 'calc',\n    useTranslate: false,\n    titleFn: row => row && row.providerTitle,\n    classFn: row => {\n      let cssClass = providerCodeClassMap.DEFAULT;\n      if (providerCodeClassMap.hasOwnProperty(row.providerCode)) {\n        cssClass = providerCodeClassMap[row.providerCode];\n      }\n      return cssClass;\n    }\n  }\n}, {\n  field: 'title',\n  title: 'ENTITY_SETUP.GAMES.MANAGE_GRID.title',\n  type: 'string',\n  isList: true,\n  isViewable: false,\n  isSortable: false,\n  isFilterable: true,\n  filterMatch: SchemaFilterMatchEnum.Contains\n}, {\n  field: 'code',\n  title: 'ENTITY_SETUP.GAMES.MANAGE_GRID.code',\n  type: 'string',\n  isList: true,\n  isViewable: false,\n  isSortable: false,\n  isFilterable: true\n}, {\n  field: 'type',\n  title: 'ENTITY_SETUP.GAMES.type',\n  type: 'multiselect',\n  data: componentGameTypes,\n  isList: true,\n  isViewable: false,\n  isSortable: false,\n  isFilterable: true,\n  td: {\n    type: 'calc',\n    useTranslate: false,\n    titleFn: row => row.type,\n    classFn: () => 'sw-chip sw-chip-blue'\n  },\n  alignment: {\n    th: 'center',\n    td: 'center'\n  },\n  filterMatch: SchemaFilterMatchEnum.In\n}, {\n  field: 'status',\n  title: 'ENTITY_SETUP.GAMES.status',\n  type: 'select',\n  isList: true,\n  isViewable: true,\n  isSortable: true,\n  isFilterable: true,\n  data: GAME_STATUS_LIST,\n  td: {\n    type: 'status',\n    statusList: GAME_STATUS_LIST,\n    displayStatusList: DISPLAY_GAME_STATUS_LIST,\n    classMap: gameStatusClassMap,\n    readonly: true\n  }\n}, {\n  field: 'labels',\n  title: 'ENTITY_SETUP.GAMES.labels',\n  type: 'multiselect',\n  data: LABEL_DISPLAY_TEXT,\n  isList: true,\n  isViewable: false,\n  isSortable: false,\n  isFilterable: true,\n  td: {\n    type: 'gameslabels',\n    classMap: tagClassMap\n  },\n  filterMatch: SchemaFilterMatchEnum.In\n}];\nexport const SCHEMA_FILTER = SCHEMA.filter(el => el.isFilterable);\nexport const SCHEMA_LIST = SCHEMA.filter(el => el.isList);", "map": {"version": 3, "names": ["SchemaFilterMatchEnum", "gameStatusClassMap", "providerCodeClassMap", "tagClassMap", "DISPLAY_GAME_STATUS_LIST", "GAME_STATUS_LIST", "LABEL_DISPLAY_TEXT", "componentGameTypes", "SCHEMA", "field", "title", "type", "isList", "isViewable", "isSortable", "isFilterable", "td", "useTranslate", "titleFn", "row", "providerTitle", "classFn", "cssClass", "DEFAULT", "hasOwnProperty", "providerCode", "filterMatch", "Contains", "data", "alignment", "th", "In", "statusList", "displayStatusList", "classMap", "readonly", "SCHEMA_FILTER", "filter", "el", "SCHEMA_LIST"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-games/manage-games.schema.ts"], "sourcesContent": ["import { SchemaFilter<PERSON><PERSON><PERSON><PERSON>, SwuiGridField } from '@skywind-group/lib-swui';\nimport { gameStatusClassMap, providerCodeClassMap, tagClassMap } from '../../../../../app.constants';\nimport { DISPLAY_GAME_STATUS_LIST, GAME_STATUS_LIST, LABEL_DISPLAY_TEXT } from './general-games-info/games.schema';\nimport { componentGameTypes } from '../../../../games-management/games-create/games-create.component';\nimport { Game } from '../../../../../common/typings';\n\n\nconst SCHEMA: SwuiGridField[] = [\n  {\n    field: 'providerTitle',\n    title: 'ENTITY_SETUP.GAMES.MANAGE_GRID.providerTitle',\n    type: 'string',\n    isList: true,\n    isViewable: false,\n    isSortable: false,\n    isFilterable: false,\n    td: {\n      type: 'calc',\n      useTranslate: false,\n      titleFn: ( row: any) => row && row.providerTitle,\n      classFn: ( row: any) => {\n        let cssClass = providerCodeClassMap.DEFAULT;\n\n        if (providerCodeClassMap.hasOwnProperty(row.providerCode)) {\n          cssClass = providerCodeClassMap[row.providerCode];\n        }\n\n        return cssClass;\n      }\n    },\n  },\n  {\n    field: 'title',\n    title: 'ENTITY_SETUP.GAMES.MANAGE_GRID.title',\n    type: 'string',\n    isList: true,\n    isViewable: false,\n    isSortable: false,\n    isFilterable: true,\n    filterMatch: SchemaFilterMatchEnum.Contains,\n  },\n  {\n    field: 'code',\n    title: 'ENTITY_SETUP.GAMES.MANAGE_GRID.code',\n    type: 'string',\n    isList: true,\n    isViewable: false,\n    isSortable: false,\n    isFilterable: true,\n  },\n  {\n    field: 'type',\n    title: 'ENTITY_SETUP.GAMES.type',\n    type: 'multiselect',\n    data: componentGameTypes,\n    isList: true,\n    isViewable: false,\n    isSortable: false,\n    isFilterable: true,\n    td: {\n      type: 'calc',\n      useTranslate: false,\n      titleFn: ( row: Game ) => row.type,\n      classFn: () => 'sw-chip sw-chip-blue'\n    },\n    alignment: {\n      th: 'center',\n      td: 'center',\n    },\n    filterMatch: SchemaFilterMatchEnum.In,\n  },\n  {\n    field: 'status',\n    title: 'ENTITY_SETUP.GAMES.status',\n    type: 'select',\n    isList: true,\n    isViewable: true,\n    isSortable: true,\n    isFilterable: true,\n    data: GAME_STATUS_LIST,\n    td: {\n      type: 'status',\n      statusList: GAME_STATUS_LIST,\n      displayStatusList: DISPLAY_GAME_STATUS_LIST,\n      classMap: gameStatusClassMap,\n      readonly: true\n    },\n  },\n  {\n    field: 'labels',\n    title: 'ENTITY_SETUP.GAMES.labels',\n    type: 'multiselect',\n    data: LABEL_DISPLAY_TEXT,\n    isList: true,\n    isViewable: false,\n    isSortable: false,\n    isFilterable: true,\n    td: {\n      type: 'gameslabels',\n      classMap: tagClassMap,\n    },\n    filterMatch: SchemaFilterMatchEnum.In,\n  },\n];\n\n\nexport const SCHEMA_FILTER = SCHEMA.filter(el => el.isFilterable);\nexport const SCHEMA_LIST = SCHEMA.filter(el => el.isList);\n"], "mappings": "AAAA,SAASA,qBAAqB,QAAuB,yBAAyB;AAC9E,SAASC,kBAAkB,EAAEC,oBAAoB,EAAEC,WAAW,QAAQ,8BAA8B;AACpG,SAASC,wBAAwB,EAAEC,gBAAgB,EAAEC,kBAAkB,QAAQ,mCAAmC;AAClH,SAASC,kBAAkB,QAAQ,kEAAkE;AAIrG,MAAMC,MAAM,GAAoB,CAC9B;EACEC,KAAK,EAAE,eAAe;EACtBC,KAAK,EAAE,8CAA8C;EACrDC,IAAI,EAAE,QAAQ;EACdC,MAAM,EAAE,IAAI;EACZC,UAAU,EAAE,KAAK;EACjBC,UAAU,EAAE,KAAK;EACjBC,YAAY,EAAE,KAAK;EACnBC,EAAE,EAAE;IACFL,IAAI,EAAE,MAAM;IACZM,YAAY,EAAE,KAAK;IACnBC,OAAO,EAAIC,GAAQ,IAAKA,GAAG,IAAIA,GAAG,CAACC,aAAa;IAChDC,OAAO,EAAIF,GAAQ,IAAI;MACrB,IAAIG,QAAQ,GAAGpB,oBAAoB,CAACqB,OAAO;MAE3C,IAAIrB,oBAAoB,CAACsB,cAAc,CAACL,GAAG,CAACM,YAAY,CAAC,EAAE;QACzDH,QAAQ,GAAGpB,oBAAoB,CAACiB,GAAG,CAACM,YAAY,CAAC;MACnD;MAEA,OAAOH,QAAQ;IACjB;;CAEH,EACD;EACEb,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE,sCAAsC;EAC7CC,IAAI,EAAE,QAAQ;EACdC,MAAM,EAAE,IAAI;EACZC,UAAU,EAAE,KAAK;EACjBC,UAAU,EAAE,KAAK;EACjBC,YAAY,EAAE,IAAI;EAClBW,WAAW,EAAE1B,qBAAqB,CAAC2B;CACpC,EACD;EACElB,KAAK,EAAE,MAAM;EACbC,KAAK,EAAE,qCAAqC;EAC5CC,IAAI,EAAE,QAAQ;EACdC,MAAM,EAAE,IAAI;EACZC,UAAU,EAAE,KAAK;EACjBC,UAAU,EAAE,KAAK;EACjBC,YAAY,EAAE;CACf,EACD;EACEN,KAAK,EAAE,MAAM;EACbC,KAAK,EAAE,yBAAyB;EAChCC,IAAI,EAAE,aAAa;EACnBiB,IAAI,EAAErB,kBAAkB;EACxBK,MAAM,EAAE,IAAI;EACZC,UAAU,EAAE,KAAK;EACjBC,UAAU,EAAE,KAAK;EACjBC,YAAY,EAAE,IAAI;EAClBC,EAAE,EAAE;IACFL,IAAI,EAAE,MAAM;IACZM,YAAY,EAAE,KAAK;IACnBC,OAAO,EAAIC,GAAS,IAAMA,GAAG,CAACR,IAAI;IAClCU,OAAO,EAAEA,CAAA,KAAM;GAChB;EACDQ,SAAS,EAAE;IACTC,EAAE,EAAE,QAAQ;IACZd,EAAE,EAAE;GACL;EACDU,WAAW,EAAE1B,qBAAqB,CAAC+B;CACpC,EACD;EACEtB,KAAK,EAAE,QAAQ;EACfC,KAAK,EAAE,2BAA2B;EAClCC,IAAI,EAAE,QAAQ;EACdC,MAAM,EAAE,IAAI;EACZC,UAAU,EAAE,IAAI;EAChBC,UAAU,EAAE,IAAI;EAChBC,YAAY,EAAE,IAAI;EAClBa,IAAI,EAAEvB,gBAAgB;EACtBW,EAAE,EAAE;IACFL,IAAI,EAAE,QAAQ;IACdqB,UAAU,EAAE3B,gBAAgB;IAC5B4B,iBAAiB,EAAE7B,wBAAwB;IAC3C8B,QAAQ,EAAEjC,kBAAkB;IAC5BkC,QAAQ,EAAE;;CAEb,EACD;EACE1B,KAAK,EAAE,QAAQ;EACfC,KAAK,EAAE,2BAA2B;EAClCC,IAAI,EAAE,aAAa;EACnBiB,IAAI,EAAEtB,kBAAkB;EACxBM,MAAM,EAAE,IAAI;EACZC,UAAU,EAAE,KAAK;EACjBC,UAAU,EAAE,KAAK;EACjBC,YAAY,EAAE,IAAI;EAClBC,EAAE,EAAE;IACFL,IAAI,EAAE,aAAa;IACnBuB,QAAQ,EAAE/B;GACX;EACDuB,WAAW,EAAE1B,qBAAqB,CAAC+B;CACpC,CACF;AAGD,OAAO,MAAMK,aAAa,GAAG5B,MAAM,CAAC6B,MAAM,CAACC,EAAE,IAAIA,EAAE,CAACvB,YAAY,CAAC;AACjE,OAAO,MAAMwB,WAAW,GAAG/B,MAAM,CAAC6B,MAAM,CAACC,EAAE,IAAIA,EAAE,CAAC1B,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}