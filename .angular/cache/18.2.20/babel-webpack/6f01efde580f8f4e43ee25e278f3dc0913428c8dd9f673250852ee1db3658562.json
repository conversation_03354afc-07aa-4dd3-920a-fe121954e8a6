{"ast": null, "code": "'use strict';\n\nvar objectCreate = void 0;\nif (typeof Object.create != 'function') {\n  objectCreate = function (undefined) {\n    var Temp = function Temp() {};\n    return function (prototype, propertiesObject) {\n      if (prototype !== Object(prototype) && prototype !== null) {\n        throw TypeError('Argument must be an object, or null');\n      }\n      Temp.prototype = prototype || {};\n      var result = new Temp();\n      Temp.prototype = null;\n      if (propertiesObject !== undefined) {\n        Object.defineProperties(result, propertiesObject);\n      }\n\n      // to imitate the case of Object.create(null)\n      if (prototype === null) {\n        result.__proto__ = null;\n      }\n      return result;\n    };\n  }();\n} else {\n  objectCreate = Object.create;\n}\nvar objectCreate$1 = objectCreate;\nvar mouseEventProps = ['altKey', 'button', 'buttons', 'clientX', 'clientY', 'ctrlKey', 'metaKey', 'movementX', 'movementY', 'offsetX', 'offsetY', 'pageX', 'pageY', 'region', 'relatedTarget', 'screenX', 'screenY', 'shiftKey', 'which', 'x', 'y'];\nfunction createDispatcher(element) {\n  var defaultSettings = {\n    screenX: 0,\n    screenY: 0,\n    clientX: 0,\n    clientY: 0,\n    ctrlKey: false,\n    shiftKey: false,\n    altKey: false,\n    metaKey: false,\n    button: 0,\n    buttons: 1,\n    relatedTarget: null,\n    region: null\n  };\n  if (element !== undefined) {\n    element.addEventListener('mousemove', onMove);\n  }\n  function onMove(e) {\n    for (var i = 0; i < mouseEventProps.length; i++) {\n      defaultSettings[mouseEventProps[i]] = e[mouseEventProps[i]];\n    }\n  }\n  var dispatch = function () {\n    if (MouseEvent) {\n      return function m1(element, initMove, data) {\n        var evt = new MouseEvent('mousemove', createMoveInit(defaultSettings, initMove));\n\n        //evt.dispatched = 'mousemove';\n        setSpecial(evt, data);\n        return element.dispatchEvent(evt);\n      };\n    } else if (typeof document.createEvent === 'function') {\n      return function m2(element, initMove, data) {\n        var settings = createMoveInit(defaultSettings, initMove);\n        var evt = document.createEvent('MouseEvents');\n        evt.initMouseEvent(\"mousemove\", true,\n        //can bubble\n        true,\n        //cancelable\n        window,\n        //view\n        0,\n        //detail\n        settings.screenX,\n        //0, //screenX\n        settings.screenY,\n        //0, //screenY\n        settings.clientX,\n        //80, //clientX\n        settings.clientY,\n        //20, //clientY\n        settings.ctrlKey,\n        //false, //ctrlKey\n        settings.altKey,\n        //false, //altKey\n        settings.shiftKey,\n        //false, //shiftKey\n        settings.metaKey,\n        //false, //metaKey\n        settings.button,\n        //0, //button\n        settings.relatedTarget //null //relatedTarget\n        );\n\n        //evt.dispatched = 'mousemove';\n        setSpecial(evt, data);\n        return element.dispatchEvent(evt);\n      };\n    } else if (typeof document.createEventObject === 'function') {\n      return function m3(element, initMove, data) {\n        var evt = document.createEventObject();\n        var settings = createMoveInit(defaultSettings, initMove);\n        for (var name in settings) {\n          evt[name] = settings[name];\n        }\n\n        //evt.dispatched = 'mousemove';\n        setSpecial(evt, data);\n        return element.dispatchEvent(evt);\n      };\n    }\n  }();\n  function destroy() {\n    if (element) element.removeEventListener('mousemove', onMove, false);\n    defaultSettings = null;\n  }\n  return {\n    destroy: destroy,\n    dispatch: dispatch\n  };\n}\nfunction createMoveInit(defaultSettings, initMove) {\n  initMove = initMove || {};\n  var settings = objectCreate$1(defaultSettings);\n  for (var i = 0; i < mouseEventProps.length; i++) {\n    if (initMove[mouseEventProps[i]] !== undefined) settings[mouseEventProps[i]] = initMove[mouseEventProps[i]];\n  }\n  return settings;\n}\nfunction setSpecial(e, data) {\n  console.log('data ', data);\n  e.data = data || {};\n  e.dispatched = 'mousemove';\n}\n\n/*\nhttp://marcgrabanski.com/simulating-mouse-click-events-in-javascript/\n*/\n\nmodule.exports = createDispatcher;", "map": {"version": 3, "names": ["objectCreate", "Object", "create", "undefined", "Temp", "prototype", "propertiesObject", "TypeError", "result", "defineProperties", "__proto__", "objectCreate$1", "mouseEventProps", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "element", "defaultSettings", "screenX", "screenY", "clientX", "clientY", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "metaKey", "button", "buttons", "relatedTarget", "region", "addEventListener", "onMove", "e", "i", "length", "dispatch", "MouseEvent", "m1", "initMove", "data", "evt", "createMoveInit", "setSpecial", "dispatchEvent", "document", "createEvent", "m2", "settings", "initMouseEvent", "window", "createEventObject", "m3", "name", "destroy", "removeEventListener", "console", "log", "dispatched", "module", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/node_modules/dom-mousemove-dispatcher/dist/bundle.js"], "sourcesContent": ["'use strict';\n\nvar objectCreate = void 0;\nif (typeof Object.create != 'function') {\n  objectCreate = function (undefined) {\n    var Temp = function Temp() {};\n    return function (prototype, propertiesObject) {\n      if (prototype !== Object(prototype) && prototype !== null) {\n        throw TypeError('Argument must be an object, or null');\n      }\n      Temp.prototype = prototype || {};\n      var result = new Temp();\n      Temp.prototype = null;\n      if (propertiesObject !== undefined) {\n        Object.defineProperties(result, propertiesObject);\n      }\n\n      // to imitate the case of Object.create(null)\n      if (prototype === null) {\n        result.__proto__ = null;\n      }\n      return result;\n    };\n  }();\n} else {\n  objectCreate = Object.create;\n}\n\nvar objectCreate$1 = objectCreate;\n\nvar mouseEventProps = ['altKey', 'button', 'buttons', 'clientX', 'clientY', 'ctrlKey', 'metaKey', 'movementX', 'movementY', 'offsetX', 'offsetY', 'pageX', 'pageY', 'region', 'relatedTarget', 'screenX', 'screenY', 'shiftKey', 'which', 'x', 'y'];\n\nfunction createDispatcher(element) {\n\n    var defaultSettings = {\n        screenX: 0,\n        screenY: 0,\n        clientX: 0,\n        clientY: 0,\n        ctrlKey: false,\n        shiftKey: false,\n        altKey: false,\n        metaKey: false,\n        button: 0,\n        buttons: 1,\n        relatedTarget: null,\n        region: null\n    };\n\n    if (element !== undefined) {\n        element.addEventListener('mousemove', onMove);\n    }\n\n    function onMove(e) {\n        for (var i = 0; i < mouseEventProps.length; i++) {\n            defaultSettings[mouseEventProps[i]] = e[mouseEventProps[i]];\n        }\n    }\n\n    var dispatch = function () {\n        if (MouseEvent) {\n            return function m1(element, initMove, data) {\n                var evt = new MouseEvent('mousemove', createMoveInit(defaultSettings, initMove));\n\n                //evt.dispatched = 'mousemove';\n                setSpecial(evt, data);\n\n                return element.dispatchEvent(evt);\n            };\n        } else if (typeof document.createEvent === 'function') {\n            return function m2(element, initMove, data) {\n                var settings = createMoveInit(defaultSettings, initMove);\n                var evt = document.createEvent('MouseEvents');\n\n                evt.initMouseEvent(\"mousemove\", true, //can bubble\n                true, //cancelable\n                window, //view\n                0, //detail\n                settings.screenX, //0, //screenX\n                settings.screenY, //0, //screenY\n                settings.clientX, //80, //clientX\n                settings.clientY, //20, //clientY\n                settings.ctrlKey, //false, //ctrlKey\n                settings.altKey, //false, //altKey\n                settings.shiftKey, //false, //shiftKey\n                settings.metaKey, //false, //metaKey\n                settings.button, //0, //button\n                settings.relatedTarget //null //relatedTarget\n                );\n\n                //evt.dispatched = 'mousemove';\n                setSpecial(evt, data);\n\n                return element.dispatchEvent(evt);\n            };\n        } else if (typeof document.createEventObject === 'function') {\n            return function m3(element, initMove, data) {\n                var evt = document.createEventObject();\n                var settings = createMoveInit(defaultSettings, initMove);\n                for (var name in settings) {\n                    evt[name] = settings[name];\n                }\n\n                //evt.dispatched = 'mousemove';\n                setSpecial(evt, data);\n\n                return element.dispatchEvent(evt);\n            };\n        }\n    }();\n\n    function destroy() {\n        if (element) element.removeEventListener('mousemove', onMove, false);\n        defaultSettings = null;\n    }\n\n    return {\n        destroy: destroy,\n        dispatch: dispatch\n    };\n}\n\nfunction createMoveInit(defaultSettings, initMove) {\n    initMove = initMove || {};\n    var settings = objectCreate$1(defaultSettings);\n    for (var i = 0; i < mouseEventProps.length; i++) {\n        if (initMove[mouseEventProps[i]] !== undefined) settings[mouseEventProps[i]] = initMove[mouseEventProps[i]];\n    }\n\n    return settings;\n}\n\nfunction setSpecial(e, data) {\n    console.log('data ', data);\n    e.data = data || {};\n    e.dispatched = 'mousemove';\n}\n\n/*\nhttp://marcgrabanski.com/simulating-mouse-click-events-in-javascript/\n*/\n\nmodule.exports = createDispatcher;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,YAAY,GAAG,KAAK,CAAC;AACzB,IAAI,OAAOC,MAAM,CAACC,MAAM,IAAI,UAAU,EAAE;EACtCF,YAAY,GAAG,UAAUG,SAAS,EAAE;IAClC,IAAIC,IAAI,GAAG,SAASA,IAAIA,CAAA,EAAG,CAAC,CAAC;IAC7B,OAAO,UAAUC,SAAS,EAAEC,gBAAgB,EAAE;MAC5C,IAAID,SAAS,KAAKJ,MAAM,CAACI,SAAS,CAAC,IAAIA,SAAS,KAAK,IAAI,EAAE;QACzD,MAAME,SAAS,CAAC,qCAAqC,CAAC;MACxD;MACAH,IAAI,CAACC,SAAS,GAAGA,SAAS,IAAI,CAAC,CAAC;MAChC,IAAIG,MAAM,GAAG,IAAIJ,IAAI,CAAC,CAAC;MACvBA,IAAI,CAACC,SAAS,GAAG,IAAI;MACrB,IAAIC,gBAAgB,KAAKH,SAAS,EAAE;QAClCF,MAAM,CAACQ,gBAAgB,CAACD,MAAM,EAAEF,gBAAgB,CAAC;MACnD;;MAEA;MACA,IAAID,SAAS,KAAK,IAAI,EAAE;QACtBG,MAAM,CAACE,SAAS,GAAG,IAAI;MACzB;MACA,OAAOF,MAAM;IACf,CAAC;EACH,CAAC,CAAC,CAAC;AACL,CAAC,MAAM;EACLR,YAAY,GAAGC,MAAM,CAACC,MAAM;AAC9B;AAEA,IAAIS,cAAc,GAAGX,YAAY;AAEjC,IAAIY,eAAe,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,eAAe,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,CAAC;AAEnP,SAASC,gBAAgBA,CAACC,OAAO,EAAE;EAE/B,IAAIC,eAAe,GAAG;IAClBC,OAAO,EAAE,CAAC;IACVC,OAAO,EAAE,CAAC;IACVC,OAAO,EAAE,CAAC;IACVC,OAAO,EAAE,CAAC;IACVC,OAAO,EAAE,KAAK;IACdC,QAAQ,EAAE,KAAK;IACfC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,KAAK;IACdC,MAAM,EAAE,CAAC;IACTC,OAAO,EAAE,CAAC;IACVC,aAAa,EAAE,IAAI;IACnBC,MAAM,EAAE;EACZ,CAAC;EAED,IAAIb,OAAO,KAAKX,SAAS,EAAE;IACvBW,OAAO,CAACc,gBAAgB,CAAC,WAAW,EAAEC,MAAM,CAAC;EACjD;EAEA,SAASA,MAAMA,CAACC,CAAC,EAAE;IACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnB,eAAe,CAACoB,MAAM,EAAED,CAAC,EAAE,EAAE;MAC7ChB,eAAe,CAACH,eAAe,CAACmB,CAAC,CAAC,CAAC,GAAGD,CAAC,CAAClB,eAAe,CAACmB,CAAC,CAAC,CAAC;IAC/D;EACJ;EAEA,IAAIE,QAAQ,GAAG,YAAY;IACvB,IAAIC,UAAU,EAAE;MACZ,OAAO,SAASC,EAAEA,CAACrB,OAAO,EAAEsB,QAAQ,EAAEC,IAAI,EAAE;QACxC,IAAIC,GAAG,GAAG,IAAIJ,UAAU,CAAC,WAAW,EAAEK,cAAc,CAACxB,eAAe,EAAEqB,QAAQ,CAAC,CAAC;;QAEhF;QACAI,UAAU,CAACF,GAAG,EAAED,IAAI,CAAC;QAErB,OAAOvB,OAAO,CAAC2B,aAAa,CAACH,GAAG,CAAC;MACrC,CAAC;IACL,CAAC,MAAM,IAAI,OAAOI,QAAQ,CAACC,WAAW,KAAK,UAAU,EAAE;MACnD,OAAO,SAASC,EAAEA,CAAC9B,OAAO,EAAEsB,QAAQ,EAAEC,IAAI,EAAE;QACxC,IAAIQ,QAAQ,GAAGN,cAAc,CAACxB,eAAe,EAAEqB,QAAQ,CAAC;QACxD,IAAIE,GAAG,GAAGI,QAAQ,CAACC,WAAW,CAAC,aAAa,CAAC;QAE7CL,GAAG,CAACQ,cAAc,CAAC,WAAW,EAAE,IAAI;QAAE;QACtC,IAAI;QAAE;QACNC,MAAM;QAAE;QACR,CAAC;QAAE;QACHF,QAAQ,CAAC7B,OAAO;QAAE;QAClB6B,QAAQ,CAAC5B,OAAO;QAAE;QAClB4B,QAAQ,CAAC3B,OAAO;QAAE;QAClB2B,QAAQ,CAAC1B,OAAO;QAAE;QAClB0B,QAAQ,CAACzB,OAAO;QAAE;QAClByB,QAAQ,CAACvB,MAAM;QAAE;QACjBuB,QAAQ,CAACxB,QAAQ;QAAE;QACnBwB,QAAQ,CAACtB,OAAO;QAAE;QAClBsB,QAAQ,CAACrB,MAAM;QAAE;QACjBqB,QAAQ,CAACnB,aAAa,CAAC;QACvB,CAAC;;QAED;QACAc,UAAU,CAACF,GAAG,EAAED,IAAI,CAAC;QAErB,OAAOvB,OAAO,CAAC2B,aAAa,CAACH,GAAG,CAAC;MACrC,CAAC;IACL,CAAC,MAAM,IAAI,OAAOI,QAAQ,CAACM,iBAAiB,KAAK,UAAU,EAAE;MACzD,OAAO,SAASC,EAAEA,CAACnC,OAAO,EAAEsB,QAAQ,EAAEC,IAAI,EAAE;QACxC,IAAIC,GAAG,GAAGI,QAAQ,CAACM,iBAAiB,CAAC,CAAC;QACtC,IAAIH,QAAQ,GAAGN,cAAc,CAACxB,eAAe,EAAEqB,QAAQ,CAAC;QACxD,KAAK,IAAIc,IAAI,IAAIL,QAAQ,EAAE;UACvBP,GAAG,CAACY,IAAI,CAAC,GAAGL,QAAQ,CAACK,IAAI,CAAC;QAC9B;;QAEA;QACAV,UAAU,CAACF,GAAG,EAAED,IAAI,CAAC;QAErB,OAAOvB,OAAO,CAAC2B,aAAa,CAACH,GAAG,CAAC;MACrC,CAAC;IACL;EACJ,CAAC,CAAC,CAAC;EAEH,SAASa,OAAOA,CAAA,EAAG;IACf,IAAIrC,OAAO,EAAEA,OAAO,CAACsC,mBAAmB,CAAC,WAAW,EAAEvB,MAAM,EAAE,KAAK,CAAC;IACpEd,eAAe,GAAG,IAAI;EAC1B;EAEA,OAAO;IACHoC,OAAO,EAAEA,OAAO;IAChBlB,QAAQ,EAAEA;EACd,CAAC;AACL;AAEA,SAASM,cAAcA,CAACxB,eAAe,EAAEqB,QAAQ,EAAE;EAC/CA,QAAQ,GAAGA,QAAQ,IAAI,CAAC,CAAC;EACzB,IAAIS,QAAQ,GAAGlC,cAAc,CAACI,eAAe,CAAC;EAC9C,KAAK,IAAIgB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnB,eAAe,CAACoB,MAAM,EAAED,CAAC,EAAE,EAAE;IAC7C,IAAIK,QAAQ,CAACxB,eAAe,CAACmB,CAAC,CAAC,CAAC,KAAK5B,SAAS,EAAE0C,QAAQ,CAACjC,eAAe,CAACmB,CAAC,CAAC,CAAC,GAAGK,QAAQ,CAACxB,eAAe,CAACmB,CAAC,CAAC,CAAC;EAC/G;EAEA,OAAOc,QAAQ;AACnB;AAEA,SAASL,UAAUA,CAACV,CAAC,EAAEO,IAAI,EAAE;EACzBgB,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEjB,IAAI,CAAC;EAC1BP,CAAC,CAACO,IAAI,GAAGA,IAAI,IAAI,CAAC,CAAC;EACnBP,CAAC,CAACyB,UAAU,GAAG,WAAW;AAC9B;;AAEA;AACA;AACA;;AAEAC,MAAM,CAACC,OAAO,GAAG5C,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}