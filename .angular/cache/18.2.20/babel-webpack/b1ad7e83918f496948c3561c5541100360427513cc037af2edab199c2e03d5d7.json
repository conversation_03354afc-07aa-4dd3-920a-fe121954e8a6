{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { SwuiGridModule } from '@skywind-group/lib-swui';\nimport { DownloadCsvModule } from '../../../../../../common/components/download-csv/download-csv.module';\nimport { GameService } from '../../../../../../common/services/game.service';\nimport { IframeViewModalModule } from '../../../../../gamehistory/components/internal-game-history/modals/iframe-view-modal/iframe-view-modal.module';\nimport { CustomerGeneralGameHistoryComponent } from './customer-general-game-history.component';\nimport * as i0 from \"@angular/core\";\nexport class CustomerGeneralGameHistoryModule {\n  static {\n    this.ɵfac = function CustomerGeneralGameHistoryModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CustomerGeneralGameHistoryModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: CustomerGeneralGameHistoryModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [GameService],\n      imports: [CommonModule, SwuiGridModule, IframeViewModalModule, MatButtonModule, MatTooltipModule, MatIconModule, DownloadCsvModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(CustomerGeneralGameHistoryModule, {\n    declarations: [CustomerGeneralGameHistoryComponent],\n    imports: [CommonModule, SwuiGridModule, IframeViewModalModule, MatButtonModule, MatTooltipModule, MatIconModule, DownloadCsvModule],\n    exports: [CustomerGeneralGameHistoryComponent]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "MatButtonModule", "MatIconModule", "MatTooltipModule", "SwuiGridModule", "DownloadCsvModule", "GameService", "IframeViewModalModule", "CustomerGeneralGameHistoryComponent", "CustomerGeneralGameHistoryModule", "imports", "declarations", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/player/components/customer-page/customer-game-history/customer-general-game-history/customer-general-game-history.module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { SwuiGridModule } from '@skywind-group/lib-swui';\nimport { DownloadCsvModule } from '../../../../../../common/components/download-csv/download-csv.module';\nimport { GameService } from '../../../../../../common/services/game.service';\nimport { IframeViewModalComponent } from '../../../../../gamehistory/components/internal-game-history/modals/iframe-view-modal/iframe-view-modal.component';\nimport { IframeViewModalModule } from '../../../../../gamehistory/components/internal-game-history/modals/iframe-view-modal/iframe-view-modal.module';\n\nimport { CustomerGeneralGameHistoryComponent } from './customer-general-game-history.component';\n\n\n@NgModule({\n  declarations: [\n    CustomerGeneralGameHistoryComponent\n  ],\n  exports: [\n    CustomerGeneralGameHistoryComponent\n  ],\n    imports: [\n        CommonModule,\n        SwuiGridModule,\n        IframeViewModalModule,\n        MatButtonModule,\n        MatTooltipModule,\n        MatIconModule,\n        DownloadCsvModule,\n    ],\n  providers: [\n    GameService,\n  ],\n})\nexport class CustomerGeneralGameHistoryModule {\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,iBAAiB,QAAQ,sEAAsE;AACxG,SAASC,WAAW,QAAQ,gDAAgD;AAE5E,SAASC,qBAAqB,QAAQ,+GAA+G;AAErJ,SAASC,mCAAmC,QAAQ,2CAA2C;;AAuB/F,OAAM,MAAOC,gCAAgC;;;uCAAhCA,gCAAgC;IAAA;EAAA;;;YAAhCA;IAAgC;EAAA;;;iBAJhC,CACTH,WAAW,CACZ;MAAAI,OAAA,GAVKV,YAAY,EACZI,cAAc,EACdG,qBAAqB,EACrBN,eAAe,EACfE,gBAAgB,EAChBD,aAAa,EACbG,iBAAiB;IAAA;EAAA;;;2EAMZI,gCAAgC;IAAAE,YAAA,GAlBzCH,mCAAmC;IAAAE,OAAA,GAM/BV,YAAY,EACZI,cAAc,EACdG,qBAAqB,EACrBN,eAAe,EACfE,gBAAgB,EAChBD,aAAa,EACbG,iBAAiB;IAAAO,OAAA,GATrBJ,mCAAmC;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}