{"ast": null, "code": "'use strict';\n\nvar typeFunc = require('type-func');\nfunction createPointCB(object, options) {\n  // A persistent object (as opposed to returned object) is used to save memory\n  // This is good to prevent layout thrashing, or for games, and such\n\n  // NOTE\n  // This uses IE fixes which should be OK to remove some day. :)\n  // Some speed will be gained by removal of these.\n\n  // pointCB should be saved in a variable on return\n  // This allows the usage of element.removeEventListener\n\n  options = options || {};\n  var allowUpdate = typeFunc.boolean(options.allowUpdate, true);\n\n  /*if(typeof options.allowUpdate === 'function'){\n      allowUpdate = options.allowUpdate;\n  }else{\n      allowUpdate = function(){return true;};\n  }*/\n\n  return function pointCB(event) {\n    event = event || window.event; // IE-ism\n    object.target = event.target || event.srcElement || event.originalTarget;\n    object.element = this;\n    object.type = event.type;\n    if (!allowUpdate(event)) {\n      return;\n    }\n\n    // Support touch\n    // http://www.creativebloq.com/javascript/make-your-site-work-touch-devices-51411644\n\n    if (event.targetTouches) {\n      object.x = event.targetTouches[0].clientX;\n      object.y = event.targetTouches[0].clientY;\n      object.pageX = event.targetTouches[0].pageX;\n      object.pageY = event.targetTouches[0].pageY;\n      object.screenX = event.targetTouches[0].screenX;\n      object.screenY = event.targetTouches[0].screenY;\n    } else {\n      // If pageX/Y aren't available and clientX/Y are,\n      // calculate pageX/Y - logic taken from jQuery.\n      // (This is to support old IE)\n      // NOTE Hopefully this can be removed soon.\n\n      if (event.pageX === null && event.clientX !== null) {\n        var eventDoc = event.target && event.target.ownerDocument || document;\n        var doc = eventDoc.documentElement;\n        var body = eventDoc.body;\n        object.pageX = event.clientX + (doc && doc.scrollLeft || body && body.scrollLeft || 0) - (doc && doc.clientLeft || body && body.clientLeft || 0);\n        object.pageY = event.clientY + (doc && doc.scrollTop || body && body.scrollTop || 0) - (doc && doc.clientTop || body && body.clientTop || 0);\n      } else {\n        object.pageX = event.pageX;\n        object.pageY = event.pageY;\n      }\n\n      // pageX, and pageY change with page scroll\n      // so we're not going to use those for x, and y.\n      // NOTE Most browsers also alias clientX/Y with x/y\n      // so that's something to consider down the road.\n\n      object.x = event.clientX;\n      object.y = event.clientY;\n      object.screenX = event.screenX;\n      object.screenY = event.screenY;\n    }\n    object.clientX = object.x;\n    object.clientY = object.y;\n  };\n\n  //NOTE Remember accessibility, Aria roles, and labels.\n}\n\n/*\ngit remote add origin https://github.com/hollowdoor/create_point_cb.git\ngit push -u origin master\n*/\n\nmodule.exports = createPointCB;", "map": {"version": 3, "names": ["typeFunc", "require", "createPointCB", "object", "options", "allowUpdate", "boolean", "pointCB", "event", "window", "target", "srcElement", "originalTarget", "element", "type", "targetTouches", "x", "clientX", "y", "clientY", "pageX", "pageY", "screenX", "screenY", "eventDoc", "ownerDocument", "document", "doc", "documentElement", "body", "scrollLeft", "clientLeft", "scrollTop", "clientTop", "module", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/node_modules/create-point-cb/dist/bundle.js"], "sourcesContent": ["'use strict';\n\nvar typeFunc = require('type-func');\n\nfunction createPointCB(object, options) {\n\n    // A persistent object (as opposed to returned object) is used to save memory\n    // This is good to prevent layout thrashing, or for games, and such\n\n    // NOTE\n    // This uses IE fixes which should be OK to remove some day. :)\n    // Some speed will be gained by removal of these.\n\n    // pointCB should be saved in a variable on return\n    // This allows the usage of element.removeEventListener\n\n    options = options || {};\n\n    var allowUpdate = typeFunc.boolean(options.allowUpdate, true);\n\n    /*if(typeof options.allowUpdate === 'function'){\n        allowUpdate = options.allowUpdate;\n    }else{\n        allowUpdate = function(){return true;};\n    }*/\n\n    return function pointCB(event) {\n\n        event = event || window.event; // IE-ism\n        object.target = event.target || event.srcElement || event.originalTarget;\n        object.element = this;\n        object.type = event.type;\n\n        if (!allowUpdate(event)) {\n            return;\n        }\n\n        // Support touch\n        // http://www.creativebloq.com/javascript/make-your-site-work-touch-devices-51411644\n\n        if (event.targetTouches) {\n            object.x = event.targetTouches[0].clientX;\n            object.y = event.targetTouches[0].clientY;\n            object.pageX = event.targetTouches[0].pageX;\n            object.pageY = event.targetTouches[0].pageY;\n            object.screenX = event.targetTouches[0].screenX;\n            object.screenY = event.targetTouches[0].screenY;\n        } else {\n\n            // If pageX/Y aren't available and clientX/Y are,\n            // calculate pageX/Y - logic taken from jQuery.\n            // (This is to support old IE)\n            // NOTE Hopefully this can be removed soon.\n\n            if (event.pageX === null && event.clientX !== null) {\n                var eventDoc = event.target && event.target.ownerDocument || document;\n                var doc = eventDoc.documentElement;\n                var body = eventDoc.body;\n\n                object.pageX = event.clientX + (doc && doc.scrollLeft || body && body.scrollLeft || 0) - (doc && doc.clientLeft || body && body.clientLeft || 0);\n                object.pageY = event.clientY + (doc && doc.scrollTop || body && body.scrollTop || 0) - (doc && doc.clientTop || body && body.clientTop || 0);\n            } else {\n                object.pageX = event.pageX;\n                object.pageY = event.pageY;\n            }\n\n            // pageX, and pageY change with page scroll\n            // so we're not going to use those for x, and y.\n            // NOTE Most browsers also alias clientX/Y with x/y\n            // so that's something to consider down the road.\n\n            object.x = event.clientX;\n            object.y = event.clientY;\n\n            object.screenX = event.screenX;\n            object.screenY = event.screenY;\n        }\n\n        object.clientX = object.x;\n        object.clientY = object.y;\n    };\n\n    //NOTE Remember accessibility, Aria roles, and labels.\n}\n\n/*\ngit remote add origin https://github.com/hollowdoor/create_point_cb.git\ngit push -u origin master\n*/\n\nmodule.exports = createPointCB;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,QAAQ,GAAGC,OAAO,CAAC,WAAW,CAAC;AAEnC,SAASC,aAAaA,CAACC,MAAM,EAAEC,OAAO,EAAE;EAEpC;EACA;;EAEA;EACA;EACA;;EAEA;EACA;;EAEAA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EAEvB,IAAIC,WAAW,GAAGL,QAAQ,CAACM,OAAO,CAACF,OAAO,CAACC,WAAW,EAAE,IAAI,CAAC;;EAE7D;AACJ;AACA;AACA;AACA;;EAEI,OAAO,SAASE,OAAOA,CAACC,KAAK,EAAE;IAE3BA,KAAK,GAAGA,KAAK,IAAIC,MAAM,CAACD,KAAK,CAAC,CAAC;IAC/BL,MAAM,CAACO,MAAM,GAAGF,KAAK,CAACE,MAAM,IAAIF,KAAK,CAACG,UAAU,IAAIH,KAAK,CAACI,cAAc;IACxET,MAAM,CAACU,OAAO,GAAG,IAAI;IACrBV,MAAM,CAACW,IAAI,GAAGN,KAAK,CAACM,IAAI;IAExB,IAAI,CAACT,WAAW,CAACG,KAAK,CAAC,EAAE;MACrB;IACJ;;IAEA;IACA;;IAEA,IAAIA,KAAK,CAACO,aAAa,EAAE;MACrBZ,MAAM,CAACa,CAAC,GAAGR,KAAK,CAACO,aAAa,CAAC,CAAC,CAAC,CAACE,OAAO;MACzCd,MAAM,CAACe,CAAC,GAAGV,KAAK,CAACO,aAAa,CAAC,CAAC,CAAC,CAACI,OAAO;MACzChB,MAAM,CAACiB,KAAK,GAAGZ,KAAK,CAACO,aAAa,CAAC,CAAC,CAAC,CAACK,KAAK;MAC3CjB,MAAM,CAACkB,KAAK,GAAGb,KAAK,CAACO,aAAa,CAAC,CAAC,CAAC,CAACM,KAAK;MAC3ClB,MAAM,CAACmB,OAAO,GAAGd,KAAK,CAACO,aAAa,CAAC,CAAC,CAAC,CAACO,OAAO;MAC/CnB,MAAM,CAACoB,OAAO,GAAGf,KAAK,CAACO,aAAa,CAAC,CAAC,CAAC,CAACQ,OAAO;IACnD,CAAC,MAAM;MAEH;MACA;MACA;MACA;;MAEA,IAAIf,KAAK,CAACY,KAAK,KAAK,IAAI,IAAIZ,KAAK,CAACS,OAAO,KAAK,IAAI,EAAE;QAChD,IAAIO,QAAQ,GAAGhB,KAAK,CAACE,MAAM,IAAIF,KAAK,CAACE,MAAM,CAACe,aAAa,IAAIC,QAAQ;QACrE,IAAIC,GAAG,GAAGH,QAAQ,CAACI,eAAe;QAClC,IAAIC,IAAI,GAAGL,QAAQ,CAACK,IAAI;QAExB1B,MAAM,CAACiB,KAAK,GAAGZ,KAAK,CAACS,OAAO,IAAIU,GAAG,IAAIA,GAAG,CAACG,UAAU,IAAID,IAAI,IAAIA,IAAI,CAACC,UAAU,IAAI,CAAC,CAAC,IAAIH,GAAG,IAAIA,GAAG,CAACI,UAAU,IAAIF,IAAI,IAAIA,IAAI,CAACE,UAAU,IAAI,CAAC,CAAC;QAChJ5B,MAAM,CAACkB,KAAK,GAAGb,KAAK,CAACW,OAAO,IAAIQ,GAAG,IAAIA,GAAG,CAACK,SAAS,IAAIH,IAAI,IAAIA,IAAI,CAACG,SAAS,IAAI,CAAC,CAAC,IAAIL,GAAG,IAAIA,GAAG,CAACM,SAAS,IAAIJ,IAAI,IAAIA,IAAI,CAACI,SAAS,IAAI,CAAC,CAAC;MAChJ,CAAC,MAAM;QACH9B,MAAM,CAACiB,KAAK,GAAGZ,KAAK,CAACY,KAAK;QAC1BjB,MAAM,CAACkB,KAAK,GAAGb,KAAK,CAACa,KAAK;MAC9B;;MAEA;MACA;MACA;MACA;;MAEAlB,MAAM,CAACa,CAAC,GAAGR,KAAK,CAACS,OAAO;MACxBd,MAAM,CAACe,CAAC,GAAGV,KAAK,CAACW,OAAO;MAExBhB,MAAM,CAACmB,OAAO,GAAGd,KAAK,CAACc,OAAO;MAC9BnB,MAAM,CAACoB,OAAO,GAAGf,KAAK,CAACe,OAAO;IAClC;IAEApB,MAAM,CAACc,OAAO,GAAGd,MAAM,CAACa,CAAC;IACzBb,MAAM,CAACgB,OAAO,GAAGhB,MAAM,CAACe,CAAC;EAC7B,CAAC;;EAED;AACJ;;AAEA;AACA;AACA;AACA;;AAEAgB,MAAM,CAACC,OAAO,GAAGjC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}