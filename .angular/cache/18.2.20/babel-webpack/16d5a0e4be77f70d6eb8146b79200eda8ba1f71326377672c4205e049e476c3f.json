{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class UsersComponent {\n  constructor() {}\n  static {\n    this.ɵfac = function UsersComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || UsersComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: UsersComponent,\n      selectors: [[\"forms\"]],\n      decls: 1,\n      vars: 0,\n      template: function UsersComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"router-outlet\");\n        }\n      },\n      dependencies: [i1.RouterOutlet],\n      styles: [\".bg-pending {\\n  background-color: #dea82f;\\n  border-color: #dea82f;\\n  color: #fff;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvdXNlcnMvdXNlcnMuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSx5QkFBQTtFQUNBLHFCQUFBO0VBQ0EsV0FBQTtBQUNGIiwic291cmNlc0NvbnRlbnQiOlsiLmJnLXBlbmRpbmcge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZGVhODJmO1xuICBib3JkZXItY29sb3I6ICNkZWE4MmY7XG4gIGNvbG9yOiAjZmZmO1xufVxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["UsersComponent", "constructor", "selectors", "decls", "vars", "template", "UsersComponent_Template", "rf", "ctx", "i0", "ɵɵelement"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/users/users.component.ts"], "sourcesContent": ["import { Component, ViewEncapsulation } from '@angular/core';\n\n@Component({\n  selector: 'forms',\n  encapsulation: ViewEncapsulation.None,\n  styleUrls: [\n    'users.component.scss',\n  ],\n  template: `<router-outlet></router-outlet>`\n})\nexport class UsersComponent {\n\n  constructor() {\n  }\n}\n"], "mappings": ";;AAUA,OAAM,MAAOA,cAAc;EAEzBC,YAAA,GACA;;;uCAHWD,cAAc;IAAA;EAAA;;;YAAdA,cAAc;MAAAE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAFdE,EAAA,CAAAC,SAAA,oBAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}