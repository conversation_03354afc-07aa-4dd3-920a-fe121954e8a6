{"ast": null, "code": "'use strict';\n\nvar cache = {};\nvar start = '(?:^|\\\\s)';\nvar end = '(?:\\\\s|$)';\nfunction lookupClass(className) {\n  var cached = cache[className];\n  if (cached) {\n    cached.lastIndex = 0;\n  } else {\n    cache[className] = cached = new RegExp(start + className + end, 'g');\n  }\n  return cached;\n}\nfunction addClass(el, className) {\n  var current = el.className;\n  if (!current.length) {\n    el.className = className;\n  } else if (!lookupClass(className).test(current)) {\n    el.className += ' ' + className;\n  }\n}\nfunction rmClass(el, className) {\n  el.className = el.className.replace(lookupClass(className), ' ').trim();\n}\nmodule.exports = {\n  add: addClass,\n  rm: rmClass\n};", "map": {"version": 3, "names": ["cache", "start", "end", "lookupClass", "className", "cached", "lastIndex", "RegExp", "addClass", "el", "current", "length", "test", "rmClass", "replace", "trim", "module", "exports", "add", "rm"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/node_modules/dragula/classes.js"], "sourcesContent": ["'use strict';\n\nvar cache = {};\nvar start = '(?:^|\\\\s)';\nvar end = '(?:\\\\s|$)';\n\nfunction lookupClass (className) {\n  var cached = cache[className];\n  if (cached) {\n    cached.lastIndex = 0;\n  } else {\n    cache[className] = cached = new RegExp(start + className + end, 'g');\n  }\n  return cached;\n}\n\nfunction addClass (el, className) {\n  var current = el.className;\n  if (!current.length) {\n    el.className = className;\n  } else if (!lookupClass(className).test(current)) {\n    el.className += ' ' + className;\n  }\n}\n\nfunction rmClass (el, className) {\n  el.className = el.className.replace(lookupClass(className), ' ').trim();\n}\n\nmodule.exports = {\n  add: addClass,\n  rm: rmClass\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAG,CAAC,CAAC;AACd,IAAIC,KAAK,GAAG,WAAW;AACvB,IAAIC,GAAG,GAAG,WAAW;AAErB,SAASC,WAAWA,CAAEC,SAAS,EAAE;EAC/B,IAAIC,MAAM,GAAGL,KAAK,CAACI,SAAS,CAAC;EAC7B,IAAIC,MAAM,EAAE;IACVA,MAAM,CAACC,SAAS,GAAG,CAAC;EACtB,CAAC,MAAM;IACLN,KAAK,CAACI,SAAS,CAAC,GAAGC,MAAM,GAAG,IAAIE,MAAM,CAACN,KAAK,GAAGG,SAAS,GAAGF,GAAG,EAAE,GAAG,CAAC;EACtE;EACA,OAAOG,MAAM;AACf;AAEA,SAASG,QAAQA,CAAEC,EAAE,EAAEL,SAAS,EAAE;EAChC,IAAIM,OAAO,GAAGD,EAAE,CAACL,SAAS;EAC1B,IAAI,CAACM,OAAO,CAACC,MAAM,EAAE;IACnBF,EAAE,CAACL,SAAS,GAAGA,SAAS;EAC1B,CAAC,MAAM,IAAI,CAACD,WAAW,CAACC,SAAS,CAAC,CAACQ,IAAI,CAACF,OAAO,CAAC,EAAE;IAChDD,EAAE,CAACL,SAAS,IAAI,GAAG,GAAGA,SAAS;EACjC;AACF;AAEA,SAASS,OAAOA,CAAEJ,EAAE,EAAEL,SAAS,EAAE;EAC/BK,EAAE,CAACL,SAAS,GAAGK,EAAE,CAACL,SAAS,CAACU,OAAO,CAACX,WAAW,CAACC,SAAS,CAAC,EAAE,GAAG,CAAC,CAACW,IAAI,CAAC,CAAC;AACzE;AAEAC,MAAM,CAACC,OAAO,GAAG;EACfC,GAAG,EAAEV,QAAQ;EACbW,EAAE,EAAEN;AACN,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}