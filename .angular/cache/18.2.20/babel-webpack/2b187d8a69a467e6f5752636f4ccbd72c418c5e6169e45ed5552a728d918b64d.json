{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { ScrollingModule } from '@angular/cdk/scrolling';\nimport { FlexModule } from '@angular/flex-layout';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { TrimInputValueModule } from '../../../../../../../common/directives/trim-input-value/trim-input-value.module';\nimport { TaggedItemsComponent } from './tagged-items.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nexport class TaggedItemsModule {\n  static {\n    this.ɵfac = function TaggedItemsModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TaggedItemsModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: TaggedItemsModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, TranslateModule.forChild(), ReactiveFormsModule, FormsModule, MatCheckboxModule, MatFormFieldModule, MatIconModule, MatChipsModule, MatInputModule, ScrollingModule, MatButtonModule, FlexModule, TrimInputValueModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(TaggedItemsModule, {\n    declarations: [TaggedItemsComponent],\n    imports: [CommonModule, i1.TranslateModule, ReactiveFormsModule, FormsModule, MatCheckboxModule, MatFormFieldModule, MatIconModule, MatChipsModule, MatInputModule, ScrollingModule, MatButtonModule, FlexModule, TrimInputValueModule],\n    exports: [TaggedItemsComponent]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "ReactiveFormsModule", "MatButtonModule", "ScrollingModule", "FlexModule", "MatCheckboxModule", "MatChipsModule", "MatFormFieldModule", "MatIconModule", "MatInputModule", "TranslateModule", "TrimInputValueModule", "TaggedItemsComponent", "TaggedItemsModule", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "imports", "i1", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-rtp-reducer/rtp-reduce-modal/tagged-items/tagged-items.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\n\nimport { ScrollingModule } from '@angular/cdk/scrolling';\nimport { FlexModule } from '@angular/flex-layout';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { TrimInputValueModule } from '../../../../../../../common/directives/trim-input-value/trim-input-value.module';\nimport { TaggedItemsComponent } from './tagged-items.component';\n\n@NgModule({\n    imports: [\n        CommonModule,\n        TranslateModule.forChild(),\n        ReactiveFormsModule,\n        FormsModule,\n        MatCheckboxModule,\n        MatFormFieldModule,\n        MatIconModule,\n        MatChipsModule,\n        MatInputModule,\n        ScrollingModule,\n        MatButtonModule,\n        FlexModule,\n        TrimInputValueModule,\n    ],\n  declarations: [TaggedItemsComponent],\n  exports: [TaggedItemsComponent],\n})\nexport class TaggedItemsModule {\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAE1D,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAASC,UAAU,QAAQ,sBAAsB;AACjD,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,oBAAoB,QAAQ,iFAAiF;AACtH,SAASC,oBAAoB,QAAQ,0BAA0B;;;AAqB/D,OAAM,MAAOC,iBAAiB;;;uCAAjBA,iBAAiB;IAAA;EAAA;;;YAAjBA;IAAiB;EAAA;;;gBAjBtBd,YAAY,EACZW,eAAe,CAACI,QAAQ,EAAE,EAC1Bb,mBAAmB,EACnBD,WAAW,EACXK,iBAAiB,EACjBE,kBAAkB,EAClBC,aAAa,EACbF,cAAc,EACdG,cAAc,EACdN,eAAe,EACfD,eAAe,EACfE,UAAU,EACVO,oBAAoB;IAAA;EAAA;;;2EAKfE,iBAAiB;IAAAE,YAAA,GAHbH,oBAAoB;IAAAI,OAAA,GAd7BjB,YAAY,EAAAkB,EAAA,CAAAP,eAAA,EAEZT,mBAAmB,EACnBD,WAAW,EACXK,iBAAiB,EACjBE,kBAAkB,EAClBC,aAAa,EACbF,cAAc,EACdG,cAAc,EACdN,eAAe,EACfD,eAAe,EACfE,UAAU,EACVO,oBAAoB;IAAAO,OAAA,GAGhBN,oBAAoB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}