{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiPagePanelModule } from '@skywind-group/lib-swui';\nimport { BaCardModule } from '../../common/components/baCard/baCard.module';\nimport { GameService } from '../../common/services/game.service';\nimport { BriefResolver } from '../../common/services/resolvers/brief.resolver';\nimport { GamesShortInfoResolver } from '../../common/services/resolvers/gamesShortInfo.resolver';\nimport { MerchantBriefResolver } from '../../common/services/resolvers/merchant-brief.resolver';\nimport { ReportAuditLogComponent } from './components/audit-log/audit-log.component';\nimport { ReportCurrencyModule } from './components/currency/report-currency.module';\nimport { ReportFinancialModule } from './components/financial/report-financial.module';\nimport { ReportIndexComponent } from './components/index/index.component';\nimport { ReportKpiComponent } from './components/kpi';\nimport { ReportPlayerModule } from './components/player/report-player.module';\nimport { ReportRgModule } from './components/report-rg/report-rg.module';\nimport { ReportsComponent } from './reports.component';\nimport { ReportsRoutingModule } from './reports.routing';\nimport * as i0 from \"@angular/core\";\nexport class ReportsSectionModule {\n  static {\n    this.ɵfac = function ReportsSectionModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ReportsSectionModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ReportsSectionModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [BriefResolver, MerchantBriefResolver, GameService, GamesShortInfoResolver],\n      imports: [CommonModule, TranslateModule, BaCardModule, ReportsRoutingModule, SwuiPagePanelModule, ReportPlayerModule, ReportCurrencyModule, ReportFinancialModule, ReportRgModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ReportsSectionModule, {\n    declarations: [ReportsComponent, ReportIndexComponent, ReportAuditLogComponent, ReportKpiComponent],\n    imports: [CommonModule, TranslateModule, BaCardModule, ReportsRoutingModule, SwuiPagePanelModule, ReportPlayerModule, ReportCurrencyModule, ReportFinancialModule, ReportRgModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "TranslateModule", "SwuiPagePanelModule", "BaCardModule", "GameService", "BriefResolver", "GamesShortInfoResolver", "MerchantBriefResolver", "ReportAuditLogComponent", "ReportCurrencyModule", "ReportFinancialModule", "ReportIndexComponent", "ReportKpiComponent", "ReportPlayerModule", "ReportRgModule", "ReportsComponent", "ReportsRoutingModule", "ReportsSectionModule", "imports", "declarations"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/reports/reports-section.module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiPagePanelModule } from '@skywind-group/lib-swui';\nimport { BaCardModule } from '../../common/components/baCard/baCard.module';\nimport { GameService } from '../../common/services/game.service';\nimport { BriefResolver } from '../../common/services/resolvers/brief.resolver';\nimport { GamesShortInfoResolver } from '../../common/services/resolvers/gamesShortInfo.resolver';\nimport { MerchantBriefResolver } from '../../common/services/resolvers/merchant-brief.resolver';\nimport { ReportAuditLogComponent } from './components/audit-log/audit-log.component';\nimport { ReportCurrencyModule } from './components/currency/report-currency.module';\nimport { ReportFinancialModule } from './components/financial/report-financial.module';\nimport { ReportIndexComponent } from './components/index/index.component';\nimport { ReportKpiComponent } from './components/kpi';\nimport { ReportPlayerModule } from './components/player/report-player.module';\nimport { ReportRgModule } from './components/report-rg/report-rg.module';\nimport { ReportsComponent } from './reports.component';\nimport { ReportsRoutingModule } from './reports.routing';\n\n\n@NgModule({\n  imports: [\n    CommonModule,\n    TranslateModule,\n    BaCardModule,\n    ReportsRoutingModule,\n    SwuiPagePanelModule,\n    ReportPlayerModule,\n    ReportCurrencyModule,\n    ReportFinancialModule,\n    ReportRgModule\n  ],\n  declarations: [\n    ReportsComponent,\n    ReportIndexComponent,\n    ReportAuditLogComponent,\n    ReportKpiComponent,\n  ],\n  providers: [\n    BriefResolver,\n    MerchantBriefResolver,\n    GameService,\n    GamesShortInfoResolver\n  ]\n})\nexport class ReportsSectionModule {\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,mBAAmB,QAAQ,yBAAyB;AAC7D,SAASC,YAAY,QAAQ,8CAA8C;AAC3E,SAASC,WAAW,QAAQ,oCAAoC;AAChE,SAASC,aAAa,QAAQ,gDAAgD;AAC9E,SAASC,sBAAsB,QAAQ,yDAAyD;AAChG,SAASC,qBAAqB,QAAQ,yDAAyD;AAC/F,SAASC,uBAAuB,QAAQ,4CAA4C;AACpF,SAASC,oBAAoB,QAAQ,8CAA8C;AACnF,SAASC,qBAAqB,QAAQ,gDAAgD;AACtF,SAASC,oBAAoB,QAAQ,oCAAoC;AACzE,SAASC,kBAAkB,QAAQ,kBAAkB;AACrD,SAASC,kBAAkB,QAAQ,0CAA0C;AAC7E,SAASC,cAAc,QAAQ,yCAAyC;AACxE,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,oBAAoB,QAAQ,mBAAmB;;AA4BxD,OAAM,MAAOC,oBAAoB;;;uCAApBA,oBAAoB;IAAA;EAAA;;;YAApBA;IAAoB;EAAA;;;iBAPpB,CACTZ,aAAa,EACbE,qBAAqB,EACrBH,WAAW,EACXE,sBAAsB,CACvB;MAAAY,OAAA,GArBClB,YAAY,EACZC,eAAe,EACfE,YAAY,EACZa,oBAAoB,EACpBd,mBAAmB,EACnBW,kBAAkB,EAClBJ,oBAAoB,EACpBC,qBAAqB,EACrBI,cAAc;IAAA;EAAA;;;2EAeLG,oBAAoB;IAAAE,YAAA,GAZ7BJ,gBAAgB,EAChBJ,oBAAoB,EACpBH,uBAAuB,EACvBI,kBAAkB;IAAAM,OAAA,GAdlBlB,YAAY,EACZC,eAAe,EACfE,YAAY,EACZa,oBAAoB,EACpBd,mBAAmB,EACnBW,kBAAkB,EAClBJ,oBAAoB,EACpBC,qBAAqB,EACrBI,cAAc;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}