{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class PaymentsComponent {\n  constructor() {}\n  static {\n    this.ɵfac = function PaymentsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PaymentsComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PaymentsComponent,\n      selectors: [[\"payments-page\"]],\n      decls: 1,\n      vars: 0,\n      template: function PaymentsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"router-outlet\");\n        }\n      },\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["PaymentsComponent", "constructor", "selectors", "decls", "vars", "template", "PaymentsComponent_Template", "rf", "ctx", "i0", "ɵɵelement"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/payments/payments.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'payments-page',\n  styles: [],\n  template: `<router-outlet></router-outlet>`\n})\nexport class PaymentsComponent {\n\n  constructor() {\n  }\n\n}\n"], "mappings": ";AAOA,OAAM,MAAOA,iBAAiB;EAE5BC,YAAA,GACA;;;uCAHWD,iBAAiB;IAAA;EAAA;;;YAAjBA,iBAAiB;MAAAE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAFjBE,EAAA,CAAAC,SAAA,oBAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}