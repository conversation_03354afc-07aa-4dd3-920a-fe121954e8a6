{"ast": null, "code": "import { SchemaFilterMatchEnum } from '@skywind-group/lib-swui';\nimport { gameStatusClassMap, providerCodeClassMap, tagClassMap } from '../../../../../../app.constants';\nimport { GAME_STATUSES } from '../../../../../../common/services/game.service';\nimport { isLiveGame } from '../../../../../../common/typings';\nimport { componentGameTypes } from '../../../../../games-management/games-create/games-create.component';\nexport const GAME_STATUS_LIST = [{\n  id: 'normal',\n  code: GAME_STATUSES.NORMAL,\n  displayName: 'ALL.active',\n  hidden: false\n}, {\n  id: 'suspended',\n  code: GAME_STATUSES.SUSPENDED,\n  displayName: 'ALL.inactive_keep',\n  hidden: false\n}, {\n  id: 'suspended',\n  code: GAME_STATUSES.KILL_SESSION,\n  displayName: 'ALL.inactive_kill',\n  hidden: false\n}, {\n  id: 'test',\n  code: GAME_STATUSES.TEST,\n  displayName: 'ALL.test',\n  hidden: false\n}, {\n  id: 'hidden',\n  code: GAME_STATUSES.HIDDEN,\n  displayName: 'ALL.hidden',\n  hidden: false\n}];\nexport const DISPLAY_GAME_STATUS_LIST = [{\n  id: 'normal',\n  code: GAME_STATUSES.NORMAL,\n  displayName: 'ALL.active',\n  hidden: false\n}, {\n  id: 'suspended',\n  code: GAME_STATUSES.SUSPENDED,\n  displayName: 'ALL.inactive',\n  hidden: false\n}, {\n  id: 'test',\n  code: GAME_STATUSES.TEST,\n  displayName: 'ALL.test',\n  hidden: false\n}, {\n  id: 'hidden',\n  code: GAME_STATUSES.HIDDEN,\n  displayName: 'ALL.hidden',\n  hidden: false\n}];\nexport const SUPPORTED_FEATURES = [{\n  id: 'true',\n  code: 'true',\n  displayName: 'Yes'\n}, {\n  id: 'false',\n  code: 'false',\n  displayName: 'No'\n}];\nexport const LABEL_DISPLAY_TEXT = [{\n  'id': 'slot',\n  'text': 'Slot'\n}, {\n  'id': 'table game',\n  'text': 'Table game'\n}, {\n  'id': 'roulette',\n  'text': 'Roulette'\n}, {\n  'id': 'html5',\n  'text': 'Html5'\n}, {\n  'id': 'flash',\n  'text': 'Flash'\n}, {\n  'id': 'downloadable',\n  'text': 'Downloadable'\n}, {\n  'id': 'progressive',\n  'text': 'Progressive'\n}, {\n  'id': 'jackpot',\n  'text': 'Jackpot'\n}, {\n  'id': 'branded',\n  'text': 'Branded'\n}];\nexport const SCHEMA = [{\n  field: 'title',\n  title: 'ENTITY_SETUP.GAMES.title',\n  type: 'string',\n  td: {\n    type: 'string',\n    nowrap: true\n  },\n  isList: true,\n  isViewable: false,\n  isSortable: false,\n  isFilterable: true,\n  isFilterableAlways: true,\n  filterMatch: SchemaFilterMatchEnum.Contains,\n  alignment: {\n    th: 'left',\n    td: 'left'\n  }\n}, {\n  field: 'code',\n  title: 'ENTITY_SETUP.GAMES.code',\n  type: 'string',\n  isList: true,\n  isViewable: false,\n  isSortable: false,\n  isFilterable: true,\n  isFilterableAlways: true,\n  filterMatch: SchemaFilterMatchEnum.Contains,\n  alignment: {\n    th: 'left',\n    td: 'left'\n  }\n}, {\n  field: 'type',\n  title: 'ENTITY_SETUP.GAMES.type',\n  type: 'multiselect',\n  data: componentGameTypes,\n  isList: true,\n  isViewable: false,\n  isSortable: false,\n  isFilterable: true,\n  td: {\n    type: 'calc',\n    useTranslate: false,\n    titleFn: row => row.type,\n    classFn: () => 'sw-chip sw-chip-blue'\n  },\n  alignment: {\n    th: 'center',\n    td: 'center'\n  },\n  filterMatch: SchemaFilterMatchEnum.In\n}, {\n  field: 'providerTitle',\n  title: 'ENTITY_SETUP.GAMES.providerTitle',\n  type: 'string',\n  isList: true,\n  isViewable: false,\n  isSortable: false,\n  isFilterable: false,\n  td: {\n    type: 'calc',\n    useTranslate: false,\n    titleFn: row => row && row.providerTitle,\n    classFn: row => {\n      let cssClass = providerCodeClassMap.DEFAULT;\n      if (providerCodeClassMap.hasOwnProperty(row.providerCode)) {\n        cssClass = providerCodeClassMap[row.providerCode];\n      }\n      return cssClass;\n    }\n  },\n  alignment: {\n    th: 'center',\n    td: 'center'\n  }\n}, {\n  field: 'providerId',\n  title: 'ENTITY_SETUP.GAMES.providerTitle',\n  type: 'select',\n  data: [],\n  isList: false,\n  isViewable: false,\n  isSortable: false,\n  isFilterable: true\n}, {\n  field: 'aamsCode',\n  title: 'ENTITY_SETUP.GAMES.MANAGE_GRID.aamsCode',\n  type: 'string',\n  td: {\n    type: 'calc',\n    titleFn: row => row?.settings?.aamsCode || '-'\n  },\n  isList: true,\n  isViewable: true,\n  isSortable: false,\n  isFilterable: false,\n  alignment: {\n    th: 'center',\n    td: 'center'\n  }\n}, {\n  field: 'mustWinJackpotBundled',\n  title: 'ENTITY_SETUP.GAMES.MANAGE_GRID.mustWinJackpotBundled',\n  type: 'select',\n  td: {\n    type: 'calc',\n    useTranslate: false,\n    titleFn: row => {\n      return SUPPORTED_FEATURES.find(type => type.code === (row?.settings?.mustWinJackpotBundled || false).toString()).displayName;\n    },\n    classFn: row => {\n      const result = SUPPORTED_FEATURES.find(type => type.code === (row?.settings?.mustWinJackpotBundled || false).toString()).code;\n      return result === 'true' ? 'sw-chip sw-chip-blue' : 'sw-chip';\n    }\n  },\n  data: SUPPORTED_FEATURES,\n  isList: true,\n  isViewable: true,\n  isSortable: false,\n  isFilterable: false,\n  alignment: {\n    th: 'center',\n    td: 'center'\n  }\n}, {\n  field: 'labels',\n  title: 'ENTITY_SETUP.GAMES.labels',\n  type: 'multiselect',\n  data: LABEL_DISPLAY_TEXT,\n  isList: true,\n  isViewable: false,\n  isSortable: false,\n  isFilterable: false,\n  td: {\n    type: 'gameslabels',\n    classMap: tagClassMap\n  },\n  alignment: {\n    th: 'center',\n    td: 'center'\n  }\n}, {\n  field: 'labelsId',\n  title: 'ENTITY_SETUP.GAMES.labels',\n  type: 'multiselect',\n  data: [],\n  isList: false,\n  isViewable: false,\n  isSortable: true,\n  isFilterable: true,\n  filterMatch: SchemaFilterMatchEnum.In,\n  alignment: {\n    th: 'center',\n    td: 'center'\n  }\n}, {\n  field: 'limitFiltersWillBeApplied',\n  title: 'ENTITY_SETUP.GAMES.MANAGE_GRID.limitFiltersSupported',\n  type: 'select',\n  data: SUPPORTED_FEATURES,\n  emptyOptionPlaceholder: 'ALL.all',\n  isList: true,\n  isViewable: false,\n  isSortable: false,\n  isFilterable: true,\n  isFilterableAlways: true,\n  td: {\n    type: 'calc',\n    useTranslate: false,\n    titleFn: row => {\n      return SUPPORTED_FEATURES.find(type => type.code === row?.limitFiltersWillBeApplied.toString()).displayName;\n    },\n    classFn: row => {\n      const result = SUPPORTED_FEATURES.find(type => type.code === row?.limitFiltersWillBeApplied.toString()).code;\n      return result === 'true' ? 'sw-chip sw-chip-blue' : 'sw-chip';\n    }\n  },\n  alignment: {\n    th: 'center',\n    td: 'center'\n  }\n}, {\n  field: 'status',\n  title: 'ENTITY_SETUP.GAMES.status',\n  type: 'select',\n  isList: true,\n  isViewable: true,\n  isSortable: false,\n  isFilterable: true,\n  data: DISPLAY_GAME_STATUS_LIST,\n  td: {\n    type: 'status',\n    statusList: GAME_STATUS_LIST,\n    displayStatusList: DISPLAY_GAME_STATUS_LIST,\n    classMap: gameStatusClassMap,\n    readonlyFn: row => {\n      const {\n        changeStateDisabled,\n        changeStateEnabled,\n        changeStateLiveDisabled,\n        changeStateLiveEnabled,\n        changeState,\n        changeStateLive\n      } = row._meta;\n      let changeGameState;\n      if (isLiveGame(row)) {\n        if (row.status === GAME_STATUSES.NORMAL) {\n          changeGameState = changeStateLiveDisabled;\n        } else if (row.status === GAME_STATUSES.SUSPENDED) {\n          changeGameState = changeStateLiveEnabled;\n        } else {\n          changeGameState = changeStateLive;\n        }\n      } else {\n        if (row.status === GAME_STATUSES.NORMAL) {\n          changeGameState = changeStateDisabled;\n        } else if (row.status === GAME_STATUSES.SUSPENDED) {\n          changeGameState = changeStateEnabled;\n        } else {\n          changeGameState = changeState;\n        }\n      }\n      // will be improved\n      /*if (isSuperAdmin) {\n        return false;\n      }\n               if ([GAME_STATUSES.TEST, GAME_STATUSES.HIDDEN].includes(row.status)) {\n        return true;\n      }*/\n      return !changeGameState;\n    }\n  },\n  alignment: {\n    th: 'center',\n    td: 'center'\n  }\n}, {\n  field: 'isFreebetSupported',\n  title: 'ENTITY_SETUP.GAMES.MANAGE_GRID.isFreebetSupported',\n  type: 'select',\n  data: SUPPORTED_FEATURES,\n  emptyOptionPlaceholder: 'ALL.all',\n  isList: true,\n  isViewable: false,\n  isSortable: false,\n  isFilterable: true,\n  td: {\n    type: 'calc',\n    useTranslate: false,\n    titleFn: row => {\n      const {\n        isFreebetSupported\n      } = row?.features;\n      if (isFreebetSupported !== undefined) {\n        return SUPPORTED_FEATURES.find(features => {\n          return features.code === row?.features?.isFreebetSupported.toString();\n        }).displayName;\n      }\n      return '-';\n    },\n    classFn: row => {\n      let result;\n      const {\n        isFreebetSupported\n      } = row?.features;\n      if (isFreebetSupported !== undefined) {\n        result = SUPPORTED_FEATURES.find(features => {\n          return features.code === row?.features?.isFreebetSupported.toString();\n        }).code;\n      } else {\n        result = 'false';\n      }\n      return result === 'true' ? 'sw-chip sw-chip-blue' : 'sw-chip';\n    }\n  },\n  alignment: {\n    th: 'center',\n    td: 'center'\n  }\n}, {\n  field: 'isMarketplaceSupported',\n  title: 'ENTITY_SETUP.GAMES.MANAGE_GRID.isMarketplaceSupported',\n  type: 'select',\n  data: SUPPORTED_FEATURES,\n  emptyOptionPlaceholder: 'ALL.all',\n  isList: true,\n  isViewable: false,\n  isSortable: false,\n  isFilterable: true,\n  td: {\n    type: 'calc',\n    useTranslate: false,\n    titleFn: row => {\n      const {\n        isMarketplaceSupported\n      } = row?.features;\n      if (isMarketplaceSupported !== undefined) {\n        return SUPPORTED_FEATURES.find(features => {\n          return features.code === row?.features?.isMarketplaceSupported.toString();\n        }).displayName;\n      }\n      return '-';\n    },\n    classFn: row => {\n      let result;\n      const {\n        isMarketplaceSupported\n      } = row?.features;\n      if (isMarketplaceSupported !== undefined) {\n        result = SUPPORTED_FEATURES.find(features => {\n          return features.code === row?.features?.isMarketplaceSupported.toString();\n        }).code;\n      } else {\n        result = 'false';\n      }\n      return result === 'true' ? 'sw-chip sw-chip-blue' : 'sw-chip';\n    }\n  },\n  alignment: {\n    th: 'center',\n    td: 'center'\n  }\n}, {\n  field: 'isCustomLimitsSupported',\n  title: 'ENTITY_SETUP.GAMES.MANAGE_GRID.isCustomLimitsSupported',\n  type: 'select',\n  data: SUPPORTED_FEATURES,\n  emptyOptionPlaceholder: 'ALL.all',\n  isList: true,\n  isViewable: false,\n  isSortable: false,\n  isFilterable: true,\n  td: {\n    type: 'calc',\n    useTranslate: false,\n    titleFn: row => {\n      const {\n        isCustomLimitsSupported\n      } = row?.features;\n      if (isCustomLimitsSupported !== undefined) {\n        return SUPPORTED_FEATURES.find(features => {\n          return features.code === row?.features?.isCustomLimitsSupported.toString();\n        }).displayName;\n      }\n      return '-';\n    },\n    classFn: row => {\n      let result;\n      const {\n        isCustomLimitsSupported\n      } = row?.features;\n      if (isCustomLimitsSupported !== undefined) {\n        result = SUPPORTED_FEATURES.find(features => {\n          return features.code === row?.features?.isCustomLimitsSupported.toString();\n        }).code;\n      } else {\n        result = 'false';\n      }\n      return result === 'true' ? 'sw-chip sw-chip-blue' : 'sw-chip';\n    }\n  },\n  alignment: {\n    th: 'center',\n    td: 'center'\n  }\n}];\nexport const SCHEMA_LIST = SCHEMA.filter(el => el.isList);\nexport const SCHEMA_FILTER = SCHEMA.filter(el => el.isFilterable);", "map": {"version": 3, "names": ["SchemaFilterMatchEnum", "gameStatusClassMap", "providerCodeClassMap", "tagClassMap", "GAME_STATUSES", "isLiveGame", "componentGameTypes", "GAME_STATUS_LIST", "id", "code", "NORMAL", "displayName", "hidden", "SUSPENDED", "KILL_SESSION", "TEST", "HIDDEN", "DISPLAY_GAME_STATUS_LIST", "SUPPORTED_FEATURES", "LABEL_DISPLAY_TEXT", "SCHEMA", "field", "title", "type", "td", "nowrap", "isList", "isViewable", "isSortable", "isFilterable", "isFilterableAlways", "filterMatch", "Contains", "alignment", "th", "data", "useTranslate", "titleFn", "row", "classFn", "In", "providerTitle", "cssClass", "DEFAULT", "hasOwnProperty", "providerCode", "settings", "aamsCode", "find", "mustWinJackpotBundled", "toString", "result", "classMap", "emptyOptionPlaceholder", "limitFiltersWillBeApplied", "statusList", "displayStatusList", "readonlyFn", "changeStateDisabled", "changeStateEnabled", "changeStateLiveDisabled", "changeStateLiveEnabled", "changeState", "changeStateLive", "_meta", "changeGameState", "status", "isFreebetSupported", "features", "undefined", "isMarketplaceSupported", "isCustomLimitsSupported", "SCHEMA_LIST", "filter", "el", "SCHEMA_FILTER"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-games/general-games-info/games.schema.ts"], "sourcesContent": ["import { Schema<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SwuiGridField } from '@skywind-group/lib-swui';\nimport { gameStatusClassMap, providerCodeClassMap, tagClassMap } from '../../../../../../app.constants';\nimport { GAME_STATUSES } from '../../../../../../common/services/game.service';\nimport { Game, isLiveGame } from '../../../../../../common/typings';\nimport { componentGameTypes } from '../../../../../games-management/games-create/games-create.component';\n\nexport const GAME_STATUS_LIST = [\n  { id: 'normal', code: GAME_STATUSES.NORMAL, displayName: 'ALL.active', hidden: false },\n  { id: 'suspended', code: GAME_STATUSES.SUSPENDED, displayName: 'ALL.inactive_keep', hidden: false },\n  { id: 'suspended', code: GAME_STATUSES.KILL_SESSION, displayName: 'ALL.inactive_kill', hidden: false },\n  { id: 'test', code: GAME_STATUSES.TEST, displayName: 'ALL.test', hidden: false },\n  { id: 'hidden', code: GAME_STATUSES.HIDDEN, displayName: 'ALL.hidden', hidden: false }\n];\n\nexport const DISPLAY_GAME_STATUS_LIST = [\n  { id: 'normal', code: GAME_STATUSES.NORMAL, displayName: 'ALL.active', hidden: false },\n  { id: 'suspended', code: GAME_STATUSES.SUSPENDED, displayName: 'ALL.inactive', hidden: false },\n  { id: 'test', code: GAME_STATUSES.TEST, displayName: 'ALL.test', hidden: false },\n  { id: 'hidden', code: GAME_STATUSES.HIDDEN, displayName: 'ALL.hidden', hidden: false }\n];\n\nexport const SUPPORTED_FEATURES = [\n  { id: 'true', code: 'true', displayName: 'Yes' },\n  { id: 'false', code: 'false', displayName: 'No' },\n];\n\nexport const LABEL_DISPLAY_TEXT = [\n  {\n    'id': 'slot',\n    'text': 'Slot'\n  },\n  {\n    'id': 'table game',\n    'text': 'Table game'\n  },\n  {\n    'id': 'roulette',\n    'text': 'Roulette'\n  },\n  {\n    'id': 'html5',\n    'text': 'Html5'\n  },\n  {\n    'id': 'flash',\n    'text': 'Flash'\n  },\n  {\n    'id': 'downloadable',\n    'text': 'Downloadable'\n  },\n  {\n    'id': 'progressive',\n    'text': 'Progressive'\n  },\n  {\n    'id': 'jackpot',\n    'text': 'Jackpot'\n  },\n  {\n    'id': 'branded',\n    'text': 'Branded'\n  }\n];\n\nexport const SCHEMA: SwuiGridField[] = [\n  {\n    field: 'title',\n    title: 'ENTITY_SETUP.GAMES.title',\n    type: 'string',\n    td: {\n      type: 'string',\n      nowrap: true,\n    },\n    isList: true,\n    isViewable: false,\n    isSortable: false,\n    isFilterable: true,\n    isFilterableAlways: true,\n    filterMatch: SchemaFilterMatchEnum.Contains,\n    alignment: {\n      th: 'left',\n      td: 'left',\n    },\n  },\n  {\n    field: 'code',\n    title: 'ENTITY_SETUP.GAMES.code',\n    type: 'string',\n    isList: true,\n    isViewable: false,\n    isSortable: false,\n    isFilterable: true,\n    isFilterableAlways: true,\n    filterMatch: SchemaFilterMatchEnum.Contains,\n    alignment: {\n      th: 'left',\n      td: 'left',\n    },\n  },\n  {\n    field: 'type',\n    title: 'ENTITY_SETUP.GAMES.type',\n    type: 'multiselect',\n    data: componentGameTypes,\n    isList: true,\n    isViewable: false,\n    isSortable: false,\n    isFilterable: true,\n    td: {\n      type: 'calc',\n      useTranslate: false,\n      titleFn: ( row: Game ) => row.type,\n      classFn: () => 'sw-chip sw-chip-blue'\n    },\n    alignment: {\n      th: 'center',\n      td: 'center',\n    },\n    filterMatch: SchemaFilterMatchEnum.In,\n  },\n  {\n    field: 'providerTitle',\n    title: 'ENTITY_SETUP.GAMES.providerTitle',\n    type: 'string',\n    isList: true,\n    isViewable: false,\n    isSortable: false,\n    isFilterable: false,\n    td: {\n      type: 'calc',\n      useTranslate: false,\n      titleFn: ( row: any ) => row && row.providerTitle,\n      classFn: ( row: any ) => {\n        let cssClass = providerCodeClassMap.DEFAULT;\n\n        if (providerCodeClassMap.hasOwnProperty(row.providerCode)) {\n          cssClass = providerCodeClassMap[row.providerCode];\n        }\n\n        return cssClass;\n      }\n    },\n    alignment: {\n      th: 'center',\n      td: 'center',\n    },\n  },\n  {\n    field: 'providerId',\n    title: 'ENTITY_SETUP.GAMES.providerTitle',\n    type: 'select',\n    data: [],\n    isList: false,\n    isViewable: false,\n    isSortable: false,\n    isFilterable: true\n  },\n  {\n    field: 'aamsCode',\n    title: 'ENTITY_SETUP.GAMES.MANAGE_GRID.aamsCode',\n    type: 'string',\n    td: {\n      type: 'calc',\n      titleFn: (row: any) => row?.settings?.aamsCode || '-'\n    },\n    isList: true,\n    isViewable: true,\n    isSortable: false,\n    isFilterable: false,\n    alignment: {\n      th: 'center',\n      td: 'center',\n    },\n  },\n  {\n    field: 'mustWinJackpotBundled',\n    title: 'ENTITY_SETUP.GAMES.MANAGE_GRID.mustWinJackpotBundled',\n    type: 'select',\n    td: {\n      type: 'calc',\n      useTranslate: false,\n      titleFn: ( row: Game ) => {\n        return SUPPORTED_FEATURES.find(type => type.code === (row?.settings?.mustWinJackpotBundled || false).toString()).displayName;\n      },\n      classFn: ( row: Game ) => {\n        const result = SUPPORTED_FEATURES.find(type => type.code === (row?.settings?.mustWinJackpotBundled || false).toString()).code;\n        return result === 'true' ? 'sw-chip sw-chip-blue' : 'sw-chip';\n      }\n    },\n    data: SUPPORTED_FEATURES,\n    isList: true,\n    isViewable: true,\n    isSortable: false,\n    isFilterable: false,\n    alignment: {\n      th: 'center',\n      td: 'center',\n    },\n  },\n  {\n    field: 'labels',\n    title: 'ENTITY_SETUP.GAMES.labels',\n    type: 'multiselect',\n    data: LABEL_DISPLAY_TEXT,\n    isList: true,\n    isViewable: false,\n    isSortable: false,\n    isFilterable: false,\n    td: {\n      type: 'gameslabels',\n      classMap: tagClassMap,\n    },\n    alignment: {\n      th: 'center',\n      td: 'center',\n    },\n  },\n  {\n    field: 'labelsId',\n    title: 'ENTITY_SETUP.GAMES.labels',\n    type: 'multiselect',\n    data: [],\n    isList: false,\n    isViewable: false,\n    isSortable: true,\n    isFilterable: true,\n    filterMatch: SchemaFilterMatchEnum.In,\n    alignment: {\n      th: 'center',\n      td: 'center',\n    },\n  },\n  {\n    field: 'limitFiltersWillBeApplied',\n    title: 'ENTITY_SETUP.GAMES.MANAGE_GRID.limitFiltersSupported',\n    type: 'select',\n    data: SUPPORTED_FEATURES,\n    emptyOptionPlaceholder: 'ALL.all',\n    isList: true,\n    isViewable: false,\n    isSortable: false,\n    isFilterable: true,\n    isFilterableAlways: true,\n    td: {\n      type: 'calc',\n      useTranslate: false,\n      titleFn: ( row: Game ) => {\n        return SUPPORTED_FEATURES.find(type => type.code === row?.limitFiltersWillBeApplied.toString()).displayName;\n      },\n      classFn: ( row: Game ) => {\n        const result = SUPPORTED_FEATURES.find(type => type.code === row?.limitFiltersWillBeApplied.toString()).code;\n        return result === 'true' ? 'sw-chip sw-chip-blue' : 'sw-chip';\n      }\n    },\n    alignment: {\n      th: 'center',\n      td: 'center',\n    },\n  },\n  {\n    field: 'status',\n    title: 'ENTITY_SETUP.GAMES.status',\n    type: 'select',\n    isList: true,\n    isViewable: true,\n    isSortable: false,\n    isFilterable: true,\n    data: DISPLAY_GAME_STATUS_LIST,\n    td: {\n      type: 'status',\n      statusList: GAME_STATUS_LIST,\n      displayStatusList: DISPLAY_GAME_STATUS_LIST,\n      classMap: gameStatusClassMap,\n      readonlyFn: ( row: Game ) => {\n        const { changeStateDisabled, changeStateEnabled, changeStateLiveDisabled, changeStateLiveEnabled, changeState, changeStateLive }\n          = row._meta;\n\n        let changeGameState: boolean;\n        if (isLiveGame(row)) {\n          if (row.status === GAME_STATUSES.NORMAL) {\n            changeGameState = changeStateLiveDisabled;\n          } else if (row.status === GAME_STATUSES.SUSPENDED) {\n            changeGameState = changeStateLiveEnabled;\n          } else {\n            changeGameState = changeStateLive;\n          }\n        } else {\n          if (row.status === GAME_STATUSES.NORMAL) {\n            changeGameState = changeStateDisabled;\n          } else if (row.status === GAME_STATUSES.SUSPENDED) {\n            changeGameState = changeStateEnabled;\n          } else {\n            changeGameState = changeState;\n          }\n        }\n\n        // will be improved\n        /*if (isSuperAdmin) {\n          return false;\n        }\n\n        if ([GAME_STATUSES.TEST, GAME_STATUSES.HIDDEN].includes(row.status)) {\n          return true;\n        }*/\n\n        return !changeGameState;\n      }\n    },\n    alignment: {\n      th: 'center',\n      td: 'center',\n    },\n  },\n  {\n    field: 'isFreebetSupported',\n    title: 'ENTITY_SETUP.GAMES.MANAGE_GRID.isFreebetSupported',\n    type: 'select',\n    data: SUPPORTED_FEATURES,\n    emptyOptionPlaceholder: 'ALL.all',\n    isList: true,\n    isViewable: false,\n    isSortable: false,\n    isFilterable: true,\n    td: {\n      type: 'calc',\n      useTranslate: false,\n      titleFn: ( row: Game ) => {\n        const { isFreebetSupported } = row?.features;\n        if (isFreebetSupported !== undefined) {\n          return SUPPORTED_FEATURES.find(features => {\n            return features.code === row?.features?.isFreebetSupported.toString();\n          }).displayName;\n        }\n        return '-';\n      },\n      classFn: ( row: Game ) => {\n        let result;\n        const { isFreebetSupported } = row?.features;\n        if (isFreebetSupported !== undefined) {\n          result = SUPPORTED_FEATURES.find(features => {\n            return features.code === row?.features?.isFreebetSupported.toString();\n          }).code;\n        } else {\n          result = 'false';\n        }\n        return result === 'true' ? 'sw-chip sw-chip-blue' : 'sw-chip';\n      }\n    },\n    alignment: {\n      th: 'center',\n      td: 'center',\n    },\n  },\n  {\n    field: 'isMarketplaceSupported',\n    title: 'ENTITY_SETUP.GAMES.MANAGE_GRID.isMarketplaceSupported',\n    type: 'select',\n    data: SUPPORTED_FEATURES,\n    emptyOptionPlaceholder: 'ALL.all',\n    isList: true,\n    isViewable: false,\n    isSortable: false,\n    isFilterable: true,\n    td: {\n      type: 'calc',\n      useTranslate: false,\n      titleFn: ( row: Game ) => {\n        const { isMarketplaceSupported } = row?.features;\n        if (isMarketplaceSupported !== undefined) {\n          return SUPPORTED_FEATURES.find(features => {\n            return features.code === row?.features?.isMarketplaceSupported.toString();\n          }).displayName;\n        }\n        return '-';\n      },\n      classFn: ( row: Game ) => {\n        let result;\n        const { isMarketplaceSupported } = row?.features;\n        if (isMarketplaceSupported !== undefined) {\n          result = SUPPORTED_FEATURES.find(features => {\n            return features.code === row?.features?.isMarketplaceSupported.toString();\n          }).code;\n        } else {\n          result = 'false';\n        }\n        return result === 'true' ? 'sw-chip sw-chip-blue' : 'sw-chip';\n      }\n    },\n    alignment: {\n      th: 'center',\n      td: 'center',\n    },\n  },\n  {\n    field: 'isCustomLimitsSupported',\n    title: 'ENTITY_SETUP.GAMES.MANAGE_GRID.isCustomLimitsSupported',\n    type: 'select',\n    data: SUPPORTED_FEATURES,\n    emptyOptionPlaceholder: 'ALL.all',\n    isList: true,\n    isViewable: false,\n    isSortable: false,\n    isFilterable: true,\n    td: {\n      type: 'calc',\n      useTranslate: false,\n      titleFn: ( row: Game ) => {\n        const { isCustomLimitsSupported } = row?.features;\n        if (isCustomLimitsSupported !== undefined) {\n          return SUPPORTED_FEATURES.find(features => {\n            return features.code === row?.features?.isCustomLimitsSupported.toString();\n          }).displayName;\n        }\n        return '-';\n      },\n      classFn: ( row: Game ) => {\n        let result;\n        const { isCustomLimitsSupported } = row?.features;\n        if (isCustomLimitsSupported !== undefined) {\n          result = SUPPORTED_FEATURES.find(features => {\n            return features.code === row?.features?.isCustomLimitsSupported.toString();\n          }).code;\n        } else {\n          result = 'false';\n        }\n        return result === 'true' ? 'sw-chip sw-chip-blue' : 'sw-chip';\n      }\n    },\n    alignment: {\n      th: 'center',\n      td: 'center',\n    },\n  },\n];\n\n\nexport const SCHEMA_LIST = SCHEMA.filter(el => el.isList);\nexport const SCHEMA_FILTER = SCHEMA.filter(el => el.isFilterable);\n"], "mappings": "AAAA,SAASA,qBAAqB,QAAuB,yBAAyB;AAC9E,SAASC,kBAAkB,EAAEC,oBAAoB,EAAEC,WAAW,QAAQ,iCAAiC;AACvG,SAASC,aAAa,QAAQ,gDAAgD;AAC9E,SAAeC,UAAU,QAAQ,kCAAkC;AACnE,SAASC,kBAAkB,QAAQ,qEAAqE;AAExG,OAAO,MAAMC,gBAAgB,GAAG,CAC9B;EAAEC,EAAE,EAAE,QAAQ;EAAEC,IAAI,EAAEL,aAAa,CAACM,MAAM;EAAEC,WAAW,EAAE,YAAY;EAAEC,MAAM,EAAE;AAAK,CAAE,EACtF;EAAEJ,EAAE,EAAE,WAAW;EAAEC,IAAI,EAAEL,aAAa,CAACS,SAAS;EAAEF,WAAW,EAAE,mBAAmB;EAAEC,MAAM,EAAE;AAAK,CAAE,EACnG;EAAEJ,EAAE,EAAE,WAAW;EAAEC,IAAI,EAAEL,aAAa,CAACU,YAAY;EAAEH,WAAW,EAAE,mBAAmB;EAAEC,MAAM,EAAE;AAAK,CAAE,EACtG;EAAEJ,EAAE,EAAE,MAAM;EAAEC,IAAI,EAAEL,aAAa,CAACW,IAAI;EAAEJ,WAAW,EAAE,UAAU;EAAEC,MAAM,EAAE;AAAK,CAAE,EAChF;EAAEJ,EAAE,EAAE,QAAQ;EAAEC,IAAI,EAAEL,aAAa,CAACY,MAAM;EAAEL,WAAW,EAAE,YAAY;EAAEC,MAAM,EAAE;AAAK,CAAE,CACvF;AAED,OAAO,MAAMK,wBAAwB,GAAG,CACtC;EAAET,EAAE,EAAE,QAAQ;EAAEC,IAAI,EAAEL,aAAa,CAACM,MAAM;EAAEC,WAAW,EAAE,YAAY;EAAEC,MAAM,EAAE;AAAK,CAAE,EACtF;EAAEJ,EAAE,EAAE,WAAW;EAAEC,IAAI,EAAEL,aAAa,CAACS,SAAS;EAAEF,WAAW,EAAE,cAAc;EAAEC,MAAM,EAAE;AAAK,CAAE,EAC9F;EAAEJ,EAAE,EAAE,MAAM;EAAEC,IAAI,EAAEL,aAAa,CAACW,IAAI;EAAEJ,WAAW,EAAE,UAAU;EAAEC,MAAM,EAAE;AAAK,CAAE,EAChF;EAAEJ,EAAE,EAAE,QAAQ;EAAEC,IAAI,EAAEL,aAAa,CAACY,MAAM;EAAEL,WAAW,EAAE,YAAY;EAAEC,MAAM,EAAE;AAAK,CAAE,CACvF;AAED,OAAO,MAAMM,kBAAkB,GAAG,CAChC;EAAEV,EAAE,EAAE,MAAM;EAAEC,IAAI,EAAE,MAAM;EAAEE,WAAW,EAAE;AAAK,CAAE,EAChD;EAAEH,EAAE,EAAE,OAAO;EAAEC,IAAI,EAAE,OAAO;EAAEE,WAAW,EAAE;AAAI,CAAE,CAClD;AAED,OAAO,MAAMQ,kBAAkB,GAAG,CAChC;EACE,IAAI,EAAE,MAAM;EACZ,MAAM,EAAE;CACT,EACD;EACE,IAAI,EAAE,YAAY;EAClB,MAAM,EAAE;CACT,EACD;EACE,IAAI,EAAE,UAAU;EAChB,MAAM,EAAE;CACT,EACD;EACE,IAAI,EAAE,OAAO;EACb,MAAM,EAAE;CACT,EACD;EACE,IAAI,EAAE,OAAO;EACb,MAAM,EAAE;CACT,EACD;EACE,IAAI,EAAE,cAAc;EACpB,MAAM,EAAE;CACT,EACD;EACE,IAAI,EAAE,aAAa;EACnB,MAAM,EAAE;CACT,EACD;EACE,IAAI,EAAE,SAAS;EACf,MAAM,EAAE;CACT,EACD;EACE,IAAI,EAAE,SAAS;EACf,MAAM,EAAE;CACT,CACF;AAED,OAAO,MAAMC,MAAM,GAAoB,CACrC;EACEC,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE,0BAA0B;EACjCC,IAAI,EAAE,QAAQ;EACdC,EAAE,EAAE;IACFD,IAAI,EAAE,QAAQ;IACdE,MAAM,EAAE;GACT;EACDC,MAAM,EAAE,IAAI;EACZC,UAAU,EAAE,KAAK;EACjBC,UAAU,EAAE,KAAK;EACjBC,YAAY,EAAE,IAAI;EAClBC,kBAAkB,EAAE,IAAI;EACxBC,WAAW,EAAE/B,qBAAqB,CAACgC,QAAQ;EAC3CC,SAAS,EAAE;IACTC,EAAE,EAAE,MAAM;IACVV,EAAE,EAAE;;CAEP,EACD;EACEH,KAAK,EAAE,MAAM;EACbC,KAAK,EAAE,yBAAyB;EAChCC,IAAI,EAAE,QAAQ;EACdG,MAAM,EAAE,IAAI;EACZC,UAAU,EAAE,KAAK;EACjBC,UAAU,EAAE,KAAK;EACjBC,YAAY,EAAE,IAAI;EAClBC,kBAAkB,EAAE,IAAI;EACxBC,WAAW,EAAE/B,qBAAqB,CAACgC,QAAQ;EAC3CC,SAAS,EAAE;IACTC,EAAE,EAAE,MAAM;IACVV,EAAE,EAAE;;CAEP,EACD;EACEH,KAAK,EAAE,MAAM;EACbC,KAAK,EAAE,yBAAyB;EAChCC,IAAI,EAAE,aAAa;EACnBY,IAAI,EAAE7B,kBAAkB;EACxBoB,MAAM,EAAE,IAAI;EACZC,UAAU,EAAE,KAAK;EACjBC,UAAU,EAAE,KAAK;EACjBC,YAAY,EAAE,IAAI;EAClBL,EAAE,EAAE;IACFD,IAAI,EAAE,MAAM;IACZa,YAAY,EAAE,KAAK;IACnBC,OAAO,EAAIC,GAAS,IAAMA,GAAG,CAACf,IAAI;IAClCgB,OAAO,EAAEA,CAAA,KAAM;GAChB;EACDN,SAAS,EAAE;IACTC,EAAE,EAAE,QAAQ;IACZV,EAAE,EAAE;GACL;EACDO,WAAW,EAAE/B,qBAAqB,CAACwC;CACpC,EACD;EACEnB,KAAK,EAAE,eAAe;EACtBC,KAAK,EAAE,kCAAkC;EACzCC,IAAI,EAAE,QAAQ;EACdG,MAAM,EAAE,IAAI;EACZC,UAAU,EAAE,KAAK;EACjBC,UAAU,EAAE,KAAK;EACjBC,YAAY,EAAE,KAAK;EACnBL,EAAE,EAAE;IACFD,IAAI,EAAE,MAAM;IACZa,YAAY,EAAE,KAAK;IACnBC,OAAO,EAAIC,GAAQ,IAAMA,GAAG,IAAIA,GAAG,CAACG,aAAa;IACjDF,OAAO,EAAID,GAAQ,IAAK;MACtB,IAAII,QAAQ,GAAGxC,oBAAoB,CAACyC,OAAO;MAE3C,IAAIzC,oBAAoB,CAAC0C,cAAc,CAACN,GAAG,CAACO,YAAY,CAAC,EAAE;QACzDH,QAAQ,GAAGxC,oBAAoB,CAACoC,GAAG,CAACO,YAAY,CAAC;MACnD;MAEA,OAAOH,QAAQ;IACjB;GACD;EACDT,SAAS,EAAE;IACTC,EAAE,EAAE,QAAQ;IACZV,EAAE,EAAE;;CAEP,EACD;EACEH,KAAK,EAAE,YAAY;EACnBC,KAAK,EAAE,kCAAkC;EACzCC,IAAI,EAAE,QAAQ;EACdY,IAAI,EAAE,EAAE;EACRT,MAAM,EAAE,KAAK;EACbC,UAAU,EAAE,KAAK;EACjBC,UAAU,EAAE,KAAK;EACjBC,YAAY,EAAE;CACf,EACD;EACER,KAAK,EAAE,UAAU;EACjBC,KAAK,EAAE,yCAAyC;EAChDC,IAAI,EAAE,QAAQ;EACdC,EAAE,EAAE;IACFD,IAAI,EAAE,MAAM;IACZc,OAAO,EAAGC,GAAQ,IAAKA,GAAG,EAAEQ,QAAQ,EAAEC,QAAQ,IAAI;GACnD;EACDrB,MAAM,EAAE,IAAI;EACZC,UAAU,EAAE,IAAI;EAChBC,UAAU,EAAE,KAAK;EACjBC,YAAY,EAAE,KAAK;EACnBI,SAAS,EAAE;IACTC,EAAE,EAAE,QAAQ;IACZV,EAAE,EAAE;;CAEP,EACD;EACEH,KAAK,EAAE,uBAAuB;EAC9BC,KAAK,EAAE,sDAAsD;EAC7DC,IAAI,EAAE,QAAQ;EACdC,EAAE,EAAE;IACFD,IAAI,EAAE,MAAM;IACZa,YAAY,EAAE,KAAK;IACnBC,OAAO,EAAIC,GAAS,IAAK;MACvB,OAAOpB,kBAAkB,CAAC8B,IAAI,CAACzB,IAAI,IAAIA,IAAI,CAACd,IAAI,KAAK,CAAC6B,GAAG,EAAEQ,QAAQ,EAAEG,qBAAqB,IAAI,KAAK,EAAEC,QAAQ,EAAE,CAAC,CAACvC,WAAW;IAC9H,CAAC;IACD4B,OAAO,EAAID,GAAS,IAAK;MACvB,MAAMa,MAAM,GAAGjC,kBAAkB,CAAC8B,IAAI,CAACzB,IAAI,IAAIA,IAAI,CAACd,IAAI,KAAK,CAAC6B,GAAG,EAAEQ,QAAQ,EAAEG,qBAAqB,IAAI,KAAK,EAAEC,QAAQ,EAAE,CAAC,CAACzC,IAAI;MAC7H,OAAO0C,MAAM,KAAK,MAAM,GAAG,sBAAsB,GAAG,SAAS;IAC/D;GACD;EACDhB,IAAI,EAAEjB,kBAAkB;EACxBQ,MAAM,EAAE,IAAI;EACZC,UAAU,EAAE,IAAI;EAChBC,UAAU,EAAE,KAAK;EACjBC,YAAY,EAAE,KAAK;EACnBI,SAAS,EAAE;IACTC,EAAE,EAAE,QAAQ;IACZV,EAAE,EAAE;;CAEP,EACD;EACEH,KAAK,EAAE,QAAQ;EACfC,KAAK,EAAE,2BAA2B;EAClCC,IAAI,EAAE,aAAa;EACnBY,IAAI,EAAEhB,kBAAkB;EACxBO,MAAM,EAAE,IAAI;EACZC,UAAU,EAAE,KAAK;EACjBC,UAAU,EAAE,KAAK;EACjBC,YAAY,EAAE,KAAK;EACnBL,EAAE,EAAE;IACFD,IAAI,EAAE,aAAa;IACnB6B,QAAQ,EAAEjD;GACX;EACD8B,SAAS,EAAE;IACTC,EAAE,EAAE,QAAQ;IACZV,EAAE,EAAE;;CAEP,EACD;EACEH,KAAK,EAAE,UAAU;EACjBC,KAAK,EAAE,2BAA2B;EAClCC,IAAI,EAAE,aAAa;EACnBY,IAAI,EAAE,EAAE;EACRT,MAAM,EAAE,KAAK;EACbC,UAAU,EAAE,KAAK;EACjBC,UAAU,EAAE,IAAI;EAChBC,YAAY,EAAE,IAAI;EAClBE,WAAW,EAAE/B,qBAAqB,CAACwC,EAAE;EACrCP,SAAS,EAAE;IACTC,EAAE,EAAE,QAAQ;IACZV,EAAE,EAAE;;CAEP,EACD;EACEH,KAAK,EAAE,2BAA2B;EAClCC,KAAK,EAAE,sDAAsD;EAC7DC,IAAI,EAAE,QAAQ;EACdY,IAAI,EAAEjB,kBAAkB;EACxBmC,sBAAsB,EAAE,SAAS;EACjC3B,MAAM,EAAE,IAAI;EACZC,UAAU,EAAE,KAAK;EACjBC,UAAU,EAAE,KAAK;EACjBC,YAAY,EAAE,IAAI;EAClBC,kBAAkB,EAAE,IAAI;EACxBN,EAAE,EAAE;IACFD,IAAI,EAAE,MAAM;IACZa,YAAY,EAAE,KAAK;IACnBC,OAAO,EAAIC,GAAS,IAAK;MACvB,OAAOpB,kBAAkB,CAAC8B,IAAI,CAACzB,IAAI,IAAIA,IAAI,CAACd,IAAI,KAAK6B,GAAG,EAAEgB,yBAAyB,CAACJ,QAAQ,EAAE,CAAC,CAACvC,WAAW;IAC7G,CAAC;IACD4B,OAAO,EAAID,GAAS,IAAK;MACvB,MAAMa,MAAM,GAAGjC,kBAAkB,CAAC8B,IAAI,CAACzB,IAAI,IAAIA,IAAI,CAACd,IAAI,KAAK6B,GAAG,EAAEgB,yBAAyB,CAACJ,QAAQ,EAAE,CAAC,CAACzC,IAAI;MAC5G,OAAO0C,MAAM,KAAK,MAAM,GAAG,sBAAsB,GAAG,SAAS;IAC/D;GACD;EACDlB,SAAS,EAAE;IACTC,EAAE,EAAE,QAAQ;IACZV,EAAE,EAAE;;CAEP,EACD;EACEH,KAAK,EAAE,QAAQ;EACfC,KAAK,EAAE,2BAA2B;EAClCC,IAAI,EAAE,QAAQ;EACdG,MAAM,EAAE,IAAI;EACZC,UAAU,EAAE,IAAI;EAChBC,UAAU,EAAE,KAAK;EACjBC,YAAY,EAAE,IAAI;EAClBM,IAAI,EAAElB,wBAAwB;EAC9BO,EAAE,EAAE;IACFD,IAAI,EAAE,QAAQ;IACdgC,UAAU,EAAEhD,gBAAgB;IAC5BiD,iBAAiB,EAAEvC,wBAAwB;IAC3CmC,QAAQ,EAAEnD,kBAAkB;IAC5BwD,UAAU,EAAInB,GAAS,IAAK;MAC1B,MAAM;QAAEoB,mBAAmB;QAAEC,kBAAkB;QAAEC,uBAAuB;QAAEC,sBAAsB;QAAEC,WAAW;QAAEC;MAAe,CAAE,GAC5HzB,GAAG,CAAC0B,KAAK;MAEb,IAAIC,eAAwB;MAC5B,IAAI5D,UAAU,CAACiC,GAAG,CAAC,EAAE;QACnB,IAAIA,GAAG,CAAC4B,MAAM,KAAK9D,aAAa,CAACM,MAAM,EAAE;UACvCuD,eAAe,GAAGL,uBAAuB;QAC3C,CAAC,MAAM,IAAItB,GAAG,CAAC4B,MAAM,KAAK9D,aAAa,CAACS,SAAS,EAAE;UACjDoD,eAAe,GAAGJ,sBAAsB;QAC1C,CAAC,MAAM;UACLI,eAAe,GAAGF,eAAe;QACnC;MACF,CAAC,MAAM;QACL,IAAIzB,GAAG,CAAC4B,MAAM,KAAK9D,aAAa,CAACM,MAAM,EAAE;UACvCuD,eAAe,GAAGP,mBAAmB;QACvC,CAAC,MAAM,IAAIpB,GAAG,CAAC4B,MAAM,KAAK9D,aAAa,CAACS,SAAS,EAAE;UACjDoD,eAAe,GAAGN,kBAAkB;QACtC,CAAC,MAAM;UACLM,eAAe,GAAGH,WAAW;QAC/B;MACF;MAEA;MACA;;;;;;MAQA,OAAO,CAACG,eAAe;IACzB;GACD;EACDhC,SAAS,EAAE;IACTC,EAAE,EAAE,QAAQ;IACZV,EAAE,EAAE;;CAEP,EACD;EACEH,KAAK,EAAE,oBAAoB;EAC3BC,KAAK,EAAE,mDAAmD;EAC1DC,IAAI,EAAE,QAAQ;EACdY,IAAI,EAAEjB,kBAAkB;EACxBmC,sBAAsB,EAAE,SAAS;EACjC3B,MAAM,EAAE,IAAI;EACZC,UAAU,EAAE,KAAK;EACjBC,UAAU,EAAE,KAAK;EACjBC,YAAY,EAAE,IAAI;EAClBL,EAAE,EAAE;IACFD,IAAI,EAAE,MAAM;IACZa,YAAY,EAAE,KAAK;IACnBC,OAAO,EAAIC,GAAS,IAAK;MACvB,MAAM;QAAE6B;MAAkB,CAAE,GAAG7B,GAAG,EAAE8B,QAAQ;MAC5C,IAAID,kBAAkB,KAAKE,SAAS,EAAE;QACpC,OAAOnD,kBAAkB,CAAC8B,IAAI,CAACoB,QAAQ,IAAG;UACxC,OAAOA,QAAQ,CAAC3D,IAAI,KAAK6B,GAAG,EAAE8B,QAAQ,EAAED,kBAAkB,CAACjB,QAAQ,EAAE;QACvE,CAAC,CAAC,CAACvC,WAAW;MAChB;MACA,OAAO,GAAG;IACZ,CAAC;IACD4B,OAAO,EAAID,GAAS,IAAK;MACvB,IAAIa,MAAM;MACV,MAAM;QAAEgB;MAAkB,CAAE,GAAG7B,GAAG,EAAE8B,QAAQ;MAC5C,IAAID,kBAAkB,KAAKE,SAAS,EAAE;QACpClB,MAAM,GAAGjC,kBAAkB,CAAC8B,IAAI,CAACoB,QAAQ,IAAG;UAC1C,OAAOA,QAAQ,CAAC3D,IAAI,KAAK6B,GAAG,EAAE8B,QAAQ,EAAED,kBAAkB,CAACjB,QAAQ,EAAE;QACvE,CAAC,CAAC,CAACzC,IAAI;MACT,CAAC,MAAM;QACL0C,MAAM,GAAG,OAAO;MAClB;MACA,OAAOA,MAAM,KAAK,MAAM,GAAG,sBAAsB,GAAG,SAAS;IAC/D;GACD;EACDlB,SAAS,EAAE;IACTC,EAAE,EAAE,QAAQ;IACZV,EAAE,EAAE;;CAEP,EACD;EACEH,KAAK,EAAE,wBAAwB;EAC/BC,KAAK,EAAE,uDAAuD;EAC9DC,IAAI,EAAE,QAAQ;EACdY,IAAI,EAAEjB,kBAAkB;EACxBmC,sBAAsB,EAAE,SAAS;EACjC3B,MAAM,EAAE,IAAI;EACZC,UAAU,EAAE,KAAK;EACjBC,UAAU,EAAE,KAAK;EACjBC,YAAY,EAAE,IAAI;EAClBL,EAAE,EAAE;IACFD,IAAI,EAAE,MAAM;IACZa,YAAY,EAAE,KAAK;IACnBC,OAAO,EAAIC,GAAS,IAAK;MACvB,MAAM;QAAEgC;MAAsB,CAAE,GAAGhC,GAAG,EAAE8B,QAAQ;MAChD,IAAIE,sBAAsB,KAAKD,SAAS,EAAE;QACxC,OAAOnD,kBAAkB,CAAC8B,IAAI,CAACoB,QAAQ,IAAG;UACxC,OAAOA,QAAQ,CAAC3D,IAAI,KAAK6B,GAAG,EAAE8B,QAAQ,EAAEE,sBAAsB,CAACpB,QAAQ,EAAE;QAC3E,CAAC,CAAC,CAACvC,WAAW;MAChB;MACA,OAAO,GAAG;IACZ,CAAC;IACD4B,OAAO,EAAID,GAAS,IAAK;MACvB,IAAIa,MAAM;MACV,MAAM;QAAEmB;MAAsB,CAAE,GAAGhC,GAAG,EAAE8B,QAAQ;MAChD,IAAIE,sBAAsB,KAAKD,SAAS,EAAE;QACxClB,MAAM,GAAGjC,kBAAkB,CAAC8B,IAAI,CAACoB,QAAQ,IAAG;UAC1C,OAAOA,QAAQ,CAAC3D,IAAI,KAAK6B,GAAG,EAAE8B,QAAQ,EAAEE,sBAAsB,CAACpB,QAAQ,EAAE;QAC3E,CAAC,CAAC,CAACzC,IAAI;MACT,CAAC,MAAM;QACL0C,MAAM,GAAG,OAAO;MAClB;MACA,OAAOA,MAAM,KAAK,MAAM,GAAG,sBAAsB,GAAG,SAAS;IAC/D;GACD;EACDlB,SAAS,EAAE;IACTC,EAAE,EAAE,QAAQ;IACZV,EAAE,EAAE;;CAEP,EACD;EACEH,KAAK,EAAE,yBAAyB;EAChCC,KAAK,EAAE,wDAAwD;EAC/DC,IAAI,EAAE,QAAQ;EACdY,IAAI,EAAEjB,kBAAkB;EACxBmC,sBAAsB,EAAE,SAAS;EACjC3B,MAAM,EAAE,IAAI;EACZC,UAAU,EAAE,KAAK;EACjBC,UAAU,EAAE,KAAK;EACjBC,YAAY,EAAE,IAAI;EAClBL,EAAE,EAAE;IACFD,IAAI,EAAE,MAAM;IACZa,YAAY,EAAE,KAAK;IACnBC,OAAO,EAAIC,GAAS,IAAK;MACvB,MAAM;QAAEiC;MAAuB,CAAE,GAAGjC,GAAG,EAAE8B,QAAQ;MACjD,IAAIG,uBAAuB,KAAKF,SAAS,EAAE;QACzC,OAAOnD,kBAAkB,CAAC8B,IAAI,CAACoB,QAAQ,IAAG;UACxC,OAAOA,QAAQ,CAAC3D,IAAI,KAAK6B,GAAG,EAAE8B,QAAQ,EAAEG,uBAAuB,CAACrB,QAAQ,EAAE;QAC5E,CAAC,CAAC,CAACvC,WAAW;MAChB;MACA,OAAO,GAAG;IACZ,CAAC;IACD4B,OAAO,EAAID,GAAS,IAAK;MACvB,IAAIa,MAAM;MACV,MAAM;QAAEoB;MAAuB,CAAE,GAAGjC,GAAG,EAAE8B,QAAQ;MACjD,IAAIG,uBAAuB,KAAKF,SAAS,EAAE;QACzClB,MAAM,GAAGjC,kBAAkB,CAAC8B,IAAI,CAACoB,QAAQ,IAAG;UAC1C,OAAOA,QAAQ,CAAC3D,IAAI,KAAK6B,GAAG,EAAE8B,QAAQ,EAAEG,uBAAuB,CAACrB,QAAQ,EAAE;QAC5E,CAAC,CAAC,CAACzC,IAAI;MACT,CAAC,MAAM;QACL0C,MAAM,GAAG,OAAO;MAClB;MACA,OAAOA,MAAM,KAAK,MAAM,GAAG,sBAAsB,GAAG,SAAS;IAC/D;GACD;EACDlB,SAAS,EAAE;IACTC,EAAE,EAAE,QAAQ;IACZV,EAAE,EAAE;;CAEP,CACF;AAGD,OAAO,MAAMgD,WAAW,GAAGpD,MAAM,CAACqD,MAAM,CAACC,EAAE,IAAIA,EAAE,CAAChD,MAAM,CAAC;AACzD,OAAO,MAAMiD,aAAa,GAAGvD,MAAM,CAACqD,MAAM,CAACC,EAAE,IAAIA,EAAE,CAAC7C,YAAY,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}