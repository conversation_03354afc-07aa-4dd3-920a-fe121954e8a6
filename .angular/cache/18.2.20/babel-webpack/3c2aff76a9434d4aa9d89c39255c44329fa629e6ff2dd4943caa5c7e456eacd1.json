{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FlexModule } from '@angular/flex-layout';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatInputModule } from '@angular/material/input';\nimport { MissingTranslationHandler, TranslateModule } from '@ngx-translate/core';\nimport { ClipboardModule } from '../../../../../common/components/clipboard/clipboard.module';\nimport { PipesModule } from '../../../../../common/pipes/pipes.module';\nimport { GameService } from '../../../../../common/services/game.service';\nimport { GameHistoryService } from '../../../../../common/services/reports/gamehistory.service';\nimport { SpinBnsComponent } from '../spin-bns/spin-bns.component';\nimport { SpinDetailsComponent } from '../spin-details/spin-details.component';\nimport { SpinJackpotComponent } from '../spin-jackpot/spin-jackpot.component';\nimport { SpinListModule } from '../spin-list/spin-list.module';\nimport { SpinPhTournamentComponent } from '../spin-ph-tournament/spin-ph-tournament.component';\nimport { SpinPrizeDropComponent } from '../spin-prize-drop/spin-prize-drop.component';\nimport { SpinSharedJackpotPrizeComponent } from '../spin-shared-jackpot-prize/spin-shared-jackpot-prize.component';\nimport { SpinSrtCanvasComponent } from '../spin-srt-canvas/spin-srt-canvas.component';\nimport { SWMissingTranslationPoolHandler } from './missing-translation-pool.handler';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { RoundInfoComponent } from './round-info.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"../../../../../common/components/clipboard/clipboard.directive\";\nimport * as i4 from \"@angular/flex-layout/flex\";\nimport * as i5 from \"@angular/material/progress-spinner\";\nimport * as i6 from \"../../../../../common/pipes/formatted-money/formatted-money.pipe\";\nexport class RoundInfoModule {\n  static {\n    this.ɵfac = function RoundInfoModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RoundInfoModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: RoundInfoModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [GameHistoryService, GameService],\n      imports: [CommonModule, ReactiveFormsModule, TranslateModule.forChild({\n        missingTranslationHandler: {\n          provide: MissingTranslationHandler,\n          useClass: SWMissingTranslationPoolHandler\n        }\n      }), PipesModule, ClipboardModule, MatButtonModule, MatInputModule, MatDialogModule, FlexModule, SpinListModule, MatProgressSpinnerModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(RoundInfoModule, {\n    declarations: [RoundInfoComponent, SpinJackpotComponent, SpinBnsComponent, SpinSrtCanvasComponent, SpinDetailsComponent, SpinPhTournamentComponent, SpinPrizeDropComponent, SpinSharedJackpotPrizeComponent],\n    imports: [CommonModule, ReactiveFormsModule, i1.TranslateModule, PipesModule, ClipboardModule, MatButtonModule, MatInputModule, MatDialogModule, FlexModule, SpinListModule, MatProgressSpinnerModule],\n    exports: [RoundInfoComponent]\n  });\n})();\ni0.ɵɵsetComponentScope(SpinDetailsComponent, function () {\n  return [i2.NgIf, i1.TranslateDirective, i3.ClipboardDirective, i4.DefaultLayoutDirective, i4.DefaultLayoutAlignDirective, i4.DefaultFlexDirective, i5.MatProgressSpinner, SpinJackpotComponent, SpinBnsComponent, SpinSrtCanvasComponent, SpinPhTournamentComponent, SpinPrizeDropComponent, SpinSharedJackpotPrizeComponent];\n}, function () {\n  return [i1.TranslatePipe, i6.FormattedMoneyPipe];\n});", "map": {"version": 3, "names": ["CommonModule", "FlexModule", "ReactiveFormsModule", "MatButtonModule", "MatDialogModule", "MatInputModule", "MissingTranslationHandler", "TranslateModule", "ClipboardModule", "PipesModule", "GameService", "GameHistoryService", "SpinBnsComponent", "SpinDetailsComponent", "SpinJackpotComponent", "SpinListModule", "SpinPhTournamentComponent", "SpinPrizeDropComponent", "SpinSharedJackpotPrizeComponent", "SpinSrtCanvasComponent", "SWMissingTranslationPoolHandler", "MatProgressSpinnerModule", "RoundInfoComponent", "RoundInfoModule", "imports", "<PERSON><PERSON><PERSON><PERSON>", "missingTranslation<PERSON><PERSON><PERSON>", "provide", "useClass", "declarations", "i1", "exports", "i2", "NgIf", "TranslateDirective", "i3", "ClipboardDirective", "i4", "DefaultLayoutDirective", "DefaultLayoutAlignDirective", "DefaultFlexDirective", "i5", "MatProgressSpinner", "TranslatePipe", "i6", "FormattedMoneyPipe"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/gamehistory/components/internal-game-history/round/round-info-module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { FlexModule } from '@angular/flex-layout';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatInputModule } from '@angular/material/input';\nimport { MissingTranslationHandler, TranslateModule } from '@ngx-translate/core';\nimport { ClipboardModule } from '../../../../../common/components/clipboard/clipboard.module';\nimport { PipesModule } from '../../../../../common/pipes/pipes.module';\nimport { GameService } from '../../../../../common/services/game.service';\nimport { GameHistoryService } from '../../../../../common/services/reports/gamehistory.service';\nimport { SpinBnsComponent } from '../spin-bns/spin-bns.component';\nimport { SpinDetailsComponent } from '../spin-details/spin-details.component';\nimport { SpinJackpotComponent } from '../spin-jackpot/spin-jackpot.component';\nimport { SpinListModule } from '../spin-list/spin-list.module';\nimport { SpinPhTournamentComponent } from '../spin-ph-tournament/spin-ph-tournament.component';\nimport { SpinPrizeDropComponent } from '../spin-prize-drop/spin-prize-drop.component';\nimport { SpinSharedJackpotPrizeComponent } from '../spin-shared-jackpot-prize/spin-shared-jackpot-prize.component';\nimport { SpinSrtCanvasComponent } from '../spin-srt-canvas/spin-srt-canvas.component';\nimport { SWMissingTranslationPoolHandler } from './missing-translation-pool.handler';\n\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { RoundInfoComponent } from './round-info.component';\n\n\n@NgModule({\n  declarations: [\n    RoundInfoComponent,\n    SpinJackpotComponent,\n    SpinBnsComponent,\n    SpinSrtCanvasComponent,\n    SpinDetailsComponent,\n    SpinPhTournamentComponent,\n    SpinPrizeDropComponent,\n    SpinSharedJackpotPrizeComponent,\n  ],\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    TranslateModule.forChild({\n      missingTranslationHandler: { provide: MissingTranslationHandler, useClass: SWMissingTranslationPoolHandler },\n    }),\n    PipesModule,\n    ClipboardModule,\n    MatButtonModule,\n    MatInputModule,\n    MatDialogModule,\n    FlexModule,\n    SpinListModule,\n    MatProgressSpinnerModule\n  ],\n  exports: [\n    RoundInfoComponent,\n  ],\n\n  providers: [\n    GameHistoryService,\n    GameService,\n  ]\n})\nexport class RoundInfoModule {\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,UAAU,QAAQ,sBAAsB;AACjD,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,yBAAyB,EAAEC,eAAe,QAAQ,qBAAqB;AAChF,SAASC,eAAe,QAAQ,6DAA6D;AAC7F,SAASC,WAAW,QAAQ,0CAA0C;AACtE,SAASC,WAAW,QAAQ,6CAA6C;AACzE,SAASC,kBAAkB,QAAQ,4DAA4D;AAC/F,SAASC,gBAAgB,QAAQ,gCAAgC;AACjE,SAASC,oBAAoB,QAAQ,wCAAwC;AAC7E,SAASC,oBAAoB,QAAQ,wCAAwC;AAC7E,SAASC,cAAc,QAAQ,+BAA+B;AAC9D,SAASC,yBAAyB,QAAQ,oDAAoD;AAC9F,SAASC,sBAAsB,QAAQ,8CAA8C;AACrF,SAASC,+BAA+B,QAAQ,kEAAkE;AAClH,SAASC,sBAAsB,QAAQ,8CAA8C;AACrF,SAASC,+BAA+B,QAAQ,oCAAoC;AAEpF,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,kBAAkB,QAAQ,wBAAwB;;;;;;;;AAsC3D,OAAM,MAAOC,eAAe;;;uCAAfA,eAAe;IAAA;EAAA;;;YAAfA;IAAe;EAAA;;;iBALf,CACTZ,kBAAkB,EAClBD,WAAW,CACZ;MAAAc,OAAA,GArBCxB,YAAY,EACZE,mBAAmB,EACnBK,eAAe,CAACkB,QAAQ,CAAC;QACvBC,yBAAyB,EAAE;UAAEC,OAAO,EAAErB,yBAAyB;UAAEsB,QAAQ,EAAER;QAA+B;OAC3G,CAAC,EACFX,WAAW,EACXD,eAAe,EACfL,eAAe,EACfE,cAAc,EACdD,eAAe,EACfH,UAAU,EACVc,cAAc,EACdM,wBAAwB;IAAA;EAAA;;;2EAWfE,eAAe;IAAAM,YAAA,GAjCxBP,kBAAkB,EAClBR,oBAAoB,EACpBF,gBAAgB,EAChBO,sBAAsB,EACtBN,oBAAoB,EACpBG,yBAAyB,EACzBC,sBAAsB,EACtBC,+BAA+B;IAAAM,OAAA,GAG/BxB,YAAY,EACZE,mBAAmB,EAAA4B,EAAA,CAAAvB,eAAA,EAInBE,WAAW,EACXD,eAAe,EACfL,eAAe,EACfE,cAAc,EACdD,eAAe,EACfH,UAAU,EACVc,cAAc,EACdM,wBAAwB;IAAAU,OAAA,GAGxBT,kBAAkB;EAAA;AAAA;uBArBlBT,oBAAoB;EAAA,QAAAmB,EAAA,CAAAC,IAAA,EAAAH,EAAA,CAAAI,kBAAA,EAAAC,EAAA,CAAAC,kBAAA,EAAAC,EAAA,CAAAC,sBAAA,EAAAD,EAAA,CAAAE,2BAAA,EAAAF,EAAA,CAAAG,oBAAA,EAAAC,EAAA,CAAAC,kBAAA,EAHpB5B,oBAAoB,EACpBF,gBAAgB,EAChBO,sBAAsB,EAEtBH,yBAAyB,EACzBC,sBAAsB,EACtBC,+BAA+B;AAAA;EAAA,QAAAY,EAAA,CAAAa,aAAA,EAAAC,EAAA,CAAAC,kBAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}