{"ast": null, "code": "export {};", "map": {"version": 3, "names": [], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/users/users.module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiPagePanelModule } from '@skywind-group/lib-swui';\nimport { ClipboardModule } from '../../common/components/clipboard/clipboard.module';\nimport { AuditService } from '../../common/services/audits.service';\nimport { CurrentUserResolver } from '../../common/services/resolvers/current-user.resolver';\nimport { RoleService } from '../../common/services/role.service';\nimport { UserService } from '../../common/services/user.service';\nimport { ActivityLogModule } from './components/activityLog/activityLog.module';\nimport { UsersListModule } from './components/list/user-list.module';\nimport { UsersComponent } from './users.component';\nimport { UsersRoutingModule } from './users.routing';\n\n@NgModule({\n  imports: [\n    CommonModule,\n    TranslateModule,\n    UsersListModule,\n    UsersRoutingModule,\n    FormsModule,\n    ReactiveFormsModule,\n    ClipboardModule,\n    SwuiPagePanelModule,\n    ActivityLogModule\n  ],\n  declarations: [\n    UsersComponent,\n  ],\n  providers: [\n    AuditService,\n    RoleService,\n    CurrentUserResolver,\n    UserService\n  ],\n"], "mappings": "AAmCI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}