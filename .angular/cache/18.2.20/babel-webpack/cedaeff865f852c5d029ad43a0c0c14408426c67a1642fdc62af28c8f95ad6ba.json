{"ast": null, "code": "import { FormControl } from '@angular/forms';\nimport { MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"@angular/material/button\";\nimport * as i4 from \"@angular/material/form-field\";\nimport * as i5 from \"@skywind-group/lib-swui\";\nimport * as i6 from \"@ngx-translate/core\";\nexport class SelectPoolDialogComponent {\n  constructor(dialogRef, data) {\n    this.dialogRef = dialogRef;\n    this.data = data;\n    this.selectOptions = [];\n    this.poolControl = new FormControl();\n    this.destroyed$ = new Subject();\n    this.poolControl.setValue(data.domainPool?.id ?? '');\n    this.selectOptions = data.domainPools?.map(item => ({\n      id: item.id,\n      text: item.name\n    })) ?? [];\n  }\n  ngOnInit() {\n    this.poolControl.valueChanges.pipe(takeUntil(this.destroyed$)).subscribe(id => {\n      this.selected = this.data.domainPools?.find(pool => pool.id === id);\n    });\n  }\n  ngOnDestroy() {\n    this.destroyed$.next();\n    this.destroyed$.complete();\n  }\n  submitSelected() {\n    if (this.selected) {\n      this.dialogRef.close(this.selected);\n    }\n  }\n  static {\n    this.ɵfac = function SelectPoolDialogComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SelectPoolDialogComponent)(i0.ɵɵdirectiveInject(i1.MatDialogRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SelectPoolDialogComponent,\n      selectors: [[\"select--pool-dialog\"]],\n      decls: 16,\n      vars: 18,\n      consts: [[\"mat-dialog-title\", \"\"], [1, \"mat-typography\"], [\"appearance\", \"outline\", 2, \"width\", \"100%\"], [3, \"data\", \"formControl\", \"showSearch\"], [\"align\", \"end\"], [\"mat-button\", \"\", \"color\", \"primary\", \"mat-dialog-close\", \"\", 1, \"mat-button-md\"], [\"mat-flat-button\", \"\", \"color\", \"primary\", \"cdkFocusInitial\", \"\", 1, \"mat-button-md\", 3, \"click\", \"disabled\"]],\n      template: function SelectPoolDialogComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"h2\", 0);\n          i0.ɵɵtext(1);\n          i0.ɵɵpipe(2, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"mat-dialog-content\", 1)(4, \"mat-form-field\", 2)(5, \"mat-label\");\n          i0.ɵɵtext(6);\n          i0.ɵɵpipe(7, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(8, \"lib-swui-select\", 3);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"mat-dialog-actions\", 4)(10, \"button\", 5);\n          i0.ɵɵtext(11);\n          i0.ɵɵpipe(12, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function SelectPoolDialogComponent_Template_button_click_13_listener() {\n            return ctx.submitSelected();\n          });\n          i0.ɵɵtext(14);\n          i0.ɵɵpipe(15, \"translate\");\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 10, \"ENTITY_SETUP.DOMAINS.modalPoolTitle\"), \"\\n\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(7, 12, \"ENTITY_SETUP.DOMAINS.domain\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"data\", ctx.selectOptions)(\"formControl\", ctx.poolControl)(\"showSearch\", true);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(12, 14, \"DIALOG.cancel\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"disabled\", !ctx.selected);\n          i0.ɵɵproperty(\"disabled\", !ctx.selected);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(15, 16, \"DIALOG.save\"), \" \");\n        }\n      },\n      dependencies: [i2.NgControlStatus, i2.FormControlDirective, i1.MatDialogClose, i1.MatDialogTitle, i1.MatDialogActions, i1.MatDialogContent, i3.MatButton, i4.MatFormField, i4.MatLabel, i5.SwuiSelectComponent, i6.TranslatePipe],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["FormControl", "MAT_DIALOG_DATA", "Subject", "takeUntil", "SelectPoolDialogComponent", "constructor", "dialogRef", "data", "selectOptions", "poolControl", "destroyed$", "setValue", "domainPool", "id", "domainPools", "map", "item", "text", "name", "ngOnInit", "valueChanges", "pipe", "subscribe", "selected", "find", "pool", "ngOnDestroy", "next", "complete", "submitSelected", "close", "i0", "ɵɵdirectiveInject", "i1", "MatDialogRef", "selectors", "decls", "vars", "consts", "template", "SelectPoolDialogComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵlistener", "SelectPoolDialogComponent_Template_button_click_13_listener", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "ɵɵtextInterpolate", "ɵɵproperty", "ɵɵclassProp"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-domains/manage-domains/select-pool-dialog/select-pool-dialog.component.ts", "/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-domains/manage-domains/select-pool-dialog/select-pool-dialog.component.html"], "sourcesContent": ["import { Component, Inject, On<PERSON><PERSON>roy, OnInit } from '@angular/core';\nimport { FormControl } from '@angular/forms';\nimport { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';\nimport { SwuiSelectOption } from '@skywind-group/lib-swui';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { DomainPool } from '../../../../../../../common/models/domain.model';\n\nexport interface SelectPoolDialogData {\n  domainPool: DomainPool;\n  domainPools: DomainPool[];\n}\n\n@Component({\n  selector: 'select--pool-dialog',\n  templateUrl: 'select-pool-dialog.component.html'\n})\nexport class SelectPoolDialogComponent implements OnInit, OnDestroy {\n  readonly selectOptions: SwuiSelectOption[] = [];\n  selected: DomainPool;\n  poolControl = new FormControl();\n\n  private readonly destroyed$ = new Subject<void>();\n\n  constructor(\n    private readonly dialogRef: MatDialogRef<SelectPoolDialogComponent, DomainPool>,\n    @Inject(MAT_DIALOG_DATA) private readonly data: SelectPoolDialogData,\n  ) {\n    this.poolControl.setValue(data.domainPool?.id ?? '');\n    this.selectOptions = data.domainPools?.map((item) => ({\n      id: item.id,\n      text: item.name\n    })) ?? [];\n  }\n\n  ngOnInit() {\n    this.poolControl.valueChanges\n      .pipe(\n        takeUntil(this.destroyed$)\n      )\n      .subscribe((id: string) => {\n        this.selected = this.data.domainPools?.find(pool => pool.id === id);\n      });\n  }\n\n  ngOnDestroy(): void {\n    this.destroyed$.next();\n    this.destroyed$.complete();\n  }\n\n  submitSelected() {\n    if (this.selected) {\n      this.dialogRef.close(this.selected);\n    }\n  }\n}\n", "<h2 mat-dialog-title>\n  {{ 'ENTITY_SETUP.DOMAINS.modalPoolTitle' | translate }}\n</h2>\n<mat-dialog-content class=\"mat-typography\">\n  <mat-form-field appearance=\"outline\" style=\"width: 100%\">\n    <mat-label>{{'ENTITY_SETUP.DOMAINS.domain' | translate}}</mat-label>\n    <lib-swui-select [data]=\"selectOptions\" [formControl]=\"poolControl\" [showSearch]=\"true\"></lib-swui-select>\n  </mat-form-field>\n</mat-dialog-content>\n<mat-dialog-actions align=\"end\">\n  <button mat-button color=\"primary\" class=\"mat-button-md\" mat-dialog-close>\n    {{'DIALOG.cancel' | translate}}\n  </button>\n  <button\n    mat-flat-button\n    color=\"primary\"\n    class=\"mat-button-md\"\n    cdkFocusInitial\n    [class.disabled]=\"!selected\"\n    [disabled]=\"!selected\"\n    (click)=\"submitSelected()\">\n    {{'DIALOG.save' | translate}}\n  </button>\n</mat-dialog-actions>\n"], "mappings": "AACA,SAASA,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,eAAe,QAAsB,0BAA0B;AAExE,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,SAAS,QAAQ,gBAAgB;;;;;;;;AAY1C,OAAM,MAAOC,yBAAyB;EAOpCC,YACmBC,SAA8D,EACrCC,IAA0B;IADnD,KAAAD,SAAS,GAATA,SAAS;IACgB,KAAAC,IAAI,GAAJA,IAAI;IARvC,KAAAC,aAAa,GAAuB,EAAE;IAE/C,KAAAC,WAAW,GAAG,IAAIT,WAAW,EAAE;IAEd,KAAAU,UAAU,GAAG,IAAIR,OAAO,EAAQ;IAM/C,IAAI,CAACO,WAAW,CAACE,QAAQ,CAACJ,IAAI,CAACK,UAAU,EAAEC,EAAE,IAAI,EAAE,CAAC;IACpD,IAAI,CAACL,aAAa,GAAGD,IAAI,CAACO,WAAW,EAAEC,GAAG,CAAEC,IAAI,KAAM;MACpDH,EAAE,EAAEG,IAAI,CAACH,EAAE;MACXI,IAAI,EAAED,IAAI,CAACE;KACZ,CAAC,CAAC,IAAI,EAAE;EACX;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACV,WAAW,CAACW,YAAY,CAC1BC,IAAI,CACHlB,SAAS,CAAC,IAAI,CAACO,UAAU,CAAC,CAC3B,CACAY,SAAS,CAAET,EAAU,IAAI;MACxB,IAAI,CAACU,QAAQ,GAAG,IAAI,CAAChB,IAAI,CAACO,WAAW,EAAEU,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACZ,EAAE,KAAKA,EAAE,CAAC;IACrE,CAAC,CAAC;EACN;EAEAa,WAAWA,CAAA;IACT,IAAI,CAAChB,UAAU,CAACiB,IAAI,EAAE;IACtB,IAAI,CAACjB,UAAU,CAACkB,QAAQ,EAAE;EAC5B;EAEAC,cAAcA,CAAA;IACZ,IAAI,IAAI,CAACN,QAAQ,EAAE;MACjB,IAAI,CAACjB,SAAS,CAACwB,KAAK,CAAC,IAAI,CAACP,QAAQ,CAAC;IACrC;EACF;;;uCArCWnB,yBAAyB,EAAA2B,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAAH,EAAA,CAAAC,iBAAA,CAS1B/B,eAAe;IAAA;EAAA;;;YATdG,yBAAyB;MAAA+B,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCjBtCV,EAAA,CAAAY,cAAA,YAAqB;UACnBZ,EAAA,CAAAa,MAAA,GACF;;UAAAb,EAAA,CAAAc,YAAA,EAAK;UAGDd,EAFJ,CAAAY,cAAA,4BAA2C,wBACgB,gBAC5C;UAAAZ,EAAA,CAAAa,MAAA,GAA6C;;UAAAb,EAAA,CAAAc,YAAA,EAAY;UACpEd,EAAA,CAAAe,SAAA,yBAA0G;UAE9Gf,EADE,CAAAc,YAAA,EAAiB,EACE;UAEnBd,EADF,CAAAY,cAAA,4BAAgC,iBAC4C;UACxEZ,EAAA,CAAAa,MAAA,IACF;;UAAAb,EAAA,CAAAc,YAAA,EAAS;UACTd,EAAA,CAAAY,cAAA,iBAO6B;UAA3BZ,EAAA,CAAAgB,UAAA,mBAAAC,4DAAA;YAAA,OAASN,GAAA,CAAAb,cAAA,EAAgB;UAAA,EAAC;UAC1BE,EAAA,CAAAa,MAAA,IACF;;UACFb,EADE,CAAAc,YAAA,EAAS,EACU;;;UAtBnBd,EAAA,CAAAkB,SAAA,EACF;UADElB,EAAA,CAAAmB,kBAAA,MAAAnB,EAAA,CAAAoB,WAAA,qDACF;UAGepB,EAAA,CAAAkB,SAAA,GAA6C;UAA7ClB,EAAA,CAAAqB,iBAAA,CAAArB,EAAA,CAAAoB,WAAA,uCAA6C;UACvCpB,EAAA,CAAAkB,SAAA,GAAsB;UAA6BlB,EAAnD,CAAAsB,UAAA,SAAAX,GAAA,CAAAlC,aAAA,CAAsB,gBAAAkC,GAAA,CAAAjC,WAAA,CAA4B,oBAAoB;UAKvFsB,EAAA,CAAAkB,SAAA,GACF;UADElB,EAAA,CAAAmB,kBAAA,MAAAnB,EAAA,CAAAoB,WAAA,+BACF;UAMEpB,EAAA,CAAAkB,SAAA,GAA4B;UAA5BlB,EAAA,CAAAuB,WAAA,cAAAZ,GAAA,CAAAnB,QAAA,CAA4B;UAC5BQ,EAAA,CAAAsB,UAAA,cAAAX,GAAA,CAAAnB,QAAA,CAAsB;UAEtBQ,EAAA,CAAAkB,SAAA,EACF;UADElB,EAAA,CAAAmB,kBAAA,MAAAnB,EAAA,CAAAoB,WAAA,6BACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}