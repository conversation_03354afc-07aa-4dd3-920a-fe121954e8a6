{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nconst _c0 = [\"*\"];\nfunction WizardComponent_li_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 5)(1, \"a\", 6)(2, \"span\", 7);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const step_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", step_r1.class);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(step_r1.stepNumber);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", step_r1.heading, \" \");\n  }\n}\nexport const WIZARD_FORM_CONSTS = {\n  label_prev: 'COMPONENTS.WIZARD.btnPrev',\n  label_next: 'COMPONENTS.WIZARD.btnNext',\n  label_submit: 'COMPONENTS.WIZARD.btnSubmit'\n};\nexport class StepDetails {\n  constructor({\n    id,\n    heading,\n    index,\n    first,\n    last\n  }) {\n    Object.assign(this, {\n      id,\n      heading,\n      index,\n      first,\n      last\n    });\n  }\n}\nexport class StepChange {}\nexport const WIZARD_DIRECTIONS = {\n  next: 1,\n  previous: -1\n};\nexport class WizardComponent {\n  constructor() {\n    this.steps = [];\n    this.onNextStep = new EventEmitter();\n    this.onPreviousStep = new EventEmitter();\n    this.onStepChange = new EventEmitter();\n    this.onComplete = new EventEmitter();\n  }\n  addStep(step) {\n    this.steps.push(step);\n    if (this.steps.length === 1) {\n      this.setCurrentStep(step);\n    }\n    step.disabled = this.steps.length > 1;\n  }\n  hasPreviousStep() {\n    return this.currentStepIndex > 0;\n  }\n  hasNextStep() {\n    return this.currentStepIndex + 1 < this.steps.length;\n  }\n  setPreviousStep() {\n    if (this.currentStep.first) {\n      return;\n    }\n    this._setNewStep(WIZARD_DIRECTIONS.previous);\n  }\n  setNextStep() {\n    if (this.currentStep.last) {\n      this._completeWizard();\n      return;\n    }\n    this._setNewStep(WIZARD_DIRECTIONS.next);\n  }\n  setCustomStep(stepId) {\n    let requiredStep = this.steps.find(step => step.id === stepId);\n    if (requiredStep) {\n      this.setCurrentStep(requiredStep);\n    }\n  }\n  setFirstStep() {\n    let first = this.currentStepIndex * -1;\n    this._setNewStep(first);\n  }\n  _completeWizard() {\n    this.onComplete.emit(new StepDetails(this.currentStep));\n  }\n  _setNewStep(direction = WIZARD_DIRECTIONS.next) {\n    let newStepIndex = this.currentStepIndex + direction;\n    let noStep = typeof this.steps[newStepIndex] === 'undefined';\n    if (noStep || !direction) {\n      return;\n    }\n    this.steps.forEach(step => {\n      if (step.index !== newStepIndex) return;\n      let change = {\n        before: new StepDetails(this.currentStep),\n        after: new StepDetails(step)\n      };\n      this.setCurrentStep(step);\n      switch (direction) {\n        case WIZARD_DIRECTIONS.next:\n          this.onNextStep.emit(change);\n          break;\n        case WIZARD_DIRECTIONS.previous:\n          this.onPreviousStep.emit(change);\n          break;\n        default:\n          this.onStepChange.emit(change);\n          break;\n      }\n    });\n  }\n  setCurrentStep(step) {\n    step.current = true;\n    this.currentStep = step;\n    this.currentStepIndex = step.index;\n  }\n  static {\n    this.ɵfac = function WizardComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || WizardComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: WizardComponent,\n      selectors: [[\"sw-wizard\"]],\n      outputs: {\n        onNextStep: \"onNextStep\",\n        onPreviousStep: \"onPreviousStep\",\n        onStepChange: \"onStepChange\",\n        onComplete: \"onComplete\"\n      },\n      exportAs: [\"wizard\"],\n      ngContentSelectors: _c0,\n      decls: 6,\n      vars: 1,\n      consts: [[1, \"steps-basic\", \"wizard\", \"clearfix\"], [1, \"steps\", \"clearfix\"], [3, \"click\"], [3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"content\", \"clearfix\"], [3, \"ngClass\"], [\"href\", \"javascript:void(0);\"], [1, \"number\"]],\n      template: function WizardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"ul\", 2);\n          i0.ɵɵlistener(\"click\", function WizardComponent_Template_ul_click_2_listener($event) {\n            return $event.preventDefault();\n          });\n          i0.ɵɵtemplate(3, WizardComponent_li_3_Template, 5, 3, \"li\", 3);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"div\", 4);\n          i0.ɵɵprojection(5);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.steps);\n        }\n      },\n      dependencies: [i1.NgClass, i1.NgForOf],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "step_r1", "class", "ɵɵadvance", "ɵɵtextInterpolate", "<PERSON><PERSON><PERSON><PERSON>", "ɵɵtextInterpolate1", "heading", "WIZARD_FORM_CONSTS", "label_prev", "label_next", "label_submit", "StepDetails", "constructor", "id", "index", "first", "last", "Object", "assign", "StepChange", "WIZARD_DIRECTIONS", "next", "previous", "WizardComponent", "steps", "onNextStep", "onPreviousStep", "onStepChange", "onComplete", "addStep", "step", "push", "length", "setCurrentStep", "disabled", "hasPreviousStep", "currentStepIndex", "hasNextStep", "setPreviousStep", "currentStep", "_setNewStep", "setNextStep", "_completeWizard", "setCustomStep", "stepId", "requiredStep", "find", "setFirstStep", "emit", "direction", "newStepIndex", "noStep", "for<PERSON>ach", "change", "before", "after", "current", "selectors", "outputs", "exportAs", "ngContentSelectors", "_c0", "decls", "vars", "consts", "template", "WizardComponent_Template", "rf", "ctx", "ɵɵlistener", "WizardComponent_Template_ul_click_2_listener", "$event", "preventDefault", "ɵɵtemplate", "WizardComponent_li_3_Template", "ɵɵprojection"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/common/components/swWizard/wizard.component.ts", "/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/common/components/swWizard/wizard.component.html"], "sourcesContent": ["import { Component, EventEmitter, Output } from '@angular/core';\nimport { WizardStepDirective, WizardStepDirectiveInterface } from './wizard-step.directive';\n\nexport const WIZARD_FORM_CONSTS = {\n  label_prev: 'COMPONENTS.WIZARD.btnPrev',\n  label_next: 'COMPONENTS.WIZARD.btnNext',\n  label_submit: 'COMPONENTS.WIZARD.btnSubmit'\n};\n\nexport class StepDetails {\n  id: string;\n  heading?: string;\n  index: number;\n  first: boolean;\n  last: boolean;\n\n  constructor( { id, heading, index, first, last }: WizardStepDirectiveInterface ) {\n    Object.assign(this, { id, heading, index, first, last });\n  }\n}\n\nexport class StepChange {\n  after: StepDetails;\n  before: StepDetails;\n}\n\nexport const WIZARD_DIRECTIONS = {\n  next: 1,\n  previous: -1,\n};\n\n@Component({\n  selector: 'sw-wizard',\n  templateUrl: './wizard.component.html',\n  exportAs: 'wizard'\n})\nexport class WizardComponent {\n  public steps: WizardStepDirective[] = [];\n  public currentStep: WizardStepDirective;\n  public currentStepIndex: number;\n\n  @Output() public onNextStep: EventEmitter<StepChange> = new EventEmitter();\n  @Output() public onPreviousStep: EventEmitter<StepChange> = new EventEmitter();\n  @Output() public onStepChange: EventEmitter<StepChange> = new EventEmitter();\n  @Output() public onComplete: EventEmitter<StepDetails> = new EventEmitter();\n\n  constructor() {\n  }\n\n  public addStep( step: WizardStepDirective ) {\n    this.steps.push(step);\n    if (this.steps.length === 1) {\n      this.setCurrentStep(step);\n    }\n    step.disabled = this.steps.length > 1;\n  }\n\n  public hasPreviousStep(): boolean {\n    return this.currentStepIndex > 0;\n  }\n\n  public hasNextStep(): boolean {\n    return this.currentStepIndex + 1 < this.steps.length;\n  }\n\n  public setPreviousStep( ) {\n    if (this.currentStep.first) {\n      return;\n    }\n\n    this._setNewStep(WIZARD_DIRECTIONS.previous);\n  }\n\n  public setNextStep( ) {\n    if (this.currentStep.last) {\n      this._completeWizard();\n      return;\n    }\n\n    this._setNewStep(WIZARD_DIRECTIONS.next);\n  }\n\n  public setCustomStep( stepId: string ) {\n    let requiredStep = this.steps.find(step => step.id === stepId);\n    if (requiredStep) {\n      this.setCurrentStep(requiredStep);\n    }\n  }\n\n  public setFirstStep() {\n    let first = this.currentStepIndex * -1;\n    this._setNewStep(first);\n  }\n\n  private _completeWizard() {\n    this.onComplete.emit(new StepDetails(this.currentStep));\n  }\n\n  private _setNewStep( direction: number = WIZARD_DIRECTIONS.next ) {\n    let newStepIndex = this.currentStepIndex + direction;\n    let noStep: boolean = typeof this.steps[newStepIndex] === 'undefined';\n\n    if (noStep || !direction) {\n      return;\n    }\n\n    this.steps.forEach(( step ) => {\n      if (step.index !== newStepIndex) return;\n\n      let change = <StepChange>{\n        before: new StepDetails(this.currentStep),\n        after: new StepDetails(step)\n      };\n\n      this.setCurrentStep(step);\n\n      switch (direction) {\n        case WIZARD_DIRECTIONS.next:\n          this.onNextStep.emit(change);\n          break;\n\n        case WIZARD_DIRECTIONS.previous:\n          this.onPreviousStep.emit(change);\n          break;\n\n        default:\n          this.onStepChange.emit(change);\n          break;\n      }\n    });\n  }\n\n  private setCurrentStep( step: WizardStepDirective ) {\n    step.current = true;\n    this.currentStep = step;\n    this.currentStepIndex = step.index;\n  }\n}\n", "<div class=\"steps-basic wizard clearfix\">\n  <div class=\"steps clearfix\">\n    <ul (click)=\"$event.preventDefault()\">\n      <li *ngFor=\"let step of steps\" [ngClass]=\"step.class\">\n        <a href=\"javascript:void(0);\">\n          <span class=\"number\">{{ step.stepNumber }}</span>\n          {{ step.heading }}\n        </a>\n      </li>\n    </ul>\n  </div>\n  <div class=\"content clearfix\">\n    <ng-content></ng-content>\n  </div>\n</div>\n"], "mappings": "AAAA,SAAoBA,YAAY,QAAgB,eAAe;;;;;;ICKrDC,EAFJ,CAAAC,cAAA,YAAsD,WACtB,cACP;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjDH,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAI,EACD;;;;IAL0BH,EAAA,CAAAI,UAAA,YAAAC,OAAA,CAAAC,KAAA,CAAsB;IAE5BN,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAAQ,iBAAA,CAAAH,OAAA,CAAAI,UAAA,CAAqB;IAC1CT,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAU,kBAAA,MAAAL,OAAA,CAAAM,OAAA,MACF;;;ADJR,OAAO,MAAMC,kBAAkB,GAAG;EAChCC,UAAU,EAAE,2BAA2B;EACvCC,UAAU,EAAE,2BAA2B;EACvCC,YAAY,EAAE;CACf;AAED,OAAM,MAAOC,WAAW;EAOtBC,YAAa;IAAEC,EAAE;IAAEP,OAAO;IAAEQ,KAAK;IAAEC,KAAK;IAAEC;EAAI,CAAgC;IAC5EC,MAAM,CAACC,MAAM,CAAC,IAAI,EAAE;MAAEL,EAAE;MAAEP,OAAO;MAAEQ,KAAK;MAAEC,KAAK;MAAEC;IAAI,CAAE,CAAC;EAC1D;;AAGF,OAAM,MAAOG,UAAU;AAKvB,OAAO,MAAMC,iBAAiB,GAAG;EAC/BC,IAAI,EAAE,CAAC;EACPC,QAAQ,EAAE,CAAC;CACZ;AAOD,OAAM,MAAOC,eAAe;EAU1BX,YAAA;IATO,KAAAY,KAAK,GAA0B,EAAE;IAIvB,KAAAC,UAAU,GAA6B,IAAI/B,YAAY,EAAE;IACzD,KAAAgC,cAAc,GAA6B,IAAIhC,YAAY,EAAE;IAC7D,KAAAiC,YAAY,GAA6B,IAAIjC,YAAY,EAAE;IAC3D,KAAAkC,UAAU,GAA8B,IAAIlC,YAAY,EAAE;EAG3E;EAEOmC,OAAOA,CAAEC,IAAyB;IACvC,IAAI,CAACN,KAAK,CAACO,IAAI,CAACD,IAAI,CAAC;IACrB,IAAI,IAAI,CAACN,KAAK,CAACQ,MAAM,KAAK,CAAC,EAAE;MAC3B,IAAI,CAACC,cAAc,CAACH,IAAI,CAAC;IAC3B;IACAA,IAAI,CAACI,QAAQ,GAAG,IAAI,CAACV,KAAK,CAACQ,MAAM,GAAG,CAAC;EACvC;EAEOG,eAAeA,CAAA;IACpB,OAAO,IAAI,CAACC,gBAAgB,GAAG,CAAC;EAClC;EAEOC,WAAWA,CAAA;IAChB,OAAO,IAAI,CAACD,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACZ,KAAK,CAACQ,MAAM;EACtD;EAEOM,eAAeA,CAAA;IACpB,IAAI,IAAI,CAACC,WAAW,CAACxB,KAAK,EAAE;MAC1B;IACF;IAEA,IAAI,CAACyB,WAAW,CAACpB,iBAAiB,CAACE,QAAQ,CAAC;EAC9C;EAEOmB,WAAWA,CAAA;IAChB,IAAI,IAAI,CAACF,WAAW,CAACvB,IAAI,EAAE;MACzB,IAAI,CAAC0B,eAAe,EAAE;MACtB;IACF;IAEA,IAAI,CAACF,WAAW,CAACpB,iBAAiB,CAACC,IAAI,CAAC;EAC1C;EAEOsB,aAAaA,CAAEC,MAAc;IAClC,IAAIC,YAAY,GAAG,IAAI,CAACrB,KAAK,CAACsB,IAAI,CAAChB,IAAI,IAAIA,IAAI,CAACjB,EAAE,KAAK+B,MAAM,CAAC;IAC9D,IAAIC,YAAY,EAAE;MAChB,IAAI,CAACZ,cAAc,CAACY,YAAY,CAAC;IACnC;EACF;EAEOE,YAAYA,CAAA;IACjB,IAAIhC,KAAK,GAAG,IAAI,CAACqB,gBAAgB,GAAG,CAAC,CAAC;IACtC,IAAI,CAACI,WAAW,CAACzB,KAAK,CAAC;EACzB;EAEQ2B,eAAeA,CAAA;IACrB,IAAI,CAACd,UAAU,CAACoB,IAAI,CAAC,IAAIrC,WAAW,CAAC,IAAI,CAAC4B,WAAW,CAAC,CAAC;EACzD;EAEQC,WAAWA,CAAES,SAAA,GAAoB7B,iBAAiB,CAACC,IAAI;IAC7D,IAAI6B,YAAY,GAAG,IAAI,CAACd,gBAAgB,GAAGa,SAAS;IACpD,IAAIE,MAAM,GAAY,OAAO,IAAI,CAAC3B,KAAK,CAAC0B,YAAY,CAAC,KAAK,WAAW;IAErE,IAAIC,MAAM,IAAI,CAACF,SAAS,EAAE;MACxB;IACF;IAEA,IAAI,CAACzB,KAAK,CAAC4B,OAAO,CAAGtB,IAAI,IAAK;MAC5B,IAAIA,IAAI,CAAChB,KAAK,KAAKoC,YAAY,EAAE;MAEjC,IAAIG,MAAM,GAAe;QACvBC,MAAM,EAAE,IAAI3C,WAAW,CAAC,IAAI,CAAC4B,WAAW,CAAC;QACzCgB,KAAK,EAAE,IAAI5C,WAAW,CAACmB,IAAI;OAC5B;MAED,IAAI,CAACG,cAAc,CAACH,IAAI,CAAC;MAEzB,QAAQmB,SAAS;QACf,KAAK7B,iBAAiB,CAACC,IAAI;UACzB,IAAI,CAACI,UAAU,CAACuB,IAAI,CAACK,MAAM,CAAC;UAC5B;QAEF,KAAKjC,iBAAiB,CAACE,QAAQ;UAC7B,IAAI,CAACI,cAAc,CAACsB,IAAI,CAACK,MAAM,CAAC;UAChC;QAEF;UACE,IAAI,CAAC1B,YAAY,CAACqB,IAAI,CAACK,MAAM,CAAC;UAC9B;MACJ;IACF,CAAC,CAAC;EACJ;EAEQpB,cAAcA,CAAEH,IAAyB;IAC/CA,IAAI,CAAC0B,OAAO,GAAG,IAAI;IACnB,IAAI,CAACjB,WAAW,GAAGT,IAAI;IACvB,IAAI,CAACM,gBAAgB,GAAGN,IAAI,CAAChB,KAAK;EACpC;;;uCApGWS,eAAe;IAAA;EAAA;;;YAAfA,eAAe;MAAAkC,SAAA;MAAAC,OAAA;QAAAjC,UAAA;QAAAC,cAAA;QAAAC,YAAA;QAAAC,UAAA;MAAA;MAAA+B,QAAA;MAAAC,kBAAA,EAAAC,GAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UClCxBxE,EAFJ,CAAAC,cAAA,aAAyC,aACX,YACY;UAAlCD,EAAA,CAAA0E,UAAA,mBAAAC,6CAAAC,MAAA;YAAA,OAASA,MAAA,CAAAC,cAAA,EAAuB;UAAA,EAAC;UACnC7E,EAAA,CAAA8E,UAAA,IAAAC,6BAAA,gBAAsD;UAO1D/E,EADE,CAAAG,YAAA,EAAK,EACD;UACNH,EAAA,CAAAC,cAAA,aAA8B;UAC5BD,EAAA,CAAAgF,YAAA,GAAyB;UAE7BhF,EADE,CAAAG,YAAA,EAAM,EACF;;;UAXqBH,EAAA,CAAAO,SAAA,GAAQ;UAARP,EAAA,CAAAI,UAAA,YAAAqE,GAAA,CAAA5C,KAAA,CAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}