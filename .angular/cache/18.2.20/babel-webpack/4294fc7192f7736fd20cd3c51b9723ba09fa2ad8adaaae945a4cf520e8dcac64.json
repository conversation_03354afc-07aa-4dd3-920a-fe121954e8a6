{"ast": null, "code": "export class Site {\n  static {\n    this.STATUS_NORMAL = 'normal';\n  }\n  static {\n    this.STATUS_SUSPENDED = 'suspended';\n  }\n  static {\n    this.STATUS_INFO = 'info';\n  }\n  constructor(data = {}) {\n    this.id = 'id' in data ? data.id : '';\n    this.entityId = 'entityId' in data ? data.entityId : '';\n    this.title = 'title' in data ? data.title : '';\n    this.url = 'url' in data ? data.url : '';\n    this.status = 'status' in data ? data.status : '';\n    this.insertedAt = 'insertedAt' in data ? new Date(data['insertedAt']) : null;\n    this.operatorSiteGroupName = 'operatorSiteGroupName' in data ? data.operatorSiteGroupName : '';\n    this.externalCode = 'externalCode' in data ? data.externalCode : '';\n    this.isDefault = 'isDefault' in data ? data.isDefault : false;\n  }\n}\nexport const SITE_STATUS_LIST = [{\n  id: Site.STATUS_NORMAL,\n  code: Site.STATUS_NORMAL,\n  displayName: 'Active'\n}, {\n  id: Site.STATUS_SUSPENDED,\n  code: Site.STATUS_SUSPENDED,\n  displayName: 'Inactive'\n}, {\n  id: Site.STATUS_INFO,\n  code: Site.STATUS_INFO,\n  displayName: 'Info'\n}];\nexport const SITE_STATUS_LIST_TRANSLATE = [{\n  id: Site.STATUS_NORMAL,\n  code: Site.STATUS_NORMAL,\n  displayName: 'ENTITY_SETUP.WHITELISTING.siteStatusActive'\n}, {\n  id: Site.STATUS_SUSPENDED,\n  code: Site.STATUS_SUSPENDED,\n  displayName: 'ENTITY_SETUP.WHITELISTING.siteStatusInactive'\n}, {\n  id: Site.STATUS_INFO,\n  code: Site.STATUS_INFO,\n  displayName: 'ENTITY_SETUP.WHITELISTING.siteStatusInfo'\n}];", "map": {"version": 3, "names": ["Site", "STATUS_NORMAL", "STATUS_SUSPENDED", "STATUS_INFO", "constructor", "data", "id", "entityId", "title", "url", "status", "insertedAt", "Date", "operatorSiteGroupName", "externalCode", "isDefault", "SITE_STATUS_LIST", "code", "displayName", "SITE_STATUS_LIST_TRANSLATE"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/common/models/site.model.ts"], "sourcesContent": ["export class Site {\n  static STATUS_NORMAL: string = 'normal';\n  static STATUS_SUSPENDED: string = 'suspended';\n  static STATUS_INFO: string = 'info';\n\n  public id?: string;\n  public entityId?: string;\n  public title?: string;\n  public url: string;\n  public status?: string;\n  public insertedAt?: Date;\n  public operatorSiteGroupName?: string;\n  public externalCode?: string;\n  public isDefault?: boolean;\n\n  constructor( data: any = {} ) {\n    this.id = 'id' in data ? data.id : '';\n    this.entityId = 'entityId' in data ? data.entityId : '';\n    this.title = 'title' in data ? data.title : '';\n    this.url = 'url' in data ? data.url : '';\n    this.status = 'status' in data ? data.status : '';\n    this.insertedAt = 'insertedAt' in data ? new Date(data['insertedAt']) : null;\n    this.operatorSiteGroupName = 'operatorSiteGroupName' in data ? data.operatorSiteGroupName : '';\n    this.externalCode = 'externalCode' in data ? data.externalCode : '';\n    this.isDefault = 'isDefault' in data ? data.isDefault : false;\n  }\n}\n\nexport interface SiteLog {\n  url: string;\n  error?: {\n    code: number;\n    message?: string;\n  };\n}\n\nexport const SITE_STATUS_LIST = [\n  { id: Site.STATUS_NORMAL, code: Site.STATUS_NORMAL, displayName: 'Active' },\n  { id: Site.STATUS_SUSPENDED, code: Site.STATUS_SUSPENDED, displayName: 'Inactive' },\n  { id: Site.STATUS_INFO, code: Site.STATUS_INFO, displayName: 'Info' }\n];\n\nexport const SITE_STATUS_LIST_TRANSLATE = [\n  {\n    id: Site.STATUS_NORMAL,\n    code: Site.STATUS_NORMAL,\n    displayName: 'ENTITY_SETUP.WHITELISTING.siteStatusActive'\n  },\n  {\n    id: Site.STATUS_SUSPENDED,\n    code: Site.STATUS_SUSPENDED,\n    displayName: 'ENTITY_SETUP.WHITELISTING.siteStatusInactive'\n  },\n  {\n    id: Site.STATUS_INFO,\n    code: Site.STATUS_INFO,\n    displayName: 'ENTITY_SETUP.WHITELISTING.siteStatusInfo'\n  }\n];\n"], "mappings": "AAAA,OAAM,MAAOA,IAAI;;IACR,KAAAC,aAAa,GAAW,QAAQ;EAAC;;IACjC,KAAAC,gBAAgB,GAAW,WAAW;EAAC;;IACvC,KAAAC,WAAW,GAAW,MAAM;EAAC;EAYpCC,YAAaC,IAAA,GAAY,EAAE;IACzB,IAAI,CAACC,EAAE,GAAG,IAAI,IAAID,IAAI,GAAGA,IAAI,CAACC,EAAE,GAAG,EAAE;IACrC,IAAI,CAACC,QAAQ,GAAG,UAAU,IAAIF,IAAI,GAAGA,IAAI,CAACE,QAAQ,GAAG,EAAE;IACvD,IAAI,CAACC,KAAK,GAAG,OAAO,IAAIH,IAAI,GAAGA,IAAI,CAACG,KAAK,GAAG,EAAE;IAC9C,IAAI,CAACC,GAAG,GAAG,KAAK,IAAIJ,IAAI,GAAGA,IAAI,CAACI,GAAG,GAAG,EAAE;IACxC,IAAI,CAACC,MAAM,GAAG,QAAQ,IAAIL,IAAI,GAAGA,IAAI,CAACK,MAAM,GAAG,EAAE;IACjD,IAAI,CAACC,UAAU,GAAG,YAAY,IAAIN,IAAI,GAAG,IAAIO,IAAI,CAACP,IAAI,CAAC,YAAY,CAAC,CAAC,GAAG,IAAI;IAC5E,IAAI,CAACQ,qBAAqB,GAAG,uBAAuB,IAAIR,IAAI,GAAGA,IAAI,CAACQ,qBAAqB,GAAG,EAAE;IAC9F,IAAI,CAACC,YAAY,GAAG,cAAc,IAAIT,IAAI,GAAGA,IAAI,CAACS,YAAY,GAAG,EAAE;IACnE,IAAI,CAACC,SAAS,GAAG,WAAW,IAAIV,IAAI,GAAGA,IAAI,CAACU,SAAS,GAAG,KAAK;EAC/D;;AAWF,OAAO,MAAMC,gBAAgB,GAAG,CAC9B;EAAEV,EAAE,EAAEN,IAAI,CAACC,aAAa;EAAEgB,IAAI,EAAEjB,IAAI,CAACC,aAAa;EAAEiB,WAAW,EAAE;AAAQ,CAAE,EAC3E;EAAEZ,EAAE,EAAEN,IAAI,CAACE,gBAAgB;EAAEe,IAAI,EAAEjB,IAAI,CAACE,gBAAgB;EAAEgB,WAAW,EAAE;AAAU,CAAE,EACnF;EAAEZ,EAAE,EAAEN,IAAI,CAACG,WAAW;EAAEc,IAAI,EAAEjB,IAAI,CAACG,WAAW;EAAEe,WAAW,EAAE;AAAM,CAAE,CACtE;AAED,OAAO,MAAMC,0BAA0B,GAAG,CACxC;EACEb,EAAE,EAAEN,IAAI,CAACC,aAAa;EACtBgB,IAAI,EAAEjB,IAAI,CAACC,aAAa;EACxBiB,WAAW,EAAE;CACd,EACD;EACEZ,EAAE,EAAEN,IAAI,CAACE,gBAAgB;EACzBe,IAAI,EAAEjB,IAAI,CAACE,gBAAgB;EAC3BgB,WAAW,EAAE;CACd,EACD;EACEZ,EAAE,EAAEN,IAAI,CAACG,WAAW;EACpBc,IAAI,EAAEjB,IAAI,CAACG,WAAW;EACtBe,WAAW,EAAE;CACd,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}