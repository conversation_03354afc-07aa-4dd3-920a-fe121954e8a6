{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { BsDropdownModule } from 'ngx-bootstrap/dropdown';\nimport { TabsModule } from 'ngx-bootstrap/tabs';\nimport { CountryService } from '../../common/services/country.service';\nimport { CurrencyService } from '../../common/services/currency.service';\nimport { LanguagesService } from '../../common/services/languages.service';\nimport { BusinessManagementComponent } from './business-management.component';\nimport { BusinessManagementRoutingModule } from './business-management.routing';\nimport { MatBusinessStructureModule } from './components/mat-business-structure/mat-business-structure.module';\nimport { SetupEntityGuard } from './components/entities/setup-entity.guard';\nimport { EntityStateService } from './entity-state.service';\nlet BusinessManagementModule = class BusinessManagementModule {};\nBusinessManagementModule = __decorate([NgModule({\n  imports: [CommonModule, BsDropdownModule.forRoot(), TabsModule.forRoot(), MatBusinessStructureModule, BusinessManagementRoutingModule],\n  declarations: [BusinessManagementComponent],\n  providers: [CountryService, CurrencyService, LanguagesService, EntityStateService, SetupEntityGuard]\n})], BusinessManagementModule);\nexport { BusinessManagementModule };", "map": {"version": 3, "names": ["CommonModule", "NgModule", "BsDropdownModule", "TabsModule", "CountryService", "CurrencyService", "LanguagesService", "BusinessManagementComponent", "BusinessManagementRoutingModule", "MatBusinessStructureModule", "SetupEntityGuard", "EntityStateService", "BusinessManagementModule", "__decorate", "imports", "forRoot", "declarations", "providers"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/business-management.module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { BsDropdownModule } from 'ngx-bootstrap/dropdown';\nimport { TabsModule } from 'ngx-bootstrap/tabs';\n\nimport { CountryService } from '../../common/services/country.service';\nimport { CurrencyService } from '../../common/services/currency.service';\nimport { LanguagesService } from '../../common/services/languages.service';\nimport { BusinessManagementComponent } from './business-management.component';\nimport { BusinessManagementRoutingModule } from './business-management.routing';\nimport { MatBusinessStructureModule } from './components/mat-business-structure/mat-business-structure.module';\nimport { SetupEntityGuard } from './components/entities/setup-entity.guard';\nimport { EntityStateService } from './entity-state.service';\n\n\n@NgModule({\n  imports: [\n    CommonModule,\n    BsDropdownModule.forRoot(),\n    TabsModule.forRoot(),\n    MatBusinessStructureModule,\n    BusinessManagementRoutingModule,\n  ],\n  declarations: [\n    BusinessManagementComponent\n  ],\n  providers: [\n    CountryService,\n    CurrencyService,\n    LanguagesService,\n    EntityStateService,\n    SetupEntityGuard,\n  ]\n})\nexport class BusinessManagementModule {\n\n}\n"], "mappings": ";AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,gBAAgB,QAAQ,wBAAwB;AACzD,SAASC,UAAU,QAAQ,oBAAoB;AAE/C,SAASC,cAAc,QAAQ,uCAAuC;AACtE,SAASC,eAAe,QAAQ,wCAAwC;AACxE,SAASC,gBAAgB,QAAQ,yCAAyC;AAC1E,SAASC,2BAA2B,QAAQ,iCAAiC;AAC7E,SAASC,+BAA+B,QAAQ,+BAA+B;AAC/E,SAASC,0BAA0B,QAAQ,mEAAmE;AAC9G,SAASC,gBAAgB,QAAQ,0CAA0C;AAC3E,SAASC,kBAAkB,QAAQ,wBAAwB;AAsBpD,IAAMC,wBAAwB,GAA9B,MAAMA,wBAAwB,GAEpC;AAFYA,wBAAwB,GAAAC,UAAA,EAnBpCZ,QAAQ,CAAC;EACRa,OAAO,EAAE,CACPd,YAAY,EACZE,gBAAgB,CAACa,OAAO,EAAE,EAC1BZ,UAAU,CAACY,OAAO,EAAE,EACpBN,0BAA0B,EAC1BD,+BAA+B,CAChC;EACDQ,YAAY,EAAE,CACZT,2BAA2B,CAC5B;EACDU,SAAS,EAAE,CACTb,cAAc,EACdC,eAAe,EACfC,gBAAgB,EAChBK,kBAAkB,EAClBD,gBAAgB;CAEnB,CAAC,C,EACWE,wBAAwB,CAEpC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}