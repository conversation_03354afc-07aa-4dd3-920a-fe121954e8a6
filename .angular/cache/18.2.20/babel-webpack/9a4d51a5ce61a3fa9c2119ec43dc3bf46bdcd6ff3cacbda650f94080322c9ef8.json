{"ast": null, "code": "import { FlexLayoutModule } from '@angular/flex-layout';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiGridModule, SwuiPagePanelModule } from '@skywind-group/lib-swui';\nimport { BoConfirmationModule } from '../../common/components/bo-confirmation/bo-confirmation.module';\nimport { ControlMessagesModule } from '../../common/components/control-messages/control-messages.module';\nimport { GameServerComponent } from './game-server.component';\nimport { GameServerRoutingModule } from './game-server.routing';\nimport { GameServerService } from './game-server.service';\nimport { GameServerModalComponent } from './modals/game-server-modal.component';\nimport * as i0 from \"@angular/core\";\nexport class GameServerModule {\n  static {\n    this.ɵfac = function GameServerModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || GameServerModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: GameServerModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [GameServerService],\n      imports: [ReactiveFormsModule, TranslateModule, MatDialogModule, MatButtonModule, ControlMessagesModule, SwuiPagePanelModule, SwuiGridModule, GameServerRoutingModule, BoConfirmationModule, FlexLayoutModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(GameServerModule, {\n    declarations: [GameServerComponent, GameServerModalComponent],\n    imports: [ReactiveFormsModule, TranslateModule, MatDialogModule, MatButtonModule, ControlMessagesModule, SwuiPagePanelModule, SwuiGridModule, GameServerRoutingModule, BoConfirmationModule, FlexLayoutModule]\n  });\n})();", "map": {"version": 3, "names": ["FlexLayoutModule", "ReactiveFormsModule", "MatButtonModule", "MatDialogModule", "TranslateModule", "SwuiGridModule", "SwuiPagePanelModule", "BoConfirmationModule", "ControlMessagesModule", "GameServerComponent", "GameServerRoutingModule", "GameServerService", "GameServerModalComponent", "GameServerModule", "imports", "declarations"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/game-server/game-server.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { FlexLayoutModule } from '@angular/flex-layout';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiGridModule, SwuiPagePanelModule } from '@skywind-group/lib-swui';\nimport { BoConfirmationModule } from '../../common/components/bo-confirmation/bo-confirmation.module';\n\nimport { ControlMessagesModule } from '../../common/components/control-messages/control-messages.module';\nimport { GameServerComponent } from './game-server.component';\nimport { GameServerRoutingModule } from './game-server.routing';\nimport { GameServerService } from './game-server.service';\nimport { GameServerModalComponent } from './modals/game-server-modal.component';\n\n\n@NgModule({\n  imports: [\n    ReactiveFormsModule,\n    TranslateModule,\n    MatDialogModule,\n    MatButtonModule,\n    ControlMessagesModule,\n    SwuiPagePanelModule,\n    SwuiGridModule,\n    GameServerRoutingModule,\n    BoConfirmationModule,\n    FlexLayoutModule\n  ],\n  declarations: [\n    GameServerComponent,\n    GameServerModalComponent,\n  ],\n  providers: [\n    GameServerService,\n  ],\n})\nexport class GameServerModule {\n}\n"], "mappings": "AACA,SAASA,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,cAAc,EAAEC,mBAAmB,QAAQ,yBAAyB;AAC7E,SAASC,oBAAoB,QAAQ,gEAAgE;AAErG,SAASC,qBAAqB,QAAQ,kEAAkE;AACxG,SAASC,mBAAmB,QAAQ,yBAAyB;AAC7D,SAASC,uBAAuB,QAAQ,uBAAuB;AAC/D,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD,SAASC,wBAAwB,QAAQ,sCAAsC;;AAwB/E,OAAM,MAAOC,gBAAgB;;;uCAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;iBAJhB,CACTF,iBAAiB,CAClB;MAAAG,OAAA,GAjBCb,mBAAmB,EACnBG,eAAe,EACfD,eAAe,EACfD,eAAe,EACfM,qBAAqB,EACrBF,mBAAmB,EACnBD,cAAc,EACdK,uBAAuB,EACvBH,oBAAoB,EACpBP,gBAAgB;IAAA;EAAA;;;2EAUPa,gBAAgB;IAAAE,YAAA,GAPzBN,mBAAmB,EACnBG,wBAAwB;IAAAE,OAAA,GAbxBb,mBAAmB,EACnBG,eAAe,EACfD,eAAe,EACfD,eAAe,EACfM,qBAAqB,EACrBF,mBAAmB,EACnBD,cAAc,EACdK,uBAAuB,EACvBH,oBAAoB,EACpBP,gBAAgB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}