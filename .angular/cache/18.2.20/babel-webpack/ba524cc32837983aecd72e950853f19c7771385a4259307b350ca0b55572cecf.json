{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FlexModule } from '@angular/flex-layout';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiGridModule, SwuiNotificationsModule, SwuiPagePanelModule } from '@skywind-group/lib-swui';\nimport { ControlMessagesModule } from '../../../../common/components/control-messages/control-messages.module';\nimport { RoleService } from '../../../../common/services/role.service';\nimport { UserService } from '../../../../common/services/user.service';\nimport { ConfirmDeleteDialogComponent } from './dialogs/confirm-delete-dialog.component';\nimport { RolesDialogComponent } from './dialogs/roles-dialog.component';\nimport { RolesComponent } from './roles.component';\nimport { RolesRoutingModule } from './roles.routing';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"@skywind-group/lib-swui\";\nexport class RolesModule {\n  static {\n    this.ɵfac = function RolesModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RolesModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: RolesModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [RoleService, UserService],\n      imports: [CommonModule, TranslateModule.forChild(), RolesRoutingModule, SwuiPagePanelModule, FormsModule, ReactiveFormsModule, MatDialogModule, MatButtonModule, MatIconModule, MatFormFieldModule, SwuiGridModule, SwuiNotificationsModule.forRoot(), MatCheckboxModule, ControlMessagesModule, FlexModule, MatInputModule, MatCheckboxModule, MatDividerModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(RolesModule, {\n    declarations: [RolesComponent, RolesDialogComponent, ConfirmDeleteDialogComponent],\n    imports: [CommonModule, i1.TranslateModule, RolesRoutingModule, SwuiPagePanelModule, FormsModule, ReactiveFormsModule, MatDialogModule, MatButtonModule, MatIconModule, MatFormFieldModule, SwuiGridModule, i2.SwuiNotificationsModule, MatCheckboxModule, ControlMessagesModule, FlexModule, MatInputModule, MatCheckboxModule, MatDividerModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FlexModule", "FormsModule", "ReactiveFormsModule", "MatButtonModule", "MatCheckboxModule", "MatDialogModule", "MatDividerModule", "MatFormFieldModule", "MatIconModule", "MatInputModule", "TranslateModule", "SwuiGridModule", "SwuiNotificationsModule", "SwuiPagePanelModule", "ControlMessagesModule", "RoleService", "UserService", "ConfirmDeleteDialogComponent", "RolesDialogComponent", "RolesComponent", "RolesRoutingModule", "RolesModule", "imports", "<PERSON><PERSON><PERSON><PERSON>", "forRoot", "declarations", "i1", "i2"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/users/components/roles/roles.module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { FlexModule } from '@angular/flex-layout';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiGridModule, SwuiNotificationsModule, SwuiPagePanelModule } from '@skywind-group/lib-swui';\nimport { ControlMessagesModule } from '../../../../common/components/control-messages/control-messages.module';\nimport { RoleService } from '../../../../common/services/role.service';\nimport { UserService } from '../../../../common/services/user.service';\nimport { ConfirmDeleteDialogComponent } from './dialogs/confirm-delete-dialog.component';\nimport { RolesDialogComponent } from './dialogs/roles-dialog.component';\n\nimport { RolesComponent } from './roles.component';\nimport { RolesRoutingModule } from './roles.routing';\n\n@NgModule({\n  imports: [\n    CommonModule,\n    TranslateModule.forChild(),\n    RolesRoutingModule,\n    SwuiPagePanelModule,\n    FormsModule,\n    ReactiveFormsModule,\n    MatDialogModule,\n    MatButtonModule,\n    MatIconModule,\n    MatFormFieldModule,\n    SwuiGridModule,\n    SwuiNotificationsModule.forRoot(),\n    MatCheckboxModule,\n    ControlMessagesModule,\n    FlexModule,\n    MatInputModule,\n    MatCheckboxModule,\n    MatDividerModule,\n  ],\n  exports: [],\n  declarations: [\n    RolesComponent,\n    RolesDialogComponent,\n    ConfirmDeleteDialogComponent,\n  ],\n  providers: [\n    RoleService,\n    UserService,\n  ],\n})\nexport class RolesModule {\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,UAAU,QAAQ,sBAAsB;AACjD,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,cAAc,EAAEC,uBAAuB,EAAEC,mBAAmB,QAAQ,yBAAyB;AACtG,SAASC,qBAAqB,QAAQ,wEAAwE;AAC9G,SAASC,WAAW,QAAQ,0CAA0C;AACtE,SAASC,WAAW,QAAQ,0CAA0C;AACtE,SAASC,4BAA4B,QAAQ,2CAA2C;AACxF,SAASC,oBAAoB,QAAQ,kCAAkC;AAEvE,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,kBAAkB,QAAQ,iBAAiB;;;;AAkCpD,OAAM,MAAOC,WAAW;;;uCAAXA,WAAW;IAAA;EAAA;;;YAAXA;IAAW;EAAA;;;iBALX,CACTN,WAAW,EACXC,WAAW,CACZ;MAAAM,OAAA,GA5BCvB,YAAY,EACZW,eAAe,CAACa,QAAQ,EAAE,EAC1BH,kBAAkB,EAClBP,mBAAmB,EACnBZ,WAAW,EACXC,mBAAmB,EACnBG,eAAe,EACfF,eAAe,EACfK,aAAa,EACbD,kBAAkB,EAClBI,cAAc,EACdC,uBAAuB,CAACY,OAAO,EAAE,EACjCpB,iBAAiB,EACjBU,qBAAqB,EACrBd,UAAU,EACVS,cAAc,EACdL,iBAAiB,EACjBE,gBAAgB;IAAA;EAAA;;;2EAaPe,WAAW;IAAAI,YAAA,GATpBN,cAAc,EACdD,oBAAoB,EACpBD,4BAA4B;IAAAK,OAAA,GAvB5BvB,YAAY,EAAA2B,EAAA,CAAAhB,eAAA,EAEZU,kBAAkB,EAClBP,mBAAmB,EACnBZ,WAAW,EACXC,mBAAmB,EACnBG,eAAe,EACfF,eAAe,EACfK,aAAa,EACbD,kBAAkB,EAClBI,cAAc,EAAAgB,EAAA,CAAAf,uBAAA,EAEdR,iBAAiB,EACjBU,qBAAqB,EACrBd,UAAU,EACVS,cAAc,EACdL,iBAAiB,EACjBE,gBAAgB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}