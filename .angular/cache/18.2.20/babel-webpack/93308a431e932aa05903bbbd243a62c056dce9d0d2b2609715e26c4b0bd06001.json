{"ast": null, "code": "import { FormControl } from '@angular/forms';\nimport { MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"@angular/material/button\";\nimport * as i4 from \"@angular/material/form-field\";\nimport * as i5 from \"@skywind-group/lib-swui\";\nimport * as i6 from \"@ngx-translate/core\";\nexport class SelectProxyDialogComponent {\n  constructor(dialogRef, data) {\n    this.dialogRef = dialogRef;\n    this.data = data;\n    this.selectboxProxies = [];\n    this.proxyControl = new FormControl();\n    this.destroyed$ = new Subject();\n    this.proxyControl.setValue(data.merchantProxy.proxy ? data.merchantProxy.proxy.id : '');\n    this.processProxies();\n  }\n  ngOnInit() {\n    this.proxyControl.valueChanges.pipe(takeUntil(this.destroyed$)).subscribe(id => {\n      this.selectedProxy = this.data.proxies.find(proxy => proxy.id === id);\n    });\n  }\n  ngOnDestroy() {\n    this.destroyed$.next();\n    this.destroyed$.complete();\n  }\n  selectProxy({\n    id\n  }) {\n    this.selectedProxy = this.data.proxies.find(proxy => proxy.id === id);\n  }\n  submitSelectedProxy() {\n    if (this.selectedProxy) {\n      this.dialogRef.close(this.selectedProxy);\n    }\n  }\n  processProxies() {\n    this.selectboxProxies = this.data.proxies.map(item => {\n      let title = item.url;\n      if ('description' in item) {\n        title += ' (' + item.description + ')';\n      }\n      let obj = {\n        id: item.id,\n        text: title\n      };\n      if (this.data.proxy && this.data.proxy.id === item.id) {\n        obj['selected'] = true;\n      }\n      return obj;\n    });\n  }\n  static {\n    this.ɵfac = function SelectProxyDialogComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SelectProxyDialogComponent)(i0.ɵɵdirectiveInject(i1.MatDialogRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SelectProxyDialogComponent,\n      selectors: [[\"select-proxy-dialog\"]],\n      decls: 16,\n      vars: 18,\n      consts: [[\"mat-dialog-title\", \"\"], [1, \"mat-typography\"], [\"appearance\", \"outline\", 2, \"width\", \"100%\"], [3, \"data\", \"formControl\", \"showSearch\"], [\"align\", \"end\"], [\"mat-button\", \"\", \"mat-dialog-close\", \"\"], [\"mat-flat-button\", \"\", \"color\", \"primary\", \"cdkFocusInitial\", \"\", 3, \"click\", \"disabled\"]],\n      template: function SelectProxyDialogComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"h2\", 0);\n          i0.ɵɵtext(1);\n          i0.ɵɵpipe(2, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"mat-dialog-content\", 1)(4, \"mat-form-field\", 2)(5, \"mat-label\");\n          i0.ɵɵtext(6);\n          i0.ɵɵpipe(7, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(8, \"lib-swui-select\", 3);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"mat-dialog-actions\", 4)(10, \"button\", 5);\n          i0.ɵɵtext(11);\n          i0.ɵɵpipe(12, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function SelectProxyDialogComponent_Template_button_click_13_listener() {\n            return ctx.submitSelectedProxy();\n          });\n          i0.ɵɵtext(14);\n          i0.ɵɵpipe(15, \"translate\");\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 10, \"ENTITY_SETUP.PROXY.labelSelectProxyForEntity\"), \"\\n\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(7, 12, \"ENTITY_SETUP.PROXY.proxyTabName\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"data\", ctx.selectboxProxies)(\"formControl\", ctx.proxyControl)(\"showSearch\", true);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(12, 14, \"ENTITY_SETUP.PROXY.btnCancelProxy\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"disabled\", !ctx.selectedProxy);\n          i0.ɵɵproperty(\"disabled\", !ctx.selectedProxy);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(15, 16, \"ENTITY_SETUP.PROXY.btnSaveChanges\"), \" \");\n        }\n      },\n      dependencies: [i2.NgControlStatus, i2.FormControlDirective, i1.MatDialogClose, i1.MatDialogTitle, i1.MatDialogActions, i1.MatDialogContent, i3.MatButton, i4.MatFormField, i4.MatLabel, i5.SwuiSelectComponent, i6.TranslatePipe],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["FormControl", "MAT_DIALOG_DATA", "Subject", "takeUntil", "SelectProxyDialogComponent", "constructor", "dialogRef", "data", "selectboxProxies", "proxyControl", "destroyed$", "setValue", "merchantProxy", "proxy", "id", "processProxies", "ngOnInit", "valueChanges", "pipe", "subscribe", "selectedProxy", "proxies", "find", "ngOnDestroy", "next", "complete", "selectProxy", "submitSelectedProxy", "close", "map", "item", "title", "url", "description", "obj", "text", "i0", "ɵɵdirectiveInject", "i1", "MatDialogRef", "selectors", "decls", "vars", "consts", "template", "SelectProxyDialogComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵlistener", "SelectProxyDialogComponent_Template_button_click_13_listener", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "ɵɵtextInterpolate", "ɵɵproperty", "ɵɵclassProp"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-domains/manage-merchant-params/select-proxy-dialog.component.ts", "/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-domains/manage-merchant-params/select-proxy-dialog.component.html"], "sourcesContent": ["import { Component, Inject, On<PERSON><PERSON>roy, OnInit } from '@angular/core';\nimport { FormControl } from '@angular/forms';\nimport { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';\nimport { SwuiSelectOption } from '@skywind-group/lib-swui';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\n\nimport { Proxy } from '../../../../../../common/models/proxy.model';\n\n\nexport interface SelectProxyDialogData {\n  merchantProxy: any;\n  proxy: Proxy;\n  proxies: Proxy[];\n}\n\n@Component({\n  selector: 'select-proxy-dialog',\n  templateUrl: 'select-proxy-dialog.component.html'\n})\nexport class SelectProxyDialogComponent implements OnInit, OnDestroy {\n\n  selectboxProxies: SwuiSelectOption[] = [];\n  selectedProxy: Proxy;\n  proxyControl: FormControl = new FormControl();\n\n  private readonly destroyed$ = new Subject<void>();\n\n  constructor(\n    public dialogRef: MatDialogRef<SelectProxyDialogComponent, Proxy>,\n    @Inject(MAT_DIALOG_DATA) public data: SelectProxyDialogData,\n  ) {\n    this.proxyControl.setValue(data.merchantProxy.proxy ? data.merchantProxy.proxy.id : '');\n    this.processProxies();\n  }\n\n  ngOnInit() {\n    this.proxyControl.valueChanges\n      .pipe(\n        takeUntil(this.destroyed$)\n      )\n      .subscribe( (id: string) => {\n        this.selectedProxy = this.data.proxies.find(proxy => proxy.id === id);\n      });\n  }\n\n  ngOnDestroy(): void {\n    this.destroyed$.next();\n    this.destroyed$.complete();\n  }\n\n  public selectProxy({ id }) {\n    this.selectedProxy = this.data.proxies.find(proxy => proxy.id === id);\n  }\n\n  public submitSelectedProxy() {\n    if (this.selectedProxy) {\n      this.dialogRef.close(this.selectedProxy);\n    }\n  }\n\n  private processProxies() {\n    this.selectboxProxies = this.data.proxies.map(( item: Proxy ) => {\n      let title = item.url;\n      if ('description' in item) {\n        title += ' (' + item.description + ')';\n      }\n\n      let obj = { id: item.id, text: title };\n\n      if (this.data.proxy && this.data.proxy.id === item.id) {\n        obj['selected'] = true;\n      }\n\n      return obj;\n    });\n  }\n}\n", "<h2 mat-dialog-title>\n  {{ 'ENTITY_SETUP.PROXY.labelSelectProxyForEntity' | translate }}\n</h2>\n<mat-dialog-content class=\"mat-typography\">\n  <mat-form-field appearance=\"outline\" style=\"width: 100%\">\n    <mat-label>{{ 'ENTITY_SETUP.PROXY.proxyTabName' | translate }}</mat-label>\n    <lib-swui-select [data]=\"selectboxProxies\" [formControl]=\"proxyControl\" [showSearch]=\"true\"></lib-swui-select>\n  </mat-form-field>\n</mat-dialog-content>\n<mat-dialog-actions align=\"end\">\n  <button mat-button mat-dialog-close>\n    {{ 'ENTITY_SETUP.PROXY.btnCancelProxy' | translate }}\n  </button>\n  <button\n    mat-flat-button\n    color=\"primary\"\n    cdkFocusInitial\n    [class.disabled]=\"!selectedProxy\"\n    [disabled]=\"!selectedProxy\"\n    (click)=\"submitSelectedProxy()\">\n    {{ 'ENTITY_SETUP.PROXY.btnSaveChanges' | translate }}\n  </button>\n</mat-dialog-actions>\n"], "mappings": "AACA,SAASA,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,eAAe,QAAsB,0BAA0B;AAExE,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,SAAS,QAAQ,gBAAgB;;;;;;;;AAe1C,OAAM,MAAOC,0BAA0B;EAQrCC,YACSC,SAA0D,EACjCC,IAA2B;IADpD,KAAAD,SAAS,GAATA,SAAS;IACgB,KAAAC,IAAI,GAAJA,IAAI;IARtC,KAAAC,gBAAgB,GAAuB,EAAE;IAEzC,KAAAC,YAAY,GAAgB,IAAIT,WAAW,EAAE;IAE5B,KAAAU,UAAU,GAAG,IAAIR,OAAO,EAAQ;IAM/C,IAAI,CAACO,YAAY,CAACE,QAAQ,CAACJ,IAAI,CAACK,aAAa,CAACC,KAAK,GAAGN,IAAI,CAACK,aAAa,CAACC,KAAK,CAACC,EAAE,GAAG,EAAE,CAAC;IACvF,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACP,YAAY,CAACQ,YAAY,CAC3BC,IAAI,CACHf,SAAS,CAAC,IAAI,CAACO,UAAU,CAAC,CAC3B,CACAS,SAAS,CAAGL,EAAU,IAAI;MACzB,IAAI,CAACM,aAAa,GAAG,IAAI,CAACb,IAAI,CAACc,OAAO,CAACC,IAAI,CAACT,KAAK,IAAIA,KAAK,CAACC,EAAE,KAAKA,EAAE,CAAC;IACvE,CAAC,CAAC;EACN;EAEAS,WAAWA,CAAA;IACT,IAAI,CAACb,UAAU,CAACc,IAAI,EAAE;IACtB,IAAI,CAACd,UAAU,CAACe,QAAQ,EAAE;EAC5B;EAEOC,WAAWA,CAAC;IAAEZ;EAAE,CAAE;IACvB,IAAI,CAACM,aAAa,GAAG,IAAI,CAACb,IAAI,CAACc,OAAO,CAACC,IAAI,CAACT,KAAK,IAAIA,KAAK,CAACC,EAAE,KAAKA,EAAE,CAAC;EACvE;EAEOa,mBAAmBA,CAAA;IACxB,IAAI,IAAI,CAACP,aAAa,EAAE;MACtB,IAAI,CAACd,SAAS,CAACsB,KAAK,CAAC,IAAI,CAACR,aAAa,CAAC;IAC1C;EACF;EAEQL,cAAcA,CAAA;IACpB,IAAI,CAACP,gBAAgB,GAAG,IAAI,CAACD,IAAI,CAACc,OAAO,CAACQ,GAAG,CAAGC,IAAW,IAAK;MAC9D,IAAIC,KAAK,GAAGD,IAAI,CAACE,GAAG;MACpB,IAAI,aAAa,IAAIF,IAAI,EAAE;QACzBC,KAAK,IAAI,IAAI,GAAGD,IAAI,CAACG,WAAW,GAAG,GAAG;MACxC;MAEA,IAAIC,GAAG,GAAG;QAAEpB,EAAE,EAAEgB,IAAI,CAAChB,EAAE;QAAEqB,IAAI,EAAEJ;MAAK,CAAE;MAEtC,IAAI,IAAI,CAACxB,IAAI,CAACM,KAAK,IAAI,IAAI,CAACN,IAAI,CAACM,KAAK,CAACC,EAAE,KAAKgB,IAAI,CAAChB,EAAE,EAAE;QACrDoB,GAAG,CAAC,UAAU,CAAC,GAAG,IAAI;MACxB;MAEA,OAAOA,GAAG;IACZ,CAAC,CAAC;EACJ;;;uCAxDW9B,0BAA0B,EAAAgC,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAAH,EAAA,CAAAC,iBAAA,CAU3BpC,eAAe;IAAA;EAAA;;;YAVdG,0BAA0B;MAAAoC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCpBvCV,EAAA,CAAAY,cAAA,YAAqB;UACnBZ,EAAA,CAAAa,MAAA,GACF;;UAAAb,EAAA,CAAAc,YAAA,EAAK;UAGDd,EAFJ,CAAAY,cAAA,4BAA2C,wBACgB,gBAC5C;UAAAZ,EAAA,CAAAa,MAAA,GAAmD;;UAAAb,EAAA,CAAAc,YAAA,EAAY;UAC1Ed,EAAA,CAAAe,SAAA,yBAA8G;UAElHf,EADE,CAAAc,YAAA,EAAiB,EACE;UAEnBd,EADF,CAAAY,cAAA,4BAAgC,iBACM;UAClCZ,EAAA,CAAAa,MAAA,IACF;;UAAAb,EAAA,CAAAc,YAAA,EAAS;UACTd,EAAA,CAAAY,cAAA,iBAMkC;UAAhCZ,EAAA,CAAAgB,UAAA,mBAAAC,6DAAA;YAAA,OAASN,GAAA,CAAApB,mBAAA,EAAqB;UAAA,EAAC;UAC/BS,EAAA,CAAAa,MAAA,IACF;;UACFb,EADE,CAAAc,YAAA,EAAS,EACU;;;UArBnBd,EAAA,CAAAkB,SAAA,EACF;UADElB,EAAA,CAAAmB,kBAAA,MAAAnB,EAAA,CAAAoB,WAAA,8DACF;UAGepB,EAAA,CAAAkB,SAAA,GAAmD;UAAnDlB,EAAA,CAAAqB,iBAAA,CAAArB,EAAA,CAAAoB,WAAA,2CAAmD;UAC7CpB,EAAA,CAAAkB,SAAA,GAAyB;UAA8BlB,EAAvD,CAAAsB,UAAA,SAAAX,GAAA,CAAAvC,gBAAA,CAAyB,gBAAAuC,GAAA,CAAAtC,YAAA,CAA6B,oBAAoB;UAK3F2B,EAAA,CAAAkB,SAAA,GACF;UADElB,EAAA,CAAAmB,kBAAA,MAAAnB,EAAA,CAAAoB,WAAA,mDACF;UAKEpB,EAAA,CAAAkB,SAAA,GAAiC;UAAjClB,EAAA,CAAAuB,WAAA,cAAAZ,GAAA,CAAA3B,aAAA,CAAiC;UACjCgB,EAAA,CAAAsB,UAAA,cAAAX,GAAA,CAAA3B,aAAA,CAA2B;UAE3BgB,EAAA,CAAAkB,SAAA,EACF;UADElB,EAAA,CAAAmB,kBAAA,MAAAnB,EAAA,CAAAoB,WAAA,mDACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}