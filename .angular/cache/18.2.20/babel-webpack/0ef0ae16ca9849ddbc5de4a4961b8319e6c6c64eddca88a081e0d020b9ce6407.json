{"ast": null, "code": "import { FormControl } from '@angular/forms';\nimport { MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport { ValidationService } from '../../../../../../../common/services/validation.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"../../../../../../../common/components/control-messages/control-messages.component\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/form-field\";\nimport * as i7 from \"@angular/material/input\";\nimport * as i8 from \"../../../../../../../common/directives/trim-input-value/trim-input-value.component\";\nimport * as i9 from \"@angular/material/list\";\nimport * as i10 from \"@ngx-translate/core\";\nfunction RemoveConfirmDialogComponent_mat_list_item_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-list-item\")(1, \"strong\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const row_r1 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(row_r1);\n  }\n}\nexport class RemoveConfirmDialogComponent {\n  constructor(data, dialogRef) {\n    this.data = data;\n    this.dialogRef = dialogRef;\n    this.issueIdControl = new FormControl('', ValidationService.issueIdValidator);\n  }\n  doConfirm() {\n    if (this.issueIdControl.valid) {\n      this.dialogRef.close([this.issueIdControl.value]);\n    }\n  }\n  static {\n    this.ɵfac = function RemoveConfirmDialogComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RemoveConfirmDialogComponent)(i0.ɵɵdirectiveInject(MAT_DIALOG_DATA), i0.ɵɵdirectiveInject(i1.MatDialogRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RemoveConfirmDialogComponent,\n      selectors: [[\"remove-confirm-dialog\"]],\n      decls: 20,\n      vars: 17,\n      consts: [[\"mat-dialog-title\", \"\"], [1, \"mat-typography\"], [4, \"ngFor\", \"ngForOf\"], [\"appearance\", \"outline\", 2, \"width\", \"100%\"], [\"matInput\", \"\", \"trimValue\", \"\", \"type\", \"text\", \"placeholder\", \"ABC-1234\", 3, \"formControl\"], [3, \"control\"], [\"align\", \"end\"], [\"mat-button\", \"\", \"color\", \"primary\", 1, \"mat-button-md\", 3, \"mat-dialog-close\"], [\"mat-flat-button\", \"\", \"color\", \"primary\", \"cdkFocusInitial\", \"\", 1, \"mat-button-md\", 3, \"click\", \"disabled\"]],\n      template: function RemoveConfirmDialogComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"h2\", 0);\n          i0.ɵɵtext(1);\n          i0.ɵɵpipe(2, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"mat-dialog-content\", 1)(4, \"mat-list\");\n          i0.ɵɵtemplate(5, RemoveConfirmDialogComponent_mat_list_item_5_Template, 3, 1, \"mat-list-item\", 2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"mat-form-field\", 3)(7, \"mat-label\");\n          i0.ɵɵtext(8);\n          i0.ɵɵpipe(9, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(10, \"input\", 4);\n          i0.ɵɵelementStart(11, \"mat-error\");\n          i0.ɵɵelement(12, \"control-messages\", 5);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"mat-dialog-actions\", 6)(14, \"button\", 7);\n          i0.ɵɵtext(15);\n          i0.ɵɵpipe(16, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"button\", 8);\n          i0.ɵɵlistener(\"click\", function RemoveConfirmDialogComponent_Template_button_click_17_listener() {\n            return ctx.doConfirm();\n          });\n          i0.ɵɵtext(18);\n          i0.ɵɵpipe(19, \"translate\");\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 9, \"ENTITY_SETUP.WHITELISTING.removedWhitelistConfirmation\"), \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngForOf\", ctx.data.rows);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(9, 11, \"ENTITY_SETUP.WHITELISTING.placeholderIssueId\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formControl\", ctx.issueIdControl);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"control\", ctx.issueIdControl);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"mat-dialog-close\", false);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(16, 13, \"ALL.decline\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.issueIdControl.invalid);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(19, 15, \"ALL.confirm\"), \" \");\n        }\n      },\n      dependencies: [i2.NgForOf, i3.ControlMessagesComponent, i4.DefaultValueAccessor, i4.NgControlStatus, i4.FormControlDirective, i5.MatButton, i6.MatFormField, i6.MatLabel, i6.MatError, i7.MatInput, i8.TrimInputValueComponent, i1.MatDialogClose, i1.MatDialogTitle, i1.MatDialogActions, i1.MatDialogContent, i9.MatList, i9.MatListItem, i10.TranslatePipe],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["FormControl", "MAT_DIALOG_DATA", "ValidationService", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "row_r1", "RemoveConfirmDialogComponent", "constructor", "data", "dialogRef", "issueIdControl", "issueIdValidator", "doConfirm", "valid", "close", "value", "ɵɵdirectiveInject", "i1", "MatDialogRef", "selectors", "decls", "vars", "consts", "template", "RemoveConfirmDialogComponent_Template", "rf", "ctx", "ɵɵtemplate", "RemoveConfirmDialogComponent_mat_list_item_5_Template", "ɵɵelement", "ɵɵlistener", "RemoveConfirmDialogComponent_Template_button_click_17_listener", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "ɵɵproperty", "rows", "invalid"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-2fa/entity-ip-whitelist/dialogs/remove-confirm-dialog.component.ts", "/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-2fa/entity-ip-whitelist/dialogs/remove-confirm-dialog.component.html"], "sourcesContent": ["import { Component, Inject } from '@angular/core';\nimport { FormControl } from '@angular/forms';\nimport { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';\nimport { ValidationService } from '../../../../../../../common/services/validation.service';\n\ninterface RemoveConfirmDialogData {\n  rows: string[];\n}\n\n@Component({\n  selector: 'remove-confirm-dialog',\n  templateUrl: 'remove-confirm-dialog.component.html'\n})\nexport class RemoveConfirmDialogComponent {\n  readonly issueIdControl = new FormControl('', ValidationService.issueIdValidator);\n\n  constructor(@Inject(MAT_DIALOG_DATA) public data: RemoveConfirmDialogData,\n    private readonly dialogRef: MatDialogRef<RemoveConfirmDialogComponent, string[]>) {\n  }\n\n  doConfirm() {\n    if (this.issueIdControl.valid) {\n      this.dialogRef.close([this.issueIdControl.value]);\n    }\n  }\n}\n", "<h2 mat-dialog-title=\"\">\n  {{'ENTITY_SETUP.WHITELISTING.removedWhitelistConfirmation' | translate}}\n\n</h2>\n<mat-dialog-content class=\"mat-typography\">\n  <mat-list>\n    <mat-list-item *ngFor=\"let row of data.rows\">\n      <strong>{{ row }}</strong>\n    </mat-list-item>\n  </mat-list>\n  <mat-form-field appearance=\"outline\" style=\"width:100%\">\n    <mat-label>{{ 'ENTITY_SETUP.WHITELISTING.placeholderIssueId' | translate }}</mat-label>\n    <input matInput trimValue type=\"text\" [formControl]=\"issueIdControl\" placeholder=\"ABC-1234\">\n    <mat-error><control-messages [control]=\"issueIdControl\"></control-messages></mat-error>\n  </mat-form-field>\n</mat-dialog-content>\n<mat-dialog-actions align=\"end\">\n  <button mat-button color=\"primary\" class=\"mat-button-md\" [mat-dialog-close]=\"false\">\n    {{ 'ALL.decline' | translate }}\n  </button>\n  <button mat-flat-button color=\"primary\" class=\"mat-button-md\" cdkFocusInitial (click)=\"doConfirm()\" [disabled]=\"issueIdControl.invalid\">\n    {{'ALL.confirm' | translate}}\n  </button>\n</mat-dialog-actions>\n"], "mappings": "AACA,SAASA,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,eAAe,QAAsB,0BAA0B;AACxE,SAASC,iBAAiB,QAAQ,yDAAyD;;;;;;;;;;;;;;ICIrFC,EADF,CAAAC,cAAA,oBAA6C,aACnC;IAAAD,EAAA,CAAAE,MAAA,GAAS;IACnBF,EADmB,CAAAG,YAAA,EAAS,EACZ;;;;IADNH,EAAA,CAAAI,SAAA,GAAS;IAATJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAS;;;ADMvB,OAAM,MAAOC,4BAA4B;EAGvCC,YAA4CC,IAA6B,EACtDC,SAA+D;IADtC,KAAAD,IAAI,GAAJA,IAAI;IAC7B,KAAAC,SAAS,GAATA,SAAS;IAHnB,KAAAC,cAAc,GAAG,IAAId,WAAW,CAAC,EAAE,EAAEE,iBAAiB,CAACa,gBAAgB,CAAC;EAIjF;EAEAC,SAASA,CAAA;IACP,IAAI,IAAI,CAACF,cAAc,CAACG,KAAK,EAAE;MAC7B,IAAI,CAACJ,SAAS,CAACK,KAAK,CAAC,CAAC,IAAI,CAACJ,cAAc,CAACK,KAAK,CAAC,CAAC;IACnD;EACF;;;uCAXWT,4BAA4B,EAAAP,EAAA,CAAAiB,iBAAA,CAGnBnB,eAAe,GAAAE,EAAA,CAAAiB,iBAAA,CAAAC,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAHxBZ,4BAA4B;MAAAa,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCbzC1B,EAAA,CAAAC,cAAA,YAAwB;UACtBD,EAAA,CAAAE,MAAA,GAEF;;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAEHH,EADF,CAAAC,cAAA,4BAA2C,eAC/B;UACRD,EAAA,CAAA4B,UAAA,IAAAC,qDAAA,2BAA6C;UAG/C7B,EAAA,CAAAG,YAAA,EAAW;UAETH,EADF,CAAAC,cAAA,wBAAwD,gBAC3C;UAAAD,EAAA,CAAAE,MAAA,GAAgE;;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACvFH,EAAA,CAAA8B,SAAA,gBAA4F;UAC5F9B,EAAA,CAAAC,cAAA,iBAAW;UAAAD,EAAA,CAAA8B,SAAA,2BAAgE;UAE/E9B,EAF+E,CAAAG,YAAA,EAAY,EACxE,EACE;UAEnBH,EADF,CAAAC,cAAA,6BAAgC,iBACsD;UAClFD,EAAA,CAAAE,MAAA,IACF;;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,iBAAwI;UAA1DD,EAAA,CAAA+B,UAAA,mBAAAC,+DAAA;YAAA,OAASL,GAAA,CAAAd,SAAA,EAAW;UAAA,EAAC;UACjGb,EAAA,CAAAE,MAAA,IACF;;UACFF,EADE,CAAAG,YAAA,EAAS,EACU;;;UAtBnBH,EAAA,CAAAI,SAAA,EAEF;UAFEJ,EAAA,CAAAiC,kBAAA,MAAAjC,EAAA,CAAAkC,WAAA,sEAEF;UAGmClC,EAAA,CAAAI,SAAA,GAAY;UAAZJ,EAAA,CAAAmC,UAAA,YAAAR,GAAA,CAAAlB,IAAA,CAAA2B,IAAA,CAAY;UAKhCpC,EAAA,CAAAI,SAAA,GAAgE;UAAhEJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAkC,WAAA,wDAAgE;UACrClC,EAAA,CAAAI,SAAA,GAA8B;UAA9BJ,EAAA,CAAAmC,UAAA,gBAAAR,GAAA,CAAAhB,cAAA,CAA8B;UACvCX,EAAA,CAAAI,SAAA,GAA0B;UAA1BJ,EAAA,CAAAmC,UAAA,YAAAR,GAAA,CAAAhB,cAAA,CAA0B;UAIAX,EAAA,CAAAI,SAAA,GAA0B;UAA1BJ,EAAA,CAAAmC,UAAA,2BAA0B;UACjFnC,EAAA,CAAAI,SAAA,EACF;UADEJ,EAAA,CAAAiC,kBAAA,MAAAjC,EAAA,CAAAkC,WAAA,6BACF;UACoGlC,EAAA,CAAAI,SAAA,GAAmC;UAAnCJ,EAAA,CAAAmC,UAAA,aAAAR,GAAA,CAAAhB,cAAA,CAAA0B,OAAA,CAAmC;UACrIrC,EAAA,CAAAI,SAAA,EACF;UADEJ,EAAA,CAAAiC,kBAAA,MAAAjC,EAAA,CAAAkC,WAAA,6BACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}