{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FlexLayoutModule } from '@angular/flex-layout';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { DragulaModule } from 'ng2-dragula';\nimport { GameCategoryPreviewModalComponent } from './game-category-preview-modal.component';\nimport { CdnService } from '../../../../common/services/cdn.service';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"ng2-dragula\";\nexport class GameCategoryPreviewModule {\n  static {\n    this.ɵfac = function GameCategoryPreviewModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || GameCategoryPreviewModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: GameCategoryPreviewModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [CdnService],\n      imports: [CommonModule, TranslateModule, DragulaModule.forRoot(), FlexLayoutModule, MatButtonModule, MatCardModule, MatChipsModule, MatDialogModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(GameCategoryPreviewModule, {\n    declarations: [GameCategoryPreviewModalComponent],\n    imports: [CommonModule, TranslateModule, i1.DragulaModule, FlexLayoutModule, MatButtonModule, MatCardModule, MatChipsModule, MatDialogModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FlexLayoutModule", "TranslateModule", "DragulaModule", "GameCategoryPreviewModalComponent", "CdnService", "MatCardModule", "MatButtonModule", "MatChipsModule", "MatDialogModule", "GameCategoryPreviewModule", "imports", "forRoot", "declarations", "i1"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/games-categories-management/game-category-details/game-category-preview/game-category-preview.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FlexLayoutModule } from '@angular/flex-layout';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { DragulaModule } from 'ng2-dragula';\nimport { GameCategoryPreviewModalComponent } from './game-category-preview-modal.component';\nimport { CdnService } from '../../../../common/services/cdn.service';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatDialogModule } from '@angular/material/dialog';\n\n@NgModule({\n  imports: [\n    CommonModule,\n    TranslateModule,\n    DragulaModule.forRoot(),\n    FlexLayoutModule,\n    MatButtonModule,\n    MatCardModule,\n    MatChipsModule,\n    MatDialogModule,\n  ],\n  exports: [],\n  declarations: [\n    GameCategoryPreviewModalComponent,\n  ],\n  providers: [\n    CdnService,\n  ],\n})\nexport class GameCategoryPreviewModule {\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,aAAa,QAAQ,aAAa;AAC3C,SAASC,iCAAiC,QAAQ,yCAAyC;AAC3F,SAASC,UAAU,QAAQ,yCAAyC;AACpE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;;;AAqB1D,OAAM,MAAOC,yBAAyB;;;uCAAzBA,yBAAyB;IAAA;EAAA;;;YAAzBA;IAAyB;EAAA;;;iBAJzB,CACTL,UAAU,CACX;MAAAM,OAAA,GAfCX,YAAY,EACZE,eAAe,EACfC,aAAa,CAACS,OAAO,EAAE,EACvBX,gBAAgB,EAChBM,eAAe,EACfD,aAAa,EACbE,cAAc,EACdC,eAAe;IAAA;EAAA;;;2EAUNC,yBAAyB;IAAAG,YAAA,GANlCT,iCAAiC;IAAAO,OAAA,GAXjCX,YAAY,EACZE,eAAe,EAAAY,EAAA,CAAAX,aAAA,EAEfF,gBAAgB,EAChBM,eAAe,EACfD,aAAa,EACbE,cAAc,EACdC,eAAe;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}