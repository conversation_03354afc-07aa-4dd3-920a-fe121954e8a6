{"ast": null, "code": "import { PreloadAllModules, RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  redirectTo: 'pages',\n  pathMatch: 'full'\n}, {\n  path: 'pages',\n  loadChildren: () => import('./pages/pages.module').then(m => m.PagesModule)\n}, {\n  path: 'empty',\n  loadChildren: () => import('./pages/empty/empty.module').then(m => m.EmptyModule)\n}, {\n  path: 'integration-test-result',\n  loadChildren: () => import('./pages/integrations/integration-test-result/integration-test-result.module').then(m => m.IntegrationTestResultModule)\n}, {\n  path: 'gitbook',\n  loadChildren: () => import('./gitbook/gitbook.module').then(m => m.GitbookModule)\n}];\nexport class AppRoutingModule {\n  static {\n    this.ɵfac = function AppRoutingModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AppRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forRoot(routes, {\n        preloadingStrategy: PreloadAllModules\n      }), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["PreloadAllModules", "RouterModule", "routes", "path", "redirectTo", "pathMatch", "loadChildren", "then", "m", "PagesModule", "EmptyModule", "IntegrationTestResultModule", "GitbookModule", "AppRoutingModule", "forRoot", "preloadingStrategy", "imports", "i1", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/app.routing.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { PreloadAllModules, RouterModule, Routes } from '@angular/router';\n\n\nconst routes: Routes = [\n  { path: '', redirectTo: 'pages', pathMatch: 'full' },\n  {\n    path: 'pages',\n    loadChildren: () => import('./pages/pages.module').then(m => m.PagesModule),\n  },\n  {\n    path: 'empty',\n    loadChildren: () => import('./pages/empty/empty.module').then(m => m.EmptyModule),\n  },\n  {\n    path: 'integration-test-result',\n    loadChildren: () =>\n      import('./pages/integrations/integration-test-result/integration-test-result.module').then(m => m.IntegrationTestResultModule),\n  },\n  {\n    path: 'gitbook',\n    loadChildren: () => import('./gitbook/gitbook.module').then(m => m.GitbookModule),\n  },\n];\n\n@NgModule({\n  imports: [RouterModule.forRoot(routes, { preloadingStrategy: PreloadAllModules })],\n  exports: [RouterModule]\n})\nexport class AppRoutingModule {\n}\n"], "mappings": "AACA,SAASA,iBAAiB,EAAEC,YAAY,QAAgB,iBAAiB;;;AAGzE,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,EAAE;EAAEC,UAAU,EAAE,OAAO;EAAEC,SAAS,EAAE;AAAM,CAAE,EACpD;EACEF,IAAI,EAAE,OAAO;EACbG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,WAAW;CAC3E,EACD;EACEN,IAAI,EAAE,OAAO;EACbG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACE,WAAW;CACjF,EACD;EACEP,IAAI,EAAE,yBAAyB;EAC/BG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,6EAA6E,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACG,2BAA2B;CAChI,EACD;EACER,IAAI,EAAE,SAAS;EACfG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,0BAA0B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACI,aAAa;CACjF,CACF;AAMD,OAAM,MAAOC,gBAAgB;;;uCAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBAHjBZ,YAAY,CAACa,OAAO,CAACZ,MAAM,EAAE;QAAEa,kBAAkB,EAAEf;MAAiB,CAAE,CAAC,EACvEC,YAAY;IAAA;EAAA;;;2EAEXY,gBAAgB;IAAAG,OAAA,GAAAC,EAAA,CAAAhB,YAAA;IAAAiB,OAAA,GAFjBjB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}