{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, '__esModule', {\n  value: true\n});\nfunction _interopDefault(ex) {\n  return ex && typeof ex === 'object' && 'default' in ex ? ex['default'] : ex;\n}\nvar createPointCb = _interopDefault(require('create-point-cb'));\nfunction createWindowRect() {\n  var props = {\n    top: {\n      value: 0,\n      enumerable: true\n    },\n    left: {\n      value: 0,\n      enumerable: true\n    },\n    right: {\n      value: window.innerWidth,\n      enumerable: true\n    },\n    bottom: {\n      value: window.innerHeight,\n      enumerable: true\n    },\n    width: {\n      value: window.innerWidth,\n      enumerable: true\n    },\n    height: {\n      value: window.innerHeight,\n      enumerable: true\n    },\n    x: {\n      value: 0,\n      enumerable: true\n    },\n    y: {\n      value: 0,\n      enumerable: true\n    }\n  };\n  if (Object.create) {\n    return Object.create({}, props);\n  } else {\n    var rect = {};\n    Object.defineProperties(rect, props);\n    return rect;\n  }\n}\nfunction getClientRect(el) {\n  if (el === window) {\n    return createWindowRect();\n  } else {\n    try {\n      var rect = el.getBoundingClientRect();\n      if (rect.x === undefined) {\n        rect.x = rect.left;\n        rect.y = rect.top;\n      }\n      return rect;\n    } catch (e) {\n      throw new TypeError(\"Can't call getBoundingClientRect on \" + el);\n    }\n  }\n}\nfunction pointInside(point, el) {\n  var rect = getClientRect(el);\n  return point.y > rect.top && point.y < rect.bottom && point.x > rect.left && point.x < rect.right;\n}\nexports.createPointCB = createPointCb;\nexports.getClientRect = getClientRect;\nexports.pointInside = pointInside;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "_interopDefault", "ex", "createPointCb", "require", "createWindowRect", "props", "top", "enumerable", "left", "right", "window", "innerWidth", "bottom", "innerHeight", "width", "height", "x", "y", "create", "rect", "defineProperties", "getClientRect", "el", "getBoundingClientRect", "undefined", "e", "TypeError", "pointInside", "point", "createPointCB"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/node_modules/dom-plane/dist/bundle.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, '__esModule', { value: true });\n\nfunction _interopDefault (ex) { return (ex && (typeof ex === 'object') && 'default' in ex) ? ex['default'] : ex; }\n\nvar createPointCb = _interopDefault(require('create-point-cb'));\n\nfunction createWindowRect() {\n    var props = {\n        top: { value: 0, enumerable: true },\n        left: { value: 0, enumerable: true },\n        right: { value: window.innerWidth, enumerable: true },\n        bottom: { value: window.innerHeight, enumerable: true },\n        width: { value: window.innerWidth, enumerable: true },\n        height: { value: window.innerHeight, enumerable: true },\n        x: { value: 0, enumerable: true },\n        y: { value: 0, enumerable: true }\n    };\n\n    if (Object.create) {\n        return Object.create({}, props);\n    } else {\n        var rect = {};\n        Object.defineProperties(rect, props);\n        return rect;\n    }\n}\n\nfunction getClientRect(el) {\n    if (el === window) {\n        return createWindowRect();\n    } else {\n        try {\n            var rect = el.getBoundingClientRect();\n            if (rect.x === undefined) {\n                rect.x = rect.left;\n                rect.y = rect.top;\n            }\n            return rect;\n        } catch (e) {\n            throw new TypeError(\"Can't call getBoundingClientRect on \" + el);\n        }\n    }\n}\n\nfunction pointInside(point, el) {\n    var rect = getClientRect(el);\n    return point.y > rect.top && point.y < rect.bottom && point.x > rect.left && point.x < rect.right;\n}\n\nexports.createPointCB = createPointCb;\nexports.getClientRect = getClientRect;\nexports.pointInside = pointInside;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAE7D,SAASC,eAAeA,CAAEC,EAAE,EAAE;EAAE,OAAQA,EAAE,IAAK,OAAOA,EAAE,KAAK,QAAS,IAAI,SAAS,IAAIA,EAAE,GAAIA,EAAE,CAAC,SAAS,CAAC,GAAGA,EAAE;AAAE;AAEjH,IAAIC,aAAa,GAAGF,eAAe,CAACG,OAAO,CAAC,iBAAiB,CAAC,CAAC;AAE/D,SAASC,gBAAgBA,CAAA,EAAG;EACxB,IAAIC,KAAK,GAAG;IACRC,GAAG,EAAE;MAAEP,KAAK,EAAE,CAAC;MAAEQ,UAAU,EAAE;IAAK,CAAC;IACnCC,IAAI,EAAE;MAAET,KAAK,EAAE,CAAC;MAAEQ,UAAU,EAAE;IAAK,CAAC;IACpCE,KAAK,EAAE;MAAEV,KAAK,EAAEW,MAAM,CAACC,UAAU;MAAEJ,UAAU,EAAE;IAAK,CAAC;IACrDK,MAAM,EAAE;MAAEb,KAAK,EAAEW,MAAM,CAACG,WAAW;MAAEN,UAAU,EAAE;IAAK,CAAC;IACvDO,KAAK,EAAE;MAAEf,KAAK,EAAEW,MAAM,CAACC,UAAU;MAAEJ,UAAU,EAAE;IAAK,CAAC;IACrDQ,MAAM,EAAE;MAAEhB,KAAK,EAAEW,MAAM,CAACG,WAAW;MAAEN,UAAU,EAAE;IAAK,CAAC;IACvDS,CAAC,EAAE;MAAEjB,KAAK,EAAE,CAAC;MAAEQ,UAAU,EAAE;IAAK,CAAC;IACjCU,CAAC,EAAE;MAAElB,KAAK,EAAE,CAAC;MAAEQ,UAAU,EAAE;IAAK;EACpC,CAAC;EAED,IAAIX,MAAM,CAACsB,MAAM,EAAE;IACf,OAAOtB,MAAM,CAACsB,MAAM,CAAC,CAAC,CAAC,EAAEb,KAAK,CAAC;EACnC,CAAC,MAAM;IACH,IAAIc,IAAI,GAAG,CAAC,CAAC;IACbvB,MAAM,CAACwB,gBAAgB,CAACD,IAAI,EAAEd,KAAK,CAAC;IACpC,OAAOc,IAAI;EACf;AACJ;AAEA,SAASE,aAAaA,CAACC,EAAE,EAAE;EACvB,IAAIA,EAAE,KAAKZ,MAAM,EAAE;IACf,OAAON,gBAAgB,CAAC,CAAC;EAC7B,CAAC,MAAM;IACH,IAAI;MACA,IAAIe,IAAI,GAAGG,EAAE,CAACC,qBAAqB,CAAC,CAAC;MACrC,IAAIJ,IAAI,CAACH,CAAC,KAAKQ,SAAS,EAAE;QACtBL,IAAI,CAACH,CAAC,GAAGG,IAAI,CAACX,IAAI;QAClBW,IAAI,CAACF,CAAC,GAAGE,IAAI,CAACb,GAAG;MACrB;MACA,OAAOa,IAAI;IACf,CAAC,CAAC,OAAOM,CAAC,EAAE;MACR,MAAM,IAAIC,SAAS,CAAC,sCAAsC,GAAGJ,EAAE,CAAC;IACpE;EACJ;AACJ;AAEA,SAASK,WAAWA,CAACC,KAAK,EAAEN,EAAE,EAAE;EAC5B,IAAIH,IAAI,GAAGE,aAAa,CAACC,EAAE,CAAC;EAC5B,OAAOM,KAAK,CAACX,CAAC,GAAGE,IAAI,CAACb,GAAG,IAAIsB,KAAK,CAACX,CAAC,GAAGE,IAAI,CAACP,MAAM,IAAIgB,KAAK,CAACZ,CAAC,GAAGG,IAAI,CAACX,IAAI,IAAIoB,KAAK,CAACZ,CAAC,GAAGG,IAAI,CAACV,KAAK;AACrG;AAEAX,OAAO,CAAC+B,aAAa,GAAG3B,aAAa;AACrCJ,OAAO,CAACuB,aAAa,GAAGA,aAAa;AACrCvB,OAAO,CAAC6B,WAAW,GAAGA,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}