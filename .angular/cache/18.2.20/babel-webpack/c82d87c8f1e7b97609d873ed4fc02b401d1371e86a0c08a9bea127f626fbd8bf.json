{"ast": null, "code": "import { SchemaFilterMatchEnum } from '@skywind-group/lib-swui';\nimport { DISPLAY_GAME_STATUS_LIST } from '../general-games-info/games.schema';\nimport { gameStatusClassMap } from '../../../../../../app.constants';\nexport const SCHEMA = [{\n  field: 'title',\n  title: 'ENTITY_SETUP.GAMES.title',\n  type: 'string',\n  td: {\n    type: 'string',\n    nowrap: true\n  },\n  isList: true,\n  isViewable: false,\n  isSortable: false,\n  isFilterable: true,\n  isFilterableAlways: false,\n  filterMatch: SchemaFilterMatchEnum.Contains,\n  alignment: {\n    th: 'left',\n    td: 'left'\n  }\n}, {\n  field: 'code',\n  title: 'ENTITY_SETUP.GAMES.code',\n  type: 'string',\n  isList: true,\n  isViewable: false,\n  isSortable: false,\n  isFilterable: true,\n  isFilterableAlways: false,\n  alignment: {\n    th: 'left',\n    td: 'left'\n  }\n}, {\n  field: 'status',\n  title: 'ENTITY_SETUP.GAMES.status',\n  type: 'select',\n  isList: true,\n  isViewable: true,\n  isSortable: true,\n  isFilterable: true,\n  data: DISPLAY_GAME_STATUS_LIST,\n  td: {\n    type: 'status',\n    displayStatusList: DISPLAY_GAME_STATUS_LIST,\n    classMap: gameStatusClassMap,\n    readonly: true\n  }\n}, {\n  field: 'connectedPool',\n  title: 'ENTITY_SETUP.GAMES.connectedPool',\n  type: 'string',\n  isList: true,\n  isViewable: false,\n  isSortable: false,\n  isFilterable: false,\n  isFilterableAlways: true,\n  td: {\n    type: 'actionList',\n    valueFn: row => {\n      if (row?.settings?.jackpotId) {\n        return Object.keys(row?.settings?.jackpotId).map(key => {\n          return `${key} {${row?.settings?.jackpotId[key]}}`;\n        });\n      } else if (row?.features?.jackpotTypes) {\n        return row.features?.jackpotTypes.map(type => `${type.toLowerCase()} {-}`);\n      }\n      return [];\n    },\n    isDisabled: row => {\n      return !row._meta.jpnDetailsAllowed;\n    }\n  },\n  alignment: {\n    th: 'left',\n    td: 'left'\n  }\n}, {\n  field: 'jackpotTypes',\n  title: 'ENTITY_SETUP.GAMES.connectedPool',\n  type: 'string',\n  isList: false,\n  isViewable: false,\n  isSortable: false,\n  isFilterable: true\n}];\nexport const SCHEMA_LIST = SCHEMA.filter(el => el.isList);\nexport const SCHEMA_FILTER = SCHEMA.filter(el => el.isFilterable);", "map": {"version": 3, "names": ["SchemaFilterMatchEnum", "DISPLAY_GAME_STATUS_LIST", "gameStatusClassMap", "SCHEMA", "field", "title", "type", "td", "nowrap", "isList", "isViewable", "isSortable", "isFilterable", "isFilterableAlways", "filterMatch", "Contains", "alignment", "th", "data", "displayStatusList", "classMap", "readonly", "valueFn", "row", "settings", "jackpotId", "Object", "keys", "map", "key", "features", "jackpotTypes", "toLowerCase", "isDisabled", "_meta", "jpnDetailsAllowed", "SCHEMA_LIST", "filter", "el", "SCHEMA_FILTER"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-games/jp-games-info/jp-games-info.schema.ts"], "sourcesContent": ["import { SchemaFilterMatchEnum, SwuiGridField } from '@skywind-group/lib-swui';\nimport { Game } from '../../../../../../common/typings';\nimport { DISPLAY_GAME_STATUS_LIST } from '../general-games-info/games.schema';\nimport { gameStatusClassMap } from '../../../../../../app.constants';\n\nexport const SCHEMA: SwuiGridField[] = [\n  {\n    field: 'title',\n    title: 'ENTITY_SETUP.GAMES.title',\n    type: 'string',\n    td: {\n      type: 'string',\n      nowrap: true,\n    },\n    isList: true,\n    isViewable: false,\n    isSortable: false,\n    isFilterable: true,\n    isFilterableAlways: false,\n    filterMatch: SchemaFilterMatchEnum.Contains,\n    alignment: {\n      th: 'left',\n      td: 'left',\n    },\n  },\n  {\n    field: 'code',\n    title: 'ENTITY_SETUP.GAMES.code',\n    type: 'string',\n    isList: true,\n    isViewable: false,\n    isSortable: false,\n    isFilterable: true,\n    isFilterableAlways: false,\n    alignment: {\n      th: 'left',\n      td: 'left',\n    },\n  },\n  {\n    field: 'status',\n    title: 'ENTITY_SETUP.GAMES.status',\n    type: 'select',\n    isList: true,\n    isViewable: true,\n    isSortable: true,\n    isFilterable: true,\n    data: DISPLAY_GAME_STATUS_LIST,\n    td: {\n      type: 'status',\n      displayStatusList: DISPLAY_GAME_STATUS_LIST,\n      classMap: gameStatusClassMap,\n      readonly: true\n    },\n  },\n  {\n    field: 'connectedPool',\n    title: 'ENTITY_SETUP.GAMES.connectedPool',\n    type: 'string',\n    isList: true,\n    isViewable: false,\n    isSortable: false,\n    isFilterable: false,\n    isFilterableAlways: true,\n    td: {\n      type: 'actionList',\n      valueFn: ( row: Game ) => {\n        if (row?.settings?.jackpotId) {\n          return Object.keys(row?.settings?.jackpotId).map(key => {\n            return `${key} {${row?.settings?.jackpotId[key]}}`;\n          });\n        } else if (row?.features?.jackpotTypes) {\n          return row.features?.jackpotTypes.map(type => `${type.toLowerCase()} {-}`);\n        }\n        return [];\n      },\n      isDisabled: ( row: Game ) => {\n        return !row._meta.jpnDetailsAllowed;\n      }\n    },\n    alignment: {\n      th: 'left',\n      td: 'left',\n    },\n  },\n  {\n    field: 'jackpotTypes',\n    title: 'ENTITY_SETUP.GAMES.connectedPool',\n    type: 'string',\n    isList: false,\n    isViewable: false,\n    isSortable: false,\n    isFilterable: true\n  }\n];\n\n\nexport const SCHEMA_LIST = SCHEMA.filter(el => el.isList);\nexport const SCHEMA_FILTER = SCHEMA.filter(el => el.isFilterable);\n"], "mappings": "AAAA,SAASA,qBAAqB,QAAuB,yBAAyB;AAE9E,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,kBAAkB,QAAQ,iCAAiC;AAEpE,OAAO,MAAMC,MAAM,GAAoB,CACrC;EACEC,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE,0BAA0B;EACjCC,IAAI,EAAE,QAAQ;EACdC,EAAE,EAAE;IACFD,IAAI,EAAE,QAAQ;IACdE,MAAM,EAAE;GACT;EACDC,MAAM,EAAE,IAAI;EACZC,UAAU,EAAE,KAAK;EACjBC,UAAU,EAAE,KAAK;EACjBC,YAAY,EAAE,IAAI;EAClBC,kBAAkB,EAAE,KAAK;EACzBC,WAAW,EAAEd,qBAAqB,CAACe,QAAQ;EAC3CC,SAAS,EAAE;IACTC,EAAE,EAAE,MAAM;IACVV,EAAE,EAAE;;CAEP,EACD;EACEH,KAAK,EAAE,MAAM;EACbC,KAAK,EAAE,yBAAyB;EAChCC,IAAI,EAAE,QAAQ;EACdG,MAAM,EAAE,IAAI;EACZC,UAAU,EAAE,KAAK;EACjBC,UAAU,EAAE,KAAK;EACjBC,YAAY,EAAE,IAAI;EAClBC,kBAAkB,EAAE,KAAK;EACzBG,SAAS,EAAE;IACTC,EAAE,EAAE,MAAM;IACVV,EAAE,EAAE;;CAEP,EACD;EACEH,KAAK,EAAE,QAAQ;EACfC,KAAK,EAAE,2BAA2B;EAClCC,IAAI,EAAE,QAAQ;EACdG,MAAM,EAAE,IAAI;EACZC,UAAU,EAAE,IAAI;EAChBC,UAAU,EAAE,IAAI;EAChBC,YAAY,EAAE,IAAI;EAClBM,IAAI,EAAEjB,wBAAwB;EAC9BM,EAAE,EAAE;IACFD,IAAI,EAAE,QAAQ;IACda,iBAAiB,EAAElB,wBAAwB;IAC3CmB,QAAQ,EAAElB,kBAAkB;IAC5BmB,QAAQ,EAAE;;CAEb,EACD;EACEjB,KAAK,EAAE,eAAe;EACtBC,KAAK,EAAE,kCAAkC;EACzCC,IAAI,EAAE,QAAQ;EACdG,MAAM,EAAE,IAAI;EACZC,UAAU,EAAE,KAAK;EACjBC,UAAU,EAAE,KAAK;EACjBC,YAAY,EAAE,KAAK;EACnBC,kBAAkB,EAAE,IAAI;EACxBN,EAAE,EAAE;IACFD,IAAI,EAAE,YAAY;IAClBgB,OAAO,EAAIC,GAAS,IAAK;MACvB,IAAIA,GAAG,EAAEC,QAAQ,EAAEC,SAAS,EAAE;QAC5B,OAAOC,MAAM,CAACC,IAAI,CAACJ,GAAG,EAAEC,QAAQ,EAAEC,SAAS,CAAC,CAACG,GAAG,CAACC,GAAG,IAAG;UACrD,OAAO,GAAGA,GAAG,KAAKN,GAAG,EAAEC,QAAQ,EAAEC,SAAS,CAACI,GAAG,CAAC,GAAG;QACpD,CAAC,CAAC;MACJ,CAAC,MAAM,IAAIN,GAAG,EAAEO,QAAQ,EAAEC,YAAY,EAAE;QACtC,OAAOR,GAAG,CAACO,QAAQ,EAAEC,YAAY,CAACH,GAAG,CAACtB,IAAI,IAAI,GAAGA,IAAI,CAAC0B,WAAW,EAAE,MAAM,CAAC;MAC5E;MACA,OAAO,EAAE;IACX,CAAC;IACDC,UAAU,EAAIV,GAAS,IAAK;MAC1B,OAAO,CAACA,GAAG,CAACW,KAAK,CAACC,iBAAiB;IACrC;GACD;EACDnB,SAAS,EAAE;IACTC,EAAE,EAAE,MAAM;IACVV,EAAE,EAAE;;CAEP,EACD;EACEH,KAAK,EAAE,cAAc;EACrBC,KAAK,EAAE,kCAAkC;EACzCC,IAAI,EAAE,QAAQ;EACdG,MAAM,EAAE,KAAK;EACbC,UAAU,EAAE,KAAK;EACjBC,UAAU,EAAE,KAAK;EACjBC,YAAY,EAAE;CACf,CACF;AAGD,OAAO,MAAMwB,WAAW,GAAGjC,MAAM,CAACkC,MAAM,CAACC,EAAE,IAAIA,EAAE,CAAC7B,MAAM,CAAC;AACzD,OAAO,MAAM8B,aAAa,GAAGpC,MAAM,CAACkC,MAAM,CAACC,EAAE,IAAIA,EAAE,CAAC1B,YAAY,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}