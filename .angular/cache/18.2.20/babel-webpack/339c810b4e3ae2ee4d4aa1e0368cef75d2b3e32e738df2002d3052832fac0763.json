{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FlexModule } from '@angular/flex-layout';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiCheckboxModule, SwuiControlMessagesModule, SwuiGridModule, SwuiSchemaTopFilterModule, SwuiSelectModule } from '@skywind-group/lib-swui';\nimport { BoLabelsGroupModule } from '../../../../../common/components/bo-labels-group/bo-labels-group.module';\nimport { DownloadCsvModule } from '../../../../../common/components/download-csv/download-csv.module';\nimport { HintsModule } from '../../../../../common/components/hints/hints.module';\nimport { WizardModule } from '../../../../../common/components/swWizard/wizard.module';\nimport { GameGroupFiltersService } from '../../../../../common/services/game-group-filters.service';\nimport { ProxyService } from '../../../../../common/services/proxy.service';\nimport { CurrenciesResolver } from '../../../../../common/services/resolvers/currencies.resolver';\nimport { LanguagesResolver } from '../../../../../common/services/resolvers/languages.resolver';\nimport { AddSpecificGamesAlertDialogComponent } from './dialogs/add-specific-games-alert-dialog.component';\nimport { GameForceRemoveDialogComponent } from './dialogs/game-force-remove-dialog.component';\nimport { GameSettingsToRunComponent } from './dialogs/game-settings-to-run.component';\nimport { ManageGamesDialogComponent } from './dialogs/manage-games-dialog.component';\nimport { ManageJackpotsDialogComponent } from './dialogs/manage-jackpots-dialog.component';\nimport { EntityGamesComponent } from './entity-games.component';\nimport { GamesSetupStepperModule } from './games-setup-stepper/games-setup-stepper.module';\nimport { GeneralGamesInfoModule } from './general-games-info/general-games-info.module';\nimport { JpGamesInfoModule } from './jp-games-info/jp-games-info.module';\nimport { ManageGamesService } from './manage-games.service';\nimport { ManageGamesModule } from './manage-games/manage-games.module';\nimport { TabGamesComponent } from './tab-games.component';\nimport { TabGamesRoutingModule } from './tab-games.routing';\nimport * as i0 from \"@angular/core\";\nexport class TabGamesModule {\n  static {\n    this.ɵfac = function TabGamesModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TabGamesModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: TabGamesModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [ManageGamesService, ProxyService, GameGroupFiltersService, LanguagesResolver, CurrenciesResolver],\n      imports: [CommonModule, TranslateModule, TabGamesRoutingModule, BoLabelsGroupModule, ManageGamesModule, WizardModule, SwuiGridModule, MatButtonModule, MatFormFieldModule, MatInputModule, MatDialogModule, GamesSetupStepperModule, FlexModule, HintsModule, MatTooltipModule, MatIconModule, SwuiGridModule, MatProgressSpinnerModule, DownloadCsvModule, SwuiSchemaTopFilterModule, ReactiveFormsModule, MatTabsModule, GeneralGamesInfoModule, JpGamesInfoModule, SwuiSelectModule, SwuiControlMessagesModule, SwuiCheckboxModule, MatCheckboxModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(TabGamesModule, {\n    declarations: [TabGamesComponent, EntityGamesComponent, ManageGamesDialogComponent, GameForceRemoveDialogComponent, ManageJackpotsDialogComponent, AddSpecificGamesAlertDialogComponent, GameSettingsToRunComponent],\n    imports: [CommonModule, TranslateModule, TabGamesRoutingModule, BoLabelsGroupModule, ManageGamesModule, WizardModule, SwuiGridModule, MatButtonModule, MatFormFieldModule, MatInputModule, MatDialogModule, GamesSetupStepperModule, FlexModule, HintsModule, MatTooltipModule, MatIconModule, SwuiGridModule, MatProgressSpinnerModule, DownloadCsvModule, SwuiSchemaTopFilterModule, ReactiveFormsModule, MatTabsModule, GeneralGamesInfoModule, JpGamesInfoModule, SwuiSelectModule, SwuiControlMessagesModule, SwuiCheckboxModule, MatCheckboxModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FlexModule", "ReactiveFormsModule", "MatButtonModule", "MatCheckboxModule", "MatDialogModule", "MatFormFieldModule", "MatIconModule", "MatInputModule", "MatProgressSpinnerModule", "MatTabsModule", "MatTooltipModule", "TranslateModule", "SwuiCheckboxModule", "SwuiControlMessagesModule", "SwuiGridModule", "SwuiSchemaTopFilterModule", "SwuiSelectModule", "BoLabelsGroupModule", "DownloadCsvModule", "HintsModule", "WizardModule", "GameGroupFiltersService", "ProxyService", "CurrenciesResolver", "LanguagesResolver", "AddSpecificGamesAlertDialogComponent", "GameForceRemoveDialogComponent", "GameSettingsToRunComponent", "ManageGamesDialogComponent", "ManageJackpotsDialogComponent", "EntityGamesComponent", "GamesSetupStepperModule", "GeneralGamesInfoModule", "JpGamesInfoModule", "ManageGamesService", "ManageGamesModule", "TabGamesComponent", "TabGamesRoutingModule", "TabGamesModule", "imports", "declarations"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-games/tab-games.module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { FlexModule } from '@angular/flex-layout';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { TranslateModule } from '@ngx-translate/core';\nimport {\n  SwuiCheckboxModule, SwuiControlMessagesModule, SwuiGridModule, SwuiSchemaTopFilterModule, SwuiSelectModule\n} from '@skywind-group/lib-swui';\nimport { BoLabelsGroupModule } from '../../../../../common/components/bo-labels-group/bo-labels-group.module';\nimport { DownloadCsvModule } from '../../../../../common/components/download-csv/download-csv.module';\nimport { HintsModule } from '../../../../../common/components/hints/hints.module';\nimport { WizardModule } from '../../../../../common/components/swWizard/wizard.module';\nimport { GameGroupFiltersService } from '../../../../../common/services/game-group-filters.service';\n\nimport { ProxyService } from '../../../../../common/services/proxy.service';\nimport { CurrenciesResolver } from '../../../../../common/services/resolvers/currencies.resolver';\nimport { LanguagesResolver } from '../../../../../common/services/resolvers/languages.resolver';\nimport { AddSpecificGamesAlertDialogComponent } from './dialogs/add-specific-games-alert-dialog.component';\nimport { GameForceRemoveDialogComponent } from './dialogs/game-force-remove-dialog.component';\nimport { GameSettingsToRunComponent } from './dialogs/game-settings-to-run.component';\nimport { ManageGamesDialogComponent } from './dialogs/manage-games-dialog.component';\nimport { ManageJackpotsDialogComponent } from './dialogs/manage-jackpots-dialog.component';\nimport { EntityGamesComponent } from './entity-games.component';\nimport { GamesSetupStepperModule } from './games-setup-stepper/games-setup-stepper.module';\nimport { GeneralGamesInfoModule } from './general-games-info/general-games-info.module';\nimport { JpGamesInfoModule } from './jp-games-info/jp-games-info.module';\nimport { ManageGamesService } from './manage-games.service';\nimport { ManageGamesModule } from './manage-games/manage-games.module';\nimport { TabGamesComponent } from './tab-games.component';\nimport { TabGamesRoutingModule } from './tab-games.routing';\n\n@NgModule({\n  imports: [\n    CommonModule,\n    TranslateModule,\n    TabGamesRoutingModule,\n    BoLabelsGroupModule,\n    ManageGamesModule,\n    WizardModule,\n    SwuiGridModule,\n    MatButtonModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatDialogModule,\n    GamesSetupStepperModule,\n    FlexModule,\n    HintsModule,\n    MatTooltipModule,\n    MatIconModule,\n    SwuiGridModule,\n    MatProgressSpinnerModule,\n    DownloadCsvModule,\n    SwuiSchemaTopFilterModule,\n    ReactiveFormsModule,\n    MatTabsModule,\n    GeneralGamesInfoModule,\n    JpGamesInfoModule,\n    SwuiSelectModule,\n    SwuiControlMessagesModule,\n    SwuiCheckboxModule,\n    MatCheckboxModule\n  ],\n  declarations: [\n    TabGamesComponent,\n    EntityGamesComponent,\n    ManageGamesDialogComponent,\n    GameForceRemoveDialogComponent,\n    ManageJackpotsDialogComponent,\n    AddSpecificGamesAlertDialogComponent,\n    GameSettingsToRunComponent\n  ],\n  exports: [],\n  providers: [\n    ManageGamesService,\n    ProxyService,\n    GameGroupFiltersService,\n    LanguagesResolver,\n    CurrenciesResolver\n  ],\n})\nexport class TabGamesModule {\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,UAAU,QAAQ,sBAAsB;AACjD,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SACEC,kBAAkB,EAAEC,yBAAyB,EAAEC,cAAc,EAAEC,yBAAyB,EAAEC,gBAAgB,QACrG,yBAAyB;AAChC,SAASC,mBAAmB,QAAQ,yEAAyE;AAC7G,SAASC,iBAAiB,QAAQ,mEAAmE;AACrG,SAASC,WAAW,QAAQ,qDAAqD;AACjF,SAASC,YAAY,QAAQ,yDAAyD;AACtF,SAASC,uBAAuB,QAAQ,2DAA2D;AAEnG,SAASC,YAAY,QAAQ,8CAA8C;AAC3E,SAASC,kBAAkB,QAAQ,8DAA8D;AACjG,SAASC,iBAAiB,QAAQ,6DAA6D;AAC/F,SAASC,oCAAoC,QAAQ,qDAAqD;AAC1G,SAASC,8BAA8B,QAAQ,8CAA8C;AAC7F,SAASC,0BAA0B,QAAQ,0CAA0C;AACrF,SAASC,0BAA0B,QAAQ,yCAAyC;AACpF,SAASC,6BAA6B,QAAQ,4CAA4C;AAC1F,SAASC,oBAAoB,QAAQ,0BAA0B;AAC/D,SAASC,uBAAuB,QAAQ,kDAAkD;AAC1F,SAASC,sBAAsB,QAAQ,gDAAgD;AACvF,SAASC,iBAAiB,QAAQ,sCAAsC;AACxE,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,SAASC,iBAAiB,QAAQ,oCAAoC;AACtE,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD,SAASC,qBAAqB,QAAQ,qBAAqB;;AAmD3D,OAAM,MAAOC,cAAc;;;uCAAdA,cAAc;IAAA;EAAA;;;YAAdA;IAAc;EAAA;;;iBARd,CACTJ,kBAAkB,EAClBZ,YAAY,EACZD,uBAAuB,EACvBG,iBAAiB,EACjBD,kBAAkB,CACnB;MAAAgB,OAAA,GA7CCxC,YAAY,EACZY,eAAe,EACf0B,qBAAqB,EACrBpB,mBAAmB,EACnBkB,iBAAiB,EACjBf,YAAY,EACZN,cAAc,EACdZ,eAAe,EACfG,kBAAkB,EAClBE,cAAc,EACdH,eAAe,EACf2B,uBAAuB,EACvB/B,UAAU,EACVmB,WAAW,EACXT,gBAAgB,EAChBJ,aAAa,EACbQ,cAAc,EACdN,wBAAwB,EACxBU,iBAAiB,EACjBH,yBAAyB,EACzBd,mBAAmB,EACnBQ,aAAa,EACbuB,sBAAsB,EACtBC,iBAAiB,EACjBjB,gBAAgB,EAChBH,yBAAyB,EACzBD,kBAAkB,EAClBT,iBAAiB;IAAA;EAAA;;;2EAoBRmC,cAAc;IAAAE,YAAA,GAjBvBJ,iBAAiB,EACjBN,oBAAoB,EACpBF,0BAA0B,EAC1BF,8BAA8B,EAC9BG,6BAA6B,EAC7BJ,oCAAoC,EACpCE,0BAA0B;IAAAY,OAAA,GApC1BxC,YAAY,EACZY,eAAe,EACf0B,qBAAqB,EACrBpB,mBAAmB,EACnBkB,iBAAiB,EACjBf,YAAY,EACZN,cAAc,EACdZ,eAAe,EACfG,kBAAkB,EAClBE,cAAc,EACdH,eAAe,EACf2B,uBAAuB,EACvB/B,UAAU,EACVmB,WAAW,EACXT,gBAAgB,EAChBJ,aAAa,EACbQ,cAAc,EACdN,wBAAwB,EACxBU,iBAAiB,EACjBH,yBAAyB,EACzBd,mBAAmB,EACnBQ,aAAa,EACbuB,sBAAsB,EACtBC,iBAAiB,EACjBjB,gBAAgB,EAChBH,yBAAyB,EACzBD,kBAAkB,EAClBT,iBAAiB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}