{"ast": null, "code": "'use strict';\n\nvar customEvent = require('custom-event');\nvar eventmap = require('./eventmap');\nvar doc = global.document;\nvar addEvent = addEventEasy;\nvar removeEvent = removeEventEasy;\nvar hardCache = [];\nif (!global.addEventListener) {\n  addEvent = addEventHard;\n  removeEvent = removeEventHard;\n}\nmodule.exports = {\n  add: addEvent,\n  remove: removeEvent,\n  fabricate: fabricateEvent\n};\nfunction addEventEasy(el, type, fn, capturing) {\n  return el.addEventListener(type, fn, capturing);\n}\nfunction addEventHard(el, type, fn) {\n  return el.attachEvent('on' + type, wrap(el, type, fn));\n}\nfunction removeEventEasy(el, type, fn, capturing) {\n  return el.removeEventListener(type, fn, capturing);\n}\nfunction removeEventHard(el, type, fn) {\n  var listener = unwrap(el, type, fn);\n  if (listener) {\n    return el.detachEvent('on' + type, listener);\n  }\n}\nfunction fabricateEvent(el, type, model) {\n  var e = eventmap.indexOf(type) === -1 ? makeCustomEvent() : makeClassicEvent();\n  if (el.dispatchEvent) {\n    el.dispatchEvent(e);\n  } else {\n    el.fireEvent('on' + type, e);\n  }\n  function makeClassicEvent() {\n    var e;\n    if (doc.createEvent) {\n      e = doc.createEvent('Event');\n      e.initEvent(type, true, true);\n    } else if (doc.createEventObject) {\n      e = doc.createEventObject();\n    }\n    return e;\n  }\n  function makeCustomEvent() {\n    return new customEvent(type, {\n      detail: model\n    });\n  }\n}\nfunction wrapperFactory(el, type, fn) {\n  return function wrapper(originalEvent) {\n    var e = originalEvent || global.event;\n    e.target = e.target || e.srcElement;\n    e.preventDefault = e.preventDefault || function preventDefault() {\n      e.returnValue = false;\n    };\n    e.stopPropagation = e.stopPropagation || function stopPropagation() {\n      e.cancelBubble = true;\n    };\n    e.which = e.which || e.keyCode;\n    fn.call(el, e);\n  };\n}\nfunction wrap(el, type, fn) {\n  var wrapper = unwrap(el, type, fn) || wrapperFactory(el, type, fn);\n  hardCache.push({\n    wrapper: wrapper,\n    element: el,\n    type: type,\n    fn: fn\n  });\n  return wrapper;\n}\nfunction unwrap(el, type, fn) {\n  var i = find(el, type, fn);\n  if (i) {\n    var wrapper = hardCache[i].wrapper;\n    hardCache.splice(i, 1); // free up a tad of memory\n    return wrapper;\n  }\n}\nfunction find(el, type, fn) {\n  var i, item;\n  for (i = 0; i < hardCache.length; i++) {\n    item = hardCache[i];\n    if (item.element === el && item.type === type && item.fn === fn) {\n      return i;\n    }\n  }\n}", "map": {"version": 3, "names": ["customEvent", "require", "eventmap", "doc", "global", "document", "addEvent", "addEventEasy", "removeEvent", "removeEventEasy", "hardCache", "addEventListener", "addEventHard", "removeEventHard", "module", "exports", "add", "remove", "fabricate", "fabricateEvent", "el", "type", "fn", "capturing", "attachEvent", "wrap", "removeEventListener", "listener", "unwrap", "detachEvent", "model", "e", "indexOf", "makeCustomEvent", "makeClassicEvent", "dispatchEvent", "fireEvent", "createEvent", "initEvent", "createEventObject", "detail", "wrapperFactory", "wrapper", "originalEvent", "event", "target", "srcElement", "preventDefault", "returnValue", "stopPropagation", "cancelBubble", "which", "keyCode", "call", "push", "element", "i", "find", "splice", "item", "length"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/node_modules/crossvent/src/crossvent.js"], "sourcesContent": ["'use strict';\n\nvar customEvent = require('custom-event');\nvar eventmap = require('./eventmap');\nvar doc = global.document;\nvar addEvent = addEventEasy;\nvar removeEvent = removeEventEasy;\nvar hardCache = [];\n\nif (!global.addEventListener) {\n  addEvent = addEventHard;\n  removeEvent = removeEventHard;\n}\n\nmodule.exports = {\n  add: addEvent,\n  remove: removeEvent,\n  fabricate: fabricateEvent\n};\n\nfunction addEventEasy (el, type, fn, capturing) {\n  return el.addEventListener(type, fn, capturing);\n}\n\nfunction addEventHard (el, type, fn) {\n  return el.attachEvent('on' + type, wrap(el, type, fn));\n}\n\nfunction removeEventEasy (el, type, fn, capturing) {\n  return el.removeEventListener(type, fn, capturing);\n}\n\nfunction removeEventHard (el, type, fn) {\n  var listener = unwrap(el, type, fn);\n  if (listener) {\n    return el.detachEvent('on' + type, listener);\n  }\n}\n\nfunction fabricateEvent (el, type, model) {\n  var e = eventmap.indexOf(type) === -1 ? makeCustomEvent() : makeClassicEvent();\n  if (el.dispatchEvent) {\n    el.dispatchEvent(e);\n  } else {\n    el.fireEvent('on' + type, e);\n  }\n  function makeClassicEvent () {\n    var e;\n    if (doc.createEvent) {\n      e = doc.createEvent('Event');\n      e.initEvent(type, true, true);\n    } else if (doc.createEventObject) {\n      e = doc.createEventObject();\n    }\n    return e;\n  }\n  function makeCustomEvent () {\n    return new customEvent(type, { detail: model });\n  }\n}\n\nfunction wrapperFactory (el, type, fn) {\n  return function wrapper (originalEvent) {\n    var e = originalEvent || global.event;\n    e.target = e.target || e.srcElement;\n    e.preventDefault = e.preventDefault || function preventDefault () { e.returnValue = false; };\n    e.stopPropagation = e.stopPropagation || function stopPropagation () { e.cancelBubble = true; };\n    e.which = e.which || e.keyCode;\n    fn.call(el, e);\n  };\n}\n\nfunction wrap (el, type, fn) {\n  var wrapper = unwrap(el, type, fn) || wrapperFactory(el, type, fn);\n  hardCache.push({\n    wrapper: wrapper,\n    element: el,\n    type: type,\n    fn: fn\n  });\n  return wrapper;\n}\n\nfunction unwrap (el, type, fn) {\n  var i = find(el, type, fn);\n  if (i) {\n    var wrapper = hardCache[i].wrapper;\n    hardCache.splice(i, 1); // free up a tad of memory\n    return wrapper;\n  }\n}\n\nfunction find (el, type, fn) {\n  var i, item;\n  for (i = 0; i < hardCache.length; i++) {\n    item = hardCache[i];\n    if (item.element === el && item.type === type && item.fn === fn) {\n      return i;\n    }\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,WAAW,GAAGC,OAAO,CAAC,cAAc,CAAC;AACzC,IAAIC,QAAQ,GAAGD,OAAO,CAAC,YAAY,CAAC;AACpC,IAAIE,GAAG,GAAGC,MAAM,CAACC,QAAQ;AACzB,IAAIC,QAAQ,GAAGC,YAAY;AAC3B,IAAIC,WAAW,GAAGC,eAAe;AACjC,IAAIC,SAAS,GAAG,EAAE;AAElB,IAAI,CAACN,MAAM,CAACO,gBAAgB,EAAE;EAC5BL,QAAQ,GAAGM,YAAY;EACvBJ,WAAW,GAAGK,eAAe;AAC/B;AAEAC,MAAM,CAACC,OAAO,GAAG;EACfC,GAAG,EAAEV,QAAQ;EACbW,MAAM,EAAET,WAAW;EACnBU,SAAS,EAAEC;AACb,CAAC;AAED,SAASZ,YAAYA,CAAEa,EAAE,EAAEC,IAAI,EAAEC,EAAE,EAAEC,SAAS,EAAE;EAC9C,OAAOH,EAAE,CAACT,gBAAgB,CAACU,IAAI,EAAEC,EAAE,EAAEC,SAAS,CAAC;AACjD;AAEA,SAASX,YAAYA,CAAEQ,EAAE,EAAEC,IAAI,EAAEC,EAAE,EAAE;EACnC,OAAOF,EAAE,CAACI,WAAW,CAAC,IAAI,GAAGH,IAAI,EAAEI,IAAI,CAACL,EAAE,EAAEC,IAAI,EAAEC,EAAE,CAAC,CAAC;AACxD;AAEA,SAASb,eAAeA,CAAEW,EAAE,EAAEC,IAAI,EAAEC,EAAE,EAAEC,SAAS,EAAE;EACjD,OAAOH,EAAE,CAACM,mBAAmB,CAACL,IAAI,EAAEC,EAAE,EAAEC,SAAS,CAAC;AACpD;AAEA,SAASV,eAAeA,CAAEO,EAAE,EAAEC,IAAI,EAAEC,EAAE,EAAE;EACtC,IAAIK,QAAQ,GAAGC,MAAM,CAACR,EAAE,EAAEC,IAAI,EAAEC,EAAE,CAAC;EACnC,IAAIK,QAAQ,EAAE;IACZ,OAAOP,EAAE,CAACS,WAAW,CAAC,IAAI,GAAGR,IAAI,EAAEM,QAAQ,CAAC;EAC9C;AACF;AAEA,SAASR,cAAcA,CAAEC,EAAE,EAAEC,IAAI,EAAES,KAAK,EAAE;EACxC,IAAIC,CAAC,GAAG7B,QAAQ,CAAC8B,OAAO,CAACX,IAAI,CAAC,KAAK,CAAC,CAAC,GAAGY,eAAe,CAAC,CAAC,GAAGC,gBAAgB,CAAC,CAAC;EAC9E,IAAId,EAAE,CAACe,aAAa,EAAE;IACpBf,EAAE,CAACe,aAAa,CAACJ,CAAC,CAAC;EACrB,CAAC,MAAM;IACLX,EAAE,CAACgB,SAAS,CAAC,IAAI,GAAGf,IAAI,EAAEU,CAAC,CAAC;EAC9B;EACA,SAASG,gBAAgBA,CAAA,EAAI;IAC3B,IAAIH,CAAC;IACL,IAAI5B,GAAG,CAACkC,WAAW,EAAE;MACnBN,CAAC,GAAG5B,GAAG,CAACkC,WAAW,CAAC,OAAO,CAAC;MAC5BN,CAAC,CAACO,SAAS,CAACjB,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC/B,CAAC,MAAM,IAAIlB,GAAG,CAACoC,iBAAiB,EAAE;MAChCR,CAAC,GAAG5B,GAAG,CAACoC,iBAAiB,CAAC,CAAC;IAC7B;IACA,OAAOR,CAAC;EACV;EACA,SAASE,eAAeA,CAAA,EAAI;IAC1B,OAAO,IAAIjC,WAAW,CAACqB,IAAI,EAAE;MAAEmB,MAAM,EAAEV;IAAM,CAAC,CAAC;EACjD;AACF;AAEA,SAASW,cAAcA,CAAErB,EAAE,EAAEC,IAAI,EAAEC,EAAE,EAAE;EACrC,OAAO,SAASoB,OAAOA,CAAEC,aAAa,EAAE;IACtC,IAAIZ,CAAC,GAAGY,aAAa,IAAIvC,MAAM,CAACwC,KAAK;IACrCb,CAAC,CAACc,MAAM,GAAGd,CAAC,CAACc,MAAM,IAAId,CAAC,CAACe,UAAU;IACnCf,CAAC,CAACgB,cAAc,GAAGhB,CAAC,CAACgB,cAAc,IAAI,SAASA,cAAcA,CAAA,EAAI;MAAEhB,CAAC,CAACiB,WAAW,GAAG,KAAK;IAAE,CAAC;IAC5FjB,CAAC,CAACkB,eAAe,GAAGlB,CAAC,CAACkB,eAAe,IAAI,SAASA,eAAeA,CAAA,EAAI;MAAElB,CAAC,CAACmB,YAAY,GAAG,IAAI;IAAE,CAAC;IAC/FnB,CAAC,CAACoB,KAAK,GAAGpB,CAAC,CAACoB,KAAK,IAAIpB,CAAC,CAACqB,OAAO;IAC9B9B,EAAE,CAAC+B,IAAI,CAACjC,EAAE,EAAEW,CAAC,CAAC;EAChB,CAAC;AACH;AAEA,SAASN,IAAIA,CAAEL,EAAE,EAAEC,IAAI,EAAEC,EAAE,EAAE;EAC3B,IAAIoB,OAAO,GAAGd,MAAM,CAACR,EAAE,EAAEC,IAAI,EAAEC,EAAE,CAAC,IAAImB,cAAc,CAACrB,EAAE,EAAEC,IAAI,EAAEC,EAAE,CAAC;EAClEZ,SAAS,CAAC4C,IAAI,CAAC;IACbZ,OAAO,EAAEA,OAAO;IAChBa,OAAO,EAAEnC,EAAE;IACXC,IAAI,EAAEA,IAAI;IACVC,EAAE,EAAEA;EACN,CAAC,CAAC;EACF,OAAOoB,OAAO;AAChB;AAEA,SAASd,MAAMA,CAAER,EAAE,EAAEC,IAAI,EAAEC,EAAE,EAAE;EAC7B,IAAIkC,CAAC,GAAGC,IAAI,CAACrC,EAAE,EAAEC,IAAI,EAAEC,EAAE,CAAC;EAC1B,IAAIkC,CAAC,EAAE;IACL,IAAId,OAAO,GAAGhC,SAAS,CAAC8C,CAAC,CAAC,CAACd,OAAO;IAClChC,SAAS,CAACgD,MAAM,CAACF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACxB,OAAOd,OAAO;EAChB;AACF;AAEA,SAASe,IAAIA,CAAErC,EAAE,EAAEC,IAAI,EAAEC,EAAE,EAAE;EAC3B,IAAIkC,CAAC,EAAEG,IAAI;EACX,KAAKH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG9C,SAAS,CAACkD,MAAM,EAAEJ,CAAC,EAAE,EAAE;IACrCG,IAAI,GAAGjD,SAAS,CAAC8C,CAAC,CAAC;IACnB,IAAIG,IAAI,CAACJ,OAAO,KAAKnC,EAAE,IAAIuC,IAAI,CAACtC,IAAI,KAAKA,IAAI,IAAIsC,IAAI,CAACrC,EAAE,KAAKA,EAAE,EAAE;MAC/D,OAAOkC,CAAC;IACV;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}