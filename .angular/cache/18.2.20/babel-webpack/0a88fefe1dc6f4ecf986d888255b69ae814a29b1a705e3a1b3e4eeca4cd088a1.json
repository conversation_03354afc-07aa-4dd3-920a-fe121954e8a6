{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { ManageBalanceModule } from '../manage-balance/manage-balance.module';\nimport { MatRegionalDialogComponent } from './mat-regional-dialog.component';\nimport { MatRegionalItemModule } from './mat-regional-item/mat-regional-item.module';\nimport * as i0 from \"@angular/core\";\nexport const matModules = [MatDialogModule, MatButtonModule, MatTabsModule, MatRegionalItemModule];\nexport class MatRegionalDialogModule {\n  static {\n    this.ɵfac = function MatRegionalDialogModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatRegionalDialogModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: MatRegionalDialogModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, TranslateModule, ReactiveFormsModule, matModules, ManageBalanceModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(MatRegionalDialogModule, {\n    declarations: [MatRegionalDialogComponent],\n    imports: [CommonModule, TranslateModule, ReactiveFormsModule, MatDialogModule, MatButtonModule, MatTabsModule, MatRegionalItemModule, ManageBalanceModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "ReactiveFormsModule", "MatButtonModule", "MatDialogModule", "MatTabsModule", "TranslateModule", "ManageBalanceModule", "MatRegionalDialogComponent", "MatRegionalItemModule", "matModules", "MatRegionalDialogModule", "declarations", "imports"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/mat-business-structure/dialogs/mat-regional-dialog/mat-regional-dialog.module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { ManageBalanceModule } from '../manage-balance/manage-balance.module';\nimport { MatRegionalDialogComponent } from './mat-regional-dialog.component';\nimport { MatRegionalItemModule } from './mat-regional-item/mat-regional-item.module';\n\nexport const matModules = [\n  MatDialogModule,\n  MatButtonModule,\n  MatTabsModule,\n  MatRegionalItemModule,\n];\n\n\n@NgModule({\n  declarations: [\n    MatRegionalDialogComponent,\n  ],\n  imports: [\n    CommonModule,\n    TranslateModule,\n    ReactiveFormsModule,\n    ...matModules,\n    ManageBalanceModule,\n  ]\n})\nexport class MatRegionalDialogModule {\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,mBAAmB,QAAQ,yCAAyC;AAC7E,SAASC,0BAA0B,QAAQ,iCAAiC;AAC5E,SAASC,qBAAqB,QAAQ,8CAA8C;;AAEpF,OAAO,MAAMC,UAAU,GAAG,CACxBN,eAAe,EACfD,eAAe,EACfE,aAAa,EACbI,qBAAqB,CACtB;AAeD,OAAM,MAAOE,uBAAuB;;;uCAAvBA,uBAAuB;IAAA;EAAA;;;YAAvBA;IAAuB;EAAA;;;gBAPhCV,YAAY,EACZK,eAAe,EACfJ,mBAAmB,EAChBQ,UAAU,EACbH,mBAAmB;IAAA;EAAA;;;2EAGVI,uBAAuB;IAAAC,YAAA,GAVhCJ,0BAA0B;IAAAK,OAAA,GAG1BZ,YAAY,EACZK,eAAe,EACfJ,mBAAmB,EAdrBE,eAAe,EACfD,eAAe,EACfE,aAAa,EACbI,qBAAqB,EAanBF,mBAAmB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}