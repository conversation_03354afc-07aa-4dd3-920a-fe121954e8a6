{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@skywind-group/lib-swui\";\nimport * as i4 from \"../../../lobby-menu-items-ribbons/lobby-menu-items-ribbons.component\";\nimport * as i5 from \"@angular/material/input\";\nimport * as i6 from \"@angular/material/form-field\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/icon\";\nimport * as i9 from \"@angular/flex-layout/flex\";\nimport * as i10 from \"@angular/flex-layout/extended\";\nimport * as i11 from \"../../../../../../../common/directives/trim-input-value/trim-input-value.component\";\nimport * as i12 from \"@ngx-translate/core\";\nimport * as i13 from \"../../../../../../../common/pipes/sanitise/sanitise.pipe\";\nconst _c0 = a0 => ({\n  \"withValue\": a0\n});\nfunction LobbyMenuItemsInfoComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵelement(1, \"lobby-menu-items-ribbons\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formControl\", ctx_r0.ribbonControl)(\"ribbons\", ctx_r0.defaultRibbons)(\"maxLength\", 9);\n  }\n}\nfunction LobbyMenuItemsInfoComponent_div_15_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function LobbyMenuItemsInfoComponent_div_15_button_6_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.removeIcon($event));\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"clear\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction LobbyMenuItemsInfoComponent_div_15_object_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"object\", 17);\n    i0.ɵɵpipe(1, \"sanitize\");\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"data\", i0.ɵɵpipeBind2(1, 1, ctx_r0.iconControl.value, \"resourceUrl\"), i0.ɵɵsanitizeResourceUrl);\n  }\n}\nfunction LobbyMenuItemsInfoComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"button\", 11)(2, \"label\", 12);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementStart(5, \"input\", 13);\n    i0.ɵɵlistener(\"change\", function LobbyMenuItemsInfoComponent_div_15_Template_input_change_5_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onIconFileChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, LobbyMenuItemsInfoComponent_div_15_button_6_Template, 3, 0, \"button\", 14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(7, LobbyMenuItemsInfoComponent_div_15_object_7_Template, 2, 4, \"object\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(7, _c0, !!ctx_r0.iconControl.value && ctx_r0.iconControl.value !== \" \"));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 5, \"GAME_CATEGORIES.chooseIcon\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"id\", \"icon-control\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isRemoveIconAvailable());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !!ctx_r0.iconControl.value && ctx_r0.iconControl.value !== \" \");\n  }\n}\nexport class LobbyMenuItemsInfoComponent {\n  constructor(fb, cdr) {\n    this.fb = fb;\n    this.cdr = cdr;\n    this.isIconEnabled = false;\n    this.isRibbonsEnabled = false;\n    this.defaultRibbons = [{\n      text: 'Hot',\n      bg: '#EC2033',\n      color: '#FFFFFF'\n    }, {\n      text: 'New',\n      bg: '#FFCF00',\n      color: '#000000'\n    }, {\n      text: 'Exclusive',\n      bg: '#1B76EE',\n      color: '#FFFFFF'\n    }];\n    this.messageErrors = {\n      required: 'VALIDATION.required'\n    };\n    this.form = this.initForm();\n  }\n  initForm(val) {\n    const group = this.fb.group({\n      id: [''],\n      title: ['', Validators.required],\n      description: [''],\n      icon: [''],\n      ribbon: []\n    });\n    if (val) {\n      group.patchValue(val);\n    }\n    return group;\n  }\n  setValue(val) {\n    if (val) {\n      this.form.patchValue(val);\n    }\n  }\n  get valueChanges() {\n    return this.form.valueChanges;\n  }\n  get statusChanges() {\n    return this.form.statusChanges;\n  }\n  get invalid() {\n    return this.form.invalid;\n  }\n  patchValue(value, options) {\n    this.form.patchValue(value, options);\n    this.form.markAsPristine();\n    this.form.markAsUntouched();\n  }\n  isRemoveIconAvailable() {\n    return this.iconControl.value && this.iconControl.value !== ' ';\n  }\n  get titleControl() {\n    return this.form.get('title');\n  }\n  get descriptionControl() {\n    return this.form.get('description');\n  }\n  get iconControl() {\n    return this.form.get('icon');\n  }\n  get ribbonControl() {\n    return this.form.get('ribbon');\n  }\n  removeIcon(event) {\n    event.preventDefault();\n    event.stopPropagation();\n    this.iconControl.reset();\n    const iconInput = document.getElementById('icon-control');\n    iconInput.value = '';\n  }\n  onIconFileChange(event) {\n    const reader = new FileReader();\n    const target = event.target;\n    if (target.files && target.files.length) {\n      const [file] = target.files;\n      if (file.type === 'image/svg+xml') {\n        reader.readAsDataURL(file);\n      } else {\n        this.iconControl.setErrors({\n          requiredFileType: true\n        });\n      }\n      reader.onload = () => {\n        this.iconControl.patchValue(reader.result);\n        this.cdr.detectChanges();\n      };\n    }\n  }\n  static {\n    this.ɵfac = function LobbyMenuItemsInfoComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LobbyMenuItemsInfoComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LobbyMenuItemsInfoComponent,\n      selectors: [[\"lobby-menu-items-info\"]],\n      inputs: {\n        submitted: \"submitted\",\n        isIconEnabled: \"isIconEnabled\",\n        isRibbonsEnabled: \"isRibbonsEnabled\"\n      },\n      decls: 16,\n      vars: 13,\n      consts: [[\"fxLayout.lt-lg\", \"column\", \"fxLayout\", \"row\", \"fxLayoutGap.gt-md\", \"32px\", 3, \"formGroup\"], [\"fxFlex.lt-md\", \"100\", \"fxLayout\", \"column\", \"fxFlex\", \"\"], [\"appearance\", \"outline\"], [\"matInput\", \"\", \"trimValue\", \"\", \"type\", \"text\", 3, \"formControl\"], [3, \"messages\", \"control\"], [\"matInput\", \"\", \"trimValue\", \"\", \"rows\", \"6\", 1, \"form-control\", 3, \"formControl\"], [\"style\", \"margin-bottom: 1.1em;\", 4, \"ngIf\"], [\"class\", \"file-control\", 4, \"ngIf\"], [2, \"margin-bottom\", \"1.1em\"], [3, \"formControl\", \"ribbons\", \"maxLength\"], [1, \"file-control\"], [\"mat-flat-button\", \"\", \"color\", \"primary\", 1, \"file-control__button\"], [1, \"file-control__label\", 3, \"ngClass\"], [\"trimValue\", \"\", \"type\", \"file\", \"accept\", \"image/svg+xml\", 1, \"file-control__input\", 3, \"change\", \"id\"], [\"mat-icon-button\", \"\", \"class\", \"file-control__clear\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"file-control__icon\", \"type\", \"image/svg+xml\", \"width\", \"150\", \"height\", \"150\", 3, \"data\", 4, \"ngIf\"], [\"mat-icon-button\", \"\", 1, \"file-control__clear\", 3, \"click\"], [\"type\", \"image/svg+xml\", \"width\", \"150\", \"height\", \"150\", 1, \"file-control__icon\", 3, \"data\"]],\n      template: function LobbyMenuItemsInfoComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"form\", 0)(1, \"div\", 1)(2, \"mat-form-field\", 2)(3, \"mat-label\");\n          i0.ɵɵtext(4);\n          i0.ɵɵpipe(5, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(6, \"input\", 3);\n          i0.ɵɵelementStart(7, \"mat-error\");\n          i0.ɵɵelement(8, \"lib-swui-control-messages\", 4);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"mat-form-field\", 2)(10, \"mat-label\");\n          i0.ɵɵtext(11);\n          i0.ɵɵpipe(12, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(13, \"textarea\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(14, LobbyMenuItemsInfoComponent_div_14_Template, 2, 3, \"div\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(15, LobbyMenuItemsInfoComponent_div_15_Template, 8, 9, \"div\", 7);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"formGroup\", ctx.form);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 9, \"ALL.title\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formControl\", ctx.titleControl);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"messages\", ctx.messageErrors)(\"control\", ctx.titleControl);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(12, 11, \"ALL.description\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formControl\", ctx.descriptionControl);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isRibbonsEnabled);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isIconEnabled);\n        }\n      },\n      dependencies: [i2.NgClass, i2.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormControlDirective, i1.FormGroupDirective, i3.SwuiControlMessagesComponent, i4.LobbyMenuItemsRibbonsComponent, i5.MatInput, i6.MatFormField, i6.MatLabel, i6.MatError, i7.MatButton, i7.MatIconButton, i8.MatIcon, i9.DefaultLayoutDirective, i9.DefaultLayoutGapDirective, i9.DefaultFlexDirective, i10.DefaultClassDirective, i11.TrimInputValueComponent, i12.TranslatePipe, i13.SanitisePipe],\n      styles: [\"form[_ngcontent-%COMP%] {\\n  overflow: auto;\\n  max-width: 700px;\\n  padding-top: 12px;\\n}\\n\\n.file-control[_ngcontent-%COMP%] {\\n  padding-top: 4px;\\n}\\n.file-control__input[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n.file-control__icon[_ngcontent-%COMP%] {\\n  display: block;\\n  -webkit-user-select: none;\\n          user-select: none;\\n  background-image: linear-gradient(45deg, #ccc 25%, transparent 25%), linear-gradient(-45deg, #ccc 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #ccc 75%), linear-gradient(-45deg, transparent 75%, #ccc 75%);\\n  background-size: 10px 10px;\\n  background-position: 0 0, 0 5px, 5px -5px, -5px 0px;\\n}\\n.file-control__button[_ngcontent-%COMP%] {\\n  margin-bottom: 10px;\\n  padding: 0;\\n  cursor: pointer;\\n}\\n.file-control__label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 100%;\\n  height: 100%;\\n  padding: 0 1.1428571429em;\\n  cursor: pointer;\\n}\\n.file-control__label.withValue[_ngcontent-%COMP%] {\\n  padding-right: 0;\\n}\\n.file-control__clear[_ngcontent-%COMP%] {\\n  width: 44px;\\n  height: 44px;\\n  line-height: 44px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ctx_r0", "ribbonControl", "defaultRibbons", "ɵɵlistener", "LobbyMenuItemsInfoComponent_div_15_button_6_Template_button_click_0_listener", "$event", "ɵɵrestoreView", "_r3", "ɵɵnextContext", "ɵɵresetView", "removeIcon", "ɵɵtext", "ɵɵpipeBind2", "iconControl", "value", "ɵɵsanitizeResourceUrl", "LobbyMenuItemsInfoComponent_div_15_Template_input_change_5_listener", "_r2", "onIconFileChange", "ɵɵtemplate", "LobbyMenuItemsInfoComponent_div_15_button_6_Template", "LobbyMenuItemsInfoComponent_div_15_object_7_Template", "ɵɵpureFunction1", "_c0", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "isRemoveIconAvailable", "LobbyMenuItemsInfoComponent", "constructor", "fb", "cdr", "isIconEnabled", "isRibbonsEnabled", "text", "bg", "color", "messageErrors", "required", "form", "initForm", "val", "group", "id", "title", "description", "icon", "ribbon", "patchValue", "setValue", "valueChanges", "statusChanges", "invalid", "options", "mark<PERSON><PERSON>ristine", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "titleControl", "get", "descriptionControl", "event", "preventDefault", "stopPropagation", "reset", "iconInput", "document", "getElementById", "reader", "FileReader", "target", "files", "length", "file", "type", "readAsDataURL", "setErrors", "requiredFileType", "onload", "result", "detectChanges", "ɵɵdirectiveInject", "i1", "FormBuilder", "ChangeDetectorRef", "selectors", "inputs", "submitted", "decls", "vars", "consts", "template", "LobbyMenuItemsInfoComponent_Template", "rf", "ctx", "LobbyMenuItemsInfoComponent_div_14_Template", "LobbyMenuItemsInfoComponent_div_15_Template", "ɵɵtextInterpolate"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/lobby/lobby-form/lobby-menu-items/lobby-menu-items-setup/lobby-menu-items-general/lobby-menu-items-info/lobby-menu-items-info.component.ts", "/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/lobby/lobby-form/lobby-menu-items/lobby-menu-items-setup/lobby-menu-items-general/lobby-menu-items-info/lobby-menu-items-info.component.html"], "sourcesContent": ["import { ChangeDetectorRef, Component, Input } from '@angular/core';\nimport { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';\nimport { Observable } from 'rxjs';\nimport { ErrorMessage } from '../../../../../../../common/components/mat-user-editor/user-form.component';\nimport { LobbyMenuItemRibbon } from '../../../../../lobby.model';\n\n\n@Component({\n  selector: 'lobby-menu-items-info',\n  templateUrl: './lobby-menu-items-info.component.html',\n  styleUrls: ['./lobby-menu-items-info.component.scss'],\n})\nexport class LobbyMenuItemsInfoComponent {\n  @Input() submitted?: boolean;\n  @Input() isIconEnabled = false;\n  @Input() isRibbonsEnabled = false;\n\n  readonly form: FormGroup;\n  defaultRibbons: LobbyMenuItemRibbon[] = [\n    {\n      text: 'Hot',\n      bg: '#EC2033',\n      color: '#FFFFFF'\n    },\n    {\n      text: 'New',\n      bg: '#FFCF00',\n      color: '#000000'\n    },\n    {\n      text: 'Exclusive',\n      bg: '#1B76EE',\n      color: '#FFFFFF'\n    }\n  ];\n\n  messageErrors: ErrorMessage = {\n    required: 'VALIDATION.required'\n  };\n\n  constructor( private fb: FormBuilder,\n               private readonly cdr: ChangeDetectorRef ) {\n    this.form = this.initForm();\n  }\n\n  initForm(val?: any): FormGroup {\n    const group = this.fb.group({\n      id: [''],\n      title: ['', Validators.required],\n      description: [''],\n      icon: [''],\n      ribbon: []\n    });\n\n    if (val) {\n      group.patchValue(val);\n    }\n\n    return group;\n  }\n\n  setValue(val: any) {\n    if (val) {\n      this.form.patchValue(val);\n    }\n  }\n\n  get valueChanges(): Observable<any> {\n    return this.form.valueChanges;\n  }\n\n  get statusChanges(): Observable<any> {\n    return this.form.statusChanges;\n  }\n\n  get invalid(): boolean {\n    return this.form.invalid;\n  }\n\n  patchValue( value: { [name: string]: any }, options?: { emitEvent: boolean } ): void {\n    this.form.patchValue(value, options);\n    this.form.markAsPristine();\n    this.form.markAsUntouched();\n  }\n\n  isRemoveIconAvailable(): boolean {\n    return this.iconControl.value && this.iconControl.value !== ' ';\n  }\n\n  get titleControl(): FormControl {\n    return this.form.get('title') as FormControl;\n  }\n\n  get descriptionControl(): FormControl {\n    return this.form.get('description') as FormControl;\n  }\n\n  get iconControl(): FormControl {\n    return this.form.get('icon') as FormControl;\n  }\n\n  get ribbonControl(): FormControl {\n    return this.form.get('ribbon') as FormControl;\n  }\n\n  removeIcon( event: MouseEvent ) {\n    event.preventDefault();\n    event.stopPropagation();\n    this.iconControl.reset();\n    const iconInput = document.getElementById('icon-control') as HTMLInputElement;\n    iconInput.value = '';\n  }\n\n  onIconFileChange( event: any ) {\n    const reader = new FileReader();\n    const target = event.target as any;\n\n    if (target.files && target.files.length) {\n      const [file] = target.files;\n      if (file.type === 'image/svg+xml') {\n        reader.readAsDataURL(file);\n      } else {\n        this.iconControl.setErrors({ requiredFileType: true });\n      }\n\n      reader.onload = () => {\n        this.iconControl.patchValue(reader.result);\n        this.cdr.detectChanges();\n      };\n    }\n  }\n}\n", "<form [formGroup]=\"form\" fxLayout.lt-lg=\"column\" fxLayout=\"row\" fxLayoutGap.gt-md=\"32px\">\n\n  <div fxFlex.lt-md=\"100\" fxLayout=\"column\" fxFlex>\n    <mat-form-field appearance=\"outline\">\n      <mat-label>{{'ALL.title' | translate}}</mat-label>\n      <input matInput trimValue type=\"text\" [formControl]=\"titleControl\">\n      <mat-error>\n        <lib-swui-control-messages [messages]=\"messageErrors\" [control]=\"titleControl\"></lib-swui-control-messages>\n      </mat-error>\n    </mat-form-field>\n\n    <mat-form-field appearance=\"outline\">\n      <mat-label>{{'ALL.description' | translate}}</mat-label>\n      <textarea matInput trimValue class=\"form-control\" rows=\"6\" [formControl]=\"descriptionControl\"></textarea>\n    </mat-form-field>\n    <div *ngIf=\"isRibbonsEnabled\" style=\"margin-bottom: 1.1em;\">\n      <lobby-menu-items-ribbons [formControl]=\"ribbonControl\" [ribbons]=\"defaultRibbons\" [maxLength]=\"9\">\n      </lobby-menu-items-ribbons>\n    </div>\n  </div>\n\n  <div class=\"file-control\" *ngIf=\"isIconEnabled\">\n    <button\n      mat-flat-button\n      color=\"primary\"\n      class=\"file-control__button\">\n      <label class=\"file-control__label\" [ngClass]=\"{'withValue': !!iconControl.value && iconControl.value!==' '}\">\n        {{'GAME_CATEGORIES.chooseIcon' | translate}}\n        <input\n          trimValue\n          [id]=\"'icon-control'\"\n          type=\"file\"\n          accept=\"image/svg+xml\"\n          class=\"file-control__input\"\n          (change)=\"onIconFileChange($event)\">\n        <button mat-icon-button class=\"file-control__clear\" *ngIf=\"isRemoveIconAvailable()\" (click)=\"removeIcon($event)\">\n          <mat-icon>clear</mat-icon>\n        </button>\n      </label>\n    </button>\n    <object\n      *ngIf=\"!!iconControl.value && iconControl.value!==' '\"\n      class=\"file-control__icon\"\n      type=\"image/svg+xml\"\n      [data]=\"iconControl.value | sanitize: 'resourceUrl'\"\n      width=\"150\"\n      height=\"150\">\n    </object>\n  </div>\n\n</form>\n"], "mappings": "AACA,SAA8CA,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;;;;;;;;;ICc5EC,EAAA,CAAAC,cAAA,aAA4D;IAC1DD,EAAA,CAAAE,SAAA,kCAC2B;IAC7BF,EAAA,CAAAG,YAAA,EAAM;;;;IAFsBH,EAAA,CAAAI,SAAA,EAA6B;IAA4BJ,EAAzD,CAAAK,UAAA,gBAAAC,MAAA,CAAAC,aAAA,CAA6B,YAAAD,MAAA,CAAAE,cAAA,CAA2B,gBAAgB;;;;;;IAmBhGR,EAAA,CAAAC,cAAA,iBAAiH;IAA7BD,EAAA,CAAAS,UAAA,mBAAAC,6EAAAC,MAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAC,GAAA;MAAA,MAAAP,MAAA,GAAAN,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAST,MAAA,CAAAU,UAAA,CAAAL,MAAA,CAAkB;IAAA,EAAC;IAC9GX,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAiB,MAAA,YAAK;IACjBjB,EADiB,CAAAG,YAAA,EAAW,EACnB;;;;;IAGbH,EAAA,CAAAE,SAAA,iBAOS;;;;;IAHPF,EAAA,CAAAK,UAAA,SAAAL,EAAA,CAAAkB,WAAA,OAAAZ,MAAA,CAAAa,WAAA,CAAAC,KAAA,kBAAApB,EAAA,CAAAqB,qBAAA,CAAoD;;;;;;IAlBpDrB,EALJ,CAAAC,cAAA,cAAgD,iBAIf,gBACgF;IAC3GD,EAAA,CAAAiB,MAAA,GACA;;IAAAjB,EAAA,CAAAC,cAAA,gBAMsC;IAApCD,EAAA,CAAAS,UAAA,oBAAAa,oEAAAX,MAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAW,GAAA;MAAA,MAAAjB,MAAA,GAAAN,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAUT,MAAA,CAAAkB,gBAAA,CAAAb,MAAA,CAAwB;IAAA,EAAC;IANrCX,EAAA,CAAAG,YAAA,EAMsC;IACtCH,EAAA,CAAAyB,UAAA,IAAAC,oDAAA,qBAAiH;IAIrH1B,EADE,CAAAG,YAAA,EAAQ,EACD;IACTH,EAAA,CAAAyB,UAAA,IAAAE,oDAAA,qBAMe;IAEjB3B,EAAA,CAAAG,YAAA,EAAM;;;;IAtBiCH,EAAA,CAAAI,SAAA,GAAyE;IAAzEJ,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAA4B,eAAA,IAAAC,GAAA,IAAAvB,MAAA,CAAAa,WAAA,CAAAC,KAAA,IAAAd,MAAA,CAAAa,WAAA,CAAAC,KAAA,UAAyE;IAC1GpB,EAAA,CAAAI,SAAA,EACA;IADAJ,EAAA,CAAA8B,kBAAA,MAAA9B,EAAA,CAAA+B,WAAA,0CACA;IAEE/B,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAK,UAAA,sBAAqB;IAK8BL,EAAA,CAAAI,SAAA,EAA6B;IAA7BJ,EAAA,CAAAK,UAAA,SAAAC,MAAA,CAAA0B,qBAAA,GAA6B;IAMnFhC,EAAA,CAAAI,SAAA,EAAoD;IAApDJ,EAAA,CAAAK,UAAA,WAAAC,MAAA,CAAAa,WAAA,CAAAC,KAAA,IAAAd,MAAA,CAAAa,WAAA,CAAAC,KAAA,SAAoD;;;AD7B3D,OAAM,MAAOa,2BAA2B;EA4BtCC,YAAqBC,EAAe,EACNC,GAAsB;IAD/B,KAAAD,EAAE,GAAFA,EAAE;IACO,KAAAC,GAAG,GAAHA,GAAG;IA3BxB,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,gBAAgB,GAAG,KAAK;IAGjC,KAAA9B,cAAc,GAA0B,CACtC;MACE+B,IAAI,EAAE,KAAK;MACXC,EAAE,EAAE,SAAS;MACbC,KAAK,EAAE;KACR,EACD;MACEF,IAAI,EAAE,KAAK;MACXC,EAAE,EAAE,SAAS;MACbC,KAAK,EAAE;KACR,EACD;MACEF,IAAI,EAAE,WAAW;MACjBC,EAAE,EAAE,SAAS;MACbC,KAAK,EAAE;KACR,CACF;IAED,KAAAC,aAAa,GAAiB;MAC5BC,QAAQ,EAAE;KACX;IAIC,IAAI,CAACC,IAAI,GAAG,IAAI,CAACC,QAAQ,EAAE;EAC7B;EAEAA,QAAQA,CAACC,GAAS;IAChB,MAAMC,KAAK,GAAG,IAAI,CAACZ,EAAE,CAACY,KAAK,CAAC;MAC1BC,EAAE,EAAE,CAAC,EAAE,CAAC;MACRC,KAAK,EAAE,CAAC,EAAE,EAAElD,UAAU,CAAC4C,QAAQ,CAAC;MAChCO,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,IAAI,EAAE,CAAC,EAAE,CAAC;MACVC,MAAM,EAAE;KACT,CAAC;IAEF,IAAIN,GAAG,EAAE;MACPC,KAAK,CAACM,UAAU,CAACP,GAAG,CAAC;IACvB;IAEA,OAAOC,KAAK;EACd;EAEAO,QAAQA,CAACR,GAAQ;IACf,IAAIA,GAAG,EAAE;MACP,IAAI,CAACF,IAAI,CAACS,UAAU,CAACP,GAAG,CAAC;IAC3B;EACF;EAEA,IAAIS,YAAYA,CAAA;IACd,OAAO,IAAI,CAACX,IAAI,CAACW,YAAY;EAC/B;EAEA,IAAIC,aAAaA,CAAA;IACf,OAAO,IAAI,CAACZ,IAAI,CAACY,aAAa;EAChC;EAEA,IAAIC,OAAOA,CAAA;IACT,OAAO,IAAI,CAACb,IAAI,CAACa,OAAO;EAC1B;EAEAJ,UAAUA,CAAEjC,KAA8B,EAAEsC,OAAgC;IAC1E,IAAI,CAACd,IAAI,CAACS,UAAU,CAACjC,KAAK,EAAEsC,OAAO,CAAC;IACpC,IAAI,CAACd,IAAI,CAACe,cAAc,EAAE;IAC1B,IAAI,CAACf,IAAI,CAACgB,eAAe,EAAE;EAC7B;EAEA5B,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAACb,WAAW,CAACC,KAAK,IAAI,IAAI,CAACD,WAAW,CAACC,KAAK,KAAK,GAAG;EACjE;EAEA,IAAIyC,YAAYA,CAAA;IACd,OAAO,IAAI,CAACjB,IAAI,CAACkB,GAAG,CAAC,OAAO,CAAgB;EAC9C;EAEA,IAAIC,kBAAkBA,CAAA;IACpB,OAAO,IAAI,CAACnB,IAAI,CAACkB,GAAG,CAAC,aAAa,CAAgB;EACpD;EAEA,IAAI3C,WAAWA,CAAA;IACb,OAAO,IAAI,CAACyB,IAAI,CAACkB,GAAG,CAAC,MAAM,CAAgB;EAC7C;EAEA,IAAIvD,aAAaA,CAAA;IACf,OAAO,IAAI,CAACqC,IAAI,CAACkB,GAAG,CAAC,QAAQ,CAAgB;EAC/C;EAEA9C,UAAUA,CAAEgD,KAAiB;IAC3BA,KAAK,CAACC,cAAc,EAAE;IACtBD,KAAK,CAACE,eAAe,EAAE;IACvB,IAAI,CAAC/C,WAAW,CAACgD,KAAK,EAAE;IACxB,MAAMC,SAAS,GAAGC,QAAQ,CAACC,cAAc,CAAC,cAAc,CAAqB;IAC7EF,SAAS,CAAChD,KAAK,GAAG,EAAE;EACtB;EAEAI,gBAAgBA,CAAEwC,KAAU;IAC1B,MAAMO,MAAM,GAAG,IAAIC,UAAU,EAAE;IAC/B,MAAMC,MAAM,GAAGT,KAAK,CAACS,MAAa;IAElC,IAAIA,MAAM,CAACC,KAAK,IAAID,MAAM,CAACC,KAAK,CAACC,MAAM,EAAE;MACvC,MAAM,CAACC,IAAI,CAAC,GAAGH,MAAM,CAACC,KAAK;MAC3B,IAAIE,IAAI,CAACC,IAAI,KAAK,eAAe,EAAE;QACjCN,MAAM,CAACO,aAAa,CAACF,IAAI,CAAC;MAC5B,CAAC,MAAM;QACL,IAAI,CAACzD,WAAW,CAAC4D,SAAS,CAAC;UAAEC,gBAAgB,EAAE;QAAI,CAAE,CAAC;MACxD;MAEAT,MAAM,CAACU,MAAM,GAAG,MAAK;QACnB,IAAI,CAAC9D,WAAW,CAACkC,UAAU,CAACkB,MAAM,CAACW,MAAM,CAAC;QAC1C,IAAI,CAAC9C,GAAG,CAAC+C,aAAa,EAAE;MAC1B,CAAC;IACH;EACF;;;uCAtHWlD,2BAA2B,EAAAjC,EAAA,CAAAoF,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAtF,EAAA,CAAAoF,iBAAA,CAAApF,EAAA,CAAAuF,iBAAA;IAAA;EAAA;;;YAA3BtD,2BAA2B;MAAAuD,SAAA;MAAAC,MAAA;QAAAC,SAAA;QAAArD,aAAA;QAAAC,gBAAA;MAAA;MAAAqD,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCRlChG,EAJN,CAAAC,cAAA,cAAyF,aAEtC,wBACV,gBACxB;UAAAD,EAAA,CAAAiB,MAAA,GAA2B;;UAAAjB,EAAA,CAAAG,YAAA,EAAY;UAClDH,EAAA,CAAAE,SAAA,eAAmE;UACnEF,EAAA,CAAAC,cAAA,gBAAW;UACTD,EAAA,CAAAE,SAAA,mCAA2G;UAE/GF,EADE,CAAAG,YAAA,EAAY,EACG;UAGfH,EADF,CAAAC,cAAA,wBAAqC,iBACxB;UAAAD,EAAA,CAAAiB,MAAA,IAAiC;;UAAAjB,EAAA,CAAAG,YAAA,EAAY;UACxDH,EAAA,CAAAE,SAAA,mBAAyG;UAC3GF,EAAA,CAAAG,YAAA,EAAiB;UACjBH,EAAA,CAAAyB,UAAA,KAAAyE,2CAAA,iBAA4D;UAI9DlG,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAyB,UAAA,KAAA0E,2CAAA,iBAAgD;UA6BlDnG,EAAA,CAAAG,YAAA,EAAO;;;UAlDDH,EAAA,CAAAK,UAAA,cAAA4F,GAAA,CAAArD,IAAA,CAAkB;UAIP5C,EAAA,CAAAI,SAAA,GAA2B;UAA3BJ,EAAA,CAAAoG,iBAAA,CAAApG,EAAA,CAAA+B,WAAA,oBAA2B;UACA/B,EAAA,CAAAI,SAAA,GAA4B;UAA5BJ,EAAA,CAAAK,UAAA,gBAAA4F,GAAA,CAAApC,YAAA,CAA4B;UAErC7D,EAAA,CAAAI,SAAA,GAA0B;UAACJ,EAA3B,CAAAK,UAAA,aAAA4F,GAAA,CAAAvD,aAAA,CAA0B,YAAAuD,GAAA,CAAApC,YAAA,CAAyB;UAKrE7D,EAAA,CAAAI,SAAA,GAAiC;UAAjCJ,EAAA,CAAAoG,iBAAA,CAAApG,EAAA,CAAA+B,WAAA,4BAAiC;UACe/B,EAAA,CAAAI,SAAA,GAAkC;UAAlCJ,EAAA,CAAAK,UAAA,gBAAA4F,GAAA,CAAAlC,kBAAA,CAAkC;UAEzF/D,EAAA,CAAAI,SAAA,EAAsB;UAAtBJ,EAAA,CAAAK,UAAA,SAAA4F,GAAA,CAAA3D,gBAAA,CAAsB;UAMHtC,EAAA,CAAAI,SAAA,EAAmB;UAAnBJ,EAAA,CAAAK,UAAA,SAAA4F,GAAA,CAAA5D,aAAA,CAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}