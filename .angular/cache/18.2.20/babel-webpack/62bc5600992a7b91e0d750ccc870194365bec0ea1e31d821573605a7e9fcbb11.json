{"ast": null, "code": "export class JurisdictionItem {\n  constructor(data) {\n    this.disabled = false;\n    this.selected = false;\n    this.code = data.code || '';\n    this.title = data.title || '';\n    this.data = data;\n  }\n}", "map": {"version": 3, "names": ["JurisdictionItem", "constructor", "data", "disabled", "selected", "code", "title"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/mat-business-structure/dialogs/mat-jurisdictions-dialog/jurisdiction-item.model.ts"], "sourcesContent": ["import { Jurisdiction } from '../../../../../../common/typings/jurisdiction';\n\nexport class JurisdictionItem {\n  code: string;\n  title: string;\n\n  disabled = false;\n  selected = false;\n\n  data: Jurisdiction;\n\n  constructor( data: Jurisdiction ) {\n    this.code = data.code || '';\n    this.title = data.title || '';\n\n    this.data = data;\n  }\n}\n"], "mappings": "AAEA,OAAM,MAAOA,gBAAgB;EAS3BC,YAAaC,IAAkB;IAL/B,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAAC,QAAQ,GAAG,KAAK;IAKd,IAAI,CAACC,IAAI,GAAGH,IAAI,CAACG,IAAI,IAAI,EAAE;IAC3B,IAAI,CAACC,KAAK,GAAGJ,IAAI,CAACI,KAAK,IAAI,EAAE;IAE7B,IAAI,CAACJ,IAAI,GAAGA,IAAI;EAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}