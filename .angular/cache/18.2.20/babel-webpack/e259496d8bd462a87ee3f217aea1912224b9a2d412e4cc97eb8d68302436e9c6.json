{"ast": null, "code": "'use strict';\n\nconst word = '[a-fA-F\\\\d:]';\nconst b = options => options && options.includeBoundaries ? `(?:(?<=\\\\s|^)(?=${word})|(?<=${word})(?=\\\\s|$))` : '';\nconst v4 = '(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)(?:\\\\.(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)){3}';\nconst v6seg = '[a-fA-F\\\\d]{1,4}';\nconst v6 = `\n(?:\n(?:${v6seg}:){7}(?:${v6seg}|:)|                                    // 1:2:3:4:5:6:7::  1:2:3:4:5:6:7:8\n(?:${v6seg}:){6}(?:${v4}|:${v6seg}|:)|                             // 1:2:3:4:5:6::    1:2:3:4:5:6::8   1:2:3:4:5:6::8  1:2:3:4:5:6::*******\n(?:${v6seg}:){5}(?::${v4}|(?::${v6seg}){1,2}|:)|                   // 1:2:3:4:5::      1:2:3:4:5::7:8   1:2:3:4:5::8    1:2:3:4:5::7:*******\n(?:${v6seg}:){4}(?:(?::${v6seg}){0,1}:${v4}|(?::${v6seg}){1,3}|:)| // 1:2:3:4::        1:2:3:4::6:7:8   1:2:3:4::8      1:2:3:4::6:7:*******\n(?:${v6seg}:){3}(?:(?::${v6seg}){0,2}:${v4}|(?::${v6seg}){1,4}|:)| // 1:2:3::          1:2:3::5:6:7:8   1:2:3::8        1:2:3::5:6:7:*******\n(?:${v6seg}:){2}(?:(?::${v6seg}){0,3}:${v4}|(?::${v6seg}){1,5}|:)| // 1:2::            1:2::4:5:6:7:8   1:2::8          1:2::4:5:6:7:*******\n(?:${v6seg}:){1}(?:(?::${v6seg}){0,4}:${v4}|(?::${v6seg}){1,6}|:)| // 1::              1::3:4:5:6:7:8   1::8            1::3:4:5:6:7:*******\n(?::(?:(?::${v6seg}){0,5}:${v4}|(?::${v6seg}){1,7}|:))             // ::2:3:4:5:6:7:8  ::2:3:4:5:6:7:8  ::8             ::*******\n)(?:%[0-9a-zA-Z]{1,})?                                             // %eth0            %1\n`.replace(/\\s*\\/\\/.*$/gm, '').replace(/\\n/g, '').trim();\n\n// Pre-compile only the exact regexes because adding a global flag make regexes stateful\nconst v46Exact = new RegExp(`(?:^${v4}$)|(?:^${v6}$)`);\nconst v4exact = new RegExp(`^${v4}$`);\nconst v6exact = new RegExp(`^${v6}$`);\nconst ip = options => options && options.exact ? v46Exact : new RegExp(`(?:${b(options)}${v4}${b(options)})|(?:${b(options)}${v6}${b(options)})`, 'g');\nip.v4 = options => options && options.exact ? v4exact : new RegExp(`${b(options)}${v4}${b(options)}`, 'g');\nip.v6 = options => options && options.exact ? v6exact : new RegExp(`${b(options)}${v6}${b(options)}`, 'g');\nmodule.exports = ip;", "map": {"version": 3, "names": ["word", "b", "options", "includeBoundaries", "v4", "v6seg", "v6", "replace", "trim", "v46Exact", "RegExp", "v4exact", "v6exact", "ip", "exact", "module", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/node_modules/ip-regex/index.js"], "sourcesContent": ["'use strict';\n\nconst word = '[a-fA-F\\\\d:]';\nconst b = options => options && options.includeBoundaries ?\n\t`(?:(?<=\\\\s|^)(?=${word})|(?<=${word})(?=\\\\s|$))` :\n\t'';\n\nconst v4 = '(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)(?:\\\\.(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)){3}';\n\nconst v6seg = '[a-fA-F\\\\d]{1,4}';\nconst v6 = `\n(?:\n(?:${v6seg}:){7}(?:${v6seg}|:)|                                    // 1:2:3:4:5:6:7::  1:2:3:4:5:6:7:8\n(?:${v6seg}:){6}(?:${v4}|:${v6seg}|:)|                             // 1:2:3:4:5:6::    1:2:3:4:5:6::8   1:2:3:4:5:6::8  1:2:3:4:5:6::*******\n(?:${v6seg}:){5}(?::${v4}|(?::${v6seg}){1,2}|:)|                   // 1:2:3:4:5::      1:2:3:4:5::7:8   1:2:3:4:5::8    1:2:3:4:5::7:*******\n(?:${v6seg}:){4}(?:(?::${v6seg}){0,1}:${v4}|(?::${v6seg}){1,3}|:)| // 1:2:3:4::        1:2:3:4::6:7:8   1:2:3:4::8      1:2:3:4::6:7:*******\n(?:${v6seg}:){3}(?:(?::${v6seg}){0,2}:${v4}|(?::${v6seg}){1,4}|:)| // 1:2:3::          1:2:3::5:6:7:8   1:2:3::8        1:2:3::5:6:7:*******\n(?:${v6seg}:){2}(?:(?::${v6seg}){0,3}:${v4}|(?::${v6seg}){1,5}|:)| // 1:2::            1:2::4:5:6:7:8   1:2::8          1:2::4:5:6:7:*******\n(?:${v6seg}:){1}(?:(?::${v6seg}){0,4}:${v4}|(?::${v6seg}){1,6}|:)| // 1::              1::3:4:5:6:7:8   1::8            1::3:4:5:6:7:*******\n(?::(?:(?::${v6seg}){0,5}:${v4}|(?::${v6seg}){1,7}|:))             // ::2:3:4:5:6:7:8  ::2:3:4:5:6:7:8  ::8             ::*******\n)(?:%[0-9a-zA-Z]{1,})?                                             // %eth0            %1\n`.replace(/\\s*\\/\\/.*$/gm, '').replace(/\\n/g, '').trim();\n\n// Pre-compile only the exact regexes because adding a global flag make regexes stateful\nconst v46Exact = new RegExp(`(?:^${v4}$)|(?:^${v6}$)`);\nconst v4exact = new RegExp(`^${v4}$`);\nconst v6exact = new RegExp(`^${v6}$`);\n\nconst ip = options => options && options.exact ?\n\tv46Exact :\n\tnew RegExp(`(?:${b(options)}${v4}${b(options)})|(?:${b(options)}${v6}${b(options)})`, 'g');\n\nip.v4 = options => options && options.exact ? v4exact : new RegExp(`${b(options)}${v4}${b(options)}`, 'g');\nip.v6 = options => options && options.exact ? v6exact : new RegExp(`${b(options)}${v6}${b(options)}`, 'g');\n\nmodule.exports = ip;\n"], "mappings": "AAAA,YAAY;;AAEZ,MAAMA,IAAI,GAAG,cAAc;AAC3B,MAAMC,CAAC,GAAGC,OAAO,IAAIA,OAAO,IAAIA,OAAO,CAACC,iBAAiB,GACxD,mBAAmBH,IAAI,SAASA,IAAI,aAAa,GACjD,EAAE;AAEH,MAAMI,EAAE,GAAG,gGAAgG;AAE3G,MAAMC,KAAK,GAAG,kBAAkB;AAChC,MAAMC,EAAE,GAAG;AACX;AACA,KAAKD,KAAK,WAAWA,KAAK;AAC1B,KAAKA,KAAK,WAAWD,EAAE,KAAKC,KAAK;AACjC,KAAKA,KAAK,YAAYD,EAAE,QAAQC,KAAK;AACrC,KAAKA,KAAK,eAAeA,KAAK,UAAUD,EAAE,QAAQC,KAAK;AACvD,KAAKA,KAAK,eAAeA,KAAK,UAAUD,EAAE,QAAQC,KAAK;AACvD,KAAKA,KAAK,eAAeA,KAAK,UAAUD,EAAE,QAAQC,KAAK;AACvD,KAAKA,KAAK,eAAeA,KAAK,UAAUD,EAAE,QAAQC,KAAK;AACvD,aAAaA,KAAK,UAAUD,EAAE,QAAQC,KAAK;AAC3C;AACA,CAAC,CAACE,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACC,IAAI,CAAC,CAAC;;AAEvD;AACA,MAAMC,QAAQ,GAAG,IAAIC,MAAM,CAAC,OAAON,EAAE,UAAUE,EAAE,IAAI,CAAC;AACtD,MAAMK,OAAO,GAAG,IAAID,MAAM,CAAC,IAAIN,EAAE,GAAG,CAAC;AACrC,MAAMQ,OAAO,GAAG,IAAIF,MAAM,CAAC,IAAIJ,EAAE,GAAG,CAAC;AAErC,MAAMO,EAAE,GAAGX,OAAO,IAAIA,OAAO,IAAIA,OAAO,CAACY,KAAK,GAC7CL,QAAQ,GACR,IAAIC,MAAM,CAAC,MAAMT,CAAC,CAACC,OAAO,CAAC,GAAGE,EAAE,GAAGH,CAAC,CAACC,OAAO,CAAC,QAAQD,CAAC,CAACC,OAAO,CAAC,GAAGI,EAAE,GAAGL,CAAC,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;AAE3FW,EAAE,CAACT,EAAE,GAAGF,OAAO,IAAIA,OAAO,IAAIA,OAAO,CAACY,KAAK,GAAGH,OAAO,GAAG,IAAID,MAAM,CAAC,GAAGT,CAAC,CAACC,OAAO,CAAC,GAAGE,EAAE,GAAGH,CAAC,CAACC,OAAO,CAAC,EAAE,EAAE,GAAG,CAAC;AAC1GW,EAAE,CAACP,EAAE,GAAGJ,OAAO,IAAIA,OAAO,IAAIA,OAAO,CAACY,KAAK,GAAGF,OAAO,GAAG,IAAIF,MAAM,CAAC,GAAGT,CAAC,CAACC,OAAO,CAAC,GAAGI,EAAE,GAAGL,CAAC,CAACC,OAAO,CAAC,EAAE,EAAE,GAAG,CAAC;AAE1Ga,MAAM,CAACC,OAAO,GAAGH,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}