{"ast": null, "code": "import { FormControl } from '@angular/forms';\nimport { MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport { Subject } from 'rxjs';\nimport { finalize, take, takeUntil } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"../../../../../domains-management/domains-management.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/progress-spinner\";\nimport * as i7 from \"@angular/material/form-field\";\nimport * as i8 from \"@skywind-group/lib-swui\";\nimport * as i9 from \"@ngx-translate/core\";\nconst _c0 = a0 => ({\n  type: a0\n});\nfunction SelectDomainDialogComponent_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"mat-form-field\", 7)(2, \"mat-label\");\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"lib-swui-select\", 8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(4, 4, \"ENTITY_SETUP.DOMAINS.domain\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"data\", ctx_r1.selectOptions)(\"formControl\", ctx_r1.domainControl)(\"showSearch\", true);\n  }\n}\nfunction SelectDomainDialogComponent_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵelement(1, \"mat-spinner\", 10);\n    i0.ɵɵelementEnd();\n  }\n}\nexport class SelectDomainDialogComponent {\n  constructor(dialogRef, {\n    domainType,\n    staticDomainType,\n    domainId\n  }, service) {\n    this.dialogRef = dialogRef;\n    this.service = service;\n    this.selectOptions = [];\n    this.loading = true;\n    this.destroyed$ = new Subject();\n    this.domainType = domainType;\n    this.staticDomainType = staticDomainType;\n    this.domainControl = new FormControl(domainId ?? '');\n    this.domainId = domainId;\n  }\n  ngOnInit() {\n    this.service.getList(this.domainType).pipe(finalize(() => this.loading = false), take(1)).subscribe(domains => {\n      if (this.staticDomainType) {\n        domains = domains.filter(({\n          type\n        }) => type === this.staticDomainType);\n      } else {\n        domains = domains;\n      }\n      this.selectOptions = domains.map(({\n        id,\n        domain,\n        environment\n      }) => ({\n        id,\n        text: `${domain}${environment ? ` (${environment})` : ''}`,\n        disabled: id === this.domainId\n      }));\n      this.domainControl.valueChanges.pipe(takeUntil(this.destroyed$)).subscribe(id => {\n        this.selectedDomain = domains.find(domain => domain.id === id);\n      });\n    });\n  }\n  ngOnDestroy() {\n    this.destroyed$.next();\n    this.destroyed$.complete();\n  }\n  submitSelectedDomain() {\n    if (this.selectedDomain) {\n      this.dialogRef.close(this.selectedDomain);\n    }\n  }\n  static {\n    this.ɵfac = function SelectDomainDialogComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SelectDomainDialogComponent)(i0.ɵɵdirectiveInject(i1.MatDialogRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA), i0.ɵɵdirectiveInject(i2.DomainsManagementService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SelectDomainDialogComponent,\n      selectors: [[\"select-domain-dialog\"]],\n      decls: 14,\n      vars: 17,\n      consts: [[\"loadingSpinner\", \"\"], [\"mat-dialog-title\", \"\"], [1, \"mat-typography\"], [4, \"ngIf\", \"ngIfElse\"], [\"align\", \"end\"], [\"mat-button\", \"\", \"color\", \"primary\", \"mat-dialog-close\", \"\", 1, \"mat-button-md\"], [\"mat-flat-button\", \"\", \"color\", \"primary\", \"cdkFocusInitial\", \"\", 1, \"mat-button-md\", 3, \"click\", \"disabled\"], [\"appearance\", \"outline\", 2, \"width\", \"100%\"], [3, \"data\", \"formControl\", \"showSearch\"], [1, \"loading-container\", 2, \"display\", \"flex\", \"justify-content\", \"center\", \"align-items\", \"center\", \"min-height\", \"100px\"], [\"diameter\", \"40\"]],\n      template: function SelectDomainDialogComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"h2\", 1);\n          i0.ɵɵtext(1);\n          i0.ɵɵpipe(2, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"mat-dialog-content\", 2);\n          i0.ɵɵtemplate(4, SelectDomainDialogComponent_ng_container_4_Template, 6, 6, \"ng-container\", 3)(5, SelectDomainDialogComponent_ng_template_5_Template, 2, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"mat-dialog-actions\", 4)(8, \"button\", 5);\n          i0.ɵɵtext(9);\n          i0.ɵɵpipe(10, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function SelectDomainDialogComponent_Template_button_click_11_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.submitSelectedDomain());\n          });\n          i0.ɵɵtext(12);\n          i0.ɵɵpipe(13, \"translate\");\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          const loadingSpinner_r3 = i0.ɵɵreference(6);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 8, \"ENTITY_SETUP.DOMAINS.modalTitle\", i0.ɵɵpureFunction1(15, _c0, ctx.domainType)), \"\\n\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading)(\"ngIfElse\", loadingSpinner_r3);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(10, 11, \"DIALOG.cancel\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"disabled\", !ctx.selectedDomain);\n          i0.ɵɵproperty(\"disabled\", !ctx.selectedDomain);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(13, 13, \"DIALOG.save\"), \" \");\n        }\n      },\n      dependencies: [i3.NgIf, i4.NgControlStatus, i4.FormControlDirective, i1.MatDialogClose, i1.MatDialogTitle, i1.MatDialogActions, i1.MatDialogContent, i5.MatButton, i6.MatProgressSpinner, i7.MatFormField, i7.MatLabel, i8.SwuiSelectComponent, i9.TranslatePipe],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["FormControl", "MAT_DIALOG_DATA", "Subject", "finalize", "take", "takeUntil", "i0", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpipeBind1", "ɵɵproperty", "ctx_r1", "selectOptions", "domainControl", "SelectDomainDialogComponent", "constructor", "dialogRef", "domainType", "staticDomainType", "domainId", "service", "loading", "destroyed$", "ngOnInit", "getList", "pipe", "subscribe", "domains", "filter", "type", "map", "id", "domain", "environment", "text", "disabled", "valueChanges", "<PERSON><PERSON><PERSON><PERSON>", "find", "ngOnDestroy", "next", "complete", "submitSelectedDomain", "close", "ɵɵdirectiveInject", "i1", "MatDialogRef", "i2", "DomainsManagementService", "selectors", "decls", "vars", "consts", "template", "SelectDomainDialogComponent_Template", "rf", "ctx", "ɵɵtemplate", "SelectDomainDialogComponent_ng_container_4_Template", "SelectDomainDialogComponent_ng_template_5_Template", "ɵɵtemplateRefExtractor", "ɵɵlistener", "SelectDomainDialogComponent_Template_button_click_11_listener", "ɵɵrestoreView", "_r1", "ɵɵresetView", "ɵɵtextInterpolate1", "ɵɵpipeBind2", "ɵɵpureFunction1", "_c0", "loadingSpinner_r3", "ɵɵclassProp"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-domains/manage-domains/select-domain-dialog.component.ts", "/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-domains/manage-domains/select-domain-dialog.component.html"], "sourcesContent": ["import { Component, Inject, On<PERSON><PERSON>roy, OnInit } from '@angular/core';\nimport { FormControl } from '@angular/forms';\nimport { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';\nimport { SwuiSelectOption } from '@skywind-group/lib-swui';\nimport { Subject } from 'rxjs';\nimport { finalize, take, takeUntil } from 'rxjs/operators';\nimport { DomainsManagementService } from '../../../../../domains-management/domains-management.service';\nimport { Domain, DomainType, StaticDomainType } from '../../../../../../common/models/domain.model';\n\nexport interface SelectDomainDialogData {\n  domainType: DomainType;\n  staticDomainType?: StaticDomainType;\n  domainId: string;\n}\n\n@Component({\n  selector: 'select-domain-dialog',\n  templateUrl: 'select-domain-dialog.component.html'\n})\nexport class SelectDomainDialogComponent implements OnInit, <PERSON><PERSON><PERSON><PERSON> {\n  readonly domainControl: FormControl;\n  readonly domainType: DomainType;\n\n  selectOptions: SwuiSelectOption[] = [];\n  selectedDomain: Domain;\n  loading = true;\n\n  private readonly staticDomainType?: StaticDomainType;\n  private readonly domainId: string;\n  private readonly destroyed$ = new Subject<void>();\n\n  constructor(\n    private readonly dialogRef: MatDialogRef<SelectDomainDialogComponent, Domain>,\n    @Inject(MAT_DIALOG_DATA) { domainType, staticDomainType, domainId }: SelectDomainDialogData,\n    private readonly service: DomainsManagementService,\n  ) {\n    this.domainType = domainType;\n    this.staticDomainType = staticDomainType;\n    this.domainControl = new FormControl(domainId ?? '');\n    this.domainId = domainId;\n  }\n\n  ngOnInit() {\n    this.service.getList(this.domainType).pipe(\n      finalize(() => this.loading = false),\n      take(1)\n    ).subscribe((domains) => {\n      if (this.staticDomainType) {\n        domains = domains.filter(({ type }) => type === this.staticDomainType);\n      } else {\n        domains = domains;\n      }\n\n      this.selectOptions = domains.map(({ id, domain, environment }) => ({\n        id,\n        text: `${domain}${environment ? ` (${environment})` : ''}`,\n        disabled: id === this.domainId\n      }));\n\n      this.domainControl.valueChanges.pipe(\n        takeUntil(this.destroyed$)\n      ).subscribe((id) => {\n        this.selectedDomain = domains.find(domain => domain.id === id);\n      });\n    });\n  }\n\n  ngOnDestroy(): void {\n    this.destroyed$.next();\n    this.destroyed$.complete();\n  }\n\n  submitSelectedDomain() {\n    if (this.selectedDomain) {\n      this.dialogRef.close(this.selectedDomain);\n    }\n  }\n}\n", "<h2 mat-dialog-title>\n  {{ 'ENTITY_SETUP.DOMAINS.modalTitle' | translate: { type: domainType } }}\n</h2>\n<mat-dialog-content class=\"mat-typography\">\n  <ng-container *ngIf=\"!loading; else loadingSpinner\">\n    <mat-form-field appearance=\"outline\" style=\"width: 100%\">\n      <mat-label>{{'ENTITY_SETUP.DOMAINS.domain' | translate}}</mat-label>\n      <lib-swui-select [data]=\"selectOptions\" [formControl]=\"domainControl\" [showSearch]=\"true\"></lib-swui-select>\n    </mat-form-field>\n  </ng-container>\n  <ng-template #loadingSpinner>\n    <div class=\"loading-container\"\n      style=\"display: flex; justify-content: center; align-items: center; min-height: 100px;\">\n      <mat-spinner diameter=\"40\"></mat-spinner>\n    </div>\n  </ng-template>\n</mat-dialog-content>\n<mat-dialog-actions align=\"end\">\n  <button mat-button color=\"primary\" class=\"mat-button-md\" mat-dialog-close>\n    {{'DIALOG.cancel' | translate}}\n  </button>\n  <button mat-flat-button color=\"primary\" class=\"mat-button-md\" cdkFocusInitial [class.disabled]=\"!selectedDomain\"\n    [disabled]=\"!selectedDomain\" (click)=\"submitSelectedDomain()\">\n    {{'DIALOG.save' | translate}}\n  </button>\n</mat-dialog-actions>\n"], "mappings": "AACA,SAASA,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,eAAe,QAAsB,0BAA0B;AAExE,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,QAAQ,EAAEC,IAAI,EAAEC,SAAS,QAAQ,gBAAgB;;;;;;;;;;;;;;;;ICDxDC,EAAA,CAAAC,uBAAA,GAAoD;IAEhDD,EADF,CAAAE,cAAA,wBAAyD,gBAC5C;IAAAF,EAAA,CAAAG,MAAA,GAA6C;;IAAAH,EAAA,CAAAI,YAAA,EAAY;IACpEJ,EAAA,CAAAK,SAAA,yBAA4G;IAC9GL,EAAA,CAAAI,YAAA,EAAiB;;;;;IAFJJ,EAAA,CAAAM,SAAA,GAA6C;IAA7CN,EAAA,CAAAO,iBAAA,CAAAP,EAAA,CAAAQ,WAAA,sCAA6C;IACvCR,EAAA,CAAAM,SAAA,GAAsB;IAA+BN,EAArD,CAAAS,UAAA,SAAAC,MAAA,CAAAC,aAAA,CAAsB,gBAAAD,MAAA,CAAAE,aAAA,CAA8B,oBAAoB;;;;;IAI3FZ,EAAA,CAAAE,cAAA,aAC0F;IACxFF,EAAA,CAAAK,SAAA,sBAAyC;IAC3CL,EAAA,CAAAI,YAAA,EAAM;;;ADKV,OAAM,MAAOS,2BAA2B;EAYtCC,YACmBC,SAA4D,EACpD;IAAEC,UAAU;IAAEC,gBAAgB;IAAEC;EAAQ,CAA0B,EAC1EC,OAAiC;IAFjC,KAAAJ,SAAS,GAATA,SAAS;IAET,KAAAI,OAAO,GAAPA,OAAO;IAX1B,KAAAR,aAAa,GAAuB,EAAE;IAEtC,KAAAS,OAAO,GAAG,IAAI;IAIG,KAAAC,UAAU,GAAG,IAAIzB,OAAO,EAAQ;IAO/C,IAAI,CAACoB,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACL,aAAa,GAAG,IAAIlB,WAAW,CAACwB,QAAQ,IAAI,EAAE,CAAC;IACpD,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC1B;EAEAI,QAAQA,CAAA;IACN,IAAI,CAACH,OAAO,CAACI,OAAO,CAAC,IAAI,CAACP,UAAU,CAAC,CAACQ,IAAI,CACxC3B,QAAQ,CAAC,MAAM,IAAI,CAACuB,OAAO,GAAG,KAAK,CAAC,EACpCtB,IAAI,CAAC,CAAC,CAAC,CACR,CAAC2B,SAAS,CAAEC,OAAO,IAAI;MACtB,IAAI,IAAI,CAACT,gBAAgB,EAAE;QACzBS,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,CAAC;UAAEC;QAAI,CAAE,KAAKA,IAAI,KAAK,IAAI,CAACX,gBAAgB,CAAC;MACxE,CAAC,MAAM;QACLS,OAAO,GAAGA,OAAO;MACnB;MAEA,IAAI,CAACf,aAAa,GAAGe,OAAO,CAACG,GAAG,CAAC,CAAC;QAAEC,EAAE;QAAEC,MAAM;QAAEC;MAAW,CAAE,MAAM;QACjEF,EAAE;QACFG,IAAI,EAAE,GAAGF,MAAM,GAAGC,WAAW,GAAG,KAAKA,WAAW,GAAG,GAAG,EAAE,EAAE;QAC1DE,QAAQ,EAAEJ,EAAE,KAAK,IAAI,CAACZ;OACvB,CAAC,CAAC;MAEH,IAAI,CAACN,aAAa,CAACuB,YAAY,CAACX,IAAI,CAClCzB,SAAS,CAAC,IAAI,CAACsB,UAAU,CAAC,CAC3B,CAACI,SAAS,CAAEK,EAAE,IAAI;QACjB,IAAI,CAACM,cAAc,GAAGV,OAAO,CAACW,IAAI,CAACN,MAAM,IAAIA,MAAM,CAACD,EAAE,KAAKA,EAAE,CAAC;MAChE,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEAQ,WAAWA,CAAA;IACT,IAAI,CAACjB,UAAU,CAACkB,IAAI,EAAE;IACtB,IAAI,CAAClB,UAAU,CAACmB,QAAQ,EAAE;EAC5B;EAEAC,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAACL,cAAc,EAAE;MACvB,IAAI,CAACrB,SAAS,CAAC2B,KAAK,CAAC,IAAI,CAACN,cAAc,CAAC;IAC3C;EACF;;;uCAzDWvB,2BAA2B,EAAAb,EAAA,CAAA2C,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAA7C,EAAA,CAAA2C,iBAAA,CAc5BhD,eAAe,GAAAK,EAAA,CAAA2C,iBAAA,CAAAG,EAAA,CAAAC,wBAAA;IAAA;EAAA;;;YAddlC,2BAA2B;MAAAmC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCnBxCtD,EAAA,CAAAE,cAAA,YAAqB;UACnBF,EAAA,CAAAG,MAAA,GACF;;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAE,cAAA,4BAA2C;UAOzCF,EANA,CAAAwD,UAAA,IAAAC,mDAAA,0BAAoD,IAAAC,kDAAA,gCAAA1D,EAAA,CAAA2D,sBAAA,CAMvB;UAM/B3D,EAAA,CAAAI,YAAA,EAAqB;UAEnBJ,EADF,CAAAE,cAAA,4BAAgC,gBAC4C;UACxEF,EAAA,CAAAG,MAAA,GACF;;UAAAH,EAAA,CAAAI,YAAA,EAAS;UACTJ,EAAA,CAAAE,cAAA,iBACgE;UAAjCF,EAAA,CAAA4D,UAAA,mBAAAC,8DAAA;YAAA7D,EAAA,CAAA8D,aAAA,CAAAC,GAAA;YAAA,OAAA/D,EAAA,CAAAgE,WAAA,CAAST,GAAA,CAAAd,oBAAA,EAAsB;UAAA,EAAC;UAC7DzC,EAAA,CAAAG,MAAA,IACF;;UACFH,EADE,CAAAI,YAAA,EAAS,EACU;;;;UAxBnBJ,EAAA,CAAAM,SAAA,EACF;UADEN,EAAA,CAAAiE,kBAAA,MAAAjE,EAAA,CAAAkE,WAAA,0CAAAlE,EAAA,CAAAmE,eAAA,KAAAC,GAAA,EAAAb,GAAA,CAAAvC,UAAA,SACF;UAEiBhB,EAAA,CAAAM,SAAA,GAAgB;UAAAN,EAAhB,CAAAS,UAAA,UAAA8C,GAAA,CAAAnC,OAAA,CAAgB,aAAAiD,iBAAA,CAAmB;UAehDrE,EAAA,CAAAM,SAAA,GACF;UADEN,EAAA,CAAAiE,kBAAA,MAAAjE,EAAA,CAAAQ,WAAA,+BACF;UAC8ER,EAAA,CAAAM,SAAA,GAAkC;UAAlCN,EAAA,CAAAsE,WAAA,cAAAf,GAAA,CAAAnB,cAAA,CAAkC;UAC9GpC,EAAA,CAAAS,UAAA,cAAA8C,GAAA,CAAAnB,cAAA,CAA4B;UAC5BpC,EAAA,CAAAM,SAAA,EACF;UADEN,EAAA,CAAAiE,kBAAA,MAAAjE,EAAA,CAAAQ,WAAA,6BACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}