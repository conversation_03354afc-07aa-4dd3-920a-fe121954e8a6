{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FlexLayoutModule } from '@angular/flex-layout';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiGridModule, SwuiSelectModule } from '@skywind-group/lib-swui';\nimport { SwuiControlMessagesModule } from '@skywind-group/lib-swui';\nimport { ControlMessagesModule } from '../../../../../common/components/control-messages/control-messages.module';\nimport { BaIfAllowedModule } from '../../../../../common/directives/baIfAllowed/baIfAllowed.module';\nimport { TrimInputValueModule } from '../../../../../common/directives/trim-input-value/trim-input-value.module';\nimport { PipesModule } from '../../../../../common/pipes/pipes.module';\nimport { GameGroupService } from '../../../../../common/services/game-group.service';\nimport { GameGroupDeleteDialogComponent } from './game-group-delete-dialog/game-group-delete-dialog.component';\nimport { GameGroupDialogComponent } from './game-group-dialog/game-group-dialog.component';\nimport { GameGroupComponent } from './game-group.component';\nimport { TabGameGroupComponent } from './tab-game-group.component';\nimport { TabGameGroupRoutingModule } from './tab-game-group.routing';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nexport class TabGameGroupModule {\n  static {\n    this.ɵfac = function TabGameGroupModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TabGameGroupModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: TabGameGroupModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [GameGroupService],\n      imports: [CommonModule, TranslateModule.forChild(), ReactiveFormsModule, FormsModule, TabGameGroupRoutingModule, ControlMessagesModule, BaIfAllowedModule, PipesModule, MatButtonModule, MatFormFieldModule, MatIconModule, MatTooltipModule, SwuiSelectModule, MatDialogModule, MatInputModule, MatCheckboxModule, FlexLayoutModule, SwuiControlMessagesModule, SwuiGridModule, TrimInputValueModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(TabGameGroupModule, {\n    declarations: [TabGameGroupComponent, GameGroupComponent, GameGroupDialogComponent, GameGroupDeleteDialogComponent],\n    imports: [CommonModule, i1.TranslateModule, ReactiveFormsModule, FormsModule, TabGameGroupRoutingModule, ControlMessagesModule, BaIfAllowedModule, PipesModule, MatButtonModule, MatFormFieldModule, MatIconModule, MatTooltipModule, SwuiSelectModule, MatDialogModule, MatInputModule, MatCheckboxModule, FlexLayoutModule, SwuiControlMessagesModule, SwuiGridModule, TrimInputValueModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FlexLayoutModule", "FormsModule", "ReactiveFormsModule", "MatButtonModule", "MatCheckboxModule", "MatDialogModule", "MatFormFieldModule", "MatIconModule", "MatInputModule", "MatTooltipModule", "TranslateModule", "SwuiGridModule", "SwuiSelectModule", "SwuiControlMessagesModule", "ControlMessagesModule", "BaIfAllowedModule", "TrimInputValueModule", "PipesModule", "GameGroupService", "GameGroupDeleteDialogComponent", "GameGroupDialogComponent", "GameGroupComponent", "TabGameGroupComponent", "TabGameGroupRoutingModule", "TabGameGroupModule", "imports", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "i1"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-game-group/tab-game-group.module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { FlexLayoutModule } from '@angular/flex-layout';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiGridModule, SwuiSelectModule } from '@skywind-group/lib-swui';\nimport { SwuiControlMessagesModule } from '@skywind-group/lib-swui';\n\nimport { ControlMessagesModule } from '../../../../../common/components/control-messages/control-messages.module';\nimport { BaIfAllowedModule } from '../../../../../common/directives/baIfAllowed/baIfAllowed.module';\nimport { TrimInputValueModule } from '../../../../../common/directives/trim-input-value/trim-input-value.module';\nimport { PipesModule } from '../../../../../common/pipes/pipes.module';\nimport { GameGroupService } from '../../../../../common/services/game-group.service';\nimport { GameGroupDeleteDialogComponent } from './game-group-delete-dialog/game-group-delete-dialog.component';\nimport { GameGroupDialogComponent } from './game-group-dialog/game-group-dialog.component';\nimport { GameGroupComponent } from './game-group.component';\nimport { TabGameGroupComponent } from './tab-game-group.component';\nimport { TabGameGroupRoutingModule } from './tab-game-group.routing';\n\n\n@NgModule({\n    imports: [\n        CommonModule,\n        TranslateModule.forChild(),\n        ReactiveFormsModule,\n        FormsModule,\n        TabGameGroupRoutingModule,\n        ControlMessagesModule,\n        BaIfAllowedModule,\n        PipesModule,\n\n        MatButtonModule,\n        MatFormFieldModule,\n        MatIconModule,\n        MatTooltipModule,\n        SwuiSelectModule,\n        MatDialogModule,\n        MatInputModule,\n        MatCheckboxModule,\n        FlexLayoutModule,\n        SwuiControlMessagesModule,\n        SwuiGridModule,\n        TrimInputValueModule,\n    ],\n  exports: [],\n  declarations: [\n    TabGameGroupComponent,\n    GameGroupComponent,\n    GameGroupDialogComponent,\n    GameGroupDeleteDialogComponent,\n  ],\n  providers: [\n    GameGroupService,\n  ],\n})\nexport class TabGameGroupModule {\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,cAAc,EAAEC,gBAAgB,QAAQ,yBAAyB;AAC1E,SAASC,yBAAyB,QAAQ,yBAAyB;AAEnE,SAASC,qBAAqB,QAAQ,2EAA2E;AACjH,SAASC,iBAAiB,QAAQ,iEAAiE;AACnG,SAASC,oBAAoB,QAAQ,2EAA2E;AAChH,SAASC,WAAW,QAAQ,0CAA0C;AACtE,SAASC,gBAAgB,QAAQ,mDAAmD;AACpF,SAASC,8BAA8B,QAAQ,+DAA+D;AAC9G,SAASC,wBAAwB,QAAQ,iDAAiD;AAC1F,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,SAASC,qBAAqB,QAAQ,4BAA4B;AAClE,SAASC,yBAAyB,QAAQ,0BAA0B;;;AAsCpE,OAAM,MAAOC,kBAAkB;;;uCAAlBA,kBAAkB;IAAA;EAAA;;;YAAlBA;IAAkB;EAAA;;;iBAJlB,CACTN,gBAAgB,CACjB;MAAAO,OAAA,GA/BK1B,YAAY,EACZW,eAAe,CAACgB,QAAQ,EAAE,EAC1BxB,mBAAmB,EACnBD,WAAW,EACXsB,yBAAyB,EACzBT,qBAAqB,EACrBC,iBAAiB,EACjBE,WAAW,EAEXd,eAAe,EACfG,kBAAkB,EAClBC,aAAa,EACbE,gBAAgB,EAChBG,gBAAgB,EAChBP,eAAe,EACfG,cAAc,EACdJ,iBAAiB,EACjBJ,gBAAgB,EAChBa,yBAAyB,EACzBF,cAAc,EACdK,oBAAoB;IAAA;EAAA;;;2EAafQ,kBAAkB;IAAAG,YAAA,GAT3BL,qBAAqB,EACrBD,kBAAkB,EAClBD,wBAAwB,EACxBD,8BAA8B;IAAAM,OAAA,GA3B1B1B,YAAY,EAAA6B,EAAA,CAAAlB,eAAA,EAEZR,mBAAmB,EACnBD,WAAW,EACXsB,yBAAyB,EACzBT,qBAAqB,EACrBC,iBAAiB,EACjBE,WAAW,EAEXd,eAAe,EACfG,kBAAkB,EAClBC,aAAa,EACbE,gBAAgB,EAChBG,gBAAgB,EAChBP,eAAe,EACfG,cAAc,EACdJ,iBAAiB,EACjBJ,gBAAgB,EAChBa,yBAAyB,EACzBF,cAAc,EACdK,oBAAoB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}