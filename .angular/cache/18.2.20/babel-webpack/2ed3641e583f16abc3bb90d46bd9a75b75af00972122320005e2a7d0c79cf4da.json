{"ast": null, "code": "import { providerCodeClassMap, tagClassMap } from '../../../../../../../app.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@ngx-translate/core\";\nfunction SetupGameInfoComponent_div_0_span_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 8);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const label_r1 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getLabelClass(label_r1));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(label_r1.title);\n  }\n}\nfunction SetupGameInfoComponent_div_0_span_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 9);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"ENTITY_SETUP.GAMES.MODALS.noLabels\"), \"\");\n  }\n}\nfunction SetupGameInfoComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"table\", 3)(3, \"tbody\")(4, \"tr\")(5, \"td\", 4);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"tr\")(11, \"td\", 4);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\");\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"tr\")(17, \"td\", 4);\n    i0.ɵɵtext(18);\n    i0.ɵɵpipe(19, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"td\")(21, \"span\", 5);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(23, \"tr\")(24, \"td\", 4);\n    i0.ɵɵtext(25);\n    i0.ɵɵpipe(26, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"td\");\n    i0.ɵɵtemplate(28, SetupGameInfoComponent_div_0_span_28_Template, 2, 2, \"span\", 6)(29, SetupGameInfoComponent_div_0_span_29_Template, 3, 3, \"span\", 7);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(7, 10, \"ENTITY_SETUP.GAMES.title\"), \":\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.game.title);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(13, 12, \"ENTITY_SETUP.GAMES.code\"), \":\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.game.code);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(19, 14, \"ENTITY_SETUP.GAMES.providerTitle\"), \":\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getProviderClass(ctx_r1.game.providerCode));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.game.providerTitle);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(26, 16, \"ENTITY_SETUP.GAMES.labels\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.game.labels);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.game.labels.length);\n  }\n}\nexport class SetupGameInfoComponent {\n  set game(value) {\n    this._game = value;\n  }\n  get game() {\n    return this._game;\n  }\n  constructor() {}\n  getProviderClass(providerCode) {\n    let cssClass = providerCodeClassMap.DEFAULT;\n    if (providerCodeClassMap.hasOwnProperty(providerCode)) {\n      cssClass = providerCodeClassMap[providerCode];\n    }\n    return cssClass;\n  }\n  getLabelClass(label) {\n    let cssClass = 'border-left-grey';\n    if (tagClassMap.hasOwnProperty(label.group)) {\n      cssClass = tagClassMap[label.group];\n    }\n    return cssClass;\n  }\n  static {\n    this.ɵfac = function SetupGameInfoComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SetupGameInfoComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SetupGameInfoComponent,\n      selectors: [[\"setup-game-info\"]],\n      inputs: {\n        game: \"game\"\n      },\n      decls: 1,\n      vars: 1,\n      consts: [[\"class\", \"panel\", 4, \"ngIf\"], [1, \"panel\"], [1, \"panel-heading\", \"no-padding-left\", \"no-padding-right\"], [1, \"table\", \"table-xxs\", \"table-borderless\", \"table-game-info\"], [1, \"text-semibold\"], [3, \"ngClass\"], [\"class\", \"label label-striped label-xs mr-5\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"label label-info\", 4, \"ngIf\"], [1, \"label\", \"label-striped\", \"label-xs\", \"mr-5\", 3, \"ngClass\"], [1, \"label\", \"label-info\"]],\n      template: function SetupGameInfoComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, SetupGameInfoComponent_div_0_Template, 30, 18, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.game);\n        }\n      },\n      dependencies: [i1.NgClass, i1.NgForOf, i1.NgIf, i2.TranslatePipe],\n      styles: [\".table-game-info[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:first-child {\\n  width: 100px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvYnVzaW5lc3MtbWFuYWdlbWVudC9jb21wb25lbnRzL2VudGl0aWVzL3RhYi1nYW1lcy9tYW5hZ2UtZ2FtZXMvc2V0dXAtZ2FtZS1pbmZvL3NldHVwLWdhbWUtaW5mby5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFFSTtFQUNFLFlBQUE7QUFETiIsInNvdXJjZXNDb250ZW50IjpbIi50YWJsZS1nYW1lLWluZm8ge1xuICB0ZCB7XG4gICAgJjpmaXJzdC1jaGlsZCB7XG4gICAgICB3aWR0aDogMTAwcHhcbiAgICB9XG4gIH1cbn1cbiJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["providerCodeClassMap", "tagClassMap", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "ctx_r1", "getLabelClass", "label_r1", "ɵɵadvance", "ɵɵtextInterpolate", "title", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "ɵɵtemplate", "SetupGameInfoComponent_div_0_span_28_Template", "SetupGameInfoComponent_div_0_span_29_Template", "game", "code", "getProviderClass", "providerCode", "providerTitle", "labels", "length", "SetupGameInfoComponent", "value", "_game", "constructor", "cssClass", "DEFAULT", "hasOwnProperty", "label", "group", "selectors", "inputs", "decls", "vars", "consts", "template", "SetupGameInfoComponent_Template", "rf", "ctx", "SetupGameInfoComponent_div_0_Template"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-games/manage-games/setup-game-info/setup-game-info.component.ts", "/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-games/manage-games/setup-game-info/setup-game-info.component.html"], "sourcesContent": ["import { Component, Input } from '@angular/core';\n\nimport { providerCodeClassMap, tagClassMap } from '../../../../../../../app.constants';\nimport { Game, GameLabel } from '../../../../../../../common/typings';\n\n\n@Component({\n  selector: 'setup-game-info',\n  templateUrl: './setup-game-info.component.html',\n  styleUrls: ['./setup-game-info.component.scss'],\n})\n\nexport class SetupGameInfoComponent {\n\n  private _game: Game;\n\n  @Input()\n  set game( value: Game ) {\n    this._game = value;\n  }\n\n  get game(): Game {\n    return this._game;\n  }\n\n  constructor() {\n\n  }\n\n\n  public getProviderClass( providerCode: string ): string {\n    let cssClass = providerCodeClassMap.DEFAULT;\n\n    if (providerCodeClassMap.hasOwnProperty(providerCode)) {\n      cssClass = providerCodeClassMap[providerCode];\n    }\n\n    return cssClass;\n  }\n\n  public getLabelClass( label: GameLabel ): string {\n    let cssClass = 'border-left-grey';\n\n    if (tagClassMap.hasOwnProperty(label.group)) {\n      cssClass = tagClassMap[label.group];\n    }\n\n    return cssClass;\n  }\n}\n", "<div class=\"panel\" *ngIf=\"game\">\n  <div class=\"panel-heading no-padding-left no-padding-right\">\n    <table class=\"table table-xxs table-borderless table-game-info\">\n      <tbody>\n      <tr>\n        <td class=\"text-semibold\">{{'ENTITY_SETUP.GAMES.title' | translate}}:</td>\n        <td>{{ game.title }}</td>\n      </tr>\n      <tr>\n        <td class=\"text-semibold\">{{'ENTITY_SETUP.GAMES.code' | translate}}:</td>\n        <td>{{ game.code }}</td>\n      </tr>\n      <tr>\n        <td class=\"text-semibold\">{{'ENTITY_SETUP.GAMES.providerTitle' | translate}}:</td>\n        <td>\n          <span [ngClass]=\"getProviderClass(game.providerCode)\">{{ game.providerTitle }}</span>\n        </td>\n      </tr>\n      <tr>\n        <td class=\"text-semibold\">{{'ENTITY_SETUP.GAMES.labels' | translate}}</td>\n        <td>\n          <span class=\"label label-striped label-xs mr-5\" *ngFor=\"let label of game.labels\"\n                [ngClass]=\"getLabelClass(label)\">{{ label.title }}</span>\n          <span *ngIf=\"!game.labels.length\" class=\"label label-info\">\n            {{'ENTITY_SETUP.GAMES.MODALS.noLabels' | translate}}</span>\n        </td>\n      </tr>\n      </tbody>\n    </table>\n  </div>\n</div>\n"], "mappings": "AAEA,SAASA,oBAAoB,EAAEC,WAAW,QAAQ,oCAAoC;;;;;;ICmB5EC,EAAA,CAAAC,cAAA,cACuC;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAAzDH,EAAA,CAAAI,UAAA,YAAAC,MAAA,CAAAC,aAAA,CAAAC,QAAA,EAAgC;IAACP,EAAA,CAAAQ,SAAA,EAAiB;IAAjBR,EAAA,CAAAS,iBAAA,CAAAF,QAAA,CAAAG,KAAA,CAAiB;;;;;IACxDV,EAAA,CAAAC,cAAA,cAA2D;IACzDD,EAAA,CAAAE,MAAA,GAAoD;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;IAA3DH,EAAA,CAAAQ,SAAA,EAAoD;IAApDR,EAAA,CAAAW,kBAAA,MAAAX,EAAA,CAAAY,WAAA,iDAAoD;;;;;IAnBxDZ,EALR,CAAAC,cAAA,aAAgC,aAC8B,eACM,YACvD,SACH,YACwB;IAAAD,EAAA,CAAAE,MAAA,GAA2C;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1EH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IACtBF,EADsB,CAAAG,YAAA,EAAK,EACtB;IAEHH,EADF,CAAAC,cAAA,UAAI,aACwB;IAAAD,EAAA,CAAAE,MAAA,IAA0C;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzEH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAe;IACrBF,EADqB,CAAAG,YAAA,EAAK,EACrB;IAEHH,EADF,CAAAC,cAAA,UAAI,aACwB;IAAAD,EAAA,CAAAE,MAAA,IAAmD;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEhFH,EADF,CAAAC,cAAA,UAAI,eACoD;IAAAD,EAAA,CAAAE,MAAA,IAAwB;IAElFF,EAFkF,CAAAG,YAAA,EAAO,EAClF,EACF;IAEHH,EADF,CAAAC,cAAA,UAAI,aACwB;IAAAD,EAAA,CAAAE,MAAA,IAA2C;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1EH,EAAA,CAAAC,cAAA,UAAI;IAGFD,EAFA,CAAAa,UAAA,KAAAC,6CAAA,kBACuC,KAAAC,6CAAA,kBACoB;IAOrEf,EALQ,CAAAG,YAAA,EAAK,EACF,EACG,EACF,EACJ,EACF;;;;IAzB4BH,EAAA,CAAAQ,SAAA,GAA2C;IAA3CR,EAAA,CAAAW,kBAAA,KAAAX,EAAA,CAAAY,WAAA,yCAA2C;IACjEZ,EAAA,CAAAQ,SAAA,GAAgB;IAAhBR,EAAA,CAAAS,iBAAA,CAAAJ,MAAA,CAAAW,IAAA,CAAAN,KAAA,CAAgB;IAGMV,EAAA,CAAAQ,SAAA,GAA0C;IAA1CR,EAAA,CAAAW,kBAAA,KAAAX,EAAA,CAAAY,WAAA,yCAA0C;IAChEZ,EAAA,CAAAQ,SAAA,GAAe;IAAfR,EAAA,CAAAS,iBAAA,CAAAJ,MAAA,CAAAW,IAAA,CAAAC,IAAA,CAAe;IAGOjB,EAAA,CAAAQ,SAAA,GAAmD;IAAnDR,EAAA,CAAAW,kBAAA,KAAAX,EAAA,CAAAY,WAAA,kDAAmD;IAErEZ,EAAA,CAAAQ,SAAA,GAA+C;IAA/CR,EAAA,CAAAI,UAAA,YAAAC,MAAA,CAAAa,gBAAA,CAAAb,MAAA,CAAAW,IAAA,CAAAG,YAAA,EAA+C;IAACnB,EAAA,CAAAQ,SAAA,EAAwB;IAAxBR,EAAA,CAAAS,iBAAA,CAAAJ,MAAA,CAAAW,IAAA,CAAAI,aAAA,CAAwB;IAItDpB,EAAA,CAAAQ,SAAA,GAA2C;IAA3CR,EAAA,CAAAS,iBAAA,CAAAT,EAAA,CAAAY,WAAA,sCAA2C;IAEDZ,EAAA,CAAAQ,SAAA,GAAc;IAAdR,EAAA,CAAAI,UAAA,YAAAC,MAAA,CAAAW,IAAA,CAAAK,MAAA,CAAc;IAEzErB,EAAA,CAAAQ,SAAA,EAAyB;IAAzBR,EAAA,CAAAI,UAAA,UAAAC,MAAA,CAAAW,IAAA,CAAAK,MAAA,CAAAC,MAAA,CAAyB;;;ADX1C,OAAM,MAAOC,sBAAsB;EAIjC,IACIP,IAAIA,CAAEQ,KAAW;IACnB,IAAI,CAACC,KAAK,GAAGD,KAAK;EACpB;EAEA,IAAIR,IAAIA,CAAA;IACN,OAAO,IAAI,CAACS,KAAK;EACnB;EAEAC,YAAA,GAEA;EAGOR,gBAAgBA,CAAEC,YAAoB;IAC3C,IAAIQ,QAAQ,GAAG7B,oBAAoB,CAAC8B,OAAO;IAE3C,IAAI9B,oBAAoB,CAAC+B,cAAc,CAACV,YAAY,CAAC,EAAE;MACrDQ,QAAQ,GAAG7B,oBAAoB,CAACqB,YAAY,CAAC;IAC/C;IAEA,OAAOQ,QAAQ;EACjB;EAEOrB,aAAaA,CAAEwB,KAAgB;IACpC,IAAIH,QAAQ,GAAG,kBAAkB;IAEjC,IAAI5B,WAAW,CAAC8B,cAAc,CAACC,KAAK,CAACC,KAAK,CAAC,EAAE;MAC3CJ,QAAQ,GAAG5B,WAAW,CAAC+B,KAAK,CAACC,KAAK,CAAC;IACrC;IAEA,OAAOJ,QAAQ;EACjB;;;uCApCWJ,sBAAsB;IAAA;EAAA;;;YAAtBA,sBAAsB;MAAAS,SAAA;MAAAC,MAAA;QAAAjB,IAAA;MAAA;MAAAkB,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZnCvC,EAAA,CAAAa,UAAA,IAAA4B,qCAAA,mBAAgC;;;UAAZzC,EAAA,CAAAI,UAAA,SAAAoC,GAAA,CAAAxB,IAAA,CAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}