{"ast": null, "code": "module.exports = typeof Array.from === 'function' ? Array.from : require('./polyfill');", "map": {"version": 3, "names": ["module", "exports", "Array", "from", "require"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/node_modules/array-from/index.js"], "sourcesContent": ["module.exports = (typeof Array.from === 'function' ?\n  Array.from :\n  require('./polyfill')\n);\n"], "mappings": "AAAAA,MAAM,CAACC,OAAO,GAAI,OAAOC,KAAK,CAACC,IAAI,KAAK,UAAU,GAChDD,KAAK,CAACC,IAAI,GACVC,OAAO,CAAC,YAAY,CACrB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}