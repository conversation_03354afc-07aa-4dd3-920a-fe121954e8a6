{"ast": null, "code": "import { coerceArray } from '@angular/cdk/coercion';\nimport { Subject } from 'rxjs';\nimport { map, take, tap } from 'rxjs/operators';\nimport { FileType, simulateBrowserDownload } from '../../../../../../common/lib/files';\nimport { transformSpin, transformSpinDetails } from '../../../../../../common/core/currecy-transform';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../../../common/services/reports/gamehistory.service\";\nimport * as i2 from \"@skywind-group/lib-swui\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"ngx-clipboard\";\nimport * as i5 from \"../../../../../../common/services/jurisdiction.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/menu\";\nimport * as i9 from \"@angular/material/icon\";\nimport * as i10 from \"../../../../../../common/components/clipboard/clipboard.directive\";\nfunction SpinListActionsComponent_button_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 5)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"more_vert\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const menu_r2 = i0.ɵɵreference(2);\n    i0.ɵɵproperty(\"matMenuTriggerFor\", menu_r2);\n  }\n}\nfunction SpinListActionsComponent_button_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 3);\n    i0.ɵɵlistener(\"click\", function SpinListActionsComponent_button_11_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.downloadSmResult());\n    });\n    i0.ɵɵelementStart(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 1, \"COMPONENTS.GRID.SM_RESULT_DOWNLOAD\"));\n  }\n}\nfunction isEmptyArray(obj) {\n  if (obj instanceof Array) {\n    return obj.every(item => {\n      return typeof item !== 'object';\n    });\n  }\n  return false;\n}\nfunction flattenObj(value, parent, res = null) {\n  if (!res) {\n    if (value instanceof Array) {\n      return value.map(item => flattenObj(item, '', {}));\n    } else {\n      res = {};\n    }\n  }\n  // tslint:disable-next-line:forin\n  for (let key in value) {\n    let propName = parent ? parent + '.' + key : key;\n    if (typeof value[key] === 'object') {\n      if (isEmptyArray(value[key])) {\n        res[propName] = `\"${value[key]}\"`;\n      } else {\n        flattenObj(value[key], propName, res);\n      }\n    } else {\n      res[propName] = `\"${value[key]}\"`;\n    }\n  }\n  return res;\n}\nfunction getHeaders(value) {\n  if (value instanceof Array) {\n    return Array.from(value.reduce((result, item) => {\n      const keys = Object.keys(item);\n      keys.forEach(key => {\n        result.add(key.toString());\n      });\n      return result;\n    }, new Set()));\n  }\n  return Object.keys(value);\n}\nfunction transformToCsv(value) {\n  const data = flattenObj(value, '');\n  const headers = getHeaders(data);\n  const sortedHeaders = headers.sort((a, b) => {\n    return a.split('.').length - a.split('.').length || a < b ? -1 : a > b ? 1 : 0;\n  });\n  const result = [sortedHeaders.map(header => `\"${header}\"`)];\n  const arr = coerceArray(data || []);\n  return arr.reduce((res, item, index) => {\n    res[index + 1] = sortedHeaders.map(header => item[header] || '').toString();\n    return res;\n  }, result);\n}\nexport class SpinListActionsComponent {\n  constructor(service, notifications, authService, translateService, clipboard, jurisdictionService) {\n    this.service = service;\n    this.notifications = notifications;\n    this.translateService = translateService;\n    this.clipboard = clipboard;\n    this.path = '';\n    this.loading = true;\n    this.gridIsEmpty = true;\n    this.spinListWithDetail = [];\n    this.destroyed$ = new Subject();\n    this.isSuperAdmin = authService.isSuperAdmin;\n    this.smResultAvailable$ = jurisdictionService.entityJurisdiction.pipe(map(codes => codes.includes('PT')));\n  }\n  ngOnInit() {\n    if (this.isSuperAdmin) {\n      this.getSpinListWithDetails();\n    }\n  }\n  ngOnDestroy() {\n    this.destroyed$.next();\n    this.destroyed$.complete();\n  }\n  getSpinListWithDetails() {\n    this.service.getSpinListWithDetails(this.path, this.roundInfo.roundId.toString()).pipe(tap(() => {\n      this.loading = this.gridIsEmpty = true;\n    })).subscribe(data => {\n      this.spinListWithDetail = data;\n      this.notifications.success(this.translateService.instant('GAMEHISTORY.GAME.notificationHistoryLoaded'));\n    }, error => {\n      this.notifications.error(error?.error?.message);\n    }, () => {\n      this.gridIsEmpty = this.spinListWithDetail.length <= 0;\n      this.loading = false;\n    });\n  }\n  onCopyClick() {\n    try {\n      this.clipboard.copy(JSON.stringify(this.spinListWithDetail, null, 4));\n      this.notifications.success(this.translateService.instant('GAMEHISTORY.GAME.notificationRoundCopy'), '');\n    } catch (exception) {\n      this.notifications.error(this.translateService.instant('GAMEHISTORY.GAME.notificationCopyFailed'), '');\n    }\n  }\n  isButtonDisabled() {\n    return this.loading || this.gridIsEmpty;\n  }\n  onCopyJsonClick() {\n    const gn = this.roundInfo.gameNameLabel;\n    const rid = this.roundInfo.roundId;\n    const filename = `${gn} spins list of round ${rid}(details)`;\n    const data = this.spinListWithDetail.reduce((res, details) => {\n      const val = JSON.parse(JSON.stringify(details));\n      transformSpin(val, details.currency);\n      transformSpinDetails(val, details.currency);\n      res.push(val);\n      return res;\n    }, []);\n    const transformToCsvResult = transformToCsv(data);\n    simulateBrowserDownload(transformToCsvResult.join('\\n'), filename, FileType.Csv);\n  }\n  downloadSmResult() {\n    this.service.getGameHistorySmResult(this.path, this.roundInfo.roundId.toString()).pipe(take(1)).subscribe(data => {\n      const {\n        name\n      } = this.roundInfo._meta;\n      const filename = `RoundId [${this.roundInfo.roundId}] entity name [${name}]`;\n      simulateBrowserDownload(data, filename, FileType.Txt);\n    });\n  }\n  static {\n    this.ɵfac = function SpinListActionsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SpinListActionsComponent)(i0.ɵɵdirectiveInject(i1.GameHistoryService), i0.ɵɵdirectiveInject(i2.SwuiNotificationsService), i0.ɵɵdirectiveInject(i2.SwHubAuthService), i0.ɵɵdirectiveInject(i3.TranslateService), i0.ɵɵdirectiveInject(i4.ClipboardService), i0.ɵɵdirectiveInject(i5.JurisdictionService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SpinListActionsComponent,\n      selectors: [[\"spin-list-actions\"]],\n      inputs: {\n        roundInfo: \"roundInfo\",\n        path: \"path\"\n      },\n      decls: 13,\n      vars: 11,\n      consts: [[\"menu\", \"matMenu\"], [\"mat-icon-button\", \"\", 3, \"matMenuTriggerFor\", 4, \"ngIf\"], [\"mat-menu-item\", \"\", \"clipboard\", \"\", 3, \"click\", \"disabled\"], [\"mat-menu-item\", \"\", 3, \"click\"], [\"mat-menu-item\", \"\", 3, \"click\", 4, \"ngIf\"], [\"mat-icon-button\", \"\", 3, \"matMenuTriggerFor\"]],\n      template: function SpinListActionsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵtemplate(0, SpinListActionsComponent_button_0_Template, 3, 1, \"button\", 1);\n          i0.ɵɵelementStart(1, \"mat-menu\", null, 0)(3, \"button\", 2);\n          i0.ɵɵlistener(\"click\", function SpinListActionsComponent_Template_button_click_3_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onCopyClick());\n          });\n          i0.ɵɵelementStart(4, \"span\");\n          i0.ɵɵtext(5);\n          i0.ɵɵpipe(6, \"translate\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"button\", 3);\n          i0.ɵɵlistener(\"click\", function SpinListActionsComponent_Template_button_click_7_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onCopyJsonClick());\n          });\n          i0.ɵɵelementStart(8, \"span\");\n          i0.ɵɵtext(9);\n          i0.ɵɵpipe(10, \"translate\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(11, SpinListActionsComponent_button_11_Template, 4, 3, \"button\", 4);\n          i0.ɵɵpipe(12, \"async\");\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.isSuperAdmin);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"disabled\", ctx.isButtonDisabled());\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(6, 5, \"GAMEHISTORY.GAME.copyJsonToClipboard\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(10, 7, \"GAMEHISTORY.GAME.downloadDetailedCSV\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(12, 9, ctx.smResultAvailable$) && ctx.roundInfo.finished);\n        }\n      },\n      dependencies: [i6.NgIf, i7.MatIconButton, i8.MatMenu, i8.MatMenuItem, i8.MatMenuTrigger, i9.MatIcon, i10.ClipboardDirective, i6.AsyncPipe, i3.TranslatePipe],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["coerce<PERSON><PERSON><PERSON>", "Subject", "map", "take", "tap", "FileType", "simulateBrowserDownload", "transformSpin", "transformSpinDetails", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "menu_r2", "ɵɵlistener", "SpinListActionsComponent_button_11_Template_button_click_0_listener", "ɵɵrestoreView", "_r3", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "downloadSmResult", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpipeBind1", "isEmptyArray", "obj", "Array", "every", "item", "flattenObj", "value", "parent", "res", "key", "propName", "getHeaders", "from", "reduce", "result", "keys", "Object", "for<PERSON>ach", "add", "toString", "Set", "transformToCsv", "data", "headers", "sortedHeaders", "sort", "a", "b", "split", "length", "header", "arr", "index", "SpinListActionsComponent", "constructor", "service", "notifications", "authService", "translateService", "clipboard", "jurisdictionService", "path", "loading", "gridIsEmpty", "spinListWithDetail", "destroyed$", "isSuperAdmin", "smResultAvailable$", "entityJurisdiction", "pipe", "codes", "includes", "ngOnInit", "getSpinListWithDetails", "ngOnDestroy", "next", "complete", "roundInfo", "roundId", "subscribe", "success", "instant", "error", "message", "onCopyClick", "copy", "JSON", "stringify", "exception", "isButtonDisabled", "onCopyJsonClick", "gn", "gameNameLabel", "rid", "filename", "details", "val", "parse", "currency", "push", "transformToCsvResult", "join", "Csv", "getGameHistorySmResult", "name", "_meta", "Txt", "ɵɵdirectiveInject", "i1", "GameHistoryService", "i2", "SwuiNotificationsService", "SwHubAuthService", "i3", "TranslateService", "i4", "ClipboardService", "i5", "JurisdictionService", "selectors", "inputs", "decls", "vars", "consts", "template", "SpinListActionsComponent_Template", "rf", "ctx", "ɵɵtemplate", "SpinListActionsComponent_button_0_Template", "SpinListActionsComponent_Template_button_click_3_listener", "_r1", "SpinListActionsComponent_Template_button_click_7_listener", "SpinListActionsComponent_button_11_Template", "finished"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/gamehistory/components/internal-game-history/spin-list/spin-list-actions/spin-list-actions.component.ts", "/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/gamehistory/components/internal-game-history/spin-list/spin-list-actions/spin-list-actions.component.html"], "sourcesContent": ["import { coerceArray } from '@angular/cdk/coercion';\nimport { HttpErrorResponse } from '@angular/common/http';\nimport { Component, Input } from '@angular/core';\nimport { TranslateService } from '@ngx-translate/core';\nimport { SwHubAuthService, SwuiNotificationsService } from '@skywind-group/lib-swui';\nimport { ClipboardService } from 'ngx-clipboard';\nimport { Observable, Subject } from 'rxjs';\nimport { map, take, tap } from 'rxjs/operators';\nimport { FileType, simulateBrowserDownload } from '../../../../../../common/lib/files';\nimport { GameHistoryService } from '../../../../../../common/services/reports/gamehistory.service';\nimport { GameHistory } from '../../../../../../common/typings';\nimport { JurisdictionService } from '../../../../../../common/services/jurisdiction.service';\nimport { transformSpin, transformSpinDetails } from '../../../../../../common/core/currecy-transform';\n\n\nfunction isEmptyArray( obj: Record<string, any> | Array<any> ): boolean {\n  if (obj instanceof Array) {\n    return obj.every(item => {\n      return typeof item !== 'object';\n    });\n  }\n  return false;\n}\n\nfunction flattenObj( value: Record<string, any> | Array<any>, parent: string, res = null ) {\n  if (!res) {\n    if (value instanceof Array) {\n      return value.map(item => flattenObj(item, '', {}));\n    } else {\n      res = {};\n    }\n  }\n  // tslint:disable-next-line:forin\n  for (let key in value) {\n    let propName = parent ? parent + '.' + key : key;\n    if (typeof value[key] === 'object') {\n      if (isEmptyArray(value[key])) {\n        res[propName] = `\"${value[key]}\"`;\n      } else {\n        flattenObj(value[key], propName, res);\n      }\n    } else {\n      res[propName] = `\"${value[key]}\"`;\n    }\n  }\n  return res;\n}\n\nfunction getHeaders( value: Record<string, any> | Array<any> ): string[] {\n  if (value instanceof Array) {\n    return Array.from(value.reduce(( result: Set<string>, item ) => {\n      const keys = Object.keys(item);\n      keys.forEach(key => {\n        result.add(key.toString());\n      });\n      return result;\n    }, new Set()));\n  }\n  return Object.keys(value);\n}\n\nfunction transformToCsv( value: Record<string, any> | Array<any> ): string[] {\n  const data = flattenObj(value, '');\n  const headers = getHeaders(data);\n  const sortedHeaders = headers.sort(( a, b ) => {\n    return a.split('.').length - a.split('.').length\n    || a < b ? -1 : a > b ? 1 : 0;\n  });\n\n  const result = [sortedHeaders.map(header => `\"${header}\"`)];\n  const arr = coerceArray(data || []);\n  return arr.reduce(( res, item, index ) => {\n    res[index + 1] = sortedHeaders.map(header => item[header] || '').toString();\n    return res;\n  }, result);\n}\n\n@Component({\n  selector: 'spin-list-actions',\n  templateUrl: './spin-list-actions.component.html',\n})\nexport class SpinListActionsComponent {\n  @Input() roundInfo: number | any;\n  @Input() path: string = '';\n\n  loading = true;\n  gridIsEmpty = true;\n\n  readonly isSuperAdmin: boolean;\n  readonly smResultAvailable$: Observable<boolean>;\n\n  private spinListWithDetail: any[] = [];\n  private readonly destroyed$ = new Subject<void>();\n\n  constructor( private readonly service: GameHistoryService<GameHistory>,\n               private readonly notifications: SwuiNotificationsService,\n               authService: SwHubAuthService,\n               private readonly translateService: TranslateService,\n               private readonly clipboard: ClipboardService,\n               jurisdictionService: JurisdictionService\n  ) {\n    this.isSuperAdmin = authService.isSuperAdmin;\n    this.smResultAvailable$ = jurisdictionService.entityJurisdiction.pipe(map(codes => codes.includes('PT')));\n  }\n\n  ngOnInit() {\n    if (this.isSuperAdmin) {\n      this.getSpinListWithDetails();\n    }\n  }\n\n  ngOnDestroy() {\n    this.destroyed$.next();\n    this.destroyed$.complete();\n  }\n\n  getSpinListWithDetails() {\n    this.service.getSpinListWithDetails(this.path, this.roundInfo.roundId.toString())\n      .pipe(\n        tap(() => {\n          this.loading = this.gridIsEmpty = true;\n        }),\n      ).subscribe(\n      data => {\n        this.spinListWithDetail = data;\n        this.notifications.success(this.translateService.instant('GAMEHISTORY.GAME.notificationHistoryLoaded'));\n      },\n      ( error: HttpErrorResponse ) => {\n        this.notifications.error(error?.error?.message);\n      },\n      () => {\n        this.gridIsEmpty = this.spinListWithDetail.length <= 0;\n        this.loading = false;\n      });\n  }\n\n  onCopyClick() {\n    try {\n      this.clipboard.copy(JSON.stringify(this.spinListWithDetail, null, 4));\n      this.notifications.success(this.translateService.instant('GAMEHISTORY.GAME.notificationRoundCopy'), '');\n    } catch (exception) {\n      this.notifications.error(this.translateService.instant('GAMEHISTORY.GAME.notificationCopyFailed'), '');\n    }\n  }\n\n  isButtonDisabled(): boolean {\n    return this.loading || this.gridIsEmpty;\n  }\n\n  onCopyJsonClick() {\n    const gn = this.roundInfo.gameNameLabel;\n    const rid = this.roundInfo.roundId;\n    const filename = `${gn} spins list of round ${rid}(details)`;\n    const data = this.spinListWithDetail.reduce((res, details) => {\n      const val = JSON.parse(JSON.stringify(details));\n      transformSpin(val, details.currency);\n      transformSpinDetails(val, details.currency);\n\n      res.push(val);\n      return res;\n    }, []);\n    const transformToCsvResult = transformToCsv(data);\n    simulateBrowserDownload(transformToCsvResult.join('\\n'), filename, FileType.Csv);\n  }\n\n  downloadSmResult() {\n    this.service.getGameHistorySmResult(this.path, this.roundInfo.roundId.toString())\n      .pipe(\n        take(1)\n      ).subscribe(( data: string ) => {\n      const { name } = this.roundInfo._meta;\n      const filename = `RoundId [${this.roundInfo.roundId}] entity name [${name}]`;\n      simulateBrowserDownload(data, filename, FileType.Txt);\n    });\n  }\n}\n", "<button mat-icon-button\n        *ngIf=\"isSuperAdmin\"\n        [matMenuTriggerFor]=\"menu\">\n  <mat-icon>more_vert</mat-icon>\n</button>\n<mat-menu #menu=\"matMenu\">\n  <button mat-menu-item\n          clipboard\n          [disabled]=\"isButtonDisabled()\"\n          (click)=\"onCopyClick()\">\n    <span>{{ 'GAMEHISTORY.GAME.copyJsonToClipboard' | translate }}</span>\n  </button>\n  <button mat-menu-item\n          (click)=\"onCopyJsonClick()\">\n    <span>{{ 'GAMEHISTORY.GAME.downloadDetailedCSV' | translate }}</span>\n  </button>\n  <button mat-menu-item\n          *ngIf=\"(smResultAvailable$ | async) && roundInfo.finished\"\n          (click)=\"downloadSmResult()\">\n    <span>{{ 'COMPONENTS.GRID.SM_RESULT_DOWNLOAD' | translate }}</span>\n  </button>\n</mat-menu>\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,uBAAuB;AAMnD,SAAqBC,OAAO,QAAQ,MAAM;AAC1C,SAASC,GAAG,EAAEC,IAAI,EAAEC,GAAG,QAAQ,gBAAgB;AAC/C,SAASC,QAAQ,EAAEC,uBAAuB,QAAQ,oCAAoC;AAItF,SAASC,aAAa,EAAEC,oBAAoB,QAAQ,iDAAiD;;;;;;;;;;;;;;ICTnGC,EAHF,CAAAC,cAAA,gBAEmC,eACvB;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IACrBF,EADqB,CAAAG,YAAA,EAAW,EACvB;;;;;IAFDH,EAAA,CAAAI,UAAA,sBAAAC,OAAA,CAA0B;;;;;;IAchCL,EAAA,CAAAC,cAAA,gBAEqC;IAA7BD,EAAA,CAAAM,UAAA,mBAAAC,oEAAA;MAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAG,gBAAA,EAAkB;IAAA,EAAC;IAClCb,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAsD;;IAC9DF,EAD8D,CAAAG,YAAA,EAAO,EAC5D;;;IADDH,EAAA,CAAAc,SAAA,GAAsD;IAAtDd,EAAA,CAAAe,iBAAA,CAAAf,EAAA,CAAAgB,WAAA,6CAAsD;;;ADJhE,SAASC,YAAYA,CAAEC,GAAqC;EAC1D,IAAIA,GAAG,YAAYC,KAAK,EAAE;IACxB,OAAOD,GAAG,CAACE,KAAK,CAACC,IAAI,IAAG;MACtB,OAAO,OAAOA,IAAI,KAAK,QAAQ;IACjC,CAAC,CAAC;EACJ;EACA,OAAO,KAAK;AACd;AAEA,SAASC,UAAUA,CAAEC,KAAuC,EAAEC,MAAc,EAAEC,GAAG,GAAG,IAAI;EACtF,IAAI,CAACA,GAAG,EAAE;IACR,IAAIF,KAAK,YAAYJ,KAAK,EAAE;MAC1B,OAAOI,KAAK,CAAC9B,GAAG,CAAC4B,IAAI,IAAIC,UAAU,CAACD,IAAI,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;IACpD,CAAC,MAAM;MACLI,GAAG,GAAG,EAAE;IACV;EACF;EACA;EACA,KAAK,IAAIC,GAAG,IAAIH,KAAK,EAAE;IACrB,IAAII,QAAQ,GAAGH,MAAM,GAAGA,MAAM,GAAG,GAAG,GAAGE,GAAG,GAAGA,GAAG;IAChD,IAAI,OAAOH,KAAK,CAACG,GAAG,CAAC,KAAK,QAAQ,EAAE;MAClC,IAAIT,YAAY,CAACM,KAAK,CAACG,GAAG,CAAC,CAAC,EAAE;QAC5BD,GAAG,CAACE,QAAQ,CAAC,GAAG,IAAIJ,KAAK,CAACG,GAAG,CAAC,GAAG;MACnC,CAAC,MAAM;QACLJ,UAAU,CAACC,KAAK,CAACG,GAAG,CAAC,EAAEC,QAAQ,EAAEF,GAAG,CAAC;MACvC;IACF,CAAC,MAAM;MACLA,GAAG,CAACE,QAAQ,CAAC,GAAG,IAAIJ,KAAK,CAACG,GAAG,CAAC,GAAG;IACnC;EACF;EACA,OAAOD,GAAG;AACZ;AAEA,SAASG,UAAUA,CAAEL,KAAuC;EAC1D,IAAIA,KAAK,YAAYJ,KAAK,EAAE;IAC1B,OAAOA,KAAK,CAACU,IAAI,CAACN,KAAK,CAACO,MAAM,CAAC,CAAEC,MAAmB,EAAEV,IAAI,KAAK;MAC7D,MAAMW,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACX,IAAI,CAAC;MAC9BW,IAAI,CAACE,OAAO,CAACR,GAAG,IAAG;QACjBK,MAAM,CAACI,GAAG,CAACT,GAAG,CAACU,QAAQ,EAAE,CAAC;MAC5B,CAAC,CAAC;MACF,OAAOL,MAAM;IACf,CAAC,EAAE,IAAIM,GAAG,EAAE,CAAC,CAAC;EAChB;EACA,OAAOJ,MAAM,CAACD,IAAI,CAACT,KAAK,CAAC;AAC3B;AAEA,SAASe,cAAcA,CAAEf,KAAuC;EAC9D,MAAMgB,IAAI,GAAGjB,UAAU,CAACC,KAAK,EAAE,EAAE,CAAC;EAClC,MAAMiB,OAAO,GAAGZ,UAAU,CAACW,IAAI,CAAC;EAChC,MAAME,aAAa,GAAGD,OAAO,CAACE,IAAI,CAAC,CAAEC,CAAC,EAAEC,CAAC,KAAK;IAC5C,OAAOD,CAAC,CAACE,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,GAAGH,CAAC,CAACE,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,IAC7CH,CAAC,GAAGC,CAAC,GAAG,CAAC,CAAC,GAAGD,CAAC,GAAGC,CAAC,GAAG,CAAC,GAAG,CAAC;EAC/B,CAAC,CAAC;EAEF,MAAMb,MAAM,GAAG,CAACU,aAAa,CAAChD,GAAG,CAACsD,MAAM,IAAI,IAAIA,MAAM,GAAG,CAAC,CAAC;EAC3D,MAAMC,GAAG,GAAGzD,WAAW,CAACgD,IAAI,IAAI,EAAE,CAAC;EACnC,OAAOS,GAAG,CAAClB,MAAM,CAAC,CAAEL,GAAG,EAAEJ,IAAI,EAAE4B,KAAK,KAAK;IACvCxB,GAAG,CAACwB,KAAK,GAAG,CAAC,CAAC,GAAGR,aAAa,CAAChD,GAAG,CAACsD,MAAM,IAAI1B,IAAI,CAAC0B,MAAM,CAAC,IAAI,EAAE,CAAC,CAACX,QAAQ,EAAE;IAC3E,OAAOX,GAAG;EACZ,CAAC,EAAEM,MAAM,CAAC;AACZ;AAMA,OAAM,MAAOmB,wBAAwB;EAanCC,YAA8BC,OAAwC,EACxCC,aAAuC,EACxDC,WAA6B,EACZC,gBAAkC,EAClCC,SAA2B,EAC5CC,mBAAwC;IALvB,KAAAL,OAAO,GAAPA,OAAO;IACP,KAAAC,aAAa,GAAbA,aAAa;IAEb,KAAAE,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,SAAS,GAATA,SAAS;IAf9B,KAAAE,IAAI,GAAW,EAAE;IAE1B,KAAAC,OAAO,GAAG,IAAI;IACd,KAAAC,WAAW,GAAG,IAAI;IAKV,KAAAC,kBAAkB,GAAU,EAAE;IACrB,KAAAC,UAAU,GAAG,IAAItE,OAAO,EAAQ;IAS/C,IAAI,CAACuE,YAAY,GAAGT,WAAW,CAACS,YAAY;IAC5C,IAAI,CAACC,kBAAkB,GAAGP,mBAAmB,CAACQ,kBAAkB,CAACC,IAAI,CAACzE,GAAG,CAAC0E,KAAK,IAAIA,KAAK,CAACC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAC3G;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACN,YAAY,EAAE;MACrB,IAAI,CAACO,sBAAsB,EAAE;IAC/B;EACF;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACT,UAAU,CAACU,IAAI,EAAE;IACtB,IAAI,CAACV,UAAU,CAACW,QAAQ,EAAE;EAC5B;EAEAH,sBAAsBA,CAAA;IACpB,IAAI,CAAClB,OAAO,CAACkB,sBAAsB,CAAC,IAAI,CAACZ,IAAI,EAAE,IAAI,CAACgB,SAAS,CAACC,OAAO,CAACvC,QAAQ,EAAE,CAAC,CAC9E8B,IAAI,CACHvE,GAAG,CAAC,MAAK;MACP,IAAI,CAACgE,OAAO,GAAG,IAAI,CAACC,WAAW,GAAG,IAAI;IACxC,CAAC,CAAC,CACH,CAACgB,SAAS,CACXrC,IAAI,IAAG;MACL,IAAI,CAACsB,kBAAkB,GAAGtB,IAAI;MAC9B,IAAI,CAACc,aAAa,CAACwB,OAAO,CAAC,IAAI,CAACtB,gBAAgB,CAACuB,OAAO,CAAC,4CAA4C,CAAC,CAAC;IACzG,CAAC,EACCC,KAAwB,IAAK;MAC7B,IAAI,CAAC1B,aAAa,CAAC0B,KAAK,CAACA,KAAK,EAAEA,KAAK,EAAEC,OAAO,CAAC;IACjD,CAAC,EACD,MAAK;MACH,IAAI,CAACpB,WAAW,GAAG,IAAI,CAACC,kBAAkB,CAACf,MAAM,IAAI,CAAC;MACtD,IAAI,CAACa,OAAO,GAAG,KAAK;IACtB,CAAC,CAAC;EACN;EAEAsB,WAAWA,CAAA;IACT,IAAI;MACF,IAAI,CAACzB,SAAS,CAAC0B,IAAI,CAACC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACvB,kBAAkB,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;MACrE,IAAI,CAACR,aAAa,CAACwB,OAAO,CAAC,IAAI,CAACtB,gBAAgB,CAACuB,OAAO,CAAC,wCAAwC,CAAC,EAAE,EAAE,CAAC;IACzG,CAAC,CAAC,OAAOO,SAAS,EAAE;MAClB,IAAI,CAAChC,aAAa,CAAC0B,KAAK,CAAC,IAAI,CAACxB,gBAAgB,CAACuB,OAAO,CAAC,yCAAyC,CAAC,EAAE,EAAE,CAAC;IACxG;EACF;EAEAQ,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAAC3B,OAAO,IAAI,IAAI,CAACC,WAAW;EACzC;EAEA2B,eAAeA,CAAA;IACb,MAAMC,EAAE,GAAG,IAAI,CAACd,SAAS,CAACe,aAAa;IACvC,MAAMC,GAAG,GAAG,IAAI,CAAChB,SAAS,CAACC,OAAO;IAClC,MAAMgB,QAAQ,GAAG,GAAGH,EAAE,wBAAwBE,GAAG,WAAW;IAC5D,MAAMnD,IAAI,GAAG,IAAI,CAACsB,kBAAkB,CAAC/B,MAAM,CAAC,CAACL,GAAG,EAAEmE,OAAO,KAAI;MAC3D,MAAMC,GAAG,GAAGV,IAAI,CAACW,KAAK,CAACX,IAAI,CAACC,SAAS,CAACQ,OAAO,CAAC,CAAC;MAC/C9F,aAAa,CAAC+F,GAAG,EAAED,OAAO,CAACG,QAAQ,CAAC;MACpChG,oBAAoB,CAAC8F,GAAG,EAAED,OAAO,CAACG,QAAQ,CAAC;MAE3CtE,GAAG,CAACuE,IAAI,CAACH,GAAG,CAAC;MACb,OAAOpE,GAAG;IACZ,CAAC,EAAE,EAAE,CAAC;IACN,MAAMwE,oBAAoB,GAAG3D,cAAc,CAACC,IAAI,CAAC;IACjD1C,uBAAuB,CAACoG,oBAAoB,CAACC,IAAI,CAAC,IAAI,CAAC,EAAEP,QAAQ,EAAE/F,QAAQ,CAACuG,GAAG,CAAC;EAClF;EAEAtF,gBAAgBA,CAAA;IACd,IAAI,CAACuC,OAAO,CAACgD,sBAAsB,CAAC,IAAI,CAAC1C,IAAI,EAAE,IAAI,CAACgB,SAAS,CAACC,OAAO,CAACvC,QAAQ,EAAE,CAAC,CAC9E8B,IAAI,CACHxE,IAAI,CAAC,CAAC,CAAC,CACR,CAACkF,SAAS,CAAGrC,IAAY,IAAK;MAC/B,MAAM;QAAE8D;MAAI,CAAE,GAAG,IAAI,CAAC3B,SAAS,CAAC4B,KAAK;MACrC,MAAMX,QAAQ,GAAG,YAAY,IAAI,CAACjB,SAAS,CAACC,OAAO,kBAAkB0B,IAAI,GAAG;MAC5ExG,uBAAuB,CAAC0C,IAAI,EAAEoD,QAAQ,EAAE/F,QAAQ,CAAC2G,GAAG,CAAC;IACvD,CAAC,CAAC;EACJ;;;uCA7FWrD,wBAAwB,EAAAlD,EAAA,CAAAwG,iBAAA,CAAAC,EAAA,CAAAC,kBAAA,GAAA1G,EAAA,CAAAwG,iBAAA,CAAAG,EAAA,CAAAC,wBAAA,GAAA5G,EAAA,CAAAwG,iBAAA,CAAAG,EAAA,CAAAE,gBAAA,GAAA7G,EAAA,CAAAwG,iBAAA,CAAAM,EAAA,CAAAC,gBAAA,GAAA/G,EAAA,CAAAwG,iBAAA,CAAAQ,EAAA,CAAAC,gBAAA,GAAAjH,EAAA,CAAAwG,iBAAA,CAAAU,EAAA,CAAAC,mBAAA;IAAA;EAAA;;;YAAxBjE,wBAAwB;MAAAkE,SAAA;MAAAC,MAAA;QAAA3C,SAAA;QAAAhB,IAAA;MAAA;MAAA4D,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCjFrC3H,EAAA,CAAA6H,UAAA,IAAAC,0CAAA,oBAEmC;UAIjC9H,EADF,CAAAC,cAAA,wBAA0B,gBAIQ;UAAxBD,EAAA,CAAAM,UAAA,mBAAAyH,0DAAA;YAAA/H,EAAA,CAAAQ,aAAA,CAAAwH,GAAA;YAAA,OAAAhI,EAAA,CAAAY,WAAA,CAASgH,GAAA,CAAA3C,WAAA,EAAa;UAAA,EAAC;UAC7BjF,EAAA,CAAAC,cAAA,WAAM;UAAAD,EAAA,CAAAE,MAAA,GAAwD;;UAChEF,EADgE,CAAAG,YAAA,EAAO,EAC9D;UACTH,EAAA,CAAAC,cAAA,gBACoC;UAA5BD,EAAA,CAAAM,UAAA,mBAAA2H,0DAAA;YAAAjI,EAAA,CAAAQ,aAAA,CAAAwH,GAAA;YAAA,OAAAhI,EAAA,CAAAY,WAAA,CAASgH,GAAA,CAAArC,eAAA,EAAiB;UAAA,EAAC;UACjCvF,EAAA,CAAAC,cAAA,WAAM;UAAAD,EAAA,CAAAE,MAAA,GAAwD;;UAChEF,EADgE,CAAAG,YAAA,EAAO,EAC9D;UACTH,EAAA,CAAA6H,UAAA,KAAAK,2CAAA,oBAEqC;;UAGvClI,EAAA,CAAAG,YAAA,EAAW;;;UApBFH,EAAA,CAAAI,UAAA,SAAAwH,GAAA,CAAA7D,YAAA,CAAkB;UAOjB/D,EAAA,CAAAc,SAAA,GAA+B;UAA/Bd,EAAA,CAAAI,UAAA,aAAAwH,GAAA,CAAAtC,gBAAA,GAA+B;UAE/BtF,EAAA,CAAAc,SAAA,GAAwD;UAAxDd,EAAA,CAAAe,iBAAA,CAAAf,EAAA,CAAAgB,WAAA,+CAAwD;UAIxDhB,EAAA,CAAAc,SAAA,GAAwD;UAAxDd,EAAA,CAAAe,iBAAA,CAAAf,EAAA,CAAAgB,WAAA,gDAAwD;UAGvDhB,EAAA,CAAAc,SAAA,GAAwD;UAAxDd,EAAA,CAAAI,UAAA,SAAAJ,EAAA,CAAAgB,WAAA,QAAA4G,GAAA,CAAA5D,kBAAA,KAAA4D,GAAA,CAAAlD,SAAA,CAAAyD,QAAA,CAAwD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}