{"ast": null, "code": "import { map, take } from 'rxjs/operators';\nimport { FileType, simulateBrowserDownload } from '../../../../../common/lib/files';\nimport { MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../../common/services/reports/gamehistory.spin.service\";\nimport * as i2 from \"../../../../../common/services/reports/gamehistory.service\";\nimport * as i3 from \"@skywind-group/lib-swui\";\nimport * as i4 from \"../../../../../common/services/jurisdiction.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"../round/round-info.component\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/dialog\";\nimport * as i9 from \"@angular/material/icon\";\nimport * as i10 from \"@angular/flex-layout/extended\";\nimport * as i11 from \"@ngx-translate/core\";\nconst _c0 = a0 => ({\n  \"sw-chip-green\": a0\n});\nfunction RoundInfoViewModalComponent_ng_container_1_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, \"GAMEHISTORY.GRID.isTest\"));\n  }\n}\nfunction RoundInfoViewModalComponent_ng_container_1_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"span\", 15);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 16);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(3, 2, \"GAMEHISTORY.GRID.extRoundId\"), \":\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.roundInfo == null ? null : ctx_r1.roundInfo.extraData == null ? null : ctx_r1.roundInfo.extraData.extRoundId);\n  }\n}\nfunction RoundInfoViewModalComponent_ng_container_1_button_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function RoundInfoViewModalComponent_ng_container_1_button_31_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.downloadSmResult());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"COMPONENTS.GRID.SM_RESULT_DOWNLOAD\"), \" \");\n  }\n}\nfunction RoundInfoViewModalComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 5)(2, \"div\", 6)(3, \"div\", 7);\n    i0.ɵɵelement(4, \"img\", 8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 9)(6, \"div\", 10)(7, \"span\", 11)(8, \"span\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"span\", 12);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, RoundInfoViewModalComponent_ng_container_1_span_15_Template, 3, 3, \"span\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 10)(17, \"div\", 14)(18, \"span\", 15);\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\", 16);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(23, RoundInfoViewModalComponent_ng_container_1_div_23_Template, 6, 4, \"div\", 17);\n    i0.ɵɵelementStart(24, \"div\", 14)(25, \"span\", 15);\n    i0.ɵɵtext(26);\n    i0.ɵɵpipe(27, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"span\", 16);\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(30, \"div\", 18);\n    i0.ɵɵtemplate(31, RoundInfoViewModalComponent_ng_container_1_button_31_Template, 3, 3, \"button\", 19);\n    i0.ɵɵpipe(32, \"async\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"div\", 20)(34, \"round-info\", 21);\n    i0.ɵɵlistener(\"showSmResult\", function RoundInfoViewModalComponent_ng_container_1_Template_round_info_showSmResult_34_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showSmResultEmitter($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", ctx_r1.noImage, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.roundInfo.gameNameLabel);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r1.roundInfo.gameCode, \")\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(22, _c0, ctx_r1.roundInfo.finished));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(14, 14, ctx_r1.roundInfo.finished ? \"GAMEHISTORY.GRID.isFinished\" : \"GAMEHISTORY.GRID.unfinished\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.roundInfo.isTest);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(20, 16, \"GAMEHISTORY.GRID.roundId\"), \":\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.roundInfo.roundId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.roundInfo == null ? null : ctx_r1.roundInfo.extraData == null ? null : ctx_r1.roundInfo.extraData.extRoundId);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(27, 18, \"GAMEHISTORY.GRID.playerCode\"), \":\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.roundInfo.playerCode);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.roundInfo.finished && ctx_r1.isSuperAdmin && ctx_r1.showSmResult && i0.ɵɵpipeBind1(32, 20, ctx_r1.smResultAvailable$));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"roundInfo\", ctx_r1.roundInfo)(\"path\", ctx_r1.path);\n  }\n}\nfunction RoundInfoViewModalComponent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 20)(2, \"div\", 24)(3, \"div\", 25)(4, \"mat-icon\", 26);\n    i0.ɵɵtext(5, \"error_outline\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 27);\n    i0.ɵɵtext(7, \" Game details for \");\n    i0.ɵɵelementStart(8, \"b\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" are temporary unavailable.\");\n    i0.ɵɵelement(11, \"br\");\n    i0.ɵɵtext(12, \" Please contact Support Team via \");\n    i0.ɵɵelementStart(13, \"a\", 28);\n    i0.ɵɵlistener(\"click\", function RoundInfoViewModalComponent_ng_container_2_Template_a_click_13_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sendEmail());\n    });\n    i0.ɵɵtext(14, \"<EMAIL>\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(15, \" to retrieve round details. \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\"Round ID \", ctx_r1.roundInfo.roundId, \"\");\n  }\n}\nfunction RoundInfoViewModalComponent_ng_container_4_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function RoundInfoViewModalComponent_ng_container_4_button_1_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.footerBack());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"DIALOG.back\"), \" \");\n  }\n}\nfunction RoundInfoViewModalComponent_ng_container_4_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function RoundInfoViewModalComponent_ng_container_4_button_2_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.footerPrev());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"DIALOG.previous\"), \" \");\n  }\n}\nfunction RoundInfoViewModalComponent_ng_container_4_button_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function RoundInfoViewModalComponent_ng_container_4_button_3_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.footerNext());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"DIALOG.next\"), \" \");\n  }\n}\nfunction RoundInfoViewModalComponent_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, RoundInfoViewModalComponent_ng_container_4_button_1_Template, 3, 3, \"button\", 29)(2, RoundInfoViewModalComponent_ng_container_4_button_2_Template, 3, 3, \"button\", 29)(3, RoundInfoViewModalComponent_ng_container_4_button_3_Template, 3, 3, \"button\", 19);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.spinItem == null ? null : ctx_r1.spinItem.hasNext) || (ctx_r1.spinItem == null ? null : ctx_r1.spinItem.hasPrev));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.spinItem == null ? null : ctx_r1.spinItem.hasPrev) && ctx_r1.isSpinTypeAllow(ctx_r1.spinItem == null ? null : ctx_r1.spinItem.prevType));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.spinItem == null ? null : ctx_r1.spinItem.hasNext) && ctx_r1.isSpinTypeAllow(ctx_r1.spinItem == null ? null : ctx_r1.spinItem.nextType));\n  }\n}\n// tslint:disable-next-line\nconst noImage = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAIEAAACACAYAAAAs/Ar1AAAABmJLR0QA/wD/AP+gvaeTAAAUEElEQVR42u2dC1RTV9bH49jqOK1VeUglDyAJIMQ6ndKaBFDiY6ardWY6nY6dWTO104dFVECZadX2m5liH7bTjtaKFpPwMhAC4aHW0n46fqJVtAoEqPVRgfKKiCCgolYtcL99QkIDBrg3uTf3Xrhnrb2yjIvc5Px/d5999tnnXB6P5c1rlfoRr3htGC8x8Sc8ro2d5hG39QEQPtozTrPHK1ar4npkDLUpsdoAz3hNMoh/HSzJ51XdfVyvjJk7f0coiK4D+8EzTt3oFa+eT+X1Wr1l93c9KPW+IhJNw3i8cZwCNDbvNZqHPWM1hSB+DxjmEas97BOTPJ2Mz8bCwu7tFEijLvOlr7cLpcZ2gfQEWCPYTTBskPWAtXUIpeXtfGlOO1/yNvzd0k5+0M8BEi4WoebO1wo8YtVqEL4bid8HgFrNi1bf66rwHb7S37TzAw0gapcDsZ2xjstC6aftQsmrlwVBczgoXL3zV26/H1x9Ioh+0yY+2C3wAC+78rlw9wraBZIPQLDLJAk/nF2Aa21p95WEc0MJsXt0HAj9olecps1OfGQtnnEpc5z91E7/mf7twsAMEOa2G8R3ZA0IviuiIDGn8TDNJyFNDBH//kHiI2v2WKWROXXnS6UPXBZI3wcRvqdJ/Ltiig6BdFebQMpNZwe0xMR7PGI1q63TvcEA1E+PU0uc+dg2vmQ+dHo9Q8R3ZJUAxHNjPnbwWp0aDEKXOxAfTH0OBYaEBxToVLj737NG8hgL7OsOfuDisRn5x6uXDXH3IwBOTV+t9XFmXg93126WiD/YjqOp6pgQf1q0eopHnCbbsfgWuzBttVrkzPgPHfkVSwHoN/Bimdd8g7xGLwCrtbMh8v9uGACuocQQ0c9t9vX9GSR6jrIdADsQWsGj/WX0jf9x6sUg8tVhAOj2itX81okYYJw16YONPgss6vAVi0aB/Ng4EHidLeU7pMVqVjqZAEoYnQD0WydkIp9ir/6Q3gWBs4YV35YKdmaBRyAOHCLHP9qsF5JNH6N0N6v0941W/wzm/0UjAQBWjdLETnkBgXTfGADAziRftgmDfVkBwNQ16VMhADyCA4A7zqaD0XRqbAHQZ5Bt7DgrDPgVowGYvGqbJ0wBK3EAAHGAeq3Ti0FCyX/HIgTIWmF4qOKLVzA2B+AZpy3FAwCqCXC2HtCyIMSejCBVHgH7BjKjDIwBtIdxeQCYKaDCUGevBXPo9WMZALt8AnZOKP2EEQAIEoyTPONTDuAEAPOK02pduR5AUMxB8KNVC6VJNK8CYvcINxb9Fy8AYF1ea9QznM468HjjGbQ0zBiPcJovfYMuAH4i1VSUeCdkYLghiNe87solrbkBTnwHMUKNMPAFtzMQlHaqSLCxqJeAF2gVJGye5Mo1L/PFCznRh5o1SHrq/YLnuw+AjLP/Cco4DalgLX4vEKt9x9XrQj79GU7woa0Fhso2P78ZlAMQmlP9VGheIzbjn4UYAS9wx5kiEQdrBUtG6dwfaxRKsDp4RfadQILVwOsF65hP5LPMAulZSiuWgo31ASHG+lvStK+JAIChGgIyrg9Lxo+zXfALfXN87IhQjBWJxFg+WNYwlg1WKJJg+wGSUrB6HNdoEEqSqSHAiI2fmV3zbWh+EzbjzV3EIIjXhJPxFToE4ofYGL1Xw519FAQsHEFwvIY+BwFxcZhrXhQEPUk6AzOzq99DAARnnccIxQJQL0jawrRUOhF+ZDcbxG+wCm8kSXhHZgA7BtdocXD9S3xpV4dYPIU0AGTGJmmIsbEHQSD68P8IeQFYTNpAJozWrWKMveuRq/8c3HcWheIPtjywbxx8n2ZBYBZ5XiCrugwBgGz62ixCEHjHpfyCZAg2MHGebhJILGO3O8UfbAfBK7QOhLK3bYYkzOVOh0DwaRsAgTvPEPUCbWQfHGFNGPUyRnzk8oViWsW3t91gZuGP3xHihmqXZgsy4+kJMw21bTYI/Ld8SRSCXCpiVPhxX9ANwCkQv6Dvzm8BK8/yC/gCXjMzRQFqm+lFATvhvbxMkfgIvH4Hr7fcMzwMnEVcEgUud35KqK95ywYAMv6GPcRmBbC7iAoI2kSSMLq8AUzx2kr4kncy/aSPpAYHTybyvYt5qnt0fn4hAMizej/xJr1IXAqidVMBAgpIUe7Bmoe4Uufv/1PCHS39vHpiSE7dDXsIpr+WSQgC2EMQSVXOAlYTk9wc9HWhwlYMhCTzd+jhEAzwGEuzhOJCsj0FykE0C/pAuAjb5gl/ucDMc6/ZAxACWULPeCJTQ02v19rUyVRBYNlvAHv63AIBHGAB5w7wqc7GZsPGE71QnAACnicLhL0wNKDY5ZJQ2kkMYAwbNzO7tt0egmD9eUJeAHIJF6nuNCQMtRtPJecAALfX9BlhyRwNGZl+4ioyQDhsHRZahEF/wr9AlH5mqT0AyIIIzgzAStzRYagClwKPcAWGm3WYTDaB1nINiOqzRJK/gpBNroJwBkCAeOZb/BDoztXeBUH6NwQhUBe4q7OaBIJJqDafhNrDDrA30WFVTCrfM8JmWwgit7gSRKJAsUUg6cZ1WEaQ8Vs+WiV01RO4WkbmwtpCPtgdgodHHLzMlzzf4jOb0cfhZftJlWiq6SwIxRAf1AkDU0e8kCS1Uj0YAGSwdkDME8RrttDVWejoOfAMr8AJY+mW08YsZwhJO613ei1E+fs7BIFb0dL0VYHMg8eiZoT1ABgijC4MC+dHzg3ozl52BAHyDl7xKfg9QbxmM49rlDS0EReGh3Ugao8Ts4Xblb7SoVP5walVwaF5DgCwms/6bALDgfojTi5qW5ZQ8kcQ9geiIHzqF/g/Qw8F2oqdQwFgyRi+U0SkkETDyUR9g2nkMyDsHSIQoDT20Ami9NMXhoNAvP0rAsOB1shJxFgQejId1SKqirF7QnLreoeDIMRQi8TFcG8145r7hgZRwB8IDQ1CcczdQ0Fy+dPDAdA/JLy1F2+eoJGTxt0gSOLwQgCB5f67IVCXG/BAINVW4h0SevxfSP8pJ417G6xOpuEE4bbOx2dgXgTWBk7ggQDNHnzW6XGC4PxxtFxzrqXDkrF1iRqPN1g04I9B4AZcEIBJdpThHRJWcLK4v+nEYhEEi60jQuAnebf/j2brWu4DcXvxQoDM91+FbttvwDUnhgVRwJKRg0PJ8f4/CClofoQIAJa1BN3ZEWcKVNQYco1IoCj+bAQQfuiPC2QFTX8mCoGl/HxTMRcXMHxYAKG7hk0c+UnCbfHABmcgCM1vHHFYAG+wiZODRm/gF/D3EfIFfWdIhuQ3ZjgHAVpdrMG8X905HAhNvCXG8Zwc9DRU3DpchZJeJNFaIWja6ywEtloD74T0ob2BE0fWco3U3MHvh5kmltogKHEFAsvGFNipDM8wGAqEfZwU9DW09DyMN7hpeU4TjO1nXIWgL39QPtSModeZE8y55p4po2UxCQS8RAYElrRySiXmtSbNEQi7OCno9QYg+NdDzhBk+U03yYLAMjRAUaqDA616PVdrHuPkoDU2+ItjbxDwHOkQWPYpZJ6DNYbBlUjaUi551NeyT8l9ckzK5w0V8m05FYoiQ4XiZE6logxejxoqlTpDpWJdboUiQl1G3unmaF0BRO9wkD7+BxoOrpMNgaX+ILcOlp4/HVxn8OJYFt9gUi40VCj3gdjdYBgOazOYFEnGqohgMq4PAeInd1caBagRBFepgMC26ui/+bB9wNjpsTJNONbEN5ZFirJN8v/FKbwj68muVBiyquQuHQCmE/jLHQwJeQiCTsogsMslPLjeYNutXDSmXH/VnAU5FfJOFwCwty7wDDEuZRFFkjODIDjACy1oaqMaAosZ6zHh+/stZx7BE08Sxob7V/wWhLtNEgD9ll2hyPq8+omJTkKwdhAEZcgTnHULBDavkHEa83k9t9s7Tjt3NAOQUy5XgGA3yQbAzvbrqojvmtrJFwcNmh3UolXEQ+6EwLbV3X/LkeuCt4oeGo0A7DkXMRlE+o5CAKym3FdcTPzMhEHb2dpgimjOcTcE/QtQOXXdoYbaRHQoxqjyApWKrdQDYLVKRbITEOywg6AD5Qm20AWB3SyiISTP/Dw6G4H1SRlTuB+Ic8ttEIDlVCiXEJwqPmMHQSeCYD3tEPxYo3AUXuUsDwaT3AmA1Vr1X0fi3lIPiaOpdvsUrqJ6gt8xBwKL9cLKZjaUvfmxLh9wWjbBkuBxPwQoPviQ0JAANYZWCK7xZu1qlDAMAouhdLYsr2kdesAGa7xAxZzF9ABgsVuZZWG4j7rXCyVJVgi6LGcUQad3MREEqx2QGeseZEViyKTYRCMEyBJxQyCSRPetIopbLW9ARx9nMATIK5hn5Tc+yoJZQRnNEFxIxPCdYGpLIcMu5RorBI3JTIbAajdC88wLGbtmj/HGUZwcwhsb4Hq0ACo3t+xQFgWYrBCYn2UBBBYQZuU1qZgIAVrcoR8ANF1U/JtAvuA8FJUcsvzjIX3DNHC53SwB4dqswvoQ5gWFynAmQIBqEvDPEMQFMBx82v8GLCSdYAkEyE4JjE2TmASBvlL5OEMguIE3lQxB4WZYUNL1vyHLM7/NIgggWDS/w7Cg8PcMgQDDW4SCVhQHbEyVFTQ+xiYIwG6jHAdzMoXyPzAFAlTDgG+aaDlce2B9AmTqqljlDfLM2xnjCUyKJxgDQaX8OVzDgSjgl1CAOvChWbL8xr+xzBtcR1vrmQBBril8HlMgwFt9pBNKZmXBSbAD3pxd2DIdOvYOm0AIKWh6mhGeoErxCFMgyKlUxuIrMJnpaXT01DRY1s1jmTdIYkZgOCeIMRBUyHE9dcayBc1RCylsnAUd28MiCA4wYgWxUslnTExQofiryz8I0si5LILgNCMgKAubwqDh4Ncu/yCUkWNRBrGWERAYl4yHVURmeAJTuJKUHwUFqFqWQGBiAgQvps311pczBIKyMC9SfpTYWDuFyNF29NUnmvcwAYJXNAtiMkuVTICgjdQfJis0LyB6vB0NELzBDAhUJ3VMgMCk/JL0H4emYAz3BrQXpkar5z30imY+llWmZNVSMu6G9gSEFpiPMLTi6BwzvMD8lD4I6I8H9BXyJyn5kUHZzV7u3q6GcyhYTjcAy7QLfQCA76MBAnDFdOcHrqUXq6g7WDykoH4Gw0A4H6Yuu5f+oWD+B8gLxKSq6I8H4IALyn+wzNjkQcfeRQfWE1LQOI9uAGKSF/ABgBsIgrgM+iGAwhb3nCDbFyM0ZdG7aNT4D4ZMC9MRAMgSMqPohqDY7R0AO5dWUHHe0YjBICSxmLBnMVqtigTxe20QrDfOoxmCOYtp6YjQ/AYZCFPhxtnAFiYAsMS4ZALEAqdsACDbsGcunRCYUMk7jUlzbDyIE0vxsTfXLbuWGdJswaC9ffBFJF0A9JC2VkDKNDKvcSPpB2FBWnjmrjp/pgAAmcFFIHrPYAiSisPpSQ6Z5Dt4TGt9exjMCSBgpQvi/wC2O6TArGDSb4tJVvmD4K2DAUCWcZKWHMFl0haLqIwZEBCwu/gzELV5BOGb+6qazDGozI1pv2XldtX9IHaVIwBWptMyPeyFCqKneGxrD++qmzorv2k2muOHFFxYNNPYFIX+Lc2qfoDJ3xsFgsu0879wBIBlZpA7b3SsEXDNcYvb+sREyAfsHQoAmoLCYmcOrOKaE22p7lf3gcj7hgMAWcoxtwaFZuPJxx7k1HFDeyn1l76oRmAkANyZLkYno+orwrlnR7ijLd+xUAkCN48EALK33JckumowzXmUU4f6AHA8BIBvgLh38ADgxqHgZrZJGcWs3oIU5fId838OmbPFiYmjI0ABQWVgR/GKj+zV7Ci31AjkmhSLmOw2Q6EzdgMMa19OedyDjeK/kK6aCmP/h0TufpttPhBB9WmlF6FG4BdscKEToEP+DdYFpkGra4kseMLJS6kRk6PVC9bDd+4gKj6yNToVRvE+gxpjuVLKsmBqwTzonLPWTjIDDB+hAItH6+rW3e3llAV+1gWgK86Ib7OtxREUDgHywztPzPFka2CFllj/iWru7DqsAWwTBFxPQuKFlswg2hgC138e7ICjxR+i9pqBsgxhDzwXaeOoSAS9nKKSwji730EHdoOVIS8B9jswL6pgXKZVqeBaG9H1yBDeZjGpMCM4TsliURs66GLUTbmg054FqxmhY2FVTvUlKttGASaCAyp4Z7+Utki84pPIaY5mH+g99H/ItcPfPgyC/xHG90T4+xz4nIpBnohU+4iCYBBmAAfRruZRO/eOVofdCwLFQgdecqHzkaht1iDuFlUCj2T/KiR9GOgAAJZhDIubKI3GoSM3WGcRGNtsbU4URvKG072j+u4frqHgEFz4arxpWSbYutwoLKtcSZbrPz0qx34nYZiIInbo5G+ZDMCbhXMxPSk7i5S16Kmo6BwDTn0HQR4KBqHDi6yzB0aIH5OiwjbtJ6VO4ALc+SvIfOTtqG4vps0TQqT/pjW3QBsAqFIo7SuX7/6TcMDlUmefX8h5B0g7w1ARhqZ9IEq1u8R/zRCFfXLYlSmgHD0IM9NQpZBzKpK8Ygnzfzl4iPesRR6kDhmroEh0w+65mLZE6eT5QApMUxLesu1Q+MdG01xvTjA3NJQkgpq/pwGMbeApTERX/1alQbQP072NRZFY8pFwFK0T3/hpUvRojoY3bDkYuX1rsUrAqcKAWcbyHapHIbhcHq+L0r2RP6/83c/mtr/7WWTP+59HYv/ZF4l9fDACU4PgO53cG6ArU97SHgs/s+1QRHrSoYgnuCJPFjTjMeUk3XGFXF0S/nfNsXAdBHnF6V8pz+pOKlsySxVX9GWK75HBySI3dKXhnbpSRVvGCaU5/YSyJuWY8qi2RJGWWhL+t+yTyqixUNT5/9icztE89sbGAAAAAElFTkSuQmCC';\nexport class RoundInfoViewModalComponent {\n  constructor(spinService, {\n    roundInfo\n  }, roundService, authService, jurisdictionService) {\n    this.spinService = spinService;\n    this.roundService = roundService;\n    this.showSmResult = true;\n    this.noImage = noImage;\n    this.subscriptions = [];\n    this.isSuperAdmin = authService.isSuperAdmin;\n    this.roundInfo = roundInfo;\n    this.path = this.roundInfo._meta.fullPath;\n    this.smResultAvailable$ = jurisdictionService.entityJurisdiction.pipe(map(codes => codes.includes('PT')));\n    this.subscriptions.push(spinService._items.subscribe(data => {\n      this.spinItemsResponse = data;\n    }), spinService._item.subscribe(data => {\n      this.spinItem = data;\n    }));\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(subscription => subscription.unsubscribe());\n  }\n  footerBack() {\n    this.spinService._item.next();\n  }\n  footerPrev() {\n    this.spinService.getPrev();\n  }\n  footerNext() {\n    this.spinService.getNext();\n  }\n  sendEmail() {\n    const mailto = 'mailto:<EMAIL>';\n    const subject = encodeURIComponent('Game Details Request');\n    const body = encodeURIComponent(`Operator ${this.brief.name}\n    ${this.path ? 'Path ' + this.path : ''}\n    Round ID ${this.roundInfo.roundId}\n    Player ID ${this.roundInfo.playerCode}`);\n    window.location.href = `${mailto}?subject=${subject}&body=${body}`;\n  }\n  isSpinTypeAllow(type) {\n    return ['force-finish', 'revert-game', 'finalize', 'noMoreBets', 'roundEnded', 'roundCanceled', 'rushBet'].indexOf(type) === -1;\n  }\n  showSmResultEmitter(event) {\n    this.showSmResult = event;\n  }\n  downloadSmResult() {\n    this.roundService.getGameHistorySmResult(this.path, this.roundInfo.roundId.toString()).pipe(take(1)).subscribe(data => {\n      const {\n        name\n      } = this.roundInfo._meta;\n      const filename = `RoundId [${this.roundInfo.roundId}] entity name [${name}]`;\n      simulateBrowserDownload(data, filename, FileType.Txt);\n    });\n  }\n  static {\n    this.ɵfac = function RoundInfoViewModalComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RoundInfoViewModalComponent)(i0.ɵɵdirectiveInject(i1.GameHistorySpinService), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA), i0.ɵɵdirectiveInject(i2.GameHistoryService), i0.ɵɵdirectiveInject(i3.SwHubAuthService), i0.ɵɵdirectiveInject(i4.JurisdictionService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RoundInfoViewModalComponent,\n      selectors: [[\"round-info-view-modal\"]],\n      decls: 8,\n      vars: 7,\n      consts: [[3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [\"align\", \"end\"], [4, \"ngIf\"], [\"mat-button\", \"\", \"color\", \"primary\", \"mat-dialog-close\", \"\", 1, \"mat-button-md\"], [1, \"info-header\"], [1, \"info-header__part-1\"], [1, \"info-header__logo\"], [\"alt\", \"\", 3, \"src\"], [1, \"info-header__body\"], [1, \"info-header__row\"], [1, \"info-header__title\"], [1, \"info-header__chip\", \"sw-chip\", 3, \"ngClass\"], [\"class\", \"info-header__chip sw-chip\", 4, \"ngIf\"], [1, \"info-header__item\"], [1, \"info-header__label\"], [1, \"info-header__value\"], [\"class\", \"info-header__item\", 4, \"ngIf\"], [1, \"info-header__part-2\"], [\"mat-flat-button\", \"\", \"color\", \"primary\", 3, \"click\", 4, \"ngIf\"], [\"mat-dialog-content\", \"\"], [3, \"showSmResult\", \"roundInfo\", \"path\"], [1, \"info-header__chip\", \"sw-chip\"], [\"mat-flat-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"alert-message\"], [1, \"alert-message__icon\"], [\"fontSet\", \"material-icons-outline\"], [1, \"alert-message__message\"], [1, \"link\", 3, \"click\"], [\"mat-stroked-button\", \"\", 3, \"click\", 4, \"ngIf\"], [\"mat-stroked-button\", \"\", 3, \"click\"]],\n      template: function RoundInfoViewModalComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementContainerStart(0, 0);\n          i0.ɵɵtemplate(1, RoundInfoViewModalComponent_ng_container_1_Template, 35, 24, \"ng-container\", 1)(2, RoundInfoViewModalComponent_ng_container_2_Template, 16, 1, \"ng-container\", 1);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementStart(3, \"mat-dialog-actions\", 2);\n          i0.ɵɵtemplate(4, RoundInfoViewModalComponent_ng_container_4_Template, 4, 3, \"ng-container\", 3);\n          i0.ɵɵelementStart(5, \"button\", 4);\n          i0.ɵɵtext(6);\n          i0.ɵɵpipe(7, \"translate\");\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngSwitch\", !!(ctx.roundInfo == null ? null : ctx.roundInfo._meta));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngSwitchCase\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngSwitchCase\", false);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", (ctx.spinItemsResponse == null ? null : ctx.spinItemsResponse.length) > 1 && ctx.spinItem !== undefined);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(7, 5, \"DIALOG.close\"), \" \");\n        }\n      },\n      dependencies: [i5.NgClass, i5.NgIf, i5.NgSwitch, i5.NgSwitchCase, i6.RoundInfoComponent, i7.MatButton, i8.MatDialogClose, i8.MatDialogActions, i8.MatDialogContent, i9.MatIcon, i10.DefaultClassDirective, i5.AsyncPipe, i11.TranslatePipe],\n      styles: [\".info-header[_ngcontent-%COMP%] {\\n  width: 100%;\\n  display: flex;\\n}\\n.info-header__part-1[_ngcontent-%COMP%], .info-header__part-2[_ngcontent-%COMP%] {\\n  width: 50%;\\n  display: flex;\\n  align-items: center;\\n}\\n.info-header__part-2[_ngcontent-%COMP%] {\\n  justify-content: end;\\n  padding-right: 1em;\\n  align-items: flex-start;\\n}\\n.info-header__logo[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n}\\n.info-header__logo[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  display: block;\\n  width: 100%;\\n  height: auto;\\n}\\n.info-header__body[_ngcontent-%COMP%] {\\n  padding-left: 24px;\\n}\\n.info-header__title[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 500;\\n}\\n.info-header__title[_ngcontent-%COMP%] {\\n  margin-right: 10px;\\n}\\n.info-header__chip[_ngcontent-%COMP%] {\\n  margin-right: 10px;\\n}\\n.info-header__item[_ngcontent-%COMP%] {\\n  display: flex;\\n  font-size: 14px;\\n  margin-right: 10px;\\n}\\n.info-header__label[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-right: 4px;\\n  font-weight: 500;\\n}\\n.info-header__row[_ngcontent-%COMP%] {\\n  display: flex;\\n}\\n\\n.alert-message[_ngcontent-%COMP%] {\\n  display: flex;\\n  padding: 6px 16px;\\n  color: rgb(102, 60, 0);\\n  background-color: rgb(255, 244, 229);\\n}\\n.alert-message__icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  opacity: 0.9;\\n  padding: 11px 0;\\n  margin-right: 12px;\\n  color: #ff9800;\\n}\\n.alert-message__icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 44px;\\n  width: 44px;\\n  height: 44px;\\n  line-height: 44px;\\n}\\n.alert-message__message[_ngcontent-%COMP%] {\\n  padding: 8px 0;\\n}\\n\\nmat-dialog-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  margin-left: 8px;\\n}\\nmat-dialog-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:first-child {\\n  margin-left: 0;\\n}\\nmat-dialog-actions[_ngcontent-%COMP%]   button.close-button[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["map", "take", "FileType", "simulateBrowserDownload", "MAT_DIALOG_DATA", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpipeBind1", "ɵɵtextInterpolate1", "ctx_r1", "roundInfo", "extraData", "extRoundId", "ɵɵlistener", "RoundInfoViewModalComponent_ng_container_1_button_31_Template_button_click_0_listener", "ɵɵrestoreView", "_r3", "ɵɵnextContext", "ɵɵresetView", "downloadSmResult", "ɵɵelementContainerStart", "ɵɵelement", "ɵɵtemplate", "RoundInfoViewModalComponent_ng_container_1_span_15_Template", "RoundInfoViewModalComponent_ng_container_1_div_23_Template", "RoundInfoViewModalComponent_ng_container_1_button_31_Template", "RoundInfoViewModalComponent_ng_container_1_Template_round_info_showSmResult_34_listener", "$event", "_r1", "showSmResultEmitter", "ɵɵproperty", "noImage", "ɵɵsanitizeUrl", "gameNameLabel", "gameCode", "ɵɵpureFunction1", "_c0", "finished", "isTest", "roundId", "playerCode", "isSuperAdmin", "showSmResult", "smResultAvailable$", "path", "RoundInfoViewModalComponent_ng_container_2_Template_a_click_13_listener", "_r4", "sendEmail", "RoundInfoViewModalComponent_ng_container_4_button_1_Template_button_click_0_listener", "_r5", "footerBack", "RoundInfoViewModalComponent_ng_container_4_button_2_Template_button_click_0_listener", "_r6", "footer<PERSON>rev", "RoundInfoViewModalComponent_ng_container_4_button_3_Template_button_click_0_listener", "_r7", "footerNext", "RoundInfoViewModalComponent_ng_container_4_button_1_Template", "RoundInfoViewModalComponent_ng_container_4_button_2_Template", "RoundInfoViewModalComponent_ng_container_4_button_3_Template", "spinItem", "hasNext", "has<PERSON>rev", "isSpinTypeAllow", "prevType", "nextType", "RoundInfoViewModalComponent", "constructor", "spinService", "roundService", "authService", "jurisdictionService", "subscriptions", "_meta", "fullPath", "entityJurisdiction", "pipe", "codes", "includes", "push", "_items", "subscribe", "data", "spinItemsResponse", "_item", "ngOnDestroy", "for<PERSON>ach", "subscription", "unsubscribe", "next", "getPrev", "getNext", "mailto", "subject", "encodeURIComponent", "body", "brief", "name", "window", "location", "href", "type", "indexOf", "event", "getGameHistorySmResult", "toString", "filename", "Txt", "ɵɵdirectiveInject", "i1", "GameHistorySpinService", "i2", "GameHistoryService", "i3", "SwHubAuthService", "i4", "JurisdictionService", "selectors", "decls", "vars", "consts", "template", "RoundInfoViewModalComponent_Template", "rf", "ctx", "RoundInfoViewModalComponent_ng_container_1_Template", "RoundInfoViewModalComponent_ng_container_2_Template", "RoundInfoViewModalComponent_ng_container_4_Template", "length", "undefined"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/gamehistory/components/internal-game-history/modals/round-info-view-modal.component.ts", "/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/gamehistory/components/internal-game-history/modals/round-info-view-modal.component.html"], "sourcesContent": ["import { Component, Inject, OnDestroy } from '@angular/core';\nimport { SwHubAuthService } from '@skywind-group/lib-swui';\nimport { Observable, Subscription } from 'rxjs';\nimport { map, take } from 'rxjs/operators';\nimport { FileType, simulateBrowserDownload } from '../../../../../common/lib/files';\nimport { JurisdictionService } from '../../../../../common/services/jurisdiction.service';\nimport { GameHistoryService } from '../../../../../common/services/reports/gamehistory.service';\nimport { GameHistorySpinService } from '../../../../../common/services/reports/gamehistory.spin.service';\n\nimport { GameHistory, GameHistorySpin } from '../../../../../common/typings';\nimport { GameHistoryModalInfo } from './iframe-view-modal/iframe-view-modal.component';\nimport { MAT_DIALOG_DATA } from '@angular/material/dialog';\n\n// tslint:disable-next-line\nconst noImage = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAIEAAACACAYAAAAs/Ar1AAAABmJLR0QA/wD/AP+gvaeTAAAUEElEQVR42u2dC1RTV9bH49jqOK1VeUglDyAJIMQ6ndKaBFDiY6ardWY6nY6dWTO104dFVECZadX2m5liH7bTjtaKFpPwMhAC4aHW0n46fqJVtAoEqPVRgfKKiCCgolYtcL99QkIDBrg3uTf3Xrhnrb2yjIvc5Px/d5999tnnXB6P5c1rlfoRr3htGC8x8Sc8ro2d5hG39QEQPtozTrPHK1ar4npkDLUpsdoAz3hNMoh/HSzJ51XdfVyvjJk7f0coiK4D+8EzTt3oFa+eT+X1Wr1l93c9KPW+IhJNw3i8cZwCNDbvNZqHPWM1hSB+DxjmEas97BOTPJ2Mz8bCwu7tFEijLvOlr7cLpcZ2gfQEWCPYTTBskPWAtXUIpeXtfGlOO1/yNvzd0k5+0M8BEi4WoebO1wo8YtVqEL4bid8HgFrNi1bf66rwHb7S37TzAw0gapcDsZ2xjstC6aftQsmrlwVBczgoXL3zV26/H1x9Ioh+0yY+2C3wAC+78rlw9wraBZIPQLDLJAk/nF2Aa21p95WEc0MJsXt0HAj9olecps1OfGQtnnEpc5z91E7/mf7twsAMEOa2G8R3ZA0IviuiIDGn8TDNJyFNDBH//kHiI2v2WKWROXXnS6UPXBZI3wcRvqdJ/Ltiig6BdFebQMpNZwe0xMR7PGI1q63TvcEA1E+PU0uc+dg2vmQ+dHo9Q8R3ZJUAxHNjPnbwWp0aDEKXOxAfTH0OBYaEBxToVLj737NG8hgL7OsOfuDisRn5x6uXDXH3IwBOTV+t9XFmXg93126WiD/YjqOp6pgQf1q0eopHnCbbsfgWuzBttVrkzPgPHfkVSwHoN/Bimdd8g7xGLwCrtbMh8v9uGACuocQQ0c9t9vX9GSR6jrIdADsQWsGj/WX0jf9x6sUg8tVhAOj2itX81okYYJw16YONPgss6vAVi0aB/Ng4EHidLeU7pMVqVjqZAEoYnQD0WydkIp9ir/6Q3gWBs4YV35YKdmaBRyAOHCLHP9qsF5JNH6N0N6v0941W/wzm/0UjAQBWjdLETnkBgXTfGADAziRftgmDfVkBwNQ16VMhADyCA4A7zqaD0XRqbAHQZ5Bt7DgrDPgVowGYvGqbJ0wBK3EAAHGAeq3Ti0FCyX/HIgTIWmF4qOKLVzA2B+AZpy3FAwCqCXC2HtCyIMSejCBVHgH7BjKjDIwBtIdxeQCYKaDCUGevBXPo9WMZALt8AnZOKP2EEQAIEoyTPONTDuAEAPOK02pduR5AUMxB8KNVC6VJNK8CYvcINxb9Fy8AYF1ea9QznM468HjjGbQ0zBiPcJovfYMuAH4i1VSUeCdkYLghiNe87solrbkBTnwHMUKNMPAFtzMQlHaqSLCxqJeAF2gVJGye5Mo1L/PFCznRh5o1SHrq/YLnuw+AjLP/Cco4DalgLX4vEKt9x9XrQj79GU7woa0Fhso2P78ZlAMQmlP9VGheIzbjn4UYAS9wx5kiEQdrBUtG6dwfaxRKsDp4RfadQILVwOsF65hP5LPMAulZSiuWgo31ASHG+lvStK+JAIChGgIyrg9Lxo+zXfALfXN87IhQjBWJxFg+WNYwlg1WKJJg+wGSUrB6HNdoEEqSqSHAiI2fmV3zbWh+EzbjzV3EIIjXhJPxFToE4ofYGL1Xw519FAQsHEFwvIY+BwFxcZhrXhQEPUk6AzOzq99DAARnnccIxQJQL0jawrRUOhF+ZDcbxG+wCm8kSXhHZgA7BtdocXD9S3xpV4dYPIU0AGTGJmmIsbEHQSD68P8IeQFYTNpAJozWrWKMveuRq/8c3HcWheIPtjywbxx8n2ZBYBZ5XiCrugwBgGz62ixCEHjHpfyCZAg2MHGebhJILGO3O8UfbAfBK7QOhLK3bYYkzOVOh0DwaRsAgTvPEPUCbWQfHGFNGPUyRnzk8oViWsW3t91gZuGP3xHihmqXZgsy4+kJMw21bTYI/Ld8SRSCXCpiVPhxX9ANwCkQv6Dvzm8BK8/yC/gCXjMzRQFqm+lFATvhvbxMkfgIvH4Hr7fcMzwMnEVcEgUud35KqK95ywYAMv6GPcRmBbC7iAoI2kSSMLq8AUzx2kr4kncy/aSPpAYHTybyvYt5qnt0fn4hAMizej/xJr1IXAqidVMBAgpIUe7Bmoe4Uufv/1PCHS39vHpiSE7dDXsIpr+WSQgC2EMQSVXOAlYTk9wc9HWhwlYMhCTzd+jhEAzwGEuzhOJCsj0FykE0C/pAuAjb5gl/ucDMc6/ZAxACWULPeCJTQ02v19rUyVRBYNlvAHv63AIBHGAB5w7wqc7GZsPGE71QnAACnicLhL0wNKDY5ZJQ2kkMYAwbNzO7tt0egmD9eUJeAHIJF6nuNCQMtRtPJecAALfX9BlhyRwNGZl+4ioyQDhsHRZahEF/wr9AlH5mqT0AyIIIzgzAStzRYagClwKPcAWGm3WYTDaB1nINiOqzRJK/gpBNroJwBkCAeOZb/BDoztXeBUH6NwQhUBe4q7OaBIJJqDafhNrDDrA30WFVTCrfM8JmWwgit7gSRKJAsUUg6cZ1WEaQ8Vs+WiV01RO4WkbmwtpCPtgdgodHHLzMlzzf4jOb0cfhZftJlWiq6SwIxRAf1AkDU0e8kCS1Uj0YAGSwdkDME8RrttDVWejoOfAMr8AJY+mW08YsZwhJO613ei1E+fs7BIFb0dL0VYHMg8eiZoT1ABgijC4MC+dHzg3ozl52BAHyDl7xKfg9QbxmM49rlDS0EReGh3Ugao8Ts4Xblb7SoVP5walVwaF5DgCwms/6bALDgfojTi5qW5ZQ8kcQ9geiIHzqF/g/Qw8F2oqdQwFgyRi+U0SkkETDyUR9g2nkMyDsHSIQoDT20Ami9NMXhoNAvP0rAsOB1shJxFgQejId1SKqirF7QnLreoeDIMRQi8TFcG8145r7hgZRwB8IDQ1CcczdQ0Fy+dPDAdA/JLy1F2+eoJGTxt0gSOLwQgCB5f67IVCXG/BAINVW4h0SevxfSP8pJ417G6xOpuEE4bbOx2dgXgTWBk7ggQDNHnzW6XGC4PxxtFxzrqXDkrF1iRqPN1g04I9B4AZcEIBJdpThHRJWcLK4v+nEYhEEi60jQuAnebf/j2brWu4DcXvxQoDM91+FbttvwDUnhgVRwJKRg0PJ8f4/CClofoQIAJa1BN3ZEWcKVNQYco1IoCj+bAQQfuiPC2QFTX8mCoGl/HxTMRcXMHxYAKG7hk0c+UnCbfHABmcgCM1vHHFYAG+wiZODRm/gF/D3EfIFfWdIhuQ3ZjgHAVpdrMG8X905HAhNvCXG8Zwc9DRU3DpchZJeJNFaIWja6ywEtloD74T0ob2BE0fWco3U3MHvh5kmltogKHEFAsvGFNipDM8wGAqEfZwU9DW09DyMN7hpeU4TjO1nXIWgL39QPtSModeZE8y55p4po2UxCQS8RAYElrRySiXmtSbNEQi7OCno9QYg+NdDzhBk+U03yYLAMjRAUaqDA616PVdrHuPkoDU2+ItjbxDwHOkQWPYpZJ6DNYbBlUjaUi551NeyT8l9ckzK5w0V8m05FYoiQ4XiZE6logxejxoqlTpDpWJdboUiQl1G3unmaF0BRO9wkD7+BxoOrpMNgaX+ILcOlp4/HVxn8OJYFt9gUi40VCj3gdjdYBgOazOYFEnGqohgMq4PAeInd1caBagRBFepgMC26ui/+bB9wNjpsTJNONbEN5ZFirJN8v/FKbwj68muVBiyquQuHQCmE/jLHQwJeQiCTsogsMslPLjeYNutXDSmXH/VnAU5FfJOFwCwty7wDDEuZRFFkjODIDjACy1oaqMaAosZ6zHh+/stZx7BE08Sxob7V/wWhLtNEgD9ll2hyPq8+omJTkKwdhAEZcgTnHULBDavkHEa83k9t9s7Tjt3NAOQUy5XgGA3yQbAzvbrqojvmtrJFwcNmh3UolXEQ+6EwLbV3X/LkeuCt4oeGo0A7DkXMRlE+o5CAKym3FdcTPzMhEHb2dpgimjOcTcE/QtQOXXdoYbaRHQoxqjyApWKrdQDYLVKRbITEOywg6AD5Qm20AWB3SyiISTP/Dw6G4H1SRlTuB+Ic8ttEIDlVCiXEJwqPmMHQSeCYD3tEPxYo3AUXuUsDwaT3AmA1Vr1X0fi3lIPiaOpdvsUrqJ6gt8xBwKL9cLKZjaUvfmxLh9wWjbBkuBxPwQoPviQ0JAANYZWCK7xZu1qlDAMAouhdLYsr2kdesAGa7xAxZzF9ABgsVuZZWG4j7rXCyVJVgi6LGcUQad3MREEqx2QGeseZEViyKTYRCMEyBJxQyCSRPetIopbLW9ARx9nMATIK5hn5Tc+yoJZQRnNEFxIxPCdYGpLIcMu5RorBI3JTIbAajdC88wLGbtmj/HGUZwcwhsb4Hq0ACo3t+xQFgWYrBCYn2UBBBYQZuU1qZgIAVrcoR8ANF1U/JtAvuA8FJUcsvzjIX3DNHC53SwB4dqswvoQ5gWFynAmQIBqEvDPEMQFMBx82v8GLCSdYAkEyE4JjE2TmASBvlL5OEMguIE3lQxB4WZYUNL1vyHLM7/NIgggWDS/w7Cg8PcMgQDDW4SCVhQHbEyVFTQ+xiYIwG6jHAdzMoXyPzAFAlTDgG+aaDlce2B9AmTqqljlDfLM2xnjCUyKJxgDQaX8OVzDgSjgl1CAOvChWbL8xr+xzBtcR1vrmQBBril8HlMgwFt9pBNKZmXBSbAD3pxd2DIdOvYOm0AIKWh6mhGeoErxCFMgyKlUxuIrMJnpaXT01DRY1s1jmTdIYkZgOCeIMRBUyHE9dcayBc1RCylsnAUd28MiCA4wYgWxUslnTExQofiryz8I0si5LILgNCMgKAubwqDh4Ncu/yCUkWNRBrGWERAYl4yHVURmeAJTuJKUHwUFqFqWQGBiAgQvps311pczBIKyMC9SfpTYWDuFyNF29NUnmvcwAYJXNAtiMkuVTICgjdQfJis0LyB6vB0NELzBDAhUJ3VMgMCk/JL0H4emYAz3BrQXpkar5z30imY+llWmZNVSMu6G9gSEFpiPMLTi6BwzvMD8lD4I6I8H9BXyJyn5kUHZzV7u3q6GcyhYTjcAy7QLfQCA76MBAnDFdOcHrqUXq6g7WDykoH4Gw0A4H6Yuu5f+oWD+B8gLxKSq6I8H4IALyn+wzNjkQcfeRQfWE1LQOI9uAGKSF/ABgBsIgrgM+iGAwhb3nCDbFyM0ZdG7aNT4D4ZMC9MRAMgSMqPohqDY7R0AO5dWUHHe0YjBICSxmLBnMVqtigTxe20QrDfOoxmCOYtp6YjQ/AYZCFPhxtnAFiYAsMS4ZALEAqdsACDbsGcunRCYUMk7jUlzbDyIE0vxsTfXLbuWGdJswaC9ffBFJF0A9JC2VkDKNDKvcSPpB2FBWnjmrjp/pgAAmcFFIHrPYAiSisPpSQ6Z5Dt4TGt9exjMCSBgpQvi/wC2O6TArGDSb4tJVvmD4K2DAUCWcZKWHMFl0haLqIwZEBCwu/gzELV5BOGb+6qazDGozI1pv2XldtX9IHaVIwBWptMyPeyFCqKneGxrD++qmzorv2k2muOHFFxYNNPYFIX+Lc2qfoDJ3xsFgsu0879wBIBlZpA7b3SsEXDNcYvb+sREyAfsHQoAmoLCYmcOrOKaE22p7lf3gcj7hgMAWcoxtwaFZuPJxx7k1HFDeyn1l76oRmAkANyZLkYno+orwrlnR7ijLd+xUAkCN48EALK33JckumowzXmUU4f6AHA8BIBvgLh38ADgxqHgZrZJGcWs3oIU5fId838OmbPFiYmjI0ABQWVgR/GKj+zV7Ci31AjkmhSLmOw2Q6EzdgMMa19OedyDjeK/kK6aCmP/h0TufpttPhBB9WmlF6FG4BdscKEToEP+DdYFpkGra4kseMLJS6kRk6PVC9bDd+4gKj6yNToVRvE+gxpjuVLKsmBqwTzonLPWTjIDDB+hAItH6+rW3e3llAV+1gWgK86Ib7OtxREUDgHywztPzPFka2CFllj/iWru7DqsAWwTBFxPQuKFlswg2hgC138e7ICjxR+i9pqBsgxhDzwXaeOoSAS9nKKSwji730EHdoOVIS8B9jswL6pgXKZVqeBaG9H1yBDeZjGpMCM4TsliURs66GLUTbmg054FqxmhY2FVTvUlKttGASaCAyp4Z7+Utki84pPIaY5mH+g99H/ItcPfPgyC/xHG90T4+xz4nIpBnohU+4iCYBBmAAfRruZRO/eOVofdCwLFQgdecqHzkaht1iDuFlUCj2T/KiR9GOgAAJZhDIubKI3GoSM3WGcRGNtsbU4URvKG072j+u4frqHgEFz4arxpWSbYutwoLKtcSZbrPz0qx34nYZiIInbo5G+ZDMCbhXMxPSk7i5S16Kmo6BwDTn0HQR4KBqHDi6yzB0aIH5OiwjbtJ6VO4ALc+SvIfOTtqG4vps0TQqT/pjW3QBsAqFIo7SuX7/6TcMDlUmefX8h5B0g7w1ARhqZ9IEq1u8R/zRCFfXLYlSmgHD0IM9NQpZBzKpK8Ygnzfzl4iPesRR6kDhmroEh0w+65mLZE6eT5QApMUxLesu1Q+MdG01xvTjA3NJQkgpq/pwGMbeApTERX/1alQbQP072NRZFY8pFwFK0T3/hpUvRojoY3bDkYuX1rsUrAqcKAWcbyHapHIbhcHq+L0r2RP6/83c/mtr/7WWTP+59HYv/ZF4l9fDACU4PgO53cG6ArU97SHgs/s+1QRHrSoYgnuCJPFjTjMeUk3XGFXF0S/nfNsXAdBHnF6V8pz+pOKlsySxVX9GWK75HBySI3dKXhnbpSRVvGCaU5/YSyJuWY8qi2RJGWWhL+t+yTyqixUNT5/9icztE89sbGAAAAAElFTkSuQmCC';\n\n@Component({\n  selector: 'round-info-view-modal',\n  templateUrl: './round-info-view-modal.component.html',\n  styleUrls: ['./round-info-view-modal.component.scss']\n})\nexport class RoundInfoViewModalComponent implements OnDestroy {\n  readonly isSuperAdmin: boolean;\n  readonly roundInfo: GameHistory;\n  readonly path: string;\n  readonly smResultAvailable$: Observable<boolean>;\n\n  brief: any;\n  spinItemsResponse;\n  spinItem;\n  showSmResult: boolean = true;\n\n  noImage = noImage;\n\n  private readonly subscriptions: Subscription[] = [];\n\n  constructor(\n    private readonly spinService: GameHistorySpinService<GameHistorySpin>,\n    @Inject(MAT_DIALOG_DATA) { roundInfo }: GameHistoryModalInfo,\n    private readonly roundService: GameHistoryService<GameHistory>,\n    authService: SwHubAuthService,\n    jurisdictionService: JurisdictionService\n  ) {\n    this.isSuperAdmin = authService.isSuperAdmin;\n    this.roundInfo = roundInfo;\n    this.path = this.roundInfo._meta.fullPath;\n\n    this.smResultAvailable$ = jurisdictionService.entityJurisdiction.pipe(map(codes => codes.includes('PT')));\n\n    this.subscriptions.push(\n      spinService._items.subscribe(( data ) => {\n        this.spinItemsResponse = data;\n      }),\n      spinService._item.subscribe(( data ) => {\n        this.spinItem = data;\n      })\n    );\n  }\n\n  ngOnDestroy() {\n    this.subscriptions.forEach(subscription => subscription.unsubscribe());\n  }\n\n  public footerBack() {\n    this.spinService._item.next();\n  }\n\n  public footerPrev() {\n    this.spinService.getPrev();\n  }\n\n  public footerNext() {\n    this.spinService.getNext();\n  }\n\n  public sendEmail() {\n    const mailto = 'mailto:<EMAIL>';\n    const subject = encodeURIComponent('Game Details Request');\n    const body = encodeURIComponent(`Operator ${this.brief.name}\n    ${this.path ? 'Path ' + this.path : ''}\n    Round ID ${this.roundInfo.roundId}\n    Player ID ${this.roundInfo.playerCode}`);\n    window.location.href = `${mailto}?subject=${subject}&body=${body}`;\n  }\n\n  public isSpinTypeAllow( type: string ): boolean {\n    return [\n      'force-finish', 'revert-game', 'finalize', 'noMoreBets', 'roundEnded', 'roundCanceled', 'rushBet'\n    ].indexOf(type) === -1;\n  }\n\n  showSmResultEmitter( event ) {\n    this.showSmResult = event;\n  }\n\n  downloadSmResult() {\n    this.roundService.getGameHistorySmResult(this.path, this.roundInfo.roundId.toString())\n      .pipe(\n        take(1)\n      ).subscribe(( data: string ) => {\n      const { name } = this.roundInfo._meta;\n      const filename = `RoundId [${this.roundInfo.roundId}] entity name [${name}]`;\n      simulateBrowserDownload(data, filename, FileType.Txt);\n    });\n  }\n}\n", "<ng-container [ngSwitch]=\"!!roundInfo?._meta\">\n  <ng-container *ngSwitchCase=\"true\">\n    <div class=\"info-header\">\n      <div class=\"info-header__part-1\">\n        <div class=\"info-header__logo\">\n          <img [src]=\"noImage\" alt>\n        </div>\n        <div class=\"info-header__body\">\n          <div class=\"info-header__row\">\n            <span class=\"info-header__title\">\n              <span>{{roundInfo.gameNameLabel}}</span>\n              <span>({{roundInfo.gameCode}})</span>\n            </span>\n            <span class=\"info-header__chip sw-chip\" [ngClass]=\"{'sw-chip-green': roundInfo.finished}\">\n              {{(roundInfo.finished ? 'GAMEHISTORY.GRID.isFinished' : 'GAMEHISTORY.GRID.unfinished') | translate}}\n            </span>\n            <span *ngIf=\"roundInfo.isTest\" class=\"info-header__chip sw-chip\">{{'GAMEHISTORY.GRID.isTest' |\n              translate}}</span>\n          </div>\n          <div class=\"info-header__row\">\n            <div class=\"info-header__item\">\n              <span class=\"info-header__label\">{{'GAMEHISTORY.GRID.roundId' | translate}}:</span>\n              <span class=\"info-header__value\">{{roundInfo.roundId}}</span>\n            </div>\n            <div class=\"info-header__item\" *ngIf=\"roundInfo?.extraData?.extRoundId\">\n              <span class=\"info-header__label\">{{ 'GAMEHISTORY.GRID.extRoundId' | translate }}:</span>\n              <span class=\"info-header__value\">{{ roundInfo?.extraData?.extRoundId }}</span>\n            </div>\n            <div class=\"info-header__item\">\n              <span class=\"info-header__label\">{{'GAMEHISTORY.GRID.playerCode' | translate}}:</span>\n              <span class=\"info-header__value\">{{roundInfo.playerCode}}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n      <div class=\"info-header__part-2\">\n        <button *ngIf=\"roundInfo.finished && isSuperAdmin && showSmResult && (smResultAvailable$ | async)\"\n          mat-flat-button color=\"primary\" (click)=\"downloadSmResult()\">\n          {{ 'COMPONENTS.GRID.SM_RESULT_DOWNLOAD' | translate }}\n        </button>\n      </div>\n    </div>\n\n    <div mat-dialog-content>\n      <round-info [roundInfo]=\"roundInfo\" [path]=\"path\" (showSmResult)=\"showSmResultEmitter($event)\"></round-info>\n    </div>\n  </ng-container>\n  <ng-container *ngSwitchCase=\"false\">\n    <div mat-dialog-content>\n      <div class=\"alert-message\">\n        <div class=\"alert-message__icon\">\n          <mat-icon fontSet=\"material-icons-outline\">error_outline</mat-icon>\n        </div>\n        <div class=\"alert-message__message\">\n          Game details for <b>Round ID {{roundInfo.roundId}}</b> are temporary unavailable.<br />\n          Please contact Support Team via <a class=\"link\" (click)=\"sendEmail()\">support&#64;skywindgroup.com</a> to\n          retrieve round\n          details.\n        </div>\n      </div>\n    </div>\n  </ng-container>\n</ng-container>\n<mat-dialog-actions align=\"end\">\n  <ng-container *ngIf=\"spinItemsResponse?.length > 1 && spinItem !== undefined\">\n    <button *ngIf=\"spinItem?.hasNext || spinItem?.hasPrev\" mat-stroked-button (click)=\"footerBack()\">\n      {{ 'DIALOG.back' | translate }}\n    </button>\n\n    <button *ngIf=\"spinItem?.hasPrev && isSpinTypeAllow(spinItem?.prevType)\" mat-stroked-button (click)=\"footerPrev()\">\n      {{ 'DIALOG.previous' | translate }}\n    </button>\n\n    <button *ngIf=\"spinItem?.hasNext && isSpinTypeAllow(spinItem?.nextType)\" mat-flat-button color=\"primary\"\n      (click)=\"footerNext()\">\n      {{ 'DIALOG.next' | translate }}\n    </button>\n  </ng-container>\n\n  <button mat-button color=\"primary\" class=\"mat-button-md\" mat-dialog-close>\n    {{ 'DIALOG.close' | translate }}\n  </button>\n</mat-dialog-actions>\n"], "mappings": "AAGA,SAASA,GAAG,EAAEC,IAAI,QAAQ,gBAAgB;AAC1C,SAASC,QAAQ,EAAEC,uBAAuB,QAAQ,iCAAiC;AAOnF,SAASC,eAAe,QAAQ,0BAA0B;;;;;;;;;;;;;;;;;;ICK9CC,EAAA,CAAAC,cAAA,eAAiE;IAAAD,EAAA,CAAAE,MAAA,GACpD;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;IAD6CH,EAAA,CAAAI,SAAA,EACpD;IADoDJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAM,WAAA,kCACpD;;;;;IAQXN,EADF,CAAAC,cAAA,cAAwE,eACrC;IAAAD,EAAA,CAAAE,MAAA,GAAgD;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACxFH,EAAA,CAAAC,cAAA,eAAiC;IAAAD,EAAA,CAAAE,MAAA,GAAsC;IACzEF,EADyE,CAAAG,YAAA,EAAO,EAC1E;;;;IAF6BH,EAAA,CAAAI,SAAA,GAAgD;IAAhDJ,EAAA,CAAAO,kBAAA,KAAAP,EAAA,CAAAM,WAAA,2CAAgD;IAChDN,EAAA,CAAAI,SAAA,GAAsC;IAAtCJ,EAAA,CAAAK,iBAAA,CAAAG,MAAA,CAAAC,SAAA,kBAAAD,MAAA,CAAAC,SAAA,CAAAC,SAAA,kBAAAF,MAAA,CAAAC,SAAA,CAAAC,SAAA,CAAAC,UAAA,CAAsC;;;;;;IAU7EX,EAAA,CAAAC,cAAA,iBAC+D;IAA7BD,EAAA,CAAAY,UAAA,mBAAAC,sFAAA;MAAAb,EAAA,CAAAc,aAAA,CAAAC,GAAA;MAAA,MAAAP,MAAA,GAAAR,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAST,MAAA,CAAAU,gBAAA,EAAkB;IAAA,EAAC;IAC5DlB,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;IADPH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAO,kBAAA,MAAAP,EAAA,CAAAM,WAAA,kDACF;;;;;;IAtCNN,EAAA,CAAAmB,uBAAA,GAAmC;IAG7BnB,EAFJ,CAAAC,cAAA,aAAyB,aACU,aACA;IAC7BD,EAAA,CAAAoB,SAAA,aAAyB;IAC3BpB,EAAA,CAAAG,YAAA,EAAM;IAIAH,EAHN,CAAAC,cAAA,aAA+B,cACC,eACK,WACzB;IAAAD,EAAA,CAAAE,MAAA,GAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACxCH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAwB;IAChCF,EADgC,CAAAG,YAAA,EAAO,EAChC;IACPH,EAAA,CAAAC,cAAA,gBAA0F;IACxFD,EAAA,CAAAE,MAAA,IACF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAqB,UAAA,KAAAC,2DAAA,mBAAiE;IAEnEtB,EAAA,CAAAG,YAAA,EAAM;IAGFH,EAFJ,CAAAC,cAAA,eAA8B,eACG,gBACI;IAAAD,EAAA,CAAAE,MAAA,IAA2C;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnFH,EAAA,CAAAC,cAAA,gBAAiC;IAAAD,EAAA,CAAAE,MAAA,IAAqB;IACxDF,EADwD,CAAAG,YAAA,EAAO,EACzD;IACNH,EAAA,CAAAqB,UAAA,KAAAE,0DAAA,kBAAwE;IAKtEvB,EADF,CAAAC,cAAA,eAA+B,gBACI;IAAAD,EAAA,CAAAE,MAAA,IAA8C;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACtFH,EAAA,CAAAC,cAAA,gBAAiC;IAAAD,EAAA,CAAAE,MAAA,IAAwB;IAIjEF,EAJiE,CAAAG,YAAA,EAAO,EAC5D,EACF,EACF,EACF;IACNH,EAAA,CAAAC,cAAA,eAAiC;IAC/BD,EAAA,CAAAqB,UAAA,KAAAG,6DAAA,qBAC+D;;IAInExB,EADE,CAAAG,YAAA,EAAM,EACF;IAGJH,EADF,CAAAC,cAAA,eAAwB,sBACyE;IAA7CD,EAAA,CAAAY,UAAA,0BAAAa,wFAAAC,MAAA;MAAA1B,EAAA,CAAAc,aAAA,CAAAa,GAAA;MAAA,MAAAnB,MAAA,GAAAR,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAgBT,MAAA,CAAAoB,mBAAA,CAAAF,MAAA,CAA2B;IAAA,EAAC;IAChG1B,EADiG,CAAAG,YAAA,EAAa,EACxG;;;;;IAxCKH,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAA6B,UAAA,QAAArB,MAAA,CAAAsB,OAAA,EAAA9B,EAAA,CAAA+B,aAAA,CAAe;IAKV/B,EAAA,CAAAI,SAAA,GAA2B;IAA3BJ,EAAA,CAAAK,iBAAA,CAAAG,MAAA,CAAAC,SAAA,CAAAuB,aAAA,CAA2B;IAC3BhC,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAO,kBAAA,MAAAC,MAAA,CAAAC,SAAA,CAAAwB,QAAA,MAAwB;IAEQjC,EAAA,CAAAI,SAAA,EAAiD;IAAjDJ,EAAA,CAAA6B,UAAA,YAAA7B,EAAA,CAAAkC,eAAA,KAAAC,GAAA,EAAA3B,MAAA,CAAAC,SAAA,CAAA2B,QAAA,EAAiD;IACvFpC,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAO,kBAAA,MAAAP,EAAA,CAAAM,WAAA,SAAAE,MAAA,CAAAC,SAAA,CAAA2B,QAAA,uEACF;IACOpC,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAA6B,UAAA,SAAArB,MAAA,CAAAC,SAAA,CAAA4B,MAAA,CAAsB;IAKMrC,EAAA,CAAAI,SAAA,GAA2C;IAA3CJ,EAAA,CAAAO,kBAAA,KAAAP,EAAA,CAAAM,WAAA,0CAA2C;IAC3CN,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAK,iBAAA,CAAAG,MAAA,CAAAC,SAAA,CAAA6B,OAAA,CAAqB;IAExBtC,EAAA,CAAAI,SAAA,EAAsC;IAAtCJ,EAAA,CAAA6B,UAAA,SAAArB,MAAA,CAAAC,SAAA,kBAAAD,MAAA,CAAAC,SAAA,CAAAC,SAAA,kBAAAF,MAAA,CAAAC,SAAA,CAAAC,SAAA,CAAAC,UAAA,CAAsC;IAKnCX,EAAA,CAAAI,SAAA,GAA8C;IAA9CJ,EAAA,CAAAO,kBAAA,KAAAP,EAAA,CAAAM,WAAA,6CAA8C;IAC9CN,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAK,iBAAA,CAAAG,MAAA,CAAAC,SAAA,CAAA8B,UAAA,CAAwB;IAMtDvC,EAAA,CAAAI,SAAA,GAAwF;IAAxFJ,EAAA,CAAA6B,UAAA,SAAArB,MAAA,CAAAC,SAAA,CAAA2B,QAAA,IAAA5B,MAAA,CAAAgC,YAAA,IAAAhC,MAAA,CAAAiC,YAAA,IAAAzC,EAAA,CAAAM,WAAA,SAAAE,MAAA,CAAAkC,kBAAA,EAAwF;IAQvF1C,EAAA,CAAAI,SAAA,GAAuB;IAACJ,EAAxB,CAAA6B,UAAA,cAAArB,MAAA,CAAAC,SAAA,CAAuB,SAAAD,MAAA,CAAAmC,IAAA,CAAc;;;;;;IAGrD3C,EAAA,CAAAmB,uBAAA,GAAoC;IAI5BnB,EAHN,CAAAC,cAAA,cAAwB,cACK,cACQ,mBACY;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAC1DF,EAD0D,CAAAG,YAAA,EAAW,EAC/D;IACNH,EAAA,CAAAC,cAAA,cAAoC;IAClCD,EAAA,CAAAE,MAAA,yBAAiB;IAAAF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAACH,EAAA,CAAAE,MAAA,mCAA0B;IAAAF,EAAA,CAAAoB,SAAA,UAAM;IACvFpB,EAAA,CAAAE,MAAA,yCAAgC;IAAAF,EAAA,CAAAC,cAAA,aAAsC;IAAtBD,EAAA,CAAAY,UAAA,mBAAAgC,wEAAA;MAAA5C,EAAA,CAAAc,aAAA,CAAA+B,GAAA;MAAA,MAAArC,MAAA,GAAAR,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAST,MAAA,CAAAsC,SAAA,EAAW;IAAA,EAAC;IAAC9C,EAAA,CAAAE,MAAA,gCAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAACH,EAAA,CAAAE,MAAA,oCAGzG;IAEJF,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;;IANoBH,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAO,kBAAA,cAAAC,MAAA,CAAAC,SAAA,CAAA6B,OAAA,KAA8B;;;;;;IAWxDtC,EAAA,CAAAC,cAAA,iBAAiG;IAAvBD,EAAA,CAAAY,UAAA,mBAAAmC,qFAAA;MAAA/C,EAAA,CAAAc,aAAA,CAAAkC,GAAA;MAAA,MAAAxC,MAAA,GAAAR,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAST,MAAA,CAAAyC,UAAA,EAAY;IAAA,EAAC;IAC9FjD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;IADPH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAO,kBAAA,MAAAP,EAAA,CAAAM,WAAA,2BACF;;;;;;IAEAN,EAAA,CAAAC,cAAA,iBAAmH;IAAvBD,EAAA,CAAAY,UAAA,mBAAAsC,qFAAA;MAAAlD,EAAA,CAAAc,aAAA,CAAAqC,GAAA;MAAA,MAAA3C,MAAA,GAAAR,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAST,MAAA,CAAA4C,UAAA,EAAY;IAAA,EAAC;IAChHpD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;IADPH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAO,kBAAA,MAAAP,EAAA,CAAAM,WAAA,+BACF;;;;;;IAEAN,EAAA,CAAAC,cAAA,iBACyB;IAAvBD,EAAA,CAAAY,UAAA,mBAAAyC,qFAAA;MAAArD,EAAA,CAAAc,aAAA,CAAAwC,GAAA;MAAA,MAAA9C,MAAA,GAAAR,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAST,MAAA,CAAA+C,UAAA,EAAY;IAAA,EAAC;IACtBvD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;IADPH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAO,kBAAA,MAAAP,EAAA,CAAAM,WAAA,2BACF;;;;;IAZFN,EAAA,CAAAmB,uBAAA,GAA8E;IAS5EnB,EARA,CAAAqB,UAAA,IAAAmC,4DAAA,qBAAiG,IAAAC,4DAAA,qBAIkB,IAAAC,4DAAA,qBAK1F;;;;;IAThB1D,EAAA,CAAAI,SAAA,EAA4C;IAA5CJ,EAAA,CAAA6B,UAAA,UAAArB,MAAA,CAAAmD,QAAA,kBAAAnD,MAAA,CAAAmD,QAAA,CAAAC,OAAA,MAAApD,MAAA,CAAAmD,QAAA,kBAAAnD,MAAA,CAAAmD,QAAA,CAAAE,OAAA,EAA4C;IAI5C7D,EAAA,CAAAI,SAAA,EAA8D;IAA9DJ,EAAA,CAAA6B,UAAA,UAAArB,MAAA,CAAAmD,QAAA,kBAAAnD,MAAA,CAAAmD,QAAA,CAAAE,OAAA,KAAArD,MAAA,CAAAsD,eAAA,CAAAtD,MAAA,CAAAmD,QAAA,kBAAAnD,MAAA,CAAAmD,QAAA,CAAAI,QAAA,EAA8D;IAI9D/D,EAAA,CAAAI,SAAA,EAA8D;IAA9DJ,EAAA,CAAA6B,UAAA,UAAArB,MAAA,CAAAmD,QAAA,kBAAAnD,MAAA,CAAAmD,QAAA,CAAAC,OAAA,KAAApD,MAAA,CAAAsD,eAAA,CAAAtD,MAAA,CAAAmD,QAAA,kBAAAnD,MAAA,CAAAmD,QAAA,CAAAK,QAAA,EAA8D;;;AD5D3E;AACA,MAAMlC,OAAO,GAAG,4zNAA4zN;AAO50N,OAAM,MAAOmC,2BAA2B;EAetCC,YACmBC,WAAoD,EAC5C;IAAE1D;EAAS,CAAwB,EAC3C2D,YAA6C,EAC9DC,WAA6B,EAC7BC,mBAAwC;IAJvB,KAAAH,WAAW,GAAXA,WAAW;IAEX,KAAAC,YAAY,GAAZA,YAAY;IAT/B,KAAA3B,YAAY,GAAY,IAAI;IAE5B,KAAAX,OAAO,GAAGA,OAAO;IAEA,KAAAyC,aAAa,GAAmB,EAAE;IASjD,IAAI,CAAC/B,YAAY,GAAG6B,WAAW,CAAC7B,YAAY;IAC5C,IAAI,CAAC/B,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACkC,IAAI,GAAG,IAAI,CAAClC,SAAS,CAAC+D,KAAK,CAACC,QAAQ;IAEzC,IAAI,CAAC/B,kBAAkB,GAAG4B,mBAAmB,CAACI,kBAAkB,CAACC,IAAI,CAAChF,GAAG,CAACiF,KAAK,IAAIA,KAAK,CAACC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;IAEzG,IAAI,CAACN,aAAa,CAACO,IAAI,CACrBX,WAAW,CAACY,MAAM,CAACC,SAAS,CAAGC,IAAI,IAAK;MACtC,IAAI,CAACC,iBAAiB,GAAGD,IAAI;IAC/B,CAAC,CAAC,EACFd,WAAW,CAACgB,KAAK,CAACH,SAAS,CAAGC,IAAI,IAAK;MACrC,IAAI,CAACtB,QAAQ,GAAGsB,IAAI;IACtB,CAAC,CAAC,CACH;EACH;EAEAG,WAAWA,CAAA;IACT,IAAI,CAACb,aAAa,CAACc,OAAO,CAACC,YAAY,IAAIA,YAAY,CAACC,WAAW,EAAE,CAAC;EACxE;EAEOtC,UAAUA,CAAA;IACf,IAAI,CAACkB,WAAW,CAACgB,KAAK,CAACK,IAAI,EAAE;EAC/B;EAEOpC,UAAUA,CAAA;IACf,IAAI,CAACe,WAAW,CAACsB,OAAO,EAAE;EAC5B;EAEOlC,UAAUA,CAAA;IACf,IAAI,CAACY,WAAW,CAACuB,OAAO,EAAE;EAC5B;EAEO5C,SAASA,CAAA;IACd,MAAM6C,MAAM,GAAG,iCAAiC;IAChD,MAAMC,OAAO,GAAGC,kBAAkB,CAAC,sBAAsB,CAAC;IAC1D,MAAMC,IAAI,GAAGD,kBAAkB,CAAC,YAAY,IAAI,CAACE,KAAK,CAACC,IAAI;MACzD,IAAI,CAACrD,IAAI,GAAG,OAAO,GAAG,IAAI,CAACA,IAAI,GAAG,EAAE;eAC3B,IAAI,CAAClC,SAAS,CAAC6B,OAAO;gBACrB,IAAI,CAAC7B,SAAS,CAAC8B,UAAU,EAAE,CAAC;IACxC0D,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAGR,MAAM,YAAYC,OAAO,SAASE,IAAI,EAAE;EACpE;EAEOhC,eAAeA,CAAEsC,IAAY;IAClC,OAAO,CACL,cAAc,EAAE,aAAa,EAAE,UAAU,EAAE,YAAY,EAAE,YAAY,EAAE,eAAe,EAAE,SAAS,CAClG,CAACC,OAAO,CAACD,IAAI,CAAC,KAAK,CAAC,CAAC;EACxB;EAEAxE,mBAAmBA,CAAE0E,KAAK;IACxB,IAAI,CAAC7D,YAAY,GAAG6D,KAAK;EAC3B;EAEApF,gBAAgBA,CAAA;IACd,IAAI,CAACkD,YAAY,CAACmC,sBAAsB,CAAC,IAAI,CAAC5D,IAAI,EAAE,IAAI,CAAClC,SAAS,CAAC6B,OAAO,CAACkE,QAAQ,EAAE,CAAC,CACnF7B,IAAI,CACH/E,IAAI,CAAC,CAAC,CAAC,CACR,CAACoF,SAAS,CAAGC,IAAY,IAAK;MAC/B,MAAM;QAAEe;MAAI,CAAE,GAAG,IAAI,CAACvF,SAAS,CAAC+D,KAAK;MACrC,MAAMiC,QAAQ,GAAG,YAAY,IAAI,CAAChG,SAAS,CAAC6B,OAAO,kBAAkB0D,IAAI,GAAG;MAC5ElG,uBAAuB,CAACmF,IAAI,EAAEwB,QAAQ,EAAE5G,QAAQ,CAAC6G,GAAG,CAAC;IACvD,CAAC,CAAC;EACJ;;;uCAnFWzC,2BAA2B,EAAAjE,EAAA,CAAA2G,iBAAA,CAAAC,EAAA,CAAAC,sBAAA,GAAA7G,EAAA,CAAA2G,iBAAA,CAiB5B5G,eAAe,GAAAC,EAAA,CAAA2G,iBAAA,CAAAG,EAAA,CAAAC,kBAAA,GAAA/G,EAAA,CAAA2G,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAAjH,EAAA,CAAA2G,iBAAA,CAAAO,EAAA,CAAAC,mBAAA;IAAA;EAAA;;;YAjBdlD,2BAA2B;MAAAmD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCrBxC1H,EAAA,CAAAmB,uBAAA,MAA8C;UA+C5CnB,EA9CA,CAAAqB,UAAA,IAAAuG,mDAAA,4BAAmC,IAAAC,mDAAA,2BA8CC;;UAgBtC7H,EAAA,CAAAC,cAAA,4BAAgC;UAC9BD,EAAA,CAAAqB,UAAA,IAAAyG,mDAAA,0BAA8E;UAe9E9H,EAAA,CAAAC,cAAA,gBAA0E;UACxED,EAAA,CAAAE,MAAA,GACF;;UACFF,EADE,CAAAG,YAAA,EAAS,EACU;;;UAlFPH,EAAA,CAAA6B,UAAA,gBAAA8F,GAAA,CAAAlH,SAAA,kBAAAkH,GAAA,CAAAlH,SAAA,CAAA+D,KAAA,EAA+B;UAC5BxE,EAAA,CAAAI,SAAA,EAAkB;UAAlBJ,EAAA,CAAA6B,UAAA,sBAAkB;UA8ClB7B,EAAA,CAAAI,SAAA,EAAmB;UAAnBJ,EAAA,CAAA6B,UAAA,uBAAmB;UAiBnB7B,EAAA,CAAAI,SAAA,GAA6D;UAA7DJ,EAAA,CAAA6B,UAAA,UAAA8F,GAAA,CAAAzC,iBAAA,kBAAAyC,GAAA,CAAAzC,iBAAA,CAAA6C,MAAA,SAAAJ,GAAA,CAAAhE,QAAA,KAAAqE,SAAA,CAA6D;UAgB1EhI,EAAA,CAAAI,SAAA,GACF;UADEJ,EAAA,CAAAO,kBAAA,MAAAP,EAAA,CAAAM,WAAA,4BACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}