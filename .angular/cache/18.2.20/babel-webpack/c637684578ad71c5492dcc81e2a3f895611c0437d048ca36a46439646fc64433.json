{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FlexLayoutModule } from '@angular/flex-layout';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatRadioModule } from '@angular/material/radio';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiChipsAutocompleteModule, SwuiControlMessagesModule, SwuiGamesSelectManagerModule, SwuiGridModule, SwuiSelectModule } from '@skywind-group/lib-swui';\nimport { ControlMessagesModule } from '../../../../../common/components/control-messages/control-messages.module';\nimport { HintsModule } from '../../../../../common/components/hints/hints.module';\nimport { BaIfAllowedModule } from '../../../../../common/directives/baIfAllowed/baIfAllowed.module';\nimport { PipesModule } from '../../../../../common/pipes/pipes.module';\nimport { GameGroupFiltersService } from '../../../../../common/services/game-group-filters.service';\nimport { GameGroupService } from '../../../../../common/services/game-group.service';\nimport { CurrenciesResolver } from '../../../../../common/services/resolvers/currencies.resolver';\nimport { GameGroupFiltersDeleteDialogComponent } from './game-group-filters-delete-dialog/game-group-filters-delete-dialog.component';\nimport { GameGroupFiltersDialogComponent } from './game-group-filters-dialog/game-group-filters-dialog.component';\nimport { GameGroupFiltersComponent } from './game-group-filters.component';\nimport { TabGameGroupFiltersComponent } from './tab-game-group-filters.component';\nimport { TabGameGroupFiltersRoutingModule } from './tab-game-group-filters.routing';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nexport class TabGameGroupFiltersModule {\n  static {\n    this.ɵfac = function TabGameGroupFiltersModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TabGameGroupFiltersModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: TabGameGroupFiltersModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [GameGroupService, GameGroupFiltersService, CurrenciesResolver],\n      imports: [CommonModule, TranslateModule.forChild(), ReactiveFormsModule, FormsModule, TabGameGroupFiltersRoutingModule, ControlMessagesModule, BaIfAllowedModule, PipesModule, MatButtonModule, MatFormFieldModule, MatIconModule, MatTooltipModule, SwuiSelectModule, SwuiControlMessagesModule, SwuiChipsAutocompleteModule, SwuiGamesSelectManagerModule, SwuiGridModule, MatDialogModule, MatInputModule, MatCheckboxModule, FlexLayoutModule, MatRadioModule, HintsModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(TabGameGroupFiltersModule, {\n    declarations: [TabGameGroupFiltersComponent, GameGroupFiltersComponent, GameGroupFiltersDialogComponent, GameGroupFiltersDeleteDialogComponent],\n    imports: [CommonModule, i1.TranslateModule, ReactiveFormsModule, FormsModule, TabGameGroupFiltersRoutingModule, ControlMessagesModule, BaIfAllowedModule, PipesModule, MatButtonModule, MatFormFieldModule, MatIconModule, MatTooltipModule, SwuiSelectModule, SwuiControlMessagesModule, SwuiChipsAutocompleteModule, SwuiGamesSelectManagerModule, SwuiGridModule, MatDialogModule, MatInputModule, MatCheckboxModule, FlexLayoutModule, MatRadioModule, HintsModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FlexLayoutModule", "FormsModule", "ReactiveFormsModule", "MatButtonModule", "MatCheckboxModule", "MatDialogModule", "MatFormFieldModule", "MatIconModule", "MatInputModule", "MatRadioModule", "MatTooltipModule", "TranslateModule", "SwuiChipsAutocompleteModule", "SwuiControlMessagesModule", "SwuiGamesSelectManagerModule", "SwuiGridModule", "SwuiSelectModule", "ControlMessagesModule", "HintsModule", "BaIfAllowedModule", "PipesModule", "GameGroupFiltersService", "GameGroupService", "CurrenciesResolver", "GameGroupFiltersDeleteDialogComponent", "GameGroupFiltersDialogComponent", "GameGroupFiltersComponent", "TabGameGroupFiltersComponent", "TabGameGroupFiltersRoutingModule", "TabGameGroupFiltersModule", "imports", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "i1"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-game-group-filters/tab-game-group-filters.module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { FlexLayoutModule } from '@angular/flex-layout';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatRadioModule } from '@angular/material/radio';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { TranslateModule } from '@ngx-translate/core';\nimport {\n  SwuiChipsAutocompleteModule, SwuiControlMessagesModule, SwuiGamesSelectManagerModule, SwuiGridModule, SwuiSelectModule\n} from '@skywind-group/lib-swui';\nimport { ControlMessagesModule } from '../../../../../common/components/control-messages/control-messages.module';\nimport { HintsModule } from '../../../../../common/components/hints/hints.module';\nimport { BaIfAllowedModule } from '../../../../../common/directives/baIfAllowed/baIfAllowed.module';\nimport { PipesModule } from '../../../../../common/pipes/pipes.module';\nimport { GameGroupFiltersService } from '../../../../../common/services/game-group-filters.service';\nimport { GameGroupService } from '../../../../../common/services/game-group.service';\nimport { CurrenciesResolver } from '../../../../../common/services/resolvers/currencies.resolver';\nimport { GameGroupFiltersDeleteDialogComponent } from './game-group-filters-delete-dialog/game-group-filters-delete-dialog.component';\nimport { GameGroupFiltersDialogComponent } from './game-group-filters-dialog/game-group-filters-dialog.component';\nimport { GameGroupFiltersComponent } from './game-group-filters.component';\nimport { TabGameGroupFiltersComponent } from './tab-game-group-filters.component';\nimport { TabGameGroupFiltersRoutingModule } from './tab-game-group-filters.routing';\n\n@NgModule({\n  imports: [\n    CommonModule,\n    TranslateModule.forChild(),\n    ReactiveFormsModule,\n    FormsModule,\n    TabGameGroupFiltersRoutingModule,\n    ControlMessagesModule,\n    BaIfAllowedModule,\n    PipesModule,\n    MatButtonModule,\n    MatFormFieldModule,\n    MatIconModule,\n    MatTooltipModule,\n    SwuiSelectModule,\n    SwuiControlMessagesModule,\n    SwuiChipsAutocompleteModule,\n    SwuiGamesSelectManagerModule,\n    SwuiGridModule,\n    MatDialogModule,\n    MatInputModule,\n    MatCheckboxModule,\n    FlexLayoutModule,\n    MatRadioModule,\n    HintsModule,\n  ],\n  declarations: [\n    TabGameGroupFiltersComponent,\n    GameGroupFiltersComponent,\n    GameGroupFiltersDialogComponent,\n    GameGroupFiltersDeleteDialogComponent\n  ],\n  providers: [\n    GameGroupService,\n    GameGroupFiltersService,\n    CurrenciesResolver,\n  ],\n})\nexport class TabGameGroupFiltersModule {\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SACEC,2BAA2B,EAAEC,yBAAyB,EAAEC,4BAA4B,EAAEC,cAAc,EAAEC,gBAAgB,QACjH,yBAAyB;AAChC,SAASC,qBAAqB,QAAQ,2EAA2E;AACjH,SAASC,WAAW,QAAQ,qDAAqD;AACjF,SAASC,iBAAiB,QAAQ,iEAAiE;AACnG,SAASC,WAAW,QAAQ,0CAA0C;AACtE,SAASC,uBAAuB,QAAQ,2DAA2D;AACnG,SAASC,gBAAgB,QAAQ,mDAAmD;AACpF,SAASC,kBAAkB,QAAQ,8DAA8D;AACjG,SAASC,qCAAqC,QAAQ,+EAA+E;AACrI,SAASC,+BAA+B,QAAQ,iEAAiE;AACjH,SAASC,yBAAyB,QAAQ,gCAAgC;AAC1E,SAASC,4BAA4B,QAAQ,oCAAoC;AACjF,SAASC,gCAAgC,QAAQ,kCAAkC;;;AAwCnF,OAAM,MAAOC,yBAAyB;;;uCAAzBA,yBAAyB;IAAA;EAAA;;;YAAzBA;IAAyB;EAAA;;;iBANzB,CACTP,gBAAgB,EAChBD,uBAAuB,EACvBE,kBAAkB,CACnB;MAAAO,OAAA,GAlCC/B,YAAY,EACZY,eAAe,CAACoB,QAAQ,EAAE,EAC1B7B,mBAAmB,EACnBD,WAAW,EACX2B,gCAAgC,EAChCX,qBAAqB,EACrBE,iBAAiB,EACjBC,WAAW,EACXjB,eAAe,EACfG,kBAAkB,EAClBC,aAAa,EACbG,gBAAgB,EAChBM,gBAAgB,EAChBH,yBAAyB,EACzBD,2BAA2B,EAC3BE,4BAA4B,EAC5BC,cAAc,EACdV,eAAe,EACfG,cAAc,EACdJ,iBAAiB,EACjBJ,gBAAgB,EAChBS,cAAc,EACdS,WAAW;IAAA;EAAA;;;2EAcFW,yBAAyB;IAAAG,YAAA,GAXlCL,4BAA4B,EAC5BD,yBAAyB,EACzBD,+BAA+B,EAC/BD,qCAAqC;IAAAM,OAAA,GA5BrC/B,YAAY,EAAAkC,EAAA,CAAAtB,eAAA,EAEZT,mBAAmB,EACnBD,WAAW,EACX2B,gCAAgC,EAChCX,qBAAqB,EACrBE,iBAAiB,EACjBC,WAAW,EACXjB,eAAe,EACfG,kBAAkB,EAClBC,aAAa,EACbG,gBAAgB,EAChBM,gBAAgB,EAChBH,yBAAyB,EACzBD,2BAA2B,EAC3BE,4BAA4B,EAC5BC,cAAc,EACdV,eAAe,EACfG,cAAc,EACdJ,iBAAiB,EACjBJ,gBAAgB,EAChBS,cAAc,EACdS,WAAW;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}