{"ast": null, "code": "/**\n * isArray\n */\n\nvar isArray = Array.isArray;\n\n/**\n * toString\n */\n\nvar str = Object.prototype.toString;\n\n/**\n * Whether or not the given `val`\n * is an array.\n *\n * example:\n *\n *        isArray([]);\n *        // > true\n *        isArray(arguments);\n *        // > false\n *        isArray('');\n *        // > false\n *\n * @param {mixed} val\n * @return {bool}\n */\n\nmodule.exports = isArray || function (val) {\n  return !!val && '[object Array]' == str.call(val);\n};", "map": {"version": 3, "names": ["isArray", "Array", "str", "Object", "prototype", "toString", "module", "exports", "val", "call"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/node_modules/is-array/index.js"], "sourcesContent": ["\n/**\n * isArray\n */\n\nvar isArray = Array.isArray;\n\n/**\n * toString\n */\n\nvar str = Object.prototype.toString;\n\n/**\n * Whether or not the given `val`\n * is an array.\n *\n * example:\n *\n *        isArray([]);\n *        // > true\n *        isArray(arguments);\n *        // > false\n *        isArray('');\n *        // > false\n *\n * @param {mixed} val\n * @return {bool}\n */\n\nmodule.exports = isArray || function (val) {\n  return !! val && '[object Array]' == str.call(val);\n};\n"], "mappings": "AACA;AACA;AACA;;AAEA,IAAIA,OAAO,GAAGC,KAAK,CAACD,OAAO;;AAE3B;AACA;AACA;;AAEA,IAAIE,GAAG,GAAGC,MAAM,CAACC,SAAS,CAACC,QAAQ;;AAEnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEAC,MAAM,CAACC,OAAO,GAAGP,OAAO,IAAI,UAAUQ,GAAG,EAAE;EACzC,OAAO,CAAC,CAAEA,GAAG,IAAI,gBAAgB,IAAIN,GAAG,CAACO,IAAI,CAACD,GAAG,CAAC;AACpD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}