{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"@angular/material/button\";\nimport * as i4 from \"@angular/material/icon\";\nimport * as i5 from \"@angular/material/tooltip\";\nimport * as i6 from \"@angular/material/input\";\nimport * as i7 from \"@angular/material/form-field\";\nimport * as i8 from \"@angular/material/select\";\nimport * as i9 from \"@angular/material/core\";\nimport * as i10 from \"../../control-messages/control-messages.component\";\nfunction MatCurrencyRateItemComponent_div_0_mat_option_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const curcode_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵpropertyInterpolate(\"value\", curcode_r2);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.alreadySelected(curcode_r2) || curcode_r2 === \"BNS\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", curcode_r2, \" \");\n  }\n}\nfunction MatCurrencyRateItemComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"span\", 2);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"mat-form-field\", 3);\n    i0.ɵɵelement(4, \"input\", 4);\n    i0.ɵɵelementStart(5, \"mat-error\");\n    i0.ɵɵelement(6, \"control-messages\", 5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"mat-form-field\", 6)(8, \"mat-select\", 7);\n    i0.ɵɵtemplate(9, MatCurrencyRateItemComponent_div_0_mat_option_9_Template, 2, 3, \"mat-option\", 8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"mat-error\");\n    i0.ɵɵelement(11, \"control-messages\", 5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function MatCurrencyRateItemComponent_div_0_Template_button_click_12_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.removeRate($event));\n    });\n    i0.ɵɵelementStart(13, \"mat-icon\");\n    i0.ɵɵtext(14, \"close\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r2.rateForm);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"1.00 \", ctx_r2.setupCurrency, \" = \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"control\", ctx_r2.rateForm.get(\"value\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.currencies);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"control\", ctx_r2.rateForm.get(\"currency\"));\n  }\n}\nexport class MatCurrencyRateItemComponent {\n  constructor() {\n    this.rateRemove = new EventEmitter();\n  }\n  ngOnInit() {}\n  alreadySelected(curcode) {\n    return this.selected.indexOf(curcode) > -1;\n  }\n  removeRate(event) {\n    event.preventDefault();\n    this.rateRemove.emit(true);\n  }\n  static {\n    this.ɵfac = function MatCurrencyRateItemComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatCurrencyRateItemComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: MatCurrencyRateItemComponent,\n      selectors: [[\"mat-currency-rate-item\"]],\n      inputs: {\n        setupCurrency: \"setupCurrency\",\n        currencies: \"currencies\",\n        selected: \"selected\",\n        rateForm: \"rateForm\"\n      },\n      outputs: {\n        rateRemove: \"rateRemove\"\n      },\n      decls: 1,\n      vars: 1,\n      consts: [[\"class\", \"rate-item\", 3, \"formGroup\", 4, \"ngIf\"], [1, \"rate-item\", 3, \"formGroup\"], [1, \"rate-item--reference\"], [\"appearance\", \"outline\", 1, \"rate-item--value\", \"no-field-padding\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"value\", \"min\", \"0.01\"], [3, \"control\"], [\"appearance\", \"outline\", 1, \"rate-item--curcode\", \"no-field-padding\"], [\"formControlName\", \"currency\"], [3, \"value\", \"disabled\", 4, \"ngFor\", \"ngForOf\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Remove this currency rate\", 1, \"btn-remove\", 3, \"click\"], [3, \"value\", \"disabled\"]],\n      template: function MatCurrencyRateItemComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, MatCurrencyRateItemComponent_div_0_Template, 15, 5, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.rateForm);\n        }\n      },\n      dependencies: [i1.NgForOf, i1.NgIf, i2.DefaultValueAccessor, i2.NumberValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.MinValidator, i2.FormGroupDirective, i2.FormControlName, i3.MatIconButton, i4.MatIcon, i5.MatTooltip, i6.MatInput, i7.MatFormField, i7.MatError, i8.MatSelect, i9.MatOption, i10.ControlMessagesComponent],\n      styles: [\".rate-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-flow: row nowrap;\\n}\\n.rate-item--reference[_ngcontent-%COMP%] {\\n  display: block;\\n  min-width: 80px;\\n  height: 40px;\\n  margin: 3px;\\n  line-height: 42px;\\n}\\n.rate-item--value[_ngcontent-%COMP%] {\\n  min-width: 150px;\\n}\\n.rate-item--curcode[_ngcontent-%COMP%] {\\n  width: 100px;\\n  margin-left: 5px;\\n}\\n.rate-item[_ngcontent-%COMP%]   .btn-remove[_ngcontent-%COMP%] {\\n  opacity: 0.3;\\n}\\n.rate-item[_ngcontent-%COMP%]   .btn-remove[_ngcontent-%COMP%]:hover {\\n  opacity: 1;\\n}\\n@media (max-width 500px) {\\n  .rate-item[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .rate-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n    margin-bottom: 20px;\\n  }\\n  .rate-item[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n    margin-bottom: 20px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY29tbW9uL2NvbXBvbmVudHMvbWF0LWN1cnJlbmN5LXJhdGVzLXNldHVwL2N1cnJlbmN5LXJhdGUtaXRlbS9tYXQtY3VycmVuY3ktcmF0ZS1pdGVtLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsYUFBQTtFQUNBLHFCQUFBO0FBQ0Y7QUFDRTtFQUNFLGNBQUE7RUFDQSxlQUFBO0VBQ0EsWUFBQTtFQUNBLFdBQUE7RUFDQSxpQkFBQTtBQUNKO0FBRUU7RUFDRSxnQkFBQTtBQUFKO0FBR0U7RUFDRSxZQUFBO0VBQ0EsZ0JBQUE7QUFESjtBQUlFO0VBQ0UsWUFBQTtBQUZKO0FBSUk7RUFDRSxVQUFBO0FBRk47QUFNRTtFQTdCRjtJQThCSSxzQkFBQTtFQUhGO0VBSUU7SUFDRSxtQkFBQTtFQUZKO0VBSUU7SUFDRSxtQkFBQTtFQUZKO0FBQ0YiLCJzb3VyY2VzQ29udGVudCI6WyIucmF0ZS1pdGVtIHtcbiAgZGlzcGxheTogZmxleDtcbiAgZmxleC1mbG93OiByb3cgbm93cmFwO1xuXG4gICYtLXJlZmVyZW5jZSB7XG4gICAgZGlzcGxheTogYmxvY2s7XG4gICAgbWluLXdpZHRoOiA4MHB4O1xuICAgIGhlaWdodDogNDBweDtcbiAgICBtYXJnaW46IDNweDtcbiAgICBsaW5lLWhlaWdodDogNDJweDtcbiAgfVxuXG4gICYtLXZhbHVlIHtcbiAgICBtaW4td2lkdGg6IDE1MHB4O1xuICB9XG5cbiAgJi0tY3VyY29kZSB7XG4gICAgd2lkdGg6MTAwcHg7XG4gICAgbWFyZ2luLWxlZnQ6NXB4O1xuICB9XG5cbiAgLmJ0bi1yZW1vdmUge1xuICAgIG9wYWNpdHk6IDAuMztcblxuICAgICY6aG92ZXIge1xuICAgICAgb3BhY2l0eTogMTtcbiAgICB9XG4gIH1cblxuICBAbWVkaWEgKG1heC13aWR0aCA1MDBweCkge1xuICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgbGFiZWwge1xuICAgICAgbWFyZ2luLWJvdHRvbTogMjBweDtcbiAgICB9XG4gICAgaW5wdXQge1xuICAgICAgbWFyZ2luLWJvdHRvbTogMjBweDtcbiAgICB9XG4gIH1cbn1cbiJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵpropertyInterpolate", "curcode_r2", "ɵɵproperty", "ctx_r2", "alreadySelected", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵelement", "ɵɵtemplate", "MatCurrencyRateItemComponent_div_0_mat_option_9_Template", "ɵɵlistener", "MatCurrencyRateItemComponent_div_0_Template_button_click_12_listener", "$event", "ɵɵrestoreView", "_r1", "ɵɵnextContext", "ɵɵresetView", "removeRate", "rateForm", "setupCurrency", "get", "currencies", "MatCurrencyRateItemComponent", "constructor", "rateRemove", "ngOnInit", "curcode", "selected", "indexOf", "event", "preventDefault", "emit", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "MatCurrencyRateItemComponent_Template", "rf", "ctx", "MatCurrencyRateItemComponent_div_0_Template"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/common/components/mat-currency-rates-setup/currency-rate-item/mat-currency-rate-item.component.ts", "/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/common/components/mat-currency-rates-setup/currency-rate-item/mat-currency-rate-item.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';\nimport { FormGroup } from '@angular/forms';\n\n@Component({\n  selector: 'mat-currency-rate-item',\n  templateUrl: 'mat-currency-rate-item.component.html',\n  styleUrls: ['./mat-currency-rate-item.component.scss'],\n})\n\nexport class MatCurrencyRateItemComponent implements OnInit {\n  @Input() setupCurrency: string;\n  @Input() currencies: string[];\n  @Input() selected: string[];\n  @Input() rateForm: FormGroup;\n\n  @Output() rateRemove: EventEmitter<any> = new EventEmitter();\n\n  constructor() {\n  }\n\n  ngOnInit() {\n  }\n\n  alreadySelected( curcode ): boolean {\n    return this.selected.indexOf(curcode) > -1;\n  }\n\n  removeRate( event ) {\n    event.preventDefault();\n    this.rateRemove.emit(true);\n  }\n}\n", "<div [formGroup]=\"rateForm\" class=\"rate-item\" *ngIf=\"rateForm\">\n\n  <span class=\"rate-item--reference\">1.00 {{ setupCurrency }} = </span>\n\n  <mat-form-field class=\"rate-item--value no-field-padding\" appearance=\"outline\">\n    <input matInput type=\"number\" formControlName=\"value\" min=\"0.01\">\n    <mat-error>\n      <control-messages [control]=\"rateForm.get('value')\"></control-messages>\n    </mat-error>\n  </mat-form-field>\n\n  <mat-form-field class=\"rate-item--curcode no-field-padding\" appearance=\"outline\">\n    <mat-select formControlName=\"currency\">\n      <mat-option *ngFor=\"let curcode of currencies\" value=\"{{ curcode }}\" [disabled]=\"alreadySelected(curcode) || curcode === 'BNS'\">\n        {{ curcode }}\n      </mat-option>\n    </mat-select>\n    <mat-error>\n      <control-messages [control]=\"rateForm.get('currency')\"></control-messages>\n    </mat-error>\n  </mat-form-field>\n\n  <button mat-icon-button (click)=\"removeRate($event)\" class=\"btn-remove\" matTooltip=\"Remove this currency rate\">\n    <mat-icon>close</mat-icon>\n  </button>\n</div>\n"], "mappings": "AAAA,SAAoBA,YAAY,QAA+B,eAAe;;;;;;;;;;;;;;ICaxEC,EAAA,CAAAC,cAAA,qBAAgI;IAC9HD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;;IAFkCH,EAAA,CAAAI,qBAAA,UAAAC,UAAA,CAAqB;IAACL,EAAA,CAAAM,UAAA,aAAAC,MAAA,CAAAC,eAAA,CAAAH,UAAA,KAAAA,UAAA,WAA0D;IAC7HL,EAAA,CAAAS,SAAA,EACF;IADET,EAAA,CAAAU,kBAAA,MAAAL,UAAA,MACF;;;;;;IAbJL,EAFF,CAAAC,cAAA,aAA+D,cAE1B;IAAAD,EAAA,CAAAE,MAAA,GAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAErEH,EAAA,CAAAC,cAAA,wBAA+E;IAC7ED,EAAA,CAAAW,SAAA,eAAiE;IACjEX,EAAA,CAAAC,cAAA,gBAAW;IACTD,EAAA,CAAAW,SAAA,0BAAuE;IAE3EX,EADE,CAAAG,YAAA,EAAY,EACG;IAGfH,EADF,CAAAC,cAAA,wBAAiF,oBACxC;IACrCD,EAAA,CAAAY,UAAA,IAAAC,wDAAA,wBAAgI;IAGlIb,EAAA,CAAAG,YAAA,EAAa;IACbH,EAAA,CAAAC,cAAA,iBAAW;IACTD,EAAA,CAAAW,SAAA,2BAA0E;IAE9EX,EADE,CAAAG,YAAA,EAAY,EACG;IAEjBH,EAAA,CAAAC,cAAA,iBAA+G;IAAvFD,EAAA,CAAAc,UAAA,mBAAAC,qEAAAC,MAAA;MAAAhB,EAAA,CAAAiB,aAAA,CAAAC,GAAA;MAAA,MAAAX,MAAA,GAAAP,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASb,MAAA,CAAAc,UAAA,CAAAL,MAAA,CAAkB;IAAA,EAAC;IAClDhB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAEnBF,EAFmB,CAAAG,YAAA,EAAW,EACnB,EACL;;;;IAzBDH,EAAA,CAAAM,UAAA,cAAAC,MAAA,CAAAe,QAAA,CAAsB;IAEUtB,EAAA,CAAAS,SAAA,GAA2B;IAA3BT,EAAA,CAAAU,kBAAA,UAAAH,MAAA,CAAAgB,aAAA,QAA2B;IAKxCvB,EAAA,CAAAS,SAAA,GAAiC;IAAjCT,EAAA,CAAAM,UAAA,YAAAC,MAAA,CAAAe,QAAA,CAAAE,GAAA,UAAiC;IAMnBxB,EAAA,CAAAS,SAAA,GAAa;IAAbT,EAAA,CAAAM,UAAA,YAAAC,MAAA,CAAAkB,UAAA,CAAa;IAK3BzB,EAAA,CAAAS,SAAA,GAAoC;IAApCT,EAAA,CAAAM,UAAA,YAAAC,MAAA,CAAAe,QAAA,CAAAE,GAAA,aAAoC;;;ADT5D,OAAM,MAAOE,4BAA4B;EAQvCC,YAAA;IAFU,KAAAC,UAAU,GAAsB,IAAI7B,YAAY,EAAE;EAG5D;EAEA8B,QAAQA,CAAA,GACR;EAEArB,eAAeA,CAAEsB,OAAO;IACtB,OAAO,IAAI,CAACC,QAAQ,CAACC,OAAO,CAACF,OAAO,CAAC,GAAG,CAAC,CAAC;EAC5C;EAEAT,UAAUA,CAAEY,KAAK;IACfA,KAAK,CAACC,cAAc,EAAE;IACtB,IAAI,CAACN,UAAU,CAACO,IAAI,CAAC,IAAI,CAAC;EAC5B;;;uCArBWT,4BAA4B;IAAA;EAAA;;;YAA5BA,4BAA4B;MAAAU,SAAA;MAAAC,MAAA;QAAAd,aAAA;QAAAE,UAAA;QAAAM,QAAA;QAAAT,QAAA;MAAA;MAAAgB,OAAA;QAAAV,UAAA;MAAA;MAAAW,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCTzC5C,EAAA,CAAAY,UAAA,IAAAkC,2CAAA,kBAA+D;;;UAAhB9C,EAAA,CAAAM,UAAA,SAAAuC,GAAA,CAAAvB,QAAA,CAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}