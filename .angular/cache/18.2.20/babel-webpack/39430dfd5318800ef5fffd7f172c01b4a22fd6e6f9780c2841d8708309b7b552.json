{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { FormControl } from '@angular/forms';\nimport { BulkAction, PERMISSIONS_NAMES, SWUI_GRID_SELECTION_TRANSFORMER_TOKEN, SwuiGridComponent } from '@skywind-group/lib-swui';\nimport { BehaviorSubject, combineLatest, ReplaySubject, Subject } from 'rxjs';\nimport { debounceTime, distinctUntilChanged, map, take, takeUntil } from 'rxjs/operators';\nimport { GameService } from '../../../../../../../common/services/game.service';\nimport { isLiveGame } from '../../../../../../../common/typings';\nimport { SCHEMA_LIST } from '../../manage-games.schema';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../../../../common/services/game.service\";\nimport * as i2 from \"@skywind-group/lib-swui\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/input\";\nimport * as i7 from \"../../../../../../../common/directives/trim-input-value/trim-input-value.component\";\nimport * as i8 from \"@ngx-translate/core\";\nconst _c0 = a0 => ({\n  disabled: a0\n});\nconst _c1 = a0 => ({\n  numberOfItems: a0\n});\nfunction ManageGamesGridComponent_ng_template_0_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"async\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_4_0;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"(\", (tmp_4_0 = i0.ɵɵpipeBind1(2, 1, ctx_r1.games$)) == null ? null : tmp_4_0.length, \")\");\n  }\n}\nfunction ManageGamesGridComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 6);\n    i0.ɵɵlistener(\"click\", function ManageGamesGridComponent_ng_template_0_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.selectAllItems());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵtemplate(3, ManageGamesGridComponent_ng_template_0_span_3_Template, 3, 3, \"span\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"input\", 8);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵlistener(\"keyup\", function ManageGamesGridComponent_ng_template_0_Template_input_keyup_4_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onFilterChanged($event.target[\"value\"]));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(6, \"lib-swui-multiselect\", 9);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.loading)(\"ngClass\", i0.ɵɵpureFunction1(16, _c0, ctx_r1.loading));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 12, \"ENTITY_SETUP.GAMES.MODALS.selectAll\"), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(5, 14, \"ENTITY_SETUP.GAMES.searchPlaceholder\"));\n    i0.ɵɵproperty(\"disabled\", ctx_r1.loading)(\"value\", ctx_r1.terms);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"title\", \"Labels\")(\"placeholder\", \"Labels filter\")(\"showSearch\", true)(\"data\", ctx_r1.availableLabels)(\"formControl\", ctx_r1.labelsFilterControl);\n  }\n}\nfunction ManageGamesGridComponent_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction ManageGamesGridComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"span\", 12);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"async\");\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" | \");\n    i0.ɵɵelementStart(6, \"span\", 12);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"async\");\n    i0.ɵɵpipe(9, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" | \");\n    i0.ɵɵelementStart(11, \"span\", 12);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    let tmp_3_0;\n    const ctx_r1 = i0.ɵɵnextContext();\n    const grid_r3 = i0.ɵɵreference(7);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(4, 5, \"ALL.available\", i0.ɵɵpureFunction1(16, _c1, (tmp_3_0 = i0.ɵɵpipeBind1(3, 3, ctx_r1.games$)) == null ? null : tmp_3_0.length)));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(9, 10, \"ALL.found\", i0.ɵɵpureFunction1(18, _c1, i0.ɵɵpipeBind1(8, 8, grid_r3.dataSource.total$) || 0)));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(13, 13, \"ALL.selected\", i0.ɵɵpureFunction1(20, _c1, grid_r3.selection.selected.length)));\n  }\n}\nconst selectionTransformer = {\n  transform: game => game.code\n};\nexport class ManageGamesGridComponent {\n  set entityGames(values) {\n    if (!values) return;\n    let res = values?.filter(game => isLiveGame(game) || !isLiveGame(game) && this.swHubAuthService.areGranted([PERMISSIONS_NAMES.ENTITY_GAME_CHANGE_STATE]));\n    if (!this.swHubAuthService.areGranted([PERMISSIONS_NAMES.ENTITY_LIVEGAME, PERMISSIONS_NAMES.ENTITY_LIVEGAME_REMOVE])) {\n      res = res.filter(game => !isLiveGame(game));\n    }\n    this._entityGames = res;\n    this.selected = res.map(game => game.code);\n    this.allSelected = values.map(game => game.code);\n    this._entityGameCodes = [...this.selected]; // need to compare codes after manipulation\n  }\n  constructor(service, swHubAuthService) {\n    this.service = service;\n    this.swHubAuthService = swHubAuthService;\n    this.labelsFilterControl = new FormControl();\n    this.items = [];\n    this.schema = SCHEMA_LIST;\n    this.loading = true;\n    this.games$ = new ReplaySubject(1);\n    this.limit = 10;\n    this.availableGames = [];\n    this.availableLabels = [];\n    this.terms = '';\n    this.selected = [];\n    this.bulkActions = [new BulkAction({\n      title: 'Select Games'\n    })];\n    this.applyButtonDisable = new EventEmitter();\n    this.selectedGamesApply = new EventEmitter();\n    this._entityGames = []; // @TODO: to remove\n    this._entityGameCodes = [];\n    this.allSelected = [];\n    this._applyButtonDisabled = true;\n    this.searchStream = new BehaviorSubject('');\n    this.selectedLabelsStream = new BehaviorSubject([]);\n    this.destroyed$ = new Subject();\n    this.service.getGameLabels().pipe(takeUntil(this.destroyed$)).subscribe(data => {\n      this.availableLabels = data.map(label => {\n        const {\n          id,\n          title\n        } = label;\n        return {\n          id,\n          text: title\n        };\n      });\n    });\n  }\n  ngOnInit() {\n    this.setupGrid();\n    this.initStreams();\n    this.labelsFilterControl.valueChanges.pipe(takeUntil(this.destroyed$)).subscribe(val => {\n      this.selectedLabelsStream.next(val);\n    });\n  }\n  ngOnDestroy() {\n    this.availableGames = [];\n    this.destroyed$.next();\n    this.destroyed$.complete();\n  }\n  setupGrid() {\n    this.schema = SCHEMA_LIST;\n    this.schemaItems = this.schema.filter(item => item.isList);\n  }\n  confirmGamesSelection(event) {\n    event.preventDefault();\n    const hash = this.availableGames.reduce((result, item) => {\n      result[item.code] = item;\n      return result;\n    }, {});\n    let addedGames = this.grid.selection.selected.filter(code => this._entityGameCodes.indexOf(code) === -1).map(code => hash[code]);\n    let removedGames = this._entityGames.filter(game => !this.grid.selection.isSelected(game.code));\n    this.selectedGamesApply.emit({\n      addedGames,\n      removedGames\n    });\n  }\n  onFilterChanged(value) {\n    this.searchStream.next(value);\n  }\n  selectAllItems() {\n    this.games$.pipe(take(1)).subscribe(games => {\n      const codes = games.map(game => game.code);\n      this.grid.selection.select(...codes);\n      this.enableApplyButton();\n    });\n  }\n  enableApplyButton() {\n    if (this._applyButtonDisabled) {\n      this._applyButtonDisabled = false;\n      this.applyButtonDisable.emit(this._applyButtonDisabled);\n    }\n  }\n  initStreams() {\n    const path = this.entity.entityParent.path || '';\n    this.service.getAllGames(path, false, true).pipe(map(items => {\n      let res = items?.filter(game => isLiveGame(game) || !isLiveGame(game) && this.swHubAuthService.areGranted([PERMISSIONS_NAMES.ENTITY_GAME_CHANGE_STATE]));\n      if (!this.swHubAuthService.areGranted([PERMISSIONS_NAMES.ENTITY_LIVEGAME, PERMISSIONS_NAMES.ENTITY_LIVEGAME_ADD])) {\n        res = res.filter(game => !isLiveGame(game) || this.allSelected.includes(game.code));\n      }\n      if (!this.swHubAuthService.areGranted([PERMISSIONS_NAMES.ENTITY_LIVEGAME, PERMISSIONS_NAMES.ENTITY_LIVEGAME_REMOVE])) {\n        res = res.filter(game => !isLiveGame(game) || !this.allSelected.includes(game.code));\n      }\n      return res;\n    })).subscribe(games => {\n      this.games$.next(games);\n    });\n    return combineLatest([this.searchStream.pipe(debounceTime(500), distinctUntilChanged()), this.selectedLabelsStream, this.games$]).pipe(map(([search, selectedLabels, items]) => {\n      this.availableGames = items;\n      return items.filter(item => {\n        const {\n          title,\n          code,\n          labels: itemLabels\n        } = item;\n        const foundByCodeOrTitle = title.toLowerCase().indexOf(search.toLowerCase()) !== -1 || code.toLowerCase().indexOf(search.toLowerCase()) !== -1;\n        const foundByLabel = !selectedLabels.length ? true : itemLabels.some(label => selectedLabels.indexOf(label.id) !== -1);\n        return foundByLabel && foundByCodeOrTitle;\n      });\n    })).subscribe(res => {\n      this.items = res;\n      this.loading = false;\n    });\n  }\n  static {\n    this.ɵfac = function ManageGamesGridComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ManageGamesGridComponent)(i0.ɵɵdirectiveInject(i1.GameService), i0.ɵɵdirectiveInject(i2.SwHubAuthService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ManageGamesGridComponent,\n      selectors: [[\"manage-games-grid\"]],\n      viewQuery: function ManageGamesGridComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(SwuiGridComponent, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.grid = _t.first);\n        }\n      },\n      inputs: {\n        entity: \"entity\",\n        entityGames: \"entityGames\"\n      },\n      outputs: {\n        applyButtonDisable: \"applyButtonDisable\",\n        selectedGamesApply: \"selectedGamesApply\"\n      },\n      features: [i0.ɵɵProvidersFeature([GameService, {\n        provide: SWUI_GRID_SELECTION_TRANSFORMER_TOKEN,\n        useValue: selectionTransformer\n      }])],\n      decls: 10,\n      vars: 15,\n      consts: [[\"gridTopMenu\", \"\"], [\"grid\", \"\"], [1, \"sw-grid-short-view\"], [\"gridId\", \"manage-games-dialog\", 3, \"loading\", \"schema\", \"data\", \"selected\", \"ignoreQueryParams\", \"pageSize\", \"columnsManagement\", \"bulkActions\", \"bulkSelectionOnly\", \"disableRefreshAction\"], [4, \"ngTemplateOutlet\"], [\"style\", \"padding: 15px 0;\", 4, \"ngIf\"], [\"mat-flat-button\", \"\", \"color\", \"primary\", 1, \"mr-10\", 3, \"click\", \"disabled\", \"ngClass\"], [\"class\", \"ml-5\", 4, \"ngIf\"], [\"matInput\", \"\", \"trimValue\", \"\", \"type\", \"search\", 1, \"mr-10\", \"search\", 3, \"keyup\", \"placeholder\", \"disabled\", \"value\"], [2, \"display\", \"flex\", 3, \"title\", \"placeholder\", \"showSearch\", \"data\", \"formControl\"], [1, \"ml-5\"], [2, \"padding\", \"15px 0\"], [1, \"count\"]],\n      template: function ManageGamesGridComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, ManageGamesGridComponent_ng_template_0_Template, 7, 18, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementStart(2, \"div\", 2)(3, \"span\");\n          i0.ɵɵtext(4);\n          i0.ɵɵpipe(5, \"async\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"lib-swui-grid\", 3, 1);\n          i0.ɵɵtemplate(8, ManageGamesGridComponent_ng_container_8_Template, 1, 0, \"ng-container\", 4);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(9, ManageGamesGridComponent_div_9_Template, 14, 22, \"div\", 5);\n        }\n        if (rf & 2) {\n          const gridTopMenu_r4 = i0.ɵɵreference(1);\n          const grid_r3 = i0.ɵɵreference(7);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 13, grid_r3 == null ? null : grid_r3.loading$));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"loading\", ctx.loading)(\"schema\", ctx.schemaItems)(\"data\", ctx.items)(\"selected\", ctx.selected)(\"ignoreQueryParams\", true)(\"pageSize\", 10)(\"columnsManagement\", false)(\"bulkActions\", ctx.bulkActions)(\"bulkSelectionOnly\", true)(\"disableRefreshAction\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngTemplateOutlet\", gridTopMenu_r4);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n        }\n      },\n      dependencies: [i3.NgClass, i3.NgIf, i3.NgTemplateOutlet, i4.NgControlStatus, i4.FormControlDirective, i2.SwuiGridComponent, i5.MatButton, i6.MatInput, i2.SwuiMultiselectComponent, i7.TrimInputValueComponent, i3.AsyncPipe, i8.TranslatePipe],\n      styles: [\".labels-filter[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  padding-top: 2px;\\n  color: #2a2c44;\\n}\\n\\n.search[_ngcontent-%COMP%] {\\n  width: 190px;\\n}\\n\\n.sw-grid-short-view[_ngcontent-%COMP%] {\\n  min-height: 300px !important;\\n  display: block !important;\\n  overflow: auto;\\n  padding-top: 5px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvYnVzaW5lc3MtbWFuYWdlbWVudC9jb21wb25lbnRzL2VudGl0aWVzL3RhYi1nYW1lcy9tYW5hZ2UtZ2FtZXMvbWFuYWdlLWdhbWVzLWdyaWQvbWFuYWdlLWdhbWVzLWdyaWQuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0FBQ0Y7O0FBRUE7RUFDRSxZQUFBO0FBQ0Y7O0FBRUE7RUFDRSw0QkFBQTtFQUNBLHlCQUFBO0VBQ0EsY0FBQTtFQUNBLGdCQUFBO0FBQ0YiLCJzb3VyY2VzQ29udGVudCI6WyIubGFiZWxzLWZpbHRlciB7XG4gIGZvbnQtc2l6ZTogMTRweDtcbiAgcGFkZGluZy10b3A6IDJweDtcbiAgY29sb3I6ICMyYTJjNDQ7XG59XG5cbi5zZWFyY2gge1xuICB3aWR0aDogMTkwcHg7XG59XG5cbi5zdy1ncmlkLXNob3J0LXZpZXcge1xuICBtaW4taGVpZ2h0OiAzMDBweCAhaW1wb3J0YW50O1xuICBkaXNwbGF5OiBibG9jayAhaW1wb3J0YW50O1xuICBvdmVyZmxvdzogYXV0bztcbiAgcGFkZGluZy10b3A6IDVweDtcbn1cbiJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "FormControl", "BulkAction", "PERMISSIONS_NAMES", "SWUI_GRID_SELECTION_TRANSFORMER_TOKEN", "SwuiGridComponent", "BehaviorSubject", "combineLatest", "ReplaySubject", "Subject", "debounceTime", "distinctUntilChanged", "map", "take", "takeUntil", "GameService", "isLiveGame", "SCHEMA_LIST", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "tmp_4_0", "ɵɵpipeBind1", "ctx_r1", "games$", "length", "ɵɵlistener", "ManageGamesGridComponent_ng_template_0_Template_button_click_0_listener", "ɵɵrestoreView", "_r1", "ɵɵnextContext", "ɵɵresetView", "selectAllItems", "ɵɵtemplate", "ManageGamesGridComponent_ng_template_0_span_3_Template", "ManageGamesGridComponent_ng_template_0_Template_input_keyup_4_listener", "$event", "onFilterChanged", "target", "ɵɵelement", "ɵɵproperty", "loading", "ɵɵpureFunction1", "_c0", "ɵɵpropertyInterpolate", "terms", "availableLabels", "labelsFilterControl", "ɵɵelementContainer", "ɵɵtextInterpolate", "ɵɵpipeBind2", "_c1", "tmp_3_0", "grid_r3", "dataSource", "total$", "selection", "selected", "selectionTransformer", "transform", "game", "code", "ManageGamesGridComponent", "entityGames", "values", "res", "filter", "swHubAuthService", "areGranted", "ENTITY_GAME_CHANGE_STATE", "ENTITY_LIVEGAME", "ENTITY_LIVEGAME_REMOVE", "_entityGames", "allSelected", "_entityGameCodes", "constructor", "service", "items", "schema", "limit", "availableGames", "bulkActions", "title", "applyButtonDisable", "selectedGamesApply", "_applyButtonDisabled", "searchStream", "selectedLabelsStream", "destroyed$", "getGameLabels", "pipe", "subscribe", "data", "label", "id", "text", "ngOnInit", "setupGrid", "initStreams", "valueChanges", "val", "next", "ngOnDestroy", "complete", "schemaItems", "item", "isList", "confirmGamesSelection", "event", "preventDefault", "hash", "reduce", "result", "addedGames", "grid", "indexOf", "removedGames", "isSelected", "emit", "value", "games", "codes", "select", "enableApplyButton", "path", "entity", "entityParent", "getAllGames", "ENTITY_LIVEGAME_ADD", "includes", "search", "<PERSON><PERSON><PERSON><PERSON>", "labels", "itemLabels", "foundByCodeOrTitle", "toLowerCase", "foundByLabel", "some", "ɵɵdirectiveInject", "i1", "i2", "SwHubAuthService", "selectors", "viewQuery", "ManageGamesGridComponent_Query", "rf", "ctx", "provide", "useValue", "decls", "vars", "consts", "template", "ManageGamesGridComponent_Template", "ManageGamesGridComponent_ng_template_0_Template", "ɵɵtemplateRefExtractor", "ManageGamesGridComponent_ng_container_8_Template", "ManageGamesGridComponent_div_9_Template", "loading$", "gridTopMenu_r4"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-games/manage-games/manage-games-grid/manage-games-grid.component.ts", "/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-games/manage-games/manage-games-grid/manage-games-grid.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, OnD<PERSON>roy, OnInit, Output, ViewChild } from '@angular/core';\nimport { FormControl } from '@angular/forms';\nimport {\n  BulkAction, PERMISSIONS_NAMES, SelectionTransformer, SwHubAuthService, SWUI_GRID_SELECTION_TRANSFORMER_TOKEN, SwuiGridComponent,\n  SwuiGridField, SwuiSelectOption\n} from '@skywind-group/lib-swui';\nimport { BehaviorSubject, combineLatest, ReplaySubject, Subject } from 'rxjs';\nimport { debounceTime, distinctUntilChanged, map, take, takeUntil } from 'rxjs/operators';\nimport { Entity } from '../../../../../../../common/models/entity.model';\nimport { GameService } from '../../../../../../../common/services/game.service';\nimport { Game, isLiveGame, Label } from '../../../../../../../common/typings';\n\nimport { SCHEMA_LIST } from '../../manage-games.schema';\n\nconst selectionTransformer: SelectionTransformer = {\n  transform: ( game: Game ) => game.code\n};\n\n@Component({\n  selector: 'manage-games-grid',\n  templateUrl: './manage-games-grid.component.html',\n  styleUrls: ['./manage-games-grid.component.scss'],\n  providers: [\n    GameService,\n    {\n      provide: SWUI_GRID_SELECTION_TRANSFORMER_TOKEN,\n      useValue: selectionTransformer\n    }\n  ],\n})\nexport class ManageGamesGridComponent implements OnInit, OnDestroy {\n\n  @Input() entity: Entity;\n\n  labelsFilterControl = new FormControl();\n  items: Game[] = [];\n  schema = SCHEMA_LIST;\n  schemaItems: SwuiGridField[];\n\n  loading: boolean = true;\n  games$ = new ReplaySubject<Game[]>(1);\n  limit: number = 10;\n  availableGames: Game[] = [];\n  availableLabels: SwuiSelectOption[] = [];\n  terms: string = '';\n  selected: string[] = [];\n\n  bulkActions: BulkAction[] = [\n    new BulkAction({ title: 'Select Games' })\n  ];\n\n  @ViewChild(SwuiGridComponent, { static: true }) grid: SwuiGridComponent<Game[], string>;\n\n  @Output() applyButtonDisable: EventEmitter<boolean> = new EventEmitter();\n  @Output() selectedGamesApply: EventEmitter<any> = new EventEmitter();\n\n  private _entityGames: Game[] = []; // @TODO: to remove\n  private _entityGameCodes: string[] = [];\n  private allSelected: string[] = [];\n\n  private _applyButtonDisabled: boolean = true;\n\n  private searchStream = new BehaviorSubject<string>('');\n  private selectedLabelsStream = new BehaviorSubject<string[]>([]);\n  private readonly destroyed$ = new Subject<void>();\n\n  @Input()\n  set entityGames( values: Game[] ) {\n    if (!values) return;\n\n    let res = values?.filter(game => isLiveGame(game)\n      || !isLiveGame(game) && this.swHubAuthService.areGranted([PERMISSIONS_NAMES.ENTITY_GAME_CHANGE_STATE]));\n\n    if (!this.swHubAuthService.areGranted([PERMISSIONS_NAMES.ENTITY_LIVEGAME, PERMISSIONS_NAMES.ENTITY_LIVEGAME_REMOVE])) {\n      res = res.filter(game => !isLiveGame(game));\n    }\n\n    this._entityGames = res;\n\n    this.selected = res.map(game => game.code);\n    this.allSelected = values.map(game => game.code);\n    this._entityGameCodes = [...this.selected]; // need to compare codes after manipulation\n  }\n\n  constructor( public service: GameService,\n               private readonly swHubAuthService: SwHubAuthService,\n  ) {\n    this.service.getGameLabels()\n      .pipe(\n        takeUntil(this.destroyed$)\n      )\n      .subscribe(data => {\n        this.availableLabels = data.map(( label: Label ) => {\n          const { id, title } = label;\n          return {\n            id,\n            text: title\n          };\n        });\n      });\n  }\n\n  ngOnInit() {\n    this.setupGrid();\n    this.initStreams();\n\n    this.labelsFilterControl.valueChanges\n      .pipe(\n        takeUntil(this.destroyed$)\n      )\n      .subscribe(( val: string[] ) => {\n        this.selectedLabelsStream.next(val);\n      });\n  }\n\n  ngOnDestroy() {\n    this.availableGames = [];\n    this.destroyed$.next();\n    this.destroyed$.complete();\n  }\n\n  setupGrid() {\n    this.schema = SCHEMA_LIST;\n    this.schemaItems = this.schema.filter(item => item.isList);\n  }\n\n  confirmGamesSelection( event: Event ) {\n    event.preventDefault();\n    const hash: { [code: string]: Game } = this.availableGames.reduce(( result, item ) => {\n      result[item.code] = item;\n      return result;\n    }, {});\n\n    let addedGames: Game[] = this.grid.selection.selected\n      .filter(code => this._entityGameCodes.indexOf(code) === -1)\n      .map(( code ) => hash[code]);\n    let removedGames: Game[] = this._entityGames\n      .filter(game => !this.grid.selection.isSelected(game.code));\n\n    this.selectedGamesApply.emit({ addedGames, removedGames });\n  }\n\n  onFilterChanged( value: string ) {\n    this.searchStream.next(value);\n  }\n\n  selectAllItems() {\n    this.games$\n      .pipe(take(1))\n      .subscribe(games => {\n        const codes: string[] = games.map(game => game.code);\n        this.grid.selection.select(...codes);\n        this.enableApplyButton();\n      });\n  }\n\n  private enableApplyButton() {\n    if (this._applyButtonDisabled) {\n      this._applyButtonDisabled = false;\n      this.applyButtonDisable.emit(this._applyButtonDisabled);\n    }\n  }\n\n  private initStreams() {\n    const path = this.entity.entityParent.path || '';\n    this.service.getAllGames(path, false, true)\n      .pipe(\n        map(items => {\n          let res = items?.filter(game => isLiveGame(game)\n            || !isLiveGame(game) && this.swHubAuthService.areGranted([PERMISSIONS_NAMES.ENTITY_GAME_CHANGE_STATE]));\n\n          if (!this.swHubAuthService.areGranted([PERMISSIONS_NAMES.ENTITY_LIVEGAME, PERMISSIONS_NAMES.ENTITY_LIVEGAME_ADD])) {\n            res = res.filter(game => !isLiveGame(game) || this.allSelected.includes(game.code));\n          }\n\n          if (!this.swHubAuthService.areGranted([PERMISSIONS_NAMES.ENTITY_LIVEGAME, PERMISSIONS_NAMES.ENTITY_LIVEGAME_REMOVE])) {\n            res = res.filter(game => !isLiveGame(game) || !this.allSelected.includes(game.code));\n          }\n\n          return res;\n        })\n      )\n      .subscribe(games => {\n        this.games$.next(games);\n      });\n\n    return combineLatest([\n      this.searchStream.pipe(debounceTime(500), distinctUntilChanged()),\n      this.selectedLabelsStream,\n      this.games$\n    ]).pipe(map(( [search, selectedLabels, items] ) => {\n      this.availableGames = items;\n        return items.filter(( item ) => {\n          const { title, code, labels: itemLabels } = item;\n          const foundByCodeOrTitle = title.toLowerCase().indexOf(search.toLowerCase()) !== -1\n            || code.toLowerCase().indexOf(search.toLowerCase()) !== -1;\n          const foundByLabel = !selectedLabels.length\n            ? true : itemLabels.some(label => selectedLabels.indexOf(label.id) !== -1);\n\n          return foundByLabel && foundByCodeOrTitle;\n        });\n      }),\n    ).subscribe(res => {\n      this.items = res;\n      this.loading = false;\n    });\n  }\n}\n", "<ng-template #gridTopMenu>\n  <button\n    mat-flat-button\n    color=\"primary\"\n    (click)=\"selectAllItems()\"\n    [disabled]=\"loading\"\n    class=\"mr-10\"\n    [ngClass]=\"{disabled:loading}\">\n    {{'ENTITY_SETUP.GAMES.MODALS.selectAll' | translate}}<span *ngIf=\"!loading\" class=\"ml-5\">({{(games$ | async)?.length}})</span>\n  </button>\n\n  <input\n    matInput trimValue\n    type=\"search\"\n    class=\"mr-10 search\"\n    placeholder=\"{{ 'ENTITY_SETUP.GAMES.searchPlaceholder' | translate }}\"\n    [disabled]=\"loading\"\n    (keyup)=\"onFilterChanged($event.target['value'])\"\n    [value]=\"terms\">\n\n  <lib-swui-multiselect\n    style=\"display: flex;\"\n    [title]=\"'Labels'\"\n    [placeholder]=\"'Labels filter'\"\n    [showSearch]=\"true\"\n    [data]=\"availableLabels\"\n    [formControl]=\"labelsFilterControl\">\n  </lib-swui-multiselect>\n\n</ng-template>\n\n<div class=\"sw-grid-short-view\">\n  <span>{{this.grid?.loading$| async}}</span>\n  <lib-swui-grid\n    #grid\n    gridId=\"manage-games-dialog\"\n    [loading]=\"loading\"\n    [schema]=\"schemaItems\"\n    [data]=\"items\"\n    [selected]=\"selected\"\n    [ignoreQueryParams]=\"true\"\n    [pageSize]=\"10\"\n    [columnsManagement]=\"false\"\n    [bulkActions]=\"bulkActions\"\n    [bulkSelectionOnly]=\"true\"\n    [disableRefreshAction]=\"true\">\n    <ng-container *ngTemplateOutlet=\"gridTopMenu\"></ng-container>\n  </lib-swui-grid>\n</div>\n\n<div *ngIf=\"!loading\" style=\"padding: 15px 0;\">\n  <span class=\"count\">{{ 'ALL.available' | translate: {numberOfItems: (games$ | async)?.length} }}</span> |\n  <span class=\"count\">{{ 'ALL.found' | translate: {numberOfItems: (grid.dataSource.total$ | async) || 0} }}</span> |\n  <span class=\"count\">{{ 'ALL.selected' | translate: {numberOfItems: grid.selection.selected.length} }}</span>\n</div>\n"], "mappings": "AAAA,SAAoBA,YAAY,QAAqD,eAAe;AACpG,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SACEC,UAAU,EAAEC,iBAAiB,EAA0CC,qCAAqC,EAAEC,iBAAiB,QAE1H,yBAAyB;AAChC,SAASC,eAAe,EAAEC,aAAa,EAAEC,aAAa,EAAEC,OAAO,QAAQ,MAAM;AAC7E,SAASC,YAAY,EAAEC,oBAAoB,EAAEC,GAAG,EAAEC,IAAI,EAAEC,SAAS,QAAQ,gBAAgB;AAEzF,SAASC,WAAW,QAAQ,mDAAmD;AAC/E,SAAeC,UAAU,QAAe,qCAAqC;AAE7E,SAASC,WAAW,QAAQ,2BAA2B;;;;;;;;;;;;;;;;;;ICJEC,EAAA,CAAAC,cAAA,eAAoC;IAAAD,EAAA,CAAAE,MAAA,GAA8B;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAArCH,EAAA,CAAAI,SAAA,EAA8B;IAA9BJ,EAAA,CAAAK,kBAAA,OAAAC,OAAA,GAAAN,EAAA,CAAAO,WAAA,OAAAC,MAAA,CAAAC,MAAA,oBAAAH,OAAA,CAAAI,MAAA,MAA8B;;;;;;IAPzHV,EAAA,CAAAC,cAAA,gBAMiC;IAH/BD,EAAA,CAAAW,UAAA,mBAAAC,wEAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAN,MAAA,GAAAR,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASR,MAAA,CAAAS,cAAA,EAAgB;IAAA,EAAC;IAI1BjB,EAAA,CAAAE,MAAA,GAAqD;;IAAAF,EAAA,CAAAkB,UAAA,IAAAC,sDAAA,kBAAoC;IAC3FnB,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAC,cAAA,eAOkB;;IADhBD,EAAA,CAAAW,UAAA,mBAAAS,uEAAAC,MAAA;MAAArB,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAN,MAAA,GAAAR,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASR,MAAA,CAAAc,eAAA,CAAAD,MAAA,CAAAE,MAAA,CAA8B,OAAO,EAAE;IAAA,EAAC;IANnDvB,EAAA,CAAAG,YAAA,EAOkB;IAElBH,EAAA,CAAAwB,SAAA,8BAOuB;;;;IApBrBxB,EAFA,CAAAyB,UAAA,aAAAjB,MAAA,CAAAkB,OAAA,CAAoB,YAAA1B,EAAA,CAAA2B,eAAA,KAAAC,GAAA,EAAApB,MAAA,CAAAkB,OAAA,EAEU;IAC9B1B,EAAA,CAAAI,SAAA,EAAqD;IAArDJ,EAAA,CAAAK,kBAAA,MAAAL,EAAA,CAAAO,WAAA,mDAAqD;IAAOP,EAAA,CAAAI,SAAA,GAAc;IAAdJ,EAAA,CAAAyB,UAAA,UAAAjB,MAAA,CAAAkB,OAAA,CAAc;IAO1E1B,EAAA,CAAAI,SAAA,EAAsE;IAAtEJ,EAAA,CAAA6B,qBAAA,gBAAA7B,EAAA,CAAAO,WAAA,gDAAsE;IAGtEP,EAFA,CAAAyB,UAAA,aAAAjB,MAAA,CAAAkB,OAAA,CAAoB,UAAAlB,MAAA,CAAAsB,KAAA,CAEL;IAIf9B,EAAA,CAAAI,SAAA,GAAkB;IAIlBJ,EAJA,CAAAyB,UAAA,mBAAkB,gCACa,oBACZ,SAAAjB,MAAA,CAAAuB,eAAA,CACK,gBAAAvB,MAAA,CAAAwB,mBAAA,CACW;;;;;IAoBnChC,EAAA,CAAAiC,kBAAA,GAA6D;;;;;IAK/DjC,EADF,CAAAC,cAAA,cAA+C,eACzB;IAAAD,EAAA,CAAAE,MAAA,GAA4E;;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,UACxG;IAAAF,EAAA,CAAAC,cAAA,eAAoB;IAAAD,EAAA,CAAAE,MAAA,GAAqF;;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,WACjH;IAAAF,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAE,MAAA,IAAiF;;IACvGF,EADuG,CAAAG,YAAA,EAAO,EACxG;;;;;;IAHgBH,EAAA,CAAAI,SAAA,GAA4E;IAA5EJ,EAAA,CAAAkC,iBAAA,CAAAlC,EAAA,CAAAmC,WAAA,wBAAAnC,EAAA,CAAA2B,eAAA,KAAAS,GAAA,GAAAC,OAAA,GAAArC,EAAA,CAAAO,WAAA,OAAAC,MAAA,CAAAC,MAAA,oBAAA4B,OAAA,CAAA3B,MAAA,GAA4E;IAC5EV,EAAA,CAAAI,SAAA,GAAqF;IAArFJ,EAAA,CAAAkC,iBAAA,CAAAlC,EAAA,CAAAmC,WAAA,qBAAAnC,EAAA,CAAA2B,eAAA,KAAAS,GAAA,EAAApC,EAAA,CAAAO,WAAA,OAAA+B,OAAA,CAAAC,UAAA,CAAAC,MAAA,SAAqF;IACrFxC,EAAA,CAAAI,SAAA,GAAiF;IAAjFJ,EAAA,CAAAkC,iBAAA,CAAAlC,EAAA,CAAAmC,WAAA,yBAAAnC,EAAA,CAAA2B,eAAA,KAAAS,GAAA,EAAAE,OAAA,CAAAG,SAAA,CAAAC,QAAA,CAAAhC,MAAA,GAAiF;;;ADvCvG,MAAMiC,oBAAoB,GAAyB;EACjDC,SAAS,EAAIC,IAAU,IAAMA,IAAI,CAACC;CACnC;AAcD,OAAM,MAAOC,wBAAwB;EAoCnC,IACIC,WAAWA,CAAEC,MAAc;IAC7B,IAAI,CAACA,MAAM,EAAE;IAEb,IAAIC,GAAG,GAAGD,MAAM,EAAEE,MAAM,CAACN,IAAI,IAAI/C,UAAU,CAAC+C,IAAI,CAAC,IAC5C,CAAC/C,UAAU,CAAC+C,IAAI,CAAC,IAAI,IAAI,CAACO,gBAAgB,CAACC,UAAU,CAAC,CAACpE,iBAAiB,CAACqE,wBAAwB,CAAC,CAAC,CAAC;IAEzG,IAAI,CAAC,IAAI,CAACF,gBAAgB,CAACC,UAAU,CAAC,CAACpE,iBAAiB,CAACsE,eAAe,EAAEtE,iBAAiB,CAACuE,sBAAsB,CAAC,CAAC,EAAE;MACpHN,GAAG,GAAGA,GAAG,CAACC,MAAM,CAACN,IAAI,IAAI,CAAC/C,UAAU,CAAC+C,IAAI,CAAC,CAAC;IAC7C;IAEA,IAAI,CAACY,YAAY,GAAGP,GAAG;IAEvB,IAAI,CAACR,QAAQ,GAAGQ,GAAG,CAACxD,GAAG,CAACmD,IAAI,IAAIA,IAAI,CAACC,IAAI,CAAC;IAC1C,IAAI,CAACY,WAAW,GAAGT,MAAM,CAACvD,GAAG,CAACmD,IAAI,IAAIA,IAAI,CAACC,IAAI,CAAC;IAChD,IAAI,CAACa,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACjB,QAAQ,CAAC,CAAC,CAAC;EAC9C;EAEAkB,YAAoBC,OAAoB,EACVT,gBAAkC;IAD5C,KAAAS,OAAO,GAAPA,OAAO;IACG,KAAAT,gBAAgB,GAAhBA,gBAAgB;IAnD9C,KAAApB,mBAAmB,GAAG,IAAIjD,WAAW,EAAE;IACvC,KAAA+E,KAAK,GAAW,EAAE;IAClB,KAAAC,MAAM,GAAGhE,WAAW;IAGpB,KAAA2B,OAAO,GAAY,IAAI;IACvB,KAAAjB,MAAM,GAAG,IAAInB,aAAa,CAAS,CAAC,CAAC;IACrC,KAAA0E,KAAK,GAAW,EAAE;IAClB,KAAAC,cAAc,GAAW,EAAE;IAC3B,KAAAlC,eAAe,GAAuB,EAAE;IACxC,KAAAD,KAAK,GAAW,EAAE;IAClB,KAAAY,QAAQ,GAAa,EAAE;IAEvB,KAAAwB,WAAW,GAAiB,CAC1B,IAAIlF,UAAU,CAAC;MAAEmF,KAAK,EAAE;IAAc,CAAE,CAAC,CAC1C;IAIS,KAAAC,kBAAkB,GAA0B,IAAItF,YAAY,EAAE;IAC9D,KAAAuF,kBAAkB,GAAsB,IAAIvF,YAAY,EAAE;IAE5D,KAAA2E,YAAY,GAAW,EAAE,CAAC,CAAC;IAC3B,KAAAE,gBAAgB,GAAa,EAAE;IAC/B,KAAAD,WAAW,GAAa,EAAE;IAE1B,KAAAY,oBAAoB,GAAY,IAAI;IAEpC,KAAAC,YAAY,GAAG,IAAInF,eAAe,CAAS,EAAE,CAAC;IAC9C,KAAAoF,oBAAoB,GAAG,IAAIpF,eAAe,CAAW,EAAE,CAAC;IAC/C,KAAAqF,UAAU,GAAG,IAAIlF,OAAO,EAAQ;IAuB/C,IAAI,CAACsE,OAAO,CAACa,aAAa,EAAE,CACzBC,IAAI,CACH/E,SAAS,CAAC,IAAI,CAAC6E,UAAU,CAAC,CAC3B,CACAG,SAAS,CAACC,IAAI,IAAG;MAChB,IAAI,CAAC9C,eAAe,GAAG8C,IAAI,CAACnF,GAAG,CAAGoF,KAAY,IAAK;QACjD,MAAM;UAAEC,EAAE;UAAEZ;QAAK,CAAE,GAAGW,KAAK;QAC3B,OAAO;UACLC,EAAE;UACFC,IAAI,EAAEb;SACP;MACH,CAAC,CAAC;IACJ,CAAC,CAAC;EACN;EAEAc,QAAQA,CAAA;IACN,IAAI,CAACC,SAAS,EAAE;IAChB,IAAI,CAACC,WAAW,EAAE;IAElB,IAAI,CAACnD,mBAAmB,CAACoD,YAAY,CAClCT,IAAI,CACH/E,SAAS,CAAC,IAAI,CAAC6E,UAAU,CAAC,CAC3B,CACAG,SAAS,CAAGS,GAAa,IAAK;MAC7B,IAAI,CAACb,oBAAoB,CAACc,IAAI,CAACD,GAAG,CAAC;IACrC,CAAC,CAAC;EACN;EAEAE,WAAWA,CAAA;IACT,IAAI,CAACtB,cAAc,GAAG,EAAE;IACxB,IAAI,CAACQ,UAAU,CAACa,IAAI,EAAE;IACtB,IAAI,CAACb,UAAU,CAACe,QAAQ,EAAE;EAC5B;EAEAN,SAASA,CAAA;IACP,IAAI,CAACnB,MAAM,GAAGhE,WAAW;IACzB,IAAI,CAAC0F,WAAW,GAAG,IAAI,CAAC1B,MAAM,CAACZ,MAAM,CAACuC,IAAI,IAAIA,IAAI,CAACC,MAAM,CAAC;EAC5D;EAEAC,qBAAqBA,CAAEC,KAAY;IACjCA,KAAK,CAACC,cAAc,EAAE;IACtB,MAAMC,IAAI,GAA6B,IAAI,CAAC9B,cAAc,CAAC+B,MAAM,CAAC,CAAEC,MAAM,EAAEP,IAAI,KAAK;MACnFO,MAAM,CAACP,IAAI,CAAC5C,IAAI,CAAC,GAAG4C,IAAI;MACxB,OAAOO,MAAM;IACf,CAAC,EAAE,EAAE,CAAC;IAEN,IAAIC,UAAU,GAAW,IAAI,CAACC,IAAI,CAAC1D,SAAS,CAACC,QAAQ,CAClDS,MAAM,CAACL,IAAI,IAAI,IAAI,CAACa,gBAAgB,CAACyC,OAAO,CAACtD,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAC1DpD,GAAG,CAAGoD,IAAI,IAAMiD,IAAI,CAACjD,IAAI,CAAC,CAAC;IAC9B,IAAIuD,YAAY,GAAW,IAAI,CAAC5C,YAAY,CACzCN,MAAM,CAACN,IAAI,IAAI,CAAC,IAAI,CAACsD,IAAI,CAAC1D,SAAS,CAAC6D,UAAU,CAACzD,IAAI,CAACC,IAAI,CAAC,CAAC;IAE7D,IAAI,CAACuB,kBAAkB,CAACkC,IAAI,CAAC;MAAEL,UAAU;MAAEG;IAAY,CAAE,CAAC;EAC5D;EAEA/E,eAAeA,CAAEkF,KAAa;IAC5B,IAAI,CAACjC,YAAY,CAACe,IAAI,CAACkB,KAAK,CAAC;EAC/B;EAEAvF,cAAcA,CAAA;IACZ,IAAI,CAACR,MAAM,CACRkE,IAAI,CAAChF,IAAI,CAAC,CAAC,CAAC,CAAC,CACbiF,SAAS,CAAC6B,KAAK,IAAG;MACjB,MAAMC,KAAK,GAAaD,KAAK,CAAC/G,GAAG,CAACmD,IAAI,IAAIA,IAAI,CAACC,IAAI,CAAC;MACpD,IAAI,CAACqD,IAAI,CAAC1D,SAAS,CAACkE,MAAM,CAAC,GAAGD,KAAK,CAAC;MACpC,IAAI,CAACE,iBAAiB,EAAE;IAC1B,CAAC,CAAC;EACN;EAEQA,iBAAiBA,CAAA;IACvB,IAAI,IAAI,CAACtC,oBAAoB,EAAE;MAC7B,IAAI,CAACA,oBAAoB,GAAG,KAAK;MACjC,IAAI,CAACF,kBAAkB,CAACmC,IAAI,CAAC,IAAI,CAACjC,oBAAoB,CAAC;IACzD;EACF;EAEQa,WAAWA,CAAA;IACjB,MAAM0B,IAAI,GAAG,IAAI,CAACC,MAAM,CAACC,YAAY,CAACF,IAAI,IAAI,EAAE;IAChD,IAAI,CAAChD,OAAO,CAACmD,WAAW,CAACH,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CACxClC,IAAI,CACHjF,GAAG,CAACoE,KAAK,IAAG;MACV,IAAIZ,GAAG,GAAGY,KAAK,EAAEX,MAAM,CAACN,IAAI,IAAI/C,UAAU,CAAC+C,IAAI,CAAC,IAC3C,CAAC/C,UAAU,CAAC+C,IAAI,CAAC,IAAI,IAAI,CAACO,gBAAgB,CAACC,UAAU,CAAC,CAACpE,iBAAiB,CAACqE,wBAAwB,CAAC,CAAC,CAAC;MAEzG,IAAI,CAAC,IAAI,CAACF,gBAAgB,CAACC,UAAU,CAAC,CAACpE,iBAAiB,CAACsE,eAAe,EAAEtE,iBAAiB,CAACgI,mBAAmB,CAAC,CAAC,EAAE;QACjH/D,GAAG,GAAGA,GAAG,CAACC,MAAM,CAACN,IAAI,IAAI,CAAC/C,UAAU,CAAC+C,IAAI,CAAC,IAAI,IAAI,CAACa,WAAW,CAACwD,QAAQ,CAACrE,IAAI,CAACC,IAAI,CAAC,CAAC;MACrF;MAEA,IAAI,CAAC,IAAI,CAACM,gBAAgB,CAACC,UAAU,CAAC,CAACpE,iBAAiB,CAACsE,eAAe,EAAEtE,iBAAiB,CAACuE,sBAAsB,CAAC,CAAC,EAAE;QACpHN,GAAG,GAAGA,GAAG,CAACC,MAAM,CAACN,IAAI,IAAI,CAAC/C,UAAU,CAAC+C,IAAI,CAAC,IAAI,CAAC,IAAI,CAACa,WAAW,CAACwD,QAAQ,CAACrE,IAAI,CAACC,IAAI,CAAC,CAAC;MACtF;MAEA,OAAOI,GAAG;IACZ,CAAC,CAAC,CACH,CACA0B,SAAS,CAAC6B,KAAK,IAAG;MACjB,IAAI,CAAChG,MAAM,CAAC6E,IAAI,CAACmB,KAAK,CAAC;IACzB,CAAC,CAAC;IAEJ,OAAOpH,aAAa,CAAC,CACnB,IAAI,CAACkF,YAAY,CAACI,IAAI,CAACnF,YAAY,CAAC,GAAG,CAAC,EAAEC,oBAAoB,EAAE,CAAC,EACjE,IAAI,CAAC+E,oBAAoB,EACzB,IAAI,CAAC/D,MAAM,CACZ,CAAC,CAACkE,IAAI,CAACjF,GAAG,CAAC,CAAE,CAACyH,MAAM,EAAEC,cAAc,EAAEtD,KAAK,CAAC,KAAK;MAChD,IAAI,CAACG,cAAc,GAAGH,KAAK;MACzB,OAAOA,KAAK,CAACX,MAAM,CAAGuC,IAAI,IAAK;QAC7B,MAAM;UAAEvB,KAAK;UAAErB,IAAI;UAAEuE,MAAM,EAAEC;QAAU,CAAE,GAAG5B,IAAI;QAChD,MAAM6B,kBAAkB,GAAGpD,KAAK,CAACqD,WAAW,EAAE,CAACpB,OAAO,CAACe,MAAM,CAACK,WAAW,EAAE,CAAC,KAAK,CAAC,CAAC,IAC9E1E,IAAI,CAAC0E,WAAW,EAAE,CAACpB,OAAO,CAACe,MAAM,CAACK,WAAW,EAAE,CAAC,KAAK,CAAC,CAAC;QAC5D,MAAMC,YAAY,GAAG,CAACL,cAAc,CAAC1G,MAAM,GACvC,IAAI,GAAG4G,UAAU,CAACI,IAAI,CAAC5C,KAAK,IAAIsC,cAAc,CAAChB,OAAO,CAACtB,KAAK,CAACC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;QAE5E,OAAO0C,YAAY,IAAIF,kBAAkB;MAC3C,CAAC,CAAC;IACJ,CAAC,CAAC,CACH,CAAC3C,SAAS,CAAC1B,GAAG,IAAG;MAChB,IAAI,CAACY,KAAK,GAAGZ,GAAG;MAChB,IAAI,CAACxB,OAAO,GAAG,KAAK;IACtB,CAAC,CAAC;EACJ;;;uCAhLWqB,wBAAwB,EAAA/C,EAAA,CAAA2H,iBAAA,CAAAC,EAAA,CAAA/H,WAAA,GAAAG,EAAA,CAAA2H,iBAAA,CAAAE,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;YAAxB/E,wBAAwB;MAAAgF,SAAA;MAAAC,SAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;yBAqBxB/I,iBAAiB;;;;;;;;;;;;;;;uCA7BjB,CACTU,WAAW,EACX;QACEuI,OAAO,EAAElJ,qCAAqC;QAC9CmJ,QAAQ,EAAE1F;OACX,CACF;MAAA2F,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAR,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC5BHlI,EAAA,CAAAkB,UAAA,IAAAyH,+CAAA,iCAAA3I,EAAA,CAAA4I,sBAAA,CAA0B;UAgCxB5I,EADF,CAAAC,cAAA,aAAgC,WACxB;UAAAD,EAAA,CAAAE,MAAA,GAA8B;;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC3CH,EAAA,CAAAC,cAAA,0BAYgC;UAC9BD,EAAA,CAAAkB,UAAA,IAAA2H,gDAAA,0BAA8C;UAElD7I,EADE,CAAAG,YAAA,EAAgB,EACZ;UAENH,EAAA,CAAAkB,UAAA,IAAA4H,uCAAA,mBAA+C;;;;;UAlBvC9I,EAAA,CAAAI,SAAA,GAA8B;UAA9BJ,EAAA,CAAAkC,iBAAA,CAAAlC,EAAA,CAAAO,WAAA,QAAA+B,OAAA,kBAAAA,OAAA,CAAAyG,QAAA,EAA8B;UAIlC/I,EAAA,CAAAI,SAAA,GAAmB;UASnBJ,EATA,CAAAyB,UAAA,YAAA0G,GAAA,CAAAzG,OAAA,CAAmB,WAAAyG,GAAA,CAAA1C,WAAA,CACG,SAAA0C,GAAA,CAAArE,KAAA,CACR,aAAAqE,GAAA,CAAAzF,QAAA,CACO,2BACK,gBACX,4BACY,gBAAAyF,GAAA,CAAAjE,WAAA,CACA,2BACD,8BACG;UACdlE,EAAA,CAAAI,SAAA,GAA6B;UAA7BJ,EAAA,CAAAyB,UAAA,qBAAAuH,cAAA,CAA6B;UAI1ChJ,EAAA,CAAAI,SAAA,EAAc;UAAdJ,EAAA,CAAAyB,UAAA,UAAA0G,GAAA,CAAAzG,OAAA,CAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}