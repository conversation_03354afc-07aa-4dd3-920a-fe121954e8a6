{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiChipsAutocompleteModule, SwuiGridModule } from '@skywind-group/lib-swui';\nimport { BoConfirmationModule } from 'src/app/common/components/bo-confirmation/bo-confirmation.module';\nimport { DownloadCsvModule } from '../../../../../../common/components/download-csv/download-csv.module';\nimport { BaIfAllowedModule } from '../../../../../../common/directives/baIfAllowed/baIfAllowed.module';\nimport { GameService } from '../../../../../../common/services/game.service';\nimport { IframeViewModalModule } from '../../../../../gamehistory/components/internal-game-history/modals/iframe-view-modal/iframe-view-modal.module';\nimport { CustomerUnfinishedGameHistoryComponent } from './customer-unfinished-game-history.component';\nimport { CustomerUnfinishedGameHistoryService } from './customer-unfinished-game-history.service';\nimport * as i0 from \"@angular/core\";\nexport class CustomerUnfinishedGameHistoryModule {\n  static {\n    this.ɵfac = function CustomerUnfinishedGameHistoryModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CustomerUnfinishedGameHistoryModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: CustomerUnfinishedGameHistoryModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [CustomerUnfinishedGameHistoryService, GameService],\n      imports: [CommonModule, BoConfirmationModule, SwuiChipsAutocompleteModule, SwuiGridModule, IframeViewModalModule, BaIfAllowedModule, MatMenuModule, TranslateModule, MatButtonModule, MatTooltipModule, MatIconModule, DownloadCsvModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(CustomerUnfinishedGameHistoryModule, {\n    declarations: [CustomerUnfinishedGameHistoryComponent],\n    imports: [CommonModule, BoConfirmationModule, SwuiChipsAutocompleteModule, SwuiGridModule, IframeViewModalModule, BaIfAllowedModule, MatMenuModule, TranslateModule, MatButtonModule, MatTooltipModule, MatIconModule, DownloadCsvModule],\n    exports: [CustomerUnfinishedGameHistoryComponent]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "MatButtonModule", "MatIconModule", "MatMenuModule", "MatTooltipModule", "TranslateModule", "SwuiChipsAutocompleteModule", "SwuiGridModule", "BoConfirmationModule", "DownloadCsvModule", "BaIfAllowedModule", "GameService", "IframeViewModalModule", "CustomerUnfinishedGameHistoryComponent", "CustomerUnfinishedGameHistoryService", "CustomerUnfinishedGameHistoryModule", "imports", "declarations", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/player/components/customer-page/customer-game-history/customer-unfinished-game-history/customer-unfinished-game-history.module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiChipsAutocompleteModule, SwuiGridModule } from '@skywind-group/lib-swui';\nimport { BoConfirmationModule } from 'src/app/common/components/bo-confirmation/bo-confirmation.module';\nimport { DownloadCsvModule } from '../../../../../../common/components/download-csv/download-csv.module';\nimport { BaIfAllowedModule } from '../../../../../../common/directives/baIfAllowed/baIfAllowed.module';\nimport { GameService } from '../../../../../../common/services/game.service';\n\nimport { IframeViewModalModule } from '../../../../../gamehistory/components/internal-game-history/modals/iframe-view-modal/iframe-view-modal.module';\n\nimport { CustomerUnfinishedGameHistoryComponent } from './customer-unfinished-game-history.component';\nimport { CustomerUnfinishedGameHistoryService } from './customer-unfinished-game-history.service';\n\n\n@NgModule({\n  declarations: [\n    CustomerUnfinishedGameHistoryComponent\n  ],\n  providers: [\n    CustomerUnfinishedGameHistoryService,\n    GameService,\n  ],\n  exports: [\n    CustomerUnfinishedGameHistoryComponent\n  ],\n  imports: [\n    CommonModule,\n    BoConfirmationModule,\n    SwuiChipsAutocompleteModule,\n    SwuiGridModule,\n    IframeViewModalModule,\n    BaIfAllowedModule,\n    MatMenuModule,\n    TranslateModule,\n    MatButtonModule,\n    MatTooltipModule,\n    MatIconModule,\n    DownloadCsvModule,\n  ],\n})\nexport class CustomerUnfinishedGameHistoryModule {\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,2BAA2B,EAAEC,cAAc,QAAQ,yBAAyB;AACrF,SAASC,oBAAoB,QAAQ,kEAAkE;AACvG,SAASC,iBAAiB,QAAQ,sEAAsE;AACxG,SAASC,iBAAiB,QAAQ,oEAAoE;AACtG,SAASC,WAAW,QAAQ,gDAAgD;AAE5E,SAASC,qBAAqB,QAAQ,+GAA+G;AAErJ,SAASC,sCAAsC,QAAQ,8CAA8C;AACrG,SAASC,oCAAoC,QAAQ,4CAA4C;;AA6BjG,OAAM,MAAOC,mCAAmC;;;uCAAnCA,mCAAmC;IAAA;EAAA;;;YAAnCA;IAAmC;EAAA;;;iBAtBnC,CACTD,oCAAoC,EACpCH,WAAW,CACZ;MAAAK,OAAA,GAKChB,YAAY,EACZQ,oBAAoB,EACpBF,2BAA2B,EAC3BC,cAAc,EACdK,qBAAqB,EACrBF,iBAAiB,EACjBP,aAAa,EACbE,eAAe,EACfJ,eAAe,EACfG,gBAAgB,EAChBF,aAAa,EACbO,iBAAiB;IAAA;EAAA;;;2EAGRM,mCAAmC;IAAAE,YAAA,GAxB5CJ,sCAAsC;IAAAG,OAAA,GAUtChB,YAAY,EACZQ,oBAAoB,EACpBF,2BAA2B,EAC3BC,cAAc,EACdK,qBAAqB,EACrBF,iBAAiB,EACjBP,aAAa,EACbE,eAAe,EACfJ,eAAe,EACfG,gBAAgB,EAChBF,aAAa,EACbO,iBAAiB;IAAAS,OAAA,GAdjBL,sCAAsC;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}