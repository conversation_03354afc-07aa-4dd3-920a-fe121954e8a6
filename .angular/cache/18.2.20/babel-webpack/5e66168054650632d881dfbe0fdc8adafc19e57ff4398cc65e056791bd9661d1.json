{"ast": null, "code": "var si = typeof setImmediate === 'function',\n  tick;\nif (si) {\n  tick = function (fn) {\n    setImmediate(fn);\n  };\n} else {\n  tick = function (fn) {\n    setTimeout(fn, 0);\n  };\n}\nmodule.exports = tick;", "map": {"version": 3, "names": ["si", "setImmediate", "tick", "fn", "setTimeout", "module", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/node_modules/ticky/ticky-browser.js"], "sourcesContent": ["var si = typeof setImmediate === 'function', tick;\nif (si) {\n  tick = function (fn) { setImmediate(fn); };\n} else {\n  tick = function (fn) { setTimeout(fn, 0); };\n}\n\nmodule.exports = tick;"], "mappings": "AAAA,IAAIA,EAAE,GAAG,OAAOC,YAAY,KAAK,UAAU;EAAEC,IAAI;AACjD,IAAIF,EAAE,EAAE;EACNE,IAAI,GAAG,SAAAA,CAAUC,EAAE,EAAE;IAAEF,YAAY,CAACE,EAAE,CAAC;EAAE,CAAC;AAC5C,CAAC,MAAM;EACLD,IAAI,GAAG,SAAAA,CAAUC,EAAE,EAAE;IAAEC,UAAU,CAACD,EAAE,EAAE,CAAC,CAAC;EAAE,CAAC;AAC7C;AAEAE,MAAM,CAACC,OAAO,GAAGJ,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}