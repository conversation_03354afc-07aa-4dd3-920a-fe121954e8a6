{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FlexLayoutModule } from '@angular/flex-layout';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { MatSelectModule } from '@angular/material/select';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { PipesModule } from '../../../../../common/pipes/pipes.module';\nimport { SimplePromoService } from '../../../../../common/services/simple-promo.service';\nimport { MatPlayerFormModule } from '../../mat-player-form/player-form.module';\nimport { ApplySimpleFreebetDialogComponent } from './apply-simple-freebet-dialog.component';\nimport { CreatePlayerDialogComponent } from './create-player-dialog.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nexport class CustomerListDialogsModule {\n  static {\n    this.ɵfac = function CustomerListDialogsModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CustomerListDialogsModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: CustomerListDialogsModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [SimplePromoService],\n      imports: [CommonModule, TranslateModule.forChild(), MatPlayerFormModule, MatDialogModule, MatButtonModule, PipesModule, MatSelectModule, FlexLayoutModule, ReactiveFormsModule, MatProgressBarModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(CustomerListDialogsModule, {\n    declarations: [ApplySimpleFreebetDialogComponent, CreatePlayerDialogComponent],\n    imports: [CommonModule, i1.TranslateModule, MatPlayerFormModule, MatDialogModule, MatButtonModule, PipesModule, MatSelectModule, FlexLayoutModule, ReactiveFormsModule, MatProgressBarModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FlexLayoutModule", "ReactiveFormsModule", "MatButtonModule", "MatDialogModule", "MatProgressBarModule", "MatSelectModule", "TranslateModule", "PipesModule", "SimplePromoService", "MatPlayerFormModule", "ApplySimpleFreebetDialogComponent", "CreatePlayerDialogComponent", "CustomerListDialogsModule", "imports", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "i1"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/player/components/list/dialogs/customer-list-dialogs.module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { FlexLayoutModule } from '@angular/flex-layout';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { MatSelectModule } from '@angular/material/select';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { PipesModule } from '../../../../../common/pipes/pipes.module';\nimport { SimplePromoService } from '../../../../../common/services/simple-promo.service';\nimport { MatPlayerFormModule } from '../../mat-player-form/player-form.module';\nimport { ApplySimpleFreebetDialogComponent } from './apply-simple-freebet-dialog.component';\nimport { CreatePlayerDialogComponent } from './create-player-dialog.component';\n\n\n@NgModule({\n  imports: [\n    CommonModule,\n    TranslateModule.forChild(),\n    MatPlayerFormModule,\n    MatDialogModule,\n    MatButtonModule,\n    PipesModule,\n    MatSelectModule,\n    FlexLayoutModule,\n    ReactiveFormsModule,\n    MatProgressBarModule,\n  ],\n  declarations: [\n    ApplySimpleFreebetDialogComponent,\n    CreatePlayerDialogComponent,\n  ],\n  exports: [],\n  providers: [\n    SimplePromoService\n  ],\n})\nexport class CustomerListDialogsModule {\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,WAAW,QAAQ,0CAA0C;AACtE,SAASC,kBAAkB,QAAQ,qDAAqD;AACxF,SAASC,mBAAmB,QAAQ,0CAA0C;AAC9E,SAASC,iCAAiC,QAAQ,yCAAyC;AAC3F,SAASC,2BAA2B,QAAQ,kCAAkC;;;AAyB9E,OAAM,MAAOC,yBAAyB;;;uCAAzBA,yBAAyB;IAAA;EAAA;;;YAAzBA;IAAyB;EAAA;;;iBAJzB,CACTJ,kBAAkB,CACnB;MAAAK,OAAA,GAlBCd,YAAY,EACZO,eAAe,CAACQ,QAAQ,EAAE,EAC1BL,mBAAmB,EACnBN,eAAe,EACfD,eAAe,EACfK,WAAW,EACXF,eAAe,EACfL,gBAAgB,EAChBC,mBAAmB,EACnBG,oBAAoB;IAAA;EAAA;;;2EAWXQ,yBAAyB;IAAAG,YAAA,GARlCL,iCAAiC,EACjCC,2BAA2B;IAAAE,OAAA,GAb3Bd,YAAY,EAAAiB,EAAA,CAAAV,eAAA,EAEZG,mBAAmB,EACnBN,eAAe,EACfD,eAAe,EACfK,WAAW,EACXF,eAAe,EACfL,gBAAgB,EAChBC,mBAAmB,EACnBG,oBAAoB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}