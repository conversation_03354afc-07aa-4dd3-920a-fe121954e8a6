{"ast": null, "code": "import { SwuiGridComponent, SwuiGridDataService, SwuiTopFilterDataService } from '@skywind-group/lib-swui';\nimport { Subject } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport { Entity as EntityModel } from '../../../../common/models/entity.model';\nimport { CsvService } from '../../../../common/services/csv.service';\nimport { entitiesStructureToSelectOptions } from '../../../../common/services/entity.service';\nimport { ReportRgService } from '../../../../common/services/reports/report-rg.service';\nimport { SCHEMA_FILTER, SCHEMA_LIST } from './schema';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../../common/services/entity.service\";\nimport * as i3 from \"../../../../common/services/reports/report-rg.service\";\nimport * as i4 from \"@skywind-group/lib-swui\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/icon\";\nimport * as i7 from \"@angular/material/tooltip\";\nconst COMPONENT_NAME = 'report-rg';\nexport class ReportRgComponent {\n  constructor({\n    snapshot: {\n      data: {\n        brief\n      }\n    }\n  }, entityService, service) {\n    this.entityService = entityService;\n    this.service = service;\n    this.componentName = COMPONENT_NAME;\n    this.schema = SCHEMA_LIST;\n    this.schemaFilter = SCHEMA_FILTER;\n    this.destroyed$ = new Subject();\n    if (brief && brief.type === EntityModel.TYPE_ENTITY) {\n      this.schemaFilter = SCHEMA_FILTER.map(item => {\n        if (item.field === 'path' && item.type === 'select') {\n          item.data = this.entityService.getShortStructure().pipe(map(structure => entitiesStructureToSelectOptions(structure, 0, [], false)));\n        }\n        return item;\n      });\n    } else {\n      this.schemaFilter = SCHEMA_FILTER.filter(({\n        field\n      }) => field !== 'path');\n    }\n  }\n  ngOnDestroy() {\n    this.destroyed$.next();\n    this.destroyed$.complete();\n  }\n  refreshGrid() {\n    this.grid.dataSource.loadData();\n  }\n  printPage() {\n    window.print();\n  }\n  exportPage() {\n    this.service.exportPage(this.grid.dataSource.data, this.grid.displayedColumns, this.grid.paginator.pageIndex + 1);\n  }\n  static {\n    this.ɵfac = function ReportRgComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ReportRgComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.EntityService), i0.ɵɵdirectiveInject(i3.ReportRgService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ReportRgComponent,\n      selectors: [[\"report-rg\"]],\n      viewQuery: function ReportRgComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(SwuiGridComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.grid = _t.first);\n        }\n      },\n      features: [i0.ɵɵProvidersFeature([CsvService, ReportRgService, SwuiTopFilterDataService, {\n        provide: SwuiGridDataService,\n        useExisting: ReportRgService\n      }])],\n      decls: 11,\n      vars: 6,\n      consts: [[3, \"title\"], [1, \"p-32\", \"sw-grid-layout\"], [1, \"sw-grid-layout__table\"], [3, \"schema\"], [3, \"schema\", \"columnsManagement\", \"savedFilteredPageName\", \"gridId\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Print current page\", 3, \"click\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Export current page\", 3, \"click\"]],\n      template: function ReportRgComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"lib-swui-page-panel\", 0);\n          i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵelement(3, \"lib-swui-schema-top-filter\", 3);\n          i0.ɵɵelementStart(4, \"lib-swui-grid\", 4)(5, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function ReportRgComponent_Template_button_click_5_listener() {\n            return ctx.printPage();\n          });\n          i0.ɵɵelementStart(6, \"mat-icon\");\n          i0.ɵɵtext(7, \"print\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function ReportRgComponent_Template_button_click_8_listener() {\n            return ctx.exportPage();\n          });\n          i0.ɵɵelementStart(9, \"mat-icon\");\n          i0.ɵɵtext(10, \"archive\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"title\", \"MENU_SECTIONS.reportsRG\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"schema\", ctx.schemaFilter);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"schema\", ctx.schema)(\"columnsManagement\", true)(\"savedFilteredPageName\", \"reportRg\")(\"gridId\", ctx.componentName);\n        }\n      },\n      dependencies: [i4.SwuiPagePanelComponent, i4.SwuiGridComponent, i5.MatIconButton, i6.MatIcon, i7.MatTooltip, i4.SwuiSchemaTopFilterComponent],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["SwuiGridComponent", "SwuiGridDataService", "SwuiTopFilterDataService", "Subject", "map", "Entity", "EntityModel", "CsvService", "entitiesStructureToSelectOptions", "ReportRgService", "SCHEMA_FILTER", "SCHEMA_LIST", "COMPONENT_NAME", "ReportRgComponent", "constructor", "snapshot", "data", "brief", "entityService", "service", "componentName", "schema", "schemaFilter", "destroyed$", "type", "TYPE_ENTITY", "item", "field", "getShortStructure", "pipe", "structure", "filter", "ngOnDestroy", "next", "complete", "refreshGrid", "grid", "dataSource", "loadData", "printPage", "window", "print", "exportPage", "displayedColumns", "paginator", "pageIndex", "i0", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "i2", "EntityService", "i3", "selectors", "viewQuery", "ReportRgComponent_Query", "rf", "ctx", "provide", "useExisting", "decls", "vars", "consts", "template", "ReportRgComponent_Template", "ɵɵelement", "ɵɵelementStart", "ɵɵlistener", "ReportRgComponent_Template_button_click_5_listener", "ɵɵtext", "ɵɵelementEnd", "ReportRgComponent_Template_button_click_8_listener", "ɵɵproperty", "ɵɵadvance"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/reports/components/report-rg/report-rg.component.ts", "/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/reports/components/report-rg/report-rg.component.html"], "sourcesContent": ["import { Component, ViewChild } from '@angular/core';\nimport { ActivatedRoute } from '@angular/router';\nimport { SwuiGridComponent, SwuiGridDataService, SwuiTopFilterDataService } from '@skywind-group/lib-swui';\nimport { Subject } from 'rxjs';\nimport { map } from 'rxjs/operators';\n\nimport { Entity as EntityModel } from '../../../../common/models/entity.model';\nimport { CsvService } from '../../../../common/services/csv.service';\nimport { entitiesStructureToSelectOptions, EntityService } from '../../../../common/services/entity.service';\nimport { ReportRgService } from '../../../../common/services/reports/report-rg.service';\nimport { Entity } from '../../../../common/typings';\nimport { RgReportItem } from '../../../../common/typings/reports/responsible-gaming';\nimport { SCHEMA_FILTER, SCHEMA_LIST } from './schema';\n\n\nconst COMPONENT_NAME: string = 'report-rg';\n\n@Component({\n  selector: COMPONENT_NAME,\n  templateUrl: './report-rg.component.html',\n  styleUrls: ['./report-rg.component.scss'],\n  providers: [\n    CsvService,\n    ReportRgService,\n    SwuiTopFilterDataService,\n    { provide: SwuiGridDataService, useExisting: ReportRgService }\n  ]\n})\n\nexport class ReportRgComponent {\n\n  readonly componentName = COMPONENT_NAME;\n  readonly schema = SCHEMA_LIST;\n  readonly schemaFilter = SCHEMA_FILTER;\n\n  @ViewChild(SwuiGridComponent) grid: SwuiGridComponent<RgReportItem>;\n\n  private readonly destroyed$ = new Subject<void>();\n\n  constructor({ snapshot: { data: { brief } } }: ActivatedRoute,\n    private readonly entityService: EntityService<Entity>,\n    private readonly service: ReportRgService,\n  ) {\n    if (brief && brief.type === EntityModel.TYPE_ENTITY) {\n      this.schemaFilter = SCHEMA_FILTER.map(item => {\n        if (item.field === 'path' && item.type === 'select') {\n          item.data = this.entityService.getShortStructure().pipe(\n            map(structure => entitiesStructureToSelectOptions(structure, 0, [], false))\n          );\n        }\n        return item;\n      });\n    } else {\n      this.schemaFilter = SCHEMA_FILTER.filter(({ field }) => field !== 'path');\n    }\n  }\n\n  ngOnDestroy() {\n    this.destroyed$.next();\n    this.destroyed$.complete();\n  }\n\n  refreshGrid() {\n    this.grid.dataSource.loadData();\n  }\n\n  printPage() {\n    window.print();\n  }\n\n  exportPage() {\n    this.service.exportPage(this.grid.dataSource.data, this.grid.displayedColumns, this.grid.paginator.pageIndex + 1);\n  }\n}\n", "<lib-swui-page-panel [title]=\"'MENU_SECTIONS.reportsRG'\"></lib-swui-page-panel>\n\n<div class=\"p-32 sw-grid-layout\">\n  <div class=\"sw-grid-layout__table\">\n    <lib-swui-schema-top-filter [schema]=\"schemaFilter\"></lib-swui-schema-top-filter>\n    <lib-swui-grid\n      [schema]=\"schema\"\n      [columnsManagement]=\"true\"\n      [savedFilteredPageName]=\"'reportRg'\"\n      [gridId]=\"componentName\">\n      <button mat-icon-button matTooltip=\"Print current page\" (click)=\"printPage()\">\n        <mat-icon>print</mat-icon>\n      </button>\n      <button mat-icon-button matTooltip=\"Export current page\" (click)=\"exportPage()\">\n        <mat-icon>archive</mat-icon>\n      </button>\n    </lib-swui-grid>\n  </div>\n</div>\n"], "mappings": "AAEA,SAASA,iBAAiB,EAAEC,mBAAmB,EAAEC,wBAAwB,QAAQ,yBAAyB;AAC1G,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,GAAG,QAAQ,gBAAgB;AAEpC,SAASC,MAAM,IAAIC,WAAW,QAAQ,wCAAwC;AAC9E,SAASC,UAAU,QAAQ,yCAAyC;AACpE,SAASC,gCAAgC,QAAuB,4CAA4C;AAC5G,SAASC,eAAe,QAAQ,uDAAuD;AAGvF,SAASC,aAAa,EAAEC,WAAW,QAAQ,UAAU;;;;;;;;;AAGrD,MAAMC,cAAc,GAAW,WAAW;AAc1C,OAAM,MAAOC,iBAAiB;EAU5BC,YAAY;IAAEC,QAAQ,EAAE;MAAEC,IAAI,EAAE;QAAEC;MAAK;IAAE;EAAE,CAAkB,EAC1CC,aAAoC,EACpCC,OAAwB;IADxB,KAAAD,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IAVjB,KAAAC,aAAa,GAAGR,cAAc;IAC9B,KAAAS,MAAM,GAAGV,WAAW;IACpB,KAAAW,YAAY,GAAGZ,aAAa;IAIpB,KAAAa,UAAU,GAAG,IAAIpB,OAAO,EAAQ;IAM/C,IAAIc,KAAK,IAAIA,KAAK,CAACO,IAAI,KAAKlB,WAAW,CAACmB,WAAW,EAAE;MACnD,IAAI,CAACH,YAAY,GAAGZ,aAAa,CAACN,GAAG,CAACsB,IAAI,IAAG;QAC3C,IAAIA,IAAI,CAACC,KAAK,KAAK,MAAM,IAAID,IAAI,CAACF,IAAI,KAAK,QAAQ,EAAE;UACnDE,IAAI,CAACV,IAAI,GAAG,IAAI,CAACE,aAAa,CAACU,iBAAiB,EAAE,CAACC,IAAI,CACrDzB,GAAG,CAAC0B,SAAS,IAAItB,gCAAgC,CAACsB,SAAS,EAAE,CAAC,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC,CAC5E;QACH;QACA,OAAOJ,IAAI;MACb,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAACJ,YAAY,GAAGZ,aAAa,CAACqB,MAAM,CAAC,CAAC;QAAEJ;MAAK,CAAE,KAAKA,KAAK,KAAK,MAAM,CAAC;IAC3E;EACF;EAEAK,WAAWA,CAAA;IACT,IAAI,CAACT,UAAU,CAACU,IAAI,EAAE;IACtB,IAAI,CAACV,UAAU,CAACW,QAAQ,EAAE;EAC5B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACC,IAAI,CAACC,UAAU,CAACC,QAAQ,EAAE;EACjC;EAEAC,SAASA,CAAA;IACPC,MAAM,CAACC,KAAK,EAAE;EAChB;EAEAC,UAAUA,CAAA;IACR,IAAI,CAACvB,OAAO,CAACuB,UAAU,CAAC,IAAI,CAACN,IAAI,CAACC,UAAU,CAACrB,IAAI,EAAE,IAAI,CAACoB,IAAI,CAACO,gBAAgB,EAAE,IAAI,CAACP,IAAI,CAACQ,SAAS,CAACC,SAAS,GAAG,CAAC,CAAC;EACnH;;;uCA3CWhC,iBAAiB,EAAAiC,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,aAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAA3C,eAAA;IAAA;EAAA;;;YAAjBI,iBAAiB;MAAAwC,SAAA;MAAAC,SAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;yBAMjBxD,iBAAiB;;;;;;;uCAdjB,CACTO,UAAU,EACVE,eAAe,EACfP,wBAAwB,EACxB;QAAEwD,OAAO,EAAEzD,mBAAmB;QAAE0D,WAAW,EAAElD;MAAe,CAAE,CAC/D;MAAAmD,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAR,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC1BHV,EAAA,CAAAmB,SAAA,6BAA+E;UAG7EnB,EADF,CAAAoB,cAAA,aAAiC,aACI;UACjCpB,EAAA,CAAAmB,SAAA,oCAAiF;UAM/EnB,EALF,CAAAoB,cAAA,uBAI2B,gBACqD;UAAtBpB,EAAA,CAAAqB,UAAA,mBAAAC,mDAAA;YAAA,OAASX,GAAA,CAAAlB,SAAA,EAAW;UAAA,EAAC;UAC3EO,EAAA,CAAAoB,cAAA,eAAU;UAAApB,EAAA,CAAAuB,MAAA,YAAK;UACjBvB,EADiB,CAAAwB,YAAA,EAAW,EACnB;UACTxB,EAAA,CAAAoB,cAAA,gBAAgF;UAAvBpB,EAAA,CAAAqB,UAAA,mBAAAI,mDAAA;YAAA,OAASd,GAAA,CAAAf,UAAA,EAAY;UAAA,EAAC;UAC7EI,EAAA,CAAAoB,cAAA,eAAU;UAAApB,EAAA,CAAAuB,MAAA,eAAO;UAIzBvB,EAJyB,CAAAwB,YAAA,EAAW,EACrB,EACK,EACZ,EACF;;;UAlBexB,EAAA,CAAA0B,UAAA,oCAAmC;UAIxB1B,EAAA,CAAA2B,SAAA,GAAuB;UAAvB3B,EAAA,CAAA0B,UAAA,WAAAf,GAAA,CAAAnC,YAAA,CAAuB;UAEjDwB,EAAA,CAAA2B,SAAA,EAAiB;UAGjB3B,EAHA,CAAA0B,UAAA,WAAAf,GAAA,CAAApC,MAAA,CAAiB,2BACS,qCACU,WAAAoC,GAAA,CAAArC,aAAA,CACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}