{"ast": null, "code": "import { fromGameInfo, PERMISSIONS_NAMES } from '@skywind-group/lib-swui';\nimport { of, Subject } from 'rxjs';\nimport { map, takeUntil, tap } from 'rxjs/operators';\nimport { isLiveGame } from '../../../../common/typings';\nimport { SetupEntityService } from '../entities/setup-entity.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../../common/services/game.service\";\nimport * as i3 from \"@skywind-group/lib-swui\";\nimport * as i4 from \"@ngx-translate/core\";\nimport * as i5 from \"../entities/setup-entity.service\";\nimport * as i6 from \"../../../../common/components/hints/hints.component\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/flex-layout/flex\";\nimport * as i9 from \"../entity-page-panel/entity-page-panel.component\";\nimport * as i10 from \"@angular/common\";\nexport class CascadeGamesComponent {\n  constructor({\n    snapshot\n  }, service, notificationService, translate, gamesSelectManagerService, swHubAuthService, entityService) {\n    this.service = service;\n    this.notificationService = notificationService;\n    this.translate = translate;\n    this.gamesSelectManagerService = gamesSelectManagerService;\n    this.swHubAuthService = swHubAuthService;\n    this.messageErrors = {\n      required: 'VALIDATION.required'\n    };\n    this.selected = [];\n    this.destroyed$ = new Subject();\n    entityService.initSnapshot(snapshot.data);\n    this.entity = entityService.entity;\n    this.entityPath = entityService.entity?.path;\n    const parentPath = entityService.entity?.entityParent.path;\n    this.games$ = parentPath ? this.service.getAllGames(parentPath, true, true).pipe(map(games => {\n      if (this.swHubAuthService.areGranted([PERMISSIONS_NAMES.ENTITY_LIVEGAME_ADD])) {\n        games = games.filter(game => !isLiveGame(game));\n      }\n      return games.map(game => fromGameInfo(game));\n    })) : of([]);\n  }\n  ngOnDestroy() {\n    this.destroyed$.next();\n    this.destroyed$.complete();\n  }\n  handleSelectedItemsChange({\n    games\n  }) {\n    this.selected = games.map(({\n      data\n    }) => data);\n  }\n  saveGames() {\n    if (this.entityPath && this.selected?.length) {\n      const gamesCode = this.selected.map(({\n        code\n      }) => code);\n      this.service.cascadeAddGames(this.entityPath, gamesCode).pipe(tap(() => {\n        const message = this.translate.instant('ADD_GAMES_CASCADE.gamesAddedSuccessfully', {\n          count: gamesCode.length\n        });\n        this.notificationService.success(message, '');\n        this.gamesSelectManagerService.setupSelectedGames([]);\n      }), takeUntil(this.destroyed$)).subscribe();\n    }\n  }\n  pageTitle() {\n    return this.translate.instant('ADD_GAMES_CASCADE.title', {\n      name: this.entity?.title || this.entity?.name\n    });\n  }\n  static {\n    this.ɵfac = function CascadeGamesComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CascadeGamesComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.GameService), i0.ɵɵdirectiveInject(i3.SwuiNotificationsService), i0.ɵɵdirectiveInject(i4.TranslateService), i0.ɵɵdirectiveInject(i3.GamesSelectManagerService), i0.ɵɵdirectiveInject(i3.SwHubAuthService), i0.ɵɵdirectiveInject(i5.SetupEntityService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CascadeGamesComponent,\n      selectors: [[\"cascade-games\"]],\n      features: [i0.ɵɵProvidersFeature([SetupEntityService])],\n      decls: 9,\n      vars: 10,\n      consts: [[3, \"entity\", \"pageTitle\"], [1, \"cascade-games-body\"], [\"message\", \"ADD_GAMES_CASCADE.hintText\"], [3, \"selectedItemsChanged\", \"hideLabelsTab\", \"availableGames\"], [\"fxLayout\", \"row\", \"fxLayoutAlign\", \"end center\", 1, \"padding-top16\"], [\"mat-flat-button\", \"\", \"color\", \"primary\", 3, \"click\", \"disabled\"]],\n      template: function CascadeGamesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"entity-page-panel\", 0);\n          i0.ɵɵelementStart(1, \"div\", 1);\n          i0.ɵɵelement(2, \"hints\", 2);\n          i0.ɵɵelementStart(3, \"lib-swui-games-select-manager\", 3);\n          i0.ɵɵpipe(4, \"async\");\n          i0.ɵɵlistener(\"selectedItemsChanged\", function CascadeGamesComponent_Template_lib_swui_games_select_manager_selectedItemsChanged_3_listener($event) {\n            return ctx.handleSelectedItemsChange($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 4)(6, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function CascadeGamesComponent_Template_button_click_6_listener() {\n            return ctx.saveGames();\n          });\n          i0.ɵɵtext(7);\n          i0.ɵɵpipe(8, \"translate\");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"entity\", ctx.entity)(\"pageTitle\", ctx.pageTitle());\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"hideLabelsTab\", true)(\"availableGames\", i0.ɵɵpipeBind1(4, 6, ctx.games$));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"disabled\", !ctx.selected || !ctx.selected.length);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(8, 8, \"DIALOG.save\"), \" \");\n        }\n      },\n      dependencies: [i6.HintsComponent, i3.SwuiGamesSelectManagerComponent, i7.MatButton, i8.DefaultLayoutDirective, i8.DefaultLayoutAlignDirective, i9.EntityPagePanelComponent, i10.AsyncPipe, i4.TranslatePipe],\n      styles: [\".cascade-games-body[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  display: block;\\n  background-color: white;\\n  margin-bottom: 58px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvYnVzaW5lc3MtbWFuYWdlbWVudC9jb21wb25lbnRzL2Nhc2NhZGUtZ2FtZXMvY2FzY2FkZS1nYW1lcy5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGFBQUE7RUFDQSxjQUFBO0VBQ0EsdUJBQUE7RUFDQSxtQkFBQTtBQUNGIiwic291cmNlc0NvbnRlbnQiOlsiLmNhc2NhZGUtZ2FtZXMtYm9keSB7XG4gIHBhZGRpbmc6IDE2cHg7XG4gIGRpc3BsYXk6IGJsb2NrO1xuICBiYWNrZ3JvdW5kLWNvbG9yOiB3aGl0ZTtcbiAgbWFyZ2luLWJvdHRvbTogNThweDtcbn1cbiJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["fromGameInfo", "PERMISSIONS_NAMES", "of", "Subject", "map", "takeUntil", "tap", "isLiveGame", "SetupEntityService", "CascadeGamesComponent", "constructor", "snapshot", "service", "notificationService", "translate", "gamesSelectManagerService", "swHubAuthService", "entityService", "messageErrors", "required", "selected", "destroyed$", "initSnapshot", "data", "entity", "entityPath", "path", "parentPath", "entityParent", "games$", "getAllGames", "pipe", "games", "areGranted", "ENTITY_LIVEGAME_ADD", "filter", "game", "ngOnDestroy", "next", "complete", "handleSelectedItemsChange", "saveGames", "length", "gamesCode", "code", "cascadeAddGames", "message", "instant", "count", "success", "setupSelectedGames", "subscribe", "pageTitle", "name", "title", "i0", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "i2", "GameService", "i3", "SwuiNotificationsService", "i4", "TranslateService", "GamesSelectManagerService", "SwHubAuthService", "i5", "selectors", "features", "ɵɵProvidersFeature", "decls", "vars", "consts", "template", "CascadeGamesComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵelementStart", "ɵɵlistener", "CascadeGamesComponent_Template_lib_swui_games_select_manager_selectedItemsChanged_3_listener", "$event", "ɵɵelementEnd", "CascadeGamesComponent_Template_button_click_6_listener", "ɵɵtext", "ɵɵproperty", "ɵɵadvance", "ɵɵpipeBind1", "ɵɵtextInterpolate1"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/cascade-games/cascade-games.component.ts", "/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/cascade-games/cascade-games.component.html"], "sourcesContent": ["import { Component, OnD<PERSON>roy } from '@angular/core';\nimport { ActivatedRoute } from '@angular/router';\nimport { TranslateService } from '@ngx-translate/core';\nimport {\n  fromGameInfo, GameSelectItem, GamesSelectManagerService, PERMISSIONS_NAMES, SelectItemGameInfo, SwHubAuthService, SwuiNotificationsService\n} from '@skywind-group/lib-swui';\nimport { of, Subject } from 'rxjs';\nimport { Observable } from 'rxjs/Observable';\nimport { map, takeUntil, tap } from 'rxjs/operators';\nimport { ErrorMessage } from '../../../../common/components/mat-user-editor/user-form.component';\nimport { GameService } from '../../../../common/services/game.service';\nimport { Entity, isLiveGame } from '../../../../common/typings';\nimport { SetupEntityService } from '../entities/setup-entity.service';\n\n@Component({\n  selector: 'cascade-games',\n  templateUrl: 'cascade-games.component.html',\n  styleUrls: [\n    './cascade-games.component.scss'\n  ],\n  providers: [\n    SetupEntityService,\n  ],\n})\nexport class CascadeGamesComponent implements OnDestroy {\n  readonly messageErrors: ErrorMessage = {\n    required: 'VALIDATION.required',\n  };\n  readonly games$: Observable<GameSelectItem[]>;\n  selected: SelectItemGameInfo[] = [];\n  entity: Entity;\n\n  private readonly entityPath?: string;\n  private readonly destroyed$ = new Subject<void>();\n\n  constructor(\n    { snapshot }: ActivatedRoute,\n    private readonly service: GameService,\n    private readonly notificationService: SwuiNotificationsService,\n    private readonly translate: TranslateService,\n    private readonly gamesSelectManagerService: GamesSelectManagerService,\n    private swHubAuthService: SwHubAuthService,\n    entityService: SetupEntityService,\n  ) {\n    entityService.initSnapshot(snapshot.data);\n    this.entity = entityService.entity;\n    this.entityPath = entityService.entity?.path;\n\n    const parentPath = entityService.entity?.entityParent.path;\n    this.games$ = parentPath ?\n      this.service.getAllGames(parentPath, true, true)\n        .pipe(\n          map(games => {\n            if (this.swHubAuthService.areGranted([PERMISSIONS_NAMES.ENTITY_LIVEGAME_ADD])) {\n              games = games.filter(game => !isLiveGame(game));\n            }\n\n            return games.map(game => fromGameInfo(game));\n          })\n        ) :\n      of([]);\n  }\n\n  ngOnDestroy() {\n    this.destroyed$.next();\n    this.destroyed$.complete();\n  }\n\n  handleSelectedItemsChange({ games }: any) {\n    this.selected = games.map(({ data }) => data);\n  }\n\n  saveGames() {\n    if (this.entityPath && this.selected?.length) {\n      const gamesCode = this.selected.map(({ code }) => code);\n      this.service.cascadeAddGames(this.entityPath, gamesCode).pipe(\n        tap(() => {\n          const message = this.translate.instant('ADD_GAMES_CASCADE.gamesAddedSuccessfully', {\n            count: gamesCode.length\n          });\n          this.notificationService.success(message, '');\n          this.gamesSelectManagerService.setupSelectedGames([]);\n        }\n        ),\n        takeUntil(this.destroyed$)\n      ).subscribe();\n    }\n  }\n\n  pageTitle(): string {\n    return this.translate.instant('ADD_GAMES_CASCADE.title', { name: (this.entity?.title || this.entity?.name) });\n  }\n}\n", "<entity-page-panel\n  [entity]=\"entity\"\n  [pageTitle]=\"pageTitle()\">\n</entity-page-panel>\n\n<div class=\"cascade-games-body\">\n  <hints message=\"ADD_GAMES_CASCADE.hintText\"></hints>\n\n  <lib-swui-games-select-manager\n    (selectedItemsChanged)=\"handleSelectedItemsChange($event)\"\n    [hideLabelsTab]=\"true\"\n    [availableGames]=\"games$ | async\"></lib-swui-games-select-manager>\n\n  <div fxLayout=\"row\" fxLayoutAlign=\"end center\" class=\"padding-top16\">\n    <button mat-flat-button\n            color=\"primary\"\n            [disabled]=\"!selected || !selected.length\"\n            (click)=\"saveGames()\">\n      {{ 'DIALOG.save' | translate }}\n    </button>\n  </div>\n</div>\n"], "mappings": "AAGA,SACEA,YAAY,EAA6CC,iBAAiB,QACrE,yBAAyB;AAChC,SAASC,EAAE,EAAEC,OAAO,QAAQ,MAAM;AAElC,SAASC,GAAG,EAAEC,SAAS,EAAEC,GAAG,QAAQ,gBAAgB;AAGpD,SAAiBC,UAAU,QAAQ,4BAA4B;AAC/D,SAASC,kBAAkB,QAAQ,kCAAkC;;;;;;;;;;;;AAYrE,OAAM,MAAOC,qBAAqB;EAWhCC,YACE;IAAEC;EAAQ,CAAkB,EACXC,OAAoB,EACpBC,mBAA6C,EAC7CC,SAA2B,EAC3BC,yBAAoD,EAC7DC,gBAAkC,EAC1CC,aAAiC;IALhB,KAAAL,OAAO,GAAPA,OAAO;IACP,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,yBAAyB,GAAzBA,yBAAyB;IAClC,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAhBjB,KAAAE,aAAa,GAAiB;MACrCC,QAAQ,EAAE;KACX;IAED,KAAAC,QAAQ,GAAyB,EAAE;IAIlB,KAAAC,UAAU,GAAG,IAAIlB,OAAO,EAAQ;IAW/Cc,aAAa,CAACK,YAAY,CAACX,QAAQ,CAACY,IAAI,CAAC;IACzC,IAAI,CAACC,MAAM,GAAGP,aAAa,CAACO,MAAM;IAClC,IAAI,CAACC,UAAU,GAAGR,aAAa,CAACO,MAAM,EAAEE,IAAI;IAE5C,MAAMC,UAAU,GAAGV,aAAa,CAACO,MAAM,EAAEI,YAAY,CAACF,IAAI;IAC1D,IAAI,CAACG,MAAM,GAAGF,UAAU,GACtB,IAAI,CAACf,OAAO,CAACkB,WAAW,CAACH,UAAU,EAAE,IAAI,EAAE,IAAI,CAAC,CAC7CI,IAAI,CACH3B,GAAG,CAAC4B,KAAK,IAAG;MACV,IAAI,IAAI,CAAChB,gBAAgB,CAACiB,UAAU,CAAC,CAAChC,iBAAiB,CAACiC,mBAAmB,CAAC,CAAC,EAAE;QAC7EF,KAAK,GAAGA,KAAK,CAACG,MAAM,CAACC,IAAI,IAAI,CAAC7B,UAAU,CAAC6B,IAAI,CAAC,CAAC;MACjD;MAEA,OAAOJ,KAAK,CAAC5B,GAAG,CAACgC,IAAI,IAAIpC,YAAY,CAACoC,IAAI,CAAC,CAAC;IAC9C,CAAC,CAAC,CACH,GACHlC,EAAE,CAAC,EAAE,CAAC;EACV;EAEAmC,WAAWA,CAAA;IACT,IAAI,CAAChB,UAAU,CAACiB,IAAI,EAAE;IACtB,IAAI,CAACjB,UAAU,CAACkB,QAAQ,EAAE;EAC5B;EAEAC,yBAAyBA,CAAC;IAAER;EAAK,CAAO;IACtC,IAAI,CAACZ,QAAQ,GAAGY,KAAK,CAAC5B,GAAG,CAAC,CAAC;MAAEmB;IAAI,CAAE,KAAKA,IAAI,CAAC;EAC/C;EAEAkB,SAASA,CAAA;IACP,IAAI,IAAI,CAAChB,UAAU,IAAI,IAAI,CAACL,QAAQ,EAAEsB,MAAM,EAAE;MAC5C,MAAMC,SAAS,GAAG,IAAI,CAACvB,QAAQ,CAAChB,GAAG,CAAC,CAAC;QAAEwC;MAAI,CAAE,KAAKA,IAAI,CAAC;MACvD,IAAI,CAAChC,OAAO,CAACiC,eAAe,CAAC,IAAI,CAACpB,UAAU,EAAEkB,SAAS,CAAC,CAACZ,IAAI,CAC3DzB,GAAG,CAAC,MAAK;QACP,MAAMwC,OAAO,GAAG,IAAI,CAAChC,SAAS,CAACiC,OAAO,CAAC,0CAA0C,EAAE;UACjFC,KAAK,EAAEL,SAAS,CAACD;SAClB,CAAC;QACF,IAAI,CAAC7B,mBAAmB,CAACoC,OAAO,CAACH,OAAO,EAAE,EAAE,CAAC;QAC7C,IAAI,CAAC/B,yBAAyB,CAACmC,kBAAkB,CAAC,EAAE,CAAC;MACvD,CAAC,CACA,EACD7C,SAAS,CAAC,IAAI,CAACgB,UAAU,CAAC,CAC3B,CAAC8B,SAAS,EAAE;IACf;EACF;EAEAC,SAASA,CAAA;IACP,OAAO,IAAI,CAACtC,SAAS,CAACiC,OAAO,CAAC,yBAAyB,EAAE;MAAEM,IAAI,EAAG,IAAI,CAAC7B,MAAM,EAAE8B,KAAK,IAAI,IAAI,CAAC9B,MAAM,EAAE6B;IAAK,CAAE,CAAC;EAC/G;;;uCAnEW5C,qBAAqB,EAAA8C,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,wBAAA,GAAAP,EAAA,CAAAC,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAAT,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAI,yBAAA,GAAAV,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAK,gBAAA,GAAAX,EAAA,CAAAC,iBAAA,CAAAW,EAAA,CAAA3D,kBAAA;IAAA;EAAA;;;YAArBC,qBAAqB;MAAA2D,SAAA;MAAAC,QAAA,GAAAd,EAAA,CAAAe,kBAAA,CAJrB,CACT9D,kBAAkB,CACnB;MAAA+D,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCtBHrB,EAAA,CAAAuB,SAAA,2BAGoB;UAEpBvB,EAAA,CAAAwB,cAAA,aAAgC;UAC9BxB,EAAA,CAAAuB,SAAA,eAAoD;UAEpDvB,EAAA,CAAAwB,cAAA,uCAGoC;;UAFlCxB,EAAA,CAAAyB,UAAA,kCAAAC,6FAAAC,MAAA;YAAA,OAAwBL,GAAA,CAAArC,yBAAA,CAAA0C,MAAA,CAAiC;UAAA,EAAC;UAExB3B,EAAA,CAAA4B,YAAA,EAAgC;UAGlE5B,EADF,CAAAwB,cAAA,aAAqE,gBAIrC;UAAtBxB,EAAA,CAAAyB,UAAA,mBAAAI,uDAAA;YAAA,OAASP,GAAA,CAAApC,SAAA,EAAW;UAAA,EAAC;UAC3Bc,EAAA,CAAA8B,MAAA,GACF;;UAEJ9B,EAFI,CAAA4B,YAAA,EAAS,EACL,EACF;;;UAnBJ5B,EADA,CAAA+B,UAAA,WAAAT,GAAA,CAAArD,MAAA,CAAiB,cAAAqD,GAAA,CAAAzB,SAAA,GACQ;UAQvBG,EAAA,CAAAgC,SAAA,GAAsB;UACtBhC,EADA,CAAA+B,UAAA,uBAAsB,mBAAA/B,EAAA,CAAAiC,WAAA,OAAAX,GAAA,CAAAhD,MAAA,EACW;UAKzB0B,EAAA,CAAAgC,SAAA,GAA0C;UAA1ChC,EAAA,CAAA+B,UAAA,cAAAT,GAAA,CAAAzD,QAAA,KAAAyD,GAAA,CAAAzD,QAAA,CAAAsB,MAAA,CAA0C;UAEhDa,EAAA,CAAAgC,SAAA,EACF;UADEhC,EAAA,CAAAkC,kBAAA,MAAAlC,EAAA,CAAAiC,WAAA,2BACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}