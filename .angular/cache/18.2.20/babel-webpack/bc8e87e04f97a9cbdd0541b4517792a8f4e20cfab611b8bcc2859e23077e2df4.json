{"ast": null, "code": "import { CdkVirtualScrollViewport } from '@angular/cdk/scrolling';\nimport { PERMISSIONS_NAMES } from '@skywind-group/lib-swui';\nimport { combineLatest, iif, of, Subject, throwError, timer, zip } from 'rxjs';\nimport { catchError, filter, finalize, map, mapTo, mergeMap, switchMap, take, takeUntil, tap } from 'rxjs/operators';\nimport { Entity as EntityModel, Entity } from '../../../../common/models/entity.model';\nimport { bsActions, bsDialogs, SHORT_STRUCTURE_ADDITIONAL } from './business-structure.service';\nimport { MatEntityEditDialogComponent } from './dialogs/mat-entity-edit-dialog/mat-entity-edit-dialog.component';\nimport { MatRegionalDialogComponent } from './dialogs/mat-regional-dialog/mat-regional-dialog.component';\nimport { SetupHintDialogComponent } from './dialogs/setup-hint-dialog/setup-hint-dialog.component';\nimport { StatusConfirmComponent } from './dialogs/status-confirm/status-confirm.component';\nimport { SearchByDomainModalComponent } from './search-by-domain-modal/search-by-domain-modal.component';\nimport { StructureEntityModel } from './structure-entity.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../common/services/entity.service\";\nimport * as i2 from \"./business-structure.service\";\nimport * as i3 from \"../../../../common/services/entity-settings.service\";\nimport * as i4 from \"../../../../common/services/merchant-types.service\";\nimport * as i5 from \"../../../../common/services/game.service\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"@angular/material/dialog\";\nimport * as i8 from \"@skywind-group/lib-swui\";\nimport * as i9 from \"@ngx-translate/core\";\nimport * as i10 from \"./mat-business-structure-search.service\";\nfunction MatBusinessStructureComponent_div_2_th_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 9);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, \"BUSINESS_STRUCTURE.actions\"));\n  }\n}\nfunction MatBusinessStructureComponent_div_2_tr_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 12);\n    i0.ɵɵlistener(\"onClick\", function MatBusinessStructureComponent_div_2_tr_21_Template_tr_onClick_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.handleExpandCollapse($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const entity_r3 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"bs-table-row--selected\", entity_r3.id === ctx_r1.foundIds[ctx_r1.foundIndex]);\n    i0.ɵɵproperty(\"entity\", entity_r3)(\"disabledCurrencyTooltips\", ctx_r1.disabledCurrencyTooltips)(\"canChangeTest\", ctx_r1.canChangeTest)(\"expanded\", ctx_r1.bsService.expandedEntities.get(entity_r3.id));\n  }\n}\nfunction MatBusinessStructureComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"div\", 6)(2, \"table\", 7)(3, \"thead\")(4, \"tr\")(5, \"th\", 8);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\", 8);\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"th\", 8);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\", 9);\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"th\", 9);\n    i0.ɵɵtext(18);\n    i0.ɵɵpipe(19, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(20, MatBusinessStructureComponent_div_2_th_20_Template, 3, 3, \"th\", 10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(21, MatBusinessStructureComponent_div_2_tr_21_Template, 1, 6, \"tr\", 11);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(7, 8, \"BUSINESS_STRUCTURE.title\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(10, 10, \"BUSINESS_STRUCTURE.code\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(13, 12, \"BUSINESS_STRUCTURE.key\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(16, 14, \"BUSINESS_STRUCTURE.regional\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(19, 16, \"BUSINESS_STRUCTURE.status\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isBrand);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"cdkVirtualForOf\", ctx_r1.entities)(\"cdkVirtualForTrackBy\", ctx_r1.trackByFn);\n  }\n}\nfunction MatBusinessStructureComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13);\n    i0.ɵɵelement(1, \"mat-progress-spinner\", 14);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MatBusinessStructureComponent_global_finder_4_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1, \"Nothing is found\");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction MatBusinessStructureComponent_global_finder_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"global-finder\", 15);\n    i0.ɵɵlistener(\"prev\", function MatBusinessStructureComponent_global_finder_4_Template_global_finder_prev_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onFind(-1));\n    })(\"next\", function MatBusinessStructureComponent_global_finder_4_Template_global_finder_next_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onFind(1));\n    })(\"valueChange\", function MatBusinessStructureComponent_global_finder_4_Template_global_finder_valueChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onFindChange($event));\n    })(\"stateChange\", function MatBusinessStructureComponent_global_finder_4_Template_global_finder_stateChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onStateFindChange($event));\n    })(\"close\", function MatBusinessStructureComponent_global_finder_4_Template_global_finder_close_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onFindClose());\n    });\n    i0.ɵɵtemplate(1, MatBusinessStructureComponent_global_finder_4_ng_container_1_Template, 2, 0, \"ng-container\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"index\", ctx_r1.foundIndex + 1)(\"length\", ctx_r1.foundIds == null ? null : ctx_r1.foundIds.length)(\"state\", ctx_r1.searchState);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !(ctx_r1.foundIds == null ? null : ctx_r1.foundIds.length));\n  }\n}\nexport class MatBusinessStructureComponent {\n  constructor(service, bsService, entitySettingsService, merchantTypesService, gameService, route, cdr, dialog, authService, translation, notifications, businessStructureSearchService) {\n    this.service = service;\n    this.bsService = bsService;\n    this.entitySettingsService = entitySettingsService;\n    this.merchantTypesService = merchantTypesService;\n    this.gameService = gameService;\n    this.route = route;\n    this.cdr = cdr;\n    this.dialog = dialog;\n    this.authService = authService;\n    this.translation = translation;\n    this.notifications = notifications;\n    this.businessStructureSearchService = businessStructureSearchService;\n    this.loading = true;\n    this.panelActions = [];\n    this.foundIndex = -1;\n    this.foundIds = [];\n    this.viewInit = false;\n    this.entities = [];\n    this.entityLoading = false;\n    this.disabledCurrencyTooltips = false;\n    this.canChangeTest = false;\n    this.destroy$ = new Subject();\n    const {\n      brief\n    } = this.route.snapshot.data;\n    const briefEntity = new EntityModel(brief);\n    this.isBrand = briefEntity.isOperator() || briefEntity.isMerchant;\n    this.searchState = businessStructureSearchService.searchState;\n    this.disabledCurrencyTooltips = !authService.areGranted([PERMISSIONS_NAMES.ENTITY_BALANCE]);\n    this.canChangeTest = authService.areGranted([PERMISSIONS_NAMES.ENTITY_CHANGESTATE_TEST]);\n  }\n  ngOnInit() {\n    this.useRouteResolvedData();\n    this.subscribeToStructure();\n    this.subscribeToActions();\n    if (!this.isBrand) {\n      this.setPanelActions();\n    }\n  }\n  ngAfterViewInit() {\n    this.viewInit = true;\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  trackByFn(_, item) {\n    return item.id;\n  }\n  isSuperAdmin() {\n    return this.authService.isSuperAdmin;\n  }\n  subscribeToActions() {\n    this.bsService.actions.pipe(takeUntil(this.destroy$)).subscribe(data => {\n      let {\n        modal,\n        type,\n        entity,\n        actionData\n      } = data;\n      if (type === bsActions.SHOW_DIALOG) {\n        switch (modal) {\n          case bsDialogs.ENTITY_EDIT:\n            if (entity.type === Entity.TYPE_MERCHANT) {\n              this.onEditItemMerchant(entity);\n            } else if (entity.type === Entity.TYPE_BRAND) {\n              this.onEditItemBrand(entity);\n            } else if (entity.type === Entity.TYPE_ENTITY) {\n              this.onEditItemReseller(entity);\n            } else {\n              this.onEditItem(entity);\n            }\n            break;\n          case bsDialogs.ENTITY_ADD:\n            this.onAddItem(entity);\n            break;\n          case bsDialogs.EDIT_REGIONAL:\n            this.onEditRegional(entity, actionData);\n            break;\n          case bsDialogs.SET_STATUS_CONFIRM:\n            this.onSetStatus(entity, {\n              confirmStatus: actionData['confirmStatus']\n            });\n            break;\n          default:\n            break;\n        }\n      }\n    });\n  }\n  useRouteResolvedData() {\n    this.checkBriefForMaster();\n  }\n  subscribeToStructure() {\n    this.service.getShortStructure(SHORT_STRUCTURE_ADDITIONAL.businessStructure, true).pipe(takeUntil(this.destroy$)).subscribe(str => {\n      this.bsService.setStructure(str);\n      if (Array.isArray(this.bsService.entities) && this.bsService.entities.length) {\n        this.handleExpandCollapse(this.bsService.entities[0].id, true);\n      }\n      this.loading = false;\n      this.cdr.markForCheck();\n    });\n  }\n  onEditItem(entity, ownSettings) {\n    this.dialogRef = this.dialog.open(MatEntityEditDialogComponent, {\n      width: '700px',\n      data: {\n        entity,\n        merchantTypeSchemas: this.merchantTypeSchemas,\n        ownSettings\n      },\n      disableClose: true\n    });\n    this.dialogRef.afterClosed().pipe(filter(val => !!val), tap(() => {\n      this.entityLoading = true;\n      this.cdr.detectChanges();\n    }), takeUntil(this.destroy$)).subscribe(data => {\n      this.onCreateEditEntity(data);\n    });\n  }\n  onEditItemMerchant(entity) {\n    combineLatest([this.service.getItemWithMerchantData(entity.path), this.entitySettingsService.getSettings(entity.path)]).pipe(map(([merchantEntity, settings]) => ({\n      ...merchantEntity,\n      settings\n    })), mergeMap(merchantEntity => this.merchantTypesService.get(merchantEntity.merchant.type, merchantEntity.path).pipe(tap(merchantTypeSchema => {\n      this.merchantTypeSchemas = merchantTypeSchema ? [merchantTypeSchema] : [];\n    }), map(() => merchantEntity))), takeUntil(this.destroy$)).subscribe(merchantEntity => {\n      entity.update({\n        merchant: merchantEntity.merchant,\n        settings: merchantEntity.settings\n      });\n      this.onEditItem(entity);\n    });\n  }\n  onEditItemBrand(ent) {\n    this.provideSettings(ent).pipe(takeUntil(this.destroy$)).subscribe(({\n      entity,\n      ownSettings\n    }) => {\n      this.onEditItem(entity, ownSettings);\n    });\n  }\n  onEditItemReseller(ent) {\n    this.provideSettings(ent).pipe(takeUntil(this.destroy$)).subscribe(({\n      entity,\n      ownSettings\n    }) => {\n      this.onEditItem(entity, ownSettings);\n    });\n  }\n  onAddItem(entity) {\n    this.merchantTypesService.fetch(entity.path).pipe(take(1)).subscribe(data => {\n      this.merchantTypeSchemas = data;\n      const newEntity = new StructureEntityModel({\n        parent: entity.path,\n        parentId: entity.id,\n        getEntityParent: () => entity\n      });\n      this.onEditItem(newEntity);\n    });\n  }\n  onFindChange({\n    value,\n    checked\n  }, fondedEntityId, position, scrollByText) {\n    if (!value) {\n      this.foundIndex = -1;\n      this.foundIds = [];\n      return;\n    }\n    this.foundIds = this.getSearchEntityIds(value, checked);\n    this.foundIndex = -1;\n    this.businessStructureSearchService.setText(value);\n    if (this.foundIds.length !== 0) {\n      this.onFind(fondedEntityId ? 0 : 1, fondedEntityId, position, scrollByText);\n    }\n  }\n  onFind(step, fondedEntityId, position, scrollByText = true) {\n    this.foundIndex = typeof position === 'number' ? position : this.foundIds.length ? (this.foundIds.length + this.foundIndex + step) % this.foundIds.length : -1;\n    this.expandToRoot(fondedEntityId || this.foundIds[this.foundIndex]);\n    this.businessStructureSearchService.setPosition(this.foundIndex);\n    this.businessStructureSearchService.setFoundedEntityId(fondedEntityId || this.foundIds[this.foundIndex]);\n    if (scrollByText) {\n      this.scrollByText();\n    }\n  }\n  onStateFindChange(state) {\n    timer(1).pipe(take(1)).subscribe(() => {\n      this.onFindChange({\n        value: state.text,\n        checked: state.checked\n      }, state.id, state.position);\n    });\n  }\n  onFindClose() {\n    this.foundIndex = -1;\n    this.foundIds = [];\n    this.businessStructureSearchService.clearState();\n  }\n  closeChildren(current) {\n    this.bsService.expandedEntities.set(current, false);\n    const {\n      children\n    } = this.bsService.entitiesObject[current];\n    children.forEach(id => {\n      this.closeChildren(id);\n    });\n  }\n  handleExpandCollapse(id, force) {\n    const isExpanded = !this.bsService.expandedEntities.get(id) || force;\n    if (isExpanded) {\n      this.bsService.expandedEntities.set(id, true);\n    } else {\n      this.closeChildren(id);\n    }\n    this.expandFounded();\n  }\n  expandFounded() {\n    this.entities = this.bsService.entities.filter((item, index) => {\n      return this.bsService.expandedEntities.get(item.id) || this.bsService.expandedEntities.get(item.parentId) || !index;\n    });\n    this.entitySuspendedStatus();\n  }\n  expandToRoot(id) {\n    if (id) {\n      this.bsService.expandedEntities.set(id, true);\n      return this.expandToRoot(this.bsService.entitiesObject[id].parentId);\n    }\n    this.expandFounded();\n  }\n  getSearchEntityIds(input, idOnly) {\n    if (idOnly) {\n      const id = this.bsService.entities.find(ent => ent.decryptedBrand.toString() === input)?.id;\n      return id ? [id] : [];\n    }\n    const fields = ['name', 'title', 'path', 'key', 'decryptedBrand', 'id'];\n    const ids = [];\n    this.bsService.entities.forEach(value => {\n      const has = fields.some(field => {\n        return value[field] && value[field].toString().toLowerCase().includes(input.toLowerCase());\n      });\n      if (has) {\n        ids.push(value.id);\n      }\n    });\n    return ids;\n  }\n  getScrollIndex(input) {\n    let indexes = 0;\n    this.entities.forEach((entity, index) => {\n      if (entity.id === input) {\n        indexes = index;\n      }\n    });\n    return indexes;\n  }\n  scrollByText() {\n    const foundEntityIndex = this.getScrollIndex(this.foundIds[this.foundIndex]) || 0;\n    this.viewPort?.scrollToIndex(foundEntityIndex, 'smooth');\n    this.cdr.detectChanges();\n  }\n  provideSettings(entity) {\n    const settings$ = iif(() => !entity.settings, this.entitySettingsService.getSettings(entity.path), of(entity.settings));\n    const ownSettings$ = this.entitySettingsService.getSettings(entity.path, true);\n    return zip(settings$, ownSettings$).pipe(map(([settings, ownSettings]) => {\n      entity.settings = settings;\n      return {\n        entity,\n        ownSettings\n      };\n    }));\n  }\n  onSetStatus(dialogEntity, data) {\n    this.dialogRef = this.dialog.open(StatusConfirmComponent, {\n      width: '400px',\n      data: {\n        entity: dialogEntity,\n        confirmStatus: data.confirmStatus\n      },\n      disableClose: true\n    });\n    this.dialogRef.afterClosed().pipe(filter(closeData => typeof closeData.entity !== 'undefined'), take(1)).subscribe(({\n      entity,\n      confirmStatus\n    }) => {\n      let statusChange$;\n      if (confirmStatus === 'maintenance' || entity.status === 'maintenance' && confirmStatus === 'normal') {\n        statusChange$ = this.service.setMaintenance(entity, confirmStatus);\n      } else if (confirmStatus === 'suspended' || entity.status === 'suspended' && confirmStatus === 'normal') {\n        statusChange$ = this.service.setStatus(entity, confirmStatus);\n      } else if (confirmStatus === 'test' || entity.status === 'test' && confirmStatus === 'normal') {\n        statusChange$ = this.service.setTest(entity, confirmStatus);\n      } else if ('blocked_by_admin' === confirmStatus || 'blocked_by_admin' === entity.status && confirmStatus === 'normal') {\n        statusChange$ = this.service.patchEntityStatus(entity, confirmStatus);\n      }\n      if (statusChange$) {\n        statusChange$.pipe(switchMap(() => this.bsService.updateEntityStructure(true)), takeUntil(this.destroy$)).subscribe(() => {\n          this.expandFounded();\n          this.cdr.detectChanges();\n        });\n      }\n    });\n  }\n  onEditRegional(dialogEntity, actionData) {\n    this.dialog.open(MatRegionalDialogComponent, {\n      width: '80vw',\n      data: {\n        ...dialogEntity,\n        activeTab: actionData,\n        entityParentPath: dialogEntity.parentId ? this.bsService.entitiesObject[dialogEntity.parentId].path : dialogEntity.path\n      },\n      disableClose: true\n    }).afterClosed().pipe(switchMap(() => this.bsService.updateEntityStructure(true)), take(1)).subscribe(() => {\n      this.expandFounded();\n      this.cdr.detectChanges();\n    });\n  }\n  onCreateEditEntity(data) {\n    const entity = new Entity(data.entity);\n    const updating = !!entity.key;\n    const settingsUpdate = {};\n    let applyEntitySource$;\n    if (entity.type === Entity.TYPE_MERCHANT) {\n      applyEntitySource$ = updating ? this.service.updateMerchantEntityItem(entity.asUpdateMerchantData(), entity.path) : this.service.createMerchantEntityItem(entity.asCreateMerchantData(), entity.entityParent.path);\n    } else {\n      applyEntitySource$ = updating ? this.service.updateEntityItem(entity) : this.service.createEntityItem(entity, entity.entityParent.path);\n    }\n    applyEntitySource$ = applyEntitySource$.pipe(this.addEntityPath(entity.path));\n    if (entity.type === Entity.TYPE_BRAND || entity.type === Entity.TYPE_MERCHANT) {\n      settingsUpdate['storePlayerInfo'] = data.entity.settings.storePlayerInfo;\n    }\n    if (entity.type === Entity.TYPE_BRAND || entity.type === Entity.TYPE_ENTITY) {\n      settingsUpdate['isPlayerPasswordChangeEnabled'] = data.entity.settings.isPlayerPasswordChangeEnabled;\n    }\n    if (entity.type === Entity.TYPE_BRAND) {\n      settingsUpdate['playerPrefix'] = data.entity.settings.playerPrefix;\n    }\n    if (settingsUpdate && Object.keys(settingsUpdate).length) {\n      applyEntitySource$ = applyEntitySource$.pipe(switchMap(newEntity => {\n        return this.entitySettingsService.patchSettings(settingsUpdate, newEntity.path).pipe(mapTo(newEntity));\n      }));\n    }\n    if (data.addGames) {\n      applyEntitySource$ = applyEntitySource$.pipe(this.addGames(entity.entityParent));\n    }\n    applyEntitySource$.pipe(tap(() => {\n      if (updating) {\n        this.notifications.success(this.translation.instant('BUSINESS_STRUCTURE.notificationEdited', {\n          name: entity.name\n        }));\n      }\n    }), catchError(err => {\n      this.service.handleErrors.call(this.service, err);\n      return throwError(err);\n    }), tap(appliedEntity => {\n      if (!updating) {\n        this.dialog.open(SetupHintDialogComponent, {\n          width: '700px',\n          data: {\n            entity: appliedEntity\n          },\n          disableClose: true\n        });\n      }\n    }), switchMap(() => this.bsService.updateEntityStructure(true)), finalize(() => {\n      this.entityLoading = false;\n      this.cdr.detectChanges();\n    }), takeUntil(this.destroy$)).subscribe(() => {\n      if (updating) {\n        this.dialogRef.close();\n      }\n      this.expandFounded();\n    });\n  }\n  addGames(entityParent) {\n    return mergeMap(newEntity => {\n      return this.gameService.getAllGames(entityParent.path, true, true).pipe(map(games => games.map(game => game.code)), filter(games => !!games.length), mergeMap(codes => this.gameService.setEntityGames(codes, newEntity.path).pipe(mapTo(newEntity))));\n    });\n  }\n  addEntityPath(builtPath) {\n    return map(newEntity => {\n      if (typeof newEntity.path === 'undefined' && builtPath !== '') {\n        newEntity.path = builtPath;\n      }\n      return newEntity;\n    });\n  }\n  setPanelActions() {\n    this.panelActions = [{\n      title: 'BUSINESS_STRUCTURE.btnExpand',\n      actionFn: () => this.expandAll(),\n      getStyle: () => {\n        return {\n          color: '#1373d5'\n        };\n      }\n    }, {\n      title: 'BUSINESS_STRUCTURE.searchByDomain',\n      color: 'primary',\n      icon: 'search',\n      availableFn: () => this.isSuperAdmin(),\n      actionFn: () => this.searchByDomain(),\n      disabledFn: () => this.loading\n    }, {\n      title: '',\n      hover: 'Download CSV',\n      icon: 'get_app',\n      getStyle: () => {\n        return {\n          width: '20px',\n          padding: '0',\n          'min-width': '20px'\n        };\n      },\n      availableFn: () => this.isSuperAdmin(),\n      actionFn: () => this.downloadCSV(),\n      disabledFn: () => this.loading\n    }];\n  }\n  expandAll() {\n    this.bsService.entities.forEach(({\n      id\n    }) => {\n      this.bsService.expandedEntities.set(id, true);\n    });\n    this.expandFounded();\n  }\n  downloadCSV() {\n    this.bsService.downloadCSV();\n  }\n  searchByDomain() {\n    this.dialogRef = this.dialog.open(SearchByDomainModalComponent, {\n      width: '500px',\n      disableClose: true\n    });\n  }\n  checkBriefForMaster() {\n    let briefData = this.route.snapshot.data['brief'];\n    if ('name' in briefData && briefData['name'] === Entity.MASTER_NAME) {\n      briefData['path'] = Entity.ROOT_PATH;\n    }\n  }\n  entitySuspendedStatus() {\n    this.entities.map(entity => {\n      const parent = entity.parentId ? this.bsService.entitiesObject[entity.parentId] : entity;\n      const isParentBlocked = parent.status === 'blocked_by_admin' || parent.status === 'suspended';\n      const isEntityBlocked = entity.status === 'blocked_by_admin' || entity.status === 'suspended';\n      if (isParentBlocked || parent.changeStatusState === 'blocked_by_parent') {\n        entity.changeStatusState = 'blocked_by_parent';\n      } else if (isEntityBlocked) {\n        entity.changeStatusState = 'blocked';\n      }\n    });\n  }\n  static {\n    this.ɵfac = function MatBusinessStructureComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatBusinessStructureComponent)(i0.ɵɵdirectiveInject(i1.EntityService), i0.ɵɵdirectiveInject(i2.BusinessStructureService), i0.ɵɵdirectiveInject(i3.EntitySettingsService), i0.ɵɵdirectiveInject(i4.MerchantTypesService), i0.ɵɵdirectiveInject(i5.GameService), i0.ɵɵdirectiveInject(i6.ActivatedRoute), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i7.MatDialog), i0.ɵɵdirectiveInject(i8.SwHubAuthService), i0.ɵɵdirectiveInject(i9.TranslateService), i0.ɵɵdirectiveInject(i8.SwuiNotificationsService), i0.ɵɵdirectiveInject(i10.MatBusinessStructureSearchService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: MatBusinessStructureComponent,\n      selectors: [[\"mat-business-structure\"]],\n      viewQuery: function MatBusinessStructureComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(CdkVirtualScrollViewport, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.viewPort = _t.first);\n        }\n      },\n      decls: 5,\n      vars: 8,\n      consts: [[3, \"title\", \"actions\"], [1, \"entities-wrapper\", 3, \"minBufferPx\", \"maxBufferPx\", \"itemSize\"], [\"class\", \"p-32\", 4, \"ngIf\"], [\"class\", \"bs-loading\", 4, \"ngIf\"], [3, \"index\", \"length\", \"state\", \"prev\", \"next\", \"valueChange\", \"stateChange\", \"close\", 4, \"ngIf\"], [1, \"p-32\"], [1, \"bs-structure-wrapper\"], [1, \"bs-table\"], [2, \"text-align\", \"left\"], [2, \"text-align\", \"center\"], [\"style\", \"text-align: center\", 4, \"ngIf\"], [\"entity-item\", \"\", \"class\", \"bs-table-row\", 3, \"bs-table-row--selected\", \"entity\", \"disabledCurrencyTooltips\", \"canChangeTest\", \"expanded\", \"onClick\", 4, \"cdkVirtualFor\", \"cdkVirtualForOf\", \"cdkVirtualForTrackBy\"], [\"entity-item\", \"\", 1, \"bs-table-row\", 3, \"onClick\", \"entity\", \"disabledCurrencyTooltips\", \"canChangeTest\", \"expanded\"], [1, \"bs-loading\"], [\"diameter\", \"48\", \"mode\", \"indeterminate\"], [3, \"prev\", \"next\", \"valueChange\", \"stateChange\", \"close\", \"index\", \"length\", \"state\"], [4, \"ngIf\"]],\n      template: function MatBusinessStructureComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"lib-swui-page-panel\", 0);\n          i0.ɵɵelementStart(1, \"cdk-virtual-scroll-viewport\", 1);\n          i0.ɵɵtemplate(2, MatBusinessStructureComponent_div_2_Template, 22, 18, \"div\", 2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(3, MatBusinessStructureComponent_div_3_Template, 2, 0, \"div\", 3)(4, MatBusinessStructureComponent_global_finder_4_Template, 2, 4, \"global-finder\", 4);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"title\", \"MENU_SECTIONS.businessManagement_structure\")(\"actions\", ctx.panelActions);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"minBufferPx\", 3960)(\"maxBufferPx\", 4008)(\"itemSize\", 48);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.loading || ctx.entityLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", (ctx.viewPort == null ? null : ctx.viewPort.getDataLength()) > 0);\n        }\n      },\n      styles: [\".entities-wrapper[_ngcontent-%COMP%] {\\n  height: calc(100vh - 122px);\\n}\\n\\n.bs-table[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border-collapse: separate;\\n  border-color: grey;\\n}\\n.bs-table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%] {\\n  vertical-align: middle;\\n  font-weight: 400;\\n}\\n.bs-table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%] {\\n  height: 48px;\\n}\\n.bs-table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  border-bottom-color: rgba(0, 0, 0, 0.12);\\n  border-bottom-width: 1px;\\n  border-bottom-style: solid;\\n  padding: 0 8px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: rgba(0, 0, 0, 0.54);\\n  text-align: center;\\n}\\n.bs-table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:first-of-type {\\n  padding-left: 15px;\\n}\\n.bs-table-body[_ngcontent-%COMP%] {\\n  display: table-row-group;\\n}\\n.bs-table-row[_ngcontent-%COMP%] {\\n  display: table-row;\\n  transition: background-color 0.15s ease-in-out;\\n}\\n.bs-table-row--selected[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 0, 0.3);\\n}\\n.bs-table-row[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 0, 0, 0.03);\\n}\\n\\n.bs-loading[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  height: 100%;\\n  width: 100%;\\n  background: rgba(0, 0, 0, 0.12);\\n}\\n\\n.bs-structure-wrapper[_ngcontent-%COMP%] {\\n  background: white;\\n  width: 100%;\\n  overflow: auto;\\n  margin-bottom: 24px;\\n}\\n\\n.toggle-expand-button[_ngcontent-%COMP%] {\\n  padding: 0;\\n  margin: 0;\\n  visibility: hidden;\\n}\\n\\n.expand-visible[_ngcontent-%COMP%] {\\n  visibility: visible;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      changeDetection: 0\n    });\n  }\n}", "map": {"version": 3, "names": ["CdkVirtualScrollViewport", "PERMISSIONS_NAMES", "combineLatest", "iif", "of", "Subject", "throwError", "timer", "zip", "catchError", "filter", "finalize", "map", "mapTo", "mergeMap", "switchMap", "take", "takeUntil", "tap", "Entity", "EntityModel", "bsActions", "bsDialogs", "SHORT_STRUCTURE_ADDITIONAL", "MatEntityEditDialogComponent", "MatRegionalDialogComponent", "SetupHintDialogComponent", "StatusConfirmComponent", "SearchByDomainModalComponent", "StructureEntityModel", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpipeBind1", "ɵɵlistener", "MatBusinessStructureComponent_div_2_tr_21_Template_tr_onClick_0_listener", "$event", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "handleExpandCollapse", "ɵɵclassProp", "entity_r3", "id", "foundIds", "foundIndex", "ɵɵproperty", "disabledCurrencyTooltips", "canChangeTest", "bsService", "expandedEntities", "get", "ɵɵtemplate", "MatBusinessStructureComponent_div_2_th_20_Template", "MatBusinessStructureComponent_div_2_tr_21_Template", "isBrand", "entities", "trackByFn", "ɵɵelement", "ɵɵelementContainerStart", "MatBusinessStructureComponent_global_finder_4_Template_global_finder_prev_0_listener", "_r4", "onFind", "MatBusinessStructureComponent_global_finder_4_Template_global_finder_next_0_listener", "MatBusinessStructureComponent_global_finder_4_Template_global_finder_valueChange_0_listener", "onFindChange", "MatBusinessStructureComponent_global_finder_4_Template_global_finder_stateChange_0_listener", "onStateFindChange", "MatBusinessStructureComponent_global_finder_4_Template_global_finder_close_0_listener", "onFindClose", "MatBusinessStructureComponent_global_finder_4_ng_container_1_Template", "length", "searchState", "MatBusinessStructureComponent", "constructor", "service", "entitySettingsService", "merchantTypesService", "gameService", "route", "cdr", "dialog", "authService", "translation", "notifications", "businessStructureSearchService", "loading", "panelActions", "viewInit", "entityLoading", "destroy$", "brief", "snapshot", "data", "briefEntity", "isOperator", "isMerchant", "areGranted", "ENTITY_BALANCE", "ENTITY_CHANGESTATE_TEST", "ngOnInit", "useRouteResolvedData", "subscribeToStructure", "subscribeToActions", "setPanelActions", "ngAfterViewInit", "ngOnDestroy", "next", "complete", "_", "item", "isSuperAdmin", "actions", "pipe", "subscribe", "modal", "type", "entity", "actionData", "SHOW_DIALOG", "ENTITY_EDIT", "TYPE_MERCHANT", "onEditItemMerchant", "TYPE_BRAND", "onEditItemBrand", "TYPE_ENTITY", "onEditItemReseller", "onEditItem", "ENTITY_ADD", "onAddItem", "EDIT_REGIONAL", "onEditRegional", "SET_STATUS_CONFIRM", "onSetStatus", "confirmStatus", "checkBriefForMaster", "getShortStructure", "businessStructure", "str", "setStructure", "Array", "isArray", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ownSettings", "dialogRef", "open", "width", "merchantTypeSchemas", "disableClose", "afterClosed", "val", "detectChanges", "onCreateEditEntity", "getItemWithMerchantData", "path", "getSettings", "merchantEntity", "settings", "merchant", "merchantTypeSchema", "update", "ent", "provideSettings", "fetch", "newEntity", "parent", "parentId", "getEntityParent", "value", "checked", "fondedEntityId", "position", "scrollByText", "getSearchEntityIds", "setText", "step", "expandToRoot", "setPosition", "setFoundedEntityId", "state", "text", "clearState", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "current", "set", "children", "entitiesObject", "for<PERSON>ach", "force", "isExpanded", "expandFounded", "index", "entitySuspendedStatus", "input", "idOnly", "find", "decry<PERSON><PERSON><PERSON>", "toString", "fields", "ids", "has", "some", "field", "toLowerCase", "includes", "push", "getScrollIndex", "indexes", "foundEntityIndex", "viewPort", "scrollToIndex", "settings$", "ownSettings$", "dialogEntity", "closeData", "statusChange$", "status", "setMaintenance", "setStatus", "setTest", "patchEntityStatus", "updateEntityStructure", "activeTab", "entityParentPath", "updating", "key", "settingsUpdate", "applyEntitySource$", "updateMerchantEntityItem", "asUpdateMerchantData", "createMerchantEntityItem", "asCreateMerchantData", "entityParent", "updateEntityItem", "createEntityItem", "addEntityPath", "storePlayerInfo", "isPlayerPasswordChangeEnabled", "playerPrefix", "Object", "keys", "patchSettings", "addGames", "success", "instant", "name", "err", "handleErrors", "call", "appliedEntity", "close", "getAllGames", "games", "game", "code", "codes", "setEntityGames", "builtPath", "title", "actionFn", "expandAll", "getStyle", "color", "icon", "availableFn", "searchByDomain", "disabledFn", "hover", "padding", "downloadCSV", "briefData", "MASTER_NAME", "ROOT_PATH", "isParentBlocked", "isEntityBlocked", "changeStatusState", "ɵɵdirectiveInject", "i1", "EntityService", "i2", "BusinessStructureService", "i3", "EntitySettingsService", "i4", "MerchantTypesService", "i5", "GameService", "i6", "ActivatedRoute", "ChangeDetectorRef", "i7", "MatDialog", "i8", "SwHubAuthService", "i9", "TranslateService", "SwuiNotificationsService", "i10", "MatBusinessStructureSearchService", "selectors", "viewQuery", "MatBusinessStructureComponent_Query", "rf", "ctx", "MatBusinessStructureComponent_div_2_Template", "MatBusinessStructureComponent_div_3_Template", "MatBusinessStructureComponent_global_finder_4_Template", "getDataLength"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/mat-business-structure/mat-business-structure.component.ts", "/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/mat-business-structure/mat-business-structure.component.html"], "sourcesContent": ["import { CdkVirtualScrollViewport } from '@angular/cdk/scrolling';\nimport {\n  AfterViewInit,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  OnDestroy,\n  OnInit,\n  ViewChild\n} from '@angular/core';\nimport { MatDialog, MatDialogRef } from '@angular/material/dialog';\nimport { ActivatedRoute } from '@angular/router';\nimport { TranslateService } from '@ngx-translate/core';\nimport { PanelAction, PERMISSIONS_NAMES, SwHubAuthService, SwuiNotificationsService } from '@skywind-group/lib-swui';\nimport { combineLatest, iif, Observable, of, OperatorFunction, Subject, throwError, timer, zip } from 'rxjs';\nimport { catchError, filter, finalize, map, mapTo, mergeMap, switchMap, take, takeUntil, tap } from 'rxjs/operators';\nimport { EntitySettingsModel } from '../../../../common/models/entity-settings.model';\nimport { Entity as EntityModel, Entity } from '../../../../common/models/entity.model';\nimport { EntitySettingsService } from '../../../../common/services/entity-settings.service';\nimport { EntityService } from '../../../../common/services/entity.service';\nimport { GameService } from '../../../../common/services/game.service';\nimport { MerchantTypeSchema, MerchantTypesService } from '../../../../common/services/merchant-types.service';\n\nimport { Country, Currency, Language } from '../../../../common/typings';\nimport { Jurisdiction } from '../../../../common/typings/jurisdiction';\nimport { bsActions, bsDialogs, BusinessStructureService, SHORT_STRUCTURE_ADDITIONAL } from './business-structure.service';\nimport { EditEntitySubmitData, MatEntityEditDialogComponent } from './dialogs/mat-entity-edit-dialog/mat-entity-edit-dialog.component';\nimport { MatRegionalDialogComponent } from './dialogs/mat-regional-dialog/mat-regional-dialog.component';\nimport { SetupHintDialogComponent } from './dialogs/setup-hint-dialog/setup-hint-dialog.component';\nimport { StatusConfirmComponent } from './dialogs/status-confirm/status-confirm.component';\nimport { GlobalFinderState } from './global-finder/global-finder.component';\nimport { MatBusinessStructureSearchService } from './mat-business-structure-search.service';\nimport { SearchByDomainModalComponent } from './search-by-domain-modal/search-by-domain-modal.component';\nimport { StructureEntityModel } from './structure-entity.model';\n\n\n@Component({\n  selector: 'mat-business-structure',\n  templateUrl: './mat-business-structure.component.html',\n  styleUrls: ['./mat-business-structure.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class MatBusinessStructureComponent implements OnInit, OnDestroy, AfterViewInit {\n\n  languages: Language[];\n  currencies: Currency[];\n  countries: Country[];\n  jurisdictions: Jurisdiction[];\n  loading = true;\n  panelActions: PanelAction[] = [];\n  merchantTypeSchemas: MerchantTypeSchema[];\n  foundIndex = -1;\n  foundIds: string[] = [];\n  isBrand: boolean;\n  viewInit = false;\n  searchState: GlobalFinderState;\n\n  entities: Entity[] = [];\n  entityLoading = false;\n  disabledCurrencyTooltips = false;\n  canChangeTest = false;\n  @ViewChild(CdkVirtualScrollViewport) viewPort: CdkVirtualScrollViewport;\n\n  private dialogRef: MatDialogRef<any>;\n  private destroy$ = new Subject();\n\n  constructor(\n    public service: EntityService<StructureEntityModel>,\n    public bsService: BusinessStructureService,\n    private entitySettingsService: EntitySettingsService<EntitySettingsModel>,\n    private merchantTypesService: MerchantTypesService,\n    private gameService: GameService,\n    private route: ActivatedRoute,\n    private cdr: ChangeDetectorRef,\n    private dialog: MatDialog,\n    private authService: SwHubAuthService,\n    private translation: TranslateService,\n    private notifications: SwuiNotificationsService,\n    private businessStructureSearchService: MatBusinessStructureSearchService\n  ) {\n    const {brief} = this.route.snapshot.data;\n    const briefEntity = new EntityModel(brief);\n    this.isBrand = briefEntity.isOperator() || briefEntity.isMerchant;\n    this.searchState = businessStructureSearchService.searchState;\n    this.disabledCurrencyTooltips = !authService.areGranted([PERMISSIONS_NAMES.ENTITY_BALANCE]);\n    this.canChangeTest = authService.areGranted([PERMISSIONS_NAMES.ENTITY_CHANGESTATE_TEST]);\n  }\n\n  ngOnInit() {\n    this.useRouteResolvedData();\n    this.subscribeToStructure();\n    this.subscribeToActions();\n\n    if (!this.isBrand) {\n      this.setPanelActions();\n    }\n  }\n\n  ngAfterViewInit() {\n    this.viewInit = true;\n  }\n\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  trackByFn(_, item: StructureEntityModel) {\n    return item.id;\n  }\n\n  isSuperAdmin(): boolean {\n    return this.authService.isSuperAdmin;\n  }\n\n  subscribeToActions() {\n    this.bsService.actions\n      .pipe(\n        takeUntil(this.destroy$)\n      ).subscribe(\n      (data) => {\n        let {modal, type, entity, actionData} = data;\n        if (type === bsActions.SHOW_DIALOG) {\n          switch (modal) {\n\n            case bsDialogs.ENTITY_EDIT:\n              if (entity.type === Entity.TYPE_MERCHANT) {\n                this.onEditItemMerchant(entity);\n              } else if (entity.type === Entity.TYPE_BRAND) {\n                this.onEditItemBrand(entity);\n              } else if (entity.type === Entity.TYPE_ENTITY) {\n                this.onEditItemReseller(entity);\n              } else {\n                this.onEditItem(entity);\n              }\n              break;\n\n            case bsDialogs.ENTITY_ADD:\n              this.onAddItem(entity);\n              break;\n\n            case bsDialogs.EDIT_REGIONAL:\n              this.onEditRegional(entity, actionData);\n              break;\n\n            case bsDialogs.SET_STATUS_CONFIRM:\n              this.onSetStatus(entity, {confirmStatus: actionData['confirmStatus']});\n              break;\n\n            default:\n              break;\n          }\n        }\n      }\n    );\n  }\n\n  useRouteResolvedData() {\n    this.checkBriefForMaster();\n  }\n\n  subscribeToStructure() {\n    this.service.getShortStructure(SHORT_STRUCTURE_ADDITIONAL.businessStructure, true)\n      .pipe(\n        takeUntil(this.destroy$)\n      )\n      .subscribe((str) => {\n        this.bsService.setStructure(str);\n\n        if (Array.isArray(this.bsService.entities) && this.bsService.entities.length) {\n          this.handleExpandCollapse(this.bsService.entities[0].id, true);\n        }\n\n        this.loading = false;\n        this.cdr.markForCheck();\n      });\n  }\n\n  onEditItem(entity: StructureEntityModel, ownSettings?: EntitySettingsModel) {\n    this.dialogRef = this.dialog.open(MatEntityEditDialogComponent, {\n      width: '700px',\n      data: {entity, merchantTypeSchemas: this.merchantTypeSchemas, ownSettings},\n      disableClose: true\n    });\n\n    this.dialogRef.afterClosed()\n      .pipe(\n        filter(val => !!val),\n        tap(() => {\n          this.entityLoading = true;\n          this.cdr.detectChanges();\n        }),\n        takeUntil(this.destroy$),\n      ).subscribe(data => {\n      this.onCreateEditEntity(data);\n    });\n  }\n\n  onEditItemMerchant(entity: StructureEntityModel) {\n    combineLatest([\n      this.service.getItemWithMerchantData(entity.path),\n      this.entitySettingsService.getSettings(entity.path)\n    ])\n      .pipe(\n        map(([merchantEntity, settings]) => ({...merchantEntity, settings})),\n        mergeMap(merchantEntity => this.merchantTypesService\n          .get(merchantEntity.merchant.type, merchantEntity.path).pipe(\n            tap((merchantTypeSchema) => {\n              this.merchantTypeSchemas = merchantTypeSchema ? [merchantTypeSchema] : [];\n            }),\n            map(() => merchantEntity)\n          )),\n        takeUntil(this.destroy$)\n      ).subscribe(merchantEntity => {\n        entity.update({merchant: merchantEntity.merchant, settings: merchantEntity.settings});\n\n        this.onEditItem(entity);\n      }\n    );\n  }\n\n  onEditItemBrand(ent: StructureEntityModel) {\n    this.provideSettings(ent)\n      .pipe(\n        takeUntil(this.destroy$)\n      ).subscribe(({entity, ownSettings}) => {\n      this.onEditItem(entity, ownSettings);\n    });\n  }\n\n  onEditItemReseller(ent: StructureEntityModel) {\n    this.provideSettings(ent).pipe(takeUntil(this.destroy$))\n      .subscribe(({entity, ownSettings}) => {\n        this.onEditItem(entity, ownSettings);\n      });\n  }\n\n  onAddItem(entity: StructureEntityModel) {\n    this.merchantTypesService.fetch(entity.path)\n      .pipe(take(1))\n      .subscribe((data) => {\n        this.merchantTypeSchemas = data;\n        const newEntity = new StructureEntityModel({\n          parent: entity.path,\n          parentId: entity.id,\n          getEntityParent: () => entity\n        });\n        this.onEditItem(newEntity);\n      });\n  }\n\n  onFindChange({value, checked}: { value: string, checked: boolean },\n               fondedEntityId?: string, position?: number, scrollByText?: boolean) {\n    if (!value) {\n      this.foundIndex = -1;\n      this.foundIds = [];\n      return;\n    }\n\n    this.foundIds = this.getSearchEntityIds(value, checked);\n    this.foundIndex = -1;\n    this.businessStructureSearchService.setText(value);\n\n    if (this.foundIds.length !== 0) {\n      this.onFind((fondedEntityId ? 0 : 1), fondedEntityId, position, scrollByText);\n    }\n  }\n\n  onFind(step: number, fondedEntityId?: string, position?: number, scrollByText: boolean = true) {\n    this.foundIndex = typeof position === 'number' ? position : this.foundIds.length\n      ? (this.foundIds.length + this.foundIndex + step) % this.foundIds.length\n      : -1;\n\n    this.expandToRoot(fondedEntityId || this.foundIds[this.foundIndex]);\n\n    this.businessStructureSearchService.setPosition(this.foundIndex);\n    this.businessStructureSearchService.setFoundedEntityId(fondedEntityId || this.foundIds[this.foundIndex]);\n\n    if (scrollByText) {\n      this.scrollByText();\n    }\n  }\n\n  onStateFindChange(state: GlobalFinderState) {\n    timer(1)\n      .pipe(take(1))\n      .subscribe(() => {\n        this.onFindChange({value: state.text, checked: state.checked}, state.id, state.position);\n      });\n  }\n\n  onFindClose() {\n    this.foundIndex = -1;\n    this.foundIds = [];\n    this.businessStructureSearchService.clearState();\n  }\n\n  closeChildren(current: string) {\n    this.bsService.expandedEntities.set(current, false);\n\n    const {children} = this.bsService.entitiesObject[current];\n\n    children.forEach((id: string) => {\n      this.closeChildren(id);\n    });\n  }\n\n  handleExpandCollapse(id: string, force?: boolean) {\n    const isExpanded = !this.bsService.expandedEntities.get(id) || force;\n\n    if (isExpanded) {\n      this.bsService.expandedEntities.set(id, true);\n    } else {\n      this.closeChildren(id);\n    }\n\n    this.expandFounded();\n  }\n\n  private expandFounded() {\n    this.entities = this.bsService.entities.filter((item: any, index: number) => {\n      return this.bsService.expandedEntities.get(item.id) || this.bsService.expandedEntities.get(item.parentId) || !index;\n    });\n\n    this.entitySuspendedStatus();\n  }\n\n  private expandToRoot(id: string) {\n    if (id) {\n      this.bsService.expandedEntities.set(id, true);\n      return this.expandToRoot(this.bsService.entitiesObject[id].parentId);\n    }\n    this.expandFounded();\n  }\n\n  private getSearchEntityIds(input: string, idOnly: boolean): string[] {\n    if (idOnly) {\n      const id = this.bsService.entities.find((ent) => ent.decryptedBrand.toString() === input)?.id;\n      return id ? [id] : [];\n    }\n\n    const fields = ['name', 'title', 'path', 'key', 'decryptedBrand', 'id'];\n    const ids = [];\n\n    this.bsService.entities.forEach((value: Entity) => {\n      const has = fields.some(field => {\n        return value[field] && value[field].toString().toLowerCase().includes(input.toLowerCase());\n      });\n\n      if (has) {\n        ids.push(value.id);\n      }\n    });\n\n    return ids;\n  }\n\n  private getScrollIndex(input: string): number {\n    let indexes: number = 0;\n    this.entities.forEach((entity, index) => {\n      if (entity.id === input) {\n        indexes = index;\n      }\n    });\n\n    return indexes;\n  }\n\n  private scrollByText() {\n    const foundEntityIndex = this.getScrollIndex(this.foundIds[this.foundIndex]) || 0;\n    this.viewPort?.scrollToIndex(foundEntityIndex, 'smooth');\n    this.cdr.detectChanges();\n  }\n\n  private provideSettings(entity): Observable<{ entity: StructureEntityModel, ownSettings: EntitySettingsModel }> {\n    const settings$ = iif(() => !entity.settings,\n      this.entitySettingsService.getSettings(entity.path),\n      of(entity.settings)\n    );\n    const ownSettings$ = this.entitySettingsService.getSettings(entity.path, true);\n\n    return zip(settings$, ownSettings$)\n      .pipe(\n        map(([settings, ownSettings]) => {\n          entity.settings = settings;\n          return {entity, ownSettings};\n        })\n      );\n  }\n\n  private onSetStatus(dialogEntity, data: { confirmStatus: string }) {\n    this.dialogRef = this.dialog.open(StatusConfirmComponent, {\n      width: '400px',\n      data: {entity: dialogEntity, confirmStatus: data.confirmStatus},\n      disableClose: true\n    });\n\n    this.dialogRef.afterClosed()\n      .pipe(\n        filter((closeData) => typeof closeData.entity !== 'undefined'),\n        take(1)\n      )\n      .subscribe(({entity, confirmStatus}) => {\n        let statusChange$;\n        if (confirmStatus === 'maintenance' || (entity.status === 'maintenance' && confirmStatus === 'normal')) {\n          statusChange$ = this.service.setMaintenance(entity, confirmStatus);\n        } else if (confirmStatus === 'suspended' || (entity.status === 'suspended' && confirmStatus === 'normal')) {\n          statusChange$ = this.service.setStatus(entity, confirmStatus);\n        } else if (confirmStatus === 'test' || (entity.status === 'test' && confirmStatus === 'normal')) {\n          statusChange$ = this.service.setTest(entity, confirmStatus);\n        } else if ('blocked_by_admin' === confirmStatus\n          || ('blocked_by_admin' === entity.status && confirmStatus === 'normal')\n        ) {\n          statusChange$ = this.service.patchEntityStatus(entity, confirmStatus);\n        }\n\n        if (statusChange$) {\n          statusChange$\n            .pipe(\n              switchMap(() => this.bsService.updateEntityStructure(true)),\n              takeUntil(this.destroy$)\n            ).subscribe(() => {\n            this.expandFounded();\n            this.cdr.detectChanges();\n          });\n        }\n      });\n  }\n\n  private onEditRegional(dialogEntity, actionData) {\n    this.dialog.open(MatRegionalDialogComponent, {\n      width: '80vw',\n      data: {\n        ...dialogEntity,\n        activeTab: actionData,\n        entityParentPath: dialogEntity.parentId ? this.bsService.entitiesObject[dialogEntity.parentId].path : dialogEntity.path\n      },\n      disableClose: true\n    }).afterClosed().pipe(\n      switchMap(() => this.bsService.updateEntityStructure(true)),\n      take(1)).subscribe(() => {\n      this.expandFounded();\n      this.cdr.detectChanges();\n    });\n  }\n\n  private onCreateEditEntity(data: EditEntitySubmitData) {\n    const entity = new Entity(data.entity);\n    const updating: boolean = !!entity.key;\n    const settingsUpdate = {};\n    let applyEntitySource$: Observable<Entity>;\n\n    if (entity.type === Entity.TYPE_MERCHANT) {\n      applyEntitySource$ = updating\n        ? this.service.updateMerchantEntityItem(entity.asUpdateMerchantData(), entity.path)\n        : this.service.createMerchantEntityItem(entity.asCreateMerchantData(), entity.entityParent.path);\n    } else {\n      applyEntitySource$ = updating\n        ? this.service.updateEntityItem(entity)\n        : this.service.createEntityItem(entity, entity.entityParent.path);\n    }\n\n    applyEntitySource$ = applyEntitySource$.pipe(\n      this.addEntityPath(entity.path)\n    );\n\n    if (entity.type === Entity.TYPE_BRAND || entity.type === Entity.TYPE_MERCHANT) {\n      settingsUpdate['storePlayerInfo'] = data.entity.settings.storePlayerInfo;\n    }\n\n    if (entity.type === Entity.TYPE_BRAND || entity.type === Entity.TYPE_ENTITY) {\n      settingsUpdate['isPlayerPasswordChangeEnabled'] = data.entity.settings.isPlayerPasswordChangeEnabled;\n    }\n\n    if (entity.type === Entity.TYPE_BRAND) {\n      settingsUpdate['playerPrefix'] = data.entity.settings.playerPrefix;\n    }\n\n    if (settingsUpdate && Object.keys(settingsUpdate).length) {\n      applyEntitySource$ = applyEntitySource$.pipe(\n        switchMap((newEntity) => {\n          return this.entitySettingsService.patchSettings(settingsUpdate, newEntity.path)\n            .pipe(mapTo(newEntity));\n        }));\n    }\n\n    if (data.addGames) {\n      applyEntitySource$ = applyEntitySource$.pipe(\n        this.addGames(entity.entityParent)\n      );\n    }\n\n    applyEntitySource$.pipe(\n      tap(() => {\n        if (updating) {\n          this.notifications.success(this.translation.instant('BUSINESS_STRUCTURE.notificationEdited', {name: entity.name}));\n        }\n      }),\n      catchError((err) => {\n        this.service.handleErrors.call(this.service, err);\n        return throwError(err);\n      }),\n      tap((appliedEntity: Entity) => {\n        if (!updating) {\n          this.dialog.open(SetupHintDialogComponent, {\n            width: '700px',\n            data: {entity: appliedEntity},\n            disableClose: true\n          });\n        }\n      }),\n      switchMap(() => this.bsService.updateEntityStructure(true)),\n      finalize(() => {\n        this.entityLoading = false;\n        this.cdr.detectChanges();\n      }),\n      takeUntil(this.destroy$),\n    ).subscribe(() => {\n        if (updating) {\n          this.dialogRef.close();\n        }\n        this.expandFounded();\n      }\n    );\n  }\n\n  private addGames(entityParent: Entity): OperatorFunction<Entity, Entity> {\n    return mergeMap((newEntity: Entity) => {\n      return this.gameService.getAllGames(entityParent.path, true, true).pipe(\n        map(games => games.map(game => game.code)),\n        filter((games: string []) => !!games.length),\n        mergeMap(codes => this.gameService.setEntityGames(codes, newEntity.path).pipe(\n          mapTo(newEntity)\n        ))\n      );\n    });\n  }\n\n  private addEntityPath(builtPath: string): OperatorFunction<Entity, Entity> {\n    return map((newEntity: Entity) => {\n      if (typeof newEntity.path === 'undefined' && builtPath !== '') {\n        newEntity.path = builtPath;\n      }\n      return newEntity;\n    });\n  }\n\n  private setPanelActions() {\n    this.panelActions = [\n      <PanelAction>{\n        title: 'BUSINESS_STRUCTURE.btnExpand',\n        actionFn: () => this.expandAll(),\n        getStyle: () => {\n          return {color: '#1373d5'};\n        }\n      },\n      <PanelAction>{\n        title: 'BUSINESS_STRUCTURE.searchByDomain',\n        color: 'primary',\n        icon: 'search',\n        availableFn: () => this.isSuperAdmin(),\n        actionFn: () => this.searchByDomain(),\n        disabledFn: () => this.loading,\n      },\n      <PanelAction>{\n        title: '',\n        hover: 'Download CSV',\n        icon: 'get_app',\n        getStyle: () => {\n          return {\n            width: '20px',\n            padding: '0',\n            'min-width': '20px'\n          };\n        },\n        availableFn: () => this.isSuperAdmin(),\n        actionFn: () => this.downloadCSV(),\n        disabledFn: () => this.loading,\n      }\n    ];\n  }\n\n  private expandAll() {\n    this.bsService.entities.forEach(({id}) => {\n      this.bsService.expandedEntities.set(id, true);\n    });\n\n    this.expandFounded();\n  }\n\n  private downloadCSV() {\n    this.bsService.downloadCSV();\n  }\n\n  private searchByDomain() {\n    this.dialogRef = this.dialog.open(SearchByDomainModalComponent, {\n      width: '500px',\n      disableClose: true\n    });\n  }\n\n  private checkBriefForMaster() {\n    let briefData = this.route.snapshot.data['brief'];\n    if ('name' in briefData && briefData['name'] === Entity.MASTER_NAME) {\n      briefData['path'] = Entity.ROOT_PATH;\n    }\n  }\n\n  private entitySuspendedStatus() {\n    this.entities.map((entity: Entity) => {\n      const parent: Entity = entity.parentId ? this.bsService.entitiesObject[entity.parentId] : entity;\n      const isParentBlocked = parent.status === 'blocked_by_admin' || parent.status === 'suspended';\n      const isEntityBlocked = entity.status === 'blocked_by_admin' || entity.status === 'suspended';\n\n      if (isParentBlocked || parent.changeStatusState === 'blocked_by_parent') {\n        entity.changeStatusState = 'blocked_by_parent';\n      } else if (isEntityBlocked) {\n        entity.changeStatusState = 'blocked';\n      }\n    });\n  }\n}\n", "<lib-swui-page-panel\n  [title]=\"'MENU_SECTIONS.businessManagement_structure'\"\n  [actions]=\"panelActions\">\n</lib-swui-page-panel>\n\n<cdk-virtual-scroll-viewport\n  class=\"entities-wrapper\"\n  [minBufferPx]=\"3960\"\n  [maxBufferPx]=\"4008\"\n  [itemSize]=\"48\">\n  <div *ngIf=\"!loading\" class=\"p-32\">\n    <div class=\"bs-structure-wrapper\">\n\n      <table class=\"bs-table\">\n\n        <thead>\n        <tr>\n          <th style=\"text-align: left\">{{'BUSINESS_STRUCTURE.title' | translate}}</th>\n          <th style=\"text-align: left\">{{'BUSINESS_STRUCTURE.code' | translate}}</th>\n          <th style=\"text-align: left\">{{'BUSINESS_STRUCTURE.key' | translate}}</th>\n          <th style=\"text-align: center\">{{'BUSINESS_STRUCTURE.regional' | translate}}</th>\n          <th style=\"text-align: center\">{{'BUSINESS_STRUCTURE.status' | translate}}</th>\n          <th style=\"text-align: center\" *ngIf=\"!isBrand\">{{'BUSINESS_STRUCTURE.actions' | translate}}</th>\n        </tr>\n        </thead>\n\n        <tr\n          entity-item\n          class=\"bs-table-row\"\n          [class.bs-table-row--selected]=\"entity.id === foundIds[foundIndex]\"\n          *cdkVirtualFor=\"let entity of entities; let i = index; trackBy trackByFn\"\n          [entity]=\"entity\"\n          [disabledCurrencyTooltips]=\"disabledCurrencyTooltips\"\n          [canChangeTest]=\"canChangeTest\"\n          [expanded]=\"bsService.expandedEntities.get(entity.id)\"\n          (onClick)=\"handleExpandCollapse($event)\"\n        ></tr>\n\n      </table>\n\n    </div>\n  </div>\n\n</cdk-virtual-scroll-viewport>\n\n<div class=\"bs-loading\" *ngIf=\"loading || entityLoading\">\n  <mat-progress-spinner diameter=\"48\" mode=\"indeterminate\"></mat-progress-spinner>\n</div>\n\n<global-finder\n  *ngIf=\"this.viewPort?.getDataLength() > 0\"\n  [index]=\"foundIndex + 1\"\n  [length]=\"foundIds?.length\"\n  [state]=\"searchState\"\n  (prev)=\"onFind(-1)\"\n  (next)=\"onFind(1)\"\n  (valueChange)=\"onFindChange($event)\"\n  (stateChange)=\"onStateFindChange($event)\"\n  (close)=\"onFindClose()\"\n>\n  <ng-container *ngIf=\"!foundIds?.length\">Nothing is found</ng-container>\n</global-finder>\n"], "mappings": "AAAA,SAASA,wBAAwB,QAAQ,wBAAwB;AAajE,SAAsBC,iBAAiB,QAAoD,yBAAyB;AACpH,SAASC,aAAa,EAAEC,GAAG,EAAcC,EAAE,EAAoBC,OAAO,EAAEC,UAAU,EAAEC,KAAK,EAAEC,GAAG,QAAQ,MAAM;AAC5G,SAASC,UAAU,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,IAAI,EAAEC,SAAS,EAAEC,GAAG,QAAQ,gBAAgB;AAEpH,SAASC,MAAM,IAAIC,WAAW,EAAED,MAAM,QAAQ,wCAAwC;AAQtF,SAASE,SAAS,EAAEC,SAAS,EAA4BC,0BAA0B,QAAQ,8BAA8B;AACzH,SAA+BC,4BAA4B,QAAQ,mEAAmE;AACtI,SAASC,0BAA0B,QAAQ,6DAA6D;AACxG,SAASC,wBAAwB,QAAQ,yDAAyD;AAClG,SAASC,sBAAsB,QAAQ,mDAAmD;AAG1F,SAASC,4BAA4B,QAAQ,2DAA2D;AACxG,SAASC,oBAAoB,QAAQ,0BAA0B;;;;;;;;;;;;;;ICXrDC,EAAA,CAAAC,cAAA,YAAgD;IAAAD,EAAA,CAAAE,MAAA,GAA4C;;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;IAAjDH,EAAA,CAAAI,SAAA,EAA4C;IAA5CJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAM,WAAA,qCAA4C;;;;;;IAI9FN,EAAA,CAAAC,cAAA,aAUC;IADCD,EAAA,CAAAO,UAAA,qBAAAC,yEAAAC,MAAA;MAAAT,EAAA,CAAAU,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAZ,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAAWF,MAAA,CAAAG,oBAAA,CAAAN,MAAA,CAA4B;IAAA,EAAC;IACzCT,EAAA,CAAAG,YAAA,EAAK;;;;;IAPJH,EAAA,CAAAgB,WAAA,2BAAAC,SAAA,CAAAC,EAAA,KAAAN,MAAA,CAAAO,QAAA,CAAAP,MAAA,CAAAQ,UAAA,EAAmE;IAKnEpB,EAHA,CAAAqB,UAAA,WAAAJ,SAAA,CAAiB,6BAAAL,MAAA,CAAAU,wBAAA,CACoC,kBAAAV,MAAA,CAAAW,aAAA,CACtB,aAAAX,MAAA,CAAAY,SAAA,CAAAC,gBAAA,CAAAC,GAAA,CAAAT,SAAA,CAAAC,EAAA,EACuB;;;;;IAjBtDlB,EAPR,CAAAC,cAAA,aAAmC,aACC,eAER,YAEf,SACH,YAC2B;IAAAD,EAAA,CAAAE,MAAA,GAA0C;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5EH,EAAA,CAAAC,cAAA,YAA6B;IAAAD,EAAA,CAAAE,MAAA,GAAyC;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3EH,EAAA,CAAAC,cAAA,aAA6B;IAAAD,EAAA,CAAAE,MAAA,IAAwC;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1EH,EAAA,CAAAC,cAAA,aAA+B;IAAAD,EAAA,CAAAE,MAAA,IAA6C;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjFH,EAAA,CAAAC,cAAA,aAA+B;IAAAD,EAAA,CAAAE,MAAA,IAA2C;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/EH,EAAA,CAAA2B,UAAA,KAAAC,kDAAA,iBAAgD;IAElD5B,EADA,CAAAG,YAAA,EAAK,EACG;IAERH,EAAA,CAAA2B,UAAA,KAAAE,kDAAA,iBAUC;IAKP7B,EAHI,CAAAG,YAAA,EAAQ,EAEJ,EACF;;;;IAxB+BH,EAAA,CAAAI,SAAA,GAA0C;IAA1CJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAM,WAAA,mCAA0C;IAC1CN,EAAA,CAAAI,SAAA,GAAyC;IAAzCJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAM,WAAA,oCAAyC;IACzCN,EAAA,CAAAI,SAAA,GAAwC;IAAxCJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAM,WAAA,mCAAwC;IACtCN,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAM,WAAA,wCAA6C;IAC7CN,EAAA,CAAAI,SAAA,GAA2C;IAA3CJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAM,WAAA,sCAA2C;IAC1CN,EAAA,CAAAI,SAAA,GAAc;IAAdJ,EAAA,CAAAqB,UAAA,UAAAT,MAAA,CAAAkB,OAAA,CAAc;IAQnB9B,EAAA,CAAAI,SAAA,EAAa;IAAeJ,EAA5B,CAAAqB,UAAA,oBAAAT,MAAA,CAAAmB,QAAA,CAAa,yBAAAnB,MAAA,CAAAoB,SAAA,CAAgC;;;;;IAelFhC,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAiC,SAAA,+BAAgF;IAClFjC,EAAA,CAAAG,YAAA,EAAM;;;;;IAaJH,EAAA,CAAAkC,uBAAA,GAAwC;IAAAlC,EAAA,CAAAE,MAAA,uBAAgB;;;;;;;IAX1DF,EAAA,CAAAC,cAAA,wBAUC;IADCD,EAJA,CAAAO,UAAA,kBAAA4B,qFAAA;MAAAnC,EAAA,CAAAU,aAAA,CAAA0B,GAAA;MAAA,MAAAxB,MAAA,GAAAZ,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAAQF,MAAA,CAAAyB,MAAA,EAAQ,CAAC,CAAC;IAAA,EAAC,kBAAAC,qFAAA;MAAAtC,EAAA,CAAAU,aAAA,CAAA0B,GAAA;MAAA,MAAAxB,MAAA,GAAAZ,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CACXF,MAAA,CAAAyB,MAAA,CAAO,CAAC,CAAC;IAAA,EAAC,yBAAAE,4FAAA9B,MAAA;MAAAT,EAAA,CAAAU,aAAA,CAAA0B,GAAA;MAAA,MAAAxB,MAAA,GAAAZ,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CACHF,MAAA,CAAA4B,YAAA,CAAA/B,MAAA,CAAoB;IAAA,EAAC,yBAAAgC,4FAAAhC,MAAA;MAAAT,EAAA,CAAAU,aAAA,CAAA0B,GAAA;MAAA,MAAAxB,MAAA,GAAAZ,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CACrBF,MAAA,CAAA8B,iBAAA,CAAAjC,MAAA,CAAyB;IAAA,EAAC,mBAAAkC,sFAAA;MAAA3C,EAAA,CAAAU,aAAA,CAAA0B,GAAA;MAAA,MAAAxB,MAAA,GAAAZ,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAChCF,MAAA,CAAAgC,WAAA,EAAa;IAAA,EAAC;IAEvB5C,EAAA,CAAA2B,UAAA,IAAAkB,qEAAA,2BAAwC;IAC1C7C,EAAA,CAAAG,YAAA,EAAgB;;;;IARdH,EAFA,CAAAqB,UAAA,UAAAT,MAAA,CAAAQ,UAAA,KAAwB,WAAAR,MAAA,CAAAO,QAAA,kBAAAP,MAAA,CAAAO,QAAA,CAAA2B,MAAA,CACG,UAAAlC,MAAA,CAAAmC,WAAA,CACN;IAON/C,EAAA,CAAAI,SAAA,EAAuB;IAAvBJ,EAAA,CAAAqB,UAAA,WAAAT,MAAA,CAAAO,QAAA,kBAAAP,MAAA,CAAAO,QAAA,CAAA2B,MAAA,EAAuB;;;ADlBxC,OAAM,MAAOE,6BAA6B;EAwBxCC,YACSC,OAA4C,EAC5C1B,SAAmC,EAClC2B,qBAAiE,EACjEC,oBAA0C,EAC1CC,WAAwB,EACxBC,KAAqB,EACrBC,GAAsB,EACtBC,MAAiB,EACjBC,WAA6B,EAC7BC,WAA6B,EAC7BC,aAAuC,EACvCC,8BAAiE;IAXlE,KAAAV,OAAO,GAAPA,OAAO;IACP,KAAA1B,SAAS,GAATA,SAAS;IACR,KAAA2B,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,8BAA8B,GAA9BA,8BAA8B;IA9BxC,KAAAC,OAAO,GAAG,IAAI;IACd,KAAAC,YAAY,GAAkB,EAAE;IAEhC,KAAA1C,UAAU,GAAG,CAAC,CAAC;IACf,KAAAD,QAAQ,GAAa,EAAE;IAEvB,KAAA4C,QAAQ,GAAG,KAAK;IAGhB,KAAAhC,QAAQ,GAAa,EAAE;IACvB,KAAAiC,aAAa,GAAG,KAAK;IACrB,KAAA1C,wBAAwB,GAAG,KAAK;IAChC,KAAAC,aAAa,GAAG,KAAK;IAIb,KAAA0C,QAAQ,GAAG,IAAI1F,OAAO,EAAE;IAgB9B,MAAM;MAAC2F;IAAK,CAAC,GAAG,IAAI,CAACZ,KAAK,CAACa,QAAQ,CAACC,IAAI;IACxC,MAAMC,WAAW,GAAG,IAAI/E,WAAW,CAAC4E,KAAK,CAAC;IAC1C,IAAI,CAACpC,OAAO,GAAGuC,WAAW,CAACC,UAAU,EAAE,IAAID,WAAW,CAACE,UAAU;IACjE,IAAI,CAACxB,WAAW,GAAGa,8BAA8B,CAACb,WAAW;IAC7D,IAAI,CAACzB,wBAAwB,GAAG,CAACmC,WAAW,CAACe,UAAU,CAAC,CAACrG,iBAAiB,CAACsG,cAAc,CAAC,CAAC;IAC3F,IAAI,CAAClD,aAAa,GAAGkC,WAAW,CAACe,UAAU,CAAC,CAACrG,iBAAiB,CAACuG,uBAAuB,CAAC,CAAC;EAC1F;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,kBAAkB,EAAE;IAEzB,IAAI,CAAC,IAAI,CAAChD,OAAO,EAAE;MACjB,IAAI,CAACiD,eAAe,EAAE;IACxB;EACF;EAEAC,eAAeA,CAAA;IACb,IAAI,CAACjB,QAAQ,GAAG,IAAI;EACtB;EAEAkB,WAAWA,CAAA;IACT,IAAI,CAAChB,QAAQ,CAACiB,IAAI,EAAE;IACpB,IAAI,CAACjB,QAAQ,CAACkB,QAAQ,EAAE;EAC1B;EAEAnD,SAASA,CAACoD,CAAC,EAAEC,IAA0B;IACrC,OAAOA,IAAI,CAACnE,EAAE;EAChB;EAEAoE,YAAYA,CAAA;IACV,OAAO,IAAI,CAAC7B,WAAW,CAAC6B,YAAY;EACtC;EAEAR,kBAAkBA,CAAA;IAChB,IAAI,CAACtD,SAAS,CAAC+D,OAAO,CACnBC,IAAI,CACHrG,SAAS,CAAC,IAAI,CAAC8E,QAAQ,CAAC,CACzB,CAACwB,SAAS,CACVrB,IAAI,IAAI;MACP,IAAI;QAACsB,KAAK;QAAEC,IAAI;QAAEC,MAAM;QAAEC;MAAU,CAAC,GAAGzB,IAAI;MAC5C,IAAIuB,IAAI,KAAKpG,SAAS,CAACuG,WAAW,EAAE;QAClC,QAAQJ,KAAK;UAEX,KAAKlG,SAAS,CAACuG,WAAW;YACxB,IAAIH,MAAM,CAACD,IAAI,KAAKtG,MAAM,CAAC2G,aAAa,EAAE;cACxC,IAAI,CAACC,kBAAkB,CAACL,MAAM,CAAC;YACjC,CAAC,MAAM,IAAIA,MAAM,CAACD,IAAI,KAAKtG,MAAM,CAAC6G,UAAU,EAAE;cAC5C,IAAI,CAACC,eAAe,CAACP,MAAM,CAAC;YAC9B,CAAC,MAAM,IAAIA,MAAM,CAACD,IAAI,KAAKtG,MAAM,CAAC+G,WAAW,EAAE;cAC7C,IAAI,CAACC,kBAAkB,CAACT,MAAM,CAAC;YACjC,CAAC,MAAM;cACL,IAAI,CAACU,UAAU,CAACV,MAAM,CAAC;YACzB;YACA;UAEF,KAAKpG,SAAS,CAAC+G,UAAU;YACvB,IAAI,CAACC,SAAS,CAACZ,MAAM,CAAC;YACtB;UAEF,KAAKpG,SAAS,CAACiH,aAAa;YAC1B,IAAI,CAACC,cAAc,CAACd,MAAM,EAAEC,UAAU,CAAC;YACvC;UAEF,KAAKrG,SAAS,CAACmH,kBAAkB;YAC/B,IAAI,CAACC,WAAW,CAAChB,MAAM,EAAE;cAACiB,aAAa,EAAEhB,UAAU,CAAC,eAAe;YAAC,CAAC,CAAC;YACtE;UAEF;YACE;QACJ;MACF;IACF,CAAC,CACF;EACH;EAEAjB,oBAAoBA,CAAA;IAClB,IAAI,CAACkC,mBAAmB,EAAE;EAC5B;EAEAjC,oBAAoBA,CAAA;IAClB,IAAI,CAAC3B,OAAO,CAAC6D,iBAAiB,CAACtH,0BAA0B,CAACuH,iBAAiB,EAAE,IAAI,CAAC,CAC/ExB,IAAI,CACHrG,SAAS,CAAC,IAAI,CAAC8E,QAAQ,CAAC,CACzB,CACAwB,SAAS,CAAEwB,GAAG,IAAI;MACjB,IAAI,CAACzF,SAAS,CAAC0F,YAAY,CAACD,GAAG,CAAC;MAEhC,IAAIE,KAAK,CAACC,OAAO,CAAC,IAAI,CAAC5F,SAAS,CAACO,QAAQ,CAAC,IAAI,IAAI,CAACP,SAAS,CAACO,QAAQ,CAACe,MAAM,EAAE;QAC5E,IAAI,CAAC/B,oBAAoB,CAAC,IAAI,CAACS,SAAS,CAACO,QAAQ,CAAC,CAAC,CAAC,CAACb,EAAE,EAAE,IAAI,CAAC;MAChE;MAEA,IAAI,CAAC2C,OAAO,GAAG,KAAK;MACpB,IAAI,CAACN,GAAG,CAAC8D,YAAY,EAAE;IACzB,CAAC,CAAC;EACN;EAEAf,UAAUA,CAACV,MAA4B,EAAE0B,WAAiC;IACxE,IAAI,CAACC,SAAS,GAAG,IAAI,CAAC/D,MAAM,CAACgE,IAAI,CAAC9H,4BAA4B,EAAE;MAC9D+H,KAAK,EAAE,OAAO;MACdrD,IAAI,EAAE;QAACwB,MAAM;QAAE8B,mBAAmB,EAAE,IAAI,CAACA,mBAAmB;QAAEJ;MAAW,CAAC;MAC1EK,YAAY,EAAE;KACf,CAAC;IAEF,IAAI,CAACJ,SAAS,CAACK,WAAW,EAAE,CACzBpC,IAAI,CACH5G,MAAM,CAACiJ,GAAG,IAAI,CAAC,CAACA,GAAG,CAAC,EACpBzI,GAAG,CAAC,MAAK;MACP,IAAI,CAAC4E,aAAa,GAAG,IAAI;MACzB,IAAI,CAACT,GAAG,CAACuE,aAAa,EAAE;IAC1B,CAAC,CAAC,EACF3I,SAAS,CAAC,IAAI,CAAC8E,QAAQ,CAAC,CACzB,CAACwB,SAAS,CAACrB,IAAI,IAAG;MACnB,IAAI,CAAC2D,kBAAkB,CAAC3D,IAAI,CAAC;IAC/B,CAAC,CAAC;EACJ;EAEA6B,kBAAkBA,CAACL,MAA4B;IAC7CxH,aAAa,CAAC,CACZ,IAAI,CAAC8E,OAAO,CAAC8E,uBAAuB,CAACpC,MAAM,CAACqC,IAAI,CAAC,EACjD,IAAI,CAAC9E,qBAAqB,CAAC+E,WAAW,CAACtC,MAAM,CAACqC,IAAI,CAAC,CACpD,CAAC,CACCzC,IAAI,CACH1G,GAAG,CAAC,CAAC,CAACqJ,cAAc,EAAEC,QAAQ,CAAC,MAAM;MAAC,GAAGD,cAAc;MAAEC;IAAQ,CAAC,CAAC,CAAC,EACpEpJ,QAAQ,CAACmJ,cAAc,IAAI,IAAI,CAAC/E,oBAAoB,CACjD1B,GAAG,CAACyG,cAAc,CAACE,QAAQ,CAAC1C,IAAI,EAAEwC,cAAc,CAACF,IAAI,CAAC,CAACzC,IAAI,CAC1DpG,GAAG,CAAEkJ,kBAAkB,IAAI;MACzB,IAAI,CAACZ,mBAAmB,GAAGY,kBAAkB,GAAG,CAACA,kBAAkB,CAAC,GAAG,EAAE;IAC3E,CAAC,CAAC,EACFxJ,GAAG,CAAC,MAAMqJ,cAAc,CAAC,CAC1B,CAAC,EACJhJ,SAAS,CAAC,IAAI,CAAC8E,QAAQ,CAAC,CACzB,CAACwB,SAAS,CAAC0C,cAAc,IAAG;MAC3BvC,MAAM,CAAC2C,MAAM,CAAC;QAACF,QAAQ,EAAEF,cAAc,CAACE,QAAQ;QAAED,QAAQ,EAAED,cAAc,CAACC;MAAQ,CAAC,CAAC;MAErF,IAAI,CAAC9B,UAAU,CAACV,MAAM,CAAC;IACzB,CAAC,CACF;EACH;EAEAO,eAAeA,CAACqC,GAAyB;IACvC,IAAI,CAACC,eAAe,CAACD,GAAG,CAAC,CACtBhD,IAAI,CACHrG,SAAS,CAAC,IAAI,CAAC8E,QAAQ,CAAC,CACzB,CAACwB,SAAS,CAAC,CAAC;MAACG,MAAM;MAAE0B;IAAW,CAAC,KAAI;MACtC,IAAI,CAAChB,UAAU,CAACV,MAAM,EAAE0B,WAAW,CAAC;IACtC,CAAC,CAAC;EACJ;EAEAjB,kBAAkBA,CAACmC,GAAyB;IAC1C,IAAI,CAACC,eAAe,CAACD,GAAG,CAAC,CAAChD,IAAI,CAACrG,SAAS,CAAC,IAAI,CAAC8E,QAAQ,CAAC,CAAC,CACrDwB,SAAS,CAAC,CAAC;MAACG,MAAM;MAAE0B;IAAW,CAAC,KAAI;MACnC,IAAI,CAAChB,UAAU,CAACV,MAAM,EAAE0B,WAAW,CAAC;IACtC,CAAC,CAAC;EACN;EAEAd,SAASA,CAACZ,MAA4B;IACpC,IAAI,CAACxC,oBAAoB,CAACsF,KAAK,CAAC9C,MAAM,CAACqC,IAAI,CAAC,CACzCzC,IAAI,CAACtG,IAAI,CAAC,CAAC,CAAC,CAAC,CACbuG,SAAS,CAAErB,IAAI,IAAI;MAClB,IAAI,CAACsD,mBAAmB,GAAGtD,IAAI;MAC/B,MAAMuE,SAAS,GAAG,IAAI5I,oBAAoB,CAAC;QACzC6I,MAAM,EAAEhD,MAAM,CAACqC,IAAI;QACnBY,QAAQ,EAAEjD,MAAM,CAAC1E,EAAE;QACnB4H,eAAe,EAAEA,CAAA,KAAMlD;OACxB,CAAC;MACF,IAAI,CAACU,UAAU,CAACqC,SAAS,CAAC;IAC5B,CAAC,CAAC;EACN;EAEAnG,YAAYA,CAAC;IAACuG,KAAK;IAAEC;EAAO,CAAsC,EACrDC,cAAuB,EAAEC,QAAiB,EAAEC,YAAsB;IAC7E,IAAI,CAACJ,KAAK,EAAE;MACV,IAAI,CAAC3H,UAAU,GAAG,CAAC,CAAC;MACpB,IAAI,CAACD,QAAQ,GAAG,EAAE;MAClB;IACF;IAEA,IAAI,CAACA,QAAQ,GAAG,IAAI,CAACiI,kBAAkB,CAACL,KAAK,EAAEC,OAAO,CAAC;IACvD,IAAI,CAAC5H,UAAU,GAAG,CAAC,CAAC;IACpB,IAAI,CAACwC,8BAA8B,CAACyF,OAAO,CAACN,KAAK,CAAC;IAElD,IAAI,IAAI,CAAC5H,QAAQ,CAAC2B,MAAM,KAAK,CAAC,EAAE;MAC9B,IAAI,CAACT,MAAM,CAAE4G,cAAc,GAAG,CAAC,GAAG,CAAC,EAAGA,cAAc,EAAEC,QAAQ,EAAEC,YAAY,CAAC;IAC/E;EACF;EAEA9G,MAAMA,CAACiH,IAAY,EAAEL,cAAuB,EAAEC,QAAiB,EAAEC,YAAA,GAAwB,IAAI;IAC3F,IAAI,CAAC/H,UAAU,GAAG,OAAO8H,QAAQ,KAAK,QAAQ,GAAGA,QAAQ,GAAG,IAAI,CAAC/H,QAAQ,CAAC2B,MAAM,GAC5E,CAAC,IAAI,CAAC3B,QAAQ,CAAC2B,MAAM,GAAG,IAAI,CAAC1B,UAAU,GAAGkI,IAAI,IAAI,IAAI,CAACnI,QAAQ,CAAC2B,MAAM,GACtE,CAAC,CAAC;IAEN,IAAI,CAACyG,YAAY,CAACN,cAAc,IAAI,IAAI,CAAC9H,QAAQ,CAAC,IAAI,CAACC,UAAU,CAAC,CAAC;IAEnE,IAAI,CAACwC,8BAA8B,CAAC4F,WAAW,CAAC,IAAI,CAACpI,UAAU,CAAC;IAChE,IAAI,CAACwC,8BAA8B,CAAC6F,kBAAkB,CAACR,cAAc,IAAI,IAAI,CAAC9H,QAAQ,CAAC,IAAI,CAACC,UAAU,CAAC,CAAC;IAExG,IAAI+H,YAAY,EAAE;MAChB,IAAI,CAACA,YAAY,EAAE;IACrB;EACF;EAEAzG,iBAAiBA,CAACgH,KAAwB;IACxCjL,KAAK,CAAC,CAAC,CAAC,CACL+G,IAAI,CAACtG,IAAI,CAAC,CAAC,CAAC,CAAC,CACbuG,SAAS,CAAC,MAAK;MACd,IAAI,CAACjD,YAAY,CAAC;QAACuG,KAAK,EAAEW,KAAK,CAACC,IAAI;QAAEX,OAAO,EAAEU,KAAK,CAACV;MAAO,CAAC,EAAEU,KAAK,CAACxI,EAAE,EAAEwI,KAAK,CAACR,QAAQ,CAAC;IAC1F,CAAC,CAAC;EACN;EAEAtG,WAAWA,CAAA;IACT,IAAI,CAACxB,UAAU,GAAG,CAAC,CAAC;IACpB,IAAI,CAACD,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACyC,8BAA8B,CAACgG,UAAU,EAAE;EAClD;EAEAC,aAAaA,CAACC,OAAe;IAC3B,IAAI,CAACtI,SAAS,CAACC,gBAAgB,CAACsI,GAAG,CAACD,OAAO,EAAE,KAAK,CAAC;IAEnD,MAAM;MAACE;IAAQ,CAAC,GAAG,IAAI,CAACxI,SAAS,CAACyI,cAAc,CAACH,OAAO,CAAC;IAEzDE,QAAQ,CAACE,OAAO,CAAEhJ,EAAU,IAAI;MAC9B,IAAI,CAAC2I,aAAa,CAAC3I,EAAE,CAAC;IACxB,CAAC,CAAC;EACJ;EAEAH,oBAAoBA,CAACG,EAAU,EAAEiJ,KAAe;IAC9C,MAAMC,UAAU,GAAG,CAAC,IAAI,CAAC5I,SAAS,CAACC,gBAAgB,CAACC,GAAG,CAACR,EAAE,CAAC,IAAIiJ,KAAK;IAEpE,IAAIC,UAAU,EAAE;MACd,IAAI,CAAC5I,SAAS,CAACC,gBAAgB,CAACsI,GAAG,CAAC7I,EAAE,EAAE,IAAI,CAAC;IAC/C,CAAC,MAAM;MACL,IAAI,CAAC2I,aAAa,CAAC3I,EAAE,CAAC;IACxB;IAEA,IAAI,CAACmJ,aAAa,EAAE;EACtB;EAEQA,aAAaA,CAAA;IACnB,IAAI,CAACtI,QAAQ,GAAG,IAAI,CAACP,SAAS,CAACO,QAAQ,CAACnD,MAAM,CAAC,CAACyG,IAAS,EAAEiF,KAAa,KAAI;MAC1E,OAAO,IAAI,CAAC9I,SAAS,CAACC,gBAAgB,CAACC,GAAG,CAAC2D,IAAI,CAACnE,EAAE,CAAC,IAAI,IAAI,CAACM,SAAS,CAACC,gBAAgB,CAACC,GAAG,CAAC2D,IAAI,CAACwD,QAAQ,CAAC,IAAI,CAACyB,KAAK;IACrH,CAAC,CAAC;IAEF,IAAI,CAACC,qBAAqB,EAAE;EAC9B;EAEQhB,YAAYA,CAACrI,EAAU;IAC7B,IAAIA,EAAE,EAAE;MACN,IAAI,CAACM,SAAS,CAACC,gBAAgB,CAACsI,GAAG,CAAC7I,EAAE,EAAE,IAAI,CAAC;MAC7C,OAAO,IAAI,CAACqI,YAAY,CAAC,IAAI,CAAC/H,SAAS,CAACyI,cAAc,CAAC/I,EAAE,CAAC,CAAC2H,QAAQ,CAAC;IACtE;IACA,IAAI,CAACwB,aAAa,EAAE;EACtB;EAEQjB,kBAAkBA,CAACoB,KAAa,EAAEC,MAAe;IACvD,IAAIA,MAAM,EAAE;MACV,MAAMvJ,EAAE,GAAG,IAAI,CAACM,SAAS,CAACO,QAAQ,CAAC2I,IAAI,CAAElC,GAAG,IAAKA,GAAG,CAACmC,cAAc,CAACC,QAAQ,EAAE,KAAKJ,KAAK,CAAC,EAAEtJ,EAAE;MAC7F,OAAOA,EAAE,GAAG,CAACA,EAAE,CAAC,GAAG,EAAE;IACvB;IAEA,MAAM2J,MAAM,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,gBAAgB,EAAE,IAAI,CAAC;IACvE,MAAMC,GAAG,GAAG,EAAE;IAEd,IAAI,CAACtJ,SAAS,CAACO,QAAQ,CAACmI,OAAO,CAAEnB,KAAa,IAAI;MAChD,MAAMgC,GAAG,GAAGF,MAAM,CAACG,IAAI,CAACC,KAAK,IAAG;QAC9B,OAAOlC,KAAK,CAACkC,KAAK,CAAC,IAAIlC,KAAK,CAACkC,KAAK,CAAC,CAACL,QAAQ,EAAE,CAACM,WAAW,EAAE,CAACC,QAAQ,CAACX,KAAK,CAACU,WAAW,EAAE,CAAC;MAC5F,CAAC,CAAC;MAEF,IAAIH,GAAG,EAAE;QACPD,GAAG,CAACM,IAAI,CAACrC,KAAK,CAAC7H,EAAE,CAAC;MACpB;IACF,CAAC,CAAC;IAEF,OAAO4J,GAAG;EACZ;EAEQO,cAAcA,CAACb,KAAa;IAClC,IAAIc,OAAO,GAAW,CAAC;IACvB,IAAI,CAACvJ,QAAQ,CAACmI,OAAO,CAAC,CAACtE,MAAM,EAAE0E,KAAK,KAAI;MACtC,IAAI1E,MAAM,CAAC1E,EAAE,KAAKsJ,KAAK,EAAE;QACvBc,OAAO,GAAGhB,KAAK;MACjB;IACF,CAAC,CAAC;IAEF,OAAOgB,OAAO;EAChB;EAEQnC,YAAYA,CAAA;IAClB,MAAMoC,gBAAgB,GAAG,IAAI,CAACF,cAAc,CAAC,IAAI,CAAClK,QAAQ,CAAC,IAAI,CAACC,UAAU,CAAC,CAAC,IAAI,CAAC;IACjF,IAAI,CAACoK,QAAQ,EAAEC,aAAa,CAACF,gBAAgB,EAAE,QAAQ,CAAC;IACxD,IAAI,CAAChI,GAAG,CAACuE,aAAa,EAAE;EAC1B;EAEQW,eAAeA,CAAC7C,MAAM;IAC5B,MAAM8F,SAAS,GAAGrN,GAAG,CAAC,MAAM,CAACuH,MAAM,CAACwC,QAAQ,EAC1C,IAAI,CAACjF,qBAAqB,CAAC+E,WAAW,CAACtC,MAAM,CAACqC,IAAI,CAAC,EACnD3J,EAAE,CAACsH,MAAM,CAACwC,QAAQ,CAAC,CACpB;IACD,MAAMuD,YAAY,GAAG,IAAI,CAACxI,qBAAqB,CAAC+E,WAAW,CAACtC,MAAM,CAACqC,IAAI,EAAE,IAAI,CAAC;IAE9E,OAAOvJ,GAAG,CAACgN,SAAS,EAAEC,YAAY,CAAC,CAChCnG,IAAI,CACH1G,GAAG,CAAC,CAAC,CAACsJ,QAAQ,EAAEd,WAAW,CAAC,KAAI;MAC9B1B,MAAM,CAACwC,QAAQ,GAAGA,QAAQ;MAC1B,OAAO;QAACxC,MAAM;QAAE0B;MAAW,CAAC;IAC9B,CAAC,CAAC,CACH;EACL;EAEQV,WAAWA,CAACgF,YAAY,EAAExH,IAA+B;IAC/D,IAAI,CAACmD,SAAS,GAAG,IAAI,CAAC/D,MAAM,CAACgE,IAAI,CAAC3H,sBAAsB,EAAE;MACxD4H,KAAK,EAAE,OAAO;MACdrD,IAAI,EAAE;QAACwB,MAAM,EAAEgG,YAAY;QAAE/E,aAAa,EAAEzC,IAAI,CAACyC;MAAa,CAAC;MAC/Dc,YAAY,EAAE;KACf,CAAC;IAEF,IAAI,CAACJ,SAAS,CAACK,WAAW,EAAE,CACzBpC,IAAI,CACH5G,MAAM,CAAEiN,SAAS,IAAK,OAAOA,SAAS,CAACjG,MAAM,KAAK,WAAW,CAAC,EAC9D1G,IAAI,CAAC,CAAC,CAAC,CACR,CACAuG,SAAS,CAAC,CAAC;MAACG,MAAM;MAAEiB;IAAa,CAAC,KAAI;MACrC,IAAIiF,aAAa;MACjB,IAAIjF,aAAa,KAAK,aAAa,IAAKjB,MAAM,CAACmG,MAAM,KAAK,aAAa,IAAIlF,aAAa,KAAK,QAAS,EAAE;QACtGiF,aAAa,GAAG,IAAI,CAAC5I,OAAO,CAAC8I,cAAc,CAACpG,MAAM,EAAEiB,aAAa,CAAC;MACpE,CAAC,MAAM,IAAIA,aAAa,KAAK,WAAW,IAAKjB,MAAM,CAACmG,MAAM,KAAK,WAAW,IAAIlF,aAAa,KAAK,QAAS,EAAE;QACzGiF,aAAa,GAAG,IAAI,CAAC5I,OAAO,CAAC+I,SAAS,CAACrG,MAAM,EAAEiB,aAAa,CAAC;MAC/D,CAAC,MAAM,IAAIA,aAAa,KAAK,MAAM,IAAKjB,MAAM,CAACmG,MAAM,KAAK,MAAM,IAAIlF,aAAa,KAAK,QAAS,EAAE;QAC/FiF,aAAa,GAAG,IAAI,CAAC5I,OAAO,CAACgJ,OAAO,CAACtG,MAAM,EAAEiB,aAAa,CAAC;MAC7D,CAAC,MAAM,IAAI,kBAAkB,KAAKA,aAAa,IACzC,kBAAkB,KAAKjB,MAAM,CAACmG,MAAM,IAAIlF,aAAa,KAAK,QAAS,EACvE;QACAiF,aAAa,GAAG,IAAI,CAAC5I,OAAO,CAACiJ,iBAAiB,CAACvG,MAAM,EAAEiB,aAAa,CAAC;MACvE;MAEA,IAAIiF,aAAa,EAAE;QACjBA,aAAa,CACVtG,IAAI,CACHvG,SAAS,CAAC,MAAM,IAAI,CAACuC,SAAS,CAAC4K,qBAAqB,CAAC,IAAI,CAAC,CAAC,EAC3DjN,SAAS,CAAC,IAAI,CAAC8E,QAAQ,CAAC,CACzB,CAACwB,SAAS,CAAC,MAAK;UACjB,IAAI,CAAC4E,aAAa,EAAE;UACpB,IAAI,CAAC9G,GAAG,CAACuE,aAAa,EAAE;QAC1B,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACN;EAEQpB,cAAcA,CAACkF,YAAY,EAAE/F,UAAU;IAC7C,IAAI,CAACrC,MAAM,CAACgE,IAAI,CAAC7H,0BAA0B,EAAE;MAC3C8H,KAAK,EAAE,MAAM;MACbrD,IAAI,EAAE;QACJ,GAAGwH,YAAY;QACfS,SAAS,EAAExG,UAAU;QACrByG,gBAAgB,EAAEV,YAAY,CAAC/C,QAAQ,GAAG,IAAI,CAACrH,SAAS,CAACyI,cAAc,CAAC2B,YAAY,CAAC/C,QAAQ,CAAC,CAACZ,IAAI,GAAG2D,YAAY,CAAC3D;OACpH;MACDN,YAAY,EAAE;KACf,CAAC,CAACC,WAAW,EAAE,CAACpC,IAAI,CACnBvG,SAAS,CAAC,MAAM,IAAI,CAACuC,SAAS,CAAC4K,qBAAqB,CAAC,IAAI,CAAC,CAAC,EAC3DlN,IAAI,CAAC,CAAC,CAAC,CAAC,CAACuG,SAAS,CAAC,MAAK;MACxB,IAAI,CAAC4E,aAAa,EAAE;MACpB,IAAI,CAAC9G,GAAG,CAACuE,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEQC,kBAAkBA,CAAC3D,IAA0B;IACnD,MAAMwB,MAAM,GAAG,IAAIvG,MAAM,CAAC+E,IAAI,CAACwB,MAAM,CAAC;IACtC,MAAM2G,QAAQ,GAAY,CAAC,CAAC3G,MAAM,CAAC4G,GAAG;IACtC,MAAMC,cAAc,GAAG,EAAE;IACzB,IAAIC,kBAAsC;IAE1C,IAAI9G,MAAM,CAACD,IAAI,KAAKtG,MAAM,CAAC2G,aAAa,EAAE;MACxC0G,kBAAkB,GAAGH,QAAQ,GACzB,IAAI,CAACrJ,OAAO,CAACyJ,wBAAwB,CAAC/G,MAAM,CAACgH,oBAAoB,EAAE,EAAEhH,MAAM,CAACqC,IAAI,CAAC,GACjF,IAAI,CAAC/E,OAAO,CAAC2J,wBAAwB,CAACjH,MAAM,CAACkH,oBAAoB,EAAE,EAAElH,MAAM,CAACmH,YAAY,CAAC9E,IAAI,CAAC;IACpG,CAAC,MAAM;MACLyE,kBAAkB,GAAGH,QAAQ,GACzB,IAAI,CAACrJ,OAAO,CAAC8J,gBAAgB,CAACpH,MAAM,CAAC,GACrC,IAAI,CAAC1C,OAAO,CAAC+J,gBAAgB,CAACrH,MAAM,EAAEA,MAAM,CAACmH,YAAY,CAAC9E,IAAI,CAAC;IACrE;IAEAyE,kBAAkB,GAAGA,kBAAkB,CAAClH,IAAI,CAC1C,IAAI,CAAC0H,aAAa,CAACtH,MAAM,CAACqC,IAAI,CAAC,CAChC;IAED,IAAIrC,MAAM,CAACD,IAAI,KAAKtG,MAAM,CAAC6G,UAAU,IAAIN,MAAM,CAACD,IAAI,KAAKtG,MAAM,CAAC2G,aAAa,EAAE;MAC7EyG,cAAc,CAAC,iBAAiB,CAAC,GAAGrI,IAAI,CAACwB,MAAM,CAACwC,QAAQ,CAAC+E,eAAe;IAC1E;IAEA,IAAIvH,MAAM,CAACD,IAAI,KAAKtG,MAAM,CAAC6G,UAAU,IAAIN,MAAM,CAACD,IAAI,KAAKtG,MAAM,CAAC+G,WAAW,EAAE;MAC3EqG,cAAc,CAAC,+BAA+B,CAAC,GAAGrI,IAAI,CAACwB,MAAM,CAACwC,QAAQ,CAACgF,6BAA6B;IACtG;IAEA,IAAIxH,MAAM,CAACD,IAAI,KAAKtG,MAAM,CAAC6G,UAAU,EAAE;MACrCuG,cAAc,CAAC,cAAc,CAAC,GAAGrI,IAAI,CAACwB,MAAM,CAACwC,QAAQ,CAACiF,YAAY;IACpE;IAEA,IAAIZ,cAAc,IAAIa,MAAM,CAACC,IAAI,CAACd,cAAc,CAAC,CAAC3J,MAAM,EAAE;MACxD4J,kBAAkB,GAAGA,kBAAkB,CAAClH,IAAI,CAC1CvG,SAAS,CAAE0J,SAAS,IAAI;QACtB,OAAO,IAAI,CAACxF,qBAAqB,CAACqK,aAAa,CAACf,cAAc,EAAE9D,SAAS,CAACV,IAAI,CAAC,CAC5EzC,IAAI,CAACzG,KAAK,CAAC4J,SAAS,CAAC,CAAC;MAC3B,CAAC,CAAC,CAAC;IACP;IAEA,IAAIvE,IAAI,CAACqJ,QAAQ,EAAE;MACjBf,kBAAkB,GAAGA,kBAAkB,CAAClH,IAAI,CAC1C,IAAI,CAACiI,QAAQ,CAAC7H,MAAM,CAACmH,YAAY,CAAC,CACnC;IACH;IAEAL,kBAAkB,CAAClH,IAAI,CACrBpG,GAAG,CAAC,MAAK;MACP,IAAImN,QAAQ,EAAE;QACZ,IAAI,CAAC5I,aAAa,CAAC+J,OAAO,CAAC,IAAI,CAAChK,WAAW,CAACiK,OAAO,CAAC,uCAAuC,EAAE;UAACC,IAAI,EAAEhI,MAAM,CAACgI;QAAI,CAAC,CAAC,CAAC;MACpH;IACF,CAAC,CAAC,EACFjP,UAAU,CAAEkP,GAAG,IAAI;MACjB,IAAI,CAAC3K,OAAO,CAAC4K,YAAY,CAACC,IAAI,CAAC,IAAI,CAAC7K,OAAO,EAAE2K,GAAG,CAAC;MACjD,OAAOrP,UAAU,CAACqP,GAAG,CAAC;IACxB,CAAC,CAAC,EACFzO,GAAG,CAAE4O,aAAqB,IAAI;MAC5B,IAAI,CAACzB,QAAQ,EAAE;QACb,IAAI,CAAC/I,MAAM,CAACgE,IAAI,CAAC5H,wBAAwB,EAAE;UACzC6H,KAAK,EAAE,OAAO;UACdrD,IAAI,EAAE;YAACwB,MAAM,EAAEoI;UAAa,CAAC;UAC7BrG,YAAY,EAAE;SACf,CAAC;MACJ;IACF,CAAC,CAAC,EACF1I,SAAS,CAAC,MAAM,IAAI,CAACuC,SAAS,CAAC4K,qBAAqB,CAAC,IAAI,CAAC,CAAC,EAC3DvN,QAAQ,CAAC,MAAK;MACZ,IAAI,CAACmF,aAAa,GAAG,KAAK;MAC1B,IAAI,CAACT,GAAG,CAACuE,aAAa,EAAE;IAC1B,CAAC,CAAC,EACF3I,SAAS,CAAC,IAAI,CAAC8E,QAAQ,CAAC,CACzB,CAACwB,SAAS,CAAC,MAAK;MACb,IAAI8G,QAAQ,EAAE;QACZ,IAAI,CAAChF,SAAS,CAAC0G,KAAK,EAAE;MACxB;MACA,IAAI,CAAC5D,aAAa,EAAE;IACtB,CAAC,CACF;EACH;EAEQoD,QAAQA,CAACV,YAAoB;IACnC,OAAO/N,QAAQ,CAAE2J,SAAiB,IAAI;MACpC,OAAO,IAAI,CAACtF,WAAW,CAAC6K,WAAW,CAACnB,YAAY,CAAC9E,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAACzC,IAAI,CACrE1G,GAAG,CAACqP,KAAK,IAAIA,KAAK,CAACrP,GAAG,CAACsP,IAAI,IAAIA,IAAI,CAACC,IAAI,CAAC,CAAC,EAC1CzP,MAAM,CAAEuP,KAAgB,IAAK,CAAC,CAACA,KAAK,CAACrL,MAAM,CAAC,EAC5C9D,QAAQ,CAACsP,KAAK,IAAI,IAAI,CAACjL,WAAW,CAACkL,cAAc,CAACD,KAAK,EAAE3F,SAAS,CAACV,IAAI,CAAC,CAACzC,IAAI,CAC3EzG,KAAK,CAAC4J,SAAS,CAAC,CACjB,CAAC,CACH;IACH,CAAC,CAAC;EACJ;EAEQuE,aAAaA,CAACsB,SAAiB;IACrC,OAAO1P,GAAG,CAAE6J,SAAiB,IAAI;MAC/B,IAAI,OAAOA,SAAS,CAACV,IAAI,KAAK,WAAW,IAAIuG,SAAS,KAAK,EAAE,EAAE;QAC7D7F,SAAS,CAACV,IAAI,GAAGuG,SAAS;MAC5B;MACA,OAAO7F,SAAS;IAClB,CAAC,CAAC;EACJ;EAEQ5D,eAAeA,CAAA;IACrB,IAAI,CAACjB,YAAY,GAAG,CACL;MACX2K,KAAK,EAAE,8BAA8B;MACrCC,QAAQ,EAAEA,CAAA,KAAM,IAAI,CAACC,SAAS,EAAE;MAChCC,QAAQ,EAAEA,CAAA,KAAK;QACb,OAAO;UAACC,KAAK,EAAE;QAAS,CAAC;MAC3B;KACD,EACY;MACXJ,KAAK,EAAE,mCAAmC;MAC1CI,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE,QAAQ;MACdC,WAAW,EAAEA,CAAA,KAAM,IAAI,CAACzJ,YAAY,EAAE;MACtCoJ,QAAQ,EAAEA,CAAA,KAAM,IAAI,CAACM,cAAc,EAAE;MACrCC,UAAU,EAAEA,CAAA,KAAM,IAAI,CAACpL;KACxB,EACY;MACX4K,KAAK,EAAE,EAAE;MACTS,KAAK,EAAE,cAAc;MACrBJ,IAAI,EAAE,SAAS;MACfF,QAAQ,EAAEA,CAAA,KAAK;QACb,OAAO;UACLnH,KAAK,EAAE,MAAM;UACb0H,OAAO,EAAE,GAAG;UACZ,WAAW,EAAE;SACd;MACH,CAAC;MACDJ,WAAW,EAAEA,CAAA,KAAM,IAAI,CAACzJ,YAAY,EAAE;MACtCoJ,QAAQ,EAAEA,CAAA,KAAM,IAAI,CAACU,WAAW,EAAE;MAClCH,UAAU,EAAEA,CAAA,KAAM,IAAI,CAACpL;KACxB,CACF;EACH;EAEQ8K,SAASA,CAAA;IACf,IAAI,CAACnN,SAAS,CAACO,QAAQ,CAACmI,OAAO,CAAC,CAAC;MAAChJ;IAAE,CAAC,KAAI;MACvC,IAAI,CAACM,SAAS,CAACC,gBAAgB,CAACsI,GAAG,CAAC7I,EAAE,EAAE,IAAI,CAAC;IAC/C,CAAC,CAAC;IAEF,IAAI,CAACmJ,aAAa,EAAE;EACtB;EAEQ+E,WAAWA,CAAA;IACjB,IAAI,CAAC5N,SAAS,CAAC4N,WAAW,EAAE;EAC9B;EAEQJ,cAAcA,CAAA;IACpB,IAAI,CAACzH,SAAS,GAAG,IAAI,CAAC/D,MAAM,CAACgE,IAAI,CAAC1H,4BAA4B,EAAE;MAC9D2H,KAAK,EAAE,OAAO;MACdE,YAAY,EAAE;KACf,CAAC;EACJ;EAEQb,mBAAmBA,CAAA;IACzB,IAAIuI,SAAS,GAAG,IAAI,CAAC/L,KAAK,CAACa,QAAQ,CAACC,IAAI,CAAC,OAAO,CAAC;IACjD,IAAI,MAAM,IAAIiL,SAAS,IAAIA,SAAS,CAAC,MAAM,CAAC,KAAKhQ,MAAM,CAACiQ,WAAW,EAAE;MACnED,SAAS,CAAC,MAAM,CAAC,GAAGhQ,MAAM,CAACkQ,SAAS;IACtC;EACF;EAEQhF,qBAAqBA,CAAA;IAC3B,IAAI,CAACxI,QAAQ,CAACjD,GAAG,CAAE8G,MAAc,IAAI;MACnC,MAAMgD,MAAM,GAAWhD,MAAM,CAACiD,QAAQ,GAAG,IAAI,CAACrH,SAAS,CAACyI,cAAc,CAACrE,MAAM,CAACiD,QAAQ,CAAC,GAAGjD,MAAM;MAChG,MAAM4J,eAAe,GAAG5G,MAAM,CAACmD,MAAM,KAAK,kBAAkB,IAAInD,MAAM,CAACmD,MAAM,KAAK,WAAW;MAC7F,MAAM0D,eAAe,GAAG7J,MAAM,CAACmG,MAAM,KAAK,kBAAkB,IAAInG,MAAM,CAACmG,MAAM,KAAK,WAAW;MAE7F,IAAIyD,eAAe,IAAI5G,MAAM,CAAC8G,iBAAiB,KAAK,mBAAmB,EAAE;QACvE9J,MAAM,CAAC8J,iBAAiB,GAAG,mBAAmB;MAChD,CAAC,MAAM,IAAID,eAAe,EAAE;QAC1B7J,MAAM,CAAC8J,iBAAiB,GAAG,SAAS;MACtC;IACF,CAAC,CAAC;EACJ;;;uCAlkBW1M,6BAA6B,EAAAhD,EAAA,CAAA2P,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAA7P,EAAA,CAAA2P,iBAAA,CAAAG,EAAA,CAAAC,wBAAA,GAAA/P,EAAA,CAAA2P,iBAAA,CAAAK,EAAA,CAAAC,qBAAA,GAAAjQ,EAAA,CAAA2P,iBAAA,CAAAO,EAAA,CAAAC,oBAAA,GAAAnQ,EAAA,CAAA2P,iBAAA,CAAAS,EAAA,CAAAC,WAAA,GAAArQ,EAAA,CAAA2P,iBAAA,CAAAW,EAAA,CAAAC,cAAA,GAAAvQ,EAAA,CAAA2P,iBAAA,CAAA3P,EAAA,CAAAwQ,iBAAA,GAAAxQ,EAAA,CAAA2P,iBAAA,CAAAc,EAAA,CAAAC,SAAA,GAAA1Q,EAAA,CAAA2P,iBAAA,CAAAgB,EAAA,CAAAC,gBAAA,GAAA5Q,EAAA,CAAA2P,iBAAA,CAAAkB,EAAA,CAAAC,gBAAA,GAAA9Q,EAAA,CAAA2P,iBAAA,CAAAgB,EAAA,CAAAI,wBAAA,GAAA/Q,EAAA,CAAA2P,iBAAA,CAAAqB,GAAA,CAAAC,iCAAA;IAAA;EAAA;;;YAA7BjO,6BAA6B;MAAAkO,SAAA;MAAAC,SAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;yBAmB7BnT,wBAAwB;;;;;;;;;;;;UC7DrC8B,EAAA,CAAAiC,SAAA,6BAGsB;UAEtBjC,EAAA,CAAAC,cAAA,qCAIkB;UAChBD,EAAA,CAAA2B,UAAA,IAAA4P,4CAAA,mBAAmC;UAiCrCvR,EAAA,CAAAG,YAAA,EAA8B;UAM9BH,EAJA,CAAA2B,UAAA,IAAA6P,4CAAA,iBAAyD,IAAAC,sDAAA,2BAcxD;;;UAzDCzR,EADA,CAAAqB,UAAA,uDAAsD,YAAAiQ,GAAA,CAAAxN,YAAA,CAC9B;UAKxB9D,EAAA,CAAAI,SAAA,EAAoB;UAEpBJ,EAFA,CAAAqB,UAAA,qBAAoB,qBACA,gBACL;UACTrB,EAAA,CAAAI,SAAA,EAAc;UAAdJ,EAAA,CAAAqB,UAAA,UAAAiQ,GAAA,CAAAzN,OAAA,CAAc;UAmCG7D,EAAA,CAAAI,SAAA,EAA8B;UAA9BJ,EAAA,CAAAqB,UAAA,SAAAiQ,GAAA,CAAAzN,OAAA,IAAAyN,GAAA,CAAAtN,aAAA,CAA8B;UAKpDhE,EAAA,CAAAI,SAAA,EAAwC;UAAxCJ,EAAA,CAAAqB,UAAA,UAAAiQ,GAAA,CAAA9F,QAAA,kBAAA8F,GAAA,CAAA9F,QAAA,CAAAkG,aAAA,QAAwC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}