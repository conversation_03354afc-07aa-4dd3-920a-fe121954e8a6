{"ast": null, "code": "export class SetupGameChanges {\n  constructor(data) {\n    this.status = data.status;\n    this.externalGameId = data.externalGameId;\n    if ('settings' in data) {\n      this.settings = data.settings;\n    }\n  }\n}\nexport class SetupGameItem {\n  constructor(game) {\n    this.unviewed = true;\n    this.complete = false;\n    this.required = false;\n    this.game = game;\n  }\n  get title() {\n    return this.game.title;\n  }\n  setChanges(data) {\n    this.complete = true;\n    if (this.required) {\n      this.required = false;\n    }\n    this.changes = new SetupGameChanges(data);\n  }\n  getChanges() {\n    let result = {\n      status: this.getChangedField('status'),\n      externalGameId: this.getChangedField('externalGameId')\n    };\n    let settings = this.getChangedField('settings');\n    if (!!settings && typeof settings === 'object' && Object.keys(settings).length > 0) {\n      Object.assign(result, {\n        settings\n      });\n    }\n    return result;\n  }\n  getGameWithChanges() {\n    return Object.assign({}, this.game, this.getChanges());\n  }\n  getChangedField(field) {\n    return this.changes && field in this.changes ? this.changes[field] : this.game[field];\n  }\n}", "map": {"version": 3, "names": ["SetupGameChanges", "constructor", "data", "status", "externalGameId", "settings", "SetupGameItem", "game", "unviewed", "complete", "required", "title", "set<PERSON><PERSON><PERSON>", "changes", "getChanges", "result", "getChangedField", "Object", "keys", "length", "assign", "getGameWithChanges", "field"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-games/manage-games/setup-game.model.ts"], "sourcesContent": ["import { Game } from '../../../../../../common/typings';\n\nexport class SetupGameChanges {\n  status: string;\n  settings?: Object;\n  externalGameId?: string;\n\n  constructor( data: SetupGameChanges ) {\n    this.status = data.status;\n    this.externalGameId = data.externalGameId;\n\n    if ('settings' in data) {\n      this.settings = data.settings;\n    }\n  }\n}\n\nexport class SetupGameItem {\n  public game: Game;\n  public unviewed: boolean = true;\n  public complete: boolean = false;\n  public required: boolean = false;\n\n  private changes: SetupGameChanges;\n\n  constructor( game: Game ) {\n    this.game = game;\n  }\n\n  get title() {\n    return this.game.title;\n  }\n\n  setChanges( data ) {\n    this.complete = true;\n    if (this.required) {\n      this.required = false;\n    }\n    this.changes = new SetupGameChanges(data);\n  }\n\n  getChanges(): SetupGameChanges {\n    let result: SetupGameChanges = {\n      status: this.getChangedField('status'),\n      externalGameId: this.getChangedField('externalGameId'),\n    };\n\n    let settings = this.getChangedField('settings');\n    if (!!settings && typeof settings === 'object' && Object.keys(settings).length > 0) {\n      Object.assign(result, { settings });\n    }\n\n    return result;\n  }\n\n  getGameWithChanges(): Game {\n    return Object.assign({}, this.game, this.getChanges()) as Game;\n  }\n\n  private getChangedField( field: string ) {\n    return this.changes && field in this.changes ? this.changes[field] : this.game[field];\n  }\n}\n"], "mappings": "AAEA,OAAM,MAAOA,gBAAgB;EAK3BC,YAAaC,IAAsB;IACjC,IAAI,CAACC,MAAM,GAAGD,IAAI,CAACC,MAAM;IACzB,IAAI,CAACC,cAAc,GAAGF,IAAI,CAACE,cAAc;IAEzC,IAAI,UAAU,IAAIF,IAAI,EAAE;MACtB,IAAI,CAACG,QAAQ,GAAGH,IAAI,CAACG,QAAQ;IAC/B;EACF;;AAGF,OAAM,MAAOC,aAAa;EAQxBL,YAAaM,IAAU;IANhB,KAAAC,QAAQ,GAAY,IAAI;IACxB,KAAAC,QAAQ,GAAY,KAAK;IACzB,KAAAC,QAAQ,GAAY,KAAK;IAK9B,IAAI,CAACH,IAAI,GAAGA,IAAI;EAClB;EAEA,IAAII,KAAKA,CAAA;IACP,OAAO,IAAI,CAACJ,IAAI,CAACI,KAAK;EACxB;EAEAC,UAAUA,CAAEV,IAAI;IACd,IAAI,CAACO,QAAQ,GAAG,IAAI;IACpB,IAAI,IAAI,CAACC,QAAQ,EAAE;MACjB,IAAI,CAACA,QAAQ,GAAG,KAAK;IACvB;IACA,IAAI,CAACG,OAAO,GAAG,IAAIb,gBAAgB,CAACE,IAAI,CAAC;EAC3C;EAEAY,UAAUA,CAAA;IACR,IAAIC,MAAM,GAAqB;MAC7BZ,MAAM,EAAE,IAAI,CAACa,eAAe,CAAC,QAAQ,CAAC;MACtCZ,cAAc,EAAE,IAAI,CAACY,eAAe,CAAC,gBAAgB;KACtD;IAED,IAAIX,QAAQ,GAAG,IAAI,CAACW,eAAe,CAAC,UAAU,CAAC;IAC/C,IAAI,CAAC,CAACX,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,IAAIY,MAAM,CAACC,IAAI,CAACb,QAAQ,CAAC,CAACc,MAAM,GAAG,CAAC,EAAE;MAClFF,MAAM,CAACG,MAAM,CAACL,MAAM,EAAE;QAAEV;MAAQ,CAAE,CAAC;IACrC;IAEA,OAAOU,MAAM;EACf;EAEAM,kBAAkBA,CAAA;IAChB,OAAOJ,MAAM,CAACG,MAAM,CAAC,EAAE,EAAE,IAAI,CAACb,IAAI,EAAE,IAAI,CAACO,UAAU,EAAE,CAAS;EAChE;EAEQE,eAAeA,CAAEM,KAAa;IACpC,OAAO,IAAI,CAACT,OAAO,IAAIS,KAAK,IAAI,IAAI,CAACT,OAAO,GAAG,IAAI,CAACA,OAAO,CAACS,KAAK,CAAC,GAAG,IAAI,CAACf,IAAI,CAACe,KAAK,CAAC;EACvF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}