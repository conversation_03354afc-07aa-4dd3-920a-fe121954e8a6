{"ast": null, "code": "import { AVERAGE_PAYMENTS_CHART_SCHEMA, GCR_CHART_SCHEMA, PAYMENTS_CHART_SCHEMA } from './chart-schemas';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@skywind-group/lib-swui\";\nimport * as i2 from \"../../common/services/charts.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"../../common/components/swHighcharts/highcharts.component\";\nimport * as i5 from \"@ngx-translate/core\";\nfunction Dashboard_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"h1\", 13);\n    i0.ɵɵtext(2, \"ALL.SERVICE_IS_UNAVAILABLE\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 13);\n    i0.ɵɵtext(4, \"ALL.SERVICE_IS_UNAVAILABLE_DESC\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class Dashboard {\n  constructor(notifications, chartsService) {\n    this.notifications = notifications;\n    this.chartsService = chartsService;\n    this.isServiceAvailable = true;\n    this.paymentsChart = {\n      data: [],\n      schema: []\n    };\n    this.avgPaymentsChart = {\n      data: [],\n      schema: []\n    };\n    this.gcrChart = {\n      data: [],\n      schema: []\n    };\n    // this.chart1Config = new PieChartConfig();\n    // this.chart1Config.settings = {\n    //   fill: 'rgba(1, 67, 163, 1)',\n    //   stroke: 'black',\n    //   interpolation: 'monotone'\n    // };\n    // this.chart1Config.dataset = D3.range(10).map(Math.random).sort(D3.descending);\n  }\n  ngOnInit() {\n    // this.biService.getIframeUrl('dashboard')\n    //  .then(( data ) => {\n    //    this.location = this.sanitizer.bypassSecurityTrustResourceUrl(data.location);\n    //  })\n    //  .catch(( error ) => {\n    //    this.isServiceAvailable = false;\n    //  });\n    this.paymentsChart.schema = PAYMENTS_CHART_SCHEMA;\n    this.chartsService.getPaymentData().subscribe(data => {\n      this.paymentsChart.data = data;\n    });\n    this.avgPaymentsChart.schema = AVERAGE_PAYMENTS_CHART_SCHEMA;\n    this.chartsService.getAvgPaymentData().subscribe(data => {\n      this.avgPaymentsChart.data = data;\n    });\n    this.gcrChart.schema = GCR_CHART_SCHEMA;\n    this.chartsService.getGcrData().subscribe(data => {\n      this.gcrChart.data = data;\n    });\n  }\n  static {\n    this.ɵfac = function Dashboard_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || Dashboard)(i0.ɵɵdirectiveInject(i1.SwuiNotificationsService), i0.ɵɵdirectiveInject(i2.ChartsService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: Dashboard,\n      selectors: [[\"dashboard\"]],\n      decls: 24,\n      vars: 16,\n      consts: [[1, \"panel\", \"panel-white\"], [1, \"panel-heading\"], [\"translate\", \"\", 1, \"panel-title\"], [\"class\", \"jumbotron\", 4, \"ngIf\"], [1, \"content\", \"mt-20\"], [1, \"row\"], [1, \"col-md-6\"], [3, \"data\", \"schema\", \"title\"], [1, \"panel\", \"panel-flat\", 2, \"height\", \"490px\"], [\"translate\", \"\", 1, \"panel-title\", \"text-semibold\"], [1, \"panel-body\"], [\"src\", \"mocks/chart-04.png\", 1, \"img-responsive\", \"center-block\"], [1, \"jumbotron\"], [\"translate\", \"\"]],\n      template: function Dashboard_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h6\", 2);\n          i0.ɵɵtext(3, \"DASHBOARD.TITLE\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(4, Dashboard_div_4_Template, 5, 0, \"div\", 3);\n          i0.ɵɵelementStart(5, \"div\", 4)(6, \"div\", 5)(7, \"div\", 6);\n          i0.ɵɵelement(8, \"sw-highcharts\", 7);\n          i0.ɵɵpipe(9, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"div\", 6);\n          i0.ɵɵelement(11, \"sw-highcharts\", 7);\n          i0.ɵɵpipe(12, \"translate\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"div\", 5)(14, \"div\", 6);\n          i0.ɵɵelement(15, \"sw-highcharts\", 7);\n          i0.ɵɵpipe(16, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"div\", 6)(18, \"div\", 8)(19, \"div\", 1)(20, \"h6\", 9);\n          i0.ɵɵtext(21, \"DASHBOARD.TOP_GAMES\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(22, \"div\", 10);\n          i0.ɵɵelement(23, \"img\", 11);\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isServiceAvailable);\n          i0.ɵɵadvance(4);\n          i0.ɵɵpropertyInterpolate(\"title\", i0.ɵɵpipeBind1(9, 10, \"DASHBOARD.PAYMENTS\"));\n          i0.ɵɵproperty(\"data\", ctx.paymentsChart.data)(\"schema\", ctx.paymentsChart.schema);\n          i0.ɵɵadvance(3);\n          i0.ɵɵpropertyInterpolate(\"title\", i0.ɵɵpipeBind1(12, 12, \"DASHBOARD.GGR\"));\n          i0.ɵɵproperty(\"data\", ctx.gcrChart.data)(\"schema\", ctx.gcrChart.schema);\n          i0.ɵɵadvance(4);\n          i0.ɵɵpropertyInterpolate(\"title\", i0.ɵɵpipeBind1(16, 14, \"DASHBOARD.AVERAGE_PAYMENTS\"));\n          i0.ɵɵproperty(\"data\", ctx.avgPaymentsChart.data)(\"schema\", ctx.avgPaymentsChart.schema);\n        }\n      },\n      dependencies: [i3.NgIf, i4.HighchartsComponent, i5.TranslateDirective, i5.TranslatePipe],\n      styles: [\"@media screen and (min-width: 1620px) {\\n  .row.shift-up > * {\\n    margin-top: -573px;\\n  }\\n}\\n@media screen and (max-width: 1620px) {\\n  .card.feed-panel.large-card {\\n    height: 824px;\\n  }\\n}\\n.user-stats-card .card-title {\\n  padding: 0 0 15px;\\n}\\n\\n.blurCalendar {\\n  height: 475px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvZGFzaGJvYXJkL2Rhc2hib2FyZC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBRUk7SUFDRSxrQkFBQTtFQUFKO0FBQ0Y7QUFJQTtFQUVJO0lBQ0UsYUFBQTtFQUhKO0FBQ0Y7QUFRRTtFQUNFLGlCQUFBO0FBTko7O0FBVUE7RUFDRSxhQUFBO0FBUEYiLCJzb3VyY2VzQ29udGVudCI6WyJAbWVkaWEgc2NyZWVuIGFuZCAobWluLXdpZHRoOiAxNjIwcHgpIHtcbiAgLnJvdy5zaGlmdC11cCB7XG4gICAgPiAqIHtcbiAgICAgIG1hcmdpbi10b3A6IC01NzNweDtcbiAgICB9XG4gIH1cbn1cblxuQG1lZGlhIHNjcmVlbiBhbmQgKG1heC13aWR0aDogMTYyMHB4KSB7XG4gIC5jYXJkLmZlZWQtcGFuZWwge1xuICAgICYubGFyZ2UtY2FyZCB7XG4gICAgICBoZWlnaHQ6IDgyNHB4O1xuICAgIH1cbiAgfVxufVxuXG4udXNlci1zdGF0cy1jYXJkIHtcbiAgLmNhcmQtdGl0bGUge1xuICAgIHBhZGRpbmc6IDAgMCAxNXB4O1xuICB9XG59XG5cbi5ibHVyQ2FsZW5kYXIge1xuICBoZWlnaHQ6IDQ3NXB4O1xufVxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["AVERAGE_PAYMENTS_CHART_SCHEMA", "GCR_CHART_SCHEMA", "PAYMENTS_CHART_SCHEMA", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "Dashboard", "constructor", "notifications", "chartsService", "isServiceAvailable", "paymentsChart", "data", "schema", "avgPaymentsChart", "gcrChart", "ngOnInit", "getPaymentData", "subscribe", "getAvgPaymentData", "getGcrData", "ɵɵdirectiveInject", "i1", "SwuiNotificationsService", "i2", "ChartsService", "selectors", "decls", "vars", "consts", "template", "Dashboard_Template", "rf", "ctx", "ɵɵtemplate", "Dashboard_div_4_Template", "ɵɵelement", "ɵɵadvance", "ɵɵproperty", "ɵɵpropertyInterpolate", "ɵɵpipeBind1"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/dashboard/dashboard.component.ts", "/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/dashboard/dashboard.html"], "sourcesContent": ["import { Component, OnInit, ViewEncapsulation } from '@angular/core';\nimport { SwuiNotificationsService } from '@skywind-group/lib-swui';\n\nimport { ChartsService } from '../../common/services/charts.service';\nimport { AVERAGE_PAYMENTS_CHART_SCHEMA, GCR_CHART_SCHEMA, PAYMENTS_CHART_SCHEMA } from './chart-schemas';\n\n\n@Component({\n  selector: 'dashboard',\n  encapsulation: ViewEncapsulation.None,\n  styleUrls: ['./dashboard.scss'],\n  templateUrl: './dashboard.html'\n})\nexport class Dashboard implements OnInit {\n  isServiceAvailable = true;\n  location: any;\n\n  paymentsChart = {\n    data: [],\n    schema: []\n  };\n  avgPaymentsChart = {\n    data: [],\n    schema: []\n  };\n  gcrChart = {\n    data: [],\n    schema: []\n  };\n\n  constructor(public notifications: SwuiNotificationsService,\n    private chartsService: ChartsService,\n  ) {\n    // this.chart1Config = new PieChartConfig();\n    // this.chart1Config.settings = {\n    //   fill: 'rgba(1, 67, 163, 1)',\n    //   stroke: 'black',\n    //   interpolation: 'monotone'\n    // };\n    // this.chart1Config.dataset = D3.range(10).map(Math.random).sort(D3.descending);\n  }\n\n  ngOnInit() {\n    // this.biService.getIframeUrl('dashboard')\n    //  .then(( data ) => {\n    //    this.location = this.sanitizer.bypassSecurityTrustResourceUrl(data.location);\n    //  })\n    //  .catch(( error ) => {\n    //    this.isServiceAvailable = false;\n    //  });\n\n    this.paymentsChart.schema = PAYMENTS_CHART_SCHEMA;\n    this.chartsService.getPaymentData().subscribe((data) => {\n      this.paymentsChart.data = data;\n    });\n\n    this.avgPaymentsChart.schema = AVERAGE_PAYMENTS_CHART_SCHEMA;\n    this.chartsService.getAvgPaymentData().subscribe((data) => {\n      this.avgPaymentsChart.data = data;\n    });\n\n    this.gcrChart.schema = GCR_CHART_SCHEMA;\n    this.chartsService.getGcrData().subscribe((data) => {\n      this.gcrChart.data = data;\n    });\n\n\n  }\n\n}\n", "<div class=\"panel panel-white\">\n  <div class=\"panel-heading\">\n    <h6 class=\"panel-title\" translate>DASHBOARD.TITLE</h6>\n  </div>\n  <div class=\"jumbotron\" *ngIf=\"!isServiceAvailable\">\n    <h1 translate>ALL.SERVICE_IS_UNAVAILABLE</h1>\n    <p translate>ALL.SERVICE_IS_UNAVAILABLE_DESC</p>\n  </div>\n\n  <!-- <iframe *ngIf=\"isServiceAvailable\" [src]=\"location\" width=\"100%\" height=\"1000px\" frameborder=\"0\"></iframe> -->\n  <div class=\"content mt-20\">\n\n    <div class=\"row\">\n      <div class=\"col-md-6\">\n        <sw-highcharts [data]=\"paymentsChart.data\" [schema]=\"paymentsChart.schema\"\n                       title=\"{{'DASHBOARD.PAYMENTS'|translate}}\">\n        </sw-highcharts>\n      </div>\n\n      <div class=\"col-md-6\">\n        <sw-highcharts [data]=\"gcrChart.data\" [schema]=\"gcrChart.schema\"\n                       title=\"{{'DASHBOARD.GGR'|translate}}\">\n        </sw-highcharts>\n      </div>\n    </div>\n\n    <div class=\"row\">\n      <div class=\"col-md-6\">\n        <sw-highcharts [data]=\"avgPaymentsChart.data\"\n                       [schema]=\"avgPaymentsChart.schema\"\n                       title=\"{{'DASHBOARD.AVERAGE_PAYMENTS'|translate}}\">\n        </sw-highcharts>\n      </div>\n      <div class=\"col-md-6\">\n        <div class=\"panel panel-flat\" style=\"height:490px\">\n          <div class=\"panel-heading\">\n            <h6 class=\"panel-title text-semibold\" translate>DASHBOARD.TOP_GAMES</h6>\n          </div>\n          <div class=\"panel-body\">\n            <img class=\"img-responsive center-block\" src=\"mocks/chart-04.png\"/>\n          </div>\n        </div>\n      </div>\n\n    </div>\n\n  </div>\n</div>\n"], "mappings": "AAIA,SAASA,6BAA6B,EAAEC,gBAAgB,EAAEC,qBAAqB,QAAQ,iBAAiB;;;;;;;;;ICCpGC,EADF,CAAAC,cAAA,cAAmD,aACnC;IAAAD,EAAA,CAAAE,MAAA,iCAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC7CH,EAAA,CAAAC,cAAA,YAAa;IAAAD,EAAA,CAAAE,MAAA,sCAA+B;IAC9CF,EAD8C,CAAAG,YAAA,EAAI,EAC5C;;;ADMR,OAAM,MAAOC,SAAS;EAiBpBC,YAAmBC,aAAuC,EAChDC,aAA4B;IADnB,KAAAD,aAAa,GAAbA,aAAa;IACtB,KAAAC,aAAa,GAAbA,aAAa;IAjBvB,KAAAC,kBAAkB,GAAG,IAAI;IAGzB,KAAAC,aAAa,GAAG;MACdC,IAAI,EAAE,EAAE;MACRC,MAAM,EAAE;KACT;IACD,KAAAC,gBAAgB,GAAG;MACjBF,IAAI,EAAE,EAAE;MACRC,MAAM,EAAE;KACT;IACD,KAAAE,QAAQ,GAAG;MACTH,IAAI,EAAE,EAAE;MACRC,MAAM,EAAE;KACT;IAKC;IACA;IACA;IACA;IACA;IACA;IACA;EACF;EAEAG,QAAQA,CAAA;IACN;IACA;IACA;IACA;IACA;IACA;IACA;IAEA,IAAI,CAACL,aAAa,CAACE,MAAM,GAAGZ,qBAAqB;IACjD,IAAI,CAACQ,aAAa,CAACQ,cAAc,EAAE,CAACC,SAAS,CAAEN,IAAI,IAAI;MACrD,IAAI,CAACD,aAAa,CAACC,IAAI,GAAGA,IAAI;IAChC,CAAC,CAAC;IAEF,IAAI,CAACE,gBAAgB,CAACD,MAAM,GAAGd,6BAA6B;IAC5D,IAAI,CAACU,aAAa,CAACU,iBAAiB,EAAE,CAACD,SAAS,CAAEN,IAAI,IAAI;MACxD,IAAI,CAACE,gBAAgB,CAACF,IAAI,GAAGA,IAAI;IACnC,CAAC,CAAC;IAEF,IAAI,CAACG,QAAQ,CAACF,MAAM,GAAGb,gBAAgB;IACvC,IAAI,CAACS,aAAa,CAACW,UAAU,EAAE,CAACF,SAAS,CAAEN,IAAI,IAAI;MACjD,IAAI,CAACG,QAAQ,CAACH,IAAI,GAAGA,IAAI;IAC3B,CAAC,CAAC;EAGJ;;;uCAtDWN,SAAS,EAAAJ,EAAA,CAAAmB,iBAAA,CAAAC,EAAA,CAAAC,wBAAA,GAAArB,EAAA,CAAAmB,iBAAA,CAAAG,EAAA,CAAAC,aAAA;IAAA;EAAA;;;YAATnB,SAAS;MAAAoB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCXlB9B,EAFJ,CAAAC,cAAA,aAA+B,aACF,YACS;UAAAD,EAAA,CAAAE,MAAA,sBAAe;UACnDF,EADmD,CAAAG,YAAA,EAAK,EAClD;UACNH,EAAA,CAAAgC,UAAA,IAAAC,wBAAA,iBAAmD;UAS/CjC,EAHJ,CAAAC,cAAA,aAA2B,aAER,aACO;UACpBD,EAAA,CAAAkC,SAAA,uBAEgB;;UAClBlC,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,cAAsB;UACpBD,EAAA,CAAAkC,SAAA,wBAEgB;;UAEpBlC,EADE,CAAAG,YAAA,EAAM,EACF;UAGJH,EADF,CAAAC,cAAA,cAAiB,cACO;UACpBD,EAAA,CAAAkC,SAAA,wBAGgB;;UAClBlC,EAAA,CAAAG,YAAA,EAAM;UAIAH,EAHN,CAAAC,cAAA,cAAsB,cAC+B,cACtB,aACuB;UAAAD,EAAA,CAAAE,MAAA,2BAAmB;UACrEF,EADqE,CAAAG,YAAA,EAAK,EACpE;UACNH,EAAA,CAAAC,cAAA,eAAwB;UACtBD,EAAA,CAAAkC,SAAA,eAAmE;UAQ/ElC,EAPU,CAAAG,YAAA,EAAM,EACF,EACF,EAEF,EAEF,EACF;;;UA3CoBH,EAAA,CAAAmC,SAAA,GAAyB;UAAzBnC,EAAA,CAAAoC,UAAA,UAAAL,GAAA,CAAAvB,kBAAA,CAAyB;UAW5BR,EAAA,CAAAmC,SAAA,GAA0C;UAA1CnC,EAAA,CAAAqC,qBAAA,UAAArC,EAAA,CAAAsC,WAAA,8BAA0C;UADdtC,EAA5B,CAAAoC,UAAA,SAAAL,GAAA,CAAAtB,aAAA,CAAAC,IAAA,CAA2B,WAAAqB,GAAA,CAAAtB,aAAA,CAAAE,MAAA,CAAgC;UAO3DX,EAAA,CAAAmC,SAAA,GAAqC;UAArCnC,EAAA,CAAAqC,qBAAA,UAAArC,EAAA,CAAAsC,WAAA,0BAAqC;UADdtC,EAAvB,CAAAoC,UAAA,SAAAL,GAAA,CAAAlB,QAAA,CAAAH,IAAA,CAAsB,WAAAqB,GAAA,CAAAlB,QAAA,CAAAF,MAAA,CAA2B;UAUjDX,EAAA,CAAAmC,SAAA,GAAkD;UAAlDnC,EAAA,CAAAqC,qBAAA,UAAArC,EAAA,CAAAsC,WAAA,uCAAkD;UADlDtC,EADA,CAAAoC,UAAA,SAAAL,GAAA,CAAAnB,gBAAA,CAAAF,IAAA,CAA8B,WAAAqB,GAAA,CAAAnB,gBAAA,CAAAD,MAAA,CACI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}