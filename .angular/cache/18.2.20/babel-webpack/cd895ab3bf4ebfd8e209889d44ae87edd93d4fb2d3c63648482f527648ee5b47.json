{"ast": null, "code": "export {};", "map": {"version": 3, "names": [], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/payments/components/transfers/transfers.module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { FlexLayoutModule } from '@angular/flex-layout';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiGridModule, SwuiNotificationsModule, SwuiPagePanelModule, SwuiSchemaTopFilterModule } from '@skywind-group/lib-swui';\nimport { DownloadCsvModule } from '../../../../common/components/download-csv/download-csv.module';\nimport { HintsModule } from '../../../../common/components/hints/hints.module';\nimport { TransfersService } from '../../../../common/services/transfers.service';\nimport { TransfersComponent } from './transfers.component';\n\n\n@NgModule({\n  imports: [\n    CommonModule,\n    SwuiPagePanelModule,\n    SwuiGridModule,\n    SwuiNotificationsModule.forRoot(),\n    TranslateModule,\n    FlexLayoutModule,\n    MatTooltipModule,\n    SwuiSchemaTopFilterModule,\n    DownloadCsvModule,\n    HintsModule\n  ],\n  exports: [],\n  declarations: [\n    TransfersComponent,\n  ],\n  providers: [\n    TransfersService,\n  ],\n"], "mappings": "AA+BI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}