{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatIconModule } from '@angular/material/icon';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { RemoveConfirmDialogComponent } from './remove-confirm-dialog.component';\nimport * as i0 from \"@angular/core\";\nexport class EntitySetupDialogsModule {\n  static {\n    this.ɵfac = function EntitySetupDialogsModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || EntitySetupDialogsModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: EntitySetupDialogsModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, TranslateModule, MatDialogModule, MatButtonModule, MatIconModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(EntitySetupDialogsModule, {\n    declarations: [RemoveConfirmDialogComponent],\n    imports: [CommonModule, TranslateModule, MatDialogModule, MatButtonModule, MatIconModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "MatButtonModule", "MatDialogModule", "MatIconModule", "TranslateModule", "RemoveConfirmDialogComponent", "EntitySetupDialogsModule", "declarations", "imports"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/dialogs/entity-setup-dialogs.module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatIconModule } from '@angular/material/icon';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { RemoveConfirmDialogComponent } from './remove-confirm-dialog.component';\n\n@NgModule({\n  imports: [\n    CommonModule,\n    TranslateModule,\n\n    MatDialogModule,\n    MatButtonModule,\n    MatIconModule,\n  ],\n  declarations: [\n    RemoveConfirmDialogComponent,\n  ],\n  exports: [],\n  providers: [\n  ],\n})\nexport class EntitySetupDialogsModule {\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,4BAA4B,QAAQ,mCAAmC;;AAkBhF,OAAM,MAAOC,wBAAwB;;;uCAAxBA,wBAAwB;IAAA;EAAA;;;YAAxBA;IAAwB;EAAA;;;gBAdjCN,YAAY,EACZI,eAAe,EAEfF,eAAe,EACfD,eAAe,EACfE,aAAa;IAAA;EAAA;;;2EASJG,wBAAwB;IAAAC,YAAA,GANjCF,4BAA4B;IAAAG,OAAA,GAR5BR,YAAY,EACZI,eAAe,EAEfF,eAAe,EACfD,eAAe,EACfE,aAAa;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}