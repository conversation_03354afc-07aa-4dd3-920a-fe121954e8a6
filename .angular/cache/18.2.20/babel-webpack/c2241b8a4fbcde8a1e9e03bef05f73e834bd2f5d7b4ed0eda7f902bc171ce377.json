{"ast": null, "code": "export {};", "map": {"version": 3, "names": [], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/reports/reports-section.module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiPagePanelModule } from '@skywind-group/lib-swui';\nimport { BaCardModule } from '../../common/components/baCard/baCard.module';\nimport { GameService } from '../../common/services/game.service';\nimport { BriefResolver } from '../../common/services/resolvers/brief.resolver';\nimport { GamesShortInfoResolver } from '../../common/services/resolvers/gamesShortInfo.resolver';\nimport { MerchantBriefResolver } from '../../common/services/resolvers/merchant-brief.resolver';\nimport { ReportAuditLogComponent } from './components/audit-log/audit-log.component';\nimport { ReportCurrencyModule } from './components/currency/report-currency.module';\nimport { ReportFinancialModule } from './components/financial/report-financial.module';\nimport { ReportIndexComponent } from './components/index/index.component';\nimport { ReportKpiComponent } from './components/kpi';\nimport { ReportPlayerModule } from './components/player/report-player.module';\nimport { ReportRgModule } from './components/report-rg/report-rg.module';\nimport { ReportsComponent } from './reports.component';\nimport { ReportsRoutingModule } from './reports.routing';\n\n\n@NgModule({\n  imports: [\n    CommonModule,\n    TranslateModule,\n    BaCardModule,\n    ReportsRoutingModule,\n    SwuiPagePanelModule,\n    ReportPlayerModule,\n    ReportCurrencyModule,\n    ReportFinancialModule,\n    ReportRgModule\n  ],\n  declarations: [\n    ReportsComponent,\n    ReportIndexComponent,\n    ReportAuditLogComponent,\n    ReportKpiComponent,\n  ],\n  providers: [\n    BriefResolver,\n    MerchantBriefResolver,\n    GameService,\n    GamesShortInfoResolver\n  ],\n"], "mappings": "AA2CI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}