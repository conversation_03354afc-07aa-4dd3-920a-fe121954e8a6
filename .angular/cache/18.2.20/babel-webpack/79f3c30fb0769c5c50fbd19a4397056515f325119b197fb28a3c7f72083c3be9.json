{"ast": null, "code": "import { of } from 'rxjs';\nimport { InjectionToken } from '@angular/core';\nexport const SW_FORM_SERVICE = new InjectionToken('sw-form-service');\nexport class MockFormService {\n  get formSubmitted$() {\n    return of(false);\n  }\n}", "map": {"version": 3, "names": ["of", "InjectionToken", "SW_FORM_SERVICE", "MockFormService", "formSubmitted$"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-rtp-reducer/rtp-reduce-modal/games-form/form-service.model.ts"], "sourcesContent": ["import { Observable, of } from 'rxjs';\nimport { InjectionToken } from '@angular/core';\n\nexport interface FormService {\n  formSubmitted$: Observable<boolean>;\n}\n\nexport const SW_FORM_SERVICE = new InjectionToken<FormService>('sw-form-service');\n\nexport class MockFormService implements FormService {\n\n  get formSubmitted$(): Observable<boolean> {\n    return of(false);\n  }\n}\n"], "mappings": "AAAA,SAAqBA,EAAE,QAAQ,MAAM;AACrC,SAASC,cAAc,QAAQ,eAAe;AAM9C,OAAO,MAAMC,eAAe,GAAG,IAAID,cAAc,CAAc,iBAAiB,CAAC;AAEjF,OAAM,MAAOE,eAAe;EAE1B,IAAIC,cAAcA,CAAA;IAChB,OAAOJ,EAAE,CAAC,KAAK,CAAC;EAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}