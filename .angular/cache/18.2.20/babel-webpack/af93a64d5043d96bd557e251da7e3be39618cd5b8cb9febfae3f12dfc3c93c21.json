{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FlexModule } from '@angular/flex-layout';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiChipsAutocompleteModule, SwuiSelectModule } from '@skywind-group/lib-swui';\nimport { EntityLabelsService } from '../../../../../../common/services/entity-labels.service';\nimport { LabelsService } from '../../../../../../common/services/labels.service';\nimport { EntityLabelsDialogComponent } from './entity-labels-dialog.component';\nimport * as i0 from \"@angular/core\";\nexport class EntityLabelsDialogModule {\n  static {\n    this.ɵfac = function EntityLabelsDialogModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || EntityLabelsDialogModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: EntityLabelsDialogModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [LabelsService, EntityLabelsService],\n      imports: [MatButtonModule, MatIconModule, MatDialogModule, TranslateModule, ReactiveFormsModule, FlexModule, MatFormFieldModule, SwuiSelectModule, SwuiChipsAutocompleteModule, CommonModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(EntityLabelsDialogModule, {\n    declarations: [EntityLabelsDialogComponent],\n    imports: [MatButtonModule, MatIconModule, MatDialogModule, TranslateModule, ReactiveFormsModule, FlexModule, MatFormFieldModule, SwuiSelectModule, SwuiChipsAutocompleteModule, CommonModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FlexModule", "ReactiveFormsModule", "MatButtonModule", "MatDialogModule", "MatFormFieldModule", "MatIconModule", "TranslateModule", "SwuiChipsAutocompleteModule", "SwuiSelectModule", "EntityLabelsService", "LabelsService", "EntityLabelsDialogComponent", "EntityLabelsDialogModule", "imports", "declarations"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/mat-business-structure/dialogs/entity-labels-dialog/entity-labels-dialog.module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { FlexModule } from '@angular/flex-layout';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiChipsAutocompleteModule, SwuiSelectModule } from '@skywind-group/lib-swui';\nimport { EntityLabelsService } from '../../../../../../common/services/entity-labels.service';\nimport { LabelsService } from '../../../../../../common/services/labels.service';\nimport { EntityLabelsDialogComponent } from './entity-labels-dialog.component';\n\n@NgModule({\n  imports: [\n    MatButtonModule,\n    MatIconModule,\n    MatDialogModule,\n    TranslateModule,\n    ReactiveFormsModule,\n    FlexModule,\n    MatFormFieldModule,\n    SwuiSelectModule,\n    SwuiChipsAutocompleteModule,\n    CommonModule,\n  ],\n  declarations: [\n    EntityLabelsDialogComponent,\n  ],\n  providers: [\n    LabelsService,\n    EntityLabelsService,\n  ],\n})\nexport class EntityLabelsDialogModule {\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,UAAU,QAAQ,sBAAsB;AACjD,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,2BAA2B,EAAEC,gBAAgB,QAAQ,yBAAyB;AACvF,SAASC,mBAAmB,QAAQ,yDAAyD;AAC7F,SAASC,aAAa,QAAQ,kDAAkD;AAChF,SAASC,2BAA2B,QAAQ,kCAAkC;;AAuB9E,OAAM,MAAOC,wBAAwB;;;uCAAxBA,wBAAwB;IAAA;EAAA;;;YAAxBA;IAAwB;EAAA;;;iBALxB,CACTF,aAAa,EACbD,mBAAmB,CACpB;MAAAI,OAAA,GAjBCX,eAAe,EACfG,aAAa,EACbF,eAAe,EACfG,eAAe,EACfL,mBAAmB,EACnBD,UAAU,EACVI,kBAAkB,EAClBI,gBAAgB,EAChBD,2BAA2B,EAC3BR,YAAY;IAAA;EAAA;;;2EAUHa,wBAAwB;IAAAE,YAAA,GAPjCH,2BAA2B;IAAAE,OAAA,GAZ3BX,eAAe,EACfG,aAAa,EACbF,eAAe,EACfG,eAAe,EACfL,mBAAmB,EACnBD,UAAU,EACVI,kBAAkB,EAClBI,gBAAgB,EAChBD,2BAA2B,EAC3BR,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}