{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { TrimInputValueModule } from '../../../../../../common/directives/trim-input-value/trim-input-value.module';\nimport { MatCountryDialogComponent } from './mat-country-dialog.component';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport * as i0 from \"@angular/core\";\nexport const matModules = [MatTableModule, MatFormFieldModule, MatInputModule, MatButtonModule, MatDialogModule, MatCheckboxModule];\nexport class MatCountryDialogModule {\n  static {\n    this.ɵfac = function MatCountryDialogModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatCountryDialogModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: MatCountryDialogModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, TranslateModule, FormsModule, matModules, TrimInputValueModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(MatCountryDialogModule, {\n    declarations: [MatCountryDialogComponent],\n    imports: [CommonModule, TranslateModule, FormsModule, MatTableModule, MatFormFieldModule, MatInputModule, MatButtonModule, MatDialogModule, MatCheckboxModule, TrimInputValueModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "TranslateModule", "TrimInputValueModule", "MatCountryDialogComponent", "MatFormFieldModule", "MatTableModule", "MatCheckboxModule", "MatDialogModule", "MatInputModule", "MatButtonModule", "matModules", "MatCountryDialogModule", "declarations", "imports"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/mat-business-structure/dialogs/mat-country-dialog/mat-country-dialog.module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { FormsModule } from '@angular/forms';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { TrimInputValueModule } from '../../../../../../common/directives/trim-input-value/trim-input-value.module';\n\n\nimport { MatCountryDialogComponent } from './mat-country-dialog.component';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\n\nexport const matModules = [\n  MatTableModule,\n  MatFormFieldModule,\n  MatInputModule,\n  MatButtonModule,\n  MatDialogModule,\n  MatCheckboxModule,\n];\n\n@NgModule({\n    imports: [\n        CommonModule,\n        TranslateModule,\n        FormsModule,\n        ...matModules,\n        TrimInputValueModule,\n    ],\n  declarations: [\n    MatCountryDialogComponent\n  ],\n})\nexport class MatCountryDialogModule {\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,oBAAoB,QAAQ,8EAA8E;AAGnH,SAASC,yBAAyB,QAAQ,gCAAgC;AAC1E,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;;AAE1D,OAAO,MAAMC,UAAU,GAAG,CACxBL,cAAc,EACdD,kBAAkB,EAClBI,cAAc,EACdC,eAAe,EACfF,eAAe,EACfD,iBAAiB,CAClB;AAcD,OAAM,MAAOK,sBAAsB;;;uCAAtBA,sBAAsB;IAAA;EAAA;;;YAAtBA;IAAsB;EAAA;;;gBAV3BZ,YAAY,EACZE,eAAe,EACfD,WAAW,EACRU,UAAU,EACbR,oBAAoB;IAAA;EAAA;;;2EAMfS,sBAAsB;IAAAC,YAAA,GAH/BT,yBAAyB;IAAAU,OAAA,GAPrBd,YAAY,EACZE,eAAe,EACfD,WAAW,EAZjBK,cAAc,EACdD,kBAAkB,EAClBI,cAAc,EACdC,eAAe,EACfF,eAAe,EACfD,iBAAiB,EASXJ,oBAAoB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}