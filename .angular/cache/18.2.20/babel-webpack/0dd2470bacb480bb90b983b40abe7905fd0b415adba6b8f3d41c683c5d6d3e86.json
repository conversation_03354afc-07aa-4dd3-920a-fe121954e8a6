{"ast": null, "code": "import { FocusKeyManager } from '@angular/cdk/a11y';\nimport * as i1 from '@angular/cdk/bidi';\nimport { BidiModule } from '@angular/cdk/bidi';\nimport { hasModifierKey, SPACE, ENTER } from '@angular/cdk/keycodes';\nimport * as i0 from '@angular/core';\nimport { Directive, InjectionToken, EventEmitter, forwardRef, booleanAttribute, TemplateRef, Component, ViewEncapsulation, ChangeDetectionStrategy, Inject, Optional, ContentChild, ContentChildren, ViewChild, Input, Output, QueryList, numberAttribute, NgModule } from '@angular/core';\nimport { ControlContainer } from '@angular/forms';\nimport { _getFocusedElementPierceShadowDom } from '@angular/cdk/platform';\nimport { Subject, of } from 'rxjs';\nimport { startWith, takeUntil } from 'rxjs/operators';\nconst _c0 = [\"*\"];\nfunction CdkStep_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\nclass CdkStepHeader {\n  constructor(_elementRef) {\n    this._elementRef = _elementRef;\n  }\n  /** Focuses the step header. */\n  focus() {\n    this._elementRef.nativeElement.focus();\n  }\n  static {\n    this.ɵfac = function CdkStepHeader_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkStepHeader)(i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkStepHeader,\n      selectors: [[\"\", \"cdkStepHeader\", \"\"]],\n      hostAttrs: [\"role\", \"tab\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkStepHeader, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkStepHeader]',\n      host: {\n        'role': 'tab'\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }], null);\n})();\nclass CdkStepLabel {\n  constructor(/** @docs-private */template) {\n    this.template = template;\n  }\n  static {\n    this.ɵfac = function CdkStepLabel_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkStepLabel)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkStepLabel,\n      selectors: [[\"\", \"cdkStepLabel\", \"\"]],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkStepLabel, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkStepLabel]',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], null);\n})();\n\n/** Used to generate unique ID for each stepper component. */\nlet nextId = 0;\n/** Change event emitted on selection changes. */\nclass StepperSelectionEvent {}\n/** Enum to represent the different states of the steps. */\nconst STEP_STATE = {\n  NUMBER: 'number',\n  EDIT: 'edit',\n  DONE: 'done',\n  ERROR: 'error'\n};\n/** InjectionToken that can be used to specify the global stepper options. */\nconst STEPPER_GLOBAL_OPTIONS = new InjectionToken('STEPPER_GLOBAL_OPTIONS');\nclass CdkStep {\n  /** Whether step is marked as completed. */\n  get completed() {\n    return this._completedOverride == null ? this._getDefaultCompleted() : this._completedOverride;\n  }\n  set completed(value) {\n    this._completedOverride = value;\n  }\n  _getDefaultCompleted() {\n    return this.stepControl ? this.stepControl.valid && this.interacted : this.interacted;\n  }\n  /** Whether step has an error. */\n  get hasError() {\n    return this._customError == null ? this._getDefaultError() : this._customError;\n  }\n  set hasError(value) {\n    this._customError = value;\n  }\n  _getDefaultError() {\n    return this.stepControl && this.stepControl.invalid && this.interacted;\n  }\n  constructor(_stepper, stepperOptions) {\n    this._stepper = _stepper;\n    /** Whether user has attempted to move away from the step. */\n    this.interacted = false;\n    /** Emits when the user has attempted to move away from the step. */\n    this.interactedStream = new EventEmitter();\n    /** Whether the user can return to this step once it has been marked as completed. */\n    this.editable = true;\n    /** Whether the completion of step is optional. */\n    this.optional = false;\n    this._completedOverride = null;\n    this._customError = null;\n    this._stepperOptions = stepperOptions ? stepperOptions : {};\n    this._displayDefaultIndicatorType = this._stepperOptions.displayDefaultIndicatorType !== false;\n  }\n  /** Selects this step component. */\n  select() {\n    this._stepper.selected = this;\n  }\n  /** Resets the step to its initial state. Note that this includes resetting form data. */\n  reset() {\n    this.interacted = false;\n    if (this._completedOverride != null) {\n      this._completedOverride = false;\n    }\n    if (this._customError != null) {\n      this._customError = false;\n    }\n    if (this.stepControl) {\n      // Reset the forms since the default error state matchers will show errors on submit and we\n      // want the form to be back to its initial state (see #29781). Submitted state is on the\n      // individual directives, rather than the control, so we need to reset them ourselves.\n      this._childForms?.forEach(form => form.resetForm?.());\n      this.stepControl.reset();\n    }\n  }\n  ngOnChanges() {\n    // Since basically all inputs of the MatStep get proxied through the view down to the\n    // underlying MatStepHeader, we have to make sure that change detection runs correctly.\n    this._stepper._stateChanged();\n  }\n  _markAsInteracted() {\n    if (!this.interacted) {\n      this.interacted = true;\n      this.interactedStream.emit(this);\n    }\n  }\n  /** Determines whether the error state can be shown. */\n  _showError() {\n    // We want to show the error state either if the user opted into/out of it using the\n    // global options, or if they've explicitly set it through the `hasError` input.\n    return this._stepperOptions.showError ?? this._customError != null;\n  }\n  static {\n    this.ɵfac = function CdkStep_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkStep)(i0.ɵɵdirectiveInject(forwardRef(() => CdkStepper)), i0.ɵɵdirectiveInject(STEPPER_GLOBAL_OPTIONS, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: CdkStep,\n      selectors: [[\"cdk-step\"]],\n      contentQueries: function CdkStep_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, CdkStepLabel, 5);\n          i0.ɵɵcontentQuery(dirIndex,\n          // Note: we look for `ControlContainer` here, because both `NgForm` and `FormGroupDirective`\n          // provides themselves as such, but we don't want to have a concrete reference to both of\n          // the directives. The type is marked as `Partial` in case we run into a class that provides\n          // itself as `ControlContainer` but doesn't have the same interface as the directives.\n          ControlContainer, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.stepLabel = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._childForms = _t);\n        }\n      },\n      viewQuery: function CdkStep_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(TemplateRef, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.content = _t.first);\n        }\n      },\n      inputs: {\n        stepControl: \"stepControl\",\n        label: \"label\",\n        errorMessage: \"errorMessage\",\n        ariaLabel: [0, \"aria-label\", \"ariaLabel\"],\n        ariaLabelledby: [0, \"aria-labelledby\", \"ariaLabelledby\"],\n        state: \"state\",\n        editable: [2, \"editable\", \"editable\", booleanAttribute],\n        optional: [2, \"optional\", \"optional\", booleanAttribute],\n        completed: [2, \"completed\", \"completed\", booleanAttribute],\n        hasError: [2, \"hasError\", \"hasError\", booleanAttribute]\n      },\n      outputs: {\n        interactedStream: \"interacted\"\n      },\n      exportAs: [\"cdkStep\"],\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature, i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 1,\n      vars: 0,\n      template: function CdkStep_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, CdkStep_ng_template_0_Template, 1, 0, \"ng-template\");\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkStep, [{\n    type: Component,\n    args: [{\n      selector: 'cdk-step',\n      exportAs: 'cdkStep',\n      template: '<ng-template><ng-content/></ng-template>',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      standalone: true\n    }]\n  }], () => [{\n    type: CdkStepper,\n    decorators: [{\n      type: Inject,\n      args: [forwardRef(() => CdkStepper)]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [STEPPER_GLOBAL_OPTIONS]\n    }]\n  }], {\n    stepLabel: [{\n      type: ContentChild,\n      args: [CdkStepLabel]\n    }],\n    _childForms: [{\n      type: ContentChildren,\n      args: [\n      // Note: we look for `ControlContainer` here, because both `NgForm` and `FormGroupDirective`\n      // provides themselves as such, but we don't want to have a concrete reference to both of\n      // the directives. The type is marked as `Partial` in case we run into a class that provides\n      // itself as `ControlContainer` but doesn't have the same interface as the directives.\n      ControlContainer, {\n        descendants: true\n      }]\n    }],\n    content: [{\n      type: ViewChild,\n      args: [TemplateRef, {\n        static: true\n      }]\n    }],\n    stepControl: [{\n      type: Input\n    }],\n    interactedStream: [{\n      type: Output,\n      args: ['interacted']\n    }],\n    label: [{\n      type: Input\n    }],\n    errorMessage: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    ariaLabelledby: [{\n      type: Input,\n      args: ['aria-labelledby']\n    }],\n    state: [{\n      type: Input\n    }],\n    editable: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    optional: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    completed: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    hasError: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\nclass CdkStepper {\n  /** The index of the selected step. */\n  get selectedIndex() {\n    return this._selectedIndex;\n  }\n  set selectedIndex(index) {\n    if (this.steps && this._steps) {\n      // Ensure that the index can't be out of bounds.\n      if (!this._isValidIndex(index) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error('cdkStepper: Cannot assign out-of-bounds value to `selectedIndex`.');\n      }\n      this.selected?._markAsInteracted();\n      if (this._selectedIndex !== index && !this._anyControlsInvalidOrPending(index) && (index >= this._selectedIndex || this.steps.toArray()[index].editable)) {\n        this._updateSelectedItemIndex(index);\n      }\n    } else {\n      this._selectedIndex = index;\n    }\n  }\n  /** The step that is selected. */\n  get selected() {\n    return this.steps ? this.steps.toArray()[this.selectedIndex] : undefined;\n  }\n  set selected(step) {\n    this.selectedIndex = step && this.steps ? this.steps.toArray().indexOf(step) : -1;\n  }\n  /** Orientation of the stepper. */\n  get orientation() {\n    return this._orientation;\n  }\n  set orientation(value) {\n    // This is a protected method so that `MatStepper` can hook into it.\n    this._orientation = value;\n    if (this._keyManager) {\n      this._keyManager.withVerticalOrientation(value === 'vertical');\n    }\n  }\n  constructor(_dir, _changeDetectorRef, _elementRef) {\n    this._dir = _dir;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._elementRef = _elementRef;\n    /** Emits when the component is destroyed. */\n    this._destroyed = new Subject();\n    /** Steps that belong to the current stepper, excluding ones from nested steppers. */\n    this.steps = new QueryList();\n    /** List of step headers sorted based on their DOM order. */\n    this._sortedHeaders = new QueryList();\n    /** Whether the validity of previous steps should be checked or not. */\n    this.linear = false;\n    this._selectedIndex = 0;\n    /** Event emitted when the selected step has changed. */\n    this.selectionChange = new EventEmitter();\n    /** Output to support two-way binding on `[(selectedIndex)]` */\n    this.selectedIndexChange = new EventEmitter();\n    this._orientation = 'horizontal';\n    this._groupId = nextId++;\n  }\n  ngAfterContentInit() {\n    this._steps.changes.pipe(startWith(this._steps), takeUntil(this._destroyed)).subscribe(steps => {\n      this.steps.reset(steps.filter(step => step._stepper === this));\n      this.steps.notifyOnChanges();\n    });\n  }\n  ngAfterViewInit() {\n    // If the step headers are defined outside of the `ngFor` that renders the steps, like in the\n    // Material stepper, they won't appear in the `QueryList` in the same order as they're\n    // rendered in the DOM which will lead to incorrect keyboard navigation. We need to sort\n    // them manually to ensure that they're correct. Alternatively, we can change the Material\n    // template to inline the headers in the `ngFor`, but that'll result in a lot of\n    // code duplication. See #23539.\n    this._stepHeader.changes.pipe(startWith(this._stepHeader), takeUntil(this._destroyed)).subscribe(headers => {\n      this._sortedHeaders.reset(headers.toArray().sort((a, b) => {\n        const documentPosition = a._elementRef.nativeElement.compareDocumentPosition(b._elementRef.nativeElement);\n        // `compareDocumentPosition` returns a bitmask so we have to use a bitwise operator.\n        // https://developer.mozilla.org/en-US/docs/Web/API/Node/compareDocumentPosition\n        // tslint:disable-next-line:no-bitwise\n        return documentPosition & Node.DOCUMENT_POSITION_FOLLOWING ? -1 : 1;\n      }));\n      this._sortedHeaders.notifyOnChanges();\n    });\n    // Note that while the step headers are content children by default, any components that\n    // extend this one might have them as view children. We initialize the keyboard handling in\n    // AfterViewInit so we're guaranteed for both view and content children to be defined.\n    this._keyManager = new FocusKeyManager(this._sortedHeaders).withWrap().withHomeAndEnd().withVerticalOrientation(this._orientation === 'vertical');\n    (this._dir ? this._dir.change : of()).pipe(startWith(this._layoutDirection()), takeUntil(this._destroyed)).subscribe(direction => this._keyManager.withHorizontalOrientation(direction));\n    this._keyManager.updateActiveItem(this._selectedIndex);\n    // No need to `takeUntil` here, because we're the ones destroying `steps`.\n    this.steps.changes.subscribe(() => {\n      if (!this.selected) {\n        this._selectedIndex = Math.max(this._selectedIndex - 1, 0);\n      }\n    });\n    // The logic which asserts that the selected index is within bounds doesn't run before the\n    // steps are initialized, because we don't how many steps there are yet so we may have an\n    // invalid index on init. If that's the case, auto-correct to the default so we don't throw.\n    if (!this._isValidIndex(this._selectedIndex)) {\n      this._selectedIndex = 0;\n    }\n  }\n  ngOnDestroy() {\n    this._keyManager?.destroy();\n    this.steps.destroy();\n    this._sortedHeaders.destroy();\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n  /** Selects and focuses the next step in list. */\n  next() {\n    this.selectedIndex = Math.min(this._selectedIndex + 1, this.steps.length - 1);\n  }\n  /** Selects and focuses the previous step in list. */\n  previous() {\n    this.selectedIndex = Math.max(this._selectedIndex - 1, 0);\n  }\n  /** Resets the stepper to its initial state. Note that this includes clearing form data. */\n  reset() {\n    this._updateSelectedItemIndex(0);\n    this.steps.forEach(step => step.reset());\n    this._stateChanged();\n  }\n  /** Returns a unique id for each step label element. */\n  _getStepLabelId(i) {\n    return `cdk-step-label-${this._groupId}-${i}`;\n  }\n  /** Returns unique id for each step content element. */\n  _getStepContentId(i) {\n    return `cdk-step-content-${this._groupId}-${i}`;\n  }\n  /** Marks the component to be change detected. */\n  _stateChanged() {\n    this._changeDetectorRef.markForCheck();\n  }\n  /** Returns position state of the step with the given index. */\n  _getAnimationDirection(index) {\n    const position = index - this._selectedIndex;\n    if (position < 0) {\n      return this._layoutDirection() === 'rtl' ? 'next' : 'previous';\n    } else if (position > 0) {\n      return this._layoutDirection() === 'rtl' ? 'previous' : 'next';\n    }\n    return 'current';\n  }\n  /** Returns the type of icon to be displayed. */\n  _getIndicatorType(index, state = STEP_STATE.NUMBER) {\n    const step = this.steps.toArray()[index];\n    const isCurrentStep = this._isCurrentStep(index);\n    return step._displayDefaultIndicatorType ? this._getDefaultIndicatorLogic(step, isCurrentStep) : this._getGuidelineLogic(step, isCurrentStep, state);\n  }\n  _getDefaultIndicatorLogic(step, isCurrentStep) {\n    if (step._showError() && step.hasError && !isCurrentStep) {\n      return STEP_STATE.ERROR;\n    } else if (!step.completed || isCurrentStep) {\n      return STEP_STATE.NUMBER;\n    } else {\n      return step.editable ? STEP_STATE.EDIT : STEP_STATE.DONE;\n    }\n  }\n  _getGuidelineLogic(step, isCurrentStep, state = STEP_STATE.NUMBER) {\n    if (step._showError() && step.hasError && !isCurrentStep) {\n      return STEP_STATE.ERROR;\n    } else if (step.completed && !isCurrentStep) {\n      return STEP_STATE.DONE;\n    } else if (step.completed && isCurrentStep) {\n      return state;\n    } else if (step.editable && isCurrentStep) {\n      return STEP_STATE.EDIT;\n    } else {\n      return state;\n    }\n  }\n  _isCurrentStep(index) {\n    return this._selectedIndex === index;\n  }\n  /** Returns the index of the currently-focused step header. */\n  _getFocusIndex() {\n    return this._keyManager ? this._keyManager.activeItemIndex : this._selectedIndex;\n  }\n  _updateSelectedItemIndex(newIndex) {\n    const stepsArray = this.steps.toArray();\n    this.selectionChange.emit({\n      selectedIndex: newIndex,\n      previouslySelectedIndex: this._selectedIndex,\n      selectedStep: stepsArray[newIndex],\n      previouslySelectedStep: stepsArray[this._selectedIndex]\n    });\n    // If focus is inside the stepper, move it to the next header, otherwise it may become\n    // lost when the active step content is hidden. We can't be more granular with the check\n    // (e.g. checking whether focus is inside the active step), because we don't have a\n    // reference to the elements that are rendering out the content.\n    this._containsFocus() ? this._keyManager.setActiveItem(newIndex) : this._keyManager.updateActiveItem(newIndex);\n    this._selectedIndex = newIndex;\n    this.selectedIndexChange.emit(this._selectedIndex);\n    this._stateChanged();\n  }\n  _onKeydown(event) {\n    const hasModifier = hasModifierKey(event);\n    const keyCode = event.keyCode;\n    const manager = this._keyManager;\n    if (manager.activeItemIndex != null && !hasModifier && (keyCode === SPACE || keyCode === ENTER)) {\n      this.selectedIndex = manager.activeItemIndex;\n      event.preventDefault();\n    } else {\n      manager.setFocusOrigin('keyboard').onKeydown(event);\n    }\n  }\n  _anyControlsInvalidOrPending(index) {\n    if (this.linear && index >= 0) {\n      return this.steps.toArray().slice(0, index).some(step => {\n        const control = step.stepControl;\n        const isIncomplete = control ? control.invalid || control.pending || !step.interacted : !step.completed;\n        return isIncomplete && !step.optional && !step._completedOverride;\n      });\n    }\n    return false;\n  }\n  _layoutDirection() {\n    return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n  }\n  /** Checks whether the stepper contains the focused element. */\n  _containsFocus() {\n    const stepperElement = this._elementRef.nativeElement;\n    const focusedElement = _getFocusedElementPierceShadowDom();\n    return stepperElement === focusedElement || stepperElement.contains(focusedElement);\n  }\n  /** Checks whether the passed-in index is a valid step index. */\n  _isValidIndex(index) {\n    return index > -1 && (!this.steps || index < this.steps.length);\n  }\n  static {\n    this.ɵfac = function CdkStepper_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkStepper)(i0.ɵɵdirectiveInject(i1.Directionality, 8), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkStepper,\n      selectors: [[\"\", \"cdkStepper\", \"\"]],\n      contentQueries: function CdkStepper_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, CdkStep, 5);\n          i0.ɵɵcontentQuery(dirIndex, CdkStepHeader, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._steps = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._stepHeader = _t);\n        }\n      },\n      inputs: {\n        linear: [2, \"linear\", \"linear\", booleanAttribute],\n        selectedIndex: [2, \"selectedIndex\", \"selectedIndex\", numberAttribute],\n        selected: \"selected\",\n        orientation: \"orientation\"\n      },\n      outputs: {\n        selectionChange: \"selectionChange\",\n        selectedIndexChange: \"selectedIndexChange\"\n      },\n      exportAs: [\"cdkStepper\"],\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkStepper, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkStepper]',\n      exportAs: 'cdkStepper',\n      standalone: true\n    }]\n  }], () => [{\n    type: i1.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i0.ElementRef\n  }], {\n    _steps: [{\n      type: ContentChildren,\n      args: [CdkStep, {\n        descendants: true\n      }]\n    }],\n    _stepHeader: [{\n      type: ContentChildren,\n      args: [CdkStepHeader, {\n        descendants: true\n      }]\n    }],\n    linear: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    selectedIndex: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    selected: [{\n      type: Input\n    }],\n    selectionChange: [{\n      type: Output\n    }],\n    selectedIndexChange: [{\n      type: Output\n    }],\n    orientation: [{\n      type: Input\n    }]\n  });\n})();\n\n/** Button that moves to the next step in a stepper workflow. */\nclass CdkStepperNext {\n  constructor(_stepper) {\n    this._stepper = _stepper;\n    /** Type of the next button. Defaults to \"submit\" if not specified. */\n    this.type = 'submit';\n  }\n  static {\n    this.ɵfac = function CdkStepperNext_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkStepperNext)(i0.ɵɵdirectiveInject(CdkStepper));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkStepperNext,\n      selectors: [[\"button\", \"cdkStepperNext\", \"\"]],\n      hostVars: 1,\n      hostBindings: function CdkStepperNext_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function CdkStepperNext_click_HostBindingHandler() {\n            return ctx._stepper.next();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵhostProperty(\"type\", ctx.type);\n        }\n      },\n      inputs: {\n        type: \"type\"\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkStepperNext, [{\n    type: Directive,\n    args: [{\n      selector: 'button[cdkStepperNext]',\n      host: {\n        '[type]': 'type',\n        '(click)': '_stepper.next()'\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: CdkStepper\n  }], {\n    type: [{\n      type: Input\n    }]\n  });\n})();\n/** Button that moves to the previous step in a stepper workflow. */\nclass CdkStepperPrevious {\n  constructor(_stepper) {\n    this._stepper = _stepper;\n    /** Type of the previous button. Defaults to \"button\" if not specified. */\n    this.type = 'button';\n  }\n  static {\n    this.ɵfac = function CdkStepperPrevious_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkStepperPrevious)(i0.ɵɵdirectiveInject(CdkStepper));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkStepperPrevious,\n      selectors: [[\"button\", \"cdkStepperPrevious\", \"\"]],\n      hostVars: 1,\n      hostBindings: function CdkStepperPrevious_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function CdkStepperPrevious_click_HostBindingHandler() {\n            return ctx._stepper.previous();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵhostProperty(\"type\", ctx.type);\n        }\n      },\n      inputs: {\n        type: \"type\"\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkStepperPrevious, [{\n    type: Directive,\n    args: [{\n      selector: 'button[cdkStepperPrevious]',\n      host: {\n        '[type]': 'type',\n        '(click)': '_stepper.previous()'\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: CdkStepper\n  }], {\n    type: [{\n      type: Input\n    }]\n  });\n})();\nclass CdkStepperModule {\n  static {\n    this.ɵfac = function CdkStepperModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkStepperModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: CdkStepperModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [BidiModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkStepperModule, [{\n    type: NgModule,\n    args: [{\n      imports: [BidiModule, CdkStep, CdkStepper, CdkStepHeader, CdkStepLabel, CdkStepperNext, CdkStepperPrevious],\n      exports: [CdkStep, CdkStepper, CdkStepHeader, CdkStepLabel, CdkStepperNext, CdkStepperPrevious]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CdkStep, CdkStepHeader, CdkStepLabel, CdkStepper, CdkStepperModule, CdkStepperNext, CdkStepperPrevious, STEPPER_GLOBAL_OPTIONS, STEP_STATE, StepperSelectionEvent };", "map": {"version": 3, "names": ["FocusKeyManager", "i1", "BidiModule", "hasModifierKey", "SPACE", "ENTER", "i0", "Directive", "InjectionToken", "EventEmitter", "forwardRef", "booleanAttribute", "TemplateRef", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Inject", "Optional", "ContentChild", "ContentChildren", "ViewChild", "Input", "Output", "QueryList", "numberAttribute", "NgModule", "ControlContainer", "_getFocusedElementPierceShadowDom", "Subject", "of", "startWith", "takeUntil", "_c0", "CdkStep_ng_template_0_Template", "rf", "ctx", "ɵɵprojection", "CdkStepHeader", "constructor", "_elementRef", "focus", "nativeElement", "ɵfac", "CdkStepHeader_Factory", "__ngFactoryType__", "ɵɵdirectiveInject", "ElementRef", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "hostAttrs", "standalone", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "host", "CdkStepLabel", "template", "CdkStepLabel_Factory", "nextId", "StepperSelectionEvent", "STEP_STATE", "NUMBER", "EDIT", "DONE", "ERROR", "STEPPER_GLOBAL_OPTIONS", "CdkStep", "completed", "_completedOverride", "_getDefaultCompleted", "value", "stepControl", "valid", "interacted", "<PERSON><PERSON><PERSON><PERSON>", "_customError", "_getDefaultError", "invalid", "_stepper", "stepperOptions", "interactedStream", "editable", "optional", "_stepperOptions", "_displayDefaultIndicatorType", "displayDefaultIndicatorType", "select", "selected", "reset", "_childForms", "for<PERSON>ach", "form", "resetForm", "ngOnChanges", "_stateChanged", "_markAsInteracted", "emit", "_showError", "showError", "CdkStep_Factory", "CdkStepper", "ɵcmp", "ɵɵdefineComponent", "contentQueries", "CdkStep_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "<PERSON><PERSON><PERSON><PERSON>", "first", "viewQuery", "CdkStep_Query", "ɵɵviewQuery", "content", "inputs", "label", "errorMessage", "aria<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state", "outputs", "exportAs", "features", "ɵɵInputTransformsFeature", "ɵɵNgOnChangesFeature", "ɵɵStandaloneFeature", "ngContentSelectors", "decls", "vars", "CdkStep_Template", "ɵɵprojectionDef", "ɵɵtemplate", "encapsulation", "changeDetection", "None", "OnPush", "decorators", "undefined", "descendants", "static", "transform", "selectedIndex", "_selectedIndex", "index", "steps", "_steps", "_isValidIndex", "Error", "_anyControlsInvalidOrPending", "toArray", "_updateSelectedItemIndex", "step", "indexOf", "orientation", "_orientation", "_keyManager", "withVerticalOrientation", "_dir", "_changeDetectorRef", "_destroyed", "_sortedHeaders", "linear", "selectionChange", "selectedIndexChange", "_groupId", "ngAfterContentInit", "changes", "pipe", "subscribe", "filter", "notifyOn<PERSON><PERSON>es", "ngAfterViewInit", "_step<PERSON><PERSON>er", "headers", "sort", "a", "b", "documentPosition", "compareDocumentPosition", "Node", "DOCUMENT_POSITION_FOLLOWING", "withWrap", "withHomeAndEnd", "change", "_layoutDirection", "direction", "withHorizontalOrientation", "updateActiveItem", "Math", "max", "ngOnDestroy", "destroy", "next", "complete", "min", "length", "previous", "_getStepLabelId", "i", "_getStepContentId", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_getAnimationDirection", "position", "_getIndicatorType", "isCurrentStep", "_isCurrentStep", "_getDefaultIndicatorLogic", "_get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_getFocusIndex", "activeItemIndex", "newIndex", "stepsArray", "previouslySelectedIndex", "selectedStep", "previouslySelectedStep", "_containsFocus", "setActiveItem", "_onKeydown", "event", "hasModifier", "keyCode", "manager", "preventDefault", "setFocusOrigin", "onKeydown", "slice", "some", "control", "isIncomplete", "pending", "stepper<PERSON><PERSON>", "focusedElement", "contains", "CdkStepper_Factory", "Directionality", "ChangeDetectorRef", "CdkStepper_ContentQueries", "CdkStepperNext", "CdkStepperNext_Factory", "hostVars", "hostBindings", "CdkStepperNext_HostBindings", "ɵɵlistener", "CdkStepperNext_click_HostBindingHandler", "ɵɵhostProperty", "CdkStepperPrevious", "CdkStepperPrevious_Factory", "CdkStepperPrevious_HostBindings", "CdkStepperPrevious_click_HostBindingHandler", "CdkStepperModule", "CdkStepperModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/node_modules/@angular/cdk/fesm2022/stepper.mjs"], "sourcesContent": ["import { FocusKeyManager } from '@angular/cdk/a11y';\nimport * as i1 from '@angular/cdk/bidi';\nimport { BidiModule } from '@angular/cdk/bidi';\nimport { hasModifierKey, SPACE, ENTER } from '@angular/cdk/keycodes';\nimport * as i0 from '@angular/core';\nimport { Directive, InjectionToken, EventEmitter, forwardRef, booleanAttribute, TemplateRef, Component, ViewEncapsulation, ChangeDetectionStrategy, Inject, Optional, ContentChild, ContentChildren, ViewChild, Input, Output, QueryList, numberAttribute, NgModule } from '@angular/core';\nimport { ControlContainer } from '@angular/forms';\nimport { _getFocusedElementPierceShadowDom } from '@angular/cdk/platform';\nimport { Subject, of } from 'rxjs';\nimport { startWith, takeUntil } from 'rxjs/operators';\n\nclass CdkStepHeader {\n    constructor(_elementRef) {\n        this._elementRef = _elementRef;\n    }\n    /** Focuses the step header. */\n    focus() {\n        this._elementRef.nativeElement.focus();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: CdkStepHeader, deps: [{ token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"18.2.0-next.2\", type: CdkStepHeader, isStandalone: true, selector: \"[cdkStepHeader]\", host: { attributes: { \"role\": \"tab\" } }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: CdkStepHeader, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkStepHeader]',\n                    host: {\n                        'role': 'tab',\n                    },\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }] });\n\nclass CdkStepLabel {\n    constructor(/** @docs-private */ template) {\n        this.template = template;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: CdkStepLabel, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"18.2.0-next.2\", type: CdkStepLabel, isStandalone: true, selector: \"[cdkStepLabel]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: CdkStepLabel, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkStepLabel]',\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.TemplateRef }] });\n\n/** Used to generate unique ID for each stepper component. */\nlet nextId = 0;\n/** Change event emitted on selection changes. */\nclass StepperSelectionEvent {\n}\n/** Enum to represent the different states of the steps. */\nconst STEP_STATE = {\n    NUMBER: 'number',\n    EDIT: 'edit',\n    DONE: 'done',\n    ERROR: 'error',\n};\n/** InjectionToken that can be used to specify the global stepper options. */\nconst STEPPER_GLOBAL_OPTIONS = new InjectionToken('STEPPER_GLOBAL_OPTIONS');\nclass CdkStep {\n    /** Whether step is marked as completed. */\n    get completed() {\n        return this._completedOverride == null ? this._getDefaultCompleted() : this._completedOverride;\n    }\n    set completed(value) {\n        this._completedOverride = value;\n    }\n    _getDefaultCompleted() {\n        return this.stepControl ? this.stepControl.valid && this.interacted : this.interacted;\n    }\n    /** Whether step has an error. */\n    get hasError() {\n        return this._customError == null ? this._getDefaultError() : this._customError;\n    }\n    set hasError(value) {\n        this._customError = value;\n    }\n    _getDefaultError() {\n        return this.stepControl && this.stepControl.invalid && this.interacted;\n    }\n    constructor(_stepper, stepperOptions) {\n        this._stepper = _stepper;\n        /** Whether user has attempted to move away from the step. */\n        this.interacted = false;\n        /** Emits when the user has attempted to move away from the step. */\n        this.interactedStream = new EventEmitter();\n        /** Whether the user can return to this step once it has been marked as completed. */\n        this.editable = true;\n        /** Whether the completion of step is optional. */\n        this.optional = false;\n        this._completedOverride = null;\n        this._customError = null;\n        this._stepperOptions = stepperOptions ? stepperOptions : {};\n        this._displayDefaultIndicatorType = this._stepperOptions.displayDefaultIndicatorType !== false;\n    }\n    /** Selects this step component. */\n    select() {\n        this._stepper.selected = this;\n    }\n    /** Resets the step to its initial state. Note that this includes resetting form data. */\n    reset() {\n        this.interacted = false;\n        if (this._completedOverride != null) {\n            this._completedOverride = false;\n        }\n        if (this._customError != null) {\n            this._customError = false;\n        }\n        if (this.stepControl) {\n            // Reset the forms since the default error state matchers will show errors on submit and we\n            // want the form to be back to its initial state (see #29781). Submitted state is on the\n            // individual directives, rather than the control, so we need to reset them ourselves.\n            this._childForms?.forEach(form => form.resetForm?.());\n            this.stepControl.reset();\n        }\n    }\n    ngOnChanges() {\n        // Since basically all inputs of the MatStep get proxied through the view down to the\n        // underlying MatStepHeader, we have to make sure that change detection runs correctly.\n        this._stepper._stateChanged();\n    }\n    _markAsInteracted() {\n        if (!this.interacted) {\n            this.interacted = true;\n            this.interactedStream.emit(this);\n        }\n    }\n    /** Determines whether the error state can be shown. */\n    _showError() {\n        // We want to show the error state either if the user opted into/out of it using the\n        // global options, or if they've explicitly set it through the `hasError` input.\n        return this._stepperOptions.showError ?? this._customError != null;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: CdkStep, deps: [{ token: forwardRef(() => CdkStepper) }, { token: STEPPER_GLOBAL_OPTIONS, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"18.2.0-next.2\", type: CdkStep, isStandalone: true, selector: \"cdk-step\", inputs: { stepControl: \"stepControl\", label: \"label\", errorMessage: \"errorMessage\", ariaLabel: [\"aria-label\", \"ariaLabel\"], ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"], state: \"state\", editable: [\"editable\", \"editable\", booleanAttribute], optional: [\"optional\", \"optional\", booleanAttribute], completed: [\"completed\", \"completed\", booleanAttribute], hasError: [\"hasError\", \"hasError\", booleanAttribute] }, outputs: { interactedStream: \"interacted\" }, queries: [{ propertyName: \"stepLabel\", first: true, predicate: CdkStepLabel, descendants: true }, { propertyName: \"_childForms\", predicate: \n                // Note: we look for `ControlContainer` here, because both `NgForm` and `FormGroupDirective`\n                // provides themselves as such, but we don't want to have a concrete reference to both of\n                // the directives. The type is marked as `Partial` in case we run into a class that provides\n                // itself as `ControlContainer` but doesn't have the same interface as the directives.\n                ControlContainer, descendants: true }], viewQueries: [{ propertyName: \"content\", first: true, predicate: TemplateRef, descendants: true, static: true }], exportAs: [\"cdkStep\"], usesOnChanges: true, ngImport: i0, template: '<ng-template><ng-content/></ng-template>', isInline: true, changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: CdkStep, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'cdk-step',\n                    exportAs: 'cdkStep',\n                    template: '<ng-template><ng-content/></ng-template>',\n                    encapsulation: ViewEncapsulation.None,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: CdkStepper, decorators: [{\n                    type: Inject,\n                    args: [forwardRef(() => CdkStepper)]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [STEPPER_GLOBAL_OPTIONS]\n                }] }], propDecorators: { stepLabel: [{\n                type: ContentChild,\n                args: [CdkStepLabel]\n            }], _childForms: [{\n                type: ContentChildren,\n                args: [\n                    // Note: we look for `ControlContainer` here, because both `NgForm` and `FormGroupDirective`\n                    // provides themselves as such, but we don't want to have a concrete reference to both of\n                    // the directives. The type is marked as `Partial` in case we run into a class that provides\n                    // itself as `ControlContainer` but doesn't have the same interface as the directives.\n                    ControlContainer,\n                    {\n                        descendants: true,\n                    }]\n            }], content: [{\n                type: ViewChild,\n                args: [TemplateRef, { static: true }]\n            }], stepControl: [{\n                type: Input\n            }], interactedStream: [{\n                type: Output,\n                args: ['interacted']\n            }], label: [{\n                type: Input\n            }], errorMessage: [{\n                type: Input\n            }], ariaLabel: [{\n                type: Input,\n                args: ['aria-label']\n            }], ariaLabelledby: [{\n                type: Input,\n                args: ['aria-labelledby']\n            }], state: [{\n                type: Input\n            }], editable: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], optional: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], completed: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], hasError: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }] } });\nclass CdkStepper {\n    /** The index of the selected step. */\n    get selectedIndex() {\n        return this._selectedIndex;\n    }\n    set selectedIndex(index) {\n        if (this.steps && this._steps) {\n            // Ensure that the index can't be out of bounds.\n            if (!this._isValidIndex(index) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throw Error('cdkStepper: Cannot assign out-of-bounds value to `selectedIndex`.');\n            }\n            this.selected?._markAsInteracted();\n            if (this._selectedIndex !== index &&\n                !this._anyControlsInvalidOrPending(index) &&\n                (index >= this._selectedIndex || this.steps.toArray()[index].editable)) {\n                this._updateSelectedItemIndex(index);\n            }\n        }\n        else {\n            this._selectedIndex = index;\n        }\n    }\n    /** The step that is selected. */\n    get selected() {\n        return this.steps ? this.steps.toArray()[this.selectedIndex] : undefined;\n    }\n    set selected(step) {\n        this.selectedIndex = step && this.steps ? this.steps.toArray().indexOf(step) : -1;\n    }\n    /** Orientation of the stepper. */\n    get orientation() {\n        return this._orientation;\n    }\n    set orientation(value) {\n        // This is a protected method so that `MatStepper` can hook into it.\n        this._orientation = value;\n        if (this._keyManager) {\n            this._keyManager.withVerticalOrientation(value === 'vertical');\n        }\n    }\n    constructor(_dir, _changeDetectorRef, _elementRef) {\n        this._dir = _dir;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._elementRef = _elementRef;\n        /** Emits when the component is destroyed. */\n        this._destroyed = new Subject();\n        /** Steps that belong to the current stepper, excluding ones from nested steppers. */\n        this.steps = new QueryList();\n        /** List of step headers sorted based on their DOM order. */\n        this._sortedHeaders = new QueryList();\n        /** Whether the validity of previous steps should be checked or not. */\n        this.linear = false;\n        this._selectedIndex = 0;\n        /** Event emitted when the selected step has changed. */\n        this.selectionChange = new EventEmitter();\n        /** Output to support two-way binding on `[(selectedIndex)]` */\n        this.selectedIndexChange = new EventEmitter();\n        this._orientation = 'horizontal';\n        this._groupId = nextId++;\n    }\n    ngAfterContentInit() {\n        this._steps.changes\n            .pipe(startWith(this._steps), takeUntil(this._destroyed))\n            .subscribe((steps) => {\n            this.steps.reset(steps.filter(step => step._stepper === this));\n            this.steps.notifyOnChanges();\n        });\n    }\n    ngAfterViewInit() {\n        // If the step headers are defined outside of the `ngFor` that renders the steps, like in the\n        // Material stepper, they won't appear in the `QueryList` in the same order as they're\n        // rendered in the DOM which will lead to incorrect keyboard navigation. We need to sort\n        // them manually to ensure that they're correct. Alternatively, we can change the Material\n        // template to inline the headers in the `ngFor`, but that'll result in a lot of\n        // code duplication. See #23539.\n        this._stepHeader.changes\n            .pipe(startWith(this._stepHeader), takeUntil(this._destroyed))\n            .subscribe((headers) => {\n            this._sortedHeaders.reset(headers.toArray().sort((a, b) => {\n                const documentPosition = a._elementRef.nativeElement.compareDocumentPosition(b._elementRef.nativeElement);\n                // `compareDocumentPosition` returns a bitmask so we have to use a bitwise operator.\n                // https://developer.mozilla.org/en-US/docs/Web/API/Node/compareDocumentPosition\n                // tslint:disable-next-line:no-bitwise\n                return documentPosition & Node.DOCUMENT_POSITION_FOLLOWING ? -1 : 1;\n            }));\n            this._sortedHeaders.notifyOnChanges();\n        });\n        // Note that while the step headers are content children by default, any components that\n        // extend this one might have them as view children. We initialize the keyboard handling in\n        // AfterViewInit so we're guaranteed for both view and content children to be defined.\n        this._keyManager = new FocusKeyManager(this._sortedHeaders)\n            .withWrap()\n            .withHomeAndEnd()\n            .withVerticalOrientation(this._orientation === 'vertical');\n        (this._dir ? this._dir.change : of())\n            .pipe(startWith(this._layoutDirection()), takeUntil(this._destroyed))\n            .subscribe(direction => this._keyManager.withHorizontalOrientation(direction));\n        this._keyManager.updateActiveItem(this._selectedIndex);\n        // No need to `takeUntil` here, because we're the ones destroying `steps`.\n        this.steps.changes.subscribe(() => {\n            if (!this.selected) {\n                this._selectedIndex = Math.max(this._selectedIndex - 1, 0);\n            }\n        });\n        // The logic which asserts that the selected index is within bounds doesn't run before the\n        // steps are initialized, because we don't how many steps there are yet so we may have an\n        // invalid index on init. If that's the case, auto-correct to the default so we don't throw.\n        if (!this._isValidIndex(this._selectedIndex)) {\n            this._selectedIndex = 0;\n        }\n    }\n    ngOnDestroy() {\n        this._keyManager?.destroy();\n        this.steps.destroy();\n        this._sortedHeaders.destroy();\n        this._destroyed.next();\n        this._destroyed.complete();\n    }\n    /** Selects and focuses the next step in list. */\n    next() {\n        this.selectedIndex = Math.min(this._selectedIndex + 1, this.steps.length - 1);\n    }\n    /** Selects and focuses the previous step in list. */\n    previous() {\n        this.selectedIndex = Math.max(this._selectedIndex - 1, 0);\n    }\n    /** Resets the stepper to its initial state. Note that this includes clearing form data. */\n    reset() {\n        this._updateSelectedItemIndex(0);\n        this.steps.forEach(step => step.reset());\n        this._stateChanged();\n    }\n    /** Returns a unique id for each step label element. */\n    _getStepLabelId(i) {\n        return `cdk-step-label-${this._groupId}-${i}`;\n    }\n    /** Returns unique id for each step content element. */\n    _getStepContentId(i) {\n        return `cdk-step-content-${this._groupId}-${i}`;\n    }\n    /** Marks the component to be change detected. */\n    _stateChanged() {\n        this._changeDetectorRef.markForCheck();\n    }\n    /** Returns position state of the step with the given index. */\n    _getAnimationDirection(index) {\n        const position = index - this._selectedIndex;\n        if (position < 0) {\n            return this._layoutDirection() === 'rtl' ? 'next' : 'previous';\n        }\n        else if (position > 0) {\n            return this._layoutDirection() === 'rtl' ? 'previous' : 'next';\n        }\n        return 'current';\n    }\n    /** Returns the type of icon to be displayed. */\n    _getIndicatorType(index, state = STEP_STATE.NUMBER) {\n        const step = this.steps.toArray()[index];\n        const isCurrentStep = this._isCurrentStep(index);\n        return step._displayDefaultIndicatorType\n            ? this._getDefaultIndicatorLogic(step, isCurrentStep)\n            : this._getGuidelineLogic(step, isCurrentStep, state);\n    }\n    _getDefaultIndicatorLogic(step, isCurrentStep) {\n        if (step._showError() && step.hasError && !isCurrentStep) {\n            return STEP_STATE.ERROR;\n        }\n        else if (!step.completed || isCurrentStep) {\n            return STEP_STATE.NUMBER;\n        }\n        else {\n            return step.editable ? STEP_STATE.EDIT : STEP_STATE.DONE;\n        }\n    }\n    _getGuidelineLogic(step, isCurrentStep, state = STEP_STATE.NUMBER) {\n        if (step._showError() && step.hasError && !isCurrentStep) {\n            return STEP_STATE.ERROR;\n        }\n        else if (step.completed && !isCurrentStep) {\n            return STEP_STATE.DONE;\n        }\n        else if (step.completed && isCurrentStep) {\n            return state;\n        }\n        else if (step.editable && isCurrentStep) {\n            return STEP_STATE.EDIT;\n        }\n        else {\n            return state;\n        }\n    }\n    _isCurrentStep(index) {\n        return this._selectedIndex === index;\n    }\n    /** Returns the index of the currently-focused step header. */\n    _getFocusIndex() {\n        return this._keyManager ? this._keyManager.activeItemIndex : this._selectedIndex;\n    }\n    _updateSelectedItemIndex(newIndex) {\n        const stepsArray = this.steps.toArray();\n        this.selectionChange.emit({\n            selectedIndex: newIndex,\n            previouslySelectedIndex: this._selectedIndex,\n            selectedStep: stepsArray[newIndex],\n            previouslySelectedStep: stepsArray[this._selectedIndex],\n        });\n        // If focus is inside the stepper, move it to the next header, otherwise it may become\n        // lost when the active step content is hidden. We can't be more granular with the check\n        // (e.g. checking whether focus is inside the active step), because we don't have a\n        // reference to the elements that are rendering out the content.\n        this._containsFocus()\n            ? this._keyManager.setActiveItem(newIndex)\n            : this._keyManager.updateActiveItem(newIndex);\n        this._selectedIndex = newIndex;\n        this.selectedIndexChange.emit(this._selectedIndex);\n        this._stateChanged();\n    }\n    _onKeydown(event) {\n        const hasModifier = hasModifierKey(event);\n        const keyCode = event.keyCode;\n        const manager = this._keyManager;\n        if (manager.activeItemIndex != null &&\n            !hasModifier &&\n            (keyCode === SPACE || keyCode === ENTER)) {\n            this.selectedIndex = manager.activeItemIndex;\n            event.preventDefault();\n        }\n        else {\n            manager.setFocusOrigin('keyboard').onKeydown(event);\n        }\n    }\n    _anyControlsInvalidOrPending(index) {\n        if (this.linear && index >= 0) {\n            return this.steps\n                .toArray()\n                .slice(0, index)\n                .some(step => {\n                const control = step.stepControl;\n                const isIncomplete = control\n                    ? control.invalid || control.pending || !step.interacted\n                    : !step.completed;\n                return isIncomplete && !step.optional && !step._completedOverride;\n            });\n        }\n        return false;\n    }\n    _layoutDirection() {\n        return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n    }\n    /** Checks whether the stepper contains the focused element. */\n    _containsFocus() {\n        const stepperElement = this._elementRef.nativeElement;\n        const focusedElement = _getFocusedElementPierceShadowDom();\n        return stepperElement === focusedElement || stepperElement.contains(focusedElement);\n    }\n    /** Checks whether the passed-in index is a valid step index. */\n    _isValidIndex(index) {\n        return index > -1 && (!this.steps || index < this.steps.length);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: CdkStepper, deps: [{ token: i1.Directionality, optional: true }, { token: i0.ChangeDetectorRef }, { token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"18.2.0-next.2\", type: CdkStepper, isStandalone: true, selector: \"[cdkStepper]\", inputs: { linear: [\"linear\", \"linear\", booleanAttribute], selectedIndex: [\"selectedIndex\", \"selectedIndex\", numberAttribute], selected: \"selected\", orientation: \"orientation\" }, outputs: { selectionChange: \"selectionChange\", selectedIndexChange: \"selectedIndexChange\" }, queries: [{ propertyName: \"_steps\", predicate: CdkStep, descendants: true }, { propertyName: \"_stepHeader\", predicate: CdkStepHeader, descendants: true }], exportAs: [\"cdkStepper\"], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: CdkStepper, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkStepper]',\n                    exportAs: 'cdkStepper',\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i1.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: i0.ChangeDetectorRef }, { type: i0.ElementRef }], propDecorators: { _steps: [{\n                type: ContentChildren,\n                args: [CdkStep, { descendants: true }]\n            }], _stepHeader: [{\n                type: ContentChildren,\n                args: [CdkStepHeader, { descendants: true }]\n            }], linear: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], selectedIndex: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], selected: [{\n                type: Input\n            }], selectionChange: [{\n                type: Output\n            }], selectedIndexChange: [{\n                type: Output\n            }], orientation: [{\n                type: Input\n            }] } });\n\n/** Button that moves to the next step in a stepper workflow. */\nclass CdkStepperNext {\n    constructor(_stepper) {\n        this._stepper = _stepper;\n        /** Type of the next button. Defaults to \"submit\" if not specified. */\n        this.type = 'submit';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: CdkStepperNext, deps: [{ token: CdkStepper }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"18.2.0-next.2\", type: CdkStepperNext, isStandalone: true, selector: \"button[cdkStepperNext]\", inputs: { type: \"type\" }, host: { listeners: { \"click\": \"_stepper.next()\" }, properties: { \"type\": \"type\" } }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: CdkStepperNext, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'button[cdkStepperNext]',\n                    host: {\n                        '[type]': 'type',\n                        '(click)': '_stepper.next()',\n                    },\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: CdkStepper }], propDecorators: { type: [{\n                type: Input\n            }] } });\n/** Button that moves to the previous step in a stepper workflow. */\nclass CdkStepperPrevious {\n    constructor(_stepper) {\n        this._stepper = _stepper;\n        /** Type of the previous button. Defaults to \"button\" if not specified. */\n        this.type = 'button';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: CdkStepperPrevious, deps: [{ token: CdkStepper }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"18.2.0-next.2\", type: CdkStepperPrevious, isStandalone: true, selector: \"button[cdkStepperPrevious]\", inputs: { type: \"type\" }, host: { listeners: { \"click\": \"_stepper.previous()\" }, properties: { \"type\": \"type\" } }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: CdkStepperPrevious, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'button[cdkStepperPrevious]',\n                    host: {\n                        '[type]': 'type',\n                        '(click)': '_stepper.previous()',\n                    },\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: CdkStepper }], propDecorators: { type: [{\n                type: Input\n            }] } });\n\nclass CdkStepperModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: CdkStepperModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: CdkStepperModule, imports: [BidiModule,\n            CdkStep,\n            CdkStepper,\n            CdkStepHeader,\n            CdkStepLabel,\n            CdkStepperNext,\n            CdkStepperPrevious], exports: [CdkStep, CdkStepper, CdkStepHeader, CdkStepLabel, CdkStepperNext, CdkStepperPrevious] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: CdkStepperModule, imports: [BidiModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: CdkStepperModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        BidiModule,\n                        CdkStep,\n                        CdkStepper,\n                        CdkStepHeader,\n                        CdkStepLabel,\n                        CdkStepperNext,\n                        CdkStepperPrevious,\n                    ],\n                    exports: [CdkStep, CdkStepper, CdkStepHeader, CdkStepLabel, CdkStepperNext, CdkStepperPrevious],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CdkStep, CdkStepHeader, CdkStepLabel, CdkStepper, CdkStepperModule, CdkStepperNext, CdkStepperPrevious, STEPPER_GLOBAL_OPTIONS, STEP_STATE, StepperSelectionEvent };\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,mBAAmB;AACnD,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,SAASC,UAAU,QAAQ,mBAAmB;AAC9C,SAASC,cAAc,EAAEC,KAAK,EAAEC,KAAK,QAAQ,uBAAuB;AACpE,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,cAAc,EAAEC,YAAY,EAAEC,UAAU,EAAEC,gBAAgB,EAAEC,WAAW,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,eAAe,EAAEC,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEC,eAAe,EAAEC,QAAQ,QAAQ,eAAe;AAC1R,SAASC,gBAAgB,QAAQ,gBAAgB;AACjD,SAASC,iCAAiC,QAAQ,uBAAuB;AACzE,SAASC,OAAO,EAAEC,EAAE,QAAQ,MAAM;AAClC,SAASC,SAAS,EAAEC,SAAS,QAAQ,gBAAgB;AAAC,MAAAC,GAAA;AAAA,SAAAC,+BAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAUqD5B,EAAE,CAAA8B,YAAA,EA2H2J,CAAC;EAAA;AAAA;AAnIzQ,MAAMC,aAAa,CAAC;EAChBC,WAAWA,CAACC,WAAW,EAAE;IACrB,IAAI,CAACA,WAAW,GAAGA,WAAW;EAClC;EACA;EACAC,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACD,WAAW,CAACE,aAAa,CAACD,KAAK,CAAC,CAAC;EAC1C;EACA;IAAS,IAAI,CAACE,IAAI,YAAAC,sBAAAC,iBAAA;MAAA,YAAAA,iBAAA,IAA+FP,aAAa,EAAvB/B,EAAE,CAAAuC,iBAAA,CAAuCvC,EAAE,CAACwC,UAAU;IAAA,CAA4C;EAAE;EAC3M;IAAS,IAAI,CAACC,IAAI,kBADqFzC,EAAE,CAAA0C,iBAAA;MAAAC,IAAA,EACJZ,aAAa;MAAAa,SAAA;MAAAC,SAAA,WAAiF,KAAK;MAAAC,UAAA;IAAA,EAAqB;EAAE;AACnO;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAH2G/C,EAAE,CAAAgD,iBAAA,CAGXjB,aAAa,EAAc,CAAC;IAClHY,IAAI,EAAE1C,SAAS;IACfgD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,iBAAiB;MAC3BC,IAAI,EAAE;QACF,MAAM,EAAE;MACZ,CAAC;MACDL,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEH,IAAI,EAAE3C,EAAE,CAACwC;EAAW,CAAC,CAAC;AAAA;AAE3D,MAAMY,YAAY,CAAC;EACfpB,WAAWA,CAAC,oBAAqBqB,QAAQ,EAAE;IACvC,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACA;IAAS,IAAI,CAACjB,IAAI,YAAAkB,qBAAAhB,iBAAA;MAAA,YAAAA,iBAAA,IAA+Fc,YAAY,EAlBtBpD,EAAE,CAAAuC,iBAAA,CAkBsCvC,EAAE,CAACM,WAAW;IAAA,CAA4C;EAAE;EAC3M;IAAS,IAAI,CAACmC,IAAI,kBAnBqFzC,EAAE,CAAA0C,iBAAA;MAAAC,IAAA,EAmBJS,YAAY;MAAAR,SAAA;MAAAE,UAAA;IAAA,EAAiE;EAAE;AACxL;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KArB2G/C,EAAE,CAAAgD,iBAAA,CAqBXI,YAAY,EAAc,CAAC;IACjHT,IAAI,EAAE1C,SAAS;IACfgD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,gBAAgB;MAC1BJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEH,IAAI,EAAE3C,EAAE,CAACM;EAAY,CAAC,CAAC;AAAA;;AAE5D;AACA,IAAIiD,MAAM,GAAG,CAAC;AACd;AACA,MAAMC,qBAAqB,CAAC;AAE5B;AACA,MAAMC,UAAU,GAAG;EACfC,MAAM,EAAE,QAAQ;EAChBC,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE,MAAM;EACZC,KAAK,EAAE;AACX,CAAC;AACD;AACA,MAAMC,sBAAsB,GAAG,IAAI5D,cAAc,CAAC,wBAAwB,CAAC;AAC3E,MAAM6D,OAAO,CAAC;EACV;EACA,IAAIC,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACC,kBAAkB,IAAI,IAAI,GAAG,IAAI,CAACC,oBAAoB,CAAC,CAAC,GAAG,IAAI,CAACD,kBAAkB;EAClG;EACA,IAAID,SAASA,CAACG,KAAK,EAAE;IACjB,IAAI,CAACF,kBAAkB,GAAGE,KAAK;EACnC;EACAD,oBAAoBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACE,WAAW,GAAG,IAAI,CAACA,WAAW,CAACC,KAAK,IAAI,IAAI,CAACC,UAAU,GAAG,IAAI,CAACA,UAAU;EACzF;EACA;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,YAAY,IAAI,IAAI,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC,GAAG,IAAI,CAACD,YAAY;EAClF;EACA,IAAID,QAAQA,CAACJ,KAAK,EAAE;IAChB,IAAI,CAACK,YAAY,GAAGL,KAAK;EAC7B;EACAM,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAI,CAACL,WAAW,IAAI,IAAI,CAACA,WAAW,CAACM,OAAO,IAAI,IAAI,CAACJ,UAAU;EAC1E;EACAtC,WAAWA,CAAC2C,QAAQ,EAAEC,cAAc,EAAE;IAClC,IAAI,CAACD,QAAQ,GAAGA,QAAQ;IACxB;IACA,IAAI,CAACL,UAAU,GAAG,KAAK;IACvB;IACA,IAAI,CAACO,gBAAgB,GAAG,IAAI1E,YAAY,CAAC,CAAC;IAC1C;IACA,IAAI,CAAC2E,QAAQ,GAAG,IAAI;IACpB;IACA,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACd,kBAAkB,GAAG,IAAI;IAC9B,IAAI,CAACO,YAAY,GAAG,IAAI;IACxB,IAAI,CAACQ,eAAe,GAAGJ,cAAc,GAAGA,cAAc,GAAG,CAAC,CAAC;IAC3D,IAAI,CAACK,4BAA4B,GAAG,IAAI,CAACD,eAAe,CAACE,2BAA2B,KAAK,KAAK;EAClG;EACA;EACAC,MAAMA,CAAA,EAAG;IACL,IAAI,CAACR,QAAQ,CAACS,QAAQ,GAAG,IAAI;EACjC;EACA;EACAC,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACf,UAAU,GAAG,KAAK;IACvB,IAAI,IAAI,CAACL,kBAAkB,IAAI,IAAI,EAAE;MACjC,IAAI,CAACA,kBAAkB,GAAG,KAAK;IACnC;IACA,IAAI,IAAI,CAACO,YAAY,IAAI,IAAI,EAAE;MAC3B,IAAI,CAACA,YAAY,GAAG,KAAK;IAC7B;IACA,IAAI,IAAI,CAACJ,WAAW,EAAE;MAClB;MACA;MACA;MACA,IAAI,CAACkB,WAAW,EAAEC,OAAO,CAACC,IAAI,IAAIA,IAAI,CAACC,SAAS,GAAG,CAAC,CAAC;MACrD,IAAI,CAACrB,WAAW,CAACiB,KAAK,CAAC,CAAC;IAC5B;EACJ;EACAK,WAAWA,CAAA,EAAG;IACV;IACA;IACA,IAAI,CAACf,QAAQ,CAACgB,aAAa,CAAC,CAAC;EACjC;EACAC,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC,IAAI,CAACtB,UAAU,EAAE;MAClB,IAAI,CAACA,UAAU,GAAG,IAAI;MACtB,IAAI,CAACO,gBAAgB,CAACgB,IAAI,CAAC,IAAI,CAAC;IACpC;EACJ;EACA;EACAC,UAAUA,CAAA,EAAG;IACT;IACA;IACA,OAAO,IAAI,CAACd,eAAe,CAACe,SAAS,IAAI,IAAI,CAACvB,YAAY,IAAI,IAAI;EACtE;EACA;IAAS,IAAI,CAACpC,IAAI,YAAA4D,gBAAA1D,iBAAA;MAAA,YAAAA,iBAAA,IAA+FyB,OAAO,EArHjB/D,EAAE,CAAAuC,iBAAA,CAqHiCnC,UAAU,CAAC,MAAM6F,UAAU,CAAC,GArH/DjG,EAAE,CAAAuC,iBAAA,CAqH0EuB,sBAAsB;IAAA,CAA4D;EAAE;EACvQ;IAAS,IAAI,CAACoC,IAAI,kBAtHqFlG,EAAE,CAAAmG,iBAAA;MAAAxD,IAAA,EAsHJoB,OAAO;MAAAnB,SAAA;MAAAwD,cAAA,WAAAC,uBAAAzE,EAAA,EAAAC,GAAA,EAAAyE,QAAA;QAAA,IAAA1E,EAAA;UAtHL5B,EAAE,CAAAuG,cAAA,CAAAD,QAAA,EAsH2jBlD,YAAY;UAtHzkBpD,EAAE,CAAAuG,cAAA,CAAAD,QAAA;UAuH7F;UACA;UACA;UACA;UACAlF,gBAAgB;QAAA;QAAA,IAAAQ,EAAA;UAAA,IAAA4E,EAAA;UA3H2ExG,EAAE,CAAAyG,cAAA,CAAAD,EAAA,GAAFxG,EAAE,CAAA0G,WAAA,QAAA7E,GAAA,CAAA8E,SAAA,GAAAH,EAAA,CAAAI,KAAA;UAAF5G,EAAE,CAAAyG,cAAA,CAAAD,EAAA,GAAFxG,EAAE,CAAA0G,WAAA,QAAA7E,GAAA,CAAAyD,WAAA,GAAAkB,EAAA;QAAA;MAAA;MAAAK,SAAA,WAAAC,cAAAlF,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF5B,EAAE,CAAA+G,WAAA,CA2HYzG,WAAW;QAAA;QAAA,IAAAsB,EAAA;UAAA,IAAA4E,EAAA;UA3HzBxG,EAAE,CAAAyG,cAAA,CAAAD,EAAA,GAAFxG,EAAE,CAAA0G,WAAA,QAAA7E,GAAA,CAAAmF,OAAA,GAAAR,EAAA,CAAAI,KAAA;QAAA;MAAA;MAAAK,MAAA;QAAA7C,WAAA;QAAA8C,KAAA;QAAAC,YAAA;QAAAC,SAAA;QAAAC,cAAA;QAAAC,KAAA;QAAAxC,QAAA,8BAsHqRzE,gBAAgB;QAAA0E,QAAA,8BAAsC1E,gBAAgB;QAAA2D,SAAA,gCAAyC3D,gBAAgB;QAAAkE,QAAA,8BAAsClE,gBAAgB;MAAA;MAAAkH,OAAA;QAAA1C,gBAAA;MAAA;MAAA2C,QAAA;MAAA1E,UAAA;MAAA2E,QAAA,GAtH5czH,EAAE,CAAA0H,wBAAA,EAAF1H,EAAE,CAAA2H,oBAAA,EAAF3H,EAAE,CAAA4H,mBAAA;MAAAC,kBAAA,EAAAnG,GAAA;MAAAoG,KAAA;MAAAC,IAAA;MAAA1E,QAAA,WAAA2E,iBAAApG,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF5B,EAAE,CAAAiI,eAAA;UAAFjI,EAAE,CAAAkI,UAAA,IAAAvG,8BAAA,qBA2H8I,CAAC;QAAA;MAAA;MAAAwG,aAAA;MAAAC,eAAA;IAAA,EAA6I;EAAE;AAC3Y;AACA;EAAA,QAAArF,SAAA,oBAAAA,SAAA,KA7H2G/C,EAAE,CAAAgD,iBAAA,CA6HXe,OAAO,EAAc,CAAC;IAC5GpB,IAAI,EAAEpC,SAAS;IACf0C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,UAAU;MACpBsE,QAAQ,EAAE,SAAS;MACnBnE,QAAQ,EAAE,0CAA0C;MACpD8E,aAAa,EAAE3H,iBAAiB,CAAC6H,IAAI;MACrCD,eAAe,EAAE3H,uBAAuB,CAAC6H,MAAM;MAC/CxF,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEH,IAAI,EAAEsD,UAAU;IAAEsC,UAAU,EAAE,CAAC;MAChD5F,IAAI,EAAEjC,MAAM;MACZuC,IAAI,EAAE,CAAC7C,UAAU,CAAC,MAAM6F,UAAU,CAAC;IACvC,CAAC;EAAE,CAAC,EAAE;IAAEtD,IAAI,EAAE6F,SAAS;IAAED,UAAU,EAAE,CAAC;MAClC5F,IAAI,EAAEhC;IACV,CAAC,EAAE;MACCgC,IAAI,EAAEjC,MAAM;MACZuC,IAAI,EAAE,CAACa,sBAAsB;IACjC,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAE6C,SAAS,EAAE,CAAC;MACrChE,IAAI,EAAE/B,YAAY;MAClBqC,IAAI,EAAE,CAACG,YAAY;IACvB,CAAC,CAAC;IAAEkC,WAAW,EAAE,CAAC;MACd3C,IAAI,EAAE9B,eAAe;MACrBoC,IAAI,EAAE;MACF;MACA;MACA;MACA;MACA7B,gBAAgB,EAChB;QACIqH,WAAW,EAAE;MACjB,CAAC;IACT,CAAC,CAAC;IAAEzB,OAAO,EAAE,CAAC;MACVrE,IAAI,EAAE7B,SAAS;MACfmC,IAAI,EAAE,CAAC3C,WAAW,EAAE;QAAEoI,MAAM,EAAE;MAAK,CAAC;IACxC,CAAC,CAAC;IAAEtE,WAAW,EAAE,CAAC;MACdzB,IAAI,EAAE5B;IACV,CAAC,CAAC;IAAE8D,gBAAgB,EAAE,CAAC;MACnBlC,IAAI,EAAE3B,MAAM;MACZiC,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAEiE,KAAK,EAAE,CAAC;MACRvE,IAAI,EAAE5B;IACV,CAAC,CAAC;IAAEoG,YAAY,EAAE,CAAC;MACfxE,IAAI,EAAE5B;IACV,CAAC,CAAC;IAAEqG,SAAS,EAAE,CAAC;MACZzE,IAAI,EAAE5B,KAAK;MACXkC,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAEoE,cAAc,EAAE,CAAC;MACjB1E,IAAI,EAAE5B,KAAK;MACXkC,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAEqE,KAAK,EAAE,CAAC;MACR3E,IAAI,EAAE5B;IACV,CAAC,CAAC;IAAE+D,QAAQ,EAAE,CAAC;MACXnC,IAAI,EAAE5B,KAAK;MACXkC,IAAI,EAAE,CAAC;QAAE0F,SAAS,EAAEtI;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE0E,QAAQ,EAAE,CAAC;MACXpC,IAAI,EAAE5B,KAAK;MACXkC,IAAI,EAAE,CAAC;QAAE0F,SAAS,EAAEtI;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE2D,SAAS,EAAE,CAAC;MACZrB,IAAI,EAAE5B,KAAK;MACXkC,IAAI,EAAE,CAAC;QAAE0F,SAAS,EAAEtI;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEkE,QAAQ,EAAE,CAAC;MACX5B,IAAI,EAAE5B,KAAK;MACXkC,IAAI,EAAE,CAAC;QAAE0F,SAAS,EAAEtI;MAAiB,CAAC;IAC1C,CAAC;EAAE,CAAC;AAAA;AAChB,MAAM4F,UAAU,CAAC;EACb;EACA,IAAI2C,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACC,cAAc;EAC9B;EACA,IAAID,aAAaA,CAACE,KAAK,EAAE;IACrB,IAAI,IAAI,CAACC,KAAK,IAAI,IAAI,CAACC,MAAM,EAAE;MAC3B;MACA,IAAI,CAAC,IAAI,CAACC,aAAa,CAACH,KAAK,CAAC,KAAK,OAAO/F,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QAC/E,MAAMmG,KAAK,CAAC,mEAAmE,CAAC;MACpF;MACA,IAAI,CAAC9D,QAAQ,EAAEQ,iBAAiB,CAAC,CAAC;MAClC,IAAI,IAAI,CAACiD,cAAc,KAAKC,KAAK,IAC7B,CAAC,IAAI,CAACK,4BAA4B,CAACL,KAAK,CAAC,KACxCA,KAAK,IAAI,IAAI,CAACD,cAAc,IAAI,IAAI,CAACE,KAAK,CAACK,OAAO,CAAC,CAAC,CAACN,KAAK,CAAC,CAAChE,QAAQ,CAAC,EAAE;QACxE,IAAI,CAACuE,wBAAwB,CAACP,KAAK,CAAC;MACxC;IACJ,CAAC,MACI;MACD,IAAI,CAACD,cAAc,GAAGC,KAAK;IAC/B;EACJ;EACA;EACA,IAAI1D,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC2D,KAAK,GAAG,IAAI,CAACA,KAAK,CAACK,OAAO,CAAC,CAAC,CAAC,IAAI,CAACR,aAAa,CAAC,GAAGJ,SAAS;EAC5E;EACA,IAAIpD,QAAQA,CAACkE,IAAI,EAAE;IACf,IAAI,CAACV,aAAa,GAAGU,IAAI,IAAI,IAAI,CAACP,KAAK,GAAG,IAAI,CAACA,KAAK,CAACK,OAAO,CAAC,CAAC,CAACG,OAAO,CAACD,IAAI,CAAC,GAAG,CAAC,CAAC;EACrF;EACA;EACA,IAAIE,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACC,YAAY;EAC5B;EACA,IAAID,WAAWA,CAACrF,KAAK,EAAE;IACnB;IACA,IAAI,CAACsF,YAAY,GAAGtF,KAAK;IACzB,IAAI,IAAI,CAACuF,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAACC,uBAAuB,CAACxF,KAAK,KAAK,UAAU,CAAC;IAClE;EACJ;EACAnC,WAAWA,CAAC4H,IAAI,EAAEC,kBAAkB,EAAE5H,WAAW,EAAE;IAC/C,IAAI,CAAC2H,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAAC5H,WAAW,GAAGA,WAAW;IAC9B;IACA,IAAI,CAAC6H,UAAU,GAAG,IAAIxI,OAAO,CAAC,CAAC;IAC/B;IACA,IAAI,CAACyH,KAAK,GAAG,IAAI9H,SAAS,CAAC,CAAC;IAC5B;IACA,IAAI,CAAC8I,cAAc,GAAG,IAAI9I,SAAS,CAAC,CAAC;IACrC;IACA,IAAI,CAAC+I,MAAM,GAAG,KAAK;IACnB,IAAI,CAACnB,cAAc,GAAG,CAAC;IACvB;IACA,IAAI,CAACoB,eAAe,GAAG,IAAI9J,YAAY,CAAC,CAAC;IACzC;IACA,IAAI,CAAC+J,mBAAmB,GAAG,IAAI/J,YAAY,CAAC,CAAC;IAC7C,IAAI,CAACsJ,YAAY,GAAG,YAAY;IAChC,IAAI,CAACU,QAAQ,GAAG5G,MAAM,EAAE;EAC5B;EACA6G,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACpB,MAAM,CAACqB,OAAO,CACdC,IAAI,CAAC9I,SAAS,CAAC,IAAI,CAACwH,MAAM,CAAC,EAAEvH,SAAS,CAAC,IAAI,CAACqI,UAAU,CAAC,CAAC,CACxDS,SAAS,CAAExB,KAAK,IAAK;MACtB,IAAI,CAACA,KAAK,CAAC1D,KAAK,CAAC0D,KAAK,CAACyB,MAAM,CAAClB,IAAI,IAAIA,IAAI,CAAC3E,QAAQ,KAAK,IAAI,CAAC,CAAC;MAC9D,IAAI,CAACoE,KAAK,CAAC0B,eAAe,CAAC,CAAC;IAChC,CAAC,CAAC;EACN;EACAC,eAAeA,CAAA,EAAG;IACd;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAACC,WAAW,CAACN,OAAO,CACnBC,IAAI,CAAC9I,SAAS,CAAC,IAAI,CAACmJ,WAAW,CAAC,EAAElJ,SAAS,CAAC,IAAI,CAACqI,UAAU,CAAC,CAAC,CAC7DS,SAAS,CAAEK,OAAO,IAAK;MACxB,IAAI,CAACb,cAAc,CAAC1E,KAAK,CAACuF,OAAO,CAACxB,OAAO,CAAC,CAAC,CAACyB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QACvD,MAAMC,gBAAgB,GAAGF,CAAC,CAAC7I,WAAW,CAACE,aAAa,CAAC8I,uBAAuB,CAACF,CAAC,CAAC9I,WAAW,CAACE,aAAa,CAAC;QACzG;QACA;QACA;QACA,OAAO6I,gBAAgB,GAAGE,IAAI,CAACC,2BAA2B,GAAG,CAAC,CAAC,GAAG,CAAC;MACvE,CAAC,CAAC,CAAC;MACH,IAAI,CAACpB,cAAc,CAACU,eAAe,CAAC,CAAC;IACzC,CAAC,CAAC;IACF;IACA;IACA;IACA,IAAI,CAACf,WAAW,GAAG,IAAIhK,eAAe,CAAC,IAAI,CAACqK,cAAc,CAAC,CACtDqB,QAAQ,CAAC,CAAC,CACVC,cAAc,CAAC,CAAC,CAChB1B,uBAAuB,CAAC,IAAI,CAACF,YAAY,KAAK,UAAU,CAAC;IAC9D,CAAC,IAAI,CAACG,IAAI,GAAG,IAAI,CAACA,IAAI,CAAC0B,MAAM,GAAG/J,EAAE,CAAC,CAAC,EAC/B+I,IAAI,CAAC9I,SAAS,CAAC,IAAI,CAAC+J,gBAAgB,CAAC,CAAC,CAAC,EAAE9J,SAAS,CAAC,IAAI,CAACqI,UAAU,CAAC,CAAC,CACpES,SAAS,CAACiB,SAAS,IAAI,IAAI,CAAC9B,WAAW,CAAC+B,yBAAyB,CAACD,SAAS,CAAC,CAAC;IAClF,IAAI,CAAC9B,WAAW,CAACgC,gBAAgB,CAAC,IAAI,CAAC7C,cAAc,CAAC;IACtD;IACA,IAAI,CAACE,KAAK,CAACsB,OAAO,CAACE,SAAS,CAAC,MAAM;MAC/B,IAAI,CAAC,IAAI,CAACnF,QAAQ,EAAE;QAChB,IAAI,CAACyD,cAAc,GAAG8C,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC/C,cAAc,GAAG,CAAC,EAAE,CAAC,CAAC;MAC9D;IACJ,CAAC,CAAC;IACF;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAACI,aAAa,CAAC,IAAI,CAACJ,cAAc,CAAC,EAAE;MAC1C,IAAI,CAACA,cAAc,GAAG,CAAC;IAC3B;EACJ;EACAgD,WAAWA,CAAA,EAAG;IACV,IAAI,CAACnC,WAAW,EAAEoC,OAAO,CAAC,CAAC;IAC3B,IAAI,CAAC/C,KAAK,CAAC+C,OAAO,CAAC,CAAC;IACpB,IAAI,CAAC/B,cAAc,CAAC+B,OAAO,CAAC,CAAC;IAC7B,IAAI,CAAChC,UAAU,CAACiC,IAAI,CAAC,CAAC;IACtB,IAAI,CAACjC,UAAU,CAACkC,QAAQ,CAAC,CAAC;EAC9B;EACA;EACAD,IAAIA,CAAA,EAAG;IACH,IAAI,CAACnD,aAAa,GAAG+C,IAAI,CAACM,GAAG,CAAC,IAAI,CAACpD,cAAc,GAAG,CAAC,EAAE,IAAI,CAACE,KAAK,CAACmD,MAAM,GAAG,CAAC,CAAC;EACjF;EACA;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACvD,aAAa,GAAG+C,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC/C,cAAc,GAAG,CAAC,EAAE,CAAC,CAAC;EAC7D;EACA;EACAxD,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACgE,wBAAwB,CAAC,CAAC,CAAC;IAChC,IAAI,CAACN,KAAK,CAACxD,OAAO,CAAC+D,IAAI,IAAIA,IAAI,CAACjE,KAAK,CAAC,CAAC,CAAC;IACxC,IAAI,CAACM,aAAa,CAAC,CAAC;EACxB;EACA;EACAyG,eAAeA,CAACC,CAAC,EAAE;IACf,OAAO,kBAAkB,IAAI,CAAClC,QAAQ,IAAIkC,CAAC,EAAE;EACjD;EACA;EACAC,iBAAiBA,CAACD,CAAC,EAAE;IACjB,OAAO,oBAAoB,IAAI,CAAClC,QAAQ,IAAIkC,CAAC,EAAE;EACnD;EACA;EACA1G,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACkE,kBAAkB,CAAC0C,YAAY,CAAC,CAAC;EAC1C;EACA;EACAC,sBAAsBA,CAAC1D,KAAK,EAAE;IAC1B,MAAM2D,QAAQ,GAAG3D,KAAK,GAAG,IAAI,CAACD,cAAc;IAC5C,IAAI4D,QAAQ,GAAG,CAAC,EAAE;MACd,OAAO,IAAI,CAAClB,gBAAgB,CAAC,CAAC,KAAK,KAAK,GAAG,MAAM,GAAG,UAAU;IAClE,CAAC,MACI,IAAIkB,QAAQ,GAAG,CAAC,EAAE;MACnB,OAAO,IAAI,CAAClB,gBAAgB,CAAC,CAAC,KAAK,KAAK,GAAG,UAAU,GAAG,MAAM;IAClE;IACA,OAAO,SAAS;EACpB;EACA;EACAmB,iBAAiBA,CAAC5D,KAAK,EAAExB,KAAK,GAAG7D,UAAU,CAACC,MAAM,EAAE;IAChD,MAAM4F,IAAI,GAAG,IAAI,CAACP,KAAK,CAACK,OAAO,CAAC,CAAC,CAACN,KAAK,CAAC;IACxC,MAAM6D,aAAa,GAAG,IAAI,CAACC,cAAc,CAAC9D,KAAK,CAAC;IAChD,OAAOQ,IAAI,CAACrE,4BAA4B,GAClC,IAAI,CAAC4H,yBAAyB,CAACvD,IAAI,EAAEqD,aAAa,CAAC,GACnD,IAAI,CAACG,kBAAkB,CAACxD,IAAI,EAAEqD,aAAa,EAAErF,KAAK,CAAC;EAC7D;EACAuF,yBAAyBA,CAACvD,IAAI,EAAEqD,aAAa,EAAE;IAC3C,IAAIrD,IAAI,CAACxD,UAAU,CAAC,CAAC,IAAIwD,IAAI,CAAC/E,QAAQ,IAAI,CAACoI,aAAa,EAAE;MACtD,OAAOlJ,UAAU,CAACI,KAAK;IAC3B,CAAC,MACI,IAAI,CAACyF,IAAI,CAACtF,SAAS,IAAI2I,aAAa,EAAE;MACvC,OAAOlJ,UAAU,CAACC,MAAM;IAC5B,CAAC,MACI;MACD,OAAO4F,IAAI,CAACxE,QAAQ,GAAGrB,UAAU,CAACE,IAAI,GAAGF,UAAU,CAACG,IAAI;IAC5D;EACJ;EACAkJ,kBAAkBA,CAACxD,IAAI,EAAEqD,aAAa,EAAErF,KAAK,GAAG7D,UAAU,CAACC,MAAM,EAAE;IAC/D,IAAI4F,IAAI,CAACxD,UAAU,CAAC,CAAC,IAAIwD,IAAI,CAAC/E,QAAQ,IAAI,CAACoI,aAAa,EAAE;MACtD,OAAOlJ,UAAU,CAACI,KAAK;IAC3B,CAAC,MACI,IAAIyF,IAAI,CAACtF,SAAS,IAAI,CAAC2I,aAAa,EAAE;MACvC,OAAOlJ,UAAU,CAACG,IAAI;IAC1B,CAAC,MACI,IAAI0F,IAAI,CAACtF,SAAS,IAAI2I,aAAa,EAAE;MACtC,OAAOrF,KAAK;IAChB,CAAC,MACI,IAAIgC,IAAI,CAACxE,QAAQ,IAAI6H,aAAa,EAAE;MACrC,OAAOlJ,UAAU,CAACE,IAAI;IAC1B,CAAC,MACI;MACD,OAAO2D,KAAK;IAChB;EACJ;EACAsF,cAAcA,CAAC9D,KAAK,EAAE;IAClB,OAAO,IAAI,CAACD,cAAc,KAAKC,KAAK;EACxC;EACA;EACAiE,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACrD,WAAW,GAAG,IAAI,CAACA,WAAW,CAACsD,eAAe,GAAG,IAAI,CAACnE,cAAc;EACpF;EACAQ,wBAAwBA,CAAC4D,QAAQ,EAAE;IAC/B,MAAMC,UAAU,GAAG,IAAI,CAACnE,KAAK,CAACK,OAAO,CAAC,CAAC;IACvC,IAAI,CAACa,eAAe,CAACpE,IAAI,CAAC;MACtB+C,aAAa,EAAEqE,QAAQ;MACvBE,uBAAuB,EAAE,IAAI,CAACtE,cAAc;MAC5CuE,YAAY,EAAEF,UAAU,CAACD,QAAQ,CAAC;MAClCI,sBAAsB,EAAEH,UAAU,CAAC,IAAI,CAACrE,cAAc;IAC1D,CAAC,CAAC;IACF;IACA;IACA;IACA;IACA,IAAI,CAACyE,cAAc,CAAC,CAAC,GACf,IAAI,CAAC5D,WAAW,CAAC6D,aAAa,CAACN,QAAQ,CAAC,GACxC,IAAI,CAACvD,WAAW,CAACgC,gBAAgB,CAACuB,QAAQ,CAAC;IACjD,IAAI,CAACpE,cAAc,GAAGoE,QAAQ;IAC9B,IAAI,CAAC/C,mBAAmB,CAACrE,IAAI,CAAC,IAAI,CAACgD,cAAc,CAAC;IAClD,IAAI,CAAClD,aAAa,CAAC,CAAC;EACxB;EACA6H,UAAUA,CAACC,KAAK,EAAE;IACd,MAAMC,WAAW,GAAG7N,cAAc,CAAC4N,KAAK,CAAC;IACzC,MAAME,OAAO,GAAGF,KAAK,CAACE,OAAO;IAC7B,MAAMC,OAAO,GAAG,IAAI,CAAClE,WAAW;IAChC,IAAIkE,OAAO,CAACZ,eAAe,IAAI,IAAI,IAC/B,CAACU,WAAW,KACXC,OAAO,KAAK7N,KAAK,IAAI6N,OAAO,KAAK5N,KAAK,CAAC,EAAE;MAC1C,IAAI,CAAC6I,aAAa,GAAGgF,OAAO,CAACZ,eAAe;MAC5CS,KAAK,CAACI,cAAc,CAAC,CAAC;IAC1B,CAAC,MACI;MACDD,OAAO,CAACE,cAAc,CAAC,UAAU,CAAC,CAACC,SAAS,CAACN,KAAK,CAAC;IACvD;EACJ;EACAtE,4BAA4BA,CAACL,KAAK,EAAE;IAChC,IAAI,IAAI,CAACkB,MAAM,IAAIlB,KAAK,IAAI,CAAC,EAAE;MAC3B,OAAO,IAAI,CAACC,KAAK,CACZK,OAAO,CAAC,CAAC,CACT4E,KAAK,CAAC,CAAC,EAAElF,KAAK,CAAC,CACfmF,IAAI,CAAC3E,IAAI,IAAI;QACd,MAAM4E,OAAO,GAAG5E,IAAI,CAAClF,WAAW;QAChC,MAAM+J,YAAY,GAAGD,OAAO,GACtBA,OAAO,CAACxJ,OAAO,IAAIwJ,OAAO,CAACE,OAAO,IAAI,CAAC9E,IAAI,CAAChF,UAAU,GACtD,CAACgF,IAAI,CAACtF,SAAS;QACrB,OAAOmK,YAAY,IAAI,CAAC7E,IAAI,CAACvE,QAAQ,IAAI,CAACuE,IAAI,CAACrF,kBAAkB;MACrE,CAAC,CAAC;IACN;IACA,OAAO,KAAK;EAChB;EACAsH,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAI,CAAC3B,IAAI,IAAI,IAAI,CAACA,IAAI,CAACzF,KAAK,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK;EACjE;EACA;EACAmJ,cAAcA,CAAA,EAAG;IACb,MAAMe,cAAc,GAAG,IAAI,CAACpM,WAAW,CAACE,aAAa;IACrD,MAAMmM,cAAc,GAAGjN,iCAAiC,CAAC,CAAC;IAC1D,OAAOgN,cAAc,KAAKC,cAAc,IAAID,cAAc,CAACE,QAAQ,CAACD,cAAc,CAAC;EACvF;EACA;EACArF,aAAaA,CAACH,KAAK,EAAE;IACjB,OAAOA,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,CAACC,KAAK,IAAID,KAAK,GAAG,IAAI,CAACC,KAAK,CAACmD,MAAM,CAAC;EACnE;EACA;IAAS,IAAI,CAAC9J,IAAI,YAAAoM,mBAAAlM,iBAAA;MAAA,YAAAA,iBAAA,IAA+F2D,UAAU,EAjcpBjG,EAAE,CAAAuC,iBAAA,CAicoC5C,EAAE,CAAC8O,cAAc,MAjcvDzO,EAAE,CAAAuC,iBAAA,CAickFvC,EAAE,CAAC0O,iBAAiB,GAjcxG1O,EAAE,CAAAuC,iBAAA,CAicmHvC,EAAE,CAACwC,UAAU;IAAA,CAA4C;EAAE;EACvR;IAAS,IAAI,CAACC,IAAI,kBAlcqFzC,EAAE,CAAA0C,iBAAA;MAAAC,IAAA,EAkcJsD,UAAU;MAAArD,SAAA;MAAAwD,cAAA,WAAAuI,0BAAA/M,EAAA,EAAAC,GAAA,EAAAyE,QAAA;QAAA,IAAA1E,EAAA;UAlcR5B,EAAE,CAAAuG,cAAA,CAAAD,QAAA,EAkcoXvC,OAAO;UAlc7X/D,EAAE,CAAAuG,cAAA,CAAAD,QAAA,EAkc4bvE,aAAa;QAAA;QAAA,IAAAH,EAAA;UAAA,IAAA4E,EAAA;UAlc3cxG,EAAE,CAAAyG,cAAA,CAAAD,EAAA,GAAFxG,EAAE,CAAA0G,WAAA,QAAA7E,GAAA,CAAAmH,MAAA,GAAAxC,EAAA;UAAFxG,EAAE,CAAAyG,cAAA,CAAAD,EAAA,GAAFxG,EAAE,CAAA0G,WAAA,QAAA7E,GAAA,CAAA8I,WAAA,GAAAnE,EAAA;QAAA;MAAA;MAAAS,MAAA;QAAA+C,MAAA,0BAkc6F3J,gBAAgB;QAAAuI,aAAA,wCAAqD1H,eAAe;QAAAkE,QAAA;QAAAoE,WAAA;MAAA;MAAAjC,OAAA;QAAA0C,eAAA;QAAAC,mBAAA;MAAA;MAAA1C,QAAA;MAAA1E,UAAA;MAAA2E,QAAA,GAlcnLzH,EAAE,CAAA0H,wBAAA;IAAA,EAkc0gB;EAAE;AACznB;AACA;EAAA,QAAA3E,SAAA,oBAAAA,SAAA,KApc2G/C,EAAE,CAAAgD,iBAAA,CAocXiD,UAAU,EAAc,CAAC;IAC/GtD,IAAI,EAAE1C,SAAS;IACfgD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,cAAc;MACxBsE,QAAQ,EAAE,YAAY;MACtB1E,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEH,IAAI,EAAEhD,EAAE,CAAC8O,cAAc;IAAElG,UAAU,EAAE,CAAC;MACvD5F,IAAI,EAAEhC;IACV,CAAC;EAAE,CAAC,EAAE;IAAEgC,IAAI,EAAE3C,EAAE,CAAC0O;EAAkB,CAAC,EAAE;IAAE/L,IAAI,EAAE3C,EAAE,CAACwC;EAAW,CAAC,CAAC,EAAkB;IAAEwG,MAAM,EAAE,CAAC;MAC3FrG,IAAI,EAAE9B,eAAe;MACrBoC,IAAI,EAAE,CAACc,OAAO,EAAE;QAAE0E,WAAW,EAAE;MAAK,CAAC;IACzC,CAAC,CAAC;IAAEkC,WAAW,EAAE,CAAC;MACdhI,IAAI,EAAE9B,eAAe;MACrBoC,IAAI,EAAE,CAAClB,aAAa,EAAE;QAAE0G,WAAW,EAAE;MAAK,CAAC;IAC/C,CAAC,CAAC;IAAEuB,MAAM,EAAE,CAAC;MACTrH,IAAI,EAAE5B,KAAK;MACXkC,IAAI,EAAE,CAAC;QAAE0F,SAAS,EAAEtI;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEuI,aAAa,EAAE,CAAC;MAChBjG,IAAI,EAAE5B,KAAK;MACXkC,IAAI,EAAE,CAAC;QAAE0F,SAAS,EAAEzH;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAEkE,QAAQ,EAAE,CAAC;MACXzC,IAAI,EAAE5B;IACV,CAAC,CAAC;IAAEkJ,eAAe,EAAE,CAAC;MAClBtH,IAAI,EAAE3B;IACV,CAAC,CAAC;IAAEkJ,mBAAmB,EAAE,CAAC;MACtBvH,IAAI,EAAE3B;IACV,CAAC,CAAC;IAAEwI,WAAW,EAAE,CAAC;MACd7G,IAAI,EAAE5B;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,MAAM6N,cAAc,CAAC;EACjB5M,WAAWA,CAAC2C,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB;IACA,IAAI,CAAChC,IAAI,GAAG,QAAQ;EACxB;EACA;IAAS,IAAI,CAACP,IAAI,YAAAyM,uBAAAvM,iBAAA;MAAA,YAAAA,iBAAA,IAA+FsM,cAAc,EA1exB5O,EAAE,CAAAuC,iBAAA,CA0ewC0D,UAAU;IAAA,CAA4C;EAAE;EACzM;IAAS,IAAI,CAACxD,IAAI,kBA3eqFzC,EAAE,CAAA0C,iBAAA;MAAAC,IAAA,EA2eJiM,cAAc;MAAAhM,SAAA;MAAAkM,QAAA;MAAAC,YAAA,WAAAC,4BAAApN,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA3eZ5B,EAAE,CAAAiP,UAAA,mBAAAC,wCAAA;YAAA,OA2eJrN,GAAA,CAAA8C,QAAA,CAAAoH,IAAA,CAAc,CAAC;UAAA,CAAF,CAAC;QAAA;QAAA,IAAAnK,EAAA;UA3eZ5B,EAAE,CAAAmP,cAAA,SAAAtN,GAAA,CAAAc,IA2eS,CAAC;QAAA;MAAA;MAAAsE,MAAA;QAAAtE,IAAA;MAAA;MAAAG,UAAA;IAAA,EAAwL;EAAE;AACjT;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA7e2G/C,EAAE,CAAAgD,iBAAA,CA6eX4L,cAAc,EAAc,CAAC;IACnHjM,IAAI,EAAE1C,SAAS;IACfgD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,wBAAwB;MAClCC,IAAI,EAAE;QACF,QAAQ,EAAE,MAAM;QAChB,SAAS,EAAE;MACf,CAAC;MACDL,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEH,IAAI,EAAEsD;EAAW,CAAC,CAAC,EAAkB;IAAEtD,IAAI,EAAE,CAAC;MACnEA,IAAI,EAAE5B;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA,MAAMqO,kBAAkB,CAAC;EACrBpN,WAAWA,CAAC2C,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB;IACA,IAAI,CAAChC,IAAI,GAAG,QAAQ;EACxB;EACA;IAAS,IAAI,CAACP,IAAI,YAAAiN,2BAAA/M,iBAAA;MAAA,YAAAA,iBAAA,IAA+F8M,kBAAkB,EAjgB5BpP,EAAE,CAAAuC,iBAAA,CAigB4C0D,UAAU;IAAA,CAA4C;EAAE;EAC7M;IAAS,IAAI,CAACxD,IAAI,kBAlgBqFzC,EAAE,CAAA0C,iBAAA;MAAAC,IAAA,EAkgBJyM,kBAAkB;MAAAxM,SAAA;MAAAkM,QAAA;MAAAC,YAAA,WAAAO,gCAAA1N,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAlgBhB5B,EAAE,CAAAiP,UAAA,mBAAAM,4CAAA;YAAA,OAkgBJ1N,GAAA,CAAA8C,QAAA,CAAAwH,QAAA,CAAkB,CAAC;UAAA,CAAF,CAAC;QAAA;QAAA,IAAAvK,EAAA;UAlgBhB5B,EAAE,CAAAmP,cAAA,SAAAtN,GAAA,CAAAc,IAkgBa,CAAC;QAAA;MAAA;MAAAsE,MAAA;QAAAtE,IAAA;MAAA;MAAAG,UAAA;IAAA,EAAgM;EAAE;AAC7T;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KApgB2G/C,EAAE,CAAAgD,iBAAA,CAogBXoM,kBAAkB,EAAc,CAAC;IACvHzM,IAAI,EAAE1C,SAAS;IACfgD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,4BAA4B;MACtCC,IAAI,EAAE;QACF,QAAQ,EAAE,MAAM;QAChB,SAAS,EAAE;MACf,CAAC;MACDL,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEH,IAAI,EAAEsD;EAAW,CAAC,CAAC,EAAkB;IAAEtD,IAAI,EAAE,CAAC;MACnEA,IAAI,EAAE5B;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMyO,gBAAgB,CAAC;EACnB;IAAS,IAAI,CAACpN,IAAI,YAAAqN,yBAAAnN,iBAAA;MAAA,YAAAA,iBAAA,IAA+FkN,gBAAgB;IAAA,CAAkD;EAAE;EACrL;IAAS,IAAI,CAACE,IAAI,kBAphBqF1P,EAAE,CAAA2P,gBAAA;MAAAhN,IAAA,EAohBS6M;IAAgB,EAMH;EAAE;EACjI;IAAS,IAAI,CAACI,IAAI,kBA3hBqF5P,EAAE,CAAA6P,gBAAA;MAAAC,OAAA,GA2hBqClQ,UAAU;IAAA,EAAI;EAAE;AAClK;AACA;EAAA,QAAAmD,SAAA,oBAAAA,SAAA,KA7hB2G/C,EAAE,CAAAgD,iBAAA,CA6hBXwM,gBAAgB,EAAc,CAAC;IACrH7M,IAAI,EAAExB,QAAQ;IACd8B,IAAI,EAAE,CAAC;MACC6M,OAAO,EAAE,CACLlQ,UAAU,EACVmE,OAAO,EACPkC,UAAU,EACVlE,aAAa,EACbqB,YAAY,EACZwL,cAAc,EACdQ,kBAAkB,CACrB;MACDW,OAAO,EAAE,CAAChM,OAAO,EAAEkC,UAAU,EAAElE,aAAa,EAAEqB,YAAY,EAAEwL,cAAc,EAAEQ,kBAAkB;IAClG,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASrL,OAAO,EAAEhC,aAAa,EAAEqB,YAAY,EAAE6C,UAAU,EAAEuJ,gBAAgB,EAAEZ,cAAc,EAAEQ,kBAAkB,EAAEtL,sBAAsB,EAAEL,UAAU,EAAED,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}