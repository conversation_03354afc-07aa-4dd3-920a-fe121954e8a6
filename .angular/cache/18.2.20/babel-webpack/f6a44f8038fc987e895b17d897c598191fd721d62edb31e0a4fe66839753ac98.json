{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./wizard.component\";\nexport class WizardStepDirective {\n  set current(current) {\n    this._current = current;\n    if (!current) {\n      return;\n    }\n    if (this.disabled) {\n      this.disabled = false;\n    }\n    this.wizard.steps.forEach(step => {\n      if (step !== this) {\n        step.current = false;\n      }\n    });\n  }\n  get current() {\n    return this._current;\n  }\n  get hidden() {\n    return !this.current;\n  }\n  get first() {\n    return this.index === 0;\n  }\n  get last() {\n    return this.index + 1 === this.wizard.steps.length;\n  }\n  get index() {\n    return this.wizard.steps.indexOf(this);\n  }\n  get stepNumber() {\n    return (this.index + 1).toString();\n  }\n  get class() {\n    return {\n      'current': this.current,\n      'first': this.first,\n      'last': this.last,\n      'disabled': this.disabled,\n      'done': !this.disabled && !this.current\n    };\n  }\n  constructor(wizard) {\n    this.wizard = wizard;\n    this.classList = '';\n    this.rendered = true;\n    this.wizard.addStep(this);\n  }\n  static {\n    this.ɵfac = function WizardStepDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || WizardStepDirective)(i0.ɵɵdirectiveInject(i1.WizardComponent));\n    };\n  }\n  static {\n    this.ɵdir = /*@__PURE__*/i0.ɵɵdefineDirective({\n      type: WizardStepDirective,\n      selectors: [[\"sw-wizard-step\"], [\"\", \"sw-wizard-step\", \"\"]],\n      hostVars: 8,\n      hostBindings: function WizardStepDirective_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.classList);\n          i0.ɵɵclassProp(\"body\", ctx.rendered)(\"current\", ctx.current)(\"hidden\", ctx.hidden);\n        }\n      },\n      inputs: {\n        heading: \"heading\",\n        id: \"id\",\n        disabled: \"disabled\",\n        classList: [0, \"class\", \"classList\"],\n        current: \"current\"\n      },\n      exportAs: [\"wizard-step\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["WizardStepDirective", "current", "_current", "disabled", "wizard", "steps", "for<PERSON>ach", "step", "hidden", "first", "index", "last", "length", "indexOf", "<PERSON><PERSON><PERSON><PERSON>", "toString", "class", "constructor", "classList", "rendered", "addStep", "i0", "ɵɵdirectiveInject", "i1", "WizardComponent", "selectors", "hostVars", "hostBindings", "WizardStepDirective_HostBindings", "rf", "ctx", "ɵɵclassMap", "ɵɵclassProp"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/common/components/swWizard/wizard-step.directive.ts"], "sourcesContent": ["import { Directive, HostBinding, Input } from '@angular/core';\nimport { WizardComponent } from './wizard.component';\n\nexport interface WizardStepDirectiveInterface {\n  heading: string;\n  id: string;\n  disabled: boolean;\n  classList: string;\n  rendered: boolean;\n  current: boolean;\n  readonly hidden: boolean;\n  readonly first: boolean;\n  readonly last: boolean;\n  readonly index: number;\n  readonly stepNumber: string;\n  readonly class: Object;\n}\n\n@Directive({\n  selector: 'sw-wizard-step,[sw-wizard-step]',\n  exportAs: 'wizard-step'\n})\nexport class WizardStepDirective {\n\n  @Input() public heading: string;\n  @Input() public id: string;\n  @Input() public disabled: boolean;\n\n  @HostBinding('class') @Input('class') classList: string = '';\n\n  @HostBinding('class.body') rendered: boolean = true;\n\n  protected _current: boolean;\n\n  @HostBinding('class.current')\n  @Input()\n  public set current( current: boolean ) {\n    this._current = current;\n\n    if (!current) {\n      return;\n    }\n\n    if (this.disabled) {\n      this.disabled = false;\n    }\n\n    this.wizard.steps.forEach(( step: WizardStepDirective ) => {\n      if (step !== this) {\n        step.current = false;\n      }\n    });\n\n  }\n\n  public get current(): boolean {\n    return this._current;\n  }\n\n  @HostBinding('class.hidden')\n  public get hidden(): boolean {\n    return !this.current;\n  }\n\n  public get first(): boolean {\n    return this.index === 0;\n  }\n\n  public get last(): boolean {\n    return this.index + 1 === this.wizard.steps.length;\n  }\n\n  public get index(): number {\n    return this.wizard.steps.indexOf(this);\n  }\n\n  public get stepNumber(): string {\n    return (this.index + 1).toString();\n  }\n\n  public get class(): Object {\n    return {\n      'current': this.current,\n      'first': this.first,\n      'last': this.last,\n      'disabled': this.disabled,\n      'done': !this.disabled && !this.current,\n    };\n  }\n\n  constructor( public wizard: WizardComponent ) {\n    this.wizard.addStep(this);\n  }\n\n}\n"], "mappings": ";;AAsBA,OAAM,MAAOA,mBAAmB;EAY9B,IAEWC,OAAOA,CAAEA,OAAgB;IAClC,IAAI,CAACC,QAAQ,GAAGD,OAAO;IAEvB,IAAI,CAACA,OAAO,EAAE;MACZ;IACF;IAEA,IAAI,IAAI,CAACE,QAAQ,EAAE;MACjB,IAAI,CAACA,QAAQ,GAAG,KAAK;IACvB;IAEA,IAAI,CAACC,MAAM,CAACC,KAAK,CAACC,OAAO,CAAGC,IAAyB,IAAK;MACxD,IAAIA,IAAI,KAAK,IAAI,EAAE;QACjBA,IAAI,CAACN,OAAO,GAAG,KAAK;MACtB;IACF,CAAC,CAAC;EAEJ;EAEA,IAAWA,OAAOA,CAAA;IAChB,OAAO,IAAI,CAACC,QAAQ;EACtB;EAEA,IACWM,MAAMA,CAAA;IACf,OAAO,CAAC,IAAI,CAACP,OAAO;EACtB;EAEA,IAAWQ,KAAKA,CAAA;IACd,OAAO,IAAI,CAACC,KAAK,KAAK,CAAC;EACzB;EAEA,IAAWC,IAAIA,CAAA;IACb,OAAO,IAAI,CAACD,KAAK,GAAG,CAAC,KAAK,IAAI,CAACN,MAAM,CAACC,KAAK,CAACO,MAAM;EACpD;EAEA,IAAWF,KAAKA,CAAA;IACd,OAAO,IAAI,CAACN,MAAM,CAACC,KAAK,CAACQ,OAAO,CAAC,IAAI,CAAC;EACxC;EAEA,IAAWC,UAAUA,CAAA;IACnB,OAAO,CAAC,IAAI,CAACJ,KAAK,GAAG,CAAC,EAAEK,QAAQ,EAAE;EACpC;EAEA,IAAWC,KAAKA,CAAA;IACd,OAAO;MACL,SAAS,EAAE,IAAI,CAACf,OAAO;MACvB,OAAO,EAAE,IAAI,CAACQ,KAAK;MACnB,MAAM,EAAE,IAAI,CAACE,IAAI;MACjB,UAAU,EAAE,IAAI,CAACR,QAAQ;MACzB,MAAM,EAAE,CAAC,IAAI,CAACA,QAAQ,IAAI,CAAC,IAAI,CAACF;KACjC;EACH;EAEAgB,YAAoBb,MAAuB;IAAvB,KAAAA,MAAM,GAANA,MAAM;IA9DY,KAAAc,SAAS,GAAW,EAAE;IAEjC,KAAAC,QAAQ,GAAY,IAAI;IA6DjD,IAAI,CAACf,MAAM,CAACgB,OAAO,CAAC,IAAI,CAAC;EAC3B;;;uCAtEWpB,mBAAmB,EAAAqB,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAAnBxB,mBAAmB;MAAAyB,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAnBR,EAAA,CAAAU,UAAA,CAAAD,GAAA,CAAAZ,SAAA,CAAmB;UAAnBG,EAAA,CAAAW,WAAA,SAAAF,GAAA,CAAAX,QAAA,CAAmB,YAAAW,GAAA,CAAA7B,OAAA,YAAA6B,GAAA,CAAAtB,MAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}