{"ast": null, "code": "import { ErrorStateMatcher } from '@angular/material/core';\nimport { MatStep } from '@angular/material/stepper';\nimport * as i0 from \"@angular/core\";\nconst _c0 = [\"*\"];\nfunction GamesSetupStepComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\nexport class GamesSetupStepComponent extends MatStep {\n  static {\n    this.ɵfac = /*@__PURE__*/(() => {\n      let ɵGamesSetupStepComponent_BaseFactory;\n      return function GamesSetupStepComponent_Factory(__ngFactoryType__) {\n        return (ɵGamesSetupStepComponent_BaseFactory || (ɵGamesSetupStepComponent_BaseFactory = i0.ɵɵgetInheritedFactory(GamesSetupStepComponent)))(__ngFactoryType__ || GamesSetupStepComponent);\n      };\n    })();\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: GamesSetupStepComponent,\n      selectors: [[\"games-setup-step\"]],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: ErrorStateMatcher,\n        useExisting: GamesSetupStepComponent\n      }]), i0.ɵɵInheritDefinitionFeature],\n      ngContentSelectors: _c0,\n      decls: 1,\n      vars: 0,\n      template: function GamesSetupStepComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, GamesSetupStepComponent_ng_template_0_Template, 1, 0, \"ng-template\");\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}", "map": {"version": 3, "names": ["ErrorStateMatcher", "MatStep", "i0", "ɵɵprojection", "GamesSetupStepComponent", "__ngFactoryType__", "selectors", "features", "ɵɵProvidersFeature", "provide", "useExisting", "ɵɵInheritDefinitionFeature", "ngContentSelectors", "_c0", "decls", "vars", "template", "GamesSetupStepComponent_Template", "rf", "ctx", "ɵɵtemplate", "GamesSetupStepComponent_ng_template_0_Template"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-games/games-setup-stepper/games-setup-step.component.ts", "/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-games/games-setup-stepper/games-setup-step.component.html"], "sourcesContent": ["import { ChangeDetectionStrategy, Component, ViewEncapsulation } from '@angular/core';\nimport { ErrorStateMatcher } from '@angular/material/core';\nimport { MatStep } from '@angular/material/stepper';\n\n@Component({\n  selector: 'games-setup-step',\n  templateUrl: 'games-setup-step.component.html',\n  providers: [{provide: ErrorStateMatcher, useExisting: GamesSetupStepComponent}],\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\n\nexport class GamesSetupStepComponent extends MatStep {\n}\n", "<ng-template><ng-content></ng-content></ng-template>\n"], "mappings": "AACA,SAASA,iBAAiB,QAAQ,wBAAwB;AAC1D,SAASC,OAAO,QAAQ,2BAA2B;;;;;ICFtCC,EAAA,CAAAC,YAAA,GAAyB;;;ADYtC,OAAM,MAAOC,uBAAwB,SAAQH,OAAO;;;;;yHAAvCG,uBAAuB,IAAAC,iBAAA,IAAvBD,uBAAuB;MAAA;IAAA;EAAA;;;YAAvBA,uBAAuB;MAAAE,SAAA;MAAAC,QAAA,GAAAL,EAAA,CAAAM,kBAAA,CALvB,CAAC;QAACC,OAAO,EAAET,iBAAiB;QAAEU,WAAW,EAAEN;MAAuB,CAAC,CAAC,GAAAF,EAAA,CAAAS,0BAAA;MAAAC,kBAAA,EAAAC,GAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCPjFhB,EAAA,CAAAkB,UAAA,IAAAC,8CAAA,sBAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}