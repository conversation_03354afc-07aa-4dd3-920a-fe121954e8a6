{"ast": null, "code": "const transformCommon = (bets, positionId) => {\n  return bets.map(({\n    bet,\n    win\n  }) => ({\n    bet,\n    win,\n    positionId\n  }));\n};\nexport const transformStraight = bets => {\n  return bets.map(({\n    bet,\n    win,\n    selector\n  }) => ({\n    bet,\n    win,\n    positionId: selector[0]\n  }));\n};\nexport const transformSplit = bets => {\n  return bets.map(({\n    bet,\n    win,\n    selector\n  }) => {\n    let positionId = '0';\n    switch (JSON.stringify(selector)) {\n      case '[\"1\",\"2\"]':\n        positionId = '37';\n        break;\n      case '[\"1\",\"4\"]':\n        positionId = '38';\n        break;\n      case '[\"1\",\"0\"]':\n        positionId = '39';\n        break;\n      case '[\"2\",\"0\"]':\n        positionId = '40';\n        break;\n      case '[\"2\",\"3\"]':\n        positionId = '41';\n        break;\n      case '[\"2\",\"5\"]':\n        positionId = '42';\n        break;\n      case '[\"3\",\"0\"]':\n        positionId = '43';\n        break;\n      case '[\"3\",\"6\"]':\n        positionId = '44';\n        break;\n      case '[\"4\",\"5\"]':\n        positionId = '45';\n        break;\n      case '[\"4\",\"7\"]':\n        positionId = '46';\n        break;\n      case '[\"5\",\"6\"]':\n        positionId = '47';\n        break;\n      case '[\"5\",\"8\"]':\n        positionId = '48';\n        break;\n      case '[\"6\",\"9\"]':\n        positionId = '49';\n        break;\n      case '[\"10\",\"7\"]':\n        positionId = '50';\n        break;\n      case '[\"7\",\"8\"]':\n        positionId = '51';\n        break;\n      case '[\"11\",\"8\"]':\n        positionId = '52';\n        break;\n      case '[\"8\",\"9\"]':\n        positionId = '53';\n        break;\n      case '[\"12\",\"9\"]':\n        positionId = '54';\n        break;\n      case '[\"10\",\"13\"]':\n        positionId = '55';\n        break;\n      case '[\"10\",\"11\"]':\n        positionId = '56';\n        break;\n      case '[\"11\",\"14\"]':\n        positionId = '57';\n        break;\n      case '[\"11\",\"12\"]':\n        positionId = '58';\n        break;\n      case '[\"12\",\"15\"]':\n        positionId = '59';\n        break;\n      case '[\"13\",\"16\"]':\n        positionId = '60';\n        break;\n      case '[\"13\",\"14\"]':\n        positionId = '61';\n        break;\n      case '[\"14\",\"17\"]':\n        positionId = '62';\n        break;\n      case '[\"14\",\"15\"]':\n        positionId = '63';\n        break;\n      case '[\"15\",\"18\"]':\n        positionId = '64';\n        break;\n      case '[\"16\",\"17\"]':\n        positionId = '65';\n        break;\n      case '[\"16\",\"19\"]':\n        positionId = '66';\n        break;\n      case '[\"17\",\"20\"]':\n        positionId = '67';\n        break;\n      case '[\"17\",\"18\"]':\n        positionId = '68';\n        break;\n      case '[\"18\",\"21\"]':\n        positionId = '69';\n        break;\n      case '[\"19\",\"22\"]':\n        positionId = '70';\n        break;\n      case '[\"19\",\"20\"]':\n        positionId = '71';\n        break;\n      case '[\"20\",\"23\"]':\n        positionId = '72';\n        break;\n      case '[\"20\",\"21\"]':\n        positionId = '73';\n        break;\n      case '[\"21\",\"24\"]':\n        positionId = '74';\n        break;\n      case '[\"22\",\"25\"]':\n        positionId = '75';\n        break;\n      case '[\"22\",\"23\"]':\n        positionId = '76';\n        break;\n      case '[\"23\",\"26\"]':\n        positionId = '77';\n        break;\n      case '[\"23\",\"24\"]':\n        positionId = '78';\n        break;\n      case '[\"24\",\"27\"]':\n        positionId = '79';\n        break;\n      case '[\"25\",\"28\"]':\n        positionId = '80';\n        break;\n      case '[\"25\",\"26\"]':\n        positionId = '81';\n        break;\n      case '[\"26\",\"29\"]':\n        positionId = '82';\n        break;\n      case '[\"26\",\"27\"]':\n        positionId = '83';\n        break;\n      case '[\"27\",\"30\"]':\n        positionId = '84';\n        break;\n      case '[\"28\",\"31\"]':\n        positionId = '85';\n        break;\n      case '[\"28\",\"29\"]':\n        positionId = '86';\n        break;\n      case '[\"29\",\"32\"]':\n        positionId = '87';\n        break;\n      case '[\"29\",\"30\"]':\n        positionId = '88';\n        break;\n      case '[\"30\",\"33\"]':\n        positionId = '89';\n        break;\n      case '[\"31\",\"34\"]':\n        positionId = '90';\n        break;\n      case '[\"31\",\"32\"]':\n        positionId = '91';\n        break;\n      case '[\"32\",\"35\"]':\n        positionId = '92';\n        break;\n      case '[\"32\",\"33\"]':\n        positionId = '93';\n        break;\n      case '[\"33\",\"36\"]':\n        positionId = '94';\n        break;\n      case '[\"34\",\"35\"]':\n        positionId = '95';\n        break;\n      case '[\"35\",\"36\"]':\n        positionId = '96';\n        break;\n      default:\n        break;\n    }\n    return {\n      bet,\n      win,\n      positionId\n    };\n  });\n};\nexport const transformStreet = bets => {\n  return bets.map(({\n    bet,\n    win,\n    selector\n  }) => {\n    let positionId = '0';\n    switch (JSON.stringify(selector)) {\n      case '[\"0\",\"1\",\"2\"]':\n        positionId = '97';\n        break;\n      case '[\"0\",\"2\",\"3\"]':\n        positionId = '98';\n        break;\n      case '[\"1\",\"2\",\"3\"]':\n        positionId = '99';\n        break;\n      case '[\"4\",\"5\",\"6\"]':\n        positionId = '100';\n        break;\n      case '[\"7\",\"8\",\"9\"]':\n        positionId = '101';\n        break;\n      case '[\"10\",\"11\",\"12\"]':\n        positionId = '102';\n        break;\n      case '[\"13\",\"14\",\"15\"]':\n        positionId = '103';\n        break;\n      case '[\"16\",\"17\",\"18\"]':\n        positionId = '104';\n        break;\n      case '[\"19\",\"20\",\"21\"]':\n        positionId = '105';\n        break;\n      case '[\"22\",\"23\",\"24\"]':\n        positionId = '106';\n        break;\n      case '[\"25\",\"26\",\"27\"]':\n        positionId = '107';\n        break;\n      case '[\"28\",\"29\",\"30\"]':\n        positionId = '108';\n        break;\n      case '[\"31\",\"32\",\"33\"]':\n        positionId = '109';\n        break;\n      case '[\"34\",\"35\",\"36\"]':\n        positionId = '110';\n        break;\n      default:\n        break;\n    }\n    return {\n      bet,\n      win,\n      positionId\n    };\n  });\n};\nexport const transformCorner = bets => {\n  return bets.map(({\n    bet,\n    win,\n    selector\n  }) => {\n    let positionId = '0';\n    switch (JSON.stringify(selector)) {\n      case '[\"0\",\"1\",\"2\",\"3\"]':\n        positionId = '111';\n        break;\n      case '[\"1\",\"2\",\"4\",\"5\"]':\n        positionId = '112';\n        break;\n      case '[\"2\",\"3\",\"5\",\"6\"]':\n        positionId = '113';\n        break;\n      case '[\"4\",\"5\",\"7\",\"8\"]':\n        positionId = '114';\n        break;\n      case '[\"5\",\"6\",\"8\",\"9\"]':\n        positionId = '115';\n        break;\n      case '[\"10\",\"11\",\"7\",\"8\"]':\n        positionId = '116';\n        break;\n      case '[\"11\",\"12\",\"8\",\"9\"]':\n        positionId = '117';\n        break;\n      case '[\"10\",\"11\",\"13\",\"14\"]':\n        positionId = '118';\n        break;\n      case '[\"11\",\"12\",\"14\",\"15\"]':\n        positionId = '119';\n        break;\n      case '[\"13\",\"14\",\"16\",\"17\"]':\n        positionId = '120';\n        break;\n      case '[\"14\",\"15\",\"17\",\"18\"]':\n        positionId = '121';\n        break;\n      case '[\"16\",\"17\",\"19\",\"20\"]':\n        positionId = '122';\n        break;\n      case '[\"17\",\"18\",\"20\",\"21\"]':\n        positionId = '123';\n        break;\n      case '[\"19\",\"20\",\"22\",\"23\"]':\n        positionId = '124';\n        break;\n      case '[\"20\",\"21\",\"23\",\"24\"]':\n        positionId = '125';\n        break;\n      case '[\"22\",\"23\",\"25\",\"26\"]':\n        positionId = '126';\n        break;\n      case '[\"23\",\"24\",\"26\",\"27\"]':\n        positionId = '127';\n        break;\n      case '[\"25\",\"26\",\"28\",\"29\"]':\n        positionId = '128';\n        break;\n      case '[\"26\",\"27\",\"29\",\"30\"]':\n        positionId = '129';\n        break;\n      case '[\"28\",\"29\",\"31\",\"32\"]':\n        positionId = '130';\n        break;\n      case '[\"29\",\"30\",\"32\",\"33\"]':\n        positionId = '131';\n        break;\n      case '[\"32\",\"32\",\"34\",\"36\"]':\n        positionId = '132';\n        break;\n      case '[\"32\",\"33\",\"35\",\"36\"]':\n        positionId = '133';\n        break;\n      default:\n        break;\n    }\n    return {\n      bet,\n      win,\n      positionId\n    };\n  });\n};\nexport const transformLine = bets => {\n  return bets.map(({\n    bet,\n    win,\n    selector\n  }) => {\n    let positionId = '0';\n    switch (JSON.stringify(selector)) {\n      case '[\"1\",\"2\",\"3\",\"4\",\"5\",\"6\"]':\n        positionId = '134';\n        break;\n      case '[\"4\",\"5\",\"6\",\"7\",\"8\",\"9\"]':\n        positionId = '135';\n        break;\n      case '[\"10\",\"11\",\"12\",\"7\",\"8\",\"9\"]':\n        positionId = '136';\n        break;\n      case '[\"10\",\"11\",\"12\",\"13\",\"14\",\"15\"]':\n        positionId = '137';\n        break;\n      case '[\"13\",\"14\",\"15\",\"16\",\"17\",\"18\"]':\n        positionId = '138';\n        break;\n      case '[\"16\",\"17\",\"18\",\"19\",\"20\",\"21\"]':\n        positionId = '139';\n        break;\n      case '[\"19\",\"20\",\"21\",\"22\",\"23\",\"24\"]':\n        positionId = '140';\n        break;\n      case '[\"22\",\"23\",\"24\",\"25\",\"26\",\"27\"]':\n        positionId = '141';\n        break;\n      case '[\"25\",\"26\",\"27\",\"28\",\"29\",\"30\"]':\n        positionId = '142';\n        break;\n      case '[\"28\",\"29\",\"30\",\"31\",\"32\",\"33\"]':\n        positionId = '143';\n        break;\n      case '[\"31\",\"32\",\"33\",\"34\",\"35\",\"36\"]':\n        positionId = '144';\n        break;\n      default:\n        break;\n    }\n    return {\n      bet,\n      win,\n      positionId\n    };\n  });\n};\nexport const transformColumn = bets => {\n  return bets.map(({\n    bet,\n    win,\n    selector\n  }) => {\n    let positionId = '145';\n    if (selector.includes('2')) {\n      positionId = '146';\n    } else if (selector.includes('3')) {\n      positionId = '147';\n    }\n    return {\n      bet,\n      win,\n      positionId\n    };\n  });\n};\nexport const transformDozen = bets => {\n  return bets.map(({\n    bet,\n    win,\n    selector\n  }) => {\n    let positionId = '148';\n    if (selector.includes('13')) {\n      positionId = '149';\n    } else if (selector.includes('25')) {\n      positionId = '150';\n    }\n    return {\n      bet,\n      win,\n      positionId\n    };\n  });\n};\nexport const transformRed = bets => {\n  return transformCommon(bets, '151');\n};\nexport const transformBlack = bets => {\n  return transformCommon(bets, '152');\n};\nexport const transformLow = bets => {\n  return transformCommon(bets, '153');\n};\nexport const transformHigh = bets => {\n  return transformCommon(bets, '154');\n};\nexport const transformOdd = bets => {\n  return transformCommon(bets, '155');\n};\nexport const transformEven = bets => {\n  return transformCommon(bets, '156');\n};", "map": {"version": 3, "names": ["transformCommon", "bets", "positionId", "map", "bet", "win", "transformStraight", "selector", "transformSplit", "JSON", "stringify", "transformStreet", "<PERSON><PERSON><PERSON><PERSON>", "transformLine", "transformColumn", "includes", "transformDozen", "transformRed", "transformBlack", "transformLow", "transformHigh", "transformOdd", "transformEven"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/gamehistory/components/internal-game-history/spin-details/sw_live_roulette_transform/transformers.ts"], "sourcesContent": ["import { IOriginalBet, ITransformedBet } from './interfaces';\n\nconst transformCommon = ( bets: IOriginalBet[], positionId: string ): ITransformedBet[] => {\n  return bets.map(( { bet, win } ) => (\n    {\n      bet,\n      win,\n      positionId\n    }\n  ));\n};\n\nexport const transformStraight = ( bets: IOriginalBet[] ): ITransformedBet[] => {\n  return bets.map(( { bet, win, selector }: IOriginalBet ) => (\n    {\n      bet,\n      win,\n      positionId: selector[0]\n    }\n  ));\n};\n\nexport const transformSplit = ( bets: IOriginalBet[] ): ITransformedBet[] => {\n  return bets.map(( { bet, win, selector }: IOriginalBet ) => {\n    let positionId = '0';\n\n    switch (JSON.stringify(selector)) {\n      case '[\"1\",\"2\"]':\n        positionId = '37';\n        break;\n      case '[\"1\",\"4\"]':\n        positionId = '38';\n        break;\n      case '[\"1\",\"0\"]':\n        positionId = '39';\n        break;\n      case '[\"2\",\"0\"]':\n        positionId = '40';\n        break;\n      case '[\"2\",\"3\"]':\n        positionId = '41';\n        break;\n      case '[\"2\",\"5\"]':\n        positionId = '42';\n        break;\n      case '[\"3\",\"0\"]':\n        positionId = '43';\n        break;\n      case '[\"3\",\"6\"]':\n        positionId = '44';\n        break;\n      case '[\"4\",\"5\"]':\n        positionId = '45';\n        break;\n      case '[\"4\",\"7\"]':\n        positionId = '46';\n        break;\n      case '[\"5\",\"6\"]':\n        positionId = '47';\n        break;\n      case '[\"5\",\"8\"]':\n        positionId = '48';\n        break;\n      case '[\"6\",\"9\"]':\n        positionId = '49';\n        break;\n      case '[\"10\",\"7\"]':\n        positionId = '50';\n        break;\n      case '[\"7\",\"8\"]':\n        positionId = '51';\n        break;\n      case '[\"11\",\"8\"]':\n        positionId = '52';\n        break;\n      case '[\"8\",\"9\"]':\n        positionId = '53';\n        break;\n      case '[\"12\",\"9\"]':\n        positionId = '54';\n        break;\n      case '[\"10\",\"13\"]':\n        positionId = '55';\n        break;\n      case '[\"10\",\"11\"]':\n        positionId = '56';\n        break;\n      case '[\"11\",\"14\"]':\n        positionId = '57';\n        break;\n      case '[\"11\",\"12\"]':\n        positionId = '58';\n        break;\n      case '[\"12\",\"15\"]':\n        positionId = '59';\n        break;\n      case '[\"13\",\"16\"]':\n        positionId = '60';\n        break;\n      case '[\"13\",\"14\"]':\n        positionId = '61';\n        break;\n      case '[\"14\",\"17\"]':\n        positionId = '62';\n        break;\n      case '[\"14\",\"15\"]':\n        positionId = '63';\n        break;\n      case '[\"15\",\"18\"]':\n        positionId = '64';\n        break;\n      case '[\"16\",\"17\"]':\n        positionId = '65';\n        break;\n      case '[\"16\",\"19\"]':\n        positionId = '66';\n        break;\n      case '[\"17\",\"20\"]':\n        positionId = '67';\n        break;\n      case '[\"17\",\"18\"]':\n        positionId = '68';\n        break;\n      case '[\"18\",\"21\"]':\n        positionId = '69';\n        break;\n      case '[\"19\",\"22\"]':\n        positionId = '70';\n        break;\n      case '[\"19\",\"20\"]':\n        positionId = '71';\n        break;\n      case '[\"20\",\"23\"]':\n        positionId = '72';\n        break;\n      case '[\"20\",\"21\"]':\n        positionId = '73';\n        break;\n      case '[\"21\",\"24\"]':\n        positionId = '74';\n        break;\n      case '[\"22\",\"25\"]':\n        positionId = '75';\n        break;\n      case '[\"22\",\"23\"]':\n        positionId = '76';\n        break;\n      case '[\"23\",\"26\"]':\n        positionId = '77';\n        break;\n      case '[\"23\",\"24\"]':\n        positionId = '78';\n        break;\n      case '[\"24\",\"27\"]':\n        positionId = '79';\n        break;\n      case '[\"25\",\"28\"]':\n        positionId = '80';\n        break;\n      case '[\"25\",\"26\"]':\n        positionId = '81';\n        break;\n      case '[\"26\",\"29\"]':\n        positionId = '82';\n        break;\n      case '[\"26\",\"27\"]':\n        positionId = '83';\n        break;\n      case '[\"27\",\"30\"]':\n        positionId = '84';\n        break;\n      case '[\"28\",\"31\"]':\n        positionId = '85';\n        break;\n      case '[\"28\",\"29\"]':\n        positionId = '86';\n        break;\n      case '[\"29\",\"32\"]':\n        positionId = '87';\n        break;\n      case '[\"29\",\"30\"]':\n        positionId = '88';\n        break;\n      case '[\"30\",\"33\"]':\n        positionId = '89';\n        break;\n      case '[\"31\",\"34\"]':\n        positionId = '90';\n        break;\n      case '[\"31\",\"32\"]':\n        positionId = '91';\n        break;\n      case '[\"32\",\"35\"]':\n        positionId = '92';\n        break;\n      case '[\"32\",\"33\"]':\n        positionId = '93';\n        break;\n      case '[\"33\",\"36\"]':\n        positionId = '94';\n        break;\n      case '[\"34\",\"35\"]':\n        positionId = '95';\n        break;\n      case '[\"35\",\"36\"]':\n        positionId = '96';\n        break;\n      default:\n        break;\n    }\n\n    return {\n      bet,\n      win,\n      positionId\n    };\n  });\n};\n\nexport const transformStreet = ( bets: IOriginalBet[] ): ITransformedBet[] => {\n  return bets.map(( { bet, win, selector }: IOriginalBet ) => {\n    let positionId = '0';\n\n    switch (JSON.stringify(selector)) {\n      case '[\"0\",\"1\",\"2\"]':\n        positionId = '97';\n        break;\n      case '[\"0\",\"2\",\"3\"]':\n        positionId = '98';\n        break;\n      case '[\"1\",\"2\",\"3\"]':\n        positionId = '99';\n        break;\n      case '[\"4\",\"5\",\"6\"]':\n        positionId = '100';\n        break;\n      case '[\"7\",\"8\",\"9\"]':\n        positionId = '101';\n        break;\n      case '[\"10\",\"11\",\"12\"]':\n        positionId = '102';\n        break;\n      case '[\"13\",\"14\",\"15\"]':\n        positionId = '103';\n        break;\n      case '[\"16\",\"17\",\"18\"]':\n        positionId = '104';\n        break;\n      case '[\"19\",\"20\",\"21\"]':\n        positionId = '105';\n        break;\n      case '[\"22\",\"23\",\"24\"]':\n        positionId = '106';\n        break;\n      case '[\"25\",\"26\",\"27\"]':\n        positionId = '107';\n        break;\n      case '[\"28\",\"29\",\"30\"]':\n        positionId = '108';\n        break;\n      case '[\"31\",\"32\",\"33\"]':\n        positionId = '109';\n        break;\n      case '[\"34\",\"35\",\"36\"]':\n        positionId = '110';\n        break;\n      default:\n        break;\n    }\n\n    return {\n      bet,\n      win,\n      positionId\n    };\n  });\n};\n\nexport const transformCorner = ( bets: IOriginalBet[] ): ITransformedBet[] => {\n  return bets.map(( { bet, win, selector }: IOriginalBet ) => {\n    let positionId = '0';\n\n    switch (JSON.stringify(selector)) {\n      case '[\"0\",\"1\",\"2\",\"3\"]':\n        positionId = '111';\n        break;\n      case '[\"1\",\"2\",\"4\",\"5\"]':\n        positionId = '112';\n        break;\n      case '[\"2\",\"3\",\"5\",\"6\"]':\n        positionId = '113';\n        break;\n      case '[\"4\",\"5\",\"7\",\"8\"]':\n        positionId = '114';\n        break;\n      case '[\"5\",\"6\",\"8\",\"9\"]':\n        positionId = '115';\n        break;\n      case '[\"10\",\"11\",\"7\",\"8\"]':\n        positionId = '116';\n        break;\n      case '[\"11\",\"12\",\"8\",\"9\"]':\n        positionId = '117';\n        break;\n      case '[\"10\",\"11\",\"13\",\"14\"]':\n        positionId = '118';\n        break;\n      case '[\"11\",\"12\",\"14\",\"15\"]':\n        positionId = '119';\n        break;\n      case '[\"13\",\"14\",\"16\",\"17\"]':\n        positionId = '120';\n        break;\n      case '[\"14\",\"15\",\"17\",\"18\"]':\n        positionId = '121';\n        break;\n      case '[\"16\",\"17\",\"19\",\"20\"]':\n        positionId = '122';\n        break;\n      case '[\"17\",\"18\",\"20\",\"21\"]':\n        positionId = '123';\n        break;\n      case '[\"19\",\"20\",\"22\",\"23\"]':\n        positionId = '124';\n        break;\n      case '[\"20\",\"21\",\"23\",\"24\"]':\n        positionId = '125';\n        break;\n      case '[\"22\",\"23\",\"25\",\"26\"]':\n        positionId = '126';\n        break;\n      case '[\"23\",\"24\",\"26\",\"27\"]':\n        positionId = '127';\n        break;\n      case '[\"25\",\"26\",\"28\",\"29\"]':\n        positionId = '128';\n        break;\n      case '[\"26\",\"27\",\"29\",\"30\"]':\n        positionId = '129';\n        break;\n      case '[\"28\",\"29\",\"31\",\"32\"]':\n        positionId = '130';\n        break;\n      case '[\"29\",\"30\",\"32\",\"33\"]':\n        positionId = '131';\n        break;\n      case '[\"32\",\"32\",\"34\",\"36\"]':\n        positionId = '132';\n        break;\n      case '[\"32\",\"33\",\"35\",\"36\"]':\n        positionId = '133';\n        break;\n      default:\n        break;\n    }\n\n    return {\n      bet,\n      win,\n      positionId\n    };\n  });\n};\n\nexport const transformLine = ( bets: IOriginalBet[] ): ITransformedBet[] => {\n  return bets.map(( { bet, win, selector }: IOriginalBet ) => {\n    let positionId = '0';\n\n    switch (JSON.stringify(selector)) {\n      case '[\"1\",\"2\",\"3\",\"4\",\"5\",\"6\"]':\n        positionId = '134';\n        break;\n      case '[\"4\",\"5\",\"6\",\"7\",\"8\",\"9\"]':\n        positionId = '135';\n        break;\n      case '[\"10\",\"11\",\"12\",\"7\",\"8\",\"9\"]':\n        positionId = '136';\n        break;\n      case '[\"10\",\"11\",\"12\",\"13\",\"14\",\"15\"]':\n        positionId = '137';\n        break;\n      case '[\"13\",\"14\",\"15\",\"16\",\"17\",\"18\"]':\n        positionId = '138';\n        break;\n      case '[\"16\",\"17\",\"18\",\"19\",\"20\",\"21\"]':\n        positionId = '139';\n        break;\n      case '[\"19\",\"20\",\"21\",\"22\",\"23\",\"24\"]':\n        positionId = '140';\n        break;\n      case '[\"22\",\"23\",\"24\",\"25\",\"26\",\"27\"]':\n        positionId = '141';\n        break;\n      case '[\"25\",\"26\",\"27\",\"28\",\"29\",\"30\"]':\n        positionId = '142';\n        break;\n      case '[\"28\",\"29\",\"30\",\"31\",\"32\",\"33\"]':\n        positionId = '143';\n        break;\n      case '[\"31\",\"32\",\"33\",\"34\",\"35\",\"36\"]':\n        positionId = '144';\n        break;\n      default:\n        break;\n    }\n\n    return {\n      bet,\n      win,\n      positionId\n    };\n  });\n};\n\nexport const transformColumn = ( bets: IOriginalBet[] ): ITransformedBet[] => {\n  return bets.map(( { bet, win, selector }: IOriginalBet ) => {\n    let positionId = '145';\n\n    if (selector.includes('2')) {\n      positionId = '146';\n    } else if (selector.includes('3')) {\n      positionId = '147';\n    }\n\n    return {\n      bet,\n      win,\n      positionId\n    };\n  });\n};\n\nexport const transformDozen = ( bets: IOriginalBet[] ): ITransformedBet[] => {\n  return bets.map(( { bet, win, selector }: IOriginalBet ) => {\n    let positionId = '148';\n\n    if (selector.includes('13')) {\n      positionId = '149';\n    } else if (selector.includes('25')) {\n      positionId = '150';\n    }\n\n    return {\n      bet,\n      win,\n      positionId\n    };\n  });\n};\n\nexport const transformRed = ( bets: IOriginalBet[] ): ITransformedBet[] => {\n  return transformCommon(bets, '151');\n};\n\nexport const transformBlack = ( bets: IOriginalBet[] ): ITransformedBet[] => {\n  return transformCommon(bets, '152');\n};\n\nexport const transformLow = ( bets: IOriginalBet[] ): ITransformedBet[] => {\n  return transformCommon(bets, '153');\n};\n\nexport const transformHigh = ( bets: IOriginalBet[] ): ITransformedBet[] => {\n  return transformCommon(bets, '154');\n};\n\nexport const transformOdd = ( bets: IOriginalBet[] ): ITransformedBet[] => {\n  return transformCommon(bets, '155');\n};\n\nexport const transformEven = ( bets: IOriginalBet[] ): ITransformedBet[] => {\n  return transformCommon(bets, '156');\n};\n"], "mappings": "AAEA,MAAMA,eAAe,GAAGA,CAAEC,IAAoB,EAAEC,UAAkB,KAAwB;EACxF,OAAOD,IAAI,CAACE,GAAG,CAAC,CAAE;IAAEC,GAAG;IAAEC;EAAG,CAAE,MAC5B;IACED,GAAG;IACHC,GAAG;IACHH;GACD,CACF,CAAC;AACJ,CAAC;AAED,OAAO,MAAMI,iBAAiB,GAAKL,IAAoB,IAAwB;EAC7E,OAAOA,IAAI,CAACE,GAAG,CAAC,CAAE;IAAEC,GAAG;IAAEC,GAAG;IAAEE;EAAQ,CAAgB,MACpD;IACEH,GAAG;IACHC,GAAG;IACHH,UAAU,EAAEK,QAAQ,CAAC,CAAC;GACvB,CACF,CAAC;AACJ,CAAC;AAED,OAAO,MAAMC,cAAc,GAAKP,IAAoB,IAAwB;EAC1E,OAAOA,IAAI,CAACE,GAAG,CAAC,CAAE;IAAEC,GAAG;IAAEC,GAAG;IAAEE;EAAQ,CAAgB,KAAK;IACzD,IAAIL,UAAU,GAAG,GAAG;IAEpB,QAAQO,IAAI,CAACC,SAAS,CAACH,QAAQ,CAAC;MAC9B,KAAK,WAAW;QACdL,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,WAAW;QACdA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,WAAW;QACdA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,WAAW;QACdA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,WAAW;QACdA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,WAAW;QACdA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,WAAW;QACdA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,WAAW;QACdA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,WAAW;QACdA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,WAAW;QACdA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,WAAW;QACdA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,WAAW;QACdA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,WAAW;QACdA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,YAAY;QACfA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,WAAW;QACdA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,YAAY;QACfA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,WAAW;QACdA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,YAAY;QACfA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,aAAa;QAChBA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,aAAa;QAChBA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,aAAa;QAChBA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,aAAa;QAChBA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,aAAa;QAChBA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,aAAa;QAChBA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,aAAa;QAChBA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,aAAa;QAChBA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,aAAa;QAChBA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,aAAa;QAChBA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,aAAa;QAChBA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,aAAa;QAChBA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,aAAa;QAChBA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,aAAa;QAChBA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,aAAa;QAChBA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,aAAa;QAChBA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,aAAa;QAChBA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,aAAa;QAChBA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,aAAa;QAChBA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,aAAa;QAChBA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,aAAa;QAChBA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,aAAa;QAChBA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,aAAa;QAChBA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,aAAa;QAChBA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,aAAa;QAChBA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,aAAa;QAChBA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,aAAa;QAChBA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,aAAa;QAChBA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,aAAa;QAChBA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,aAAa;QAChBA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,aAAa;QAChBA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,aAAa;QAChBA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,aAAa;QAChBA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,aAAa;QAChBA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,aAAa;QAChBA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,aAAa;QAChBA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,aAAa;QAChBA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,aAAa;QAChBA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,aAAa;QAChBA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,aAAa;QAChBA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,aAAa;QAChBA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,aAAa;QAChBA,UAAU,GAAG,IAAI;QACjB;MACF;QACE;IACJ;IAEA,OAAO;MACLE,GAAG;MACHC,GAAG;MACHH;KACD;EACH,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMS,eAAe,GAAKV,IAAoB,IAAwB;EAC3E,OAAOA,IAAI,CAACE,GAAG,CAAC,CAAE;IAAEC,GAAG;IAAEC,GAAG;IAAEE;EAAQ,CAAgB,KAAK;IACzD,IAAIL,UAAU,GAAG,GAAG;IAEpB,QAAQO,IAAI,CAACC,SAAS,CAACH,QAAQ,CAAC;MAC9B,KAAK,eAAe;QAClBL,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,eAAe;QAClBA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,eAAe;QAClBA,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,eAAe;QAClBA,UAAU,GAAG,KAAK;QAClB;MACF,KAAK,eAAe;QAClBA,UAAU,GAAG,KAAK;QAClB;MACF,KAAK,kBAAkB;QACrBA,UAAU,GAAG,KAAK;QAClB;MACF,KAAK,kBAAkB;QACrBA,UAAU,GAAG,KAAK;QAClB;MACF,KAAK,kBAAkB;QACrBA,UAAU,GAAG,KAAK;QAClB;MACF,KAAK,kBAAkB;QACrBA,UAAU,GAAG,KAAK;QAClB;MACF,KAAK,kBAAkB;QACrBA,UAAU,GAAG,KAAK;QAClB;MACF,KAAK,kBAAkB;QACrBA,UAAU,GAAG,KAAK;QAClB;MACF,KAAK,kBAAkB;QACrBA,UAAU,GAAG,KAAK;QAClB;MACF,KAAK,kBAAkB;QACrBA,UAAU,GAAG,KAAK;QAClB;MACF,KAAK,kBAAkB;QACrBA,UAAU,GAAG,KAAK;QAClB;MACF;QACE;IACJ;IAEA,OAAO;MACLE,GAAG;MACHC,GAAG;MACHH;KACD;EACH,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMU,eAAe,GAAKX,IAAoB,IAAwB;EAC3E,OAAOA,IAAI,CAACE,GAAG,CAAC,CAAE;IAAEC,GAAG;IAAEC,GAAG;IAAEE;EAAQ,CAAgB,KAAK;IACzD,IAAIL,UAAU,GAAG,GAAG;IAEpB,QAAQO,IAAI,CAACC,SAAS,CAACH,QAAQ,CAAC;MAC9B,KAAK,mBAAmB;QACtBL,UAAU,GAAG,KAAK;QAClB;MACF,KAAK,mBAAmB;QACtBA,UAAU,GAAG,KAAK;QAClB;MACF,KAAK,mBAAmB;QACtBA,UAAU,GAAG,KAAK;QAClB;MACF,KAAK,mBAAmB;QACtBA,UAAU,GAAG,KAAK;QAClB;MACF,KAAK,mBAAmB;QACtBA,UAAU,GAAG,KAAK;QAClB;MACF,KAAK,qBAAqB;QACxBA,UAAU,GAAG,KAAK;QAClB;MACF,KAAK,qBAAqB;QACxBA,UAAU,GAAG,KAAK;QAClB;MACF,KAAK,uBAAuB;QAC1BA,UAAU,GAAG,KAAK;QAClB;MACF,KAAK,uBAAuB;QAC1BA,UAAU,GAAG,KAAK;QAClB;MACF,KAAK,uBAAuB;QAC1BA,UAAU,GAAG,KAAK;QAClB;MACF,KAAK,uBAAuB;QAC1BA,UAAU,GAAG,KAAK;QAClB;MACF,KAAK,uBAAuB;QAC1BA,UAAU,GAAG,KAAK;QAClB;MACF,KAAK,uBAAuB;QAC1BA,UAAU,GAAG,KAAK;QAClB;MACF,KAAK,uBAAuB;QAC1BA,UAAU,GAAG,KAAK;QAClB;MACF,KAAK,uBAAuB;QAC1BA,UAAU,GAAG,KAAK;QAClB;MACF,KAAK,uBAAuB;QAC1BA,UAAU,GAAG,KAAK;QAClB;MACF,KAAK,uBAAuB;QAC1BA,UAAU,GAAG,KAAK;QAClB;MACF,KAAK,uBAAuB;QAC1BA,UAAU,GAAG,KAAK;QAClB;MACF,KAAK,uBAAuB;QAC1BA,UAAU,GAAG,KAAK;QAClB;MACF,KAAK,uBAAuB;QAC1BA,UAAU,GAAG,KAAK;QAClB;MACF,KAAK,uBAAuB;QAC1BA,UAAU,GAAG,KAAK;QAClB;MACF,KAAK,uBAAuB;QAC1BA,UAAU,GAAG,KAAK;QAClB;MACF,KAAK,uBAAuB;QAC1BA,UAAU,GAAG,KAAK;QAClB;MACF;QACE;IACJ;IAEA,OAAO;MACLE,GAAG;MACHC,GAAG;MACHH;KACD;EACH,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMW,aAAa,GAAKZ,IAAoB,IAAwB;EACzE,OAAOA,IAAI,CAACE,GAAG,CAAC,CAAE;IAAEC,GAAG;IAAEC,GAAG;IAAEE;EAAQ,CAAgB,KAAK;IACzD,IAAIL,UAAU,GAAG,GAAG;IAEpB,QAAQO,IAAI,CAACC,SAAS,CAACH,QAAQ,CAAC;MAC9B,KAAK,2BAA2B;QAC9BL,UAAU,GAAG,KAAK;QAClB;MACF,KAAK,2BAA2B;QAC9BA,UAAU,GAAG,KAAK;QAClB;MACF,KAAK,8BAA8B;QACjCA,UAAU,GAAG,KAAK;QAClB;MACF,KAAK,iCAAiC;QACpCA,UAAU,GAAG,KAAK;QAClB;MACF,KAAK,iCAAiC;QACpCA,UAAU,GAAG,KAAK;QAClB;MACF,KAAK,iCAAiC;QACpCA,UAAU,GAAG,KAAK;QAClB;MACF,KAAK,iCAAiC;QACpCA,UAAU,GAAG,KAAK;QAClB;MACF,KAAK,iCAAiC;QACpCA,UAAU,GAAG,KAAK;QAClB;MACF,KAAK,iCAAiC;QACpCA,UAAU,GAAG,KAAK;QAClB;MACF,KAAK,iCAAiC;QACpCA,UAAU,GAAG,KAAK;QAClB;MACF,KAAK,iCAAiC;QACpCA,UAAU,GAAG,KAAK;QAClB;MACF;QACE;IACJ;IAEA,OAAO;MACLE,GAAG;MACHC,GAAG;MACHH;KACD;EACH,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMY,eAAe,GAAKb,IAAoB,IAAwB;EAC3E,OAAOA,IAAI,CAACE,GAAG,CAAC,CAAE;IAAEC,GAAG;IAAEC,GAAG;IAAEE;EAAQ,CAAgB,KAAK;IACzD,IAAIL,UAAU,GAAG,KAAK;IAEtB,IAAIK,QAAQ,CAACQ,QAAQ,CAAC,GAAG,CAAC,EAAE;MAC1Bb,UAAU,GAAG,KAAK;IACpB,CAAC,MAAM,IAAIK,QAAQ,CAACQ,QAAQ,CAAC,GAAG,CAAC,EAAE;MACjCb,UAAU,GAAG,KAAK;IACpB;IAEA,OAAO;MACLE,GAAG;MACHC,GAAG;MACHH;KACD;EACH,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMc,cAAc,GAAKf,IAAoB,IAAwB;EAC1E,OAAOA,IAAI,CAACE,GAAG,CAAC,CAAE;IAAEC,GAAG;IAAEC,GAAG;IAAEE;EAAQ,CAAgB,KAAK;IACzD,IAAIL,UAAU,GAAG,KAAK;IAEtB,IAAIK,QAAQ,CAACQ,QAAQ,CAAC,IAAI,CAAC,EAAE;MAC3Bb,UAAU,GAAG,KAAK;IACpB,CAAC,MAAM,IAAIK,QAAQ,CAACQ,QAAQ,CAAC,IAAI,CAAC,EAAE;MAClCb,UAAU,GAAG,KAAK;IACpB;IAEA,OAAO;MACLE,GAAG;MACHC,GAAG;MACHH;KACD;EACH,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMe,YAAY,GAAKhB,IAAoB,IAAwB;EACxE,OAAOD,eAAe,CAACC,IAAI,EAAE,KAAK,CAAC;AACrC,CAAC;AAED,OAAO,MAAMiB,cAAc,GAAKjB,IAAoB,IAAwB;EAC1E,OAAOD,eAAe,CAACC,IAAI,EAAE,KAAK,CAAC;AACrC,CAAC;AAED,OAAO,MAAMkB,YAAY,GAAKlB,IAAoB,IAAwB;EACxE,OAAOD,eAAe,CAACC,IAAI,EAAE,KAAK,CAAC;AACrC,CAAC;AAED,OAAO,MAAMmB,aAAa,GAAKnB,IAAoB,IAAwB;EACzE,OAAOD,eAAe,CAACC,IAAI,EAAE,KAAK,CAAC;AACrC,CAAC;AAED,OAAO,MAAMoB,YAAY,GAAKpB,IAAoB,IAAwB;EACxE,OAAOD,eAAe,CAACC,IAAI,EAAE,KAAK,CAAC;AACrC,CAAC;AAED,OAAO,MAAMqB,aAAa,GAAKrB,IAAoB,IAAwB;EACzE,OAAOD,eAAe,CAACC,IAAI,EAAE,KAAK,CAAC;AACrC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}