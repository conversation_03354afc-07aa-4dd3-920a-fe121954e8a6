{"ast": null, "code": "import { VerticalTabsBodyComponent } from '../vertical-tabs-body/vertical-tabs-body.component';\nimport { VerticalTabsLabelComponent } from '../vertical-tabs-label/vertical-tabs-label.component';\nimport * as i0 from \"@angular/core\";\nconst _c0 = [\"*\"];\nexport class VerticalTabsItemComponent {\n  constructor() {\n    this.bodyComponent = null;\n    this.labelComponent = null;\n  }\n  static {\n    this.ɵfac = function VerticalTabsItemComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || VerticalTabsItemComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: VerticalTabsItemComponent,\n      selectors: [[\"sw-vertical-tabs-item\"]],\n      contentQueries: function VerticalTabsItemComponent_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, VerticalTabsBodyComponent, 5);\n          i0.ɵɵcontentQuery(dirIndex, VerticalTabsLabelComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.bodyComponent = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.labelComponent = _t.first);\n        }\n      },\n      inputs: {\n        label: \"label\",\n        isActive: \"isActive\"\n      },\n      ngContentSelectors: _c0,\n      decls: 1,\n      vars: 0,\n      template: function VerticalTabsItemComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n        }\n      },\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["VerticalTabsBodyComponent", "VerticalTabsLabelComponent", "VerticalTabsItemComponent", "constructor", "bodyComponent", "labelComponent", "selectors", "contentQueries", "VerticalTabsItemComponent_ContentQueries", "rf", "ctx", "dirIndex", "i0", "ɵɵprojection"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/common/components/vertical-tabs/vertical-tabs-item/vertical-tabs-item.component.ts", "/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/common/components/vertical-tabs/vertical-tabs-item/vertical-tabs-item.component.html"], "sourcesContent": ["import { Component, ContentChild, Input } from '@angular/core';\nimport { VerticalTabsBodyComponent } from '../vertical-tabs-body/vertical-tabs-body.component';\nimport { VerticalTabsLabelComponent } from '../vertical-tabs-label/vertical-tabs-label.component';\n\n\n\n@Component({\n  selector: 'sw-vertical-tabs-item',\n  templateUrl: './vertical-tabs-item.component.html',\n})\nexport class VerticalTabsItemComponent {\n\n  @Input() label?: string;\n  @Input() isActive?: boolean;\n\n  @ContentChild(VerticalTabsBodyComponent) bodyComponent: VerticalTabsBodyComponent | null;\n  @ContentChild(VerticalTabsLabelComponent) labelComponent: VerticalTabsLabelComponent | null;\n\n  constructor() {\n    this.bodyComponent = null;\n    this.labelComponent = null;\n  }\n}\n", "<ng-content></ng-content>\n"], "mappings": "AACA,SAASA,yBAAyB,QAAQ,oDAAoD;AAC9F,SAASC,0BAA0B,QAAQ,sDAAsD;;;AAQjG,OAAM,MAAOC,yBAAyB;EAQpCC,YAAA;IACE,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,cAAc,GAAG,IAAI;EAC5B;;;uCAXWH,yBAAyB;IAAA;EAAA;;;YAAzBA,yBAAyB;MAAAI,SAAA;MAAAC,cAAA,WAAAC,yCAAAC,EAAA,EAAAC,GAAA,EAAAC,QAAA;QAAA,IAAAF,EAAA;sCAKtBT,yBAAyB;sCACzBC,0BAA0B;;;;;;;;;;;;;;;;;;UChB1CW,EAAA,CAAAC,YAAA,GAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}