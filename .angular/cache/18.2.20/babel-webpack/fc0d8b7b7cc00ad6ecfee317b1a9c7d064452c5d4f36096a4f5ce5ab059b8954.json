{"ast": null, "code": "export const SCHEMA = [{\n  field: 'address',\n  title: 'ENTITY_SETUP.WHITELISTING.gridSiteTitle',\n  type: 'string',\n  isList: true,\n  isViewable: false,\n  isSortable: false,\n  alignment: {\n    th: 'full-width',\n    td: 'full-width'\n  },\n  td: {\n    type: 'calc',\n    titleFn: row => {\n      let title = row.address;\n      if (row.isParentItem()) {\n        title += ' (inherited)';\n      }\n      if (row.isDraftItem()) {\n        title += ' (not saved)';\n      }\n      return title;\n    },\n    classFn: row => {\n      let extraClasses;\n      if (row.isDraftItem()) {\n        extraClasses = 'text-warning';\n      }\n      return extraClasses;\n    }\n  }\n}, {\n  field: 'issueId',\n  title: 'ACTIVITY_LOG.GRID.initiatorIssueId',\n  type: 'issue',\n  isList: true,\n  isViewable: false,\n  isSortable: false\n}];", "map": {"version": 3, "names": ["SCHEMA", "field", "title", "type", "isList", "isViewable", "isSortable", "alignment", "th", "td", "titleFn", "row", "address", "isParentItem", "isDraftItem", "classFn", "extraClasses"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-2fa/entity-ip-whitelist/ip-whitelist.schema.ts"], "sourcesContent": ["import { SwuiGridField } from '@skywind-group/lib-swui';\nimport { WhitelistIp } from '../../../../../../common/models/whitelist-ip.model';\n\n\nexport const SCHEMA: SwuiGridField[] = [\n  {\n    field: 'address',\n    title: 'ENTITY_SETUP.WHITELISTING.gridSiteTitle',\n    type: 'string',\n    isList: true,\n    isViewable: false,\n    isSortable: false,\n    alignment: {\n      th: 'full-width',\n      td: 'full-width',\n    },\n    td: {\n      type: 'calc',\n      titleFn: ( row: WhitelistIp ) => {\n        let title = row.address;\n        if (row.isParentItem()) {\n          title += ' (inherited)';\n        }\n        if (row.isDraftItem()) {\n          title += ' (not saved)';\n        }\n        return title;\n      },\n      classFn: ( row ) => {\n        let extraClasses;\n        if (row.isDraftItem()) {\n          extraClasses = 'text-warning';\n        }\n        return extraClasses;\n      },\n    }\n  },\n  {\n    field: 'issueId',\n    title: 'ACTIVITY_LOG.GRID.initiatorIssueId',\n    type: 'issue',\n    isList: true,\n    isViewable: false,\n    isSortable: false,\n  },\n];\n"], "mappings": "AAIA,OAAO,MAAMA,MAAM,GAAoB,CACrC;EACEC,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,yCAAyC;EAChDC,IAAI,EAAE,QAAQ;EACdC,MAAM,EAAE,IAAI;EACZC,UAAU,EAAE,KAAK;EACjBC,UAAU,EAAE,KAAK;EACjBC,SAAS,EAAE;IACTC,EAAE,EAAE,YAAY;IAChBC,EAAE,EAAE;GACL;EACDA,EAAE,EAAE;IACFN,IAAI,EAAE,MAAM;IACZO,OAAO,EAAIC,GAAgB,IAAK;MAC9B,IAAIT,KAAK,GAAGS,GAAG,CAACC,OAAO;MACvB,IAAID,GAAG,CAACE,YAAY,EAAE,EAAE;QACtBX,KAAK,IAAI,cAAc;MACzB;MACA,IAAIS,GAAG,CAACG,WAAW,EAAE,EAAE;QACrBZ,KAAK,IAAI,cAAc;MACzB;MACA,OAAOA,KAAK;IACd,CAAC;IACDa,OAAO,EAAIJ,GAAG,IAAK;MACjB,IAAIK,YAAY;MAChB,IAAIL,GAAG,CAACG,WAAW,EAAE,EAAE;QACrBE,YAAY,GAAG,cAAc;MAC/B;MACA,OAAOA,YAAY;IACrB;;CAEH,EACD;EACEf,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,oCAAoC;EAC3CC,IAAI,EAAE,OAAO;EACbC,MAAM,EAAE,IAAI;EACZC,UAAU,EAAE,KAAK;EACjBC,UAAU,EAAE;CACb,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}