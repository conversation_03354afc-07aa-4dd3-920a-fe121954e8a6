{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { ControlMessagesModule } from '../../../../../../common/components/control-messages/control-messages.module';\nimport { TouchspinModule } from '../../../../../../common/components/touchspin/touchspin.module';\nimport { TrimInputValueModule } from '../../../../../../common/directives/trim-input-value/trim-input-value.module';\nimport { FormComponent } from './form.component';\nimport { ManageBalanceComponent } from './manage-balance.component';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport * as i0 from \"@angular/core\";\nexport const matModules = [MatDialogModule, MatButtonModule, MatFormFieldModule, MatSelectModule, MatInputModule, MatProgressSpinnerModule];\nexport class ManageBalanceModule {\n  static {\n    this.ɵfac = function ManageBalanceModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ManageBalanceModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ManageBalanceModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, FormsModule, ReactiveFormsModule, TranslateModule, TouchspinModule, ControlMessagesModule, matModules, TrimInputValueModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ManageBalanceModule, {\n    declarations: [ManageBalanceComponent, FormComponent],\n    imports: [CommonModule, FormsModule, ReactiveFormsModule, TranslateModule, TouchspinModule, ControlMessagesModule, MatDialogModule, MatButtonModule, MatFormFieldModule, MatSelectModule, MatInputModule, MatProgressSpinnerModule, TrimInputValueModule],\n    exports: [FormComponent]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "ReactiveFormsModule", "MatProgressSpinnerModule", "TranslateModule", "ControlMessagesModule", "TouchspinModule", "TrimInputValueModule", "FormComponent", "ManageBalanceComponent", "MatFormFieldModule", "MatSelectModule", "MatDialogModule", "MatInputModule", "MatButtonModule", "matModules", "ManageBalanceModule", "declarations", "imports", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/mat-business-structure/dialogs/manage-balance/manage-balance.module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { ControlMessagesModule } from '../../../../../../common/components/control-messages/control-messages.module';\nimport { TouchspinModule } from '../../../../../../common/components/touchspin/touchspin.module';\nimport { TrimInputValueModule } from '../../../../../../common/directives/trim-input-value/trim-input-value.module';\nimport { FormComponent } from './form.component';\nimport { ManageBalanceComponent } from './manage-balance.component';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\n\nexport const matModules = [\n  MatDialogModule,\n  MatButtonModule,\n  MatFormFieldModule,\n  MatSelectModule,\n  MatInputModule,\n  MatProgressSpinnerModule,\n];\n\n@NgModule({\n    imports: [\n        CommonModule,\n        FormsModule,\n        ReactiveFormsModule,\n        TranslateModule,\n        TouchspinModule,\n        ControlMessagesModule,\n        ...matModules,\n        TrimInputValueModule,\n\n    ],\n  declarations: [\n    ManageBalanceComponent,\n    FormComponent,\n  ],\n  exports: [\n    FormComponent\n  ],\n})\nexport class ManageBalanceModule {\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,qBAAqB,QAAQ,8EAA8E;AACpH,SAASC,eAAe,QAAQ,gEAAgE;AAChG,SAASC,oBAAoB,QAAQ,8EAA8E;AACnH,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,sBAAsB,QAAQ,4BAA4B;AACnE,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;;AAE1D,OAAO,MAAMC,UAAU,GAAG,CACxBH,eAAe,EACfE,eAAe,EACfJ,kBAAkB,EAClBC,eAAe,EACfE,cAAc,EACdV,wBAAwB,CACzB;AAsBD,OAAM,MAAOa,mBAAmB;;;uCAAnBA,mBAAmB;IAAA;EAAA;;;YAAnBA;IAAmB;EAAA;;;gBAlBxBhB,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBE,eAAe,EACfE,eAAe,EACfD,qBAAqB,EAClBU,UAAU,EACbR,oBAAoB;IAAA;EAAA;;;2EAWfS,mBAAmB;IAAAC,YAAA,GAP5BR,sBAAsB,EACtBD,aAAa;IAAAU,OAAA,GAZTlB,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBE,eAAe,EACfE,eAAe,EACfD,qBAAqB,EAf3BO,eAAe,EACfE,eAAe,EACfJ,kBAAkB,EAClBC,eAAe,EACfE,cAAc,EACdV,wBAAwB,EAYlBI,oBAAoB;IAAAY,OAAA,GAQxBX,aAAa;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}