{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FlexModule } from '@angular/flex-layout';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiGridModule, SwuiNotificationsModule, SwuiSchemaTopFilterModule } from '@skywind-group/lib-swui';\nimport { DownloadCsvModule } from '../../../../common/components/download-csv/download-csv.module';\nimport { GameService } from '../../../../common/services/game.service';\nimport { GameNotifyModule } from '../game-notify/game-notify.module';\nimport { IframeViewModalModule } from '../internal-game-history/modals/iframe-view-modal/iframe-view-modal.module';\nimport { RoundInfoViewModalModule } from '../internal-game-history/modals/round-info-view-modal.module';\nimport { GameHistoryGeneralComponent } from './game-history-general.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@skywind-group/lib-swui\";\nexport class GameHistoryGeneralModule {\n  static {\n    this.ɵfac = function GameHistoryGeneralModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || GameHistoryGeneralModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: GameHistoryGeneralModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [GameService],\n      imports: [CommonModule, FlexModule, TranslateModule, SwuiGridModule, SwuiNotificationsModule.forRoot(), RoundInfoViewModalModule, GameNotifyModule, MatTooltipModule, SwuiSchemaTopFilterModule, IframeViewModalModule, DownloadCsvModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(GameHistoryGeneralModule, {\n    declarations: [GameHistoryGeneralComponent],\n    imports: [CommonModule, FlexModule, TranslateModule, SwuiGridModule, i1.SwuiNotificationsModule, RoundInfoViewModalModule, GameNotifyModule, MatTooltipModule, SwuiSchemaTopFilterModule, IframeViewModalModule, DownloadCsvModule],\n    exports: [GameHistoryGeneralComponent]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FlexModule", "MatTooltipModule", "TranslateModule", "SwuiGridModule", "SwuiNotificationsModule", "SwuiSchemaTopFilterModule", "DownloadCsvModule", "GameService", "GameNotifyModule", "IframeViewModalModule", "RoundInfoViewModalModule", "GameHistoryGeneralComponent", "GameHistoryGeneralModule", "imports", "forRoot", "declarations", "i1", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/gamehistory/components/game-history-general/game-history-general.module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { FlexModule } from '@angular/flex-layout';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiGridModule, SwuiNotificationsModule, SwuiSchemaTopFilterModule } from '@skywind-group/lib-swui';\nimport { DownloadCsvModule } from '../../../../common/components/download-csv/download-csv.module';\nimport { GameService } from '../../../../common/services/game.service';\nimport { GameNotifyModule } from '../game-notify/game-notify.module';\n\nimport { IframeViewModalModule } from '../internal-game-history/modals/iframe-view-modal/iframe-view-modal.module';\nimport { RoundInfoViewModalModule } from '../internal-game-history/modals/round-info-view-modal.module';\nimport { GameHistoryGeneralComponent } from './game-history-general.component';\n\n\n@NgModule({\n  declarations: [\n    GameHistoryGeneralComponent,\n  ],\n  exports: [\n    GameHistoryGeneralComponent,\n  ],\n  imports: [\n    CommonModule,\n    FlexModule,\n    TranslateModule,\n    SwuiGridModule,\n    SwuiNotificationsModule.forRoot(),\n    RoundInfoViewModalModule,\n    GameNotifyModule,\n    MatTooltipModule,\n    SwuiSchemaTopFilterModule,\n    IframeViewModalModule,\n    DownloadCsvModule,\n  ],\n  providers: [\n    GameService,\n  ],\n})\nexport class GameHistoryGeneralModule {\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,UAAU,QAAQ,sBAAsB;AACjD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,cAAc,EAAEC,uBAAuB,EAAEC,yBAAyB,QAAQ,yBAAyB;AAC5G,SAASC,iBAAiB,QAAQ,gEAAgE;AAClG,SAASC,WAAW,QAAQ,0CAA0C;AACtE,SAASC,gBAAgB,QAAQ,mCAAmC;AAEpE,SAASC,qBAAqB,QAAQ,4EAA4E;AAClH,SAASC,wBAAwB,QAAQ,8DAA8D;AACvG,SAASC,2BAA2B,QAAQ,kCAAkC;;;AA2B9E,OAAM,MAAOC,wBAAwB;;;uCAAxBA,wBAAwB;IAAA;EAAA;;;YAAxBA;IAAwB;EAAA;;;iBAJxB,CACTL,WAAW,CACZ;MAAAM,OAAA,GAdCd,YAAY,EACZC,UAAU,EACVE,eAAe,EACfC,cAAc,EACdC,uBAAuB,CAACU,OAAO,EAAE,EACjCJ,wBAAwB,EACxBF,gBAAgB,EAChBP,gBAAgB,EAChBI,yBAAyB,EACzBI,qBAAqB,EACrBH,iBAAiB;IAAA;EAAA;;;2EAMRM,wBAAwB;IAAAG,YAAA,GAtBjCJ,2BAA2B;IAAAE,OAAA,GAM3Bd,YAAY,EACZC,UAAU,EACVE,eAAe,EACfC,cAAc,EAAAa,EAAA,CAAAZ,uBAAA,EAEdM,wBAAwB,EACxBF,gBAAgB,EAChBP,gBAAgB,EAChBI,yBAAyB,EACzBI,qBAAqB,EACrBH,iBAAiB;IAAAW,OAAA,GAbjBN,2BAA2B;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}