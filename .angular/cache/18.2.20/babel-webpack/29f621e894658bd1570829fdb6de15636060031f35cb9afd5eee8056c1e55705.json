{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { FormControl, FormGroup } from '@angular/forms';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/material/slide-toggle\";\nimport * as i3 from \"@ngx-translate/core\";\nexport class LobbyMenuItemsOptionsComponent {\n  set setSubmitted(val) {\n    if (val) {\n      this.form.markAllAsTouched();\n    }\n  }\n  set setOptions(val) {\n    this.form.patchValue({\n      isCommissionFilter: val?.isCommissionFilter ?? false,\n      isGridLayout: val?.isGridLayout ?? false\n    }, {\n      emitEvent: false\n    });\n  }\n  constructor() {\n    this.optionsChanged = new EventEmitter();\n    this.validStatusChange = new EventEmitter();\n    this.destroyed$ = new Subject();\n    this.form = new FormGroup({\n      isCommissionFilter: new FormControl(false),\n      isGridLayout: new FormControl(false)\n    });\n  }\n  ngOnInit() {\n    this.form.valueChanges.pipe(takeUntil(this.destroyed$)).subscribe(val => {\n      if (this.form.valid) {\n        this.optionsChanged.emit(val);\n      }\n    });\n    this.form.statusChanges.pipe(takeUntil(this.destroyed$)).subscribe(result => {\n      this.validStatusChange.emit(result === 'VALID');\n    });\n  }\n  ngOnDestroy() {\n    this.destroyed$.next();\n    this.destroyed$.complete();\n  }\n  static {\n    this.ɵfac = function LobbyMenuItemsOptionsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LobbyMenuItemsOptionsComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LobbyMenuItemsOptionsComponent,\n      selectors: [[\"lobby-menu-items-options\"]],\n      inputs: {\n        setSubmitted: [0, \"submitted\", \"setSubmitted\"],\n        setOptions: [0, \"options\", \"setOptions\"]\n      },\n      outputs: {\n        optionsChanged: \"optionsChanged\",\n        validStatusChange: \"validStatusChange\"\n      },\n      decls: 11,\n      vars: 7,\n      consts: [[3, \"formGroup\"], [1, \"section\"], [1, \"section__label\"], [\"formControlName\", \"isCommissionFilter\", \"color\", \"primary\"], [\"formControlName\", \"isGridLayout\", \"color\", \"primary\"]],\n      template: function LobbyMenuItemsOptionsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"form\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵtext(3);\n          i0.ɵɵpipe(4, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(5, \"mat-slide-toggle\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 1)(7, \"div\", 2);\n          i0.ɵɵtext(8);\n          i0.ɵɵpipe(9, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(10, \"mat-slide-toggle\", 4);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"formGroup\", ctx.form);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(4, 3, \"LOBBY.MENU_ITEMS.showCommissionFilter\"), \":\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(9, 5, \"LOBBY.MENU_ITEMS.useGridLayout\"), \":\");\n        }\n      },\n      dependencies: [i1.ɵNgNoValidate, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i2.MatSlideToggle, i3.TranslatePipe],\n      styles: [\".section[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin: 10px 0 16px;\\n}\\n.section__label[_ngcontent-%COMP%] {\\n  margin-right: 16px;\\n}\\n\\n.inline-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n.inline-group__label[_ngcontent-%COMP%] {\\n  margin-right: 20px;\\n  margin-bottom: 4px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvbG9iYnkvbG9iYnktZm9ybS9sb2JieS1tZW51LWl0ZW1zL2xvYmJ5LW1lbnUtaXRlbXMtc2V0dXAvbG9iYnktbWVudS1pdGVtcy1vcHRpb25zL2xvYmJ5LW1lbnUtaXRlbXMtb3B0aW9ucy5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLG1CQUFBO0FBQ0Y7QUFBRTtFQUNFLGtCQUFBO0FBRUo7O0FBRUE7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7QUFDRjtBQUFFO0VBQ0Usa0JBQUE7RUFDQSxrQkFBQTtBQUVKIiwic291cmNlc0NvbnRlbnQiOlsiLnNlY3Rpb24ge1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBtYXJnaW46IDEwcHggMCAxNnB4O1xuICAmX19sYWJlbCB7XG4gICAgbWFyZ2luLXJpZ2h0OiAxNnB4O1xuICB9XG59XG5cbi5pbmxpbmUtZ3JvdXAge1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAmX19sYWJlbCB7XG4gICAgbWFyZ2luLXJpZ2h0OiAyMHB4O1xuICAgIG1hcmdpbi1ib3R0b206IDRweDtcbiAgfVxufVxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"],\n      changeDetection: 0\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "FormControl", "FormGroup", "Subject", "takeUntil", "LobbyMenuItemsOptionsComponent", "setSubmitted", "val", "form", "mark<PERSON>llAsTouched", "setOptions", "patchValue", "isCommissionFilter", "isGridLayout", "emitEvent", "constructor", "optionsChanged", "validStatus<PERSON>hange", "destroyed$", "ngOnInit", "valueChanges", "pipe", "subscribe", "valid", "emit", "statusChanges", "result", "ngOnDestroy", "next", "complete", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "LobbyMenuItemsOptionsComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵproperty", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵpipeBind1"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/lobby/lobby-form/lobby-menu-items/lobby-menu-items-setup/lobby-menu-items-options/lobby-menu-items-options.component.ts", "/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/lobby/lobby-form/lobby-menu-items/lobby-menu-items-setup/lobby-menu-items-options/lobby-menu-items-options.component.html"], "sourcesContent": ["import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';\nimport { FormControl, FormGroup } from '@angular/forms';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { LobbyWidget } from '../../../../../../common/services/lobby-widgets.service';\nimport { LobbyMenuItemOptions } from '../../../../lobby.model';\n\nexport interface LobbyWidgets {\n  [tag: string]: LobbyWidget;\n}\n\nexport interface WidgetOptions {\n  [prop: string]: any;\n}\n\n@Component({\n  selector: 'lobby-menu-items-options',\n  templateUrl: './lobby-menu-items-options.component.html',\n  styleUrls: ['./lobby-menu-items-options.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class LobbyMenuItemsOptionsComponent implements OnInit, OnDestroy {\n  readonly form: FormGroup;\n\n  @Input('submitted')\n  set setSubmitted( val: boolean | undefined ) {\n    if (val) {\n      this.form.markAllAsTouched();\n    }\n  }\n\n  @Input('options')\n  set setOptions( val: LobbyMenuItemOptions | undefined ) {\n    this.form.patchValue({\n      isCommissionFilter: val?.isCommissionFilter ?? false,\n      isGridLayout: val?.isGridLayout ?? false\n    }, { emitEvent: false });\n  }\n\n  @Output() optionsChanged = new EventEmitter<LobbyMenuItemOptions>();\n  @Output() validStatusChange = new EventEmitter<boolean>();\n\n  private readonly destroyed$ = new Subject<void>();\n\n  constructor() {\n    this.form = new FormGroup({\n      isCommissionFilter: new FormControl(false),\n      isGridLayout: new FormControl(false)\n    });\n  }\n\n  ngOnInit(): void {\n    this.form.valueChanges.pipe(\n      takeUntil(this.destroyed$)\n    ).subscribe(val => {\n      if (this.form.valid) {\n        this.optionsChanged.emit(val);\n      }\n    });\n\n    this.form.statusChanges.pipe(\n      takeUntil(this.destroyed$)\n    ).subscribe(result => {\n      this.validStatusChange.emit(result === 'VALID');\n    });\n  }\n\n  ngOnDestroy() {\n    this.destroyed$.next();\n    this.destroyed$.complete();\n  }\n}\n", "<form [formGroup]=\"form\">\n  <div class=\"section\">\n    <div class=\"section__label\">{{'LOBBY.MENU_ITEMS.showCommissionFilter' | translate}}:</div>\n    <mat-slide-toggle formControlName=\"isCommissionFilter\" color=\"primary\"></mat-slide-toggle>\n  </div>\n  <div class=\"section\">\n    <div class=\"section__label\">{{'LOBBY.MENU_ITEMS.useGridLayout' | translate}}:</div>\n    <mat-slide-toggle formControlName=\"isGridLayout\" color=\"primary\"></mat-slide-toggle>\n  </div>\n</form>\n"], "mappings": "AAAA,SAA6CA,YAAY,QAA0C,eAAe;AAClH,SAASC,WAAW,EAAEC,SAAS,QAAQ,gBAAgB;AACvD,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,SAAS,QAAQ,gBAAgB;;;;;AAkB1C,OAAM,MAAOC,8BAA8B;EAGzC,IACIC,YAAYA,CAAEC,GAAwB;IACxC,IAAIA,GAAG,EAAE;MACP,IAAI,CAACC,IAAI,CAACC,gBAAgB,EAAE;IAC9B;EACF;EAEA,IACIC,UAAUA,CAAEH,GAAqC;IACnD,IAAI,CAACC,IAAI,CAACG,UAAU,CAAC;MACnBC,kBAAkB,EAAEL,GAAG,EAAEK,kBAAkB,IAAI,KAAK;MACpDC,YAAY,EAAEN,GAAG,EAAEM,YAAY,IAAI;KACpC,EAAE;MAAEC,SAAS,EAAE;IAAK,CAAE,CAAC;EAC1B;EAOAC,YAAA;IALU,KAAAC,cAAc,GAAG,IAAIhB,YAAY,EAAwB;IACzD,KAAAiB,iBAAiB,GAAG,IAAIjB,YAAY,EAAW;IAExC,KAAAkB,UAAU,GAAG,IAAIf,OAAO,EAAQ;IAG/C,IAAI,CAACK,IAAI,GAAG,IAAIN,SAAS,CAAC;MACxBU,kBAAkB,EAAE,IAAIX,WAAW,CAAC,KAAK,CAAC;MAC1CY,YAAY,EAAE,IAAIZ,WAAW,CAAC,KAAK;KACpC,CAAC;EACJ;EAEAkB,QAAQA,CAAA;IACN,IAAI,CAACX,IAAI,CAACY,YAAY,CAACC,IAAI,CACzBjB,SAAS,CAAC,IAAI,CAACc,UAAU,CAAC,CAC3B,CAACI,SAAS,CAACf,GAAG,IAAG;MAChB,IAAI,IAAI,CAACC,IAAI,CAACe,KAAK,EAAE;QACnB,IAAI,CAACP,cAAc,CAACQ,IAAI,CAACjB,GAAG,CAAC;MAC/B;IACF,CAAC,CAAC;IAEF,IAAI,CAACC,IAAI,CAACiB,aAAa,CAACJ,IAAI,CAC1BjB,SAAS,CAAC,IAAI,CAACc,UAAU,CAAC,CAC3B,CAACI,SAAS,CAACI,MAAM,IAAG;MACnB,IAAI,CAACT,iBAAiB,CAACO,IAAI,CAACE,MAAM,KAAK,OAAO,CAAC;IACjD,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACT,UAAU,CAACU,IAAI,EAAE;IACtB,IAAI,CAACV,UAAU,CAACW,QAAQ,EAAE;EAC5B;;;uCAjDWxB,8BAA8B;IAAA;EAAA;;;YAA9BA,8BAA8B;MAAAyB,SAAA;MAAAC,MAAA;QAAAzB,YAAA;QAAAI,UAAA;MAAA;MAAAsB,OAAA;QAAAhB,cAAA;QAAAC,iBAAA;MAAA;MAAAgB,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCnBvCE,EAFJ,CAAAC,cAAA,cAAyB,aACF,aACS;UAAAD,EAAA,CAAAE,MAAA,GAAwD;;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC1FH,EAAA,CAAAI,SAAA,0BAA0F;UAC5FJ,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,aAAqB,aACS;UAAAD,EAAA,CAAAE,MAAA,GAAiD;;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACnFH,EAAA,CAAAI,SAAA,2BAAoF;UAExFJ,EADE,CAAAG,YAAA,EAAM,EACD;;;UATDH,EAAA,CAAAK,UAAA,cAAAN,GAAA,CAAA/B,IAAA,CAAkB;UAEQgC,EAAA,CAAAM,SAAA,GAAwD;UAAxDN,EAAA,CAAAO,kBAAA,KAAAP,EAAA,CAAAQ,WAAA,qDAAwD;UAIxDR,EAAA,CAAAM,SAAA,GAAiD;UAAjDN,EAAA,CAAAO,kBAAA,KAAAP,EAAA,CAAAQ,WAAA,8CAAiD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}