{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { Validators } from '@angular/forms';\nimport { BehaviorSubject, Subject } from 'rxjs';\nimport { distinctUntilChanged, filter, finalize, map, switchMap, takeUntil, tap } from 'rxjs/operators';\nimport { PERMISSIONS_NAMES } from '../../../app.constants';\nimport { USER_CHANGE_PASSWORD_PERIOD_TYPE_LIST, USER_STATUS_LIST, USER_TYPES_MAP } from '../../../pages/business-management/components/entities/tab-users/users.schema';\nimport { Role } from '../../../pages/users/components/roles/role.model';\nimport { User } from '../../../pages/users/user.model';\nimport { entitiesStructureToSelectOptions } from '../../services/entity.service';\nimport { ValidationService } from '../../services/validation.service';\nimport { BoConfirmationComponent } from '../bo-confirmation/bo-confirmation.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@skywind-group/lib-swui\";\nimport * as i3 from \"../../services/entity.service\";\nimport * as i4 from \"@angular/material/dialog\";\nimport * as i5 from \"./user-editor.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/material/form-field\";\nimport * as i8 from \"@angular/material/input\";\nimport * as i9 from \"@angular/flex-layout/flex\";\nimport * as i10 from \"@angular/material/progress-spinner\";\nimport * as i11 from \"@angular/material/select\";\nimport * as i12 from \"@angular/material/core\";\nimport * as i13 from \"@angular/material/radio\";\nimport * as i14 from \"@angular/material/card\";\nimport * as i15 from \"../../directives/trim-input-value/trim-input-value.component\";\nimport * as i16 from \"@ngx-translate/core\";\nfunction UserFormComponent_mat_form_field_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-form-field\", 25);\n    i0.ɵɵelement(1, \"lib-swui-select\", 26);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementStart(3, \"mat-error\");\n    i0.ɵɵelement(4, \"lib-swui-control-messages\", 7);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(2, 6, \"INTEGRATIONS.selectOperator\"));\n    i0.ɵɵproperty(\"disableEmptyOption\", true)(\"showSearch\", (ctx_r1.entityOptions == null ? null : ctx_r1.entityOptions.length) > 10)(\"data\", ctx_r1.entityOptions);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"messages\", ctx_r1.messageErrors)(\"control\", ctx_r1.entityControl);\n  }\n}\nfunction UserFormComponent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"mat-label\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"mat-progress-spinner\", 27);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const load_r3 = ctx.ngIf;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", i0.ɵɵpipeBind1(3, 5, \"ALL.IN_PROGRESS\"), \" \", load_r3, \" qqqq\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", 50)(\"diameter\", 20)(\"mode\", \"indeterminate\");\n  }\n}\nfunction UserFormComponent_ng_template_63_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-label\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"mat-progress-spinner\", 27);\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 4, \"ALL.IN_PROGRESS\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", 50)(\"diameter\", 40)(\"mode\", \"indeterminate\");\n  }\n}\nfunction UserFormComponent_mat_form_field_66_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-form-field\", 14)(1, \"mat-label\", 28);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"lib-swui-chips-autocomplete\", 29);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 3, \"ENTITY_SETUP.USERS.MODALS.additionalroles\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", true)(\"value\", ctx_r1.unsharedRoles);\n  }\n}\nfunction UserFormComponent_mat_option_73_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 21);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r4.code);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, status_r4.displayName), \" \");\n  }\n}\nfunction UserFormComponent_mat_option_95_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 21);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const periodType_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", periodType_r5.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, periodType_r5.displayName), \" \");\n  }\n}\nexport class UserFormComponent {\n  set roles(values) {\n    if (!Array.isArray(values)) return;\n    this._roles = values;\n    this.roleSelectOptions = values.map(role => new Role(role)).map(role => role.toSelectOption()).sort((a, b) => {\n      return a.text.localeCompare(b.text);\n    });\n  }\n  get roles() {\n    return this._roles;\n  }\n  set excludedEmails(emails) {\n    if (!Array.isArray(emails)) return;\n    this._excludedEmails = emails;\n    this.updateEmailValidators();\n  }\n  get excludedEmails() {\n    return this._excludedEmails;\n  }\n  set excludedPasswords(passwords) {\n    if (!Array.isArray(passwords)) return;\n    this._excludedPasswords = passwords;\n    this.updatePasswordValidators();\n  }\n  get excludedPasswords() {\n    return this._excludedPasswords;\n  }\n  set selectedRoles(value) {\n    if (!value) {\n      return;\n    }\n    value.sort((a, b) => {\n      return a.title.localeCompare(b.title);\n    });\n    if (Array.isArray(value) && value.length) {\n      this.rolesControl.patchValue(value.map(item => item.id));\n    }\n    this._selectedRoles = value;\n  }\n  get selectedRoles() {\n    return this._selectedRoles;\n  }\n  constructor(fb, authService, entityService, dialog, service) {\n    this.fb = fb;\n    this.authService = authService;\n    this.entityService = entityService;\n    this.dialog = dialog;\n    this.service = service;\n    this.user = new User();\n    this.backendErrorMessages = {};\n    this.formSubmitted = new EventEmitter();\n    this.statuses = USER_STATUS_LIST;\n    this.userTypes = USER_TYPES_MAP;\n    this.changePasswordPeriodTypes = USER_CHANGE_PASSWORD_PERIOD_TYPE_LIST;\n    this.submitted = false;\n    this.editMode = false;\n    this.entityOptions = [];\n    this.entityOptionsLoading$ = new BehaviorSubject(false);\n    this.roleSelectOptions = [];\n    this.messageErrors = {\n      required: 'VALIDATION.required',\n      minLength: `VALIDATION.minLength`,\n      invalidPassword: 'VALIDATION.invalidPassword',\n      passwordMinLength: 'VALIDATION.passwordMinLength',\n      notEqualsPassword: 'VALIDATION.notEqualsPassword',\n      invalidEmailAddress: 'VALIDATION.invalidEmailAddress',\n      invalidPhoneNumberMask: 'VALIDATION.invalidPhoneNumberMask',\n      passwordContainLowercase: `VALIDATION.passwordContainLowercase`,\n      passwordContainUppercase: `VALIDATION.passwordContainUppercase`,\n      invalidLatinCharsDigitsSymbols: `VALIDATION.invalidLatinCharsDigitsSymbols`,\n      min: `VALIDATION.min`\n    };\n    this.destroyed$ = new Subject();\n    this.form = this.initForm();\n  }\n  ngOnInit() {\n    this.editMode = !!this.user?.createdAt;\n    this.buildEntityOptions();\n    this.populateForm();\n    this.onUserTypeChanged();\n    this.subscribeToRoles();\n    this.entityControl.valueChanges.pipe(distinctUntilChanged(), takeUntil(this.destroyed$)).subscribe(() => {\n      this.rolesControl.setValue([]);\n      this.updateRoles();\n    });\n  }\n  ngOnDestroy() {\n    this.destroyed$.next();\n    this.destroyed$.complete();\n  }\n  get entityControl() {\n    return this.form.get('entity');\n  }\n  get firstNameControl() {\n    return this.form.get('firstName');\n  }\n  get lastNameControl() {\n    return this.form.get('lastName');\n  }\n  get usernameControl() {\n    return this.form.get('username');\n  }\n  get passwordControl() {\n    return this.form.get('password');\n  }\n  get emailControl() {\n    return this.form.get('email');\n  }\n  get phoneControl() {\n    return this.form.get('phone');\n  }\n  get rolesControl() {\n    return this.form.get('roles');\n  }\n  get statusControl() {\n    return this.form.get('status');\n  }\n  get userTypeControl() {\n    return this.form.get('userType');\n  }\n  onFormSubmitFn(event) {\n    event.preventDefault();\n    this.form.markAllAsTouched();\n    this.submitted = true;\n    if (this.form.valid) {\n      if (this.editMode && this.userTypeControl.value === this.userTypes.operatorApi && this.passwordControl.value) {\n        this.dialog.open(BoConfirmationComponent, {\n          width: '500px',\n          data: {\n            message: 'ENTITY_SETUP.USERS.changePasswordNote'\n          },\n          disableClose: true\n        }).afterClosed().pipe(filter(result => result), takeUntil(this.destroyed$)).subscribe(() => {\n          this.onFormSubmitConfirmFn();\n        });\n      } else {\n        this.onFormSubmitConfirmFn();\n      }\n    }\n  }\n  onFormSubmitConfirmFn() {\n    const value = this.form.value;\n    if (value.entity === ':') {\n      value.entity = '';\n    }\n    const data = this.editMode ? {\n      ...this.user,\n      ...value\n    } : value;\n    if ('roles' in data) {\n      data.roles = this._roles.filter(({\n        id\n      }) => data.roles.includes(id));\n      delete data['role'];\n    }\n    this.formSubmitted.emit({\n      isEdit: this.editMode,\n      data\n    });\n  }\n  initForm() {\n    return this.fb.group({\n      firstName: ['', Validators.required],\n      lastName: ['', Validators.required],\n      username: ['', [Validators.required, ValidationService.latinCharsDigitsSymbols, ValidationService.minLength(4)]],\n      email: ['', this.getEmailValidators()],\n      phone: ['', ValidationService.IfNotEmpty(ValidationService.phoneNumberValidator)],\n      password: ['', this.getPasswordValidators()],\n      status: [''],\n      roles: [[], Validators.required],\n      entity: [null, Validators.required],\n      userType: ['', Validators.required],\n      forcePasswordChangePeriodType: ['monthly', Validators.required],\n      forcePasswordChangePeriod: [3, Validators.compose([Validators.required, Validators.min(1)])]\n    });\n  }\n  subscribeToRoles() {\n    this.service.fetchAvailableRoles().pipe(takeUntil(this.destroyed$)).subscribe(([roles, rolesHash, currentUser]) => {\n      const selectedRoles = [];\n      const unsharedRoles = [];\n      this.user.roles.forEach(role => {\n        if (rolesHash[role.id]) {\n          selectedRoles.push(role);\n        } else {\n          unsharedRoles.push({\n            id: role.id,\n            text: role.title\n          });\n        }\n      });\n      this.selectedRoles = selectedRoles;\n      if (this.selectedRoles) {\n        this.rolesControl.patchValue(this.selectedRoles.map(item => item.id));\n      }\n      this.unsharedRoles = unsharedRoles;\n      // add currentUser roles to sharedRoles\n      this.allRoles = roles;\n      this.userRoles = [...this.selectedRoles].map(role => {\n        return Object.assign({}, role, {\n          disabled: true\n        });\n      }).concat(currentUser.roles).filter((value, pos, arr) => arr.findIndex(i => i.id === value.id) === pos);\n      this.updateRoles();\n    });\n  }\n  updateRoles() {\n    if (!this.entityControl.value) {\n      this.roles = [];\n      return;\n    }\n    const entity = this.entityOptions[this.entityControl.value];\n    if (this.service.hasCreateEditRoleAccess(entity?.isRoot())) {\n      this.roles = this.allRoles;\n    } else {\n      this.roles = this.userRoles;\n    }\n  }\n  onUserTypeChanged() {\n    const permission = this.user.entity === '' ? PERMISSIONS_NAMES.KEYENTITY_USER_CHANGE_TYPE : PERMISSIONS_NAMES.USER_CHANGE_TYPE;\n    if (this.editMode && !this.authService.areGranted([permission])) {\n      this.form.get('forcePasswordChangePeriodType').disable();\n      this.form.get('forcePasswordChangePeriod').disable();\n      this.userTypeControl.disable();\n    }\n    this.userTypeControl.valueChanges.pipe(filter(userType => this.editMode && userType === this.userTypes.bo), switchMap(() => this.dialog.open(BoConfirmationComponent, {\n      width: '500px',\n      data: {\n        message: 'ENTITY_SETUP.USERS.changeUserTypeNote'\n      },\n      disableClose: true\n    }).afterClosed()), filter(result => !result), tap(() => this.userTypeControl.setValue(this.userTypes.operatorApi)), takeUntil(this.destroyed$)).subscribe();\n  }\n  hasForcePasswordPermissions() {\n    let permission = PERMISSIONS_NAMES.FORCE_RESET_PASSWORD;\n    if (this.entity.isRoot()) {\n      permission = PERMISSIONS_NAMES.KEYENTITY_FORCE_RESET_PASSWORD;\n    }\n    return this.authService.allowedTo([permission]);\n  }\n  hasForceEmailPermissions() {\n    let permission = PERMISSIONS_NAMES.FORCE_SET_EMAIL;\n    if (this.entity.isRoot()) {\n      permission = PERMISSIONS_NAMES.KEYENTITY_FORCE_SET_EMAIL;\n    }\n    return this.authService.allowedTo([permission]);\n  }\n  updateEmailValidators() {\n    this.emailControl.setValidators(this.getEmailValidators());\n    this.emailControl.updateValueAndValidity();\n  }\n  updatePasswordValidators() {\n    const optional = this.user && 'createdAt' in this.user;\n    this.passwordControl.setValidators(this.getPasswordValidators(optional));\n    this.passwordControl.updateValueAndValidity();\n  }\n  getEmailValidators() {\n    let validators = [Validators.required, ValidationService.emailValidator];\n    if (this.excludedEmails && this.excludedEmails.length) {\n      validators = [...validators, ...this.excludedEmails.map(email => ValidationService.notEqualsString(email))];\n    }\n    return Validators.compose(validators);\n  }\n  getPasswordValidators(optional = false) {\n    let validators = [ValidationService.latinCharsDigitsSymbols, ValidationService.passwordConditions(), ValidationService.passwordValidator];\n    if (this.excludedPasswords && this.excludedPasswords.length) {\n      validators = [...validators, ...this.excludedPasswords.map(pwd => ValidationService.notEqualsPassword(pwd))];\n    }\n    if (this.user && 'username' in this.user) {\n      validators.push(ValidationService.notEqualsString(this.user.username));\n    }\n    if (!optional) {\n      validators.push(Validators.required);\n    }\n    return optional ? ValidationService.IfNotEmpty(Validators.compose(validators)) : Validators.compose(validators);\n  }\n  populateForm() {\n    this.form.patchValue(this.user);\n    if (this.editMode) {\n      if (this.user.entity === '') {\n        this.entityControl.setValue(':');\n        if (!this.authService.isSuperAdmin) {\n          this.form.disable({\n            emitEvent: false\n          });\n        }\n      }\n      this.passwordControl.disable();\n      this.emailControl.disable();\n      this.entityControl.disable();\n      if (this.selectedRoles) {\n        this.rolesControl.patchValue(this.selectedRoles.map(item => item.id));\n      }\n      this.statusControl.patchValue(this.user.status);\n      this.userStatus = this.statusControl.value;\n    }\n    if (this.hasForcePasswordPermissions()) {\n      this.passwordControl.enable();\n      this.emailControl.setValidators(this.getEmailValidators());\n    }\n    if (this.hasForceEmailPermissions()) {\n      this.emailControl.enable();\n    }\n  }\n  buildEntityOptions() {\n    this.entityOptionsLoading$.next(true);\n    this.entityService.getShortStructure().pipe(map(structure => entitiesStructureToSelectOptions(structure, 0, [], false)), map(entityOptions => entityOptions.map(option => ({\n      ...option,\n      id: option.id || ':'\n    }))), map(entityOptions => {\n      if (this.entity.path && this.entity.path !== ':') {\n        return entityOptions.filter(({\n          id\n        }) => id.indexOf(this.entity.path) === 0);\n      }\n      return entityOptions;\n    }), map(entityOptions => {\n      if (!this.authService.isSuperAdmin) {\n        const rootOption = entityOptions.find(({\n          id\n        }) => id === ':');\n        if (rootOption) {\n          rootOption.disabled = true;\n        }\n        const selfOption = entityOptions.find(({\n          id\n        }) => id === this.brief.path);\n        if (selfOption) {\n          selfOption.disabled = true;\n        }\n      }\n      return entityOptions;\n    }), finalize(() => {\n      this.entityOptionsLoading$.next(false);\n    }), takeUntil(this.destroyed$)).subscribe(entityOptions => {\n      this.entityOptions = entityOptions;\n    });\n  }\n  static {\n    this.ɵfac = function UserFormComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || UserFormComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.SwHubAuthService), i0.ɵɵdirectiveInject(i3.EntityService), i0.ɵɵdirectiveInject(i4.MatDialog), i0.ɵɵdirectiveInject(i5.UserEditorService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: UserFormComponent,\n      selectors: [[\"user-form\"]],\n      inputs: {\n        user: \"user\",\n        brief: \"brief\",\n        entity: \"entity\",\n        backendErrorMessages: \"backendErrorMessages\",\n        roles: \"roles\",\n        excludedEmails: \"excludedEmails\",\n        excludedPasswords: \"excludedPasswords\",\n        selectedRoles: \"selectedRoles\"\n      },\n      outputs: {\n        formSubmitted: \"formSubmitted\"\n      },\n      decls: 102,\n      vars: 86,\n      consts: [[\"rolesLoading\", \"\"], [3, \"formGroup\"], [\"class\", \"full-width\", \"appearance\", \"outline\", 4, \"ngIf\"], [4, \"ngIf\"], [\"fxLayout\", \"row\"], [\"fxFlex\", \"50\", \"appearance\", \"outline\", 1, \"mr-10\"], [\"matInput\", \"\", \"trimValue\", \"\", \"autocomplete\", \"off\", \"formControlName\", \"firstName\", 3, \"placeholder\"], [3, \"messages\", \"control\"], [\"fxFlex\", \"50\", \"appearance\", \"outline\"], [\"matInput\", \"\", \"trimValue\", \"\", \"autocomplete\", \"off\", \"formControlName\", \"lastName\", 3, \"placeholder\"], [\"matInput\", \"\", \"trimValue\", \"\", \"autocomplete\", \"off\", \"formControlName\", \"username\", 3, \"placeholder\"], [\"matInput\", \"\", \"trimValue\", \"\", \"autocomplete\", \"off\", \"formControlName\", \"password\", 3, \"placeholder\"], [\"matInput\", \"\", \"trimValue\", \"\", \"autocomplete\", \"off\", \"formControlName\", \"email\", 3, \"placeholder\"], [\"matInput\", \"\", \"trimValue\", \"\", \"autocomplete\", \"off\", \"formControlName\", \"phone\", 3, \"placeholder\"], [\"appearance\", \"outline\", 1, \"width100\"], [\"formControlName\", \"roles\", 3, \"items\"], [\"appearance\", \"outline\", \"class\", \"width100\", 4, \"ngIf\"], [\"formControlName\", \"status\", 3, \"valueChange\", \"value\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"fxLayout\", \"row\", \"fxLayout\", \"start center\"], [\"formControlName\", \"userType\", \"fxFlex\", \"100\"], [3, \"value\"], [\"matInput\", \"\", \"type\", \"number\", \"size\", \"2\", \"formControlName\", \"forcePasswordChangePeriod\"], [\"formControlName\", \"forcePasswordChangePeriodType\", \"fxFlex\", \"20\", 2, \"padding\", \"14px 0 0 10px\"], [2, \"margin\", \"10px 0 10px 0\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"formControlName\", \"entity\", 3, \"placeholder\", \"disableEmptyOption\", \"showSearch\", \"data\"], [3, \"value\", \"diameter\", \"mode\"], [\"title\", \"This roles can not be changed\"], [3, \"disabled\", \"value\"]],\n      template: function UserFormComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"form\", 1);\n          i0.ɵɵtemplate(1, UserFormComponent_mat_form_field_1_Template, 5, 8, \"mat-form-field\", 2)(2, UserFormComponent_ng_container_2_Template, 5, 7, \"ng-container\", 3);\n          i0.ɵɵpipe(3, \"async\");\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"mat-form-field\", 5)(6, \"mat-label\");\n          i0.ɵɵtext(7);\n          i0.ɵɵpipe(8, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(9, \"input\", 6);\n          i0.ɵɵpipe(10, \"translate\");\n          i0.ɵɵelementStart(11, \"mat-error\");\n          i0.ɵɵelement(12, \"lib-swui-control-messages\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"mat-form-field\", 8)(14, \"mat-label\");\n          i0.ɵɵtext(15);\n          i0.ɵɵpipe(16, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(17, \"input\", 9);\n          i0.ɵɵpipe(18, \"translate\");\n          i0.ɵɵelementStart(19, \"mat-error\");\n          i0.ɵɵelement(20, \"lib-swui-control-messages\", 7);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(21, \"div\", 4)(22, \"mat-form-field\", 5)(23, \"mat-label\");\n          i0.ɵɵtext(24);\n          i0.ɵɵpipe(25, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(26, \"input\", 10);\n          i0.ɵɵpipe(27, \"translate\");\n          i0.ɵɵelementStart(28, \"mat-error\");\n          i0.ɵɵelement(29, \"lib-swui-control-messages\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(30, \"mat-form-field\", 8)(31, \"mat-label\");\n          i0.ɵɵtext(32);\n          i0.ɵɵpipe(33, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(34, \"input\", 11);\n          i0.ɵɵpipe(35, \"translate\");\n          i0.ɵɵelementStart(36, \"mat-error\");\n          i0.ɵɵelement(37, \"lib-swui-control-messages\", 7);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(38, \"div\", 4)(39, \"mat-form-field\", 5)(40, \"mat-label\");\n          i0.ɵɵtext(41);\n          i0.ɵɵpipe(42, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(43, \"input\", 12);\n          i0.ɵɵpipe(44, \"translate\");\n          i0.ɵɵelementStart(45, \"mat-error\");\n          i0.ɵɵelement(46, \"lib-swui-control-messages\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(47, \"mat-form-field\", 8)(48, \"mat-label\");\n          i0.ɵɵtext(49);\n          i0.ɵɵpipe(50, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(51, \"input\", 13);\n          i0.ɵɵpipe(52, \"translate\");\n          i0.ɵɵelementStart(53, \"mat-error\");\n          i0.ɵɵelement(54, \"lib-swui-control-messages\", 7);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(55, \"div\", 4)(56, \"mat-form-field\", 14)(57, \"mat-label\");\n          i0.ɵɵtext(58);\n          i0.ɵɵpipe(59, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(60, \"lib-swui-chips-autocomplete\", 15);\n          i0.ɵɵelementStart(61, \"mat-error\");\n          i0.ɵɵelement(62, \"lib-swui-control-messages\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(63, UserFormComponent_ng_template_63_Template, 4, 6, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"div\", 4);\n          i0.ɵɵtemplate(66, UserFormComponent_mat_form_field_66_Template, 5, 5, \"mat-form-field\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(67, \"div\", 4)(68, \"mat-form-field\", 8)(69, \"mat-label\");\n          i0.ɵɵtext(70);\n          i0.ɵɵpipe(71, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(72, \"mat-select\", 17);\n          i0.ɵɵtwoWayListener(\"valueChange\", function UserFormComponent_Template_mat_select_valueChange_72_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.userStatus, $event) || (ctx.userStatus = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(73, UserFormComponent_mat_option_73_Template, 3, 4, \"mat-option\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(74, \"mat-error\");\n          i0.ɵɵelement(75, \"lib-swui-control-messages\", 7);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(76, \"div\", 19)(77, \"h3\");\n          i0.ɵɵtext(78);\n          i0.ɵɵpipe(79, \"translate\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(80, \"div\", 4)(81, \"mat-radio-group\", 20)(82, \"mat-card\")(83, \"mat-radio-button\", 21);\n          i0.ɵɵtext(84);\n          i0.ɵɵpipe(85, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(86, \"div\")(87, \"mat-form-field\", 8)(88, \"mat-label\");\n          i0.ɵɵtext(89);\n          i0.ɵɵpipe(90, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(91, \"input\", 22);\n          i0.ɵɵelementStart(92, \"mat-error\");\n          i0.ɵɵelement(93, \"lib-swui-control-messages\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(94, \"mat-select\", 23);\n          i0.ɵɵtemplate(95, UserFormComponent_mat_option_95_Template, 3, 4, \"mat-option\", 18);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(96, \"mat-card\", 24)(97, \"mat-radio-button\", 21);\n          i0.ɵɵtext(98);\n          i0.ɵɵpipe(99, \"translate\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(100, \"mat-error\");\n          i0.ɵɵelement(101, \"lib-swui-control-messages\", 7);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"formGroup\", ctx.form);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", (ctx.entityOptions == null ? null : ctx.entityOptions.length) > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(3, 48, ctx.entityOptionsLoading$));\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(8, 50, \"ENTITY_SETUP.USERS.MODALS.firstname\"), \":\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"placeholder\", i0.ɵɵpipeBind1(10, 52, \"ENTITY_SETUP.USERS.MODALS.firstname\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"messages\", ctx.messageErrors)(\"control\", ctx.firstNameControl);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(16, 54, \"ENTITY_SETUP.USERS.MODALS.lastname\"), \":\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"placeholder\", i0.ɵɵpipeBind1(18, 56, \"ENTITY_SETUP.USERS.MODALS.lastname\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"messages\", ctx.messageErrors)(\"control\", ctx.lastNameControl);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(25, 58, \"ENTITY_SETUP.USERS.MODALS.username\"), \":\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"placeholder\", i0.ɵɵpipeBind1(27, 60, \"ENTITY_SETUP.USERS.MODALS.username\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"messages\", ctx.messageErrors)(\"control\", ctx.usernameControl);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(33, 62, \"ENTITY_SETUP.USERS.MODALS.password\"), \":\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"placeholder\", i0.ɵɵpipeBind1(35, 64, \"ENTITY_SETUP.USERS.MODALS.password\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"messages\", ctx.messageErrors)(\"control\", ctx.passwordControl);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(42, 66, \"ENTITY_SETUP.USERS.MODALS.email\"), \":\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"placeholder\", i0.ɵɵpipeBind1(44, 68, \"ENTITY_SETUP.USERS.MODALS.email\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"messages\", ctx.messageErrors)(\"control\", ctx.emailControl);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(50, 70, \"ENTITY_SETUP.USERS.MODALS.phoneNumber\"), \":\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"placeholder\", i0.ɵɵpipeBind1(52, 72, \"ENTITY_SETUP.USERS.MODALS.phoneNumber\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"messages\", ctx.messageErrors)(\"control\", ctx.phoneControl);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(59, 74, \"ENTITY_SETUP.USERS.MODALS.roles\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"items\", ctx.roleSelectOptions);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"messages\", ctx.messageErrors)(\"control\", ctx.rolesControl);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.editMode);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(71, 76, \"ENTITY_SETUP.USERS.MODALS.status\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"value\", ctx.userStatus);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.statuses);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"messages\", ctx.messageErrors)(\"control\", ctx.statusControl);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(79, 78, \"ENTITY_SETUP.USERS.MODALS.userType\"), \":\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"value\", \"bo\");\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(85, 80, \"ENTITY_SETUP.USERS.userTypeBO\"), \" \");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(90, 82, \"ENTITY_SETUP.USERS.boUserPasswordExpires\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"messages\", ctx.messageErrors)(\"control\", ctx.form.get(\"forcePasswordChangePeriod\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.changePasswordPeriodTypes);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", \"operator_api\");\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(99, 84, \"ENTITY_SETUP.USERS.userTypeOperatorAPI\"), \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"messages\", ctx.messageErrors)(\"control\", ctx.userTypeControl);\n        }\n      },\n      dependencies: [i6.NgForOf, i6.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i7.MatFormField, i7.MatLabel, i7.MatError, i8.MatInput, i9.DefaultLayoutDirective, i9.DefaultFlexDirective, i10.MatProgressSpinner, i11.MatSelect, i12.MatOption, i13.MatRadioGroup, i13.MatRadioButton, i2.SwuiChipsAutocompleteComponent, i2.SwuiSelectComponent, i2.SwuiControlMessagesComponent, i14.MatCard, i15.TrimInputValueComponent, i6.AsyncPipe, i16.TranslatePipe],\n      styles: [\".scroll-chip-list {\\n  max-height: 100% !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY29tbW9uL2NvbXBvbmVudHMvbWF0LXVzZXItZWRpdG9yL3VzZXItZm9ybS5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLDJCQUFBO0FBQ0YiLCJzb3VyY2VzQ29udGVudCI6WyI6Om5nLWRlZXAgLnNjcm9sbC1jaGlwLWxpc3Qge1xuICBtYXgtaGVpZ2h0OiAxMDAlICFpbXBvcnRhbnQ7XG59XG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "Validators", "BehaviorSubject", "Subject", "distinctUntilChanged", "filter", "finalize", "map", "switchMap", "takeUntil", "tap", "PERMISSIONS_NAMES", "USER_CHANGE_PASSWORD_PERIOD_TYPE_LIST", "USER_STATUS_LIST", "USER_TYPES_MAP", "Role", "User", "entitiesStructureToSelectOptions", "ValidationService", "BoConfirmationComponent", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵpropertyInterpolate", "ɵɵpipeBind1", "ɵɵproperty", "ctx_r1", "entityOptions", "length", "messageErrors", "entityControl", "ɵɵelementContainerStart", "ɵɵtext", "ɵɵtextInterpolate2", "load_r3", "ɵɵtextInterpolate", "ɵɵtextInterpolate1", "unsharedRoles", "status_r4", "code", "displayName", "periodType_r5", "id", "UserFormComponent", "roles", "values", "Array", "isArray", "_roles", "roleSelectOptions", "role", "toSelectOption", "sort", "a", "b", "text", "localeCompare", "excludedEmails", "emails", "_excludedEmails", "updateEmailValidators", "excludedPasswords", "passwords", "_excludedPasswords", "updatePasswordValidators", "selectedRoles", "value", "title", "rolesControl", "patchValue", "item", "_selectedRoles", "constructor", "fb", "authService", "entityService", "dialog", "service", "user", "backendErrorMessages", "formSubmitted", "statuses", "userTypes", "changePasswordPeriodTypes", "submitted", "editMode", "entityOptionsLoading$", "required", "<PERSON><PERSON><PERSON><PERSON>", "invalidPassword", "<PERSON><PERSON>in<PERSON><PERSON><PERSON>", "notEqualsPassword", "invalid<PERSON><PERSON><PERSON><PERSON><PERSON>", "invalidPhoneNumberMask", "passwordContainLowercase", "passwordContainUppercase", "invalidLatinCharsDigitsSymbols", "min", "destroyed$", "form", "initForm", "ngOnInit", "createdAt", "buildEntityOptions", "populateForm", "onUserTypeChanged", "subscribeToRoles", "valueChanges", "pipe", "subscribe", "setValue", "updateRoles", "ngOnDestroy", "next", "complete", "get", "firstNameControl", "lastNameControl", "usernameControl", "passwordControl", "emailControl", "phoneControl", "statusControl", "userTypeControl", "onFormSubmitFn", "event", "preventDefault", "mark<PERSON>llAsTouched", "valid", "operatorApi", "open", "width", "data", "message", "disableClose", "afterClosed", "result", "onFormSubmitConfirmFn", "entity", "includes", "emit", "isEdit", "group", "firstName", "lastName", "username", "latinCharsDigitsSymbols", "email", "getEmailValidators", "phone", "IfNotEmpty", "phoneNumberValidator", "password", "getPasswordValidators", "status", "userType", "forcePasswordChangePeriodType", "forcePasswordChangePeriod", "compose", "fetchAvailableRoles", "rolesHash", "currentUser", "for<PERSON>ach", "push", "allRoles", "userRoles", "Object", "assign", "disabled", "concat", "pos", "arr", "findIndex", "i", "hasCreateEditRoleAccess", "isRoot", "permission", "KEYENTITY_USER_CHANGE_TYPE", "USER_CHANGE_TYPE", "areGranted", "disable", "bo", "hasForcePasswordPermissions", "FORCE_RESET_PASSWORD", "KEYENTITY_FORCE_RESET_PASSWORD", "allowedTo", "hasForceEmailPermissions", "FORCE_SET_EMAIL", "KEYENTITY_FORCE_SET_EMAIL", "setValidators", "updateValueAndValidity", "optional", "validators", "emailValidator", "notEqualsString", "passwordConditions", "passwordValidator", "pwd", "isSuperAdmin", "emitEvent", "userStatus", "enable", "getShortStructure", "structure", "option", "path", "indexOf", "rootOption", "find", "selfOption", "brief", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "SwHubAuthService", "i3", "EntityService", "i4", "MatDialog", "i5", "UserEditorService", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "UserFormComponent_Template", "rf", "ctx", "ɵɵtemplate", "UserFormComponent_mat_form_field_1_Template", "UserFormComponent_ng_container_2_Template", "UserFormComponent_ng_template_63_Template", "ɵɵtemplateRefExtractor", "UserFormComponent_mat_form_field_66_Template", "ɵɵtwoWayListener", "UserFormComponent_Template_mat_select_valueChange_72_listener", "$event", "ɵɵrestoreView", "_r1", "ɵɵtwoWayBindingSet", "ɵɵresetView", "UserFormComponent_mat_option_73_Template", "UserFormComponent_mat_option_95_Template", "ɵɵtwoWayProperty"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/common/components/mat-user-editor/user-form.component.ts", "/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/common/components/mat-user-editor/user-form.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, <PERSON><PERSON><PERSON>roy, OnInit, Output } from '@angular/core';\nimport { Form<PERSON>uilder, FormControl, FormGroup, ValidatorFn, Validators } from '@angular/forms';\nimport { MatDialog } from '@angular/material/dialog';\nimport { SelectOptionItem, SwHubAuthService, SwuiSelectOption } from '@skywind-group/lib-swui';\nimport { BehaviorSubject, Subject } from 'rxjs';\nimport { distinctUntilChanged, filter, finalize, map, switchMap, takeUntil, tap } from 'rxjs/operators';\nimport { PERMISSIONS_NAMES } from '../../../app.constants';\nimport {\n  USER_CHANGE_PASSWORD_PERIOD_TYPE_LIST, USER_STATUS_LIST, USER_TYPES_MAP\n} from '../../../pages/business-management/components/entities/tab-users/users.schema';\nimport { Role } from '../../../pages/users/components/roles/role.model';\nimport { User } from '../../../pages/users/user.model';\nimport { Entity } from '../../models/entity.model';\nimport { SelectOptionModel } from '../../models/select-option.model';\nimport { entitiesStructureToSelectOptions, EntityService } from '../../services/entity.service';\nimport { ValidationService } from '../../services/validation.service';\nimport { BoConfirmationComponent } from '../bo-confirmation/bo-confirmation.component';\nimport { UserEditorService } from './user-editor.service';\n\n\nexport interface ErrorMessage {\n  [key: string]: any;\n}\n\n@Component({\n  selector: 'user-form',\n  templateUrl: './user-form.component.html',\n  styleUrls: ['./user-form.component.scss'],\n})\nexport class UserFormComponent implements OnInit, OnDestroy {\n  @Input() user: User = new User();\n  @Input() brief?: Entity;\n  @Input() entity?: Entity;\n  @Input() backendErrorMessages: { [validation: string]: string; } = {};\n  userStatus?: string;\n\n  @Output() formSubmitted = new EventEmitter<any>();\n\n  readonly statuses = USER_STATUS_LIST;\n  readonly userTypes = USER_TYPES_MAP;\n  readonly changePasswordPeriodTypes = USER_CHANGE_PASSWORD_PERIOD_TYPE_LIST;\n  readonly form: FormGroup;\n  submitted = false;\n  editMode = false;\n\n  entityOptions: SelectOptionItem[] = [];\n  entityOptionsLoading$ = new BehaviorSubject(false);\n\n  roleSelectOptions: SelectOptionModel[] = [];\n\n  messageErrors: ErrorMessage = {\n    required: 'VALIDATION.required',\n    minLength: `VALIDATION.minLength`,\n    invalidPassword: 'VALIDATION.invalidPassword',\n    passwordMinLength: 'VALIDATION.passwordMinLength',\n    notEqualsPassword: 'VALIDATION.notEqualsPassword',\n    invalidEmailAddress: 'VALIDATION.invalidEmailAddress',\n    invalidPhoneNumberMask: 'VALIDATION.invalidPhoneNumberMask',\n    passwordContainLowercase: `VALIDATION.passwordContainLowercase`,\n    passwordContainUppercase: `VALIDATION.passwordContainUppercase`,\n    invalidLatinCharsDigitsSymbols: `VALIDATION.invalidLatinCharsDigitsSymbols`,\n    min: `VALIDATION.min`,\n  };\n  unsharedRoles: SwuiSelectOption[];\n\n  private _roles: Role[];\n  private _selectedRoles: Role[];\n  private _excludedEmails: string[];\n  private _excludedPasswords: string[];\n  private readonly destroyed$ = new Subject<any>();\n  private allRoles: Role[];\n  private userRoles: Role[];\n\n  @Input() set roles(values: Role[]) {\n    if (!Array.isArray(values)) return;\n    this._roles = values;\n    this.roleSelectOptions = values.map(role => new Role(role)).map(role => role.toSelectOption()).sort((a, b) => {\n      return a.text.localeCompare(b.text);\n    });\n  }\n\n  get roles(): Role[] {\n    return this._roles;\n  }\n\n  @Input()\n  set excludedEmails(emails: string[]) {\n    if (!Array.isArray(emails)) return;\n    this._excludedEmails = emails;\n    this.updateEmailValidators();\n  }\n\n  get excludedEmails(): string[] {\n    return this._excludedEmails;\n  }\n\n  @Input()\n  set excludedPasswords(passwords: string[]) {\n    if (!Array.isArray(passwords)) return;\n    this._excludedPasswords = passwords;\n    this.updatePasswordValidators();\n  }\n\n  get excludedPasswords(): string[] {\n    return this._excludedPasswords;\n  }\n\n  @Input()\n  set selectedRoles(value: Role[]) {\n    if (!value) {\n      return;\n    }\n\n    value.sort((a, b) => {\n      return a.title.localeCompare(b.title);\n    });\n\n    if (Array.isArray(value) && value.length) {\n      this.rolesControl.patchValue(value.map(item => item.id));\n    }\n\n    this._selectedRoles = value;\n  }\n\n  get selectedRoles(): Role[] {\n    return this._selectedRoles;\n  }\n\n  constructor(private fb: FormBuilder,\n    private authService: SwHubAuthService,\n    private entityService: EntityService<Entity>,\n    private dialog: MatDialog,\n    private service: UserEditorService,\n  ) {\n    this.form = this.initForm();\n  }\n\n  ngOnInit() {\n    this.editMode = !!this.user?.createdAt;\n    this.buildEntityOptions();\n    this.populateForm();\n    this.onUserTypeChanged();\n    this.subscribeToRoles();\n\n    this.entityControl.valueChanges\n      .pipe(\n        distinctUntilChanged(),\n        takeUntil(this.destroyed$)\n      )\n      .subscribe(() => {\n        this.rolesControl.setValue([]);\n        this.updateRoles();\n      });\n  }\n\n  ngOnDestroy(): void {\n    this.destroyed$.next();\n    this.destroyed$.complete();\n  }\n\n  get entityControl(): FormControl {\n    return this.form.get('entity') as FormControl;\n  }\n\n  get firstNameControl(): FormControl {\n    return this.form.get('firstName') as FormControl;\n  }\n\n  get lastNameControl(): FormControl {\n    return this.form.get('lastName') as FormControl;\n  }\n\n  get usernameControl(): FormControl {\n    return this.form.get('username') as FormControl;\n  }\n\n  get passwordControl(): FormControl {\n    return this.form.get('password') as FormControl;\n  }\n\n  get emailControl(): FormControl {\n    return this.form.get('email') as FormControl;\n  }\n\n  get phoneControl(): FormControl {\n    return this.form.get('phone') as FormControl;\n  }\n\n  get rolesControl(): FormControl {\n    return this.form.get('roles') as FormControl;\n  }\n\n  get statusControl(): FormControl {\n    return this.form.get('status') as FormControl;\n  }\n\n  get userTypeControl(): FormControl {\n    return this.form.get('userType') as FormControl;\n  }\n\n  onFormSubmitFn(event) {\n    event.preventDefault();\n    this.form.markAllAsTouched();\n    this.submitted = true;\n    if (this.form.valid) {\n      if (this.editMode && this.userTypeControl.value === this.userTypes.operatorApi && this.passwordControl.value) {\n        this.dialog.open(BoConfirmationComponent, {\n          width: '500px',\n          data: { message: 'ENTITY_SETUP.USERS.changePasswordNote' },\n          disableClose: true\n        }).afterClosed().pipe(\n          filter(result => result),\n          takeUntil(this.destroyed$)\n        ).subscribe(() => {\n          this.onFormSubmitConfirmFn();\n        });\n      } else {\n        this.onFormSubmitConfirmFn();\n      }\n    }\n  }\n\n  onFormSubmitConfirmFn() {\n    const value = this.form.value;\n    if (value.entity === ':') {\n      value.entity = '';\n    }\n    const data = this.editMode ? { ...this.user, ...value } : value;\n\n    if ('roles' in data) {\n      data.roles = this._roles.filter(({ id }) => data.roles.includes(id));\n      delete data['role'];\n    }\n\n    this.formSubmitted.emit({ isEdit: this.editMode, data });\n  }\n\n  private initForm(): FormGroup {\n    return this.fb.group({\n      firstName: ['', Validators.required],\n      lastName: ['', Validators.required],\n      username: [\n        '', [\n          Validators.required,\n          ValidationService.latinCharsDigitsSymbols,\n          ValidationService.minLength(4)\n        ]\n      ],\n      email: ['', this.getEmailValidators()],\n      phone: ['', ValidationService.IfNotEmpty(ValidationService.phoneNumberValidator)],\n      password: ['', this.getPasswordValidators()],\n      status: [''],\n      roles: [[], Validators.required],\n      entity: [null, Validators.required],\n      userType: ['', Validators.required],\n      forcePasswordChangePeriodType: ['monthly', Validators.required],\n      forcePasswordChangePeriod: [3, Validators.compose([Validators.required, Validators.min(1)])]\n    });\n  }\n\n  private subscribeToRoles() {\n    this.service.fetchAvailableRoles()\n      .pipe(takeUntil(this.destroyed$))\n      .subscribe(([roles, rolesHash, currentUser]) => {\n        const selectedRoles = [];\n        const unsharedRoles = [];\n\n        this.user.roles.forEach((role) => {\n          if (rolesHash[role.id]) {\n            selectedRoles.push(role);\n          } else {\n            unsharedRoles.push({ id: role.id, text: role.title });\n          }\n        });\n\n        this.selectedRoles = selectedRoles;\n\n        if (this.selectedRoles) {\n          this.rolesControl.patchValue(this.selectedRoles.map(item => item.id));\n        }\n\n        this.unsharedRoles = unsharedRoles;\n\n        // add currentUser roles to sharedRoles\n        this.allRoles = roles;\n        this.userRoles = [...this.selectedRoles].map(role => {\n          return Object.assign({}, role, { disabled: true });\n        })\n          .concat(currentUser.roles)\n          .filter((value, pos, arr) => arr.findIndex(i => i.id === value.id) === pos);\n\n        this.updateRoles();\n      });\n  }\n\n  private updateRoles() {\n    if (!this.entityControl.value) {\n      this.roles = [];\n      return;\n    }\n    const entity = this.entityOptions[this.entityControl.value];\n\n    if (this.service.hasCreateEditRoleAccess(entity?.isRoot())) {\n      this.roles = this.allRoles;\n    } else {\n      this.roles = this.userRoles;\n    }\n  }\n\n  private onUserTypeChanged() {\n    const permission = this.user.entity === '' ? PERMISSIONS_NAMES.KEYENTITY_USER_CHANGE_TYPE : PERMISSIONS_NAMES.USER_CHANGE_TYPE;\n\n    if (this.editMode && !this.authService.areGranted([permission])) {\n      this.form.get('forcePasswordChangePeriodType').disable();\n      this.form.get('forcePasswordChangePeriod').disable();\n      this.userTypeControl.disable();\n    }\n\n    this.userTypeControl.valueChanges.pipe(\n      filter((userType) => this.editMode && userType === this.userTypes.bo),\n      switchMap(() =>\n        this.dialog.open(BoConfirmationComponent, {\n          width: '500px',\n          data: { message: 'ENTITY_SETUP.USERS.changeUserTypeNote' },\n          disableClose: true\n        }).afterClosed()),\n      filter(result => !result),\n      tap(() => this.userTypeControl.setValue(this.userTypes.operatorApi)),\n      takeUntil(this.destroyed$)\n    ).subscribe();\n  }\n\n  private hasForcePasswordPermissions(): boolean {\n    let permission: string = PERMISSIONS_NAMES.FORCE_RESET_PASSWORD;\n\n    if (this.entity.isRoot()) {\n      permission = PERMISSIONS_NAMES.KEYENTITY_FORCE_RESET_PASSWORD as string;\n    }\n\n    return this.authService.allowedTo([permission]);\n  }\n\n  private hasForceEmailPermissions(): boolean {\n    let permission: string = PERMISSIONS_NAMES.FORCE_SET_EMAIL;\n\n    if (this.entity.isRoot()) {\n      permission = PERMISSIONS_NAMES.KEYENTITY_FORCE_SET_EMAIL as string;\n    }\n\n    return this.authService.allowedTo([permission]);\n  }\n\n  private updateEmailValidators() {\n    this.emailControl.setValidators(this.getEmailValidators());\n    this.emailControl.updateValueAndValidity();\n  }\n\n  private updatePasswordValidators() {\n    const optional = this.user && 'createdAt' in this.user;\n\n    this.passwordControl.setValidators(this.getPasswordValidators(optional));\n    this.passwordControl.updateValueAndValidity();\n  }\n\n  private getEmailValidators() {\n    let validators: ValidatorFn[] = [Validators.required, ValidationService.emailValidator];\n\n    if (this.excludedEmails && this.excludedEmails.length) {\n      validators = [\n        ...validators,\n        ...this.excludedEmails.map((email) => ValidationService.notEqualsString(email))\n      ];\n    }\n\n    return Validators.compose(validators);\n  }\n\n  private getPasswordValidators(optional: boolean = false) {\n    let validators: ValidatorFn[] = [\n      ValidationService.latinCharsDigitsSymbols,\n      ValidationService.passwordConditions(),\n      ValidationService.passwordValidator,\n    ];\n\n    if (this.excludedPasswords && this.excludedPasswords.length) {\n      validators = [\n        ...validators,\n        ...this.excludedPasswords.map((pwd) => ValidationService.notEqualsPassword(pwd))\n      ];\n    }\n\n    if (this.user && 'username' in this.user) {\n      validators.push(ValidationService.notEqualsString(this.user.username));\n    }\n\n    if (!optional) {\n      validators.push(Validators.required);\n    }\n\n    return optional\n      ? ValidationService.IfNotEmpty(Validators.compose(validators))\n      : Validators.compose(validators);\n  }\n\n  private populateForm() {\n    this.form.patchValue(this.user);\n\n    if (this.editMode) {\n      if (this.user.entity === '') {\n        this.entityControl.setValue(':');\n\n        if (!this.authService.isSuperAdmin) {\n          this.form.disable({ emitEvent: false });\n        }\n      }\n\n      this.passwordControl.disable();\n      this.emailControl.disable();\n      this.entityControl.disable();\n\n      if (this.selectedRoles) {\n        this.rolesControl.patchValue(this.selectedRoles.map(item => item.id));\n      }\n\n      this.statusControl.patchValue(this.user.status);\n      this.userStatus = this.statusControl.value;\n    }\n\n    if (this.hasForcePasswordPermissions()) {\n      this.passwordControl.enable();\n      this.emailControl.setValidators(this.getEmailValidators());\n    }\n\n    if (this.hasForceEmailPermissions()) {\n      this.emailControl.enable();\n    }\n  }\n\n  private buildEntityOptions() {\n    this.entityOptionsLoading$.next(true);\n    this.entityService.getShortStructure().pipe(\n      map(structure => entitiesStructureToSelectOptions(structure, 0, [], false)),\n      map(entityOptions => entityOptions.map(option => ({\n        ...option,\n        id: option.id || ':'\n      }))),\n      map(entityOptions => {\n        if (this.entity.path && this.entity.path !== ':') {\n          return entityOptions.filter(({ id }) => id.indexOf(this.entity.path) === 0);\n        }\n        return entityOptions;\n      }),\n      map((entityOptions: SelectOptionItem[]) => {\n        if (!this.authService.isSuperAdmin) {\n          const rootOption = entityOptions.find(({ id }) => id === ':');\n          if (rootOption) {\n            rootOption.disabled = true;\n          }\n          const selfOption = entityOptions.find(({ id }) => id === this.brief.path);\n          if (selfOption) {\n            selfOption.disabled = true;\n          }\n        }\n        return entityOptions;\n      }),\n      finalize(() => {\n        this.entityOptionsLoading$.next(false);\n      }),\n      takeUntil(this.destroyed$)\n    ).subscribe(entityOptions => {\n      this.entityOptions = entityOptions;\n    });\n  }\n}\n", "<form [formGroup]=\"form\">\n  <mat-form-field\n    class=\"full-width\"\n    appearance=\"outline\"\n    *ngIf=\"entityOptions?.length > 0\">\n    <lib-swui-select\n      formControlName=\"entity\"\n      placeholder=\"{{ 'INTEGRATIONS.selectOperator' | translate }}\"\n      [disableEmptyOption]=\"true\"\n      [showSearch]=\"entityOptions?.length > 10\"\n      [data]=\"entityOptions\">\n    </lib-swui-select>\n    <mat-error>\n      <lib-swui-control-messages\n        [messages]=\"messageErrors\"\n        [control]=\"entityControl\">\n      </lib-swui-control-messages>\n    </mat-error>\n  </mat-form-field>\n  <ng-container *ngIf=\"entityOptionsLoading$ | async as load\">\n    <mat-label>{{ 'ALL.IN_PROGRESS' | translate}} {{load}} qqqq</mat-label>\n    <mat-progress-spinner\n      [value]=\"50\"\n      [diameter]=\"20\"\n      [mode]=\"'indeterminate'\">\n    </mat-progress-spinner>\n  </ng-container>\n\n  <div fxLayout=\"row\">\n    <mat-form-field fxFlex=\"50\" appearance=\"outline\" class=\"mr-10\">\n      <mat-label>{{ 'ENTITY_SETUP.USERS.MODALS.firstname' | translate }}:</mat-label>\n      <input matInput trimValue autocomplete=\"off\"\n             formControlName=\"firstName\"\n             [placeholder]=\"'ENTITY_SETUP.USERS.MODALS.firstname' | translate\"/>\n      <mat-error>\n        <lib-swui-control-messages\n          [messages]=\"messageErrors\"\n          [control]=\"firstNameControl\">\n        </lib-swui-control-messages>\n      </mat-error>\n    </mat-form-field>\n\n    <mat-form-field fxFlex=\"50\" appearance=\"outline\">\n      <mat-label>{{ 'ENTITY_SETUP.USERS.MODALS.lastname' | translate }}:</mat-label>\n      <input matInput trimValue autocomplete=\"off\"\n             formControlName=\"lastName\"\n             [placeholder]=\"'ENTITY_SETUP.USERS.MODALS.lastname' | translate\"/>\n      <mat-error>\n        <lib-swui-control-messages\n          [messages]=\"messageErrors\"\n          [control]=\"lastNameControl\">\n        </lib-swui-control-messages>\n      </mat-error>\n    </mat-form-field>\n  </div>\n\n  <div fxLayout=\"row\">\n    <mat-form-field fxFlex=\"50\" appearance=\"outline\" class=\"mr-10\">\n      <mat-label>{{ 'ENTITY_SETUP.USERS.MODALS.username' | translate }}:</mat-label>\n      <input matInput trimValue autocomplete=\"off\"\n             formControlName=\"username\"\n             [placeholder]=\"'ENTITY_SETUP.USERS.MODALS.username' | translate\"/>\n      <mat-error>\n        <lib-swui-control-messages\n          [messages]=\"messageErrors\"\n          [control]=\"usernameControl\">\n        </lib-swui-control-messages>\n      </mat-error>\n    </mat-form-field>\n\n    <mat-form-field fxFlex=\"50\" appearance=\"outline\">\n      <mat-label>{{ 'ENTITY_SETUP.USERS.MODALS.password' | translate }}:</mat-label>\n      <input matInput trimValue autocomplete=\"off\"\n             formControlName=\"password\"\n             [placeholder]=\"'ENTITY_SETUP.USERS.MODALS.password' | translate\"/>\n      <mat-error>\n        <lib-swui-control-messages\n          [messages]=\"messageErrors\"\n          [control]=\"passwordControl\">\n        </lib-swui-control-messages>\n      </mat-error>\n    </mat-form-field>\n  </div>\n\n  <div fxLayout=\"row\">\n    <mat-form-field fxFlex=\"50\" appearance=\"outline\" class=\"mr-10\">\n      <mat-label>{{ 'ENTITY_SETUP.USERS.MODALS.email' | translate }}:</mat-label>\n      <input matInput trimValue autocomplete=\"off\"\n             formControlName=\"email\"\n             [placeholder]=\"'ENTITY_SETUP.USERS.MODALS.email' | translate\"/>\n      <mat-error>\n        <lib-swui-control-messages\n          [messages]=\"messageErrors\"\n          [control]=\"emailControl\">\n        </lib-swui-control-messages>\n      </mat-error>\n    </mat-form-field>\n\n    <mat-form-field fxFlex=\"50\" appearance=\"outline\">\n      <mat-label>{{ 'ENTITY_SETUP.USERS.MODALS.phoneNumber' | translate }}:</mat-label>\n      <input matInput trimValue autocomplete=\"off\"\n             formControlName=\"phone\"\n             [placeholder]=\"'ENTITY_SETUP.USERS.MODALS.phoneNumber' | translate\"/>\n      <mat-error>\n        <lib-swui-control-messages\n          [messages]=\"messageErrors\"\n          [control]=\"phoneControl\">\n        </lib-swui-control-messages>\n      </mat-error>\n    </mat-form-field>\n  </div>\n\n  <div fxLayout=\"row\">\n    <mat-form-field appearance=\"outline\" class=\"width100\">\n      <mat-label>{{ 'ENTITY_SETUP.USERS.MODALS.roles' | translate}}</mat-label>\n      <lib-swui-chips-autocomplete\n        formControlName=\"roles\"\n        [items]=\"roleSelectOptions\">\n      </lib-swui-chips-autocomplete>\n      <mat-error>\n        <lib-swui-control-messages\n          [messages]=\"messageErrors\"\n          [control]=\"rolesControl\">\n        </lib-swui-control-messages>\n      </mat-error>\n    </mat-form-field>\n\n    <ng-template #rolesLoading>\n      <mat-label>{{ 'ALL.IN_PROGRESS' | translate}}</mat-label>\n      <mat-progress-spinner\n        [value]=\"50\"\n        [diameter]=\"40\"\n        [mode]=\"'indeterminate'\">\n      </mat-progress-spinner>\n    </ng-template>\n  </div>\n\n  <div fxLayout=\"row\">\n    <mat-form-field *ngIf=\"editMode\" appearance=\"outline\"  class=\"width100\">\n      <mat-label title=\"This roles can not be changed\">\n        {{ 'ENTITY_SETUP.USERS.MODALS.additionalroles' | translate}}\n      </mat-label>\n      <lib-swui-chips-autocomplete\n        [disabled]=\"true\"\n        [value]=\"unsharedRoles\">\n      </lib-swui-chips-autocomplete>\n    </mat-form-field>\n  </div>\n\n  <div fxLayout=\"row\">\n    <mat-form-field fxFlex=\"50\" appearance=\"outline\">\n      <mat-label>{{ 'ENTITY_SETUP.USERS.MODALS.status' | translate}}</mat-label>\n      <mat-select [(value)]=\"userStatus\" formControlName=\"status\">\n        <mat-option *ngFor=\"let status of statuses\" [value]=\"status.code\">\n          {{ status.displayName | translate }}\n        </mat-option>\n      </mat-select>\n      <mat-error>\n        <lib-swui-control-messages\n          [messages]=\"messageErrors\"\n          [control]=\"statusControl\">\n        </lib-swui-control-messages>\n      </mat-error>\n    </mat-form-field>\n  </div>\n\n  <div fxLayout=\"row\" fxLayout=\"start center\">\n    <h3>{{ 'ENTITY_SETUP.USERS.MODALS.userType' | translate }}:</h3>\n  </div>\n\n  <div fxLayout=\"row\">\n    <mat-radio-group formControlName=\"userType\" fxFlex=\"100\">\n      <mat-card>\n        <mat-radio-button [value]=\"'bo'\">\n          {{ 'ENTITY_SETUP.USERS.userTypeBO' | translate }}\n        </mat-radio-button>\n\n        <div>\n          <mat-form-field fxFlex=\"50\" appearance=\"outline\">\n            <mat-label>{{ 'ENTITY_SETUP.USERS.boUserPasswordExpires' | translate }}</mat-label>\n            <input matInput type=\"number\" size=\"2\" formControlName=\"forcePasswordChangePeriod\"/>\n            <mat-error>\n              <lib-swui-control-messages\n                [messages]=\"messageErrors\"\n                [control]=\"form.get('forcePasswordChangePeriod')\">\n              </lib-swui-control-messages>\n            </mat-error>\n          </mat-form-field>\n\n          <mat-select formControlName=\"forcePasswordChangePeriodType\" fxFlex=\"20\" style=\"padding: 14px 0 0 10px\">\n            <mat-option *ngFor=\"let periodType of changePasswordPeriodTypes\"\n                        [value]=\"periodType.id\">\n              {{ periodType.displayName | translate }}\n            </mat-option>\n          </mat-select>\n        </div>\n      </mat-card>\n\n      <mat-card style=\"margin: 10px 0 10px 0\">\n        <mat-radio-button [value]=\"'operator_api'\">\n          {{ 'ENTITY_SETUP.USERS.userTypeOperatorAPI' | translate }}\n        </mat-radio-button>\n      </mat-card>\n    </mat-radio-group>\n\n    <mat-error>\n      <lib-swui-control-messages\n        [messages]=\"messageErrors\"\n        [control]=\"userTypeControl\">\n      </lib-swui-control-messages>\n    </mat-error>\n  </div>\n</form>\n"], "mappings": "AAAA,SAAoBA,YAAY,QAA0C,eAAe;AACzF,SAA2DC,UAAU,QAAQ,gBAAgB;AAG7F,SAASC,eAAe,EAAEC,OAAO,QAAQ,MAAM;AAC/C,SAASC,oBAAoB,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,SAAS,EAAEC,SAAS,EAAEC,GAAG,QAAQ,gBAAgB;AACvG,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,SACEC,qCAAqC,EAAEC,gBAAgB,EAAEC,cAAc,QAClE,+EAA+E;AACtF,SAASC,IAAI,QAAQ,kDAAkD;AACvE,SAASC,IAAI,QAAQ,iCAAiC;AAGtD,SAASC,gCAAgC,QAAuB,+BAA+B;AAC/F,SAASC,iBAAiB,QAAQ,mCAAmC;AACrE,SAASC,uBAAuB,QAAQ,8CAA8C;;;;;;;;;;;;;;;;;;;;ICfpFC,EAAA,CAAAC,cAAA,yBAGoC;IAClCD,EAAA,CAAAE,SAAA,0BAMkB;;IAClBF,EAAA,CAAAC,cAAA,gBAAW;IACTD,EAAA,CAAAE,SAAA,mCAG4B;IAEhCF,EADE,CAAAG,YAAA,EAAY,EACG;;;;IAXbH,EAAA,CAAAI,SAAA,EAA6D;IAA7DJ,EAAA,CAAAK,qBAAA,gBAAAL,EAAA,CAAAM,WAAA,sCAA6D;IAG7DN,EAFA,CAAAO,UAAA,4BAA2B,gBAAAC,MAAA,CAAAC,aAAA,kBAAAD,MAAA,CAAAC,aAAA,CAAAC,MAAA,OACc,SAAAF,MAAA,CAAAC,aAAA,CACnB;IAIpBT,EAAA,CAAAI,SAAA,GAA0B;IAC1BJ,EADA,CAAAO,UAAA,aAAAC,MAAA,CAAAG,aAAA,CAA0B,YAAAH,MAAA,CAAAI,aAAA,CACD;;;;;IAI/BZ,EAAA,CAAAa,uBAAA,GAA4D;IAC1Db,EAAA,CAAAC,cAAA,gBAAW;IAAAD,EAAA,CAAAc,MAAA,GAAgD;;IAAAd,EAAA,CAAAG,YAAA,EAAY;IACvEH,EAAA,CAAAE,SAAA,+BAIuB;;;;;IALZF,EAAA,CAAAI,SAAA,GAAgD;IAAhDJ,EAAA,CAAAe,kBAAA,KAAAf,EAAA,CAAAM,WAAA,gCAAAU,OAAA,UAAgD;IAEzDhB,EAAA,CAAAI,SAAA,GAAY;IAEZJ,EAFA,CAAAO,UAAA,aAAY,gBACG,yBACS;;;;;IAwGxBP,EAAA,CAAAC,cAAA,gBAAW;IAAAD,EAAA,CAAAc,MAAA,GAAkC;;IAAAd,EAAA,CAAAG,YAAA,EAAY;IACzDH,EAAA,CAAAE,SAAA,+BAIuB;;;IALZF,EAAA,CAAAI,SAAA,EAAkC;IAAlCJ,EAAA,CAAAiB,iBAAA,CAAAjB,EAAA,CAAAM,WAAA,0BAAkC;IAE3CN,EAAA,CAAAI,SAAA,GAAY;IAEZJ,EAFA,CAAAO,UAAA,aAAY,gBACG,yBACS;;;;;IAO1BP,EADF,CAAAC,cAAA,yBAAwE,oBACrB;IAC/CD,EAAA,CAAAc,MAAA,GACF;;IAAAd,EAAA,CAAAG,YAAA,EAAY;IACZH,EAAA,CAAAE,SAAA,sCAG8B;IAChCF,EAAA,CAAAG,YAAA,EAAiB;;;;IANbH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAkB,kBAAA,MAAAlB,EAAA,CAAAM,WAAA,yDACF;IAEEN,EAAA,CAAAI,SAAA,GAAiB;IACjBJ,EADA,CAAAO,UAAA,kBAAiB,UAAAC,MAAA,CAAAW,aAAA,CACM;;;;;IASvBnB,EAAA,CAAAC,cAAA,qBAAkE;IAChED,EAAA,CAAAc,MAAA,GACF;;IAAAd,EAAA,CAAAG,YAAA,EAAa;;;;IAF+BH,EAAA,CAAAO,UAAA,UAAAa,SAAA,CAAAC,IAAA,CAAqB;IAC/DrB,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAkB,kBAAA,MAAAlB,EAAA,CAAAM,WAAA,OAAAc,SAAA,CAAAE,WAAA,OACF;;;;;IAmCItB,EAAA,CAAAC,cAAA,qBACoC;IAClCD,EAAA,CAAAc,MAAA,GACF;;IAAAd,EAAA,CAAAG,YAAA,EAAa;;;;IAFDH,EAAA,CAAAO,UAAA,UAAAgB,aAAA,CAAAC,EAAA,CAAuB;IACjCxB,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAkB,kBAAA,MAAAlB,EAAA,CAAAM,WAAA,OAAAiB,aAAA,CAAAD,WAAA,OACF;;;ADpKZ,OAAM,MAAOG,iBAAiB;EA4C5B,IAAaC,KAAKA,CAACC,MAAc;IAC/B,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,EAAE;IAC5B,IAAI,CAACG,MAAM,GAAGH,MAAM;IACpB,IAAI,CAACI,iBAAiB,GAAGJ,MAAM,CAACxC,GAAG,CAAC6C,IAAI,IAAI,IAAIrC,IAAI,CAACqC,IAAI,CAAC,CAAC,CAAC7C,GAAG,CAAC6C,IAAI,IAAIA,IAAI,CAACC,cAAc,EAAE,CAAC,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MAC3G,OAAOD,CAAC,CAACE,IAAI,CAACC,aAAa,CAACF,CAAC,CAACC,IAAI,CAAC;IACrC,CAAC,CAAC;EACJ;EAEA,IAAIX,KAAKA,CAAA;IACP,OAAO,IAAI,CAACI,MAAM;EACpB;EAEA,IACIS,cAAcA,CAACC,MAAgB;IACjC,IAAI,CAACZ,KAAK,CAACC,OAAO,CAACW,MAAM,CAAC,EAAE;IAC5B,IAAI,CAACC,eAAe,GAAGD,MAAM;IAC7B,IAAI,CAACE,qBAAqB,EAAE;EAC9B;EAEA,IAAIH,cAAcA,CAAA;IAChB,OAAO,IAAI,CAACE,eAAe;EAC7B;EAEA,IACIE,iBAAiBA,CAACC,SAAmB;IACvC,IAAI,CAAChB,KAAK,CAACC,OAAO,CAACe,SAAS,CAAC,EAAE;IAC/B,IAAI,CAACC,kBAAkB,GAAGD,SAAS;IACnC,IAAI,CAACE,wBAAwB,EAAE;EACjC;EAEA,IAAIH,iBAAiBA,CAAA;IACnB,OAAO,IAAI,CAACE,kBAAkB;EAChC;EAEA,IACIE,aAAaA,CAACC,KAAa;IAC7B,IAAI,CAACA,KAAK,EAAE;MACV;IACF;IAEAA,KAAK,CAACd,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MAClB,OAAOD,CAAC,CAACc,KAAK,CAACX,aAAa,CAACF,CAAC,CAACa,KAAK,CAAC;IACvC,CAAC,CAAC;IAEF,IAAIrB,KAAK,CAACC,OAAO,CAACmB,KAAK,CAAC,IAAIA,KAAK,CAACtC,MAAM,EAAE;MACxC,IAAI,CAACwC,YAAY,CAACC,UAAU,CAACH,KAAK,CAAC7D,GAAG,CAACiE,IAAI,IAAIA,IAAI,CAAC5B,EAAE,CAAC,CAAC;IAC1D;IAEA,IAAI,CAAC6B,cAAc,GAAGL,KAAK;EAC7B;EAEA,IAAID,aAAaA,CAAA;IACf,OAAO,IAAI,CAACM,cAAc;EAC5B;EAEAC,YAAoBC,EAAe,EACzBC,WAA6B,EAC7BC,aAAoC,EACpCC,MAAiB,EACjBC,OAA0B;IAJhB,KAAAJ,EAAE,GAAFA,EAAE;IACZ,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,OAAO,GAAPA,OAAO;IAtGR,KAAAC,IAAI,GAAS,IAAIhE,IAAI,EAAE;IAGvB,KAAAiE,oBAAoB,GAAsC,EAAE;IAG3D,KAAAC,aAAa,GAAG,IAAIlF,YAAY,EAAO;IAExC,KAAAmF,QAAQ,GAAGtE,gBAAgB;IAC3B,KAAAuE,SAAS,GAAGtE,cAAc;IAC1B,KAAAuE,yBAAyB,GAAGzE,qCAAqC;IAE1E,KAAA0E,SAAS,GAAG,KAAK;IACjB,KAAAC,QAAQ,GAAG,KAAK;IAEhB,KAAA1D,aAAa,GAAuB,EAAE;IACtC,KAAA2D,qBAAqB,GAAG,IAAItF,eAAe,CAAC,KAAK,CAAC;IAElD,KAAAiD,iBAAiB,GAAwB,EAAE;IAE3C,KAAApB,aAAa,GAAiB;MAC5B0D,QAAQ,EAAE,qBAAqB;MAC/BC,SAAS,EAAE,sBAAsB;MACjCC,eAAe,EAAE,4BAA4B;MAC7CC,iBAAiB,EAAE,8BAA8B;MACjDC,iBAAiB,EAAE,8BAA8B;MACjDC,mBAAmB,EAAE,gCAAgC;MACrDC,sBAAsB,EAAE,mCAAmC;MAC3DC,wBAAwB,EAAE,qCAAqC;MAC/DC,wBAAwB,EAAE,qCAAqC;MAC/DC,8BAA8B,EAAE,2CAA2C;MAC3EC,GAAG,EAAE;KACN;IAOgB,KAAAC,UAAU,GAAG,IAAIjG,OAAO,EAAO;IAiE9C,IAAI,CAACkG,IAAI,GAAG,IAAI,CAACC,QAAQ,EAAE;EAC7B;EAEAC,QAAQA,CAAA;IACN,IAAI,CAAChB,QAAQ,GAAG,CAAC,CAAC,IAAI,CAACP,IAAI,EAAEwB,SAAS;IACtC,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAACC,iBAAiB,EAAE;IACxB,IAAI,CAACC,gBAAgB,EAAE;IAEvB,IAAI,CAAC5E,aAAa,CAAC6E,YAAY,CAC5BC,IAAI,CACH1G,oBAAoB,EAAE,EACtBK,SAAS,CAAC,IAAI,CAAC2F,UAAU,CAAC,CAC3B,CACAW,SAAS,CAAC,MAAK;MACd,IAAI,CAACzC,YAAY,CAAC0C,QAAQ,CAAC,EAAE,CAAC;MAC9B,IAAI,CAACC,WAAW,EAAE;IACpB,CAAC,CAAC;EACN;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACd,UAAU,CAACe,IAAI,EAAE;IACtB,IAAI,CAACf,UAAU,CAACgB,QAAQ,EAAE;EAC5B;EAEA,IAAIpF,aAAaA,CAAA;IACf,OAAO,IAAI,CAACqE,IAAI,CAACgB,GAAG,CAAC,QAAQ,CAAgB;EAC/C;EAEA,IAAIC,gBAAgBA,CAAA;IAClB,OAAO,IAAI,CAACjB,IAAI,CAACgB,GAAG,CAAC,WAAW,CAAgB;EAClD;EAEA,IAAIE,eAAeA,CAAA;IACjB,OAAO,IAAI,CAAClB,IAAI,CAACgB,GAAG,CAAC,UAAU,CAAgB;EACjD;EAEA,IAAIG,eAAeA,CAAA;IACjB,OAAO,IAAI,CAACnB,IAAI,CAACgB,GAAG,CAAC,UAAU,CAAgB;EACjD;EAEA,IAAII,eAAeA,CAAA;IACjB,OAAO,IAAI,CAACpB,IAAI,CAACgB,GAAG,CAAC,UAAU,CAAgB;EACjD;EAEA,IAAIK,YAAYA,CAAA;IACd,OAAO,IAAI,CAACrB,IAAI,CAACgB,GAAG,CAAC,OAAO,CAAgB;EAC9C;EAEA,IAAIM,YAAYA,CAAA;IACd,OAAO,IAAI,CAACtB,IAAI,CAACgB,GAAG,CAAC,OAAO,CAAgB;EAC9C;EAEA,IAAI/C,YAAYA,CAAA;IACd,OAAO,IAAI,CAAC+B,IAAI,CAACgB,GAAG,CAAC,OAAO,CAAgB;EAC9C;EAEA,IAAIO,aAAaA,CAAA;IACf,OAAO,IAAI,CAACvB,IAAI,CAACgB,GAAG,CAAC,QAAQ,CAAgB;EAC/C;EAEA,IAAIQ,eAAeA,CAAA;IACjB,OAAO,IAAI,CAACxB,IAAI,CAACgB,GAAG,CAAC,UAAU,CAAgB;EACjD;EAEAS,cAAcA,CAACC,KAAK;IAClBA,KAAK,CAACC,cAAc,EAAE;IACtB,IAAI,CAAC3B,IAAI,CAAC4B,gBAAgB,EAAE;IAC5B,IAAI,CAAC3C,SAAS,GAAG,IAAI;IACrB,IAAI,IAAI,CAACe,IAAI,CAAC6B,KAAK,EAAE;MACnB,IAAI,IAAI,CAAC3C,QAAQ,IAAI,IAAI,CAACsC,eAAe,CAACzD,KAAK,KAAK,IAAI,CAACgB,SAAS,CAAC+C,WAAW,IAAI,IAAI,CAACV,eAAe,CAACrD,KAAK,EAAE;QAC5G,IAAI,CAACU,MAAM,CAACsD,IAAI,CAACjH,uBAAuB,EAAE;UACxCkH,KAAK,EAAE,OAAO;UACdC,IAAI,EAAE;YAAEC,OAAO,EAAE;UAAuC,CAAE;UAC1DC,YAAY,EAAE;SACf,CAAC,CAACC,WAAW,EAAE,CAAC3B,IAAI,CACnBzG,MAAM,CAACqI,MAAM,IAAIA,MAAM,CAAC,EACxBjI,SAAS,CAAC,IAAI,CAAC2F,UAAU,CAAC,CAC3B,CAACW,SAAS,CAAC,MAAK;UACf,IAAI,CAAC4B,qBAAqB,EAAE;QAC9B,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,IAAI,CAACA,qBAAqB,EAAE;MAC9B;IACF;EACF;EAEAA,qBAAqBA,CAAA;IACnB,MAAMvE,KAAK,GAAG,IAAI,CAACiC,IAAI,CAACjC,KAAK;IAC7B,IAAIA,KAAK,CAACwE,MAAM,KAAK,GAAG,EAAE;MACxBxE,KAAK,CAACwE,MAAM,GAAG,EAAE;IACnB;IACA,MAAMN,IAAI,GAAG,IAAI,CAAC/C,QAAQ,GAAG;MAAE,GAAG,IAAI,CAACP,IAAI;MAAE,GAAGZ;IAAK,CAAE,GAAGA,KAAK;IAE/D,IAAI,OAAO,IAAIkE,IAAI,EAAE;MACnBA,IAAI,CAACxF,KAAK,GAAG,IAAI,CAACI,MAAM,CAAC7C,MAAM,CAAC,CAAC;QAAEuC;MAAE,CAAE,KAAK0F,IAAI,CAACxF,KAAK,CAAC+F,QAAQ,CAACjG,EAAE,CAAC,CAAC;MACpE,OAAO0F,IAAI,CAAC,MAAM,CAAC;IACrB;IAEA,IAAI,CAACpD,aAAa,CAAC4D,IAAI,CAAC;MAAEC,MAAM,EAAE,IAAI,CAACxD,QAAQ;MAAE+C;IAAI,CAAE,CAAC;EAC1D;EAEQhC,QAAQA,CAAA;IACd,OAAO,IAAI,CAAC3B,EAAE,CAACqE,KAAK,CAAC;MACnBC,SAAS,EAAE,CAAC,EAAE,EAAEhJ,UAAU,CAACwF,QAAQ,CAAC;MACpCyD,QAAQ,EAAE,CAAC,EAAE,EAAEjJ,UAAU,CAACwF,QAAQ,CAAC;MACnC0D,QAAQ,EAAE,CACR,EAAE,EAAE,CACFlJ,UAAU,CAACwF,QAAQ,EACnBvE,iBAAiB,CAACkI,uBAAuB,EACzClI,iBAAiB,CAACwE,SAAS,CAAC,CAAC,CAAC,CAC/B,CACF;MACD2D,KAAK,EAAE,CAAC,EAAE,EAAE,IAAI,CAACC,kBAAkB,EAAE,CAAC;MACtCC,KAAK,EAAE,CAAC,EAAE,EAAErI,iBAAiB,CAACsI,UAAU,CAACtI,iBAAiB,CAACuI,oBAAoB,CAAC,CAAC;MACjFC,QAAQ,EAAE,CAAC,EAAE,EAAE,IAAI,CAACC,qBAAqB,EAAE,CAAC;MAC5CC,MAAM,EAAE,CAAC,EAAE,CAAC;MACZ9G,KAAK,EAAE,CAAC,EAAE,EAAE7C,UAAU,CAACwF,QAAQ,CAAC;MAChCmD,MAAM,EAAE,CAAC,IAAI,EAAE3I,UAAU,CAACwF,QAAQ,CAAC;MACnCoE,QAAQ,EAAE,CAAC,EAAE,EAAE5J,UAAU,CAACwF,QAAQ,CAAC;MACnCqE,6BAA6B,EAAE,CAAC,SAAS,EAAE7J,UAAU,CAACwF,QAAQ,CAAC;MAC/DsE,yBAAyB,EAAE,CAAC,CAAC,EAAE9J,UAAU,CAAC+J,OAAO,CAAC,CAAC/J,UAAU,CAACwF,QAAQ,EAAExF,UAAU,CAACkG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;KAC5F,CAAC;EACJ;EAEQS,gBAAgBA,CAAA;IACtB,IAAI,CAAC7B,OAAO,CAACkF,mBAAmB,EAAE,CAC/BnD,IAAI,CAACrG,SAAS,CAAC,IAAI,CAAC2F,UAAU,CAAC,CAAC,CAChCW,SAAS,CAAC,CAAC,CAACjE,KAAK,EAAEoH,SAAS,EAAEC,WAAW,CAAC,KAAI;MAC7C,MAAMhG,aAAa,GAAG,EAAE;MACxB,MAAM5B,aAAa,GAAG,EAAE;MAExB,IAAI,CAACyC,IAAI,CAAClC,KAAK,CAACsH,OAAO,CAAEhH,IAAI,IAAI;QAC/B,IAAI8G,SAAS,CAAC9G,IAAI,CAACR,EAAE,CAAC,EAAE;UACtBuB,aAAa,CAACkG,IAAI,CAACjH,IAAI,CAAC;QAC1B,CAAC,MAAM;UACLb,aAAa,CAAC8H,IAAI,CAAC;YAAEzH,EAAE,EAAEQ,IAAI,CAACR,EAAE;YAAEa,IAAI,EAAEL,IAAI,CAACiB;UAAK,CAAE,CAAC;QACvD;MACF,CAAC,CAAC;MAEF,IAAI,CAACF,aAAa,GAAGA,aAAa;MAElC,IAAI,IAAI,CAACA,aAAa,EAAE;QACtB,IAAI,CAACG,YAAY,CAACC,UAAU,CAAC,IAAI,CAACJ,aAAa,CAAC5D,GAAG,CAACiE,IAAI,IAAIA,IAAI,CAAC5B,EAAE,CAAC,CAAC;MACvE;MAEA,IAAI,CAACL,aAAa,GAAGA,aAAa;MAElC;MACA,IAAI,CAAC+H,QAAQ,GAAGxH,KAAK;MACrB,IAAI,CAACyH,SAAS,GAAG,CAAC,GAAG,IAAI,CAACpG,aAAa,CAAC,CAAC5D,GAAG,CAAC6C,IAAI,IAAG;QAClD,OAAOoH,MAAM,CAACC,MAAM,CAAC,EAAE,EAAErH,IAAI,EAAE;UAAEsH,QAAQ,EAAE;QAAI,CAAE,CAAC;MACpD,CAAC,CAAC,CACCC,MAAM,CAACR,WAAW,CAACrH,KAAK,CAAC,CACzBzC,MAAM,CAAC,CAAC+D,KAAK,EAAEwG,GAAG,EAAEC,GAAG,KAAKA,GAAG,CAACC,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACnI,EAAE,KAAKwB,KAAK,CAACxB,EAAE,CAAC,KAAKgI,GAAG,CAAC;MAE7E,IAAI,CAAC3D,WAAW,EAAE;IACpB,CAAC,CAAC;EACN;EAEQA,WAAWA,CAAA;IACjB,IAAI,CAAC,IAAI,CAACjF,aAAa,CAACoC,KAAK,EAAE;MAC7B,IAAI,CAACtB,KAAK,GAAG,EAAE;MACf;IACF;IACA,MAAM8F,MAAM,GAAG,IAAI,CAAC/G,aAAa,CAAC,IAAI,CAACG,aAAa,CAACoC,KAAK,CAAC;IAE3D,IAAI,IAAI,CAACW,OAAO,CAACiG,uBAAuB,CAACpC,MAAM,EAAEqC,MAAM,EAAE,CAAC,EAAE;MAC1D,IAAI,CAACnI,KAAK,GAAG,IAAI,CAACwH,QAAQ;IAC5B,CAAC,MAAM;MACL,IAAI,CAACxH,KAAK,GAAG,IAAI,CAACyH,SAAS;IAC7B;EACF;EAEQ5D,iBAAiBA,CAAA;IACvB,MAAMuE,UAAU,GAAG,IAAI,CAAClG,IAAI,CAAC4D,MAAM,KAAK,EAAE,GAAGjI,iBAAiB,CAACwK,0BAA0B,GAAGxK,iBAAiB,CAACyK,gBAAgB;IAE9H,IAAI,IAAI,CAAC7F,QAAQ,IAAI,CAAC,IAAI,CAACX,WAAW,CAACyG,UAAU,CAAC,CAACH,UAAU,CAAC,CAAC,EAAE;MAC/D,IAAI,CAAC7E,IAAI,CAACgB,GAAG,CAAC,+BAA+B,CAAC,CAACiE,OAAO,EAAE;MACxD,IAAI,CAACjF,IAAI,CAACgB,GAAG,CAAC,2BAA2B,CAAC,CAACiE,OAAO,EAAE;MACpD,IAAI,CAACzD,eAAe,CAACyD,OAAO,EAAE;IAChC;IAEA,IAAI,CAACzD,eAAe,CAAChB,YAAY,CAACC,IAAI,CACpCzG,MAAM,CAAEwJ,QAAQ,IAAK,IAAI,CAACtE,QAAQ,IAAIsE,QAAQ,KAAK,IAAI,CAACzE,SAAS,CAACmG,EAAE,CAAC,EACrE/K,SAAS,CAAC,MACR,IAAI,CAACsE,MAAM,CAACsD,IAAI,CAACjH,uBAAuB,EAAE;MACxCkH,KAAK,EAAE,OAAO;MACdC,IAAI,EAAE;QAAEC,OAAO,EAAE;MAAuC,CAAE;MAC1DC,YAAY,EAAE;KACf,CAAC,CAACC,WAAW,EAAE,CAAC,EACnBpI,MAAM,CAACqI,MAAM,IAAI,CAACA,MAAM,CAAC,EACzBhI,GAAG,CAAC,MAAM,IAAI,CAACmH,eAAe,CAACb,QAAQ,CAAC,IAAI,CAAC5B,SAAS,CAAC+C,WAAW,CAAC,CAAC,EACpE1H,SAAS,CAAC,IAAI,CAAC2F,UAAU,CAAC,CAC3B,CAACW,SAAS,EAAE;EACf;EAEQyE,2BAA2BA,CAAA;IACjC,IAAIN,UAAU,GAAWvK,iBAAiB,CAAC8K,oBAAoB;IAE/D,IAAI,IAAI,CAAC7C,MAAM,CAACqC,MAAM,EAAE,EAAE;MACxBC,UAAU,GAAGvK,iBAAiB,CAAC+K,8BAAwC;IACzE;IAEA,OAAO,IAAI,CAAC9G,WAAW,CAAC+G,SAAS,CAAC,CAACT,UAAU,CAAC,CAAC;EACjD;EAEQU,wBAAwBA,CAAA;IAC9B,IAAIV,UAAU,GAAWvK,iBAAiB,CAACkL,eAAe;IAE1D,IAAI,IAAI,CAACjD,MAAM,CAACqC,MAAM,EAAE,EAAE;MACxBC,UAAU,GAAGvK,iBAAiB,CAACmL,yBAAmC;IACpE;IAEA,OAAO,IAAI,CAAClH,WAAW,CAAC+G,SAAS,CAAC,CAACT,UAAU,CAAC,CAAC;EACjD;EAEQpH,qBAAqBA,CAAA;IAC3B,IAAI,CAAC4D,YAAY,CAACqE,aAAa,CAAC,IAAI,CAACzC,kBAAkB,EAAE,CAAC;IAC1D,IAAI,CAAC5B,YAAY,CAACsE,sBAAsB,EAAE;EAC5C;EAEQ9H,wBAAwBA,CAAA;IAC9B,MAAM+H,QAAQ,GAAG,IAAI,CAACjH,IAAI,IAAI,WAAW,IAAI,IAAI,CAACA,IAAI;IAEtD,IAAI,CAACyC,eAAe,CAACsE,aAAa,CAAC,IAAI,CAACpC,qBAAqB,CAACsC,QAAQ,CAAC,CAAC;IACxE,IAAI,CAACxE,eAAe,CAACuE,sBAAsB,EAAE;EAC/C;EAEQ1C,kBAAkBA,CAAA;IACxB,IAAI4C,UAAU,GAAkB,CAACjM,UAAU,CAACwF,QAAQ,EAAEvE,iBAAiB,CAACiL,cAAc,CAAC;IAEvF,IAAI,IAAI,CAACxI,cAAc,IAAI,IAAI,CAACA,cAAc,CAAC7B,MAAM,EAAE;MACrDoK,UAAU,GAAG,CACX,GAAGA,UAAU,EACb,GAAG,IAAI,CAACvI,cAAc,CAACpD,GAAG,CAAE8I,KAAK,IAAKnI,iBAAiB,CAACkL,eAAe,CAAC/C,KAAK,CAAC,CAAC,CAChF;IACH;IAEA,OAAOpJ,UAAU,CAAC+J,OAAO,CAACkC,UAAU,CAAC;EACvC;EAEQvC,qBAAqBA,CAACsC,QAAA,GAAoB,KAAK;IACrD,IAAIC,UAAU,GAAkB,CAC9BhL,iBAAiB,CAACkI,uBAAuB,EACzClI,iBAAiB,CAACmL,kBAAkB,EAAE,EACtCnL,iBAAiB,CAACoL,iBAAiB,CACpC;IAED,IAAI,IAAI,CAACvI,iBAAiB,IAAI,IAAI,CAACA,iBAAiB,CAACjC,MAAM,EAAE;MAC3DoK,UAAU,GAAG,CACX,GAAGA,UAAU,EACb,GAAG,IAAI,CAACnI,iBAAiB,CAACxD,GAAG,CAAEgM,GAAG,IAAKrL,iBAAiB,CAAC2E,iBAAiB,CAAC0G,GAAG,CAAC,CAAC,CACjF;IACH;IAEA,IAAI,IAAI,CAACvH,IAAI,IAAI,UAAU,IAAI,IAAI,CAACA,IAAI,EAAE;MACxCkH,UAAU,CAAC7B,IAAI,CAACnJ,iBAAiB,CAACkL,eAAe,CAAC,IAAI,CAACpH,IAAI,CAACmE,QAAQ,CAAC,CAAC;IACxE;IAEA,IAAI,CAAC8C,QAAQ,EAAE;MACbC,UAAU,CAAC7B,IAAI,CAACpK,UAAU,CAACwF,QAAQ,CAAC;IACtC;IAEA,OAAOwG,QAAQ,GACX/K,iBAAiB,CAACsI,UAAU,CAACvJ,UAAU,CAAC+J,OAAO,CAACkC,UAAU,CAAC,CAAC,GAC5DjM,UAAU,CAAC+J,OAAO,CAACkC,UAAU,CAAC;EACpC;EAEQxF,YAAYA,CAAA;IAClB,IAAI,CAACL,IAAI,CAAC9B,UAAU,CAAC,IAAI,CAACS,IAAI,CAAC;IAE/B,IAAI,IAAI,CAACO,QAAQ,EAAE;MACjB,IAAI,IAAI,CAACP,IAAI,CAAC4D,MAAM,KAAK,EAAE,EAAE;QAC3B,IAAI,CAAC5G,aAAa,CAACgF,QAAQ,CAAC,GAAG,CAAC;QAEhC,IAAI,CAAC,IAAI,CAACpC,WAAW,CAAC4H,YAAY,EAAE;UAClC,IAAI,CAACnG,IAAI,CAACiF,OAAO,CAAC;YAAEmB,SAAS,EAAE;UAAK,CAAE,CAAC;QACzC;MACF;MAEA,IAAI,CAAChF,eAAe,CAAC6D,OAAO,EAAE;MAC9B,IAAI,CAAC5D,YAAY,CAAC4D,OAAO,EAAE;MAC3B,IAAI,CAACtJ,aAAa,CAACsJ,OAAO,EAAE;MAE5B,IAAI,IAAI,CAACnH,aAAa,EAAE;QACtB,IAAI,CAACG,YAAY,CAACC,UAAU,CAAC,IAAI,CAACJ,aAAa,CAAC5D,GAAG,CAACiE,IAAI,IAAIA,IAAI,CAAC5B,EAAE,CAAC,CAAC;MACvE;MAEA,IAAI,CAACgF,aAAa,CAACrD,UAAU,CAAC,IAAI,CAACS,IAAI,CAAC4E,MAAM,CAAC;MAC/C,IAAI,CAAC8C,UAAU,GAAG,IAAI,CAAC9E,aAAa,CAACxD,KAAK;IAC5C;IAEA,IAAI,IAAI,CAACoH,2BAA2B,EAAE,EAAE;MACtC,IAAI,CAAC/D,eAAe,CAACkF,MAAM,EAAE;MAC7B,IAAI,CAACjF,YAAY,CAACqE,aAAa,CAAC,IAAI,CAACzC,kBAAkB,EAAE,CAAC;IAC5D;IAEA,IAAI,IAAI,CAACsC,wBAAwB,EAAE,EAAE;MACnC,IAAI,CAAClE,YAAY,CAACiF,MAAM,EAAE;IAC5B;EACF;EAEQlG,kBAAkBA,CAAA;IACxB,IAAI,CAACjB,qBAAqB,CAAC2B,IAAI,CAAC,IAAI,CAAC;IACrC,IAAI,CAACtC,aAAa,CAAC+H,iBAAiB,EAAE,CAAC9F,IAAI,CACzCvG,GAAG,CAACsM,SAAS,IAAI5L,gCAAgC,CAAC4L,SAAS,EAAE,CAAC,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC,EAC3EtM,GAAG,CAACsB,aAAa,IAAIA,aAAa,CAACtB,GAAG,CAACuM,MAAM,KAAK;MAChD,GAAGA,MAAM;MACTlK,EAAE,EAAEkK,MAAM,CAAClK,EAAE,IAAI;KAClB,CAAC,CAAC,CAAC,EACJrC,GAAG,CAACsB,aAAa,IAAG;MAClB,IAAI,IAAI,CAAC+G,MAAM,CAACmE,IAAI,IAAI,IAAI,CAACnE,MAAM,CAACmE,IAAI,KAAK,GAAG,EAAE;QAChD,OAAOlL,aAAa,CAACxB,MAAM,CAAC,CAAC;UAAEuC;QAAE,CAAE,KAAKA,EAAE,CAACoK,OAAO,CAAC,IAAI,CAACpE,MAAM,CAACmE,IAAI,CAAC,KAAK,CAAC,CAAC;MAC7E;MACA,OAAOlL,aAAa;IACtB,CAAC,CAAC,EACFtB,GAAG,CAAEsB,aAAiC,IAAI;MACxC,IAAI,CAAC,IAAI,CAAC+C,WAAW,CAAC4H,YAAY,EAAE;QAClC,MAAMS,UAAU,GAAGpL,aAAa,CAACqL,IAAI,CAAC,CAAC;UAAEtK;QAAE,CAAE,KAAKA,EAAE,KAAK,GAAG,CAAC;QAC7D,IAAIqK,UAAU,EAAE;UACdA,UAAU,CAACvC,QAAQ,GAAG,IAAI;QAC5B;QACA,MAAMyC,UAAU,GAAGtL,aAAa,CAACqL,IAAI,CAAC,CAAC;UAAEtK;QAAE,CAAE,KAAKA,EAAE,KAAK,IAAI,CAACwK,KAAK,CAACL,IAAI,CAAC;QACzE,IAAII,UAAU,EAAE;UACdA,UAAU,CAACzC,QAAQ,GAAG,IAAI;QAC5B;MACF;MACA,OAAO7I,aAAa;IACtB,CAAC,CAAC,EACFvB,QAAQ,CAAC,MAAK;MACZ,IAAI,CAACkF,qBAAqB,CAAC2B,IAAI,CAAC,KAAK,CAAC;IACxC,CAAC,CAAC,EACF1G,SAAS,CAAC,IAAI,CAAC2F,UAAU,CAAC,CAC3B,CAACW,SAAS,CAAClF,aAAa,IAAG;MAC1B,IAAI,CAACA,aAAa,GAAGA,aAAa;IACpC,CAAC,CAAC;EACJ;;;uCA3bWgB,iBAAiB,EAAAzB,EAAA,CAAAiM,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAnM,EAAA,CAAAiM,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAArM,EAAA,CAAAiM,iBAAA,CAAAK,EAAA,CAAAC,aAAA,GAAAvM,EAAA,CAAAiM,iBAAA,CAAAO,EAAA,CAAAC,SAAA,GAAAzM,EAAA,CAAAiM,iBAAA,CAAAS,EAAA,CAAAC,iBAAA;IAAA;EAAA;;;YAAjBlL,iBAAiB;MAAAmL,SAAA;MAAAC,MAAA;QAAAjJ,IAAA;QAAAoI,KAAA;QAAAxE,MAAA;QAAA3D,oBAAA;QAAAnC,KAAA;QAAAa,cAAA;QAAAI,iBAAA;QAAAI,aAAA;MAAA;MAAA+J,OAAA;QAAAhJ,aAAA;MAAA;MAAAiJ,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UC7B9BpN,EAAA,CAAAC,cAAA,cAAyB;UAmBvBD,EAlBA,CAAAsN,UAAA,IAAAC,2CAAA,4BAGoC,IAAAC,yCAAA,0BAewB;;UAWxDxN,EAFJ,CAAAC,cAAA,aAAoB,wBAC6C,gBAClD;UAAAD,EAAA,CAAAc,MAAA,GAAwD;;UAAAd,EAAA,CAAAG,YAAA,EAAY;UAC/EH,EAAA,CAAAE,SAAA,eAE0E;;UAC1EF,EAAA,CAAAC,cAAA,iBAAW;UACTD,EAAA,CAAAE,SAAA,oCAG4B;UAEhCF,EADE,CAAAG,YAAA,EAAY,EACG;UAGfH,EADF,CAAAC,cAAA,yBAAiD,iBACpC;UAAAD,EAAA,CAAAc,MAAA,IAAuD;;UAAAd,EAAA,CAAAG,YAAA,EAAY;UAC9EH,EAAA,CAAAE,SAAA,gBAEyE;;UACzEF,EAAA,CAAAC,cAAA,iBAAW;UACTD,EAAA,CAAAE,SAAA,oCAG4B;UAGlCF,EAFI,CAAAG,YAAA,EAAY,EACG,EACb;UAIFH,EAFJ,CAAAC,cAAA,cAAoB,yBAC6C,iBAClD;UAAAD,EAAA,CAAAc,MAAA,IAAuD;;UAAAd,EAAA,CAAAG,YAAA,EAAY;UAC9EH,EAAA,CAAAE,SAAA,iBAEyE;;UACzEF,EAAA,CAAAC,cAAA,iBAAW;UACTD,EAAA,CAAAE,SAAA,oCAG4B;UAEhCF,EADE,CAAAG,YAAA,EAAY,EACG;UAGfH,EADF,CAAAC,cAAA,yBAAiD,iBACpC;UAAAD,EAAA,CAAAc,MAAA,IAAuD;;UAAAd,EAAA,CAAAG,YAAA,EAAY;UAC9EH,EAAA,CAAAE,SAAA,iBAEyE;;UACzEF,EAAA,CAAAC,cAAA,iBAAW;UACTD,EAAA,CAAAE,SAAA,oCAG4B;UAGlCF,EAFI,CAAAG,YAAA,EAAY,EACG,EACb;UAIFH,EAFJ,CAAAC,cAAA,cAAoB,yBAC6C,iBAClD;UAAAD,EAAA,CAAAc,MAAA,IAAoD;;UAAAd,EAAA,CAAAG,YAAA,EAAY;UAC3EH,EAAA,CAAAE,SAAA,iBAEsE;;UACtEF,EAAA,CAAAC,cAAA,iBAAW;UACTD,EAAA,CAAAE,SAAA,oCAG4B;UAEhCF,EADE,CAAAG,YAAA,EAAY,EACG;UAGfH,EADF,CAAAC,cAAA,yBAAiD,iBACpC;UAAAD,EAAA,CAAAc,MAAA,IAA0D;;UAAAd,EAAA,CAAAG,YAAA,EAAY;UACjFH,EAAA,CAAAE,SAAA,iBAE4E;;UAC5EF,EAAA,CAAAC,cAAA,iBAAW;UACTD,EAAA,CAAAE,SAAA,oCAG4B;UAGlCF,EAFI,CAAAG,YAAA,EAAY,EACG,EACb;UAIFH,EAFJ,CAAAC,cAAA,cAAoB,0BACoC,iBACzC;UAAAD,EAAA,CAAAc,MAAA,IAAkD;;UAAAd,EAAA,CAAAG,YAAA,EAAY;UACzEH,EAAA,CAAAE,SAAA,uCAG8B;UAC9BF,EAAA,CAAAC,cAAA,iBAAW;UACTD,EAAA,CAAAE,SAAA,oCAG4B;UAEhCF,EADE,CAAAG,YAAA,EAAY,EACG;UAEjBH,EAAA,CAAAsN,UAAA,KAAAG,yCAAA,gCAAAzN,EAAA,CAAA0N,sBAAA,CAA2B;UAQ7B1N,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,cAAoB;UAClBD,EAAA,CAAAsN,UAAA,KAAAK,4CAAA,6BAAwE;UAS1E3N,EAAA,CAAAG,YAAA,EAAM;UAIFH,EAFJ,CAAAC,cAAA,cAAoB,yBAC+B,iBACpC;UAAAD,EAAA,CAAAc,MAAA,IAAmD;;UAAAd,EAAA,CAAAG,YAAA,EAAY;UAC1EH,EAAA,CAAAC,cAAA,sBAA4D;UAAhDD,EAAA,CAAA4N,gBAAA,yBAAAC,8DAAAC,MAAA;YAAA9N,EAAA,CAAA+N,aAAA,CAAAC,GAAA;YAAAhO,EAAA,CAAAiO,kBAAA,CAAAZ,GAAA,CAAA/B,UAAA,EAAAwC,MAAA,MAAAT,GAAA,CAAA/B,UAAA,GAAAwC,MAAA;YAAA,OAAA9N,EAAA,CAAAkO,WAAA,CAAAJ,MAAA;UAAA,EAAsB;UAChC9N,EAAA,CAAAsN,UAAA,KAAAa,wCAAA,yBAAkE;UAGpEnO,EAAA,CAAAG,YAAA,EAAa;UACbH,EAAA,CAAAC,cAAA,iBAAW;UACTD,EAAA,CAAAE,SAAA,oCAG4B;UAGlCF,EAFI,CAAAG,YAAA,EAAY,EACG,EACb;UAGJH,EADF,CAAAC,cAAA,eAA4C,UACtC;UAAAD,EAAA,CAAAc,MAAA,IAAuD;;UAC7Dd,EAD6D,CAAAG,YAAA,EAAK,EAC5D;UAKAH,EAHN,CAAAC,cAAA,cAAoB,2BACuC,gBAC7C,4BACyB;UAC/BD,EAAA,CAAAc,MAAA,IACF;;UAAAd,EAAA,CAAAG,YAAA,EAAmB;UAIfH,EAFJ,CAAAC,cAAA,WAAK,yBAC8C,iBACpC;UAAAD,EAAA,CAAAc,MAAA,IAA4D;;UAAAd,EAAA,CAAAG,YAAA,EAAY;UACnFH,EAAA,CAAAE,SAAA,iBAAoF;UACpFF,EAAA,CAAAC,cAAA,iBAAW;UACTD,EAAA,CAAAE,SAAA,oCAG4B;UAEhCF,EADE,CAAAG,YAAA,EAAY,EACG;UAEjBH,EAAA,CAAAC,cAAA,sBAAuG;UACrGD,EAAA,CAAAsN,UAAA,KAAAc,wCAAA,yBACoC;UAK1CpO,EAFI,CAAAG,YAAA,EAAa,EACT,EACG;UAGTH,EADF,CAAAC,cAAA,oBAAwC,4BACK;UACzCD,EAAA,CAAAc,MAAA,IACF;;UAEJd,EAFI,CAAAG,YAAA,EAAmB,EACV,EACK;UAElBH,EAAA,CAAAC,cAAA,kBAAW;UACTD,EAAA,CAAAE,SAAA,qCAG4B;UAGlCF,EAFI,CAAAG,YAAA,EAAY,EACR,EACD;;;UApNDH,EAAA,CAAAO,UAAA,cAAA8M,GAAA,CAAApI,IAAA,CAAkB;UAInBjF,EAAA,CAAAI,SAAA,EAA+B;UAA/BJ,EAAA,CAAAO,UAAA,UAAA8M,GAAA,CAAA5M,aAAA,kBAAA4M,GAAA,CAAA5M,aAAA,CAAAC,MAAA,MAA+B;UAenBV,EAAA,CAAAI,SAAA,EAAoC;UAApCJ,EAAA,CAAAO,UAAA,SAAAP,EAAA,CAAAM,WAAA,QAAA+M,GAAA,CAAAjJ,qBAAA,EAAoC;UAWpCpE,EAAA,CAAAI,SAAA,GAAwD;UAAxDJ,EAAA,CAAAkB,kBAAA,KAAAlB,EAAA,CAAAM,WAAA,oDAAwD;UAG5DN,EAAA,CAAAI,SAAA,GAAiE;UAAjEJ,EAAA,CAAAO,UAAA,gBAAAP,EAAA,CAAAM,WAAA,gDAAiE;UAGpEN,EAAA,CAAAI,SAAA,GAA0B;UAC1BJ,EADA,CAAAO,UAAA,aAAA8M,GAAA,CAAA1M,aAAA,CAA0B,YAAA0M,GAAA,CAAAnH,gBAAA,CACE;UAMrBlG,EAAA,CAAAI,SAAA,GAAuD;UAAvDJ,EAAA,CAAAkB,kBAAA,KAAAlB,EAAA,CAAAM,WAAA,oDAAuD;UAG3DN,EAAA,CAAAI,SAAA,GAAgE;UAAhEJ,EAAA,CAAAO,UAAA,gBAAAP,EAAA,CAAAM,WAAA,+CAAgE;UAGnEN,EAAA,CAAAI,SAAA,GAA0B;UAC1BJ,EADA,CAAAO,UAAA,aAAA8M,GAAA,CAAA1M,aAAA,CAA0B,YAAA0M,GAAA,CAAAlH,eAAA,CACC;UAQpBnG,EAAA,CAAAI,SAAA,GAAuD;UAAvDJ,EAAA,CAAAkB,kBAAA,KAAAlB,EAAA,CAAAM,WAAA,oDAAuD;UAG3DN,EAAA,CAAAI,SAAA,GAAgE;UAAhEJ,EAAA,CAAAO,UAAA,gBAAAP,EAAA,CAAAM,WAAA,+CAAgE;UAGnEN,EAAA,CAAAI,SAAA,GAA0B;UAC1BJ,EADA,CAAAO,UAAA,aAAA8M,GAAA,CAAA1M,aAAA,CAA0B,YAAA0M,GAAA,CAAAjH,eAAA,CACC;UAMpBpG,EAAA,CAAAI,SAAA,GAAuD;UAAvDJ,EAAA,CAAAkB,kBAAA,KAAAlB,EAAA,CAAAM,WAAA,oDAAuD;UAG3DN,EAAA,CAAAI,SAAA,GAAgE;UAAhEJ,EAAA,CAAAO,UAAA,gBAAAP,EAAA,CAAAM,WAAA,+CAAgE;UAGnEN,EAAA,CAAAI,SAAA,GAA0B;UAC1BJ,EADA,CAAAO,UAAA,aAAA8M,GAAA,CAAA1M,aAAA,CAA0B,YAAA0M,GAAA,CAAAhH,eAAA,CACC;UAQpBrG,EAAA,CAAAI,SAAA,GAAoD;UAApDJ,EAAA,CAAAkB,kBAAA,KAAAlB,EAAA,CAAAM,WAAA,iDAAoD;UAGxDN,EAAA,CAAAI,SAAA,GAA6D;UAA7DJ,EAAA,CAAAO,UAAA,gBAAAP,EAAA,CAAAM,WAAA,4CAA6D;UAGhEN,EAAA,CAAAI,SAAA,GAA0B;UAC1BJ,EADA,CAAAO,UAAA,aAAA8M,GAAA,CAAA1M,aAAA,CAA0B,YAAA0M,GAAA,CAAA/G,YAAA,CACF;UAMjBtG,EAAA,CAAAI,SAAA,GAA0D;UAA1DJ,EAAA,CAAAkB,kBAAA,KAAAlB,EAAA,CAAAM,WAAA,uDAA0D;UAG9DN,EAAA,CAAAI,SAAA,GAAmE;UAAnEJ,EAAA,CAAAO,UAAA,gBAAAP,EAAA,CAAAM,WAAA,kDAAmE;UAGtEN,EAAA,CAAAI,SAAA,GAA0B;UAC1BJ,EADA,CAAAO,UAAA,aAAA8M,GAAA,CAAA1M,aAAA,CAA0B,YAAA0M,GAAA,CAAA9G,YAAA,CACF;UAQjBvG,EAAA,CAAAI,SAAA,GAAkD;UAAlDJ,EAAA,CAAAiB,iBAAA,CAAAjB,EAAA,CAAAM,WAAA,4CAAkD;UAG3DN,EAAA,CAAAI,SAAA,GAA2B;UAA3BJ,EAAA,CAAAO,UAAA,UAAA8M,GAAA,CAAAtL,iBAAA,CAA2B;UAIzB/B,EAAA,CAAAI,SAAA,GAA0B;UAC1BJ,EADA,CAAAO,UAAA,aAAA8M,GAAA,CAAA1M,aAAA,CAA0B,YAAA0M,GAAA,CAAAnK,YAAA,CACF;UAgBblD,EAAA,CAAAI,SAAA,GAAc;UAAdJ,EAAA,CAAAO,UAAA,SAAA8M,GAAA,CAAAlJ,QAAA,CAAc;UAalBnE,EAAA,CAAAI,SAAA,GAAmD;UAAnDJ,EAAA,CAAAiB,iBAAA,CAAAjB,EAAA,CAAAM,WAAA,6CAAmD;UAClDN,EAAA,CAAAI,SAAA,GAAsB;UAAtBJ,EAAA,CAAAqO,gBAAA,UAAAhB,GAAA,CAAA/B,UAAA,CAAsB;UACDtL,EAAA,CAAAI,SAAA,EAAW;UAAXJ,EAAA,CAAAO,UAAA,YAAA8M,GAAA,CAAAtJ,QAAA,CAAW;UAMxC/D,EAAA,CAAAI,SAAA,GAA0B;UAC1BJ,EADA,CAAAO,UAAA,aAAA8M,GAAA,CAAA1M,aAAA,CAA0B,YAAA0M,GAAA,CAAA7G,aAAA,CACD;UAO3BxG,EAAA,CAAAI,SAAA,GAAuD;UAAvDJ,EAAA,CAAAkB,kBAAA,KAAAlB,EAAA,CAAAM,WAAA,oDAAuD;UAMrCN,EAAA,CAAAI,SAAA,GAAc;UAAdJ,EAAA,CAAAO,UAAA,eAAc;UAC9BP,EAAA,CAAAI,SAAA,EACF;UADEJ,EAAA,CAAAkB,kBAAA,MAAAlB,EAAA,CAAAM,WAAA,+CACF;UAIeN,EAAA,CAAAI,SAAA,GAA4D;UAA5DJ,EAAA,CAAAiB,iBAAA,CAAAjB,EAAA,CAAAM,WAAA,qDAA4D;UAInEN,EAAA,CAAAI,SAAA,GAA0B;UAC1BJ,EADA,CAAAO,UAAA,aAAA8M,GAAA,CAAA1M,aAAA,CAA0B,YAAA0M,GAAA,CAAApI,IAAA,CAAAgB,GAAA,8BACuB;UAMlBjG,EAAA,CAAAI,SAAA,GAA4B;UAA5BJ,EAAA,CAAAO,UAAA,YAAA8M,GAAA,CAAApJ,yBAAA,CAA4B;UASjDjE,EAAA,CAAAI,SAAA,GAAwB;UAAxBJ,EAAA,CAAAO,UAAA,yBAAwB;UACxCP,EAAA,CAAAI,SAAA,EACF;UADEJ,EAAA,CAAAkB,kBAAA,MAAAlB,EAAA,CAAAM,WAAA,wDACF;UAMAN,EAAA,CAAAI,SAAA,GAA0B;UAC1BJ,EADA,CAAAO,UAAA,aAAA8M,GAAA,CAAA1M,aAAA,CAA0B,YAAA0M,GAAA,CAAA5G,eAAA,CACC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}