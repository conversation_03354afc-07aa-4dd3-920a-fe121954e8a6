{"ast": null, "code": "import { MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nconst _c0 = a0 => ({\n  statusTitle: a0\n});\nexport class StatusConfirmComponent {\n  constructor(data, dialogRef) {\n    this.data = data;\n    this.dialogRef = dialogRef;\n    this.statusTitleMap = {\n      'normal': 'Active',\n      'suspended': 'Inactive',\n      'maintenance': 'Maintenance',\n      'blocked_by_admin': 'Blocked',\n      'test': 'Test'\n    };\n  }\n  confirm() {\n    this.dialogRef.close(this.data);\n  }\n  getStatusTitle(status) {\n    return this.statusTitleMap[status];\n  }\n  static {\n    this.ɵfac = function StatusConfirmComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || StatusConfirmComponent)(i0.ɵɵdirectiveInject(MAT_DIALOG_DATA), i0.ɵɵdirectiveInject(i1.MatDialogRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: StatusConfirmComponent,\n      selectors: [[\"status-confirm\"]],\n      decls: 13,\n      vars: 18,\n      consts: [[\"mat-dialog-title\", \"\"], [1, \"mat-typography\"], [3, \"innerHTML\"], [\"align\", \"end\"], [\"mat-button\", \"\", \"color\", \"primary\", \"mat-dialog-close\", \"\", 1, \"mat-button-md\"], [\"mat-flat-button\", \"\", \"color\", \"primary\", \"cdkFocusInitial\", \"\", 1, \"mat-button-md\", 3, \"click\"]],\n      template: function StatusConfirmComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"h2\", 0);\n          i0.ɵɵtext(1);\n          i0.ɵɵpipe(2, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"mat-dialog-content\", 1);\n          i0.ɵɵelement(4, \"div\", 2);\n          i0.ɵɵpipe(5, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"mat-dialog-actions\", 3)(7, \"button\", 4);\n          i0.ɵɵtext(8);\n          i0.ɵɵpipe(9, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function StatusConfirmComponent_Template_button_click_10_listener() {\n            return ctx.confirm();\n          });\n          i0.ɵɵtext(11);\n          i0.ɵɵpipe(12, \"translate\");\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 4, \"BUSINESS_STRUCTURE.MODAL_STATUS.setStatus\", i0.ɵɵpureFunction1(14, _c0, ctx.getStatusTitle(ctx.data.confirmStatus))), \"\\n\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind2(5, 7, \"BUSINESS_STRUCTURE.MODAL_STATUS.changeStatus\", i0.ɵɵpureFunction1(16, _c0, ctx.getStatusTitle(ctx.data.confirmStatus))), i0.ɵɵsanitizeHtml);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(9, 10, \"ENTITY_SETUP.REGIONAL.MODALS.btnCancel\"), \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(12, 12, \"ENTITY_SETUP.REGIONAL.MODALS.btnApply\"), \" \");\n        }\n      },\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["MAT_DIALOG_DATA", "StatusConfirmComponent", "constructor", "data", "dialogRef", "statusTitleMap", "confirm", "close", "getStatusTitle", "status", "i0", "ɵɵdirectiveInject", "i1", "MatDialogRef", "selectors", "decls", "vars", "consts", "template", "StatusConfirmComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵlistener", "StatusConfirmComponent_Template_button_click_10_listener", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵpipeBind2", "ɵɵpureFunction1", "_c0", "confirmStatus", "ɵɵproperty", "ɵɵsanitizeHtml", "ɵɵpipeBind1"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/mat-business-structure/dialogs/status-confirm/status-confirm.component.ts", "/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/mat-business-structure/dialogs/status-confirm/status-confirm.component.html"], "sourcesContent": ["import { Component, Inject } from '@angular/core';\nimport { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';\nimport { Entity } from '../../../../../../common/models/entity.model';\n\nexport interface StatusConfirmDialogData {\n  entity: Entity;\n  confirmStatus: string;\n}\n\n@Component({\n  selector: 'status-confirm',\n  templateUrl: './status-confirm.component.html',\n  styleUrls: ['./status-confirm.component.scss']\n})\nexport class StatusConfirmComponent {\n\n  private statusTitleMap = {\n    'normal': 'Active',\n    'suspended': 'Inactive',\n    'maintenance': 'Maintenance',\n    'blocked_by_admin': 'Blocked',\n    'test': 'Test'\n  };\n\n  constructor(\n    @Inject(MAT_DIALOG_DATA) public data: StatusConfirmDialogData,\n    public dialogRef: MatDialogRef<StatusConfirmComponent, StatusConfirmDialogData>,\n  ) {\n  }\n\n  confirm() {\n    this.dialogRef.close(this.data);\n  }\n\n  getStatusTitle( status ) {\n    return this.statusTitleMap[status];\n  }\n}\n", "<h2 mat-dialog-title>\n  {{ 'BUSINESS_STRUCTURE.MODAL_STATUS.setStatus' | translate: { statusTitle: getStatusTitle(data.confirmStatus) } }}\n</h2>\n<mat-dialog-content class=\"mat-typography\">\n  <div [innerHTML]=\"'BUSINESS_STRUCTURE.MODAL_STATUS.changeStatus' | translate:\n          { statusTitle: getStatusTitle(data.confirmStatus) }\">\n  </div>\n</mat-dialog-content>\n<mat-dialog-actions align=\"end\">\n  <button mat-button color=\"primary\" class=\"mat-button-md\" mat-dialog-close>\n    {{ 'ENTITY_SETUP.REGIONAL.MODALS.btnCancel' | translate }}\n  </button>\n  <button mat-flat-button color=\"primary\" class=\"mat-button-md\" cdkFocusInitial (click)=\"confirm()\">\n    {{ 'ENTITY_SETUP.REGIONAL.MODALS.btnApply' | translate }}\n  </button>\n</mat-dialog-actions>\n"], "mappings": "AACA,SAASA,eAAe,QAAsB,0BAA0B;;;;;;AAaxE,OAAM,MAAOC,sBAAsB;EAUjCC,YACkCC,IAA6B,EACtDC,SAAwE;IAD/C,KAAAD,IAAI,GAAJA,IAAI;IAC7B,KAAAC,SAAS,GAATA,SAAS;IAVV,KAAAC,cAAc,GAAG;MACvB,QAAQ,EAAE,QAAQ;MAClB,WAAW,EAAE,UAAU;MACvB,aAAa,EAAE,aAAa;MAC5B,kBAAkB,EAAE,SAAS;MAC7B,MAAM,EAAE;KACT;EAMD;EAEAC,OAAOA,CAAA;IACL,IAAI,CAACF,SAAS,CAACG,KAAK,CAAC,IAAI,CAACJ,IAAI,CAAC;EACjC;EAEAK,cAAcA,CAAEC,MAAM;IACpB,OAAO,IAAI,CAACJ,cAAc,CAACI,MAAM,CAAC;EACpC;;;uCAtBWR,sBAAsB,EAAAS,EAAA,CAAAC,iBAAA,CAWvBX,eAAe,GAAAU,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAXdZ,sBAAsB;MAAAa,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCdnCV,EAAA,CAAAY,cAAA,YAAqB;UACnBZ,EAAA,CAAAa,MAAA,GACF;;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACLd,EAAA,CAAAY,cAAA,4BAA2C;UACzCZ,EAAA,CAAAe,SAAA,aAEM;;UACRf,EAAA,CAAAc,YAAA,EAAqB;UAEnBd,EADF,CAAAY,cAAA,4BAAgC,gBAC4C;UACxEZ,EAAA,CAAAa,MAAA,GACF;;UAAAb,EAAA,CAAAc,YAAA,EAAS;UACTd,EAAA,CAAAY,cAAA,iBAAkG;UAApBZ,EAAA,CAAAgB,UAAA,mBAAAC,yDAAA;YAAA,OAASN,GAAA,CAAAf,OAAA,EAAS;UAAA,EAAC;UAC/FI,EAAA,CAAAa,MAAA,IACF;;UACFb,EADE,CAAAc,YAAA,EAAS,EACU;;;UAdnBd,EAAA,CAAAkB,SAAA,EACF;UADElB,EAAA,CAAAmB,kBAAA,MAAAnB,EAAA,CAAAoB,WAAA,oDAAApB,EAAA,CAAAqB,eAAA,KAAAC,GAAA,EAAAX,GAAA,CAAAb,cAAA,CAAAa,GAAA,CAAAlB,IAAA,CAAA8B,aAAA,UACF;UAEOvB,EAAA,CAAAkB,SAAA,GACuD;UADvDlB,EAAA,CAAAwB,UAAA,cAAAxB,EAAA,CAAAoB,WAAA,uDAAApB,EAAA,CAAAqB,eAAA,KAAAC,GAAA,EAAAX,GAAA,CAAAb,cAAA,CAAAa,GAAA,CAAAlB,IAAA,CAAA8B,aAAA,KAAAvB,EAAA,CAAAyB,cAAA,CACuD;UAK1DzB,EAAA,CAAAkB,SAAA,GACF;UADElB,EAAA,CAAAmB,kBAAA,MAAAnB,EAAA,CAAA0B,WAAA,uDACF;UAEE1B,EAAA,CAAAkB,SAAA,GACF;UADElB,EAAA,CAAAmB,kBAAA,MAAAnB,EAAA,CAAA0B,WAAA,uDACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}