{"ast": null, "code": "module.exports = function atoa(a, n) {\n  return Array.prototype.slice.call(a, n);\n};", "map": {"version": 3, "names": ["module", "exports", "atoa", "a", "n", "Array", "prototype", "slice", "call"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/node_modules/atoa/atoa.js"], "sourcesContent": ["module.exports = function atoa (a, n) { return Array.prototype.slice.call(a, n); }\n"], "mappings": "AAAAA,MAAM,CAACC,OAAO,GAAG,SAASC,IAAIA,CAAEC,CAAC,EAAEC,CAAC,EAAE;EAAE,OAAOC,KAAK,CAACC,SAAS,CAACC,KAAK,CAACC,IAAI,CAACL,CAAC,EAAEC,CAAC,CAAC;AAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}