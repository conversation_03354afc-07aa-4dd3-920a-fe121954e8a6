{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiGridModule } from '@skywind-group/lib-swui';\nimport { ControlMessagesModule } from '../../../../../../common/components/control-messages/control-messages.module';\nimport { EntityIpWhitelistComponent } from './entity-ip-whitelist.component';\nimport { TrimInputValueModule } from '../../../../../../common/directives/trim-input-value/trim-input-value.module';\nimport { RemoveConfirmDialogComponent } from './dialogs/remove-confirm-dialog.component';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatListModule } from '@angular/material/list';\nimport { MatIconModule } from '@angular/material/icon';\nimport { RouterModule } from '@angular/router';\nimport { DownloadCsvModule } from '../../../../../../common/components/download-csv/download-csv.module';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nexport class EntityIpWhitelistModule {\n  static {\n    this.ɵfac = function EntityIpWhitelistModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || EntityIpWhitelistModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: EntityIpWhitelistModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, TranslateModule.forChild(), ControlMessagesModule, ReactiveFormsModule, SwuiGridModule, MatButtonModule, MatFormFieldModule, MatInputModule, TrimInputValueModule, MatDialogModule, MatListModule, MatIconModule, RouterModule, DownloadCsvModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(EntityIpWhitelistModule, {\n    declarations: [EntityIpWhitelistComponent, RemoveConfirmDialogComponent],\n    imports: [CommonModule, i1.TranslateModule, ControlMessagesModule, ReactiveFormsModule, SwuiGridModule, MatButtonModule, MatFormFieldModule, MatInputModule, TrimInputValueModule, MatDialogModule, MatListModule, MatIconModule, RouterModule, DownloadCsvModule],\n    exports: [EntityIpWhitelistComponent]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "ReactiveFormsModule", "MatButtonModule", "MatFormFieldModule", "MatInputModule", "TranslateModule", "SwuiGridModule", "ControlMessagesModule", "EntityIpWhitelistComponent", "TrimInputValueModule", "RemoveConfirmDialogComponent", "MatDialogModule", "MatListModule", "MatIconModule", "RouterModule", "DownloadCsvModule", "EntityIpWhitelistModule", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "imports", "i1", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-2fa/entity-ip-whitelist/entity-ip-whitelist.module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiGridModule } from '@skywind-group/lib-swui';\nimport { ControlMessagesModule } from '../../../../../../common/components/control-messages/control-messages.module';\n\nimport { EntityIpWhitelistComponent } from './entity-ip-whitelist.component';\nimport { TrimInputValueModule } from '../../../../../../common/directives/trim-input-value/trim-input-value.module';\nimport { RemoveConfirmDialogComponent } from './dialogs/remove-confirm-dialog.component';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatListModule } from '@angular/material/list';\nimport { MatIconModule } from '@angular/material/icon';\nimport { RouterModule } from '@angular/router';\nimport { DownloadCsvModule } from '../../../../../../common/components/download-csv/download-csv.module';\n\n@NgModule({\n    imports: [\n        CommonModule,\n        TranslateModule.forChild(),\n        ControlMessagesModule,\n        ReactiveFormsModule,\n        SwuiGridModule,\n        MatButtonModule,\n        MatFormFieldModule,\n        MatInputModule,\n        TrimInputValueModule,\n        MatDialogModule,\n        MatListModule,\n        MatIconModule,\n        RouterModule,\n        DownloadCsvModule,\n    ],\n  declarations: [\n    EntityIpWhitelistComponent,\n    RemoveConfirmDialogComponent\n  ],\n  exports: [\n    EntityIpWhitelistComponent\n  ],\n  providers: [],\n})\nexport class EntityIpWhitelistModule {\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,qBAAqB,QAAQ,8EAA8E;AAEpH,SAASC,0BAA0B,QAAQ,iCAAiC;AAC5E,SAASC,oBAAoB,QAAQ,8EAA8E;AACnH,SAASC,4BAA4B,QAAQ,2CAA2C;AACxF,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,iBAAiB,QAAQ,sEAAsE;;;AA4BxG,OAAM,MAAOC,uBAAuB;;;uCAAvBA,uBAAuB;IAAA;EAAA;;;YAAvBA;IAAuB;EAAA;;;gBAxB5BhB,YAAY,EACZK,eAAe,CAACY,QAAQ,EAAE,EAC1BV,qBAAqB,EACrBN,mBAAmB,EACnBK,cAAc,EACdJ,eAAe,EACfC,kBAAkB,EAClBC,cAAc,EACdK,oBAAoB,EACpBE,eAAe,EACfC,aAAa,EACbC,aAAa,EACbC,YAAY,EACZC,iBAAiB;IAAA;EAAA;;;2EAWZC,uBAAuB;IAAAE,YAAA,GARhCV,0BAA0B,EAC1BE,4BAA4B;IAAAS,OAAA,GAjBxBnB,YAAY,EAAAoB,EAAA,CAAAf,eAAA,EAEZE,qBAAqB,EACrBN,mBAAmB,EACnBK,cAAc,EACdJ,eAAe,EACfC,kBAAkB,EAClBC,cAAc,EACdK,oBAAoB,EACpBE,eAAe,EACfC,aAAa,EACbC,aAAa,EACbC,YAAY,EACZC,iBAAiB;IAAAM,OAAA,GAOrBb,0BAA0B;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}