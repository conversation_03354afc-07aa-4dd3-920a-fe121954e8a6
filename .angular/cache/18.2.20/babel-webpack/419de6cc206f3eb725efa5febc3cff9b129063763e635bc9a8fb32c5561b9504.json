{"ast": null, "code": "import { ScrollingModule } from '@angular/cdk/scrolling';\nimport { CommonModule } from '@angular/common';\nimport { FlexLayoutModule } from '@angular/flex-layout';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatTreeModule } from '@angular/material/tree';\nimport { RouterModule } from '@angular/router';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiGridRowActionsModule, SwuiPagePanelModule } from '@skywind-group/lib-swui';\nimport { TrimInputValueModule } from '../../../../common/directives/trim-input-value/trim-input-value.module';\nimport { PipesModule } from '../../../../common/pipes/pipes.module';\nimport { EntityLabelsService } from '../../../../common/services/entity-labels.service';\nimport { EntitySettingsService } from '../../../../common/services/entity-settings.service';\nimport { GameService } from '../../../../common/services/game.service';\nimport { MerchantTypesService } from '../../../../common/services/merchant-types.service';\nimport { BusinessStructureService } from './business-structure.service';\nimport { EntityLabelsDialogModule } from './dialogs/entity-labels-dialog/entity-labels-dialog.module';\nimport { ManageBalanceModule } from './dialogs/manage-balance/manage-balance.module';\nimport { MatCountryDialogModule } from './dialogs/mat-country-dialog/mat-country-dialog.module';\nimport { MatEntityEditDialogModule } from './dialogs/mat-entity-edit-dialog/mat-entity-edit-dialog.module';\nimport { MatLanguageDialogModule } from './dialogs/mat-language-dialog/mat-language-dialog.module';\nimport { MatRegionalDialogModule } from './dialogs/mat-regional-dialog/mat-regional-dialog.module';\nimport { SetupHintDialogModule } from './dialogs/setup-hint-dialog/setup-hint-dialog.module';\nimport { ShowLimitsModalModule } from './dialogs/show-limits-modal/show-limits-modal.module';\nimport { StatusConfirmModule } from './dialogs/status-confirm/status-confirm.module';\nimport { EntityItemComponent } from './entity-item/entity-item.component';\nimport { GlobalFinderComponent } from './global-finder/global-finder.component';\nimport { MatBusinessStructureComponent } from './mat-business-structure.component';\nimport { SearchByDomainModalModule } from './search-by-domain-modal/search-by-domain-modal.module';\nimport * as i0 from \"@angular/core\";\nexport const bsMatModules = [MatDividerModule, MatTreeModule, MatButtonModule, MatIconModule, MatProgressBarModule, MatProgressSpinnerModule, MatTableModule, MatMenuModule, MatChipsModule, MatDialogModule, SwuiPagePanelModule, MatTooltipModule, PipesModule];\nexport class MatBusinessStructureModule {\n  static {\n    this.ɵfac = function MatBusinessStructureModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatBusinessStructureModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: MatBusinessStructureModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [GameService, BusinessStructureService, EntitySettingsService, MerchantTypesService, EntityLabelsService],\n      imports: [CommonModule, FlexLayoutModule, TranslateModule, RouterModule, bsMatModules, ScrollingModule, ManageBalanceModule, MatCountryDialogModule, StatusConfirmModule, MatLanguageDialogModule, MatRegionalDialogModule, MatEntityEditDialogModule, SearchByDomainModalModule, ShowLimitsModalModule, ReactiveFormsModule, MatInputModule, MatCardModule, EntityLabelsDialogModule, SetupHintDialogModule, SwuiGridRowActionsModule, TrimInputValueModule, MatCheckboxModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(MatBusinessStructureModule, {\n    declarations: [MatBusinessStructureComponent, EntityItemComponent, GlobalFinderComponent],\n    imports: [CommonModule, FlexLayoutModule, TranslateModule, RouterModule, MatDividerModule, MatTreeModule, MatButtonModule, MatIconModule, MatProgressBarModule, MatProgressSpinnerModule, MatTableModule, MatMenuModule, MatChipsModule, MatDialogModule, SwuiPagePanelModule, MatTooltipModule, PipesModule, ScrollingModule, ManageBalanceModule, MatCountryDialogModule, StatusConfirmModule, MatLanguageDialogModule, MatRegionalDialogModule, MatEntityEditDialogModule, SearchByDomainModalModule, ShowLimitsModalModule, ReactiveFormsModule, MatInputModule, MatCardModule, EntityLabelsDialogModule, SetupHintDialogModule, SwuiGridRowActionsModule, TrimInputValueModule, MatCheckboxModule],\n    exports: [MatBusinessStructureComponent]\n  });\n})();", "map": {"version": 3, "names": ["ScrollingModule", "CommonModule", "FlexLayoutModule", "ReactiveFormsModule", "MatButtonModule", "MatCardModule", "MatCheckboxModule", "MatChipsModule", "MatDialogModule", "MatDividerModule", "MatIconModule", "MatInputModule", "MatMenuModule", "MatProgressBarModule", "MatProgressSpinnerModule", "MatTableModule", "MatTooltipModule", "MatTreeModule", "RouterModule", "TranslateModule", "SwuiGridRowActionsModule", "SwuiPagePanelModule", "TrimInputValueModule", "PipesModule", "EntityLabelsService", "EntitySettingsService", "GameService", "MerchantTypesService", "BusinessStructureService", "EntityLabelsDialogModule", "ManageBalanceModule", "MatCountryDialogModule", "MatEntityEditDialogModule", "MatLanguageDialogModule", "MatRegionalDialogModule", "SetupHintDialogModule", "ShowLimitsModalModule", "StatusConfirmModule", "EntityItemComponent", "GlobalFinderComponent", "MatBusinessStructureComponent", "SearchByDomainModalModule", "bsMatModules", "MatBusinessStructureModule", "imports", "declarations", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/mat-business-structure/mat-business-structure.module.ts"], "sourcesContent": ["import { ScrollingModule } from '@angular/cdk/scrolling';\nimport { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { FlexLayoutModule } from '@angular/flex-layout';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatTreeModule } from '@angular/material/tree';\nimport { RouterModule } from '@angular/router';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiGridRowActionsModule, SwuiPagePanelModule } from '@skywind-group/lib-swui';\nimport { TrimInputValueModule } from '../../../../common/directives/trim-input-value/trim-input-value.module';\nimport { PipesModule } from '../../../../common/pipes/pipes.module';\nimport { EntityLabelsService } from '../../../../common/services/entity-labels.service';\nimport { EntitySettingsService } from '../../../../common/services/entity-settings.service';\nimport { GameService } from '../../../../common/services/game.service';\nimport { MerchantTypesService } from '../../../../common/services/merchant-types.service';\nimport { BusinessStructureService } from './business-structure.service';\nimport { EntityLabelsDialogModule } from './dialogs/entity-labels-dialog/entity-labels-dialog.module';\nimport { ManageBalanceModule } from './dialogs/manage-balance/manage-balance.module';\nimport { MatCountryDialogModule } from './dialogs/mat-country-dialog/mat-country-dialog.module';\nimport { MatEntityEditDialogModule } from './dialogs/mat-entity-edit-dialog/mat-entity-edit-dialog.module';\nimport { MatLanguageDialogModule } from './dialogs/mat-language-dialog/mat-language-dialog.module';\nimport { MatRegionalDialogModule } from './dialogs/mat-regional-dialog/mat-regional-dialog.module';\nimport { SetupHintDialogModule } from './dialogs/setup-hint-dialog/setup-hint-dialog.module';\nimport { ShowLimitsModalComponent } from './dialogs/show-limits-modal/show-limits-modal.component';\nimport { ShowLimitsModalModule } from './dialogs/show-limits-modal/show-limits-modal.module';\nimport { StatusConfirmModule } from './dialogs/status-confirm/status-confirm.module';\nimport { EntityItemComponent } from './entity-item/entity-item.component';\nimport { GlobalFinderComponent } from './global-finder/global-finder.component';\nimport { MatBusinessStructureComponent } from './mat-business-structure.component';\nimport { SearchByDomainModalComponent } from './search-by-domain-modal/search-by-domain-modal.component';\nimport { SearchByDomainModalModule } from './search-by-domain-modal/search-by-domain-modal.module';\n\nexport const bsMatModules = [\n  MatDividerModule,\n  MatTreeModule,\n  MatButtonModule,\n  MatIconModule,\n  MatProgressBarModule,\n  MatProgressSpinnerModule,\n  MatTableModule,\n  MatMenuModule,\n  MatChipsModule,\n  MatDialogModule,\n  SwuiPagePanelModule,\n  MatTooltipModule,\n  PipesModule,\n];\n\n@NgModule({\n  imports: [\n    CommonModule,\n    FlexLayoutModule,\n    TranslateModule,\n    RouterModule,\n    ...bsMatModules,\n    ScrollingModule,\n    ManageBalanceModule,\n    MatCountryDialogModule,\n    StatusConfirmModule,\n    MatLanguageDialogModule,\n    MatRegionalDialogModule,\n    MatEntityEditDialogModule,\n    SearchByDomainModalModule,\n    ShowLimitsModalModule,\n    ReactiveFormsModule,\n    MatInputModule,\n    MatCardModule,\n    EntityLabelsDialogModule,\n    SetupHintDialogModule,\n    SwuiGridRowActionsModule,\n    TrimInputValueModule,\n    MatCheckboxModule,\n  ],\n  declarations: [\n    MatBusinessStructureComponent,\n    EntityItemComponent,\n    GlobalFinderComponent\n  ],\n  exports: [\n    MatBusinessStructureComponent,\n  ],\n  providers: [\n    GameService,\n    BusinessStructureService,\n    EntitySettingsService,\n    MerchantTypesService,\n    EntityLabelsService,\n  ],\n})\nexport class MatBusinessStructureModule {\n}\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,wBAAwB;AACxD,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,wBAAwB,EAAEC,mBAAmB,QAAQ,yBAAyB;AACvF,SAASC,oBAAoB,QAAQ,wEAAwE;AAC7G,SAASC,WAAW,QAAQ,uCAAuC;AACnE,SAASC,mBAAmB,QAAQ,mDAAmD;AACvF,SAASC,qBAAqB,QAAQ,qDAAqD;AAC3F,SAASC,WAAW,QAAQ,0CAA0C;AACtE,SAASC,oBAAoB,QAAQ,oDAAoD;AACzF,SAASC,wBAAwB,QAAQ,8BAA8B;AACvE,SAASC,wBAAwB,QAAQ,4DAA4D;AACrG,SAASC,mBAAmB,QAAQ,gDAAgD;AACpF,SAASC,sBAAsB,QAAQ,wDAAwD;AAC/F,SAASC,yBAAyB,QAAQ,gEAAgE;AAC1G,SAASC,uBAAuB,QAAQ,0DAA0D;AAClG,SAASC,uBAAuB,QAAQ,0DAA0D;AAClG,SAASC,qBAAqB,QAAQ,sDAAsD;AAE5F,SAASC,qBAAqB,QAAQ,sDAAsD;AAC5F,SAASC,mBAAmB,QAAQ,gDAAgD;AACpF,SAASC,mBAAmB,QAAQ,qCAAqC;AACzE,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,6BAA6B,QAAQ,oCAAoC;AAElF,SAASC,yBAAyB,QAAQ,wDAAwD;;AAElG,OAAO,MAAMC,YAAY,GAAG,CAC1BjC,gBAAgB,EAChBQ,aAAa,EACbb,eAAe,EACfM,aAAa,EACbG,oBAAoB,EACpBC,wBAAwB,EACxBC,cAAc,EACdH,aAAa,EACbL,cAAc,EACdC,eAAe,EACfa,mBAAmB,EACnBL,gBAAgB,EAChBO,WAAW,CACZ;AA2CD,OAAM,MAAOoB,0BAA0B;;;uCAA1BA,0BAA0B;IAAA;EAAA;;;YAA1BA;IAA0B;EAAA;;;iBAR1B,CACTjB,WAAW,EACXE,wBAAwB,EACxBH,qBAAqB,EACrBE,oBAAoB,EACpBH,mBAAmB,CACpB;MAAAoB,OAAA,GArCC3C,YAAY,EACZC,gBAAgB,EAChBiB,eAAe,EACfD,YAAY,EACTwB,YAAY,EACf1C,eAAe,EACf8B,mBAAmB,EACnBC,sBAAsB,EACtBM,mBAAmB,EACnBJ,uBAAuB,EACvBC,uBAAuB,EACvBF,yBAAyB,EACzBS,yBAAyB,EACzBL,qBAAqB,EACrBjC,mBAAmB,EACnBQ,cAAc,EACdN,aAAa,EACbwB,wBAAwB,EACxBM,qBAAqB,EACrBf,wBAAwB,EACxBE,oBAAoB,EACpBhB,iBAAiB;IAAA;EAAA;;;2EAkBRqC,0BAA0B;IAAAE,YAAA,GAfnCL,6BAA6B,EAC7BF,mBAAmB,EACnBC,qBAAqB;IAAAK,OAAA,GA1BrB3C,YAAY,EACZC,gBAAgB,EAChBiB,eAAe,EACfD,YAAY,EApBdT,gBAAgB,EAChBQ,aAAa,EACbb,eAAe,EACfM,aAAa,EACbG,oBAAoB,EACpBC,wBAAwB,EACxBC,cAAc,EACdH,aAAa,EACbL,cAAc,EACdC,eAAe,EACfa,mBAAmB,EACnBL,gBAAgB,EAChBO,WAAW,EAUTvB,eAAe,EACf8B,mBAAmB,EACnBC,sBAAsB,EACtBM,mBAAmB,EACnBJ,uBAAuB,EACvBC,uBAAuB,EACvBF,yBAAyB,EACzBS,yBAAyB,EACzBL,qBAAqB,EACrBjC,mBAAmB,EACnBQ,cAAc,EACdN,aAAa,EACbwB,wBAAwB,EACxBM,qBAAqB,EACrBf,wBAAwB,EACxBE,oBAAoB,EACpBhB,iBAAiB;IAAAwC,OAAA,GAQjBN,6BAA6B;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}