{"ast": null, "code": "import { MatJurisdictionsDialogComponent } from './mat-jurisdictions-dialog.component';\nimport { CommonModule } from '@angular/common';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { FormsModule } from '@angular/forms';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { TrimInputValueModule } from '../../../../../../common/directives/trim-input-value/trim-input-value.module';\nimport * as i0 from \"@angular/core\";\nexport class MatJurisdictionsDialogModule {\n  static {\n    this.ɵfac = function MatJurisdictionsDialogModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatJurisdictionsDialogModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: MatJurisdictionsDialogModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, FormsModule, TranslateModule, MatTableModule, MatDialogModule, MatButtonModule, MatCheckboxModule, MatFormFieldModule, MatInputModule, TrimInputValueModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(MatJurisdictionsDialogModule, {\n    declarations: [MatJurisdictionsDialogComponent],\n    imports: [CommonModule, FormsModule, TranslateModule, MatTableModule, MatDialogModule, MatButtonModule, MatCheckboxModule, MatFormFieldModule, MatInputModule, TrimInputValueModule]\n  });\n})();", "map": {"version": 3, "names": ["MatJurisdictionsDialogComponent", "CommonModule", "TranslateModule", "MatDialogModule", "MatButtonModule", "MatCheckboxModule", "FormsModule", "MatTableModule", "MatFormFieldModule", "MatInputModule", "TrimInputValueModule", "MatJurisdictionsDialogModule", "declarations", "imports"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/mat-business-structure/dialogs/mat-jurisdictions-dialog/mat-jurisdictions-dialog.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\n\nimport { MatJurisdictionsDialogComponent } from './mat-jurisdictions-dialog.component';\nimport { CommonModule } from '@angular/common';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { FormsModule } from '@angular/forms';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { TrimInputValueModule } from '../../../../../../common/directives/trim-input-value/trim-input-value.module';\n\n@NgModule({\n  imports: [\n    CommonModule,\n    FormsModule,\n    TranslateModule,\n\n    MatTableModule,\n    MatDialogModule,\n    MatButtonModule,\n    MatCheckboxModule,\n    MatFormFieldModule,\n    MatInputModule,\n    TrimInputValueModule,\n  ],\n  declarations: [\n    MatJurisdictionsDialogComponent\n  ],\n  exports: [],\n  providers: [],\n})\nexport class MatJurisdictionsDialogModule {\n}\n"], "mappings": "AAEA,SAASA,+BAA+B,QAAQ,sCAAsC;AACtF,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,oBAAoB,QAAQ,8EAA8E;;AAsBnH,OAAM,MAAOC,4BAA4B;;;uCAA5BA,4BAA4B;IAAA;EAAA;;;YAA5BA;IAA4B;EAAA;;;gBAlBrCV,YAAY,EACZK,WAAW,EACXJ,eAAe,EAEfK,cAAc,EACdJ,eAAe,EACfC,eAAe,EACfC,iBAAiB,EACjBG,kBAAkB,EAClBC,cAAc,EACdC,oBAAoB;IAAA;EAAA;;;2EAQXC,4BAA4B;IAAAC,YAAA,GALrCZ,+BAA+B;IAAAa,OAAA,GAb/BZ,YAAY,EACZK,WAAW,EACXJ,eAAe,EAEfK,cAAc,EACdJ,eAAe,EACfC,eAAe,EACfC,iBAAiB,EACjBG,kBAAkB,EAClBC,cAAc,EACdC,oBAAoB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}