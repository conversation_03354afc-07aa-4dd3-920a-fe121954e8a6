{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { EventEmitter, Directive, Input, Output, HostListener, Injectable, PLATFORM_ID, Component, ViewEncapsulation, Inject, ViewChild, Injector, NgModule } from '@angular/core';\nimport * as i2 from '@angular/common';\nimport { isPlatformBrowser, DOCUMENT, CommonModule } from '@angular/common';\nconst _c0 = [\"dialogPopup\"];\nconst _c1 = [\"hueSlider\"];\nconst _c2 = [\"alphaSlider\"];\nfunction ColorPickerComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\");\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMapInterpolate1(\"arrow arrow-\", ctx_r1.cpUsePosition, \"\");\n    i0.ɵɵstyleProp(\"left\", ctx_r1.cpArrowPosition)(\"top\", ctx_r1.arrowTop, \"px\");\n  }\n}\nfunction ColorPickerComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵlistener(\"newValue\", function ColorPickerComponent_div_3_Template_div_newValue_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onColorChange($event));\n    })(\"dragStart\", function ColorPickerComponent_div_3_Template_div_dragStart_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onDragStart(\"saturation-lightness\"));\n    })(\"dragEnd\", function ColorPickerComponent_div_3_Template_div_dragEnd_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onDragEnd(\"saturation-lightness\"));\n    });\n    i0.ɵɵelement(1, \"div\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"background-color\", ctx_r1.hueSliderColor);\n    i0.ɵɵproperty(\"rgX\", 1)(\"rgY\", 1);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"top\", ctx_r1.slider == null ? null : ctx_r1.slider.v, \"px\")(\"left\", ctx_r1.slider == null ? null : ctx_r1.slider.s, \"px\");\n  }\n}\nfunction ColorPickerComponent__svg_svg_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 29);\n    i0.ɵɵelement(1, \"path\", 30)(2, \"path\", 31);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ColorPickerComponent_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function ColorPickerComponent_button_9_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAddPresetColor($event, ctx_r1.selectedColor));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r1.cpAddColorButtonClass);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.cpPresetColors && ctx_r1.cpPresetColors.length >= ctx_r1.cpMaxPresetColorsLength);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.cpAddColorButtonText, \" \");\n  }\n}\nfunction ColorPickerComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 33);\n  }\n}\nfunction ColorPickerComponent_div_21_input_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 39);\n    i0.ɵɵlistener(\"keyup.enter\", function ColorPickerComponent_div_21_input_6_Template_input_keyup_enter_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAcceptColor($event));\n    })(\"newValue\", function ColorPickerComponent_div_21_input_6_Template_input_newValue_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAlphaInput($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"rg\", 1)(\"value\", ctx_r1.cmykText == null ? null : ctx_r1.cmykText.a);\n  }\n}\nfunction ColorPickerComponent_div_21_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"A\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ColorPickerComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35)(2, \"input\", 36);\n    i0.ɵɵlistener(\"keyup.enter\", function ColorPickerComponent_div_21_Template_input_keyup_enter_2_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAcceptColor($event));\n    })(\"newValue\", function ColorPickerComponent_div_21_Template_input_newValue_2_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onCyanInput($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"input\", 36);\n    i0.ɵɵlistener(\"keyup.enter\", function ColorPickerComponent_div_21_Template_input_keyup_enter_3_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAcceptColor($event));\n    })(\"newValue\", function ColorPickerComponent_div_21_Template_input_newValue_3_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onMagentaInput($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"input\", 36);\n    i0.ɵɵlistener(\"keyup.enter\", function ColorPickerComponent_div_21_Template_input_keyup_enter_4_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAcceptColor($event));\n    })(\"newValue\", function ColorPickerComponent_div_21_Template_input_newValue_4_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onYellowInput($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"input\", 36);\n    i0.ɵɵlistener(\"keyup.enter\", function ColorPickerComponent_div_21_Template_input_keyup_enter_5_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAcceptColor($event));\n    })(\"newValue\", function ColorPickerComponent_div_21_Template_input_newValue_5_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onBlackInput($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, ColorPickerComponent_div_21_input_6_Template, 1, 2, \"input\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 35)(8, \"div\");\n    i0.ɵɵtext(9, \"C\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\");\n    i0.ɵɵtext(11, \"M\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\");\n    i0.ɵɵtext(13, \"Y\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\");\n    i0.ɵɵtext(15, \"K\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(16, ColorPickerComponent_div_21_div_16_Template, 2, 0, \"div\", 38);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"display\", ctx_r1.format !== 3 ? \"none\" : \"block\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"rg\", 100)(\"value\", ctx_r1.cmykText == null ? null : ctx_r1.cmykText.c);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"rg\", 100)(\"value\", ctx_r1.cmykText == null ? null : ctx_r1.cmykText.m);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"rg\", 100)(\"value\", ctx_r1.cmykText == null ? null : ctx_r1.cmykText.y);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"rg\", 100)(\"value\", ctx_r1.cmykText == null ? null : ctx_r1.cmykText.k);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.cpAlphaChannel !== \"disabled\");\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.cpAlphaChannel !== \"disabled\");\n  }\n}\nfunction ColorPickerComponent_div_22_input_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 39);\n    i0.ɵɵlistener(\"keyup.enter\", function ColorPickerComponent_div_22_input_5_Template_input_keyup_enter_0_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAcceptColor($event));\n    })(\"newValue\", function ColorPickerComponent_div_22_input_5_Template_input_newValue_0_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAlphaInput($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"rg\", 1)(\"value\", ctx_r1.hslaText == null ? null : ctx_r1.hslaText.a);\n  }\n}\nfunction ColorPickerComponent_div_22_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"A\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ColorPickerComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"div\", 35)(2, \"input\", 41);\n    i0.ɵɵlistener(\"keyup.enter\", function ColorPickerComponent_div_22_Template_input_keyup_enter_2_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAcceptColor($event));\n    })(\"newValue\", function ColorPickerComponent_div_22_Template_input_newValue_2_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onHueInput($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"input\", 36);\n    i0.ɵɵlistener(\"keyup.enter\", function ColorPickerComponent_div_22_Template_input_keyup_enter_3_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAcceptColor($event));\n    })(\"newValue\", function ColorPickerComponent_div_22_Template_input_newValue_3_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSaturationInput($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"input\", 36);\n    i0.ɵɵlistener(\"keyup.enter\", function ColorPickerComponent_div_22_Template_input_keyup_enter_4_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAcceptColor($event));\n    })(\"newValue\", function ColorPickerComponent_div_22_Template_input_newValue_4_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onLightnessInput($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, ColorPickerComponent_div_22_input_5_Template, 1, 2, \"input\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 35)(7, \"div\");\n    i0.ɵɵtext(8, \"H\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\");\n    i0.ɵɵtext(10, \"S\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\");\n    i0.ɵɵtext(12, \"L\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, ColorPickerComponent_div_22_div_13_Template, 2, 0, \"div\", 38);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"display\", ctx_r1.format !== 2 ? \"none\" : \"block\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"rg\", 360)(\"value\", ctx_r1.hslaText == null ? null : ctx_r1.hslaText.h);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"rg\", 100)(\"value\", ctx_r1.hslaText == null ? null : ctx_r1.hslaText.s);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"rg\", 100)(\"value\", ctx_r1.hslaText == null ? null : ctx_r1.hslaText.l);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.cpAlphaChannel !== \"disabled\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.cpAlphaChannel !== \"disabled\");\n  }\n}\nfunction ColorPickerComponent_div_23_input_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 39);\n    i0.ɵɵlistener(\"keyup.enter\", function ColorPickerComponent_div_23_input_5_Template_input_keyup_enter_0_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAcceptColor($event));\n    })(\"newValue\", function ColorPickerComponent_div_23_input_5_Template_input_newValue_0_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAlphaInput($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"rg\", 1)(\"value\", ctx_r1.rgbaText == null ? null : ctx_r1.rgbaText.a);\n  }\n}\nfunction ColorPickerComponent_div_23_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"A\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ColorPickerComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"div\", 35)(2, \"input\", 43);\n    i0.ɵɵlistener(\"keyup.enter\", function ColorPickerComponent_div_23_Template_input_keyup_enter_2_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAcceptColor($event));\n    })(\"newValue\", function ColorPickerComponent_div_23_Template_input_newValue_2_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onRedInput($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"input\", 43);\n    i0.ɵɵlistener(\"keyup.enter\", function ColorPickerComponent_div_23_Template_input_keyup_enter_3_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAcceptColor($event));\n    })(\"newValue\", function ColorPickerComponent_div_23_Template_input_newValue_3_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onGreenInput($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"input\", 43);\n    i0.ɵɵlistener(\"keyup.enter\", function ColorPickerComponent_div_23_Template_input_keyup_enter_4_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAcceptColor($event));\n    })(\"newValue\", function ColorPickerComponent_div_23_Template_input_newValue_4_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onBlueInput($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, ColorPickerComponent_div_23_input_5_Template, 1, 2, \"input\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 35)(7, \"div\");\n    i0.ɵɵtext(8, \"R\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\");\n    i0.ɵɵtext(10, \"G\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\");\n    i0.ɵɵtext(12, \"B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, ColorPickerComponent_div_23_div_13_Template, 2, 0, \"div\", 38);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"display\", ctx_r1.format !== 1 ? \"none\" : \"block\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"rg\", 255)(\"value\", ctx_r1.rgbaText == null ? null : ctx_r1.rgbaText.r);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"rg\", 255)(\"value\", ctx_r1.rgbaText == null ? null : ctx_r1.rgbaText.g);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"rg\", 255)(\"value\", ctx_r1.rgbaText == null ? null : ctx_r1.rgbaText.b);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.cpAlphaChannel !== \"disabled\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.cpAlphaChannel !== \"disabled\");\n  }\n}\nfunction ColorPickerComponent_div_24_input_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 39);\n    i0.ɵɵlistener(\"keyup.enter\", function ColorPickerComponent_div_24_input_3_Template_input_keyup_enter_0_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAcceptColor($event));\n    })(\"newValue\", function ColorPickerComponent_div_24_input_3_Template_input_newValue_0_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAlphaInput($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"rg\", 1)(\"value\", ctx_r1.hexAlpha);\n  }\n}\nfunction ColorPickerComponent_div_24_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"A\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ColorPickerComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"div\", 35)(2, \"input\", 45);\n    i0.ɵɵlistener(\"blur\", function ColorPickerComponent_div_24_Template_input_blur_2_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onHexInput(null));\n    })(\"keyup.enter\", function ColorPickerComponent_div_24_Template_input_keyup_enter_2_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAcceptColor($event));\n    })(\"newValue\", function ColorPickerComponent_div_24_Template_input_newValue_2_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onHexInput($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, ColorPickerComponent_div_24_input_3_Template, 1, 2, \"input\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 35)(5, \"div\");\n    i0.ɵɵtext(6, \"Hex\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, ColorPickerComponent_div_24_div_7_Template, 2, 0, \"div\", 38);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"display\", ctx_r1.format !== 0 ? \"none\" : \"block\");\n    i0.ɵɵclassProp(\"hex-alpha\", ctx_r1.cpAlphaChannel === \"forced\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", ctx_r1.hexText);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.cpAlphaChannel === \"forced\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.cpAlphaChannel === \"forced\");\n  }\n}\nfunction ColorPickerComponent_div_25_input_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 39);\n    i0.ɵɵlistener(\"keyup.enter\", function ColorPickerComponent_div_25_input_3_Template_input_keyup_enter_0_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAcceptColor($event));\n    })(\"newValue\", function ColorPickerComponent_div_25_input_3_Template_input_newValue_0_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAlphaInput($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"rg\", 1)(\"value\", ctx_r1.hslaText == null ? null : ctx_r1.hslaText.a);\n  }\n}\nfunction ColorPickerComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"div\", 35)(2, \"input\", 36);\n    i0.ɵɵlistener(\"keyup.enter\", function ColorPickerComponent_div_25_Template_input_keyup_enter_2_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAcceptColor($event));\n    })(\"newValue\", function ColorPickerComponent_div_25_Template_input_newValue_2_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onValueInput($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, ColorPickerComponent_div_25_input_3_Template, 1, 2, \"input\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 35)(5, \"div\");\n    i0.ɵɵtext(6, \"V\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\");\n    i0.ɵɵtext(8, \"A\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"rg\", 100)(\"value\", ctx_r1.hslaText == null ? null : ctx_r1.hslaText.l);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.cpAlphaChannel !== \"disabled\");\n  }\n}\nfunction ColorPickerComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"span\", 48);\n    i0.ɵɵlistener(\"click\", function ColorPickerComponent_div_26_Template_span_click_1_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onFormatToggle(-1));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"span\", 48);\n    i0.ɵɵlistener(\"click\", function ColorPickerComponent_div_26_Template_span_click_2_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onFormatToggle(1));\n    });\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ColorPickerComponent_div_27_div_4_div_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 55);\n    i0.ɵɵlistener(\"click\", function ColorPickerComponent_div_27_div_4_div_1_span_1_Template_span_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const color_r17 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onRemovePresetColor($event, color_r17));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵclassMap(ctx_r1.cpRemoveColorButtonClass);\n  }\n}\nfunction ColorPickerComponent_div_27_div_4_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵlistener(\"click\", function ColorPickerComponent_div_27_div_4_div_1_Template_div_click_0_listener() {\n      const color_r17 = i0.ɵɵrestoreView(_r16).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.setColorFromString(color_r17));\n    });\n    i0.ɵɵtemplate(1, ColorPickerComponent_div_27_div_4_div_1_span_1_Template, 1, 3, \"span\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const color_r17 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵstyleProp(\"background-color\", color_r17);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.cpAddColorButton);\n  }\n}\nfunction ColorPickerComponent_div_27_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, ColorPickerComponent_div_27_div_4_div_1_Template, 2, 3, \"div\", 52);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(ctx_r1.cpPresetColorsClass);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.cpPresetColors);\n  }\n}\nfunction ColorPickerComponent_div_27_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(ctx_r1.cpPresetEmptyMessageClass);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.cpPresetEmptyMessage);\n  }\n}\nfunction ColorPickerComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49);\n    i0.ɵɵelement(1, \"hr\");\n    i0.ɵɵelementStart(2, \"div\", 50);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, ColorPickerComponent_div_27_div_4_Template, 2, 4, \"div\", 51)(5, ColorPickerComponent_div_27_div_5_Template, 2, 4, \"div\", 51);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.cpPresetLabel);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.cpPresetColors == null ? null : ctx_r1.cpPresetColors.length);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !(ctx_r1.cpPresetColors == null ? null : ctx_r1.cpPresetColors.length) && ctx_r1.cpAddColorButton);\n  }\n}\nfunction ColorPickerComponent_div_28_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 58);\n    i0.ɵɵlistener(\"click\", function ColorPickerComponent_div_28_button_1_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onCancelColor($event));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(ctx_r1.cpCancelButtonClass);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.cpCancelButtonText);\n  }\n}\nfunction ColorPickerComponent_div_28_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 58);\n    i0.ɵɵlistener(\"click\", function ColorPickerComponent_div_28_button_2_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAcceptColor($event));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(ctx_r1.cpOKButtonClass);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.cpOKButtonText);\n  }\n}\nfunction ColorPickerComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56);\n    i0.ɵɵtemplate(1, ColorPickerComponent_div_28_button_1_Template, 2, 4, \"button\", 57)(2, ColorPickerComponent_div_28_button_2_Template, 2, 4, \"button\", 57);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.cpCancelButton);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.cpOKButton);\n  }\n}\nfunction ColorPickerComponent_div_29_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction ColorPickerComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtemplate(1, ColorPickerComponent_div_29_ng_container_1_Template, 1, 0, \"ng-container\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.cpExtraTemplate);\n  }\n}\nvar ColorFormats;\n(function (ColorFormats) {\n  ColorFormats[ColorFormats[\"HEX\"] = 0] = \"HEX\";\n  ColorFormats[ColorFormats[\"RGBA\"] = 1] = \"RGBA\";\n  ColorFormats[ColorFormats[\"HSLA\"] = 2] = \"HSLA\";\n  ColorFormats[ColorFormats[\"CMYK\"] = 3] = \"CMYK\";\n})(ColorFormats || (ColorFormats = {}));\nclass Rgba {\n  r;\n  g;\n  b;\n  a;\n  constructor(r, g, b, a) {\n    this.r = r;\n    this.g = g;\n    this.b = b;\n    this.a = a;\n  }\n}\nclass Hsva {\n  h;\n  s;\n  v;\n  a;\n  constructor(h, s, v, a) {\n    this.h = h;\n    this.s = s;\n    this.v = v;\n    this.a = a;\n  }\n}\nclass Hsla {\n  h;\n  s;\n  l;\n  a;\n  constructor(h, s, l, a) {\n    this.h = h;\n    this.s = s;\n    this.l = l;\n    this.a = a;\n  }\n}\nclass Cmyk {\n  c;\n  m;\n  y;\n  k;\n  a;\n  constructor(c, m, y, k, a = 1) {\n    this.c = c;\n    this.m = m;\n    this.y = y;\n    this.k = k;\n    this.a = a;\n  }\n}\nfunction calculateAutoPositioning(elBounds, triggerElBounds) {\n  // Defaults\n  let usePositionX = 'right';\n  let usePositionY = 'bottom';\n  // Calculate collisions\n  const {\n    height,\n    width\n  } = elBounds;\n  const {\n    top,\n    left\n  } = triggerElBounds;\n  const bottom = top + triggerElBounds.height;\n  const right = left + triggerElBounds.width;\n  const collisionTop = top - height < 0;\n  const collisionBottom = bottom + height > (window.innerHeight || document.documentElement.clientHeight);\n  const collisionLeft = left - width < 0;\n  const collisionRight = right + width > (window.innerWidth || document.documentElement.clientWidth);\n  const collisionAll = collisionTop && collisionBottom && collisionLeft && collisionRight;\n  // Generate X & Y position values\n  if (collisionBottom) {\n    usePositionY = 'top';\n  }\n  if (collisionTop) {\n    usePositionY = 'bottom';\n  }\n  if (collisionLeft) {\n    usePositionX = 'right';\n  }\n  if (collisionRight) {\n    usePositionX = 'left';\n  }\n  // Choose the largest gap available\n  if (collisionAll) {\n    const postions = ['left', 'right', 'top', 'bottom'];\n    return postions.reduce((prev, next) => elBounds[prev] > elBounds[next] ? prev : next);\n  }\n  if (collisionLeft && collisionRight) {\n    if (collisionTop) {\n      return 'bottom';\n    }\n    if (collisionBottom) {\n      return 'top';\n    }\n    return top > bottom ? 'top' : 'bottom';\n  }\n  if (collisionTop && collisionBottom) {\n    if (collisionLeft) {\n      return 'right';\n    }\n    if (collisionRight) {\n      return 'left';\n    }\n    return left > right ? 'left' : 'right';\n  }\n  return `${usePositionY}-${usePositionX}`;\n}\nfunction detectIE() {\n  let ua = '';\n  if (typeof navigator !== 'undefined') {\n    ua = navigator.userAgent.toLowerCase();\n  }\n  const msie = ua.indexOf('msie ');\n  if (msie > 0) {\n    // IE 10 or older => return version number\n    return parseInt(ua.substring(msie + 5, ua.indexOf('.', msie)), 10);\n  }\n  // Other browser\n  return false;\n}\nclass TextDirective {\n  rg;\n  text;\n  newValue = new EventEmitter();\n  inputChange(event) {\n    const value = event.target.value;\n    if (this.rg === undefined) {\n      this.newValue.emit(value);\n    } else {\n      const numeric = parseFloat(value);\n      this.newValue.emit({\n        v: numeric,\n        rg: this.rg\n      });\n    }\n  }\n  static ɵfac = function TextDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || TextDirective)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: TextDirective,\n    selectors: [[\"\", \"text\", \"\"]],\n    hostBindings: function TextDirective_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"input\", function TextDirective_input_HostBindingHandler($event) {\n          return ctx.inputChange($event);\n        });\n      }\n    },\n    inputs: {\n      rg: \"rg\",\n      text: \"text\"\n    },\n    outputs: {\n      newValue: \"newValue\"\n    }\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TextDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[text]'\n    }]\n  }], null, {\n    rg: [{\n      type: Input\n    }],\n    text: [{\n      type: Input\n    }],\n    newValue: [{\n      type: Output\n    }],\n    inputChange: [{\n      type: HostListener,\n      args: ['input', ['$event']]\n    }]\n  });\n})();\nclass SliderDirective {\n  elRef;\n  listenerMove;\n  listenerStop;\n  rgX;\n  rgY;\n  slider;\n  dragEnd = new EventEmitter();\n  dragStart = new EventEmitter();\n  newValue = new EventEmitter();\n  mouseDown(event) {\n    this.start(event);\n  }\n  touchStart(event) {\n    this.start(event);\n  }\n  constructor(elRef) {\n    this.elRef = elRef;\n    this.listenerMove = event => this.move(event);\n    this.listenerStop = () => this.stop();\n  }\n  move(event) {\n    event.preventDefault();\n    this.setCursor(event);\n  }\n  start(event) {\n    this.setCursor(event);\n    event.stopPropagation();\n    document.addEventListener('mouseup', this.listenerStop);\n    document.addEventListener('touchend', this.listenerStop);\n    document.addEventListener('mousemove', this.listenerMove);\n    document.addEventListener('touchmove', this.listenerMove);\n    this.dragStart.emit();\n  }\n  stop() {\n    document.removeEventListener('mouseup', this.listenerStop);\n    document.removeEventListener('touchend', this.listenerStop);\n    document.removeEventListener('mousemove', this.listenerMove);\n    document.removeEventListener('touchmove', this.listenerMove);\n    this.dragEnd.emit();\n  }\n  getX(event) {\n    const position = this.elRef.nativeElement.getBoundingClientRect();\n    const pageX = event.pageX !== undefined ? event.pageX : event.touches[0].pageX;\n    return pageX - position.left - window.pageXOffset;\n  }\n  getY(event) {\n    const position = this.elRef.nativeElement.getBoundingClientRect();\n    const pageY = event.pageY !== undefined ? event.pageY : event.touches[0].pageY;\n    return pageY - position.top - window.pageYOffset;\n  }\n  setCursor(event) {\n    const width = this.elRef.nativeElement.offsetWidth;\n    const height = this.elRef.nativeElement.offsetHeight;\n    const x = Math.max(0, Math.min(this.getX(event), width));\n    const y = Math.max(0, Math.min(this.getY(event), height));\n    if (this.rgX !== undefined && this.rgY !== undefined) {\n      this.newValue.emit({\n        s: x / width,\n        v: 1 - y / height,\n        rgX: this.rgX,\n        rgY: this.rgY\n      });\n    } else if (this.rgX === undefined && this.rgY !== undefined) {\n      this.newValue.emit({\n        v: y / height,\n        rgY: this.rgY\n      });\n    } else if (this.rgX !== undefined && this.rgY === undefined) {\n      this.newValue.emit({\n        v: x / width,\n        rgX: this.rgX\n      });\n    }\n  }\n  static ɵfac = function SliderDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || SliderDirective)(i0.ɵɵdirectiveInject(i0.ElementRef));\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: SliderDirective,\n    selectors: [[\"\", \"slider\", \"\"]],\n    hostBindings: function SliderDirective_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"mousedown\", function SliderDirective_mousedown_HostBindingHandler($event) {\n          return ctx.mouseDown($event);\n        })(\"touchstart\", function SliderDirective_touchstart_HostBindingHandler($event) {\n          return ctx.touchStart($event);\n        });\n      }\n    },\n    inputs: {\n      rgX: \"rgX\",\n      rgY: \"rgY\",\n      slider: \"slider\"\n    },\n    outputs: {\n      dragEnd: \"dragEnd\",\n      dragStart: \"dragStart\",\n      newValue: \"newValue\"\n    }\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SliderDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[slider]'\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }], {\n    rgX: [{\n      type: Input\n    }],\n    rgY: [{\n      type: Input\n    }],\n    slider: [{\n      type: Input\n    }],\n    dragEnd: [{\n      type: Output\n    }],\n    dragStart: [{\n      type: Output\n    }],\n    newValue: [{\n      type: Output\n    }],\n    mouseDown: [{\n      type: HostListener,\n      args: ['mousedown', ['$event']]\n    }],\n    touchStart: [{\n      type: HostListener,\n      args: ['touchstart', ['$event']]\n    }]\n  });\n})();\nclass SliderPosition {\n  h;\n  s;\n  v;\n  a;\n  constructor(h, s, v, a) {\n    this.h = h;\n    this.s = s;\n    this.v = v;\n    this.a = a;\n  }\n}\nclass SliderDimension {\n  h;\n  s;\n  v;\n  a;\n  constructor(h, s, v, a) {\n    this.h = h;\n    this.s = s;\n    this.v = v;\n    this.a = a;\n  }\n}\nclass ColorPickerService {\n  active = null;\n  setActive(active) {\n    if (this.active && this.active !== active && this.active.cpDialogDisplay !== 'inline') {\n      this.active.closeDialog();\n    }\n    this.active = active;\n  }\n  hsva2hsla(hsva) {\n    const h = hsva.h,\n      s = hsva.s,\n      v = hsva.v,\n      a = hsva.a;\n    if (v === 0) {\n      return new Hsla(h, 0, 0, a);\n    } else if (s === 0 && v === 1) {\n      return new Hsla(h, 1, 1, a);\n    } else {\n      const l = v * (2 - s) / 2;\n      return new Hsla(h, v * s / (1 - Math.abs(2 * l - 1)), l, a);\n    }\n  }\n  hsla2hsva(hsla) {\n    const h = Math.min(hsla.h, 1),\n      s = Math.min(hsla.s, 1);\n    const l = Math.min(hsla.l, 1),\n      a = Math.min(hsla.a, 1);\n    if (l === 0) {\n      return new Hsva(h, 0, 0, a);\n    } else {\n      const v = l + s * (1 - Math.abs(2 * l - 1)) / 2;\n      return new Hsva(h, 2 * (v - l) / v, v, a);\n    }\n  }\n  hsvaToRgba(hsva) {\n    let r, g, b;\n    const h = hsva.h,\n      s = hsva.s,\n      v = hsva.v,\n      a = hsva.a;\n    const i = Math.floor(h * 6);\n    const f = h * 6 - i;\n    const p = v * (1 - s);\n    const q = v * (1 - f * s);\n    const t = v * (1 - (1 - f) * s);\n    switch (i % 6) {\n      case 0:\n        r = v, g = t, b = p;\n        break;\n      case 1:\n        r = q, g = v, b = p;\n        break;\n      case 2:\n        r = p, g = v, b = t;\n        break;\n      case 3:\n        r = p, g = q, b = v;\n        break;\n      case 4:\n        r = t, g = p, b = v;\n        break;\n      case 5:\n        r = v, g = p, b = q;\n        break;\n      default:\n        r = 0, g = 0, b = 0;\n    }\n    return new Rgba(r, g, b, a);\n  }\n  cmykToRgb(cmyk) {\n    const r = (1 - cmyk.c) * (1 - cmyk.k);\n    const g = (1 - cmyk.m) * (1 - cmyk.k);\n    const b = (1 - cmyk.y) * (1 - cmyk.k);\n    return new Rgba(r, g, b, cmyk.a);\n  }\n  rgbaToCmyk(rgba) {\n    const k = 1 - Math.max(rgba.r, rgba.g, rgba.b);\n    if (k === 1) {\n      return new Cmyk(0, 0, 0, 1, rgba.a);\n    } else {\n      const c = (1 - rgba.r - k) / (1 - k);\n      const m = (1 - rgba.g - k) / (1 - k);\n      const y = (1 - rgba.b - k) / (1 - k);\n      return new Cmyk(c, m, y, k, rgba.a);\n    }\n  }\n  rgbaToHsva(rgba) {\n    let h, s;\n    const r = Math.min(rgba.r, 1),\n      g = Math.min(rgba.g, 1);\n    const b = Math.min(rgba.b, 1),\n      a = Math.min(rgba.a, 1);\n    const max = Math.max(r, g, b),\n      min = Math.min(r, g, b);\n    const v = max,\n      d = max - min;\n    s = max === 0 ? 0 : d / max;\n    if (max === min) {\n      h = 0;\n    } else {\n      switch (max) {\n        case r:\n          h = (g - b) / d + (g < b ? 6 : 0);\n          break;\n        case g:\n          h = (b - r) / d + 2;\n          break;\n        case b:\n          h = (r - g) / d + 4;\n          break;\n        default:\n          h = 0;\n      }\n      h /= 6;\n    }\n    return new Hsva(h, s, v, a);\n  }\n  rgbaToHex(rgba, allowHex8) {\n    /* eslint-disable no-bitwise */\n    let hex = '#' + (1 << 24 | rgba.r << 16 | rgba.g << 8 | rgba.b).toString(16).substr(1);\n    if (allowHex8) {\n      hex += (1 << 8 | Math.round(rgba.a * 255)).toString(16).substr(1);\n    }\n    /* eslint-enable no-bitwise */\n    return hex;\n  }\n  normalizeCMYK(cmyk) {\n    return new Cmyk(cmyk.c / 100, cmyk.m / 100, cmyk.y / 100, cmyk.k / 100, cmyk.a);\n  }\n  denormalizeCMYK(cmyk) {\n    return new Cmyk(Math.floor(cmyk.c * 100), Math.floor(cmyk.m * 100), Math.floor(cmyk.y * 100), Math.floor(cmyk.k * 100), cmyk.a);\n  }\n  denormalizeRGBA(rgba) {\n    return new Rgba(Math.round(rgba.r * 255), Math.round(rgba.g * 255), Math.round(rgba.b * 255), rgba.a);\n  }\n  stringToHsva(colorString = '', allowHex8 = false) {\n    let hsva = null;\n    colorString = (colorString || '').toLowerCase();\n    const stringParsers = [{\n      re: /(rgb)a?\\(\\s*(\\d{1,3})\\s*,\\s*(\\d{1,3})\\s*%?,\\s*(\\d{1,3})\\s*%?(?:,\\s*(\\d+(?:\\.\\d+)?)\\s*)?\\)/,\n      parse: function (execResult) {\n        return new Rgba(parseInt(execResult[2], 10) / 255, parseInt(execResult[3], 10) / 255, parseInt(execResult[4], 10) / 255, isNaN(parseFloat(execResult[5])) ? 1 : parseFloat(execResult[5]));\n      }\n    }, {\n      re: /(hsl)a?\\(\\s*(\\d{1,3})\\s*,\\s*(\\d{1,3})%\\s*,\\s*(\\d{1,3})%\\s*(?:,\\s*(\\d+(?:\\.\\d+)?)\\s*)?\\)/,\n      parse: function (execResult) {\n        return new Hsla(parseInt(execResult[2], 10) / 360, parseInt(execResult[3], 10) / 100, parseInt(execResult[4], 10) / 100, isNaN(parseFloat(execResult[5])) ? 1 : parseFloat(execResult[5]));\n      }\n    }];\n    if (allowHex8) {\n      stringParsers.push({\n        re: /#([a-fA-F0-9]{2})([a-fA-F0-9]{2})([a-fA-F0-9]{2})([a-fA-F0-9]{2})?$/,\n        parse: function (execResult) {\n          return new Rgba(parseInt(execResult[1], 16) / 255, parseInt(execResult[2], 16) / 255, parseInt(execResult[3], 16) / 255, parseInt(execResult[4] || 'FF', 16) / 255);\n        }\n      });\n    } else {\n      stringParsers.push({\n        re: /#([a-fA-F0-9]{2})([a-fA-F0-9]{2})([a-fA-F0-9]{2})$/,\n        parse: function (execResult) {\n          return new Rgba(parseInt(execResult[1], 16) / 255, parseInt(execResult[2], 16) / 255, parseInt(execResult[3], 16) / 255, 1);\n        }\n      });\n    }\n    stringParsers.push({\n      re: /#([a-fA-F0-9])([a-fA-F0-9])([a-fA-F0-9])$/,\n      parse: function (execResult) {\n        return new Rgba(parseInt(execResult[1] + execResult[1], 16) / 255, parseInt(execResult[2] + execResult[2], 16) / 255, parseInt(execResult[3] + execResult[3], 16) / 255, 1);\n      }\n    });\n    for (const key in stringParsers) {\n      if (stringParsers.hasOwnProperty(key)) {\n        const parser = stringParsers[key];\n        const match = parser.re.exec(colorString),\n          color = match && parser.parse(match);\n        if (color) {\n          if (color instanceof Rgba) {\n            hsva = this.rgbaToHsva(color);\n          } else if (color instanceof Hsla) {\n            hsva = this.hsla2hsva(color);\n          }\n          return hsva;\n        }\n      }\n    }\n    return hsva;\n  }\n  outputFormat(hsva, outputFormat, alphaChannel) {\n    if (outputFormat === 'auto') {\n      outputFormat = hsva.a < 1 ? 'rgba' : 'hex';\n    }\n    switch (outputFormat) {\n      case 'hsla':\n        const hsla = this.hsva2hsla(hsva);\n        const hslaText = new Hsla(Math.round(hsla.h * 360), Math.round(hsla.s * 100), Math.round(hsla.l * 100), Math.round(hsla.a * 100) / 100);\n        if (hsva.a < 1 || alphaChannel === 'always') {\n          return 'hsla(' + hslaText.h + ',' + hslaText.s + '%,' + hslaText.l + '%,' + hslaText.a + ')';\n        } else {\n          return 'hsl(' + hslaText.h + ',' + hslaText.s + '%,' + hslaText.l + '%)';\n        }\n      case 'rgba':\n        const rgba = this.denormalizeRGBA(this.hsvaToRgba(hsva));\n        if (hsva.a < 1 || alphaChannel === 'always') {\n          return 'rgba(' + rgba.r + ',' + rgba.g + ',' + rgba.b + ',' + Math.round(rgba.a * 100) / 100 + ')';\n        } else {\n          return 'rgb(' + rgba.r + ',' + rgba.g + ',' + rgba.b + ')';\n        }\n      default:\n        const allowHex8 = alphaChannel === 'always' || alphaChannel === 'forced';\n        return this.rgbaToHex(this.denormalizeRGBA(this.hsvaToRgba(hsva)), allowHex8);\n    }\n  }\n  static ɵfac = function ColorPickerService_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ColorPickerService)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: ColorPickerService,\n    factory: ColorPickerService.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ColorPickerService, [{\n    type: Injectable\n  }], null, null);\n})();\n\n// Do not store that on the class instance since the condition will be run\n// every time the class is created.\nconst SUPPORTS_TOUCH = typeof window !== 'undefined' && 'ontouchstart' in window;\nclass ColorPickerComponent {\n  ngZone;\n  elRef;\n  cdRef;\n  document;\n  platformId;\n  service;\n  isIE10 = false;\n  cmyk;\n  hsva;\n  width;\n  height;\n  cmykColor;\n  outputColor;\n  initialColor;\n  fallbackColor;\n  listenerResize;\n  listenerMouseDown;\n  directiveInstance;\n  sliderH;\n  sliderDimMax;\n  directiveElementRef;\n  dialogArrowSize = 10;\n  dialogArrowOffset = 15;\n  dialogInputFields = [ColorFormats.HEX, ColorFormats.RGBA, ColorFormats.HSLA, ColorFormats.CMYK];\n  useRootViewContainer = false;\n  show;\n  hidden;\n  top;\n  left;\n  position;\n  format;\n  slider;\n  hexText;\n  hexAlpha;\n  cmykText;\n  hslaText;\n  rgbaText;\n  arrowTop;\n  selectedColor;\n  hueSliderColor;\n  alphaSliderColor;\n  cpWidth;\n  cpHeight;\n  cpColorMode;\n  cpCmykEnabled;\n  cpAlphaChannel;\n  cpOutputFormat;\n  cpDisableInput;\n  cpDialogDisplay;\n  cpIgnoredElements;\n  cpSaveClickOutside;\n  cpCloseClickOutside;\n  cpPosition;\n  cpUsePosition;\n  cpPositionOffset;\n  cpOKButton;\n  cpOKButtonText;\n  cpOKButtonClass;\n  cpCancelButton;\n  cpCancelButtonText;\n  cpCancelButtonClass;\n  cpEyeDropper;\n  eyeDropperSupported;\n  cpPresetLabel;\n  cpPresetColors;\n  cpPresetColorsClass;\n  cpMaxPresetColorsLength;\n  cpPresetEmptyMessage;\n  cpPresetEmptyMessageClass;\n  cpAddColorButton;\n  cpAddColorButtonText;\n  cpAddColorButtonClass;\n  cpRemoveColorButtonClass;\n  cpArrowPosition;\n  cpTriggerElement;\n  cpExtraTemplate;\n  dialogElement;\n  hueSlider;\n  alphaSlider;\n  handleEsc(event) {\n    if (this.show && this.cpDialogDisplay === 'popup') {\n      this.onCancelColor(event);\n    }\n  }\n  handleEnter(event) {\n    if (this.show && this.cpDialogDisplay === 'popup') {\n      this.onAcceptColor(event);\n    }\n  }\n  constructor(ngZone, elRef, cdRef, document, platformId, service) {\n    this.ngZone = ngZone;\n    this.elRef = elRef;\n    this.cdRef = cdRef;\n    this.document = document;\n    this.platformId = platformId;\n    this.service = service;\n    this.eyeDropperSupported = isPlatformBrowser(this.platformId) && 'EyeDropper' in this.document.defaultView;\n  }\n  ngOnInit() {\n    this.slider = new SliderPosition(0, 0, 0, 0);\n    const hueWidth = this.hueSlider.nativeElement.offsetWidth || 140;\n    const alphaWidth = this.alphaSlider.nativeElement.offsetWidth || 140;\n    this.sliderDimMax = new SliderDimension(hueWidth, this.cpWidth, 130, alphaWidth);\n    if (this.cpCmykEnabled) {\n      this.format = ColorFormats.CMYK;\n    } else if (this.cpOutputFormat === 'rgba') {\n      this.format = ColorFormats.RGBA;\n    } else if (this.cpOutputFormat === 'hsla') {\n      this.format = ColorFormats.HSLA;\n    } else {\n      this.format = ColorFormats.HEX;\n    }\n    this.listenerMouseDown = event => {\n      this.onMouseDown(event);\n    };\n    this.listenerResize = () => {\n      this.onResize();\n    };\n    this.openDialog(this.initialColor, false);\n  }\n  ngOnDestroy() {\n    this.closeDialog();\n  }\n  ngAfterViewInit() {\n    if (this.cpWidth !== 230 || this.cpDialogDisplay === 'inline') {\n      const hueWidth = this.hueSlider.nativeElement.offsetWidth || 140;\n      const alphaWidth = this.alphaSlider.nativeElement.offsetWidth || 140;\n      this.sliderDimMax = new SliderDimension(hueWidth, this.cpWidth, 130, alphaWidth);\n      this.updateColorPicker(false);\n      this.cdRef.detectChanges();\n    }\n  }\n  openDialog(color, emit = true) {\n    this.service.setActive(this);\n    if (!this.width) {\n      this.cpWidth = this.directiveElementRef.nativeElement.offsetWidth;\n    }\n    if (!this.height) {\n      this.height = 320;\n    }\n    this.setInitialColor(color);\n    this.setColorFromString(color, emit);\n    this.openColorPicker();\n  }\n  closeDialog() {\n    this.closeColorPicker();\n  }\n  setupDialog(instance, elementRef, color, cpWidth, cpHeight, cpDialogDisplay, cpFallbackColor, cpColorMode, cpCmykEnabled, cpAlphaChannel, cpOutputFormat, cpDisableInput, cpIgnoredElements, cpSaveClickOutside, cpCloseClickOutside, cpUseRootViewContainer, cpPosition, cpPositionOffset, cpPositionRelativeToArrow, cpPresetLabel, cpPresetColors, cpPresetColorsClass, cpMaxPresetColorsLength, cpPresetEmptyMessage, cpPresetEmptyMessageClass, cpOKButton, cpOKButtonClass, cpOKButtonText, cpCancelButton, cpCancelButtonClass, cpCancelButtonText, cpAddColorButton, cpAddColorButtonClass, cpAddColorButtonText, cpRemoveColorButtonClass, cpEyeDropper, cpTriggerElement, cpExtraTemplate) {\n    this.setInitialColor(color);\n    this.setColorMode(cpColorMode);\n    this.isIE10 = detectIE() === 10;\n    this.directiveInstance = instance;\n    this.directiveElementRef = elementRef;\n    this.cpDisableInput = cpDisableInput;\n    this.cpCmykEnabled = cpCmykEnabled;\n    this.cpAlphaChannel = cpAlphaChannel;\n    this.cpOutputFormat = cpOutputFormat;\n    this.cpDialogDisplay = cpDialogDisplay;\n    this.cpIgnoredElements = cpIgnoredElements;\n    this.cpSaveClickOutside = cpSaveClickOutside;\n    this.cpCloseClickOutside = cpCloseClickOutside;\n    this.useRootViewContainer = cpUseRootViewContainer;\n    this.width = this.cpWidth = parseInt(cpWidth, 10);\n    this.height = this.cpHeight = parseInt(cpHeight, 10);\n    this.cpPosition = cpPosition;\n    this.cpPositionOffset = parseInt(cpPositionOffset, 10);\n    this.cpOKButton = cpOKButton;\n    this.cpOKButtonText = cpOKButtonText;\n    this.cpOKButtonClass = cpOKButtonClass;\n    this.cpCancelButton = cpCancelButton;\n    this.cpCancelButtonText = cpCancelButtonText;\n    this.cpCancelButtonClass = cpCancelButtonClass;\n    this.cpEyeDropper = cpEyeDropper;\n    this.fallbackColor = cpFallbackColor || '#fff';\n    this.setPresetConfig(cpPresetLabel, cpPresetColors);\n    this.cpPresetColorsClass = cpPresetColorsClass;\n    this.cpMaxPresetColorsLength = cpMaxPresetColorsLength;\n    this.cpPresetEmptyMessage = cpPresetEmptyMessage;\n    this.cpPresetEmptyMessageClass = cpPresetEmptyMessageClass;\n    this.cpAddColorButton = cpAddColorButton;\n    this.cpAddColorButtonText = cpAddColorButtonText;\n    this.cpAddColorButtonClass = cpAddColorButtonClass;\n    this.cpRemoveColorButtonClass = cpRemoveColorButtonClass;\n    this.cpTriggerElement = cpTriggerElement;\n    this.cpExtraTemplate = cpExtraTemplate;\n    if (!cpPositionRelativeToArrow) {\n      this.dialogArrowOffset = 0;\n    }\n    if (cpDialogDisplay === 'inline') {\n      this.dialogArrowSize = 0;\n      this.dialogArrowOffset = 0;\n    }\n    if (cpOutputFormat === 'hex' && cpAlphaChannel !== 'always' && cpAlphaChannel !== 'forced') {\n      this.cpAlphaChannel = 'disabled';\n    }\n  }\n  setColorMode(mode) {\n    switch (mode.toString().toUpperCase()) {\n      case '1':\n      case 'C':\n      case 'COLOR':\n        this.cpColorMode = 1;\n        break;\n      case '2':\n      case 'G':\n      case 'GRAYSCALE':\n        this.cpColorMode = 2;\n        break;\n      case '3':\n      case 'P':\n      case 'PRESETS':\n        this.cpColorMode = 3;\n        break;\n      default:\n        this.cpColorMode = 1;\n    }\n  }\n  setInitialColor(color) {\n    this.initialColor = color;\n  }\n  setPresetConfig(cpPresetLabel, cpPresetColors) {\n    this.cpPresetLabel = cpPresetLabel;\n    this.cpPresetColors = cpPresetColors;\n  }\n  setColorFromString(value, emit = true, update = true) {\n    let hsva;\n    if (this.cpAlphaChannel === 'always' || this.cpAlphaChannel === 'forced') {\n      hsva = this.service.stringToHsva(value, true);\n      if (!hsva && !this.hsva) {\n        hsva = this.service.stringToHsva(value, false);\n      }\n    } else {\n      hsva = this.service.stringToHsva(value, false);\n    }\n    if (!hsva && !this.hsva) {\n      hsva = this.service.stringToHsva(this.fallbackColor, false);\n    }\n    if (hsva) {\n      this.hsva = hsva;\n      this.sliderH = this.hsva.h;\n      if (this.cpOutputFormat === 'hex' && this.cpAlphaChannel === 'disabled') {\n        this.hsva.a = 1;\n      }\n      this.updateColorPicker(emit, update);\n    }\n  }\n  onResize() {\n    if (this.position === 'fixed') {\n      this.setDialogPosition();\n    } else if (this.cpDialogDisplay !== 'inline') {\n      this.closeColorPicker();\n    }\n  }\n  onDragEnd(slider) {\n    this.directiveInstance.sliderDragEnd({\n      slider: slider,\n      color: this.outputColor\n    });\n  }\n  onDragStart(slider) {\n    this.directiveInstance.sliderDragStart({\n      slider: slider,\n      color: this.outputColor\n    });\n  }\n  onMouseDown(event) {\n    if (this.show && !this.isIE10 && this.cpDialogDisplay === 'popup' && event.target !== this.directiveElementRef.nativeElement && !this.isDescendant(this.elRef.nativeElement, event.target) && !this.isDescendant(this.directiveElementRef.nativeElement, event.target) && this.cpIgnoredElements.filter(item => item === event.target).length === 0) {\n      this.ngZone.run(() => {\n        if (this.cpSaveClickOutside) {\n          this.directiveInstance.colorSelected(this.outputColor);\n        } else {\n          this.hsva = null;\n          this.setColorFromString(this.initialColor, false);\n          if (this.cpCmykEnabled) {\n            this.directiveInstance.cmykChanged(this.cmykColor);\n          }\n          this.directiveInstance.colorChanged(this.initialColor);\n          this.directiveInstance.colorCanceled();\n        }\n        if (this.cpCloseClickOutside) {\n          this.closeColorPicker();\n        }\n      });\n    }\n  }\n  onAcceptColor(event) {\n    event.stopPropagation();\n    if (this.outputColor) {\n      this.directiveInstance.colorSelected(this.outputColor);\n    }\n    if (this.cpDialogDisplay === 'popup') {\n      this.closeColorPicker();\n    }\n  }\n  onCancelColor(event) {\n    this.hsva = null;\n    event.stopPropagation();\n    this.directiveInstance.colorCanceled();\n    this.setColorFromString(this.initialColor, true);\n    if (this.cpDialogDisplay === 'popup') {\n      if (this.cpCmykEnabled) {\n        this.directiveInstance.cmykChanged(this.cmykColor);\n      }\n      this.directiveInstance.colorChanged(this.initialColor, true);\n      this.closeColorPicker();\n    }\n  }\n  onEyeDropper() {\n    if (!this.eyeDropperSupported) return;\n    const eyeDropper = new window.EyeDropper();\n    eyeDropper.open().then(eyeDropperResult => {\n      this.setColorFromString(eyeDropperResult.sRGBHex, true);\n    });\n  }\n  onFormatToggle(change) {\n    const availableFormats = this.dialogInputFields.length - (this.cpCmykEnabled ? 0 : 1);\n    const nextFormat = ((this.dialogInputFields.indexOf(this.format) + change) % availableFormats + availableFormats) % availableFormats;\n    this.format = this.dialogInputFields[nextFormat];\n  }\n  onColorChange(value) {\n    this.hsva.s = value.s / value.rgX;\n    this.hsva.v = value.v / value.rgY;\n    this.updateColorPicker();\n    this.directiveInstance.sliderChanged({\n      slider: 'lightness',\n      value: this.hsva.v,\n      color: this.outputColor\n    });\n    this.directiveInstance.sliderChanged({\n      slider: 'saturation',\n      value: this.hsva.s,\n      color: this.outputColor\n    });\n  }\n  onHueChange(value) {\n    this.hsva.h = value.v / value.rgX;\n    this.sliderH = this.hsva.h;\n    this.updateColorPicker();\n    this.directiveInstance.sliderChanged({\n      slider: 'hue',\n      value: this.hsva.h,\n      color: this.outputColor\n    });\n  }\n  onValueChange(value) {\n    this.hsva.v = value.v / value.rgX;\n    this.updateColorPicker();\n    this.directiveInstance.sliderChanged({\n      slider: 'value',\n      value: this.hsva.v,\n      color: this.outputColor\n    });\n  }\n  onAlphaChange(value) {\n    this.hsva.a = value.v / value.rgX;\n    this.updateColorPicker();\n    this.directiveInstance.sliderChanged({\n      slider: 'alpha',\n      value: this.hsva.a,\n      color: this.outputColor\n    });\n  }\n  onHexInput(value) {\n    if (value === null) {\n      this.updateColorPicker();\n    } else {\n      if (value && value[0] !== '#') {\n        value = '#' + value;\n      }\n      let validHex = /^#([a-f0-9]{3}|[a-f0-9]{6})$/gi;\n      if (this.cpAlphaChannel === 'always') {\n        validHex = /^#([a-f0-9]{3}|[a-f0-9]{6}|[a-f0-9]{8})$/gi;\n      }\n      const valid = validHex.test(value);\n      if (valid) {\n        if (value.length < 5) {\n          value = '#' + value.substring(1).split('').map(c => c + c).join('');\n        }\n        if (this.cpAlphaChannel === 'forced') {\n          value += Math.round(this.hsva.a * 255).toString(16);\n        }\n        this.setColorFromString(value, true, false);\n      }\n      this.directiveInstance.inputChanged({\n        input: 'hex',\n        valid: valid,\n        value: value,\n        color: this.outputColor\n      });\n    }\n  }\n  onRedInput(value) {\n    const rgba = this.service.hsvaToRgba(this.hsva);\n    const valid = !isNaN(value.v) && value.v >= 0 && value.v <= value.rg;\n    if (valid) {\n      rgba.r = value.v / value.rg;\n      this.hsva = this.service.rgbaToHsva(rgba);\n      this.sliderH = this.hsva.h;\n      this.updateColorPicker();\n    }\n    this.directiveInstance.inputChanged({\n      input: 'red',\n      valid: valid,\n      value: rgba.r,\n      color: this.outputColor\n    });\n  }\n  onBlueInput(value) {\n    const rgba = this.service.hsvaToRgba(this.hsva);\n    const valid = !isNaN(value.v) && value.v >= 0 && value.v <= value.rg;\n    if (valid) {\n      rgba.b = value.v / value.rg;\n      this.hsva = this.service.rgbaToHsva(rgba);\n      this.sliderH = this.hsva.h;\n      this.updateColorPicker();\n    }\n    this.directiveInstance.inputChanged({\n      input: 'blue',\n      valid: valid,\n      value: rgba.b,\n      color: this.outputColor\n    });\n  }\n  onGreenInput(value) {\n    const rgba = this.service.hsvaToRgba(this.hsva);\n    const valid = !isNaN(value.v) && value.v >= 0 && value.v <= value.rg;\n    if (valid) {\n      rgba.g = value.v / value.rg;\n      this.hsva = this.service.rgbaToHsva(rgba);\n      this.sliderH = this.hsva.h;\n      this.updateColorPicker();\n    }\n    this.directiveInstance.inputChanged({\n      input: 'green',\n      valid: valid,\n      value: rgba.g,\n      color: this.outputColor\n    });\n  }\n  onHueInput(value) {\n    const valid = !isNaN(value.v) && value.v >= 0 && value.v <= value.rg;\n    if (valid) {\n      this.hsva.h = value.v / value.rg;\n      this.sliderH = this.hsva.h;\n      this.updateColorPicker();\n    }\n    this.directiveInstance.inputChanged({\n      input: 'hue',\n      valid: valid,\n      value: this.hsva.h,\n      color: this.outputColor\n    });\n  }\n  onValueInput(value) {\n    const valid = !isNaN(value.v) && value.v >= 0 && value.v <= value.rg;\n    if (valid) {\n      this.hsva.v = value.v / value.rg;\n      this.updateColorPicker();\n    }\n    this.directiveInstance.inputChanged({\n      input: 'value',\n      valid: valid,\n      value: this.hsva.v,\n      color: this.outputColor\n    });\n  }\n  onAlphaInput(value) {\n    const valid = !isNaN(value.v) && value.v >= 0 && value.v <= value.rg;\n    if (valid) {\n      this.hsva.a = value.v / value.rg;\n      this.updateColorPicker();\n    }\n    this.directiveInstance.inputChanged({\n      input: 'alpha',\n      valid: valid,\n      value: this.hsva.a,\n      color: this.outputColor\n    });\n  }\n  onLightnessInput(value) {\n    const hsla = this.service.hsva2hsla(this.hsva);\n    const valid = !isNaN(value.v) && value.v >= 0 && value.v <= value.rg;\n    if (valid) {\n      hsla.l = value.v / value.rg;\n      this.hsva = this.service.hsla2hsva(hsla);\n      this.sliderH = this.hsva.h;\n      this.updateColorPicker();\n    }\n    this.directiveInstance.inputChanged({\n      input: 'lightness',\n      valid: valid,\n      value: hsla.l,\n      color: this.outputColor\n    });\n  }\n  onSaturationInput(value) {\n    const hsla = this.service.hsva2hsla(this.hsva);\n    const valid = !isNaN(value.v) && value.v >= 0 && value.v <= value.rg;\n    if (valid) {\n      hsla.s = value.v / value.rg;\n      this.hsva = this.service.hsla2hsva(hsla);\n      this.sliderH = this.hsva.h;\n      this.updateColorPicker();\n    }\n    this.directiveInstance.inputChanged({\n      input: 'saturation',\n      valid: valid,\n      value: hsla.s,\n      color: this.outputColor\n    });\n  }\n  onCyanInput(value) {\n    const valid = !isNaN(value.v) && value.v >= 0 && value.v <= value.rg;\n    if (valid) {\n      this.cmyk.c = value.v;\n      this.updateColorPicker(false, true, true);\n    }\n    this.directiveInstance.inputChanged({\n      input: 'cyan',\n      valid: true,\n      value: this.cmyk.c,\n      color: this.outputColor\n    });\n  }\n  onMagentaInput(value) {\n    const valid = !isNaN(value.v) && value.v >= 0 && value.v <= value.rg;\n    if (valid) {\n      this.cmyk.m = value.v;\n      this.updateColorPicker(false, true, true);\n    }\n    this.directiveInstance.inputChanged({\n      input: 'magenta',\n      valid: true,\n      value: this.cmyk.m,\n      color: this.outputColor\n    });\n  }\n  onYellowInput(value) {\n    const valid = !isNaN(value.v) && value.v >= 0 && value.v <= value.rg;\n    if (valid) {\n      this.cmyk.y = value.v;\n      this.updateColorPicker(false, true, true);\n    }\n    this.directiveInstance.inputChanged({\n      input: 'yellow',\n      valid: true,\n      value: this.cmyk.y,\n      color: this.outputColor\n    });\n  }\n  onBlackInput(value) {\n    const valid = !isNaN(value.v) && value.v >= 0 && value.v <= value.rg;\n    if (valid) {\n      this.cmyk.k = value.v;\n      this.updateColorPicker(false, true, true);\n    }\n    this.directiveInstance.inputChanged({\n      input: 'black',\n      valid: true,\n      value: this.cmyk.k,\n      color: this.outputColor\n    });\n  }\n  onAddPresetColor(event, value) {\n    event.stopPropagation();\n    if (!this.cpPresetColors.filter(color => color === value).length) {\n      this.cpPresetColors = this.cpPresetColors.concat(value);\n      this.directiveInstance.presetColorsChanged(this.cpPresetColors);\n    }\n  }\n  onRemovePresetColor(event, value) {\n    event.stopPropagation();\n    this.cpPresetColors = this.cpPresetColors.filter(color => color !== value);\n    this.directiveInstance.presetColorsChanged(this.cpPresetColors);\n  }\n  // Private helper functions for the color picker dialog status\n  openColorPicker() {\n    if (!this.show) {\n      this.show = true;\n      this.hidden = true;\n      setTimeout(() => {\n        this.hidden = false;\n        this.setDialogPosition();\n        this.cdRef.detectChanges();\n      }, 0);\n      this.directiveInstance.stateChanged(true);\n      if (!this.isIE10) {\n        // The change detection should be run on `mousedown` event only when the condition\n        // is met within the `onMouseDown` method.\n        this.ngZone.runOutsideAngular(() => {\n          // There's no sense to add both event listeners on touch devices since the `touchstart`\n          // event is handled earlier than `mousedown`, so we'll get 2 change detections and the\n          // second one will be unnecessary.\n          if (SUPPORTS_TOUCH) {\n            document.addEventListener('touchstart', this.listenerMouseDown);\n          } else {\n            document.addEventListener('mousedown', this.listenerMouseDown);\n          }\n        });\n      }\n      window.addEventListener('resize', this.listenerResize);\n    }\n  }\n  closeColorPicker() {\n    if (this.show) {\n      this.show = false;\n      this.directiveInstance.stateChanged(false);\n      if (!this.isIE10) {\n        if (SUPPORTS_TOUCH) {\n          document.removeEventListener('touchstart', this.listenerMouseDown);\n        } else {\n          document.removeEventListener('mousedown', this.listenerMouseDown);\n        }\n      }\n      window.removeEventListener('resize', this.listenerResize);\n      if (!this.cdRef['destroyed']) {\n        this.cdRef.detectChanges();\n      }\n    }\n  }\n  updateColorPicker(emit = true, update = true, cmykInput = false) {\n    if (this.sliderDimMax) {\n      if (this.cpColorMode === 2) {\n        this.hsva.s = 0;\n      }\n      let hue, hsla, rgba;\n      const lastOutput = this.outputColor;\n      hsla = this.service.hsva2hsla(this.hsva);\n      if (!this.cpCmykEnabled) {\n        rgba = this.service.denormalizeRGBA(this.service.hsvaToRgba(this.hsva));\n      } else {\n        if (!cmykInput) {\n          rgba = this.service.hsvaToRgba(this.hsva);\n          this.cmyk = this.service.denormalizeCMYK(this.service.rgbaToCmyk(rgba));\n        } else {\n          rgba = this.service.cmykToRgb(this.service.normalizeCMYK(this.cmyk));\n          this.hsva = this.service.rgbaToHsva(rgba);\n        }\n        rgba = this.service.denormalizeRGBA(rgba);\n        this.sliderH = this.hsva.h;\n      }\n      hue = this.service.denormalizeRGBA(this.service.hsvaToRgba(new Hsva(this.sliderH || this.hsva.h, 1, 1, 1)));\n      if (update) {\n        this.hslaText = new Hsla(Math.round(hsla.h * 360), Math.round(hsla.s * 100), Math.round(hsla.l * 100), Math.round(hsla.a * 100) / 100);\n        this.rgbaText = new Rgba(rgba.r, rgba.g, rgba.b, Math.round(rgba.a * 100) / 100);\n        if (this.cpCmykEnabled) {\n          this.cmykText = new Cmyk(this.cmyk.c, this.cmyk.m, this.cmyk.y, this.cmyk.k, Math.round(this.cmyk.a * 100) / 100);\n        }\n        const allowHex8 = this.cpAlphaChannel === 'always';\n        this.hexText = this.service.rgbaToHex(rgba, allowHex8);\n        this.hexAlpha = this.rgbaText.a;\n      }\n      if (this.cpOutputFormat === 'auto') {\n        if (this.format !== ColorFormats.RGBA && this.format !== ColorFormats.CMYK && this.format !== ColorFormats.HSLA) {\n          if (this.hsva.a < 1) {\n            this.format = this.hsva.a < 1 ? ColorFormats.RGBA : ColorFormats.HEX;\n          }\n        }\n      }\n      this.hueSliderColor = 'rgb(' + hue.r + ',' + hue.g + ',' + hue.b + ')';\n      this.alphaSliderColor = 'rgb(' + rgba.r + ',' + rgba.g + ',' + rgba.b + ')';\n      this.outputColor = this.service.outputFormat(this.hsva, this.cpOutputFormat, this.cpAlphaChannel);\n      this.selectedColor = this.service.outputFormat(this.hsva, 'rgba', null);\n      if (this.format !== ColorFormats.CMYK) {\n        this.cmykColor = '';\n      } else {\n        if (this.cpAlphaChannel === 'always' || this.cpAlphaChannel === 'enabled' || this.cpAlphaChannel === 'forced') {\n          const alpha = Math.round(this.cmyk.a * 100) / 100;\n          this.cmykColor = `cmyka(${this.cmyk.c},${this.cmyk.m},${this.cmyk.y},${this.cmyk.k},${alpha})`;\n        } else {\n          this.cmykColor = `cmyk(${this.cmyk.c},${this.cmyk.m},${this.cmyk.y},${this.cmyk.k})`;\n        }\n      }\n      this.slider = new SliderPosition((this.sliderH || this.hsva.h) * this.sliderDimMax.h - 8, this.hsva.s * this.sliderDimMax.s - 8, (1 - this.hsva.v) * this.sliderDimMax.v - 8, this.hsva.a * this.sliderDimMax.a - 8);\n      if (emit && lastOutput !== this.outputColor) {\n        if (this.cpCmykEnabled) {\n          this.directiveInstance.cmykChanged(this.cmykColor);\n        }\n        this.directiveInstance.colorChanged(this.outputColor);\n      }\n    }\n  }\n  // Private helper functions for the color picker dialog positioning\n  setDialogPosition() {\n    if (this.cpDialogDisplay === 'inline') {\n      this.position = 'relative';\n    } else {\n      let position = 'static',\n        transform = '',\n        style;\n      let parentNode = null,\n        transformNode = null;\n      let node = this.directiveElementRef.nativeElement.parentNode;\n      const dialogHeight = this.dialogElement.nativeElement.offsetHeight;\n      while (node !== null && node.tagName !== 'HTML') {\n        style = window.getComputedStyle(node);\n        position = style.getPropertyValue('position');\n        transform = style.getPropertyValue('transform');\n        if (position !== 'static' && parentNode === null) {\n          parentNode = node;\n        }\n        if (transform && transform !== 'none' && transformNode === null) {\n          transformNode = node;\n        }\n        if (position === 'fixed') {\n          parentNode = transformNode;\n          break;\n        }\n        node = node.parentNode;\n      }\n      const boxDirective = this.createDialogBox(this.directiveElementRef.nativeElement, position !== 'fixed');\n      if (this.useRootViewContainer || position === 'fixed' && (!parentNode || parentNode instanceof HTMLUnknownElement)) {\n        this.top = boxDirective.top;\n        this.left = boxDirective.left;\n      } else {\n        if (parentNode === null) {\n          parentNode = node;\n        }\n        const boxParent = this.createDialogBox(parentNode, position !== 'fixed');\n        this.top = boxDirective.top - boxParent.top;\n        this.left = boxDirective.left - boxParent.left;\n      }\n      if (position === 'fixed') {\n        this.position = 'fixed';\n      }\n      let usePosition = this.cpPosition;\n      const dialogBounds = this.dialogElement.nativeElement.getBoundingClientRect();\n      if (this.cpPosition === 'auto') {\n        const triggerBounds = this.cpTriggerElement.nativeElement.getBoundingClientRect();\n        usePosition = calculateAutoPositioning(dialogBounds, triggerBounds);\n      }\n      this.arrowTop = usePosition === 'top' ? dialogHeight - 1 : undefined;\n      this.cpArrowPosition = undefined;\n      switch (usePosition) {\n        case 'top':\n          this.top -= dialogHeight + this.dialogArrowSize;\n          this.left += this.cpPositionOffset / 100 * boxDirective.width - this.dialogArrowOffset;\n          break;\n        case 'bottom':\n          this.top += boxDirective.height + this.dialogArrowSize;\n          this.left += this.cpPositionOffset / 100 * boxDirective.width - this.dialogArrowOffset;\n          break;\n        case 'top-left':\n        case 'left-top':\n          this.top -= dialogHeight - boxDirective.height + boxDirective.height * this.cpPositionOffset / 100;\n          this.left -= this.cpWidth + this.dialogArrowSize - 2 - this.dialogArrowOffset;\n          break;\n        case 'top-right':\n        case 'right-top':\n          this.top -= dialogHeight - boxDirective.height + boxDirective.height * this.cpPositionOffset / 100;\n          this.left += boxDirective.width + this.dialogArrowSize - 2 - this.dialogArrowOffset;\n          break;\n        case 'left':\n        case 'bottom-left':\n        case 'left-bottom':\n          this.top += boxDirective.height * this.cpPositionOffset / 100 - this.dialogArrowOffset;\n          this.left -= this.cpWidth + this.dialogArrowSize - 2;\n          break;\n        case 'right':\n        case 'bottom-right':\n        case 'right-bottom':\n        default:\n          this.top += boxDirective.height * this.cpPositionOffset / 100 - this.dialogArrowOffset;\n          this.left += boxDirective.width + this.dialogArrowSize - 2;\n          break;\n      }\n      const windowInnerHeight = window.innerHeight;\n      const windowInnerWidth = window.innerWidth;\n      const elRefClientRect = this.elRef.nativeElement.getBoundingClientRect();\n      const bottom = this.top + dialogBounds.height;\n      if (bottom > windowInnerHeight) {\n        this.top = windowInnerHeight - dialogBounds.height;\n        this.cpArrowPosition = elRefClientRect.x / 2 - 20;\n      }\n      const right = this.left + dialogBounds.width;\n      if (right > windowInnerWidth) {\n        this.left = windowInnerWidth - dialogBounds.width;\n        this.cpArrowPosition = elRefClientRect.x / 2 - 20;\n      }\n      this.cpUsePosition = usePosition;\n    }\n  }\n  // Private helper functions for the color picker dialog positioning and opening\n  isDescendant(parent, child) {\n    let node = child.parentNode;\n    while (node !== null) {\n      if (node === parent) {\n        return true;\n      }\n      node = node.parentNode;\n    }\n    return false;\n  }\n  createDialogBox(element, offset) {\n    const {\n      top,\n      left\n    } = element.getBoundingClientRect();\n    return {\n      top: top + (offset ? window.pageYOffset : 0),\n      left: left + (offset ? window.pageXOffset : 0),\n      width: element.offsetWidth,\n      height: element.offsetHeight\n    };\n  }\n  static ɵfac = function ColorPickerComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ColorPickerComponent)(i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(ColorPickerService));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: ColorPickerComponent,\n    selectors: [[\"color-picker\"]],\n    viewQuery: function ColorPickerComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 7);\n        i0.ɵɵviewQuery(_c1, 7);\n        i0.ɵɵviewQuery(_c2, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dialogElement = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.hueSlider = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.alphaSlider = _t.first);\n      }\n    },\n    hostBindings: function ColorPickerComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"keyup.esc\", function ColorPickerComponent_keyup_esc_HostBindingHandler($event) {\n          return ctx.handleEsc($event);\n        }, false, i0.ɵɵresolveDocument)(\"keyup.enter\", function ColorPickerComponent_keyup_enter_HostBindingHandler($event) {\n          return ctx.handleEnter($event);\n        }, false, i0.ɵɵresolveDocument);\n      }\n    },\n    decls: 30,\n    vars: 51,\n    consts: [[\"dialogPopup\", \"\"], [\"hueSlider\", \"\"], [\"valueSlider\", \"\"], [\"alphaSlider\", \"\"], [1, \"color-picker\", 3, \"click\"], [3, \"left\", \"class\", \"top\", 4, \"ngIf\"], [\"class\", \"saturation-lightness\", 3, \"slider\", \"rgX\", \"rgY\", \"background-color\", \"newValue\", \"dragStart\", \"dragEnd\", 4, \"ngIf\"], [1, \"hue-alpha\", \"box\"], [1, \"left\"], [1, \"selected-color-background\"], [1, \"selected-color\", 3, \"click\"], [\"class\", \"eyedropper-icon\", \"xmlns\", \"http://www.w3.org/2000/svg\", \"height\", \"24px\", \"viewBox\", \"0 0 24 24\", \"width\", \"24px\", \"fill\", \"#000000\", 4, \"ngIf\"], [\"type\", \"button\", 3, \"class\", \"disabled\", \"click\", 4, \"ngIf\"], [1, \"right\"], [\"style\", \"height: 16px;\", 4, \"ngIf\"], [1, \"hue\", 3, \"newValue\", \"dragStart\", \"dragEnd\", \"slider\", \"rgX\"], [1, \"cursor\"], [1, \"value\", 3, \"newValue\", \"dragStart\", \"dragEnd\", \"slider\", \"rgX\"], [1, \"alpha\", 3, \"newValue\", \"dragStart\", \"dragEnd\", \"slider\", \"rgX\"], [\"class\", \"cmyk-text\", 3, \"display\", 4, \"ngIf\"], [\"class\", \"hsla-text\", 3, \"display\", 4, \"ngIf\"], [\"class\", \"rgba-text\", 3, \"display\", 4, \"ngIf\"], [\"class\", \"hex-text\", 3, \"hex-alpha\", \"display\", 4, \"ngIf\"], [\"class\", \"value-text\", 4, \"ngIf\"], [\"class\", \"type-policy\", 4, \"ngIf\"], [\"class\", \"preset-area\", 4, \"ngIf\"], [\"class\", \"button-area\", 4, \"ngIf\"], [\"class\", \"extra-template\", 4, \"ngIf\"], [1, \"saturation-lightness\", 3, \"newValue\", \"dragStart\", \"dragEnd\", \"slider\", \"rgX\", \"rgY\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"height\", \"24px\", \"viewBox\", \"0 0 24 24\", \"width\", \"24px\", \"fill\", \"#000000\", 1, \"eyedropper-icon\"], [\"d\", \"M0 0h24v24H0V0z\", \"fill\", \"none\"], [\"d\", \"M17.66 5.41l.92.92-2.69 2.69-.92-.92 2.69-2.69M17.67 3c-.26 0-.51.1-.71.29l-3.12 3.12-1.93-1.91-1.41 1.41 1.42 1.42L3 16.25V21h4.75l8.92-8.92 1.42 1.42 1.41-1.41-1.92-1.92 3.12-3.12c.4-.4.4-1.03.01-1.42l-2.34-2.34c-.2-.19-.45-.29-.7-.29zM6.92 19L5 17.08l8.06-8.06 1.92 1.92L6.92 19z\"], [\"type\", \"button\", 3, \"click\", \"disabled\"], [2, \"height\", \"16px\"], [1, \"cmyk-text\"], [1, \"box\"], [\"type\", \"number\", \"pattern\", \"[0-9]*\", \"min\", \"0\", \"max\", \"100\", 3, \"keyup.enter\", \"newValue\", \"text\", \"rg\", \"value\"], [\"type\", \"number\", \"pattern\", \"[0-9]+([\\\\.,][0-9]{1,2})?\", \"min\", \"0\", \"max\", \"1\", \"step\", \"0.1\", 3, \"text\", \"rg\", \"value\", \"keyup.enter\", \"newValue\", 4, \"ngIf\"], [4, \"ngIf\"], [\"type\", \"number\", \"pattern\", \"[0-9]+([\\\\.,][0-9]{1,2})?\", \"min\", \"0\", \"max\", \"1\", \"step\", \"0.1\", 3, \"keyup.enter\", \"newValue\", \"text\", \"rg\", \"value\"], [1, \"hsla-text\"], [\"type\", \"number\", \"pattern\", \"[0-9]*\", \"min\", \"0\", \"max\", \"360\", 3, \"keyup.enter\", \"newValue\", \"text\", \"rg\", \"value\"], [1, \"rgba-text\"], [\"type\", \"number\", \"pattern\", \"[0-9]*\", \"min\", \"0\", \"max\", \"255\", 3, \"keyup.enter\", \"newValue\", \"text\", \"rg\", \"value\"], [1, \"hex-text\"], [3, \"blur\", \"keyup.enter\", \"newValue\", \"text\", \"value\"], [1, \"value-text\"], [1, \"type-policy\"], [1, \"type-policy-arrow\", 3, \"click\"], [1, \"preset-area\"], [1, \"preset-label\"], [3, \"class\", 4, \"ngIf\"], [\"class\", \"preset-color\", 3, \"backgroundColor\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"preset-color\", 3, \"click\"], [3, \"class\", \"click\", 4, \"ngIf\"], [3, \"click\"], [1, \"button-area\"], [\"type\", \"button\", 3, \"class\", \"click\", 4, \"ngIf\"], [\"type\", \"button\", 3, \"click\"], [1, \"extra-template\"], [4, \"ngTemplateOutlet\"]],\n    template: function ColorPickerComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelementStart(0, \"div\", 4, 0);\n        i0.ɵɵlistener(\"click\", function ColorPickerComponent_Template_div_click_0_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView($event.stopPropagation());\n        });\n        i0.ɵɵtemplate(2, ColorPickerComponent_div_2_Template, 1, 7, \"div\", 5)(3, ColorPickerComponent_div_3_Template, 2, 8, \"div\", 6);\n        i0.ɵɵelementStart(4, \"div\", 7)(5, \"div\", 8);\n        i0.ɵɵelement(6, \"div\", 9);\n        i0.ɵɵelementStart(7, \"div\", 10);\n        i0.ɵɵlistener(\"click\", function ColorPickerComponent_Template_div_click_7_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.eyeDropperSupported && ctx.cpEyeDropper && ctx.onEyeDropper());\n        });\n        i0.ɵɵtemplate(8, ColorPickerComponent__svg_svg_8_Template, 3, 0, \"svg\", 11);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(9, ColorPickerComponent_button_9_Template, 2, 5, \"button\", 12);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(10, \"div\", 13);\n        i0.ɵɵtemplate(11, ColorPickerComponent_div_11_Template, 1, 0, \"div\", 14);\n        i0.ɵɵelementStart(12, \"div\", 15, 1);\n        i0.ɵɵlistener(\"newValue\", function ColorPickerComponent_Template_div_newValue_12_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onHueChange($event));\n        })(\"dragStart\", function ColorPickerComponent_Template_div_dragStart_12_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onDragStart(\"hue\"));\n        })(\"dragEnd\", function ColorPickerComponent_Template_div_dragEnd_12_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onDragEnd(\"hue\"));\n        });\n        i0.ɵɵelement(14, \"div\", 16);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(15, \"div\", 17, 2);\n        i0.ɵɵlistener(\"newValue\", function ColorPickerComponent_Template_div_newValue_15_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onValueChange($event));\n        })(\"dragStart\", function ColorPickerComponent_Template_div_dragStart_15_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onDragStart(\"value\"));\n        })(\"dragEnd\", function ColorPickerComponent_Template_div_dragEnd_15_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onDragEnd(\"value\"));\n        });\n        i0.ɵɵelement(17, \"div\", 16);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(18, \"div\", 18, 3);\n        i0.ɵɵlistener(\"newValue\", function ColorPickerComponent_Template_div_newValue_18_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onAlphaChange($event));\n        })(\"dragStart\", function ColorPickerComponent_Template_div_dragStart_18_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onDragStart(\"alpha\"));\n        })(\"dragEnd\", function ColorPickerComponent_Template_div_dragEnd_18_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onDragEnd(\"alpha\"));\n        });\n        i0.ɵɵelement(20, \"div\", 16);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵtemplate(21, ColorPickerComponent_div_21_Template, 17, 12, \"div\", 19)(22, ColorPickerComponent_div_22_Template, 14, 10, \"div\", 20)(23, ColorPickerComponent_div_23_Template, 14, 10, \"div\", 21)(24, ColorPickerComponent_div_24_Template, 8, 7, \"div\", 22)(25, ColorPickerComponent_div_25_Template, 9, 3, \"div\", 23)(26, ColorPickerComponent_div_26_Template, 3, 0, \"div\", 24)(27, ColorPickerComponent_div_27_Template, 6, 3, \"div\", 25)(28, ColorPickerComponent_div_28_Template, 3, 2, \"div\", 26)(29, ColorPickerComponent_div_29_Template, 2, 1, \"div\", 27);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵstyleProp(\"display\", !ctx.show ? \"none\" : \"block\")(\"visibility\", ctx.hidden ? \"hidden\" : \"visible\")(\"top\", ctx.top, \"px\")(\"left\", ctx.left, \"px\")(\"position\", ctx.position)(\"height\", ctx.cpHeight, \"px\")(\"width\", ctx.cpWidth, \"px\");\n        i0.ɵɵclassProp(\"open\", ctx.show);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.cpDialogDisplay === \"popup\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", (ctx.cpColorMode || 1) === 1);\n        i0.ɵɵadvance(4);\n        i0.ɵɵstyleProp(\"background-color\", ctx.selectedColor)(\"cursor\", ctx.eyeDropperSupported && ctx.cpEyeDropper ? \"pointer\" : null);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.eyeDropperSupported && ctx.cpEyeDropper);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.cpAddColorButton);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.cpAlphaChannel === \"disabled\");\n        i0.ɵɵadvance();\n        i0.ɵɵstyleProp(\"display\", (ctx.cpColorMode || 1) === 1 ? \"block\" : \"none\");\n        i0.ɵɵproperty(\"rgX\", 1);\n        i0.ɵɵadvance(2);\n        i0.ɵɵstyleProp(\"left\", ctx.slider == null ? null : ctx.slider.h, \"px\");\n        i0.ɵɵadvance();\n        i0.ɵɵstyleProp(\"display\", (ctx.cpColorMode || 1) === 2 ? \"block\" : \"none\");\n        i0.ɵɵproperty(\"rgX\", 1);\n        i0.ɵɵadvance(2);\n        i0.ɵɵstyleProp(\"right\", ctx.slider == null ? null : ctx.slider.v, \"px\");\n        i0.ɵɵadvance();\n        i0.ɵɵstyleProp(\"display\", ctx.cpAlphaChannel === \"disabled\" ? \"none\" : \"block\")(\"background-color\", ctx.alphaSliderColor);\n        i0.ɵɵproperty(\"rgX\", 1);\n        i0.ɵɵadvance(2);\n        i0.ɵɵstyleProp(\"left\", ctx.slider == null ? null : ctx.slider.a, \"px\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.cpDisableInput && (ctx.cpColorMode || 1) === 1);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.cpDisableInput && (ctx.cpColorMode || 1) === 1);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.cpDisableInput && (ctx.cpColorMode || 1) === 1);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.cpDisableInput && (ctx.cpColorMode || 1) === 1);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.cpDisableInput && (ctx.cpColorMode || 1) === 2);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.cpDisableInput && (ctx.cpColorMode || 1) === 1);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", (ctx.cpPresetColors == null ? null : ctx.cpPresetColors.length) || ctx.cpAddColorButton);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.cpOKButton || ctx.cpCancelButton);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.cpExtraTemplate);\n      }\n    },\n    dependencies: [i2.NgForOf, i2.NgIf, i2.NgTemplateOutlet, TextDirective, SliderDirective],\n    styles: [\".color-picker{position:absolute;z-index:1000;width:230px;height:auto;border:#777 solid 1px;cursor:default;-webkit-user-select:none;user-select:none;background-color:#fff}.color-picker *{box-sizing:border-box;margin:0;font-size:11px}.color-picker input{width:0;height:26px;min-width:0;font-size:13px;text-align:center;color:#000}.color-picker input:invalid,.color-picker input:-moz-ui-invalid,.color-picker input:-moz-submit-invalid{box-shadow:none}.color-picker input::-webkit-inner-spin-button,.color-picker input::-webkit-outer-spin-button{margin:0;-webkit-appearance:none}.color-picker .arrow{position:absolute;z-index:999999;width:0;height:0;border-style:solid}.color-picker .arrow.arrow-top{left:8px;border-width:10px 5px;border-color:#777 rgba(0,0,0,0) rgba(0,0,0,0) rgba(0,0,0,0)}.color-picker .arrow.arrow-bottom{top:-20px;left:8px;border-width:10px 5px;border-color:rgba(0,0,0,0) rgba(0,0,0,0) #777 rgba(0,0,0,0)}.color-picker .arrow.arrow-top-left,.color-picker .arrow.arrow-left-top{right:-21px;bottom:8px;border-width:5px 10px;border-color:rgba(0,0,0,0) rgba(0,0,0,0) rgba(0,0,0,0) #777}.color-picker .arrow.arrow-top-right,.color-picker .arrow.arrow-right-top{bottom:8px;left:-20px;border-width:5px 10px;border-color:rgba(0,0,0,0) #777 rgba(0,0,0,0) rgba(0,0,0,0)}.color-picker .arrow.arrow-left,.color-picker .arrow.arrow-left-bottom,.color-picker .arrow.arrow-bottom-left{top:8px;right:-21px;border-width:5px 10px;border-color:rgba(0,0,0,0) rgba(0,0,0,0) rgba(0,0,0,0) #777}.color-picker .arrow.arrow-right,.color-picker .arrow.arrow-right-bottom,.color-picker .arrow.arrow-bottom-right{top:8px;left:-20px;border-width:5px 10px;border-color:rgba(0,0,0,0) #777 rgba(0,0,0,0) rgba(0,0,0,0)}.color-picker .cursor{position:relative;width:16px;height:16px;border:#222 solid 2px;border-radius:50%;cursor:default}.color-picker .box{display:flex;padding:4px 8px}.color-picker .left{position:relative;padding:16px 8px}.color-picker .right{flex:1 1 auto;padding:12px 8px}.color-picker .button-area{padding:0 16px 16px;text-align:right}.color-picker .button-area button{margin-left:8px}.color-picker .preset-area{padding:4px 15px}.color-picker .preset-area .preset-label{overflow:hidden;width:100%;padding:4px;font-size:11px;white-space:nowrap;text-align:left;text-overflow:ellipsis;color:#555}.color-picker .preset-area .preset-color{position:relative;display:inline-block;width:18px;height:18px;margin:4px 6px 8px;border:#a9a9a9 solid 1px;border-radius:25%;cursor:pointer}.color-picker .preset-area .preset-empty-message{min-height:18px;margin-top:4px;margin-bottom:8px;font-style:italic;text-align:center}.color-picker .hex-text{width:100%;padding:4px 8px;font-size:11px}.color-picker .hex-text .box{padding:0 24px 8px 8px}.color-picker .hex-text .box div{float:left;flex:1 1 auto;text-align:center;color:#555;clear:left}.color-picker .hex-text .box input{flex:1 1 auto;padding:1px;border:#a9a9a9 solid 1px}.color-picker .hex-alpha .box div:first-child,.color-picker .hex-alpha .box input:first-child{flex-grow:3;margin-right:8px}.color-picker .cmyk-text,.color-picker .hsla-text,.color-picker .rgba-text,.color-picker .value-text{width:100%;padding:4px 8px;font-size:11px}.color-picker .cmyk-text .box,.color-picker .hsla-text .box,.color-picker .rgba-text .box{padding:0 24px 8px 8px}.color-picker .value-text .box{padding:0 8px 8px}.color-picker .cmyk-text .box div,.color-picker .hsla-text .box div,.color-picker .rgba-text .box div,.color-picker .value-text .box div{flex:1 1 auto;margin-right:8px;text-align:center;color:#555}.color-picker .cmyk-text .box div:last-child,.color-picker .hsla-text .box div:last-child,.color-picker .rgba-text .box div:last-child,.color-picker .value-text .box div:last-child{margin-right:0}.color-picker .cmyk-text .box input,.color-picker .hsla-text .box input,.color-picker .rgba-text .box input,.color-picker .value-text .box input{float:left;flex:1;padding:1px;margin:0 8px 0 0;border:#a9a9a9 solid 1px}.color-picker .cmyk-text .box input:last-child,.color-picker .hsla-text .box input:last-child,.color-picker .rgba-text .box input:last-child,.color-picker .value-text .box input:last-child{margin-right:0}.color-picker .hue-alpha{align-items:center;margin-bottom:3px}.color-picker .hue{direction:ltr;width:100%;height:16px;margin-bottom:16px;border:none;cursor:pointer;background-size:100% 100%;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJYAAAAQCAYAAAD06IYnAAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH4AIWDwkUFWbCCAAAAFxJREFUaN7t0kEKg0AQAME2x83/n2qu5qCgD1iDhCoYdpnbQC9bbY1qVO/jvc6k3ad91s7/7F1/csgPrujuQ17BDYSFsBAWwgJhISyEBcJCWAgLhIWwEBYIi2f7Ar/1TCgFH2X9AAAAAElFTkSuQmCC)}.color-picker .value{direction:rtl;width:100%;height:16px;margin-bottom:16px;border:none;cursor:pointer;background-size:100% 100%;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJYAAAAQCAYAAAD06IYnAAACTklEQVR42u3SYUcrABhA4U2SkmRJMmWSJklKJiWZZpKUJJskKUmaTFImKZOUzMySpGRmliRNJilJSpKSJEtmSpIpmWmSdO736/6D+x7OP3gUCoWCv1cqlSQlJZGcnExKSgqpqamkpaWRnp5ORkYGmZmZqFQqsrKyyM7OJicnh9zcXNRqNXl5eeTn56PRaCgoKKCwsJCioiK0Wi3FxcWUlJRQWlpKWVkZ5eXlVFRUUFlZiU6no6qqiurqampqaqitraWurg69Xk99fT0GgwGj0UhDQwONjY00NTXR3NxMS0sLra2ttLW10d7ejslkwmw209HRQWdnJ11dXXR3d9PT00Nvby99fX309/czMDDA4OAgFouFoaEhrFYrw8PDjIyMMDo6ytjYGDabjfHxcSYmJpicnGRqagq73c709DQzMzPMzs4yNzfH/Pw8DocDp9OJy+XC7XazsLDA4uIiS0tLLC8vs7KywurqKmtra3g8HrxeLz6fD7/fz/r6OhsbG2xubrK1tcX29jaBQICdnR2CwSC7u7vs7e2xv7/PwcEBh4eHHB0dcXx8zMnJCaenp5ydnXF+fs7FxQWXl5dcXV1xfX3Nzc0Nt7e33N3dEQqFuL+/5+HhgXA4TCQS4fHxkaenJ56fn3l5eeH19ZVoNMrb2xvv7+98fHwQi8WIx+N8fn6SSCT4+vri+/ubn58ffn9/+VcKgSWwBJbAElgCS2AJLIElsASWwBJYAktgCSyBJbAElsASWAJLYAksgSWwBJbAElgCS2AJLIElsP4/WH8AmJ5Z6jHS4h8AAAAASUVORK5CYII=)}.color-picker .alpha{direction:ltr;width:100%;height:16px;border:none;cursor:pointer;background-size:100% 100%;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJYAAAAQCAYAAAD06IYnAAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH4AIWDwYQlZMa3gAAAWVJREFUaN7tmEGO6jAQRCsOArHgBpyAJYGjcGocxAm4A2IHpmoWE0eBH+ezmFlNvU06shJ3W6VEelWMUQAIIF9f6qZpimsA1LYtS2uF51/u27YVAFZVRUkEoGHdPV/sIcbIEIIkUdI/9Xa7neyv61+SWFUVAVCSct00TWn2fv6u3+Ecfd3tXzy/0+nEUu+SPjo/kqzrmiQpScN6v98XewfA8/lMkiLJ2WxGSUopcT6fM6U0NX9/frfbjev1WtfrlZfLhYfDQQHG/AIOlnGwjINlHCxjHCzjYJm/TJWdCwquJXseFFzGwDNNeiKMOJTO8xQdDQaeB29+K9efeLaBo9J7vdvtJj1RjFFjfiv7qv95tjx/7leSQgh93e1ffMeIp6O+YQjho/N791t1XVOSSI7N//K+4/GoxWLBx+PB5/Op5XLJ+/3OlJJWqxU3m83ovv5iGf8KjYNlHCxjHCzjYBkHy5gf5gusvQU7U37jTAAAAABJRU5ErkJggg==)}.color-picker .type-policy{position:absolute;top:218px;right:12px;width:16px;height:24px;background-size:8px 16px;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAgCAYAAAAffCjxAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAACewAAAnsB01CO3AAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAIASURBVEiJ7ZY9axRRFIafsxMStrLQJpAgpBFhi+C9w1YSo00I6RZ/g9vZpBf/QOr4GyRgkSKNSrAadsZqQGwCkuAWyRZJsySwvhZ7N/vhzrgbLH3Ld8597jlzz50zJokyxXH8DqDVar0qi6v8BbItqSGpEcfxdlmsFWXkvX8AfAVWg3UKPEnT9GKujMzsAFgZsVaCN1VTQd77XUnrgE1kv+6935268WRpzrnHZvYRWC7YvC3pRZZl3wozqtVqiyH9IgjAspkd1Gq1xUJQtVrdB9ZKIAOthdg/Qc65LUk7wNIMoCVJO865rYFhkqjX6/d7vV4GPJwBMqofURS5JEk6FYBer/eeYb/Mo9WwFnPOvQbeAvfuAAK4BN4sAJtAG/gJIElmNuiJyba3EGNmZiPeZuEVmVell/Y/6N+CzDn3AXhEOOo7Hv/3BeAz8IzQkMPnJbuPx1wC+yYJ7/0nYIP5S/0FHKdp+rwCEEXRS/rf5Hl1Gtb2M0iSpCOpCZzPATmX1EySpHMLAsiy7MjMDoHrGSDXZnaYZdnRwBh7J91utwmczAA6CbG3GgPleX4jqUH/a1CktqRGnuc3hSCAMB32gKspkCtgb3KCQMmkjeP4WNJThrNNZval1WptTIsv7JtQ4tmIdRa8qSoEpWl6YWZNoAN0zKxZNPehpLSBZv2t+Q0CJ9lLnARQLAAAAABJRU5ErkJggg==);background-repeat:no-repeat;background-position:center}.color-picker .type-policy .type-policy-arrow{display:block;width:100%;height:50%}.color-picker .selected-color{position:absolute;top:16px;left:8px;width:40px;height:40px;border:1px solid #a9a9a9;border-radius:50%}.color-picker .selected-color-background{width:40px;height:40px;border-radius:50%;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAh0lEQVRYR+2W0QlAMQgD60zdfwOdqa8TmI/wQMr5K0I5bZLIzLOa2nt37VVVbd+dDx5obgCC3KBLwJ2ff4PnVidkf+ucIhw80HQaCLo3DMH3CRK3iFsmAWVl6hPNDwt8EvNE5q+YuEXcMgkonVM6SdyCoEvAnZ8v1Hjx817MilmxSUB5rdLJDycZgUAZUch/AAAAAElFTkSuQmCC)}.color-picker .saturation-lightness{direction:ltr;width:100%;height:130px;border:none;cursor:pointer;touch-action:manipulation;background-size:100% 100%;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAOYAAACCCAYAAABSD7T3AAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH4AIWDwksPWR6lgAAIABJREFUeNrtnVuT47gRrAHN+P//Or/61Y5wONZ7mZ1u3XAeLMjJZGZVgdKsfc5xR3S0RIIUW+CHzCpc2McYo7XGv3ex7UiZd57rjyzzv+v+33X/R/+3r/f7vR386Y+TvKNcf/wdhTLPcv9qU2wZd74uth0t1821jkIZLPcsI/6nWa4XvutquU0Z85mnx80S/ZzgpnLnOtHNt7/ofx1TKXcSNzN/7qbMQ3ju7rNQmMYYd/4s2j9aa+P+gGaMcZrb1M/tdrvf7/d2v99P9/t93O/3cbvdxu12G9frdVwul3E+n8c///nP+2+//Xb66aefxl//+tfx5z//2YK5Al2rgvf4UsbpdGrB52bAvArXpuzjmiqAVSGz5eDmGYXzhbAZmCrnmzddpUU+8Y1dAOYeXCtDUwVwV7YCGH6uAmyMcZ9l5vkUaBPGMUZ7/J5w/792/fvv9Xq93263dr/fTxPECeME8nK5jM/Pz/HTTz/dv337dvrll1/GP/7xj/G3v/1t/OUvfwkVswongjdOp9PzH3U3D3zmWGnZVXn4jCqs7wC2BKP4/8tAzkZsoWx6XrqeHZymvp4ABCBJhTQwKfDT8gzrZCIqi5AhiACjBfEB2rP8/X63MM7f6/V6v9/v7Xa7bYC83W7jcrlsVHIq5ffv30+//fbb+OWXX8ZPP/00/v73v4+ff/75JSvbeu+bL2WMMaFbAlpBNM85QX+ct6qoSqkPAwuQlBVKqGNFSUOAA3Bmu7gC5hNOd15nSwvAOUW7C4giUCV8Sgn5L9hNFIqTsp0GxI0ysioyjAjkY/tGJVEpz+fz+OWXX+7fv38//f777+Pbt2/j119/HT///PP49ddfx8fHRwrmTjV779EXu2px2xhjwtdJZQcAWQIPLPISsMJaSwiD8gzIKrwSyATE5j5nAbR5c1dBUwBlsEWW0h6LqiYsqFPAQxCyRZ3wOSARxmlXMX5k64pQfvv27f75+dk+Pj5OHx8f4/v37+Pbt2/jt99+G9++fRsfHx/jcrmUFLO31gYDWblxRIs/TqfT7ousxJsAxXA2Gc7TA9XdgfdoHbFsj76X2+1WArgI1ageGwA3qupqoHsmcbI6Fu93quggFa9d7LeDtgKfAFHBJ+NEByIkcJ5KervdTmhhGcgJJSZ5vn//fj+fz+18Pp8+Pz/H5+fnmGD+/vvv4/v37+Pj42N8fn6O2+1Ws7JjjP6wraMI5E4RZ8x2vV5TSwkquotV7/d7Tz6HFWsD/qNcdw0CQ3q/321c686TwDVIdbuy73zNldhSHb8I2klZznm+InBS4U6n0302aBFsLhHDAKJVJVglfI9jhvu53W53sLANYNxAiDA6MCeUHx8f9+v12i6XS7tcLqcZW57P5yeY8/fz83Ocz+fnsSmYUyknWEG85WBst9stzSLyMdfr9Qi08iY15UZ0LlDGLhR3o5zK2j7OPUTD0E+nU3tk7Xb/16NFbhloAMuY1zjLUOO3BKeIDe+Z8s3/J4gFo4TM5jPmuRg28foUKKVSwo16TgA5npywcWLHgYl/Pz8/73/605/ab7/91m63W7tcLie0sZj4mao5gTyfz88E0f1+j8EcYzwTPEG2cqjyfHNF0M8fuqEiaOVnRzZZQNh5fwQyHg/HDGfJo89Q1zb/quu5XC6773I2XKfTqd/v9+d3wuqWva/YTdUdEV3fhIv/Viyps6YE3x3r43K5bJQS66zaxVGFsvd+//j4aF+/fm3fv39vt9utff36tf3+++/tdrudvn37ZuNLBaaCMgUzC+rZRiFowxUuJI8YMqcCp9Opq5vagaYU6lGJA1XQqejchw6Cj0Gw5nYBrGw01A2O206n04BGouNNyTfp/FwElhUey6nXrIKw7QQWddxuN2ldL5fL839gSPF8ahu/JvBO48CPSuqMf8Vp9/P53L58+dLu93s7n8/tfr8/39/v9/b5+TkhPJ3P56mQ436/j+/fv+/iSgbzer0+AZx/5+88bv6OMda6S5z6kd21fYC9dxv7cIJJ2d9AOS30fPMzyHiTM8B4DF6XUlYHp4KQW3W+1t77MNB1vGHxWq7Xa7vf78+y5/N5A+H1et29xuP5dbYtyaRu4AksbPq6936fjRzXRxBbPr/b+b18+fKljTHaBBBfn8/n0/1+H1++fBnn8zm0sB8fH5u4cr5GuBhMVk0EEn9RsctgVhM+ixlJtMA23R8B6yysAstBOgFXIKKCMIgToMqNEu2fYMH7ztc732dQKkCj1ytAZtY0Kx8pIr8GGJ+AT3V+2Hirhl++fBmXy2Wz73w+b17P8p+fn8/tUwGVleVkTyUb68DkfayWY4zxNRihU4EpLJPZVrK+u7J4/mgfKqeLW9X2REWlItL1diynbDDb3+jXgYjQqn0rrxWc+NkILP7F7xIbMvx7vV53x40xnlbWJF12ZSag/N0pW6t+ZzmOMzHjajKwDfond78zYTdfq18up97zr2q8v3IioBprRtBl0EZ9og5WBRGOdOHjIjXF7UotFbgOWnXzIJyzYvjG5IYgsmMOxHkz8OsMSrVNWeq5T8DaOcbEv1Od5rbs9aO7YvMet63EkF++fMExq+MRl4/L5bLZN/+ez+fnZ6KazuMqXSQVO5spJXflHAIzes/xJseckRJiDMog9d6VfRrqXMr6KpVV27jRwJacGovOAM1zMdQMnwK1AubK63kdCChvI1C7g0z9nf/D+Xze2Vj8H7Gx4P9duQlsYCrqyN8XqG3Hm/10Oj3jw/n+crlstuM+jPmmxT2dTuPz83Pzt2pn1XsEHX/bnPaVqVmh0xwOt0o6XLLAHePUU203wHfcrspCwmV3TryB5s0Mseeg97x/BwzCjBlbB+pRAPla0BVQuT6V6QHdBlj3d0KG147b+DqxQeUymDO43W4dQar+TIjwmAd0z8/h65vf0/yLv3Pb5XLpru/ydDo9s7ET0I+Pj6dKK9VUEIeKWQWPAOrJ8LKd4vE+t91Y3e7UFlWatg2VwJnb+HPmtvm/sfK59/OaWF3x/eP1UPHvA5DDYDpYXfb0drv1V2DkBkxtw/tEWVVlXWdC9pFYs5/jfh9dS/16vW7s6lTG+TfqsxSJHxkXXq/Xdr1eu4LsfD6P3vsT3N77DkL+zPm5jSdKL4zR3AxQd6rHkLkYlSowsrq7znzu6wSwdsMJOXmA5fBcjxtgMGBYHlr5zokhtsMCTgXLQOW4XC6dEyEMprL8mAQzXRgduix2yZzorxkYsDn3hB1VeMLGsXsVtgl2pW8S3svk0vw7R4hNaHvv4cACl5HFzwIH0Kc6zu4XjDPR/jpAVxWzO1Xk2DDb3vTcxeGU1iWZHkmIDWziWKvirCJ4Dravs6IJ/GG6cTqWdXDy+fArQDVVkLqkVjAoZIITdmmIqXwqa95N3+MGYoZQdRVNO53Y1xRkhO16vY7eu507Ca9lJnbGpxOemQhSw/AQsmmp5zU9BiU8G6wvX76M6/U6Pj4+do0Bz4CpgiknTUeDqwlKBmg3u4OVjrZ1A+rAcgaejWq6eJCvCYFDONSwOgHX4EQRw8lxbzDOdEK6gZ3Hk1b+8g2o1JFtKXyv/fEdTXuWjWXdAZiBp6ADeDrCFiim7B6ZFneeI7Gvm/PMkUDX67W7xI8b0D7/v8dA9qfN5oaCf74WZjH0mf1cmfY1Y0JUFmVrTWu8uzkNcLtEj7u5FXBTkfC6GOA5q8YMxO8KVvF6sAVGdcrUbsKODcQKkLMOMdmlxum642YrPm26AlhZW1YB1R+rrGswE8TaYAWeUMxdf+WjwSvZ2Ef3ytOyfn5+PpVPAaqOn43MtNBqvmjjxbjM4lZjZY4gqNMI5ktaW/sYKNwS+9lFQzGihmMCKPa7+Z0V6Eb0GRmobtpX8JljWu5FMLN5ja6hG9kwQgZqf5+1NH5UxzkFReCdWhJ8XdlGUkxO7HRlYRm4mVO43W7ter12TPJEw/rmEN3L5SKHIWZg9mz+pUoKOYq5bJTJdX2gme1UcxMZQFaEQIlHct32M+Y1BzGkGuzfiyAN9z+ugplZ1symCrDCYYkGxDTpI9RzBy0rHyeDUC1nWaeUaD9n4xkNyYMBDZtzZ3B++fJlY21XFDOcARJlabOyiS3uCpLI9jrZjCDkaVvcCCjwognKShWdzXZWlZMvVTgD8LpqlCLrqgbcB+qYwrgKYpT0ccCqbKyCValkEabn/FynogCrPKfqf51xJ7sGB2ZXcZmxoSOztjx300DZi7a0/2AIR0UlBag9SuDw6KcAzlaB7vHZvWpjK90dyrq6bKyDUZQbR0B05biLQkHIcSUmgIK+SwuqgHCnoio2RQU1yj+BnBy9pphVKLGyC7ZzFK1pxWK+E8IhVCWLN/uLtnUU4ayoYLoaANz8FdtaSvY4pV0BEW2ls61czqllBKpTyKgMAhrZ1cdc1RROtPmvWNkdcKZ7ZKxaWjiPLJMpp7OZKxA+rqG/oJLjxf0pnJlqLoDZo3gyU0mKGys2taKecj/d1C+rJSplBqlTyAqgR+D8KjKlmRL2gtUcAdCtsL+ijCNT1oqqqkH2OHEbG5sDFnUg5Aa+yLou2VU1ptj1S2ZQqv1ORZN9IWzRfgaRBxKoBE8UWyqlJFtrIc0AxNjSjed99CTY/XDfSzCz5M0IZoVEsWnPFNTsl8ooVC1TzbGgqFZNDSgVwKK+1sGDMKqxZCWGVMDysiEr1jVSQJUYwj5iHOlThdHt44SQg9CN+nl8D90NMIgAdgr46JqRiR9I8vRdFvbr17m/yxUMKjNLMiVUADwu2CWGhhi+F55TWM9M9cogzms1dnM4uOF/LAEYWdcqnM7yFmyq3IfwmOROd7Y1iFWtOjoY8To41mTV5IysgFFuRzsbWFGbNIIJCDv1dOo4lZG7jWBwRFtVTKuWyeCByJKOan8oZ3ep9XddNl0tDuaywLz9cXPYeDAA0SpkBO9sbVcTOVWldPv4uyzEkzxHtjvonHoSkFEWNoo1d8DhcQputd2ppNon4BzoAiJ1hBFQg0dVtdbGHHDQWushmNEQukLM2QO1G2Y8bgTXqFhcBJj7EjPgcPts8US8qPpPB/dXznOh5Z438tzH5ec6QgrOKrRRfKmysBmUDB+PhYabMlVPER+GCSITTzr7am2tArH3bgcEzPJm+cr5jJ4NnHNFDVrFXcI5Le9k5Jnw+bedbV+FfRzZIHaOOaOsLY0/7UGs58DjrGwKMIMFIGzOEW1/jGsdAtCN6hEAI4hBe9YXeRROBSVPAVPAqvIM5bx5hVKWAMP6zBRy3iescridVdFBinBxXDnG2GRY2XbCvp1lhvGtO9Bxu5h908XQu42lnSArMFdizMim8uwRCxPGnnOS8lwpnbOiDqTAjsrRN/PcoAScCbaACqVM40ylnjjTBs+bwWlAG23/UKbdkiwKWIQPGzWaczpoSlxPEj822cNWkpS7FyzsDrqpfgpG3jahw2vgbaSQAxuLWZYt7JzyNe8JoZpNAcvDFOdw0wqYT9AK1rZz/DdbSlLPp0ryIxgQJlK9AZlEq7IOXpohg9PIhrCng88JsOxiV4ZWAYfg4sikx/8ky2Z9l862uqwrfscIH8+ugTmVGyiddeVYUgEMn4GZzg14EwIsh9sx2cKKiWXReuOE5gzGOQgdlRKVVdlevqb279Xq0Qnsts2VDaBO0coezsruWtHApu6sKG4IBhN0aGU2kLrMKGRTN3HmbCDwKV14zvkMEDG4QfZVspVlaNU2mhc5TEZ3N1h/zqTheuLpW05ZWTGVjb3dbnNmxKZBnN8JqidaVLKAOyARNLS+MB54Z2+VaqoMLKroVBlngefnTPAcoHNWCSvlfA8CI0HEmBNBnBlXyMrzU7A7WVm94PPqQ2gmqKx+WDGsnvilmcSOBJqOK1nYyAIzuAyesq3UdSK3KfWcYKD95HmfYOU3qser2CtYEUA+FpfqdNvgPBZUBhDrGONRVlQsh8rLcaUCykHG0OOUwTlLBrsh5soEMGezi1E4HRVt1icp5wZEFXdibCkG8Y8vX75sbO4E0iom9z+hjSiOfy3DhpXItpVhE+UGQdvoWjtChmrGHf4YAzKgBNnGtuJxFCeGdhUAfQLLK8kBYAP6gvFJZajMG3Xkycy8KuC0q4Eyymwtwdxdv2M0mIBtK0LKnf640j00Auq4gUkdWGlhs22qJc6dZCsL19oxnlTJG4SYVRIGpD8TPFBuM6OElbS1pldid4mGAyN6ZIupbC5bXJN9fdpbThSxLUaI8IG1XIYBxW3Tjs6KQosKcxfxcQmdnwRGM10GnFcCy2XYunLMyAkdgk4mePiczsLygthcBut6goOqS7YVFXADLjaosB6s6ofcZWAZSIRYqSUkizYwttYab3vUOQ9w2HRxIIg8WwRVeE68xi4UtL3zRphxplzwuZrcqYCq1I3jPI5dnJIygEohMbPqVJSzrwzxBJTs5zN+ReUSgxikPQVF3JVBeNQxbHENrEMNvEdFZVV9lH9+ORGEsNZQpyTNc4C3AG7XF4ngzq+DrO2zbuaaOXgdaFcdkEotoSFBVX2qJ0C8OWZeG4KGlpghA0XfTOPCqV2qqwQ26QWfF2PMLhI2w1lVAa2aPsYd0za25MQRwgcZN6uQDCi+ZxiD4XEM2kZxOT41FnZnaRlcpZouzlRqqdbQVWopQoSB58RV50lBNrHi/AwXS5LrwDVlpY3Fc3ByiYGc52Trist6kOXdwInAQtJpp5QchyaquYOV7Su+fxVMaV3dc0RE2S6mUY0gLt2pMcYqrKIQ9w2l1gpQUMtQYcmmbt5DTNxdhnUCjQqtbK9SUSzvrC0mmhhE1e2FS2+oxypy/ZASutkmtjx3vcBC24PX65nbqkBCRhfjS9kIYPnee8cMagVOhI/3T1fAmdtAWZsCswTJCkQVNa0qWKSKPOpHAUhD9DrbVcyoYkwqhvh17vYAayXLQyKGYdxlUDFp494rBXRjYgO17DDYetNIUj/ezp6S0lnlpEwsWmJMkOwsKXeZKEAjIHn0EQJISaRBcO6UMINz7p/bEjjnw4ft+xmDvksxX4G2rIris7qaeKwAFMP2Oi7n4criuZwtpSUwpfLxSnORSrIqusc5ZFaXysqRWjiZ2DyAWEIL35tVSoQElFACjOeGGSE7AHEQgdo/LSvCOgGBvkxsmDbvlS3Fp5vhaB2TAGqRKrKKMrhLVpaGzEVjZ0OQxDhaCTA+QyRR1d15aQzrJntL3RibsipjG6jlgL4yqbS0sNYg1e84vhbBVrElK64CUcWYXDfKxhpIuxiVJZUxsbMy/uRBKTNRQ4kQ3LdRYLS0rJjRPlTPqY6gdJsEDc+aQXAn+HgsNUCbRuF0Oj0zwnA7bWDkbhO5Ens00qeQhS1laBMl5M/cAaxsLF8rKyql+Tf7ELLEGu/ixiimdCvo0TjfpjKwaggen4eh5v7LokLKbLuyvHhcZG8dhGrEDx7Hg93ZppJF7qBqO3iVveXEDQNInzeoe8Yq6ePaZBZ2JviM3W2UAGotekRCAGq4EkF1X3DOnR11yRsBL1tRa0PVcZiNFXZ2c34FskvomInQQ6lzpJoZbJxk43NwKJFBquJSsrByHydxKOnTxQASBmS3j+JMnsHSla3Ec6K9VWoJVn9zfjwOM7hqYAAqJQwE2a3nA48J2QGegRkpZNivSY+ys3EkKd4oJIwsvIHl3cWgLt5k4NH6OmtLWdpurOkwEMupYc7eMtDRhOcI2ui5JhVIzXzLyto/GAPuZoyo8wkoduVgJglCt7OhGbgID4Mq4si+63zUS1FuFFXFlqyaj2emHlLMcBqYu0FMuR28BbB7lOxRMSiCQXFhCKuwkhZ+pYDiGSgbsKKV8MiSRsuHSIWM9rklRiIlZZuqXjsQK8ooYJMgq3JKWVkhHbhsVxFUzthOWPkYijcbx54IKsSdT+uLr3crGKyoYgFiGR9iBk4kfloUX+JIlQRQqabmpgnhqtpQpb6RVQ1WH5DnrS4hEoGZqaerQ2dhFbz8XePxShmDbo70eISjoorO2vK8SJXI4SUmEU4zWKDzUDtWTYw7xXlbSTEj4FRg7zKnKoGRALv0Gs9Tgc1BpCywGZRQAtqVz2xrBcAMzEpfZwFSa2G5W0QBFjSMapWAEFa3HcGN7CxDzECyIkJ97qwrqWNTWVo876PPsjPkj2wvgroM5lLZKMETKVql/CvnWVFiFa/SzJUQwkoZsr67Y6vlSRV3/2tmNTOY3vnaxYwMuoPKqdzR1w7IqHymlPxaAThfU7Ko2ZXYj4AYJHL+kNdKwRQYESTRa5fsUZ/rVC1TMTyWVyYoqNtuzaHsMyv2tvoarxdfqwYgU1axFo/cnql1FGsqK+uAROV8BX4GU8WcZTATi2q7Qcyi0O0V+GhWBMNRUkn8H1SsWVE5By3Gi0ECqUeJoBfAtDa4amkdXG37AGP5Ggeb84p7UazpoKRzdFzeQ8HkoHGxprKy/Hpm5t12p47J6xTYDEz7uINEXSuxYXvFskYAc+ySxH9sf5ftKzU6IbwVBcUGg5e5FMCEXSErZR0wGayV19woM9guPjTqJdVTqR4uE4nJnLldWVkECCZLd2VLF+xtamex7IpiriSDUpvrpn9lrwGMCHyppMH+ps6LILsuFGUj1XEOXiqbqSHPUKnClpWV68kqtURVNDY4TNaocykoYeTU5ngGEQa/S1DnnE4AeXMcKjHPAmFVjCBENaeyLVNHfr3px8xUstJ94hIpfH4HKE/eDaArK6lSyVVFbdt1gxTIVk3pppVlFXi4pEhVBTObquohU85MLXn1iahvUkHJjSCMc01tLFveVVBx0DodM6jftCu7DOtIzYxrc0qp1JGP2ayYFz2Gb6HvMrO8cnGtV6Gjm3uImSfD2GpWK6uowbZGMxFKQCo1pOMtcMXFpRst+hXGoAomF3sSTBGgTglbBKWwsQ3tZqaYSp0Z1CimRDWFcCJUPYJ00BI5FkKYNoifuQxmN88SWVXWLMaUqqqgC0BmQJR6sk3u9NCf6jYLXxAfqsYEgVLAhRY2AtgtflZNFmFyhxdrLkAdWlk4D88M2ixHyepIdhMHrG/iR1ZGtq0MGpbDbRPYOXeSY1M6Ny4ZstvGSktK+XbFPATj2D371saPEsAMXhXrsZ0km/XStkhhMyBfsa6uXFZe2VCe+YMr1+GKgwrQyNYq1VRrB+EizAow6NsdNKcyVEkYeM73ys6q4kAHp6BiFklTkIrVC5oYV7uzwOGCz4UJ0Stq2lWMJy4wtb+RetL6tZFicnJmBw5UjCvXXMZVJX2MQkbf+XN5EWd78Vz8/JEsMZTBiKNzsm1inLRUQ74H4NidaqI68j5sAFgxcRveC7ieLJXfQYxjZZ2CsiWFewZXJmBIlZ1tdtrX4hSuateKso/RZOtOKW2nmq1oTzeK6dRWAWu2NRVb4hq0SXm1GvtugHrbr5IXqmSktg5CuDE2MSlPwsY5kNE2Wp3AqiZbWVLAxiBF+2iBZbuNj6MB6rsMLC7FyasaYDyo7KkoPyEtw3pEMXfPvxAJi2jAQQgjrz0rLIZSWZlIoNhwd5xK4AR9mYNjWAaLrnuImJeBVN9zBORObVvbr+mTTfFSEJLSRnHo7hEJoIi8MFqjxmvgmF5URZz4zLFgZZ8Ctu2X7ggVccKm9gVxIsOHqxXgNMKnFWZYnf1dBnOhayXq17QwFlWW09eNKyVJFmXqaONGA5aCegMbJ3UUkGY1ic3nKWgjq8qfVYGQG1gRt6rs62a6HiqqUOqdesK5NmX4nGofJoiE1d0dF9lVVkvT1/kEEaaCoYOwFpcVcoLM+7669PxC9rWqktH0sWUYld0VCpuBZ/stVRcGgy9WX2+U1Qthi9SzAqSxzZsy+OiFzBYnySGV6Gku44rD8BCOZBV3BvD5+AKRHNwMEsB6EzHnJpkTAeiUlEGkcECeB6GDZTp5YEJTlvdrknxYjTllMkfNtXwDjM7uVjK5JXUUn43rrqpK2jytaxHW0M5G8DC8rtHMYs7KSgduVQMGTYFqFvVS6rkD3sDJ46afdYFwoq11AOKCBLhvwoUgc8IGANycR6knZrdJPdsuxnyjfd3FovTlRMdEdtOl5CMV5EHsXQBis7TOwvIDZaGj2Vnpbh7cpK63VwYEMLwqbjzyl699sawFFkF1yqjUU31HfC6sW1ZFVFuXVXVgz9keEaw0ys1lWfm+azQAQSWA+hKYVfsZjPncAcUB9oIayy/UZXRNckDGji77GsWbvBo6tPrWPqOyVkBUq+INeqpzNdYs/u0ifh5qmpqIW+33JVSUcwY70KL4U9lYdU6ljtSls7lmfi9g3YzeQfVkaGFaV3ODCnaD2N8wsEDFklE3RzM3ZghdYkWHsszq70FIecnKkVkt8ezMzRq9bkGuKojRLBVSod3Y1yPqKgYW7JRQTPVyy5xIYLjOgxgT52RKJUY1dOrIiRd4futQx/A5AcSmEjz0vFWrkLzvbWAu9HOWbGgxFk1VNTpnBKk6TgwisI/HcxYXP1uAWO72ULFlBTq+aSu2VTUs6hrxM2CF+hEor1VIA9ZmFUaab1lSSgZsVs4sxzHlVLoJHr9H4DhONTkI1XC0/wiY2NoWAG5RlnHFnq6oLccpQddMuJ/O17JVA5OHLi0BqCztq7Y1++ucCd98qLI8MIHBV/cKjxQTme3hFBS3MyCqnDsuym2o80HjvFFTtrURmNaGJsmVahImjTsUXKtQZTAVs7Mvv8/+fzUrZAXcLJ6M4koe6XP0b6SmWWNDzyUpQ8bl+LtWx4tuqZ36cRYV3yuVxPNwvIiqiQCSmu7srgTzR6nkyhpCarXwFy1vGd5iP2cY06lFr5Njhhg1Y6+NB28ftbK83s8rf7kLJbKwDFPbLg25a0AdZJEiqr5phixKMDlRUtcssq1hriLqGoH+zeNgVm9OemjsETV8JdF0NHnkIFxWY1OB4Yrp7rtWJ7NgAAAPXklEQVQ3oNs5nplyVf8u2FoLu1JrHveaZWQjqAkshtFa2gzsSG3Zpkbvg3HafF9slPPlldjFlK80Gysm8Mr4MPhneNWENPGjAIpmilTPATdTRTXlCBYHYAQuPwA36xIpWtGN4q3Y2MhiGsUpuSSnlEJRD8PorC7CFYVw+F51qThgabxsTxWzCGY0ZSsb3lfqAy0OPNjNy8xiQQKsHYFQ2HBZVvVbBuq3m1oWKajqaonsM6uZUr6CjXWNZ0l5E3h3jURma6kP3MJIiy1Lm+kahQq41N2iZja5sjtlLYNZHZrH6qUGm4vMbDp6Rw2CFmvuyFkrBcCyMtFqBaECmsHoK9BZ2LA/lJcRqSaDqnaWbrZdGaz3DLgIvBln4woGztbyJGqslwxkhhHrTjTYFXCtOoKS8uLdofVdAbOylGU6nlYpXWZts4nXBq6WxJitMNokHUJnbnJplQm+aGpY2a5GMV2QD1hRubBPFKdumf5OHkLHz0F9luE5kjBjRa0nFE5CUGqHw32MmjZ6xkgINVnSnZ1VZStK2qKlRaLlQgK7uTq7JFXJwM+3SOEKyhZNI+tJ0I5qMYy9k2qJD7dVWdqKXa0CKNR0Ccjg+B2IYu2fcBZJZkMFgM11r0X92wilghFGgzVnexlqB7xL9mS29SiYUVY2nXOZjNBRsyDsQPRWW5hrZ4XcdC4HVWRbjgJr4sFofK5SzjQ7rhI1UebdPdEbj6sqIvTZQZ5va08rABsAW0UxeWytAk7A2KJ9ZpxzCioB24XFtYAeXYxr6anSqhLgppEqWbGwLunTgrV+IjWlL29ljaAl4EQMGsErp4apeZiquwRXLXAqOCeru32mmydc6oWTSWpFAGdzeTB8RTHVMEtlM90CbbQCYhPjq3egYr1FGdYIQjiuDGZ5zZ/AzobKGOyLxti6c4Rwtv2anyWlLICnlLhxJRXt6A5ebDBWFNONbxWZ2d02mnu4S9YECpeppV1zSWRBWxHYzVIv1CXSouwqqX3jBBBDZdYQbpTQW4ZQlS8r5kH4suSRmg2++3JN10x1PaAmEkmtYlEdeGpJEM6kOuCqCR22oSujj5IV2HdT0zj5prLKTjXFAPjdQlyq7xIBxAQP5yMczG4VxAKw0n6ilZ2QBce2pLulkuxxqnoIzFfgqyqjil9S1VNwBrFmeyeops8yOjZUybZdfS8CuaTIJumzs5tODaNtLpFDQ/PcJGweLhmeL1nB0KqiUDScsiUVD89Di3HtrKtSULw3RLiygZD+7sF8JTObgYsrGvDNUFRGl1iy0Ll1YkUc2aJYMog920I8qW6YDCg1Mqk0JHJFKXkbgbRreI+qpYNOZHrVcDUba7pjsphSJNtK6upgRNAVoOS0mugBeN4bIZgHhuPZ/s1ENaX6KsVr+YNrh1Nb7ipR0PE5zbNRegCbrHRUw6Yf07dLBJl1f8KB9as2V1nNqAsl62LBBhehwalerkHmB1JFIEZKSEusdl5JQj1nJlHXSCF342gJ9CYGrXelknJIXqVP8sD+qtplCR3XH2qfKq0ygMp+KnVkKxNlZ8m2YkIlVMiCnXUwl7qznBKSvQz3m3Pt6oQbXO5b5FixCh/fHxUQW/AEcK6zCNqKQnL9sywqmKuwvqSYzT/aPVNNpVyhvRW21aqciCsjdWvBwILUvh5VyCzbWoC1pJjJ680CWsl+udKB6T5RwG1mlohnlpbg47iz5U9ha0FGtmRLFYBtO99y97Ap0z+ZDTAog6kSLZsMHg/IFkkgp6CpvU2U0cYVSdnmkjwBdOmXbxTWNWzuIbipMioVxEckZEoahSOiy2M3K0jcC1LhVDwaqG0ZvkcWqCnrG4GIxykrqlbWdw6LQyBaZR8HmLRIhQWsHswD42ZXVLNkf9l+FlW0HVQ2lwFsC/Z1FdzlQR0KaPfo+Fdfu+/dwVRICu1CGR7AEIiAhc+AZUF0kOBaPxmUqg4i64vQnU4nFDYJ9Nz+1fVXveH9qmr+kPILx8oKcRV/BFbxbE0JMT0kSD4w6L/lNY8ocsqagVdU3A3MjxhxcGuqzsPH4irpaow1q6OyrVjvp9Npc59E91LldboYVzJWdimWfAW2SNEKcDaX2FmBLLA/uKxlmhh613Is1URQApbKfttwxL02q6Onx5pQxSbPojAg+v5hAnN6LHVRDXIsvKtRjiS0qJUyZTAXVbAK82ElFJWaQdVoqUC1Unt7BVaTQudM6SuqexjQJN4+0icaxv/utbKv83ETbT8H8gjcOKxOJmbUa6OOVXht3dFY6rHv9XoNzFLceEA1o8+pKm0LAHPHZ2rYKjFq0hfZFixsqHJgD3eD5n+U0kb1mFjXkn2lvMSSOsNE/CdIAKF0Sytq6urOHUN5gwg4GZosgbmggM5ucra2qrS2Ig1cbiBBcxYzgzUDNLCvL8GbZXNp6ORy3LmS+Kk83zRIAK6A1ioKa2I9NapIuiUFdfC9766PFZUtqUr6KbWk+zZU1a/ZrIXEztrjTOfz7hwKziCeXIaraHtbZIMz+2pGgazCmw4qWAFvEdhodYp0Xq0pV7G1YWYWbO4qhGq42+Z8BYtrLWvluNPpZAeaFFS1vubPgbgxsqcpnAaszBovKaFoDQ8BGtjfUOl4NAG2nmQV04feJgumvX2fsrQEWZghL0JnVdYkn3DOZIeRN86RqPWCmsvGVqEMRnwxQAxwS8EMYo3IzmY2+BCcLp4MKiuyuhImamlbZFcNoNl7tp+RHd18ZjQIRKyXdFRhN98/hyKqwXWNo7O1wiaXoHN108REZZWEq6grnIfjzeg8jdRf1XEL4kkXa5bBjKxoKaljBjeHlVxQ4GaycpW4lDOAKtnTxHAtOfzOtZwHAM7sqVXkV6yu6kap1nHkXKqWF/4XHqjenNKqBjpR3l1ch3Ejg1+EsgdQhsdG0B4FM9sWAVWpuAyiwTPleZxt9VyZVS2qXfReWqTAilpr9ApoWTjxymit7NwV4JTriZyOA9B0k7HFfULourmKYHVnRQvqGL5HMHdqFcR2qWpmcK6eTwx2dipWrviDilr+fKWq3OWRWdHKwA4eu8wjchbeRzFilqjjZN3ufCpfkJ0/scVpnYk6L0PI77lxdWCZ87WiWm7B/AGquQSnujGKsB8CJmiJq8q1pKIVWyqOiTK66r18BN8r74/AE71fdC3yPS2MxdOpnE1tlVxD9JmVOoggN+r4PjAXVFPa3Eg5jVJGFVUGNolH20GVrUB7BOySWq6WqYQdWR92pcFMYMwckbSgCKCqD67DiiWu1g8MQC9ByfcFqW1L+jL714qNCuznoSxt0da2gtWN1G8F0BK0NN0nuimelUF9dIdAfjO44UT3CjQLoUeLHJFTO3gmpRuIIOvwBQCbqNeo3qtZ9iF6xVK13GRlo4zqimq+CGdTiR1uRY8oqgE02hZBa79kZXPMquxRHKla2saZWN4mRqZUj0vLCKhkjKnqOQHNuSZVJoKvAqS1wpEquvWDC1B2ypwrCPsRMEPVTODMLJMDv6qeKXwi2JYV5Sq4qKyvgGsHCLiuj2jR59V8gMqSJ2FJZRXEHVRHj3sFPrct6OpqlW1GpatQdt0GvwfM6n63InsGVFhJGaBqgqqIV6IsXllZgySPq4R3bnt3wi5cv+cN2yqQLW1T95KYVsWWtKk4cB9W53WQQflQYR6Wl4HaJZjvVE0D5yvq+RKgZCs5qdBEP5sD94cAvQLlSgNaSMAtHx88BuNQ41zdFsX30zKbcs0MLD/ihkpQzl0wiTqKLTfbKmCmyYICnK0IbaieC4CG9iSyLQ7cIMGQwau6TKoq60Apl3WN40LZpca1CKKK9VQyyIEn8w0F8F6CL2h8o3ixGwC7s7EWzCOqmcApYxYD4jsAzVS0sl2t98pA7vrKophCVSonbYpgH6mvSn24pTBV4sdtV3BtMq5k82y+IADvUJ0uAlkCVTxIaPm+UNu/qkV4F1TzHXCGrXIAqItBKypqK99VtAOVs64O4ObX7pHLVCpYHcRmwvLR7TvYAKBBN58LGVzDuFz+hQbWgncQyCZAk+VbsPSouf93261iZgmfCpwRbAvqmSqriU2PwhjaoOyYqtIegVXViTsmyta6bGySpY3gyRrpIyAeaWDDxtpsXwKyalMDKNP7YBXMqEskUsi2uC8FNAPxAKTVfT1o6VzM0E0jF+1rWcUuHvdyg7vgoFplX8HpvHpMCOMRUPHzZkInsqlFKNX/EIO52E0SxSzOwob2VmRLW5D1XIU0rbgM1AzWgyC7fe8G7xUAK/taEBat7luqtyP7EmsaJQOj5F+mrnZfCuYCfBUAWwShyd6pMY/vAHG1UqOYpbI/gy5T0CMKm+UO3gFuC85dgfDVeguPDfITrIBLsLrcgdh3CFgFZjaKJ4Iv3F8ANEqvuxR1tVKOgLoCa1jxboBAkj6v7j/icFbA7f4rfRnQDLRViG13i0vqBQrYVqBbADZT0ZpiHoSzvQpopKIFS3sE1HfBWlHXd0H7LnArqvougMtljHBgZnh3Eoz/BKjLML4Z2Aq0+hEJr9jaVUBbvNzCIUiroC7AWmmFw4o5AK3MtB5VypZMSFgs05JyGVwlwBqsEGAAa2ZU1CjUexXGsE4rKriilBvFzOKKo3AuAroE6QFQU3u8YpNXwS5k+1TZt5UrwouN4KiUEw+k3ZWDp1RXHNRqXb21Ts39945yZSg3VnZFNQ9CF3XeZyr5DgBXKiwCMa2MxeTDYXgP1Fsf9QNKZc0k81RJk3r6EQ3rCmBVyLL75EjZ1pIVDHoFtiOAHoB0BdTVylqBsKKKS+AeBXJVLY+CXASuGvO/Auq7GuEjDfGKg1oKa1z/dmmi9I9SUGNhl0AtfulHAawoYrnSkmNXAVuGEhrEVXvUF+A5Ct2PqNOjDetyna4CmeUolmeXLN4Aq7C5Sj10Q7yjgl+t6CNxSRHmI5X+CpwreYB3Qfdqna4q21KdBuc4GoZsn49ZOOiVinwHqK9WzjvgeweEh2AU5+vtxZ9Cd9Wqkh49V18E5oj6vVyn0RStAyGIO5edXRKd5B0VGVXq2yr3xYp+5Ut+C4QJ4P1N339pQMjRejj4vb/Dcr6rQc3O/0rjmtZpeYCBiCHfCemRbNhbK/pNUPc3wfKy5f2D7OlL3/uPhve/oU4T0F8f+VNM2vyoiv0jK+KHQfdHq+0bncz4oz73/+Y6LbKw1o/5B7eOf1Rl/0du9B9tn/9bvrf/j+v0h6ttn2tp/r/4819y4/zv5391uvzzfwDifz6phT1MPgAAAABJRU5ErkJggg==)}.color-picker .cp-add-color-button-class{position:absolute;display:inline;padding:0;margin:3px -3px;border:0;cursor:pointer;background:transparent}.color-picker .cp-add-color-button-class:hover{text-decoration:underline}.color-picker .cp-add-color-button-class:disabled{cursor:not-allowed;color:#999}.color-picker .cp-add-color-button-class:disabled:hover{text-decoration:none}.color-picker .cp-remove-color-button-class{position:absolute;top:-5px;right:-5px;display:block;width:10px;height:10px;border-radius:50%;cursor:pointer;text-align:center;background:#fff;box-shadow:1px 1px 5px #333}.color-picker .cp-remove-color-button-class:before{content:\\\"x\\\";position:relative;bottom:3.5px;display:inline-block;font-size:10px}.color-picker .eyedropper-icon{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);fill:#fff;mix-blend-mode:exclusion}\\n\"],\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ColorPickerComponent, [{\n    type: Component,\n    args: [{\n      selector: 'color-picker',\n      encapsulation: ViewEncapsulation.None,\n      template: \"<div #dialogPopup class=\\\"color-picker\\\" [class.open]=\\\"show\\\" [style.display]=\\\"!show ? 'none' : 'block'\\\" [style.visibility]=\\\"hidden ? 'hidden' : 'visible'\\\" [style.top.px]=\\\"top\\\" [style.left.px]=\\\"left\\\" [style.position]=\\\"position\\\" [style.height.px]=\\\"cpHeight\\\" [style.width.px]=\\\"cpWidth\\\" (click)=\\\"$event.stopPropagation()\\\">\\n  <div *ngIf=\\\"cpDialogDisplay === 'popup'\\\" [style.left]=\\\"cpArrowPosition\\\" class=\\\"arrow arrow-{{cpUsePosition}}\\\" [style.top.px]=\\\"arrowTop\\\"></div>\\n\\n  <div *ngIf=\\\"(cpColorMode ||\\u00A01) === 1\\\" class=\\\"saturation-lightness\\\" [slider] [rgX]=\\\"1\\\" [rgY]=\\\"1\\\" [style.background-color]=\\\"hueSliderColor\\\" (newValue)=\\\"onColorChange($event)\\\" (dragStart)=\\\"onDragStart('saturation-lightness')\\\" (dragEnd)=\\\"onDragEnd('saturation-lightness')\\\">\\n    <div class=\\\"cursor\\\" [style.top.px]=\\\"slider?.v\\\" [style.left.px]=\\\"slider?.s\\\"></div>\\n  </div>\\n\\n  <div class=\\\"hue-alpha box\\\">\\n    <div class=\\\"left\\\">\\n      <div class=\\\"selected-color-background\\\"></div>\\n\\n      <div class=\\\"selected-color\\\" [style.background-color]=\\\"selectedColor\\\" [style.cursor]=\\\"eyeDropperSupported && cpEyeDropper ? 'pointer' : null\\\" (click)=\\\"eyeDropperSupported && cpEyeDropper && onEyeDropper()\\\">\\n        <svg *ngIf=\\\"eyeDropperSupported && cpEyeDropper\\\" class=\\\"eyedropper-icon\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" height=\\\"24px\\\" viewBox=\\\"0 0 24 24\\\" width=\\\"24px\\\" fill=\\\"#000000\\\"><path d=\\\"M0 0h24v24H0V0z\\\" fill=\\\"none\\\"/><path d=\\\"M17.66 5.41l.92.92-2.69 2.69-.92-.92 2.69-2.69M17.67 3c-.26 0-.51.1-.71.29l-3.12 3.12-1.93-1.91-1.41 1.41 1.42 1.42L3 16.25V21h4.75l8.92-8.92 1.42 1.42 1.41-1.41-1.92-1.92 3.12-3.12c.4-.4.4-1.03.01-1.42l-2.34-2.34c-.2-.19-.45-.29-.7-.29zM6.92 19L5 17.08l8.06-8.06 1.92 1.92L6.92 19z\\\"/></svg>\\n      </div>\\n\\n      <button *ngIf=\\\"cpAddColorButton\\\" type=\\\"button\\\" class=\\\"{{cpAddColorButtonClass}}\\\" [disabled]=\\\"cpPresetColors && cpPresetColors.length >= cpMaxPresetColorsLength\\\" (click)=\\\"onAddPresetColor($event, selectedColor)\\\">\\n        {{cpAddColorButtonText}}\\n      </button>\\n    </div>\\n\\n    <div class=\\\"right\\\">\\n      <div *ngIf=\\\"cpAlphaChannel==='disabled'\\\" style=\\\"height: 16px;\\\"></div>\\n\\n      <div #hueSlider class=\\\"hue\\\" [slider] [rgX]=\\\"1\\\" [style.display]=\\\"(cpColorMode ||\\u00A01) === 1 ? 'block' : 'none'\\\" (newValue)=\\\"onHueChange($event)\\\" (dragStart)=\\\"onDragStart('hue')\\\" (dragEnd)=\\\"onDragEnd('hue')\\\">\\n        <div class=\\\"cursor\\\" [style.left.px]=\\\"slider?.h\\\"></div>\\n      </div>\\n\\n      <div #valueSlider class=\\\"value\\\" [slider] [rgX]=\\\"1\\\" [style.display]=\\\"(cpColorMode ||\\u00A01) === 2 ? 'block': 'none'\\\" (newValue)=\\\"onValueChange($event)\\\" (dragStart)=\\\"onDragStart('value')\\\" (dragEnd)=\\\"onDragEnd('value')\\\">\\n        <div class=\\\"cursor\\\" [style.right.px]=\\\"slider?.v\\\"></div>\\n      </div>\\n\\n      <div #alphaSlider class=\\\"alpha\\\" [slider] [rgX]=\\\"1\\\" [style.display]=\\\"cpAlphaChannel === 'disabled' ? 'none' : 'block'\\\" [style.background-color]=\\\"alphaSliderColor\\\" (newValue)=\\\"onAlphaChange($event)\\\" (dragStart)=\\\"onDragStart('alpha')\\\" (dragEnd)=\\\"onDragEnd('alpha')\\\">\\n        <div class=\\\"cursor\\\" [style.left.px]=\\\"slider?.a\\\"></div>\\n      </div>\\n    </div>\\n  </div>\\n\\n  <div *ngIf=\\\"!cpDisableInput && (cpColorMode ||\\u00A01) === 1\\\" class=\\\"cmyk-text\\\" [style.display]=\\\"format !== 3 ? 'none' : 'block'\\\">\\n    <div class=\\\"box\\\">\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"100\\\" [text] [rg]=\\\"100\\\" [value]=\\\"cmykText?.c\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onCyanInput($event)\\\" />\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"100\\\" [text] [rg]=\\\"100\\\" [value]=\\\"cmykText?.m\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onMagentaInput($event)\\\" />\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"100\\\" [text] [rg]=\\\"100\\\" [value]=\\\"cmykText?.y\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onYellowInput($event)\\\" />\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"100\\\" [text] [rg]=\\\"100\\\" [value]=\\\"cmykText?.k\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onBlackInput($event)\\\" />\\n      <input *ngIf=\\\"cpAlphaChannel!=='disabled'\\\" type=\\\"number\\\" pattern=\\\"[0-9]+([\\\\.,][0-9]{1,2})?\\\" min=\\\"0\\\" max=\\\"1\\\" step=\\\"0.1\\\" [text] [rg]=\\\"1\\\" [value]=\\\"cmykText?.a\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onAlphaInput($event)\\\" />\\n    </div>\\n\\n     <div class=\\\"box\\\">\\n      <div>C</div><div>M</div><div>Y</div><div>K</div><div *ngIf=\\\"cpAlphaChannel!=='disabled'\\\" >A</div>\\n    </div>\\n  </div>\\n\\n  <div *ngIf=\\\"!cpDisableInput && (cpColorMode ||\\u00A01) === 1 \\\" class=\\\"hsla-text\\\" [style.display]=\\\"format !== 2 ? 'none' : 'block'\\\">\\n    <div class=\\\"box\\\">\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"360\\\" [text] [rg]=\\\"360\\\" [value]=\\\"hslaText?.h\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onHueInput($event)\\\" />\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"100\\\" [text] [rg]=\\\"100\\\" [value]=\\\"hslaText?.s\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onSaturationInput($event)\\\" />\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"100\\\" [text] [rg]=\\\"100\\\" [value]=\\\"hslaText?.l\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onLightnessInput($event)\\\" />\\n      <input *ngIf=\\\"cpAlphaChannel!=='disabled'\\\" type=\\\"number\\\" pattern=\\\"[0-9]+([\\\\.,][0-9]{1,2})?\\\" min=\\\"0\\\" max=\\\"1\\\" step=\\\"0.1\\\" [text] [rg]=\\\"1\\\" [value]=\\\"hslaText?.a\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onAlphaInput($event)\\\" />\\n    </div>\\n\\n    <div class=\\\"box\\\">\\n      <div>H</div><div>S</div><div>L</div><div *ngIf=\\\"cpAlphaChannel!=='disabled'\\\">A</div>\\n    </div>\\n  </div>\\n\\n  <div *ngIf=\\\"!cpDisableInput && (cpColorMode ||\\u00A01) === 1 \\\" [style.display]=\\\"format !== 1 ? 'none' : 'block'\\\" class=\\\"rgba-text\\\">\\n    <div class=\\\"box\\\">\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"255\\\" [text] [rg]=\\\"255\\\" [value]=\\\"rgbaText?.r\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onRedInput($event)\\\" />\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"255\\\" [text] [rg]=\\\"255\\\" [value]=\\\"rgbaText?.g\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onGreenInput($event)\\\" />\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"255\\\" [text] [rg]=\\\"255\\\" [value]=\\\"rgbaText?.b\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onBlueInput($event)\\\" />\\n      <input *ngIf=\\\"cpAlphaChannel!=='disabled'\\\" type=\\\"number\\\" pattern=\\\"[0-9]+([\\\\.,][0-9]{1,2})?\\\" min=\\\"0\\\" max=\\\"1\\\" step=\\\"0.1\\\" [text] [rg]=\\\"1\\\" [value]=\\\"rgbaText?.a\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onAlphaInput($event)\\\" />\\n    </div>\\n\\n    <div class=\\\"box\\\">\\n      <div>R</div><div>G</div><div>B</div><div *ngIf=\\\"cpAlphaChannel!=='disabled'\\\" >A</div>\\n    </div>\\n  </div>\\n\\n  <div *ngIf=\\\"!cpDisableInput && (cpColorMode ||\\u00A01) === 1\\\" class=\\\"hex-text\\\" [class.hex-alpha]=\\\"cpAlphaChannel==='forced'\\\"\\n    [style.display]=\\\"format !== 0 ? 'none' : 'block'\\\">\\n    <div class=\\\"box\\\">\\n      <input [text] [value]=\\\"hexText\\\" (blur)=\\\"onHexInput(null)\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onHexInput($event)\\\"/>\\n      <input *ngIf=\\\"cpAlphaChannel==='forced'\\\" type=\\\"number\\\" pattern=\\\"[0-9]+([\\\\.,][0-9]{1,2})?\\\" min=\\\"0\\\" max=\\\"1\\\" step=\\\"0.1\\\" [text] [rg]=\\\"1\\\" [value]=\\\"hexAlpha\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onAlphaInput($event)\\\"/>\\n    </div>\\n\\n    <div class=\\\"box\\\">\\n      <div>Hex</div>\\n      <div *ngIf=\\\"cpAlphaChannel==='forced'\\\">A</div>\\n    </div>\\n  </div>\\n\\n  <div *ngIf=\\\"!cpDisableInput && (cpColorMode ||\\u00A01) === 2\\\" class=\\\"value-text\\\">\\n    <div class=\\\"box\\\">\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"100\\\" [text] [rg]=\\\"100\\\" [value]=\\\"hslaText?.l\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onValueInput($event)\\\" />\\n      <input *ngIf=\\\"cpAlphaChannel!=='disabled'\\\" type=\\\"number\\\" pattern=\\\"[0-9]+([\\\\.,][0-9]{1,2})?\\\" min=\\\"0\\\" max=\\\"1\\\" step=\\\"0.1\\\"  [text] [rg]=\\\"1\\\" [value]=\\\"hslaText?.a\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onAlphaInput($event)\\\" />\\n    </div>\\n\\n    <div class=\\\"box\\\">\\n      <div>V</div><div>A</div>\\n    </div>\\n  </div>\\n\\n  <div *ngIf=\\\"!cpDisableInput && (cpColorMode ||\\u00A01) === 1\\\" class=\\\"type-policy\\\">\\n    <span class=\\\"type-policy-arrow\\\" (click)=\\\"onFormatToggle(-1)\\\"></span>\\n    <span class=\\\"type-policy-arrow\\\" (click)=\\\"onFormatToggle(1)\\\"></span>\\n  </div>\\n\\n  <div *ngIf=\\\"cpPresetColors?.length || cpAddColorButton\\\" class=\\\"preset-area\\\">\\n    <hr>\\n\\n    <div class=\\\"preset-label\\\">{{cpPresetLabel}}</div>\\n\\n    <div *ngIf=\\\"cpPresetColors?.length\\\" class=\\\"{{cpPresetColorsClass}}\\\">\\n      <div *ngFor=\\\"let color of cpPresetColors\\\" class=\\\"preset-color\\\" [style.backgroundColor]=\\\"color\\\" (click)=\\\"setColorFromString(color)\\\">\\n        <span *ngIf=\\\"cpAddColorButton\\\" class=\\\"{{cpRemoveColorButtonClass}}\\\" (click)=\\\"onRemovePresetColor($event, color)\\\"></span>\\n      </div>\\n    </div>\\n\\n    <div *ngIf=\\\"!cpPresetColors?.length && cpAddColorButton\\\" class=\\\"{{cpPresetEmptyMessageClass}}\\\">{{cpPresetEmptyMessage}}</div>\\n  </div>\\n\\n  <div *ngIf=\\\"cpOKButton || cpCancelButton\\\" class=\\\"button-area\\\">\\n    <button *ngIf=\\\"cpCancelButton\\\" type=\\\"button\\\" class=\\\"{{cpCancelButtonClass}}\\\" (click)=\\\"onCancelColor($event)\\\">{{cpCancelButtonText}}</button>\\n\\n    <button *ngIf=\\\"cpOKButton\\\" type=\\\"button\\\" class=\\\"{{cpOKButtonClass}}\\\" (click)=\\\"onAcceptColor($event)\\\">{{cpOKButtonText}}</button>\\n  </div>\\n\\n  <div class=\\\"extra-template\\\" *ngIf=\\\"cpExtraTemplate\\\">\\n    <ng-container *ngTemplateOutlet=\\\"cpExtraTemplate\\\"></ng-container>\\n  </div>\\n</div>\\n\",\n      styles: [\".color-picker{position:absolute;z-index:1000;width:230px;height:auto;border:#777 solid 1px;cursor:default;-webkit-user-select:none;user-select:none;background-color:#fff}.color-picker *{box-sizing:border-box;margin:0;font-size:11px}.color-picker input{width:0;height:26px;min-width:0;font-size:13px;text-align:center;color:#000}.color-picker input:invalid,.color-picker input:-moz-ui-invalid,.color-picker input:-moz-submit-invalid{box-shadow:none}.color-picker input::-webkit-inner-spin-button,.color-picker input::-webkit-outer-spin-button{margin:0;-webkit-appearance:none}.color-picker .arrow{position:absolute;z-index:999999;width:0;height:0;border-style:solid}.color-picker .arrow.arrow-top{left:8px;border-width:10px 5px;border-color:#777 rgba(0,0,0,0) rgba(0,0,0,0) rgba(0,0,0,0)}.color-picker .arrow.arrow-bottom{top:-20px;left:8px;border-width:10px 5px;border-color:rgba(0,0,0,0) rgba(0,0,0,0) #777 rgba(0,0,0,0)}.color-picker .arrow.arrow-top-left,.color-picker .arrow.arrow-left-top{right:-21px;bottom:8px;border-width:5px 10px;border-color:rgba(0,0,0,0) rgba(0,0,0,0) rgba(0,0,0,0) #777}.color-picker .arrow.arrow-top-right,.color-picker .arrow.arrow-right-top{bottom:8px;left:-20px;border-width:5px 10px;border-color:rgba(0,0,0,0) #777 rgba(0,0,0,0) rgba(0,0,0,0)}.color-picker .arrow.arrow-left,.color-picker .arrow.arrow-left-bottom,.color-picker .arrow.arrow-bottom-left{top:8px;right:-21px;border-width:5px 10px;border-color:rgba(0,0,0,0) rgba(0,0,0,0) rgba(0,0,0,0) #777}.color-picker .arrow.arrow-right,.color-picker .arrow.arrow-right-bottom,.color-picker .arrow.arrow-bottom-right{top:8px;left:-20px;border-width:5px 10px;border-color:rgba(0,0,0,0) #777 rgba(0,0,0,0) rgba(0,0,0,0)}.color-picker .cursor{position:relative;width:16px;height:16px;border:#222 solid 2px;border-radius:50%;cursor:default}.color-picker .box{display:flex;padding:4px 8px}.color-picker .left{position:relative;padding:16px 8px}.color-picker .right{flex:1 1 auto;padding:12px 8px}.color-picker .button-area{padding:0 16px 16px;text-align:right}.color-picker .button-area button{margin-left:8px}.color-picker .preset-area{padding:4px 15px}.color-picker .preset-area .preset-label{overflow:hidden;width:100%;padding:4px;font-size:11px;white-space:nowrap;text-align:left;text-overflow:ellipsis;color:#555}.color-picker .preset-area .preset-color{position:relative;display:inline-block;width:18px;height:18px;margin:4px 6px 8px;border:#a9a9a9 solid 1px;border-radius:25%;cursor:pointer}.color-picker .preset-area .preset-empty-message{min-height:18px;margin-top:4px;margin-bottom:8px;font-style:italic;text-align:center}.color-picker .hex-text{width:100%;padding:4px 8px;font-size:11px}.color-picker .hex-text .box{padding:0 24px 8px 8px}.color-picker .hex-text .box div{float:left;flex:1 1 auto;text-align:center;color:#555;clear:left}.color-picker .hex-text .box input{flex:1 1 auto;padding:1px;border:#a9a9a9 solid 1px}.color-picker .hex-alpha .box div:first-child,.color-picker .hex-alpha .box input:first-child{flex-grow:3;margin-right:8px}.color-picker .cmyk-text,.color-picker .hsla-text,.color-picker .rgba-text,.color-picker .value-text{width:100%;padding:4px 8px;font-size:11px}.color-picker .cmyk-text .box,.color-picker .hsla-text .box,.color-picker .rgba-text .box{padding:0 24px 8px 8px}.color-picker .value-text .box{padding:0 8px 8px}.color-picker .cmyk-text .box div,.color-picker .hsla-text .box div,.color-picker .rgba-text .box div,.color-picker .value-text .box div{flex:1 1 auto;margin-right:8px;text-align:center;color:#555}.color-picker .cmyk-text .box div:last-child,.color-picker .hsla-text .box div:last-child,.color-picker .rgba-text .box div:last-child,.color-picker .value-text .box div:last-child{margin-right:0}.color-picker .cmyk-text .box input,.color-picker .hsla-text .box input,.color-picker .rgba-text .box input,.color-picker .value-text .box input{float:left;flex:1;padding:1px;margin:0 8px 0 0;border:#a9a9a9 solid 1px}.color-picker .cmyk-text .box input:last-child,.color-picker .hsla-text .box input:last-child,.color-picker .rgba-text .box input:last-child,.color-picker .value-text .box input:last-child{margin-right:0}.color-picker .hue-alpha{align-items:center;margin-bottom:3px}.color-picker .hue{direction:ltr;width:100%;height:16px;margin-bottom:16px;border:none;cursor:pointer;background-size:100% 100%;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJYAAAAQCAYAAAD06IYnAAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH4AIWDwkUFWbCCAAAAFxJREFUaN7t0kEKg0AQAME2x83/n2qu5qCgD1iDhCoYdpnbQC9bbY1qVO/jvc6k3ad91s7/7F1/csgPrujuQ17BDYSFsBAWwgJhISyEBcJCWAgLhIWwEBYIi2f7Ar/1TCgFH2X9AAAAAElFTkSuQmCC)}.color-picker .value{direction:rtl;width:100%;height:16px;margin-bottom:16px;border:none;cursor:pointer;background-size:100% 100%;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJYAAAAQCAYAAAD06IYnAAACTklEQVR42u3SYUcrABhA4U2SkmRJMmWSJklKJiWZZpKUJJskKUmaTFImKZOUzMySpGRmliRNJilJSpKSJEtmSpIpmWmSdO736/6D+x7OP3gUCoWCv1cqlSQlJZGcnExKSgqpqamkpaWRnp5ORkYGmZmZqFQqsrKyyM7OJicnh9zcXNRqNXl5eeTn56PRaCgoKKCwsJCioiK0Wi3FxcWUlJRQWlpKWVkZ5eXlVFRUUFlZiU6no6qqiurqampqaqitraWurg69Xk99fT0GgwGj0UhDQwONjY00NTXR3NxMS0sLra2ttLW10d7ejslkwmw209HRQWdnJ11dXXR3d9PT00Nvby99fX309/czMDDA4OAgFouFoaEhrFYrw8PDjIyMMDo6ytjYGDabjfHxcSYmJpicnGRqagq73c709DQzMzPMzs4yNzfH/Pw8DocDp9OJy+XC7XazsLDA4uIiS0tLLC8vs7KywurqKmtra3g8HrxeLz6fD7/fz/r6OhsbG2xubrK1tcX29jaBQICdnR2CwSC7u7vs7e2xv7/PwcEBh4eHHB0dcXx8zMnJCaenp5ydnXF+fs7FxQWXl5dcXV1xfX3Nzc0Nt7e33N3dEQqFuL+/5+HhgXA4TCQS4fHxkaenJ56fn3l5eeH19ZVoNMrb2xvv7+98fHwQi8WIx+N8fn6SSCT4+vri+/ubn58ffn9/+VcKgSWwBJbAElgCS2AJLIElsASWwBJYAktgCSyBJbAElsASWAJLYAksgSWwBJbAElgCS2AJLIElsP4/WH8AmJ5Z6jHS4h8AAAAASUVORK5CYII=)}.color-picker .alpha{direction:ltr;width:100%;height:16px;border:none;cursor:pointer;background-size:100% 100%;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJYAAAAQCAYAAAD06IYnAAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH4AIWDwYQlZMa3gAAAWVJREFUaN7tmEGO6jAQRCsOArHgBpyAJYGjcGocxAm4A2IHpmoWE0eBH+ezmFlNvU06shJ3W6VEelWMUQAIIF9f6qZpimsA1LYtS2uF51/u27YVAFZVRUkEoGHdPV/sIcbIEIIkUdI/9Xa7neyv61+SWFUVAVCSct00TWn2fv6u3+Ecfd3tXzy/0+nEUu+SPjo/kqzrmiQpScN6v98XewfA8/lMkiLJ2WxGSUopcT6fM6U0NX9/frfbjev1WtfrlZfLhYfDQQHG/AIOlnGwjINlHCxjHCzjYJm/TJWdCwquJXseFFzGwDNNeiKMOJTO8xQdDQaeB29+K9efeLaBo9J7vdvtJj1RjFFjfiv7qv95tjx/7leSQgh93e1ffMeIp6O+YQjho/N791t1XVOSSI7N//K+4/GoxWLBx+PB5/Op5XLJ+/3OlJJWqxU3m83ovv5iGf8KjYNlHCxjHCzjYBkHy5gf5gusvQU7U37jTAAAAABJRU5ErkJggg==)}.color-picker .type-policy{position:absolute;top:218px;right:12px;width:16px;height:24px;background-size:8px 16px;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAgCAYAAAAffCjxAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAACewAAAnsB01CO3AAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAIASURBVEiJ7ZY9axRRFIafsxMStrLQJpAgpBFhi+C9w1YSo00I6RZ/g9vZpBf/QOr4GyRgkSKNSrAadsZqQGwCkuAWyRZJsySwvhZ7N/vhzrgbLH3Ld8597jlzz50zJokyxXH8DqDVar0qi6v8BbItqSGpEcfxdlmsFWXkvX8AfAVWg3UKPEnT9GKujMzsAFgZsVaCN1VTQd77XUnrgE1kv+6935268WRpzrnHZvYRWC7YvC3pRZZl3wozqtVqiyH9IgjAspkd1Gq1xUJQtVrdB9ZKIAOthdg/Qc65LUk7wNIMoCVJO865rYFhkqjX6/d7vV4GPJwBMqofURS5JEk6FYBer/eeYb/Mo9WwFnPOvQbeAvfuAAK4BN4sAJtAG/gJIElmNuiJyba3EGNmZiPeZuEVmVell/Y/6N+CzDn3AXhEOOo7Hv/3BeAz8IzQkMPnJbuPx1wC+yYJ7/0nYIP5S/0FHKdp+rwCEEXRS/rf5Hl1Gtb2M0iSpCOpCZzPATmX1EySpHMLAsiy7MjMDoHrGSDXZnaYZdnRwBh7J91utwmczAA6CbG3GgPleX4jqUH/a1CktqRGnuc3hSCAMB32gKspkCtgb3KCQMmkjeP4WNJThrNNZval1WptTIsv7JtQ4tmIdRa8qSoEpWl6YWZNoAN0zKxZNPehpLSBZv2t+Q0CJ9lLnARQLAAAAABJRU5ErkJggg==);background-repeat:no-repeat;background-position:center}.color-picker .type-policy .type-policy-arrow{display:block;width:100%;height:50%}.color-picker .selected-color{position:absolute;top:16px;left:8px;width:40px;height:40px;border:1px solid #a9a9a9;border-radius:50%}.color-picker .selected-color-background{width:40px;height:40px;border-radius:50%;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAh0lEQVRYR+2W0QlAMQgD60zdfwOdqa8TmI/wQMr5K0I5bZLIzLOa2nt37VVVbd+dDx5obgCC3KBLwJ2ff4PnVidkf+ucIhw80HQaCLo3DMH3CRK3iFsmAWVl6hPNDwt8EvNE5q+YuEXcMgkonVM6SdyCoEvAnZ8v1Hjx817MilmxSUB5rdLJDycZgUAZUch/AAAAAElFTkSuQmCC)}.color-picker .saturation-lightness{direction:ltr;width:100%;height:130px;border:none;cursor:pointer;touch-action:manipulation;background-size:100% 100%;background-image:url(data:image/png;base64,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)}.color-picker .cp-add-color-button-class{position:absolute;display:inline;padding:0;margin:3px -3px;border:0;cursor:pointer;background:transparent}.color-picker .cp-add-color-button-class:hover{text-decoration:underline}.color-picker .cp-add-color-button-class:disabled{cursor:not-allowed;color:#999}.color-picker .cp-add-color-button-class:disabled:hover{text-decoration:none}.color-picker .cp-remove-color-button-class{position:absolute;top:-5px;right:-5px;display:block;width:10px;height:10px;border-radius:50%;cursor:pointer;text-align:center;background:#fff;box-shadow:1px 1px 5px #333}.color-picker .cp-remove-color-button-class:before{content:\\\"x\\\";position:relative;bottom:3.5px;display:inline-block;font-size:10px}.color-picker .eyedropper-icon{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);fill:#fff;mix-blend-mode:exclusion}\\n\"]\n    }]\n  }], () => [{\n    type: i0.NgZone\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: Document,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }, {\n    type: ColorPickerService\n  }], {\n    dialogElement: [{\n      type: ViewChild,\n      args: ['dialogPopup', {\n        static: true\n      }]\n    }],\n    hueSlider: [{\n      type: ViewChild,\n      args: ['hueSlider', {\n        static: true\n      }]\n    }],\n    alphaSlider: [{\n      type: ViewChild,\n      args: ['alphaSlider', {\n        static: true\n      }]\n    }],\n    handleEsc: [{\n      type: HostListener,\n      args: ['document:keyup.esc', ['$event']]\n    }],\n    handleEnter: [{\n      type: HostListener,\n      args: ['document:keyup.enter', ['$event']]\n    }]\n  });\n})();\n\n// Caretaker note: we have still left the `typeof` condition in order to avoid\n// creating a breaking change for projects that still use the View Engine.\n// The `ngDevMode` is always available when Ivy is enabled.\n// This will be evaluated during compilation into `const NG_DEV_MODE = false`,\n// thus Terser will be able to tree-shake `console.warn` calls.\nconst NG_DEV_MODE = typeof ngDevMode === 'undefined' || !!ngDevMode;\nclass ColorPickerDirective {\n  injector;\n  cfr;\n  appRef;\n  vcRef;\n  elRef;\n  _service;\n  dialog;\n  dialogCreated = false;\n  ignoreChanges = false;\n  cmpRef;\n  viewAttachedToAppRef = false;\n  colorPicker;\n  cpWidth = '230px';\n  cpHeight = 'auto';\n  cpToggle = false;\n  cpDisabled = false;\n  cpIgnoredElements = [];\n  cpFallbackColor = '';\n  cpColorMode = 'color';\n  cpCmykEnabled = false;\n  cpOutputFormat = 'auto';\n  cpAlphaChannel = 'enabled';\n  cpDisableInput = false;\n  cpDialogDisplay = 'popup';\n  cpSaveClickOutside = true;\n  cpCloseClickOutside = true;\n  cpUseRootViewContainer = false;\n  cpPosition = 'auto';\n  cpPositionOffset = '0%';\n  cpPositionRelativeToArrow = false;\n  cpOKButton = false;\n  cpOKButtonText = 'OK';\n  cpOKButtonClass = 'cp-ok-button-class';\n  cpCancelButton = false;\n  cpCancelButtonText = 'Cancel';\n  cpCancelButtonClass = 'cp-cancel-button-class';\n  cpEyeDropper = false;\n  cpPresetLabel = 'Preset colors';\n  cpPresetColors;\n  cpPresetColorsClass = 'cp-preset-colors-class';\n  cpMaxPresetColorsLength = 6;\n  cpPresetEmptyMessage = 'No colors added';\n  cpPresetEmptyMessageClass = 'preset-empty-message';\n  cpAddColorButton = false;\n  cpAddColorButtonText = 'Add color';\n  cpAddColorButtonClass = 'cp-add-color-button-class';\n  cpRemoveColorButtonClass = 'cp-remove-color-button-class';\n  cpArrowPosition = 0;\n  cpExtraTemplate;\n  cpInputChange = new EventEmitter(true);\n  cpToggleChange = new EventEmitter(true);\n  cpSliderChange = new EventEmitter(true);\n  cpSliderDragEnd = new EventEmitter(true);\n  cpSliderDragStart = new EventEmitter(true);\n  colorPickerOpen = new EventEmitter(true);\n  colorPickerClose = new EventEmitter(true);\n  colorPickerCancel = new EventEmitter(true);\n  colorPickerSelect = new EventEmitter(true);\n  colorPickerChange = new EventEmitter(false);\n  cpCmykColorChange = new EventEmitter(true);\n  cpPresetColorsChange = new EventEmitter(true);\n  handleClick() {\n    this.inputFocus();\n  }\n  handleFocus() {\n    this.inputFocus();\n  }\n  handleInput(event) {\n    this.inputChange(event);\n  }\n  constructor(injector, cfr, appRef, vcRef, elRef, _service) {\n    this.injector = injector;\n    this.cfr = cfr;\n    this.appRef = appRef;\n    this.vcRef = vcRef;\n    this.elRef = elRef;\n    this._service = _service;\n  }\n  ngOnDestroy() {\n    if (this.cmpRef != null) {\n      if (this.viewAttachedToAppRef) {\n        this.appRef.detachView(this.cmpRef.hostView);\n      }\n      this.cmpRef.destroy();\n      this.cmpRef = null;\n      this.dialog = null;\n    }\n  }\n  ngOnChanges(changes) {\n    if (changes.cpToggle && !this.cpDisabled) {\n      if (changes.cpToggle.currentValue) {\n        this.openDialog();\n      } else if (!changes.cpToggle.currentValue) {\n        this.closeDialog();\n      }\n    }\n    if (changes.colorPicker) {\n      if (this.dialog && !this.ignoreChanges) {\n        if (this.cpDialogDisplay === 'inline') {\n          this.dialog.setInitialColor(changes.colorPicker.currentValue);\n        }\n        this.dialog.setColorFromString(changes.colorPicker.currentValue, false);\n        if (this.cpUseRootViewContainer && this.cpDialogDisplay !== 'inline') {\n          this.cmpRef.changeDetectorRef.detectChanges();\n        }\n      }\n      this.ignoreChanges = false;\n    }\n    if (changes.cpPresetLabel || changes.cpPresetColors) {\n      if (this.dialog) {\n        this.dialog.setPresetConfig(this.cpPresetLabel, this.cpPresetColors);\n      }\n    }\n  }\n  openDialog() {\n    if (!this.dialogCreated) {\n      let vcRef = this.vcRef;\n      this.dialogCreated = true;\n      this.viewAttachedToAppRef = false;\n      if (this.cpUseRootViewContainer && this.cpDialogDisplay !== 'inline') {\n        const classOfRootComponent = this.appRef.componentTypes[0];\n        const appInstance = this.injector.get(classOfRootComponent, Injector.NULL);\n        if (appInstance !== Injector.NULL) {\n          vcRef = appInstance.vcRef || appInstance.viewContainerRef || this.vcRef;\n          if (NG_DEV_MODE && vcRef === this.vcRef) {\n            console.warn('You are using cpUseRootViewContainer, ' + 'but the root component is not exposing viewContainerRef!' + 'Please expose it by adding \\'public vcRef: ViewContainerRef\\' to the constructor.');\n          }\n        } else {\n          this.viewAttachedToAppRef = true;\n        }\n      }\n      const compFactory = this.cfr.resolveComponentFactory(ColorPickerComponent);\n      if (this.viewAttachedToAppRef) {\n        this.cmpRef = compFactory.create(this.injector);\n        this.appRef.attachView(this.cmpRef.hostView);\n        document.body.appendChild(this.cmpRef.hostView.rootNodes[0]);\n      } else {\n        const injector = Injector.create({\n          providers: [],\n          // We shouldn't use `vcRef.parentInjector` since it's been deprecated long time ago and might be removed\n          // in newer Angular versions: https://github.com/angular/angular/pull/25174.\n          parent: vcRef.injector\n        });\n        this.cmpRef = vcRef.createComponent(compFactory, 0, injector, []);\n      }\n      this.cmpRef.instance.setupDialog(this, this.elRef, this.colorPicker, this.cpWidth, this.cpHeight, this.cpDialogDisplay, this.cpFallbackColor, this.cpColorMode, this.cpCmykEnabled, this.cpAlphaChannel, this.cpOutputFormat, this.cpDisableInput, this.cpIgnoredElements, this.cpSaveClickOutside, this.cpCloseClickOutside, this.cpUseRootViewContainer, this.cpPosition, this.cpPositionOffset, this.cpPositionRelativeToArrow, this.cpPresetLabel, this.cpPresetColors, this.cpPresetColorsClass, this.cpMaxPresetColorsLength, this.cpPresetEmptyMessage, this.cpPresetEmptyMessageClass, this.cpOKButton, this.cpOKButtonClass, this.cpOKButtonText, this.cpCancelButton, this.cpCancelButtonClass, this.cpCancelButtonText, this.cpAddColorButton, this.cpAddColorButtonClass, this.cpAddColorButtonText, this.cpRemoveColorButtonClass, this.cpEyeDropper, this.elRef, this.cpExtraTemplate);\n      this.dialog = this.cmpRef.instance;\n      if (this.vcRef !== vcRef) {\n        this.cmpRef.changeDetectorRef.detectChanges();\n      }\n    } else if (this.dialog) {\n      this.dialog.openDialog(this.colorPicker);\n    }\n  }\n  closeDialog() {\n    if (this.dialog && this.cpDialogDisplay === 'popup') {\n      this.dialog.closeDialog();\n    }\n  }\n  cmykChanged(value) {\n    this.cpCmykColorChange.emit(value);\n  }\n  stateChanged(state) {\n    this.cpToggleChange.emit(state);\n    if (state) {\n      this.colorPickerOpen.emit(this.colorPicker);\n    } else {\n      this.colorPickerClose.emit(this.colorPicker);\n    }\n  }\n  colorChanged(value, ignore = true) {\n    this.ignoreChanges = ignore;\n    this.colorPickerChange.emit(value);\n  }\n  colorSelected(value) {\n    this.colorPickerSelect.emit(value);\n  }\n  colorCanceled() {\n    this.colorPickerCancel.emit();\n  }\n  inputFocus() {\n    const element = this.elRef.nativeElement;\n    const ignored = this.cpIgnoredElements.filter(item => item === element);\n    if (!this.cpDisabled && !ignored.length) {\n      if (typeof document !== 'undefined' && element === document.activeElement) {\n        this.openDialog();\n      } else if (!this.dialog || !this.dialog.show) {\n        this.openDialog();\n      } else {\n        this.closeDialog();\n      }\n    }\n  }\n  inputChange(event) {\n    if (this.dialog) {\n      this.dialog.setColorFromString(event.target.value, true);\n    } else {\n      this.colorPicker = event.target.value;\n      this.colorPickerChange.emit(this.colorPicker);\n    }\n  }\n  inputChanged(event) {\n    this.cpInputChange.emit(event);\n  }\n  sliderChanged(event) {\n    this.cpSliderChange.emit(event);\n  }\n  sliderDragEnd(event) {\n    this.cpSliderDragEnd.emit(event);\n  }\n  sliderDragStart(event) {\n    this.cpSliderDragStart.emit(event);\n  }\n  presetColorsChanged(value) {\n    this.cpPresetColorsChange.emit(value);\n  }\n  static ɵfac = function ColorPickerDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ColorPickerDirective)(i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i0.ComponentFactoryResolver), i0.ɵɵdirectiveInject(i0.ApplicationRef), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(ColorPickerService));\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: ColorPickerDirective,\n    selectors: [[\"\", \"colorPicker\", \"\"]],\n    hostBindings: function ColorPickerDirective_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function ColorPickerDirective_click_HostBindingHandler() {\n          return ctx.handleClick();\n        })(\"focus\", function ColorPickerDirective_focus_HostBindingHandler() {\n          return ctx.handleFocus();\n        })(\"input\", function ColorPickerDirective_input_HostBindingHandler($event) {\n          return ctx.handleInput($event);\n        });\n      }\n    },\n    inputs: {\n      colorPicker: \"colorPicker\",\n      cpWidth: \"cpWidth\",\n      cpHeight: \"cpHeight\",\n      cpToggle: \"cpToggle\",\n      cpDisabled: \"cpDisabled\",\n      cpIgnoredElements: \"cpIgnoredElements\",\n      cpFallbackColor: \"cpFallbackColor\",\n      cpColorMode: \"cpColorMode\",\n      cpCmykEnabled: \"cpCmykEnabled\",\n      cpOutputFormat: \"cpOutputFormat\",\n      cpAlphaChannel: \"cpAlphaChannel\",\n      cpDisableInput: \"cpDisableInput\",\n      cpDialogDisplay: \"cpDialogDisplay\",\n      cpSaveClickOutside: \"cpSaveClickOutside\",\n      cpCloseClickOutside: \"cpCloseClickOutside\",\n      cpUseRootViewContainer: \"cpUseRootViewContainer\",\n      cpPosition: \"cpPosition\",\n      cpPositionOffset: \"cpPositionOffset\",\n      cpPositionRelativeToArrow: \"cpPositionRelativeToArrow\",\n      cpOKButton: \"cpOKButton\",\n      cpOKButtonText: \"cpOKButtonText\",\n      cpOKButtonClass: \"cpOKButtonClass\",\n      cpCancelButton: \"cpCancelButton\",\n      cpCancelButtonText: \"cpCancelButtonText\",\n      cpCancelButtonClass: \"cpCancelButtonClass\",\n      cpEyeDropper: \"cpEyeDropper\",\n      cpPresetLabel: \"cpPresetLabel\",\n      cpPresetColors: \"cpPresetColors\",\n      cpPresetColorsClass: \"cpPresetColorsClass\",\n      cpMaxPresetColorsLength: \"cpMaxPresetColorsLength\",\n      cpPresetEmptyMessage: \"cpPresetEmptyMessage\",\n      cpPresetEmptyMessageClass: \"cpPresetEmptyMessageClass\",\n      cpAddColorButton: \"cpAddColorButton\",\n      cpAddColorButtonText: \"cpAddColorButtonText\",\n      cpAddColorButtonClass: \"cpAddColorButtonClass\",\n      cpRemoveColorButtonClass: \"cpRemoveColorButtonClass\",\n      cpArrowPosition: \"cpArrowPosition\",\n      cpExtraTemplate: \"cpExtraTemplate\"\n    },\n    outputs: {\n      cpInputChange: \"cpInputChange\",\n      cpToggleChange: \"cpToggleChange\",\n      cpSliderChange: \"cpSliderChange\",\n      cpSliderDragEnd: \"cpSliderDragEnd\",\n      cpSliderDragStart: \"cpSliderDragStart\",\n      colorPickerOpen: \"colorPickerOpen\",\n      colorPickerClose: \"colorPickerClose\",\n      colorPickerCancel: \"colorPickerCancel\",\n      colorPickerSelect: \"colorPickerSelect\",\n      colorPickerChange: \"colorPickerChange\",\n      cpCmykColorChange: \"cpCmykColorChange\",\n      cpPresetColorsChange: \"cpPresetColorsChange\"\n    },\n    exportAs: [\"ngxColorPicker\"],\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ColorPickerDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[colorPicker]',\n      exportAs: 'ngxColorPicker'\n    }]\n  }], () => [{\n    type: i0.Injector\n  }, {\n    type: i0.ComponentFactoryResolver\n  }, {\n    type: i0.ApplicationRef\n  }, {\n    type: i0.ViewContainerRef\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: ColorPickerService\n  }], {\n    colorPicker: [{\n      type: Input\n    }],\n    cpWidth: [{\n      type: Input\n    }],\n    cpHeight: [{\n      type: Input\n    }],\n    cpToggle: [{\n      type: Input\n    }],\n    cpDisabled: [{\n      type: Input\n    }],\n    cpIgnoredElements: [{\n      type: Input\n    }],\n    cpFallbackColor: [{\n      type: Input\n    }],\n    cpColorMode: [{\n      type: Input\n    }],\n    cpCmykEnabled: [{\n      type: Input\n    }],\n    cpOutputFormat: [{\n      type: Input\n    }],\n    cpAlphaChannel: [{\n      type: Input\n    }],\n    cpDisableInput: [{\n      type: Input\n    }],\n    cpDialogDisplay: [{\n      type: Input\n    }],\n    cpSaveClickOutside: [{\n      type: Input\n    }],\n    cpCloseClickOutside: [{\n      type: Input\n    }],\n    cpUseRootViewContainer: [{\n      type: Input\n    }],\n    cpPosition: [{\n      type: Input\n    }],\n    cpPositionOffset: [{\n      type: Input\n    }],\n    cpPositionRelativeToArrow: [{\n      type: Input\n    }],\n    cpOKButton: [{\n      type: Input\n    }],\n    cpOKButtonText: [{\n      type: Input\n    }],\n    cpOKButtonClass: [{\n      type: Input\n    }],\n    cpCancelButton: [{\n      type: Input\n    }],\n    cpCancelButtonText: [{\n      type: Input\n    }],\n    cpCancelButtonClass: [{\n      type: Input\n    }],\n    cpEyeDropper: [{\n      type: Input\n    }],\n    cpPresetLabel: [{\n      type: Input\n    }],\n    cpPresetColors: [{\n      type: Input\n    }],\n    cpPresetColorsClass: [{\n      type: Input\n    }],\n    cpMaxPresetColorsLength: [{\n      type: Input\n    }],\n    cpPresetEmptyMessage: [{\n      type: Input\n    }],\n    cpPresetEmptyMessageClass: [{\n      type: Input\n    }],\n    cpAddColorButton: [{\n      type: Input\n    }],\n    cpAddColorButtonText: [{\n      type: Input\n    }],\n    cpAddColorButtonClass: [{\n      type: Input\n    }],\n    cpRemoveColorButtonClass: [{\n      type: Input\n    }],\n    cpArrowPosition: [{\n      type: Input\n    }],\n    cpExtraTemplate: [{\n      type: Input\n    }],\n    cpInputChange: [{\n      type: Output\n    }],\n    cpToggleChange: [{\n      type: Output\n    }],\n    cpSliderChange: [{\n      type: Output\n    }],\n    cpSliderDragEnd: [{\n      type: Output\n    }],\n    cpSliderDragStart: [{\n      type: Output\n    }],\n    colorPickerOpen: [{\n      type: Output\n    }],\n    colorPickerClose: [{\n      type: Output\n    }],\n    colorPickerCancel: [{\n      type: Output\n    }],\n    colorPickerSelect: [{\n      type: Output\n    }],\n    colorPickerChange: [{\n      type: Output\n    }],\n    cpCmykColorChange: [{\n      type: Output\n    }],\n    cpPresetColorsChange: [{\n      type: Output\n    }],\n    handleClick: [{\n      type: HostListener,\n      args: ['click']\n    }],\n    handleFocus: [{\n      type: HostListener,\n      args: ['focus']\n    }],\n    handleInput: [{\n      type: HostListener,\n      args: ['input', ['$event']]\n    }]\n  });\n})();\nclass ColorPickerModule {\n  static ɵfac = function ColorPickerModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ColorPickerModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: ColorPickerModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [ColorPickerService],\n    imports: [CommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ColorPickerModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      exports: [ColorPickerDirective],\n      providers: [ColorPickerService],\n      declarations: [ColorPickerComponent, ColorPickerDirective, TextDirective, SliderDirective]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Cmyk, ColorPickerComponent, ColorPickerDirective, ColorPickerModule, ColorPickerService, Hsla, Hsva, Rgba, SliderDirective, TextDirective };", "map": {"version": 3, "names": ["i0", "EventEmitter", "Directive", "Input", "Output", "HostListener", "Injectable", "PLATFORM_ID", "Component", "ViewEncapsulation", "Inject", "ViewChild", "Injector", "NgModule", "i2", "isPlatformBrowser", "DOCUMENT", "CommonModule", "_c0", "_c1", "_c2", "ColorPickerComponent_div_2_Template", "rf", "ctx", "ɵɵelement", "ctx_r1", "ɵɵnextContext", "ɵɵclassMapInterpolate1", "cpUsePosition", "ɵɵstyleProp", "cpArrowPosition", "arrowTop", "ColorPickerComponent_div_3_Template", "_r3", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "ColorPickerComponent_div_3_Template_div_newValue_0_listener", "$event", "ɵɵrestoreView", "ɵɵresetView", "onColorChange", "ColorPickerComponent_div_3_Template_div_dragStart_0_listener", "onDragStart", "ColorPickerComponent_div_3_Template_div_dragEnd_0_listener", "onDragEnd", "ɵɵelementEnd", "hueSliderColor", "ɵɵproperty", "ɵɵadvance", "slider", "v", "s", "ColorPickerComponent__svg_svg_8_Template", "ɵɵnamespaceSVG", "ColorPickerComponent_button_9_Template", "_r4", "ColorPickerComponent_button_9_Template_button_click_0_listener", "onAddPresetColor", "selectedColor", "ɵɵtext", "ɵɵclassMap", "cpAddColorButtonClass", "cpPresetColors", "length", "cpMaxPresetColorsLength", "ɵɵtextInterpolate1", "cpAddColorButtonText", "ColorPickerComponent_div_11_Template", "ColorPickerComponent_div_21_input_6_Template", "_r6", "ColorPickerComponent_div_21_input_6_Template_input_keyup_enter_0_listener", "onAcceptColor", "ColorPickerComponent_div_21_input_6_Template_input_newValue_0_listener", "onAlphaInput", "cmykText", "a", "ColorPickerComponent_div_21_div_16_Template", "ColorPickerComponent_div_21_Template", "_r5", "ColorPickerComponent_div_21_Template_input_keyup_enter_2_listener", "ColorPickerComponent_div_21_Template_input_newValue_2_listener", "onCyanInput", "ColorPickerComponent_div_21_Template_input_keyup_enter_3_listener", "ColorPickerComponent_div_21_Template_input_newValue_3_listener", "onMagentaInput", "ColorPickerComponent_div_21_Template_input_keyup_enter_4_listener", "ColorPickerComponent_div_21_Template_input_newValue_4_listener", "onYellowInput", "ColorPickerComponent_div_21_Template_input_keyup_enter_5_listener", "ColorPickerComponent_div_21_Template_input_newValue_5_listener", "onBlackInput", "ɵɵtemplate", "format", "c", "m", "y", "k", "cpAlphaChannel", "ColorPickerComponent_div_22_input_5_Template", "_r8", "ColorPickerComponent_div_22_input_5_Template_input_keyup_enter_0_listener", "ColorPickerComponent_div_22_input_5_Template_input_newValue_0_listener", "hslaText", "ColorPickerComponent_div_22_div_13_Template", "ColorPickerComponent_div_22_Template", "_r7", "ColorPickerComponent_div_22_Template_input_keyup_enter_2_listener", "ColorPickerComponent_div_22_Template_input_newValue_2_listener", "onHueInput", "ColorPickerComponent_div_22_Template_input_keyup_enter_3_listener", "ColorPickerComponent_div_22_Template_input_newValue_3_listener", "onSaturationInput", "ColorPickerComponent_div_22_Template_input_keyup_enter_4_listener", "ColorPickerComponent_div_22_Template_input_newValue_4_listener", "onLightnessInput", "h", "l", "ColorPickerComponent_div_23_input_5_Template", "_r10", "ColorPickerComponent_div_23_input_5_Template_input_keyup_enter_0_listener", "ColorPickerComponent_div_23_input_5_Template_input_newValue_0_listener", "rgbaText", "ColorPickerComponent_div_23_div_13_Template", "ColorPickerComponent_div_23_Template", "_r9", "ColorPickerComponent_div_23_Template_input_keyup_enter_2_listener", "ColorPickerComponent_div_23_Template_input_newValue_2_listener", "onRedInput", "ColorPickerComponent_div_23_Template_input_keyup_enter_3_listener", "ColorPickerComponent_div_23_Template_input_newValue_3_listener", "onGreenInput", "ColorPickerComponent_div_23_Template_input_keyup_enter_4_listener", "ColorPickerComponent_div_23_Template_input_newValue_4_listener", "onBlueInput", "r", "g", "b", "ColorPickerComponent_div_24_input_3_Template", "_r12", "ColorPickerComponent_div_24_input_3_Template_input_keyup_enter_0_listener", "ColorPickerComponent_div_24_input_3_Template_input_newValue_0_listener", "hexAlpha", "ColorPickerComponent_div_24_div_7_Template", "ColorPickerComponent_div_24_Template", "_r11", "ColorPickerComponent_div_24_Template_input_blur_2_listener", "onHexInput", "ColorPickerComponent_div_24_Template_input_keyup_enter_2_listener", "ColorPickerComponent_div_24_Template_input_newValue_2_listener", "ɵɵclassProp", "hexText", "ColorPickerComponent_div_25_input_3_Template", "_r14", "ColorPickerComponent_div_25_input_3_Template_input_keyup_enter_0_listener", "ColorPickerComponent_div_25_input_3_Template_input_newValue_0_listener", "ColorPickerComponent_div_25_Template", "_r13", "ColorPickerComponent_div_25_Template_input_keyup_enter_2_listener", "ColorPickerComponent_div_25_Template_input_newValue_2_listener", "onValueInput", "ColorPickerComponent_div_26_Template", "_r15", "ColorPickerComponent_div_26_Template_span_click_1_listener", "onFormatToggle", "ColorPickerComponent_div_26_Template_span_click_2_listener", "ColorPickerComponent_div_27_div_4_div_1_span_1_Template", "_r18", "ColorPickerComponent_div_27_div_4_div_1_span_1_Template_span_click_0_listener", "color_r17", "$implicit", "onRemovePresetColor", "cpRemoveColorButtonClass", "ColorPickerComponent_div_27_div_4_div_1_Template", "_r16", "ColorPickerComponent_div_27_div_4_div_1_Template_div_click_0_listener", "setColorFromString", "cpAddColorButton", "ColorPickerComponent_div_27_div_4_Template", "cpPresetColorsClass", "ColorPickerComponent_div_27_div_5_Template", "cpPresetEmptyMessageClass", "ɵɵtextInterpolate", "cpPresetEmptyMessage", "ColorPickerComponent_div_27_Template", "cpPresetLabel", "ColorPickerComponent_div_28_button_1_Template", "_r19", "ColorPickerComponent_div_28_button_1_Template_button_click_0_listener", "onCancelColor", "cpCancelButtonClass", "cpCancelButtonText", "ColorPickerComponent_div_28_button_2_Template", "_r20", "ColorPickerComponent_div_28_button_2_Template_button_click_0_listener", "cpOKButtonClass", "cpOKButtonText", "ColorPickerComponent_div_28_Template", "cpCancelButton", "cpOK<PERSON><PERSON>on", "ColorPickerComponent_div_29_ng_container_1_Template", "ɵɵelementContainer", "ColorPickerComponent_div_29_Template", "cpExtraTemplate", "ColorFormats", "Rgba", "constructor", "Hsva", "Hsla", "Cmyk", "calculateAutoPositioning", "elBounds", "triggerElBounds", "usePositionX", "usePositionY", "height", "width", "top", "left", "bottom", "right", "collisionTop", "collisionBottom", "window", "innerHeight", "document", "documentElement", "clientHeight", "collisionLeft", "collisionRight", "innerWidth", "clientWidth", "collisionAll", "postions", "reduce", "prev", "next", "detectIE", "ua", "navigator", "userAgent", "toLowerCase", "msie", "indexOf", "parseInt", "substring", "TextDirective", "rg", "text", "newValue", "inputChange", "event", "value", "target", "undefined", "emit", "numeric", "parseFloat", "ɵfac", "TextDirective_Factory", "__ngFactoryType__", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "hostBindings", "TextDirective_HostBindings", "TextDirective_input_HostBindingHandler", "inputs", "outputs", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "SliderDirective", "elRef", "listenerMove", "listenerStop", "rgX", "rgY", "dragEnd", "dragStart", "mouseDown", "start", "touchStart", "move", "stop", "preventDefault", "setCursor", "stopPropagation", "addEventListener", "removeEventListener", "getX", "position", "nativeElement", "getBoundingClientRect", "pageX", "touches", "pageXOffset", "getY", "pageY", "pageYOffset", "offsetWidth", "offsetHeight", "x", "Math", "max", "min", "SliderDirective_Factory", "ɵɵdirectiveInject", "ElementRef", "SliderDirective_HostBindings", "SliderDirective_mousedown_HostBindingHandler", "SliderDirective_touchstart_HostBindingHandler", "SliderPosition", "SliderDimension", "ColorPickerService", "active", "setActive", "cpDialogDisplay", "closeDialog", "hsva2hsla", "hsva", "abs", "hsla2hsva", "hsla", "hsvaToRgba", "i", "floor", "f", "p", "q", "t", "cmykToRgb", "cmyk", "rgbaToCmyk", "rgba", "rgbaToHsva", "d", "rgbaToHex", "allowHex8", "hex", "toString", "substr", "round", "normalizeCMYK", "denormalizeCMYK", "denormalizeRGBA", "stringToHsva", "colorString", "stringParsers", "re", "parse", "execResult", "isNaN", "push", "key", "hasOwnProperty", "parser", "match", "exec", "color", "outputFormat", "alphaChannel", "ColorPickerService_Factory", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "SUPPORTS_TOUCH", "ColorPickerComponent", "ngZone", "cdRef", "platformId", "service", "isIE10", "cmykColor", "outputColor", "initialColor", "fallbackColor", "listenerResize", "listenerMouseDown", "directiveInstance", "sliderH", "sliderDimMax", "directiveElementRef", "dialogArrowSize", "dialogArrowOffset", "dialogInputFields", "HEX", "RGBA", "HSLA", "CMYK", "useRootViewContainer", "show", "hidden", "alphaSliderColor", "cp<PERSON>idth", "cpHeight", "cpColorMode", "cpCmykEnabled", "cpOutputFormat", "cpDisableInput", "cpIgnoredElements", "cpSaveClickOutside", "cpCloseClickOutside", "cpPosition", "cpPositionOffset", "cpEyeDropper", "eyeDropperSupported", "cpTriggerElement", "dialogElement", "hue<PERSON><PERSON><PERSON>", "alphaSlider", "handleEsc", "handleEnter", "defaultView", "ngOnInit", "hue<PERSON><PERSON><PERSON>", "alphaWidth", "onMouseDown", "onResize", "openDialog", "ngOnDestroy", "ngAfterViewInit", "updateColorPicker", "detectChanges", "setInitialColor", "openColorPicker", "closeColorPicker", "setupDialog", "instance", "elementRef", "cpFallbackColor", "cpUseRootViewContainer", "cpPositionRelativeToArrow", "setColorMode", "setPresetConfig", "mode", "toUpperCase", "update", "setDialogPosition", "sliderDragEnd", "sliderDragStart", "isDescendant", "filter", "item", "run", "colorSelected", "cmykChanged", "colorChanged", "colorCanceled", "onEyeDropper", "eyeDropper", "EyeDropper", "open", "then", "eyeDropperResult", "sRGBHex", "change", "availableFormats", "nextFormat", "slider<PERSON><PERSON>ed", "onHueChange", "onValueChange", "onAlphaChange", "validHex", "valid", "test", "split", "map", "join", "inputChanged", "input", "concat", "presetColorsChanged", "setTimeout", "stateChanged", "runOutsideAngular", "cmykInput", "hue", "lastOutput", "alpha", "transform", "style", "parentNode", "transformNode", "node", "dialogHeight", "tagName", "getComputedStyle", "getPropertyValue", "boxDirective", "createDialogBox", "HTMLUnknownElement", "boxParent", "usePosition", "dialogBounds", "triggerBounds", "windowInnerHeight", "windowInnerWidth", "elRefClientRect", "parent", "child", "element", "offset", "ColorPickerComponent_Factory", "NgZone", "ChangeDetectorRef", "ɵcmp", "ɵɵdefineComponent", "viewQuery", "ColorPickerComponent_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "ColorPickerComponent_HostBindings", "ColorPickerComponent_keyup_esc_HostBindingHandler", "ɵɵresolveDocument", "ColorPickerComponent_keyup_enter_HostBindingHandler", "decls", "vars", "consts", "template", "ColorPickerComponent_Template", "_r1", "ColorPickerComponent_Template_div_click_0_listener", "ColorPickerComponent_Template_div_click_7_listener", "ColorPickerComponent_Template_div_newValue_12_listener", "ColorPickerComponent_Template_div_dragStart_12_listener", "ColorPickerComponent_Template_div_dragEnd_12_listener", "ColorPickerComponent_Template_div_newValue_15_listener", "ColorPickerComponent_Template_div_dragStart_15_listener", "ColorPickerComponent_Template_div_dragEnd_15_listener", "ColorPickerComponent_Template_div_newValue_18_listener", "ColorPickerComponent_Template_div_dragStart_18_listener", "ColorPickerComponent_Template_div_dragEnd_18_listener", "dependencies", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "NgTemplateOutlet", "styles", "encapsulation", "None", "Document", "decorators", "static", "NG_DEV_MODE", "ColorPickerDirective", "injector", "cfr", "appRef", "vcRef", "_service", "dialog", "dialogCreated", "ignore<PERSON><PERSON>es", "cmpRef", "viewAttachedToAppRef", "colorPicker", "cpToggle", "cpDisabled", "cpInputChange", "cpToggleChange", "cpSliderChange", "cpSliderDragEnd", "cpSliderDragStart", "colorPickerOpen", "colorPickerClose", "colorPickerCancel", "colorPickerSelect", "colorPickerChange", "cpCmykColorChange", "cpPresetColorsChange", "handleClick", "inputFocus", "handleFocus", "handleInput", "detach<PERSON>iew", "<PERSON><PERSON><PERSON><PERSON>", "destroy", "ngOnChanges", "changes", "currentValue", "changeDetectorRef", "classOfRootComponent", "componentTypes", "appInstance", "get", "NULL", "viewContainerRef", "console", "warn", "compFactory", "resolveComponentFactory", "create", "attachView", "body", "append<PERSON><PERSON><PERSON>", "rootNodes", "providers", "createComponent", "state", "ignore", "ignored", "activeElement", "ColorPickerDirective_Factory", "ComponentFactoryResolver", "ApplicationRef", "ViewContainerRef", "ColorPickerDirective_HostBindings", "ColorPickerDirective_click_HostBindingHandler", "ColorPickerDirective_focus_HostBindingHandler", "ColorPickerDirective_input_HostBindingHandler", "exportAs", "features", "ɵɵNgOnChangesFeature", "ColorPickerModule", "ColorPickerModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "declarations"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/node_modules/ngx-color-picker/fesm2022/ngx-color-picker.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { EventEmitter, Directive, Input, Output, HostListener, Injectable, PLATFORM_ID, Component, ViewEncapsulation, Inject, ViewChild, Injector, NgModule } from '@angular/core';\nimport * as i2 from '@angular/common';\nimport { isPlatformBrowser, DOCUMENT, CommonModule } from '@angular/common';\n\nvar ColorFormats;\n(function (ColorFormats) {\n    ColorFormats[ColorFormats[\"HEX\"] = 0] = \"HEX\";\n    ColorFormats[ColorFormats[\"RGBA\"] = 1] = \"RGBA\";\n    ColorFormats[ColorFormats[\"HSLA\"] = 2] = \"HSLA\";\n    ColorFormats[ColorFormats[\"CMYK\"] = 3] = \"CMYK\";\n})(ColorFormats || (ColorFormats = {}));\nclass Rgba {\n    r;\n    g;\n    b;\n    a;\n    constructor(r, g, b, a) {\n        this.r = r;\n        this.g = g;\n        this.b = b;\n        this.a = a;\n    }\n}\nclass Hsva {\n    h;\n    s;\n    v;\n    a;\n    constructor(h, s, v, a) {\n        this.h = h;\n        this.s = s;\n        this.v = v;\n        this.a = a;\n    }\n}\nclass Hsla {\n    h;\n    s;\n    l;\n    a;\n    constructor(h, s, l, a) {\n        this.h = h;\n        this.s = s;\n        this.l = l;\n        this.a = a;\n    }\n}\nclass Cmyk {\n    c;\n    m;\n    y;\n    k;\n    a;\n    constructor(c, m, y, k, a = 1) {\n        this.c = c;\n        this.m = m;\n        this.y = y;\n        this.k = k;\n        this.a = a;\n    }\n}\n\nfunction calculateAutoPositioning(elBounds, triggerElBounds) {\n    // Defaults\n    let usePositionX = 'right';\n    let usePositionY = 'bottom';\n    // Calculate collisions\n    const { height, width } = elBounds;\n    const { top, left } = triggerElBounds;\n    const bottom = top + triggerElBounds.height;\n    const right = left + triggerElBounds.width;\n    const collisionTop = top - height < 0;\n    const collisionBottom = bottom + height > (window.innerHeight || document.documentElement.clientHeight);\n    const collisionLeft = left - width < 0;\n    const collisionRight = right + width > (window.innerWidth || document.documentElement.clientWidth);\n    const collisionAll = collisionTop && collisionBottom && collisionLeft && collisionRight;\n    // Generate X & Y position values\n    if (collisionBottom) {\n        usePositionY = 'top';\n    }\n    if (collisionTop) {\n        usePositionY = 'bottom';\n    }\n    if (collisionLeft) {\n        usePositionX = 'right';\n    }\n    if (collisionRight) {\n        usePositionX = 'left';\n    }\n    // Choose the largest gap available\n    if (collisionAll) {\n        const postions = ['left', 'right', 'top', 'bottom'];\n        return postions.reduce((prev, next) => elBounds[prev] > elBounds[next] ? prev : next);\n    }\n    if ((collisionLeft && collisionRight)) {\n        if (collisionTop) {\n            return 'bottom';\n        }\n        if (collisionBottom) {\n            return 'top';\n        }\n        return top > bottom ? 'top' : 'bottom';\n    }\n    if ((collisionTop && collisionBottom)) {\n        if (collisionLeft) {\n            return 'right';\n        }\n        if (collisionRight) {\n            return 'left';\n        }\n        return left > right ? 'left' : 'right';\n    }\n    return `${usePositionY}-${usePositionX}`;\n}\nfunction detectIE() {\n    let ua = '';\n    if (typeof navigator !== 'undefined') {\n        ua = navigator.userAgent.toLowerCase();\n    }\n    const msie = ua.indexOf('msie ');\n    if (msie > 0) {\n        // IE 10 or older => return version number\n        return parseInt(ua.substring(msie + 5, ua.indexOf('.', msie)), 10);\n    }\n    // Other browser\n    return false;\n}\nclass TextDirective {\n    rg;\n    text;\n    newValue = new EventEmitter();\n    inputChange(event) {\n        const value = event.target.value;\n        if (this.rg === undefined) {\n            this.newValue.emit(value);\n        }\n        else {\n            const numeric = parseFloat(value);\n            this.newValue.emit({ v: numeric, rg: this.rg });\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.2\", ngImport: i0, type: TextDirective, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.0.2\", type: TextDirective, selector: \"[text]\", inputs: { rg: \"rg\", text: \"text\" }, outputs: { newValue: \"newValue\" }, host: { listeners: { \"input\": \"inputChange($event)\" } }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.2\", ngImport: i0, type: TextDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[text]'\n                }]\n        }], propDecorators: { rg: [{\n                type: Input\n            }], text: [{\n                type: Input\n            }], newValue: [{\n                type: Output\n            }], inputChange: [{\n                type: HostListener,\n                args: ['input', ['$event']]\n            }] } });\nclass SliderDirective {\n    elRef;\n    listenerMove;\n    listenerStop;\n    rgX;\n    rgY;\n    slider;\n    dragEnd = new EventEmitter();\n    dragStart = new EventEmitter();\n    newValue = new EventEmitter();\n    mouseDown(event) {\n        this.start(event);\n    }\n    touchStart(event) {\n        this.start(event);\n    }\n    constructor(elRef) {\n        this.elRef = elRef;\n        this.listenerMove = (event) => this.move(event);\n        this.listenerStop = () => this.stop();\n    }\n    move(event) {\n        event.preventDefault();\n        this.setCursor(event);\n    }\n    start(event) {\n        this.setCursor(event);\n        event.stopPropagation();\n        document.addEventListener('mouseup', this.listenerStop);\n        document.addEventListener('touchend', this.listenerStop);\n        document.addEventListener('mousemove', this.listenerMove);\n        document.addEventListener('touchmove', this.listenerMove);\n        this.dragStart.emit();\n    }\n    stop() {\n        document.removeEventListener('mouseup', this.listenerStop);\n        document.removeEventListener('touchend', this.listenerStop);\n        document.removeEventListener('mousemove', this.listenerMove);\n        document.removeEventListener('touchmove', this.listenerMove);\n        this.dragEnd.emit();\n    }\n    getX(event) {\n        const position = this.elRef.nativeElement.getBoundingClientRect();\n        const pageX = (event.pageX !== undefined) ? event.pageX : event.touches[0].pageX;\n        return pageX - position.left - window.pageXOffset;\n    }\n    getY(event) {\n        const position = this.elRef.nativeElement.getBoundingClientRect();\n        const pageY = (event.pageY !== undefined) ? event.pageY : event.touches[0].pageY;\n        return pageY - position.top - window.pageYOffset;\n    }\n    setCursor(event) {\n        const width = this.elRef.nativeElement.offsetWidth;\n        const height = this.elRef.nativeElement.offsetHeight;\n        const x = Math.max(0, Math.min(this.getX(event), width));\n        const y = Math.max(0, Math.min(this.getY(event), height));\n        if (this.rgX !== undefined && this.rgY !== undefined) {\n            this.newValue.emit({ s: x / width, v: (1 - y / height), rgX: this.rgX, rgY: this.rgY });\n        }\n        else if (this.rgX === undefined && this.rgY !== undefined) {\n            this.newValue.emit({ v: y / height, rgY: this.rgY });\n        }\n        else if (this.rgX !== undefined && this.rgY === undefined) {\n            this.newValue.emit({ v: x / width, rgX: this.rgX });\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.2\", ngImport: i0, type: SliderDirective, deps: [{ token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.0.2\", type: SliderDirective, selector: \"[slider]\", inputs: { rgX: \"rgX\", rgY: \"rgY\", slider: \"slider\" }, outputs: { dragEnd: \"dragEnd\", dragStart: \"dragStart\", newValue: \"newValue\" }, host: { listeners: { \"mousedown\": \"mouseDown($event)\", \"touchstart\": \"touchStart($event)\" } }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.2\", ngImport: i0, type: SliderDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[slider]'\n                }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }], propDecorators: { rgX: [{\n                type: Input\n            }], rgY: [{\n                type: Input\n            }], slider: [{\n                type: Input\n            }], dragEnd: [{\n                type: Output\n            }], dragStart: [{\n                type: Output\n            }], newValue: [{\n                type: Output\n            }], mouseDown: [{\n                type: HostListener,\n                args: ['mousedown', ['$event']]\n            }], touchStart: [{\n                type: HostListener,\n                args: ['touchstart', ['$event']]\n            }] } });\nclass SliderPosition {\n    h;\n    s;\n    v;\n    a;\n    constructor(h, s, v, a) {\n        this.h = h;\n        this.s = s;\n        this.v = v;\n        this.a = a;\n    }\n}\nclass SliderDimension {\n    h;\n    s;\n    v;\n    a;\n    constructor(h, s, v, a) {\n        this.h = h;\n        this.s = s;\n        this.v = v;\n        this.a = a;\n    }\n}\n\nclass ColorPickerService {\n    active = null;\n    setActive(active) {\n        if (this.active && this.active !== active && this.active.cpDialogDisplay !== 'inline') {\n            this.active.closeDialog();\n        }\n        this.active = active;\n    }\n    hsva2hsla(hsva) {\n        const h = hsva.h, s = hsva.s, v = hsva.v, a = hsva.a;\n        if (v === 0) {\n            return new Hsla(h, 0, 0, a);\n        }\n        else if (s === 0 && v === 1) {\n            return new Hsla(h, 1, 1, a);\n        }\n        else {\n            const l = v * (2 - s) / 2;\n            return new Hsla(h, v * s / (1 - Math.abs(2 * l - 1)), l, a);\n        }\n    }\n    hsla2hsva(hsla) {\n        const h = Math.min(hsla.h, 1), s = Math.min(hsla.s, 1);\n        const l = Math.min(hsla.l, 1), a = Math.min(hsla.a, 1);\n        if (l === 0) {\n            return new Hsva(h, 0, 0, a);\n        }\n        else {\n            const v = l + s * (1 - Math.abs(2 * l - 1)) / 2;\n            return new Hsva(h, 2 * (v - l) / v, v, a);\n        }\n    }\n    hsvaToRgba(hsva) {\n        let r, g, b;\n        const h = hsva.h, s = hsva.s, v = hsva.v, a = hsva.a;\n        const i = Math.floor(h * 6);\n        const f = h * 6 - i;\n        const p = v * (1 - s);\n        const q = v * (1 - f * s);\n        const t = v * (1 - (1 - f) * s);\n        switch (i % 6) {\n            case 0:\n                r = v, g = t, b = p;\n                break;\n            case 1:\n                r = q, g = v, b = p;\n                break;\n            case 2:\n                r = p, g = v, b = t;\n                break;\n            case 3:\n                r = p, g = q, b = v;\n                break;\n            case 4:\n                r = t, g = p, b = v;\n                break;\n            case 5:\n                r = v, g = p, b = q;\n                break;\n            default:\n                r = 0, g = 0, b = 0;\n        }\n        return new Rgba(r, g, b, a);\n    }\n    cmykToRgb(cmyk) {\n        const r = (1 - cmyk.c) * (1 - cmyk.k);\n        const g = (1 - cmyk.m) * (1 - cmyk.k);\n        const b = (1 - cmyk.y) * (1 - cmyk.k);\n        return new Rgba(r, g, b, cmyk.a);\n    }\n    rgbaToCmyk(rgba) {\n        const k = 1 - Math.max(rgba.r, rgba.g, rgba.b);\n        if (k === 1) {\n            return new Cmyk(0, 0, 0, 1, rgba.a);\n        }\n        else {\n            const c = (1 - rgba.r - k) / (1 - k);\n            const m = (1 - rgba.g - k) / (1 - k);\n            const y = (1 - rgba.b - k) / (1 - k);\n            return new Cmyk(c, m, y, k, rgba.a);\n        }\n    }\n    rgbaToHsva(rgba) {\n        let h, s;\n        const r = Math.min(rgba.r, 1), g = Math.min(rgba.g, 1);\n        const b = Math.min(rgba.b, 1), a = Math.min(rgba.a, 1);\n        const max = Math.max(r, g, b), min = Math.min(r, g, b);\n        const v = max, d = max - min;\n        s = (max === 0) ? 0 : d / max;\n        if (max === min) {\n            h = 0;\n        }\n        else {\n            switch (max) {\n                case r:\n                    h = (g - b) / d + (g < b ? 6 : 0);\n                    break;\n                case g:\n                    h = (b - r) / d + 2;\n                    break;\n                case b:\n                    h = (r - g) / d + 4;\n                    break;\n                default:\n                    h = 0;\n            }\n            h /= 6;\n        }\n        return new Hsva(h, s, v, a);\n    }\n    rgbaToHex(rgba, allowHex8) {\n        /* eslint-disable no-bitwise */\n        let hex = '#' + ((1 << 24) | (rgba.r << 16) | (rgba.g << 8) | rgba.b).toString(16).substr(1);\n        if (allowHex8) {\n            hex += ((1 << 8) | Math.round(rgba.a * 255)).toString(16).substr(1);\n        }\n        /* eslint-enable no-bitwise */\n        return hex;\n    }\n    normalizeCMYK(cmyk) {\n        return new Cmyk(cmyk.c / 100, cmyk.m / 100, cmyk.y / 100, cmyk.k / 100, cmyk.a);\n    }\n    denormalizeCMYK(cmyk) {\n        return new Cmyk(Math.floor(cmyk.c * 100), Math.floor(cmyk.m * 100), Math.floor(cmyk.y * 100), Math.floor(cmyk.k * 100), cmyk.a);\n    }\n    denormalizeRGBA(rgba) {\n        return new Rgba(Math.round(rgba.r * 255), Math.round(rgba.g * 255), Math.round(rgba.b * 255), rgba.a);\n    }\n    stringToHsva(colorString = '', allowHex8 = false) {\n        let hsva = null;\n        colorString = (colorString || '').toLowerCase();\n        const stringParsers = [\n            {\n                re: /(rgb)a?\\(\\s*(\\d{1,3})\\s*,\\s*(\\d{1,3})\\s*%?,\\s*(\\d{1,3})\\s*%?(?:,\\s*(\\d+(?:\\.\\d+)?)\\s*)?\\)/,\n                parse: function (execResult) {\n                    return new Rgba(parseInt(execResult[2], 10) / 255, parseInt(execResult[3], 10) / 255, parseInt(execResult[4], 10) / 255, isNaN(parseFloat(execResult[5])) ? 1 : parseFloat(execResult[5]));\n                }\n            }, {\n                re: /(hsl)a?\\(\\s*(\\d{1,3})\\s*,\\s*(\\d{1,3})%\\s*,\\s*(\\d{1,3})%\\s*(?:,\\s*(\\d+(?:\\.\\d+)?)\\s*)?\\)/,\n                parse: function (execResult) {\n                    return new Hsla(parseInt(execResult[2], 10) / 360, parseInt(execResult[3], 10) / 100, parseInt(execResult[4], 10) / 100, isNaN(parseFloat(execResult[5])) ? 1 : parseFloat(execResult[5]));\n                }\n            }\n        ];\n        if (allowHex8) {\n            stringParsers.push({\n                re: /#([a-fA-F0-9]{2})([a-fA-F0-9]{2})([a-fA-F0-9]{2})([a-fA-F0-9]{2})?$/,\n                parse: function (execResult) {\n                    return new Rgba(parseInt(execResult[1], 16) / 255, parseInt(execResult[2], 16) / 255, parseInt(execResult[3], 16) / 255, parseInt(execResult[4] || 'FF', 16) / 255);\n                }\n            });\n        }\n        else {\n            stringParsers.push({\n                re: /#([a-fA-F0-9]{2})([a-fA-F0-9]{2})([a-fA-F0-9]{2})$/,\n                parse: function (execResult) {\n                    return new Rgba(parseInt(execResult[1], 16) / 255, parseInt(execResult[2], 16) / 255, parseInt(execResult[3], 16) / 255, 1);\n                }\n            });\n        }\n        stringParsers.push({\n            re: /#([a-fA-F0-9])([a-fA-F0-9])([a-fA-F0-9])$/,\n            parse: function (execResult) {\n                return new Rgba(parseInt(execResult[1] + execResult[1], 16) / 255, parseInt(execResult[2] + execResult[2], 16) / 255, parseInt(execResult[3] + execResult[3], 16) / 255, 1);\n            }\n        });\n        for (const key in stringParsers) {\n            if (stringParsers.hasOwnProperty(key)) {\n                const parser = stringParsers[key];\n                const match = parser.re.exec(colorString), color = match && parser.parse(match);\n                if (color) {\n                    if (color instanceof Rgba) {\n                        hsva = this.rgbaToHsva(color);\n                    }\n                    else if (color instanceof Hsla) {\n                        hsva = this.hsla2hsva(color);\n                    }\n                    return hsva;\n                }\n            }\n        }\n        return hsva;\n    }\n    outputFormat(hsva, outputFormat, alphaChannel) {\n        if (outputFormat === 'auto') {\n            outputFormat = hsva.a < 1 ? 'rgba' : 'hex';\n        }\n        switch (outputFormat) {\n            case 'hsla':\n                const hsla = this.hsva2hsla(hsva);\n                const hslaText = new Hsla(Math.round((hsla.h) * 360), Math.round(hsla.s * 100), Math.round(hsla.l * 100), Math.round(hsla.a * 100) / 100);\n                if (hsva.a < 1 || alphaChannel === 'always') {\n                    return 'hsla(' + hslaText.h + ',' + hslaText.s + '%,' + hslaText.l + '%,' +\n                        hslaText.a + ')';\n                }\n                else {\n                    return 'hsl(' + hslaText.h + ',' + hslaText.s + '%,' + hslaText.l + '%)';\n                }\n            case 'rgba':\n                const rgba = this.denormalizeRGBA(this.hsvaToRgba(hsva));\n                if (hsva.a < 1 || alphaChannel === 'always') {\n                    return 'rgba(' + rgba.r + ',' + rgba.g + ',' + rgba.b + ',' +\n                        Math.round(rgba.a * 100) / 100 + ')';\n                }\n                else {\n                    return 'rgb(' + rgba.r + ',' + rgba.g + ',' + rgba.b + ')';\n                }\n            default:\n                const allowHex8 = (alphaChannel === 'always' || alphaChannel === 'forced');\n                return this.rgbaToHex(this.denormalizeRGBA(this.hsvaToRgba(hsva)), allowHex8);\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.2\", ngImport: i0, type: ColorPickerService, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.0.2\", ngImport: i0, type: ColorPickerService });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.2\", ngImport: i0, type: ColorPickerService, decorators: [{\n            type: Injectable\n        }] });\n\n// Do not store that on the class instance since the condition will be run\n// every time the class is created.\nconst SUPPORTS_TOUCH = typeof window !== 'undefined' && 'ontouchstart' in window;\nclass ColorPickerComponent {\n    ngZone;\n    elRef;\n    cdRef;\n    document;\n    platformId;\n    service;\n    isIE10 = false;\n    cmyk;\n    hsva;\n    width;\n    height;\n    cmykColor;\n    outputColor;\n    initialColor;\n    fallbackColor;\n    listenerResize;\n    listenerMouseDown;\n    directiveInstance;\n    sliderH;\n    sliderDimMax;\n    directiveElementRef;\n    dialogArrowSize = 10;\n    dialogArrowOffset = 15;\n    dialogInputFields = [\n        ColorFormats.HEX,\n        ColorFormats.RGBA,\n        ColorFormats.HSLA,\n        ColorFormats.CMYK\n    ];\n    useRootViewContainer = false;\n    show;\n    hidden;\n    top;\n    left;\n    position;\n    format;\n    slider;\n    hexText;\n    hexAlpha;\n    cmykText;\n    hslaText;\n    rgbaText;\n    arrowTop;\n    selectedColor;\n    hueSliderColor;\n    alphaSliderColor;\n    cpWidth;\n    cpHeight;\n    cpColorMode;\n    cpCmykEnabled;\n    cpAlphaChannel;\n    cpOutputFormat;\n    cpDisableInput;\n    cpDialogDisplay;\n    cpIgnoredElements;\n    cpSaveClickOutside;\n    cpCloseClickOutside;\n    cpPosition;\n    cpUsePosition;\n    cpPositionOffset;\n    cpOKButton;\n    cpOKButtonText;\n    cpOKButtonClass;\n    cpCancelButton;\n    cpCancelButtonText;\n    cpCancelButtonClass;\n    cpEyeDropper;\n    eyeDropperSupported;\n    cpPresetLabel;\n    cpPresetColors;\n    cpPresetColorsClass;\n    cpMaxPresetColorsLength;\n    cpPresetEmptyMessage;\n    cpPresetEmptyMessageClass;\n    cpAddColorButton;\n    cpAddColorButtonText;\n    cpAddColorButtonClass;\n    cpRemoveColorButtonClass;\n    cpArrowPosition;\n    cpTriggerElement;\n    cpExtraTemplate;\n    dialogElement;\n    hueSlider;\n    alphaSlider;\n    handleEsc(event) {\n        if (this.show && this.cpDialogDisplay === 'popup') {\n            this.onCancelColor(event);\n        }\n    }\n    handleEnter(event) {\n        if (this.show && this.cpDialogDisplay === 'popup') {\n            this.onAcceptColor(event);\n        }\n    }\n    constructor(ngZone, elRef, cdRef, document, platformId, service) {\n        this.ngZone = ngZone;\n        this.elRef = elRef;\n        this.cdRef = cdRef;\n        this.document = document;\n        this.platformId = platformId;\n        this.service = service;\n        this.eyeDropperSupported = isPlatformBrowser(this.platformId) && 'EyeDropper' in this.document.defaultView;\n    }\n    ngOnInit() {\n        this.slider = new SliderPosition(0, 0, 0, 0);\n        const hueWidth = this.hueSlider.nativeElement.offsetWidth || 140;\n        const alphaWidth = this.alphaSlider.nativeElement.offsetWidth || 140;\n        this.sliderDimMax = new SliderDimension(hueWidth, this.cpWidth, 130, alphaWidth);\n        if (this.cpCmykEnabled) {\n            this.format = ColorFormats.CMYK;\n        }\n        else if (this.cpOutputFormat === 'rgba') {\n            this.format = ColorFormats.RGBA;\n        }\n        else if (this.cpOutputFormat === 'hsla') {\n            this.format = ColorFormats.HSLA;\n        }\n        else {\n            this.format = ColorFormats.HEX;\n        }\n        this.listenerMouseDown = (event) => { this.onMouseDown(event); };\n        this.listenerResize = () => { this.onResize(); };\n        this.openDialog(this.initialColor, false);\n    }\n    ngOnDestroy() {\n        this.closeDialog();\n    }\n    ngAfterViewInit() {\n        if (this.cpWidth !== 230 || this.cpDialogDisplay === 'inline') {\n            const hueWidth = this.hueSlider.nativeElement.offsetWidth || 140;\n            const alphaWidth = this.alphaSlider.nativeElement.offsetWidth || 140;\n            this.sliderDimMax = new SliderDimension(hueWidth, this.cpWidth, 130, alphaWidth);\n            this.updateColorPicker(false);\n            this.cdRef.detectChanges();\n        }\n    }\n    openDialog(color, emit = true) {\n        this.service.setActive(this);\n        if (!this.width) {\n            this.cpWidth = this.directiveElementRef.nativeElement.offsetWidth;\n        }\n        if (!this.height) {\n            this.height = 320;\n        }\n        this.setInitialColor(color);\n        this.setColorFromString(color, emit);\n        this.openColorPicker();\n    }\n    closeDialog() {\n        this.closeColorPicker();\n    }\n    setupDialog(instance, elementRef, color, cpWidth, cpHeight, cpDialogDisplay, cpFallbackColor, cpColorMode, cpCmykEnabled, cpAlphaChannel, cpOutputFormat, cpDisableInput, cpIgnoredElements, cpSaveClickOutside, cpCloseClickOutside, cpUseRootViewContainer, cpPosition, cpPositionOffset, cpPositionRelativeToArrow, cpPresetLabel, cpPresetColors, cpPresetColorsClass, cpMaxPresetColorsLength, cpPresetEmptyMessage, cpPresetEmptyMessageClass, cpOKButton, cpOKButtonClass, cpOKButtonText, cpCancelButton, cpCancelButtonClass, cpCancelButtonText, cpAddColorButton, cpAddColorButtonClass, cpAddColorButtonText, cpRemoveColorButtonClass, cpEyeDropper, cpTriggerElement, cpExtraTemplate) {\n        this.setInitialColor(color);\n        this.setColorMode(cpColorMode);\n        this.isIE10 = (detectIE() === 10);\n        this.directiveInstance = instance;\n        this.directiveElementRef = elementRef;\n        this.cpDisableInput = cpDisableInput;\n        this.cpCmykEnabled = cpCmykEnabled;\n        this.cpAlphaChannel = cpAlphaChannel;\n        this.cpOutputFormat = cpOutputFormat;\n        this.cpDialogDisplay = cpDialogDisplay;\n        this.cpIgnoredElements = cpIgnoredElements;\n        this.cpSaveClickOutside = cpSaveClickOutside;\n        this.cpCloseClickOutside = cpCloseClickOutside;\n        this.useRootViewContainer = cpUseRootViewContainer;\n        this.width = this.cpWidth = parseInt(cpWidth, 10);\n        this.height = this.cpHeight = parseInt(cpHeight, 10);\n        this.cpPosition = cpPosition;\n        this.cpPositionOffset = parseInt(cpPositionOffset, 10);\n        this.cpOKButton = cpOKButton;\n        this.cpOKButtonText = cpOKButtonText;\n        this.cpOKButtonClass = cpOKButtonClass;\n        this.cpCancelButton = cpCancelButton;\n        this.cpCancelButtonText = cpCancelButtonText;\n        this.cpCancelButtonClass = cpCancelButtonClass;\n        this.cpEyeDropper = cpEyeDropper;\n        this.fallbackColor = cpFallbackColor || '#fff';\n        this.setPresetConfig(cpPresetLabel, cpPresetColors);\n        this.cpPresetColorsClass = cpPresetColorsClass;\n        this.cpMaxPresetColorsLength = cpMaxPresetColorsLength;\n        this.cpPresetEmptyMessage = cpPresetEmptyMessage;\n        this.cpPresetEmptyMessageClass = cpPresetEmptyMessageClass;\n        this.cpAddColorButton = cpAddColorButton;\n        this.cpAddColorButtonText = cpAddColorButtonText;\n        this.cpAddColorButtonClass = cpAddColorButtonClass;\n        this.cpRemoveColorButtonClass = cpRemoveColorButtonClass;\n        this.cpTriggerElement = cpTriggerElement;\n        this.cpExtraTemplate = cpExtraTemplate;\n        if (!cpPositionRelativeToArrow) {\n            this.dialogArrowOffset = 0;\n        }\n        if (cpDialogDisplay === 'inline') {\n            this.dialogArrowSize = 0;\n            this.dialogArrowOffset = 0;\n        }\n        if (cpOutputFormat === 'hex' &&\n            cpAlphaChannel !== 'always' && cpAlphaChannel !== 'forced') {\n            this.cpAlphaChannel = 'disabled';\n        }\n    }\n    setColorMode(mode) {\n        switch (mode.toString().toUpperCase()) {\n            case '1':\n            case 'C':\n            case 'COLOR':\n                this.cpColorMode = 1;\n                break;\n            case '2':\n            case 'G':\n            case 'GRAYSCALE':\n                this.cpColorMode = 2;\n                break;\n            case '3':\n            case 'P':\n            case 'PRESETS':\n                this.cpColorMode = 3;\n                break;\n            default:\n                this.cpColorMode = 1;\n        }\n    }\n    setInitialColor(color) {\n        this.initialColor = color;\n    }\n    setPresetConfig(cpPresetLabel, cpPresetColors) {\n        this.cpPresetLabel = cpPresetLabel;\n        this.cpPresetColors = cpPresetColors;\n    }\n    setColorFromString(value, emit = true, update = true) {\n        let hsva;\n        if (this.cpAlphaChannel === 'always' || this.cpAlphaChannel === 'forced') {\n            hsva = this.service.stringToHsva(value, true);\n            if (!hsva && !this.hsva) {\n                hsva = this.service.stringToHsva(value, false);\n            }\n        }\n        else {\n            hsva = this.service.stringToHsva(value, false);\n        }\n        if (!hsva && !this.hsva) {\n            hsva = this.service.stringToHsva(this.fallbackColor, false);\n        }\n        if (hsva) {\n            this.hsva = hsva;\n            this.sliderH = this.hsva.h;\n            if (this.cpOutputFormat === 'hex' && this.cpAlphaChannel === 'disabled') {\n                this.hsva.a = 1;\n            }\n            this.updateColorPicker(emit, update);\n        }\n    }\n    onResize() {\n        if (this.position === 'fixed') {\n            this.setDialogPosition();\n        }\n        else if (this.cpDialogDisplay !== 'inline') {\n            this.closeColorPicker();\n        }\n    }\n    onDragEnd(slider) {\n        this.directiveInstance.sliderDragEnd({ slider: slider, color: this.outputColor });\n    }\n    onDragStart(slider) {\n        this.directiveInstance.sliderDragStart({ slider: slider, color: this.outputColor });\n    }\n    onMouseDown(event) {\n        if (this.show &&\n            !this.isIE10 &&\n            this.cpDialogDisplay === 'popup' &&\n            event.target !== this.directiveElementRef.nativeElement &&\n            !this.isDescendant(this.elRef.nativeElement, event.target) &&\n            !this.isDescendant(this.directiveElementRef.nativeElement, event.target) &&\n            this.cpIgnoredElements.filter((item) => item === event.target).length === 0) {\n            this.ngZone.run(() => {\n                if (this.cpSaveClickOutside) {\n                    this.directiveInstance.colorSelected(this.outputColor);\n                }\n                else {\n                    this.hsva = null;\n                    this.setColorFromString(this.initialColor, false);\n                    if (this.cpCmykEnabled) {\n                        this.directiveInstance.cmykChanged(this.cmykColor);\n                    }\n                    this.directiveInstance.colorChanged(this.initialColor);\n                    this.directiveInstance.colorCanceled();\n                }\n                if (this.cpCloseClickOutside) {\n                    this.closeColorPicker();\n                }\n            });\n        }\n    }\n    onAcceptColor(event) {\n        event.stopPropagation();\n        if (this.outputColor) {\n            this.directiveInstance.colorSelected(this.outputColor);\n        }\n        if (this.cpDialogDisplay === 'popup') {\n            this.closeColorPicker();\n        }\n    }\n    onCancelColor(event) {\n        this.hsva = null;\n        event.stopPropagation();\n        this.directiveInstance.colorCanceled();\n        this.setColorFromString(this.initialColor, true);\n        if (this.cpDialogDisplay === 'popup') {\n            if (this.cpCmykEnabled) {\n                this.directiveInstance.cmykChanged(this.cmykColor);\n            }\n            this.directiveInstance.colorChanged(this.initialColor, true);\n            this.closeColorPicker();\n        }\n    }\n    onEyeDropper() {\n        if (!this.eyeDropperSupported)\n            return;\n        const eyeDropper = new window.EyeDropper();\n        eyeDropper.open().then((eyeDropperResult) => {\n            this.setColorFromString(eyeDropperResult.sRGBHex, true);\n        });\n    }\n    onFormatToggle(change) {\n        const availableFormats = this.dialogInputFields.length -\n            (this.cpCmykEnabled ? 0 : 1);\n        const nextFormat = (((this.dialogInputFields.indexOf(this.format) + change) %\n            availableFormats) + availableFormats) % availableFormats;\n        this.format = this.dialogInputFields[nextFormat];\n    }\n    onColorChange(value) {\n        this.hsva.s = value.s / value.rgX;\n        this.hsva.v = value.v / value.rgY;\n        this.updateColorPicker();\n        this.directiveInstance.sliderChanged({\n            slider: 'lightness',\n            value: this.hsva.v,\n            color: this.outputColor\n        });\n        this.directiveInstance.sliderChanged({\n            slider: 'saturation',\n            value: this.hsva.s,\n            color: this.outputColor\n        });\n    }\n    onHueChange(value) {\n        this.hsva.h = value.v / value.rgX;\n        this.sliderH = this.hsva.h;\n        this.updateColorPicker();\n        this.directiveInstance.sliderChanged({\n            slider: 'hue',\n            value: this.hsva.h,\n            color: this.outputColor\n        });\n    }\n    onValueChange(value) {\n        this.hsva.v = value.v / value.rgX;\n        this.updateColorPicker();\n        this.directiveInstance.sliderChanged({\n            slider: 'value',\n            value: this.hsva.v,\n            color: this.outputColor\n        });\n    }\n    onAlphaChange(value) {\n        this.hsva.a = value.v / value.rgX;\n        this.updateColorPicker();\n        this.directiveInstance.sliderChanged({\n            slider: 'alpha',\n            value: this.hsva.a,\n            color: this.outputColor\n        });\n    }\n    onHexInput(value) {\n        if (value === null) {\n            this.updateColorPicker();\n        }\n        else {\n            if (value && value[0] !== '#') {\n                value = '#' + value;\n            }\n            let validHex = /^#([a-f0-9]{3}|[a-f0-9]{6})$/gi;\n            if (this.cpAlphaChannel === 'always') {\n                validHex = /^#([a-f0-9]{3}|[a-f0-9]{6}|[a-f0-9]{8})$/gi;\n            }\n            const valid = validHex.test(value);\n            if (valid) {\n                if (value.length < 5) {\n                    value = '#' + value.substring(1)\n                        .split('')\n                        .map(c => c + c)\n                        .join('');\n                }\n                if (this.cpAlphaChannel === 'forced') {\n                    value += Math.round(this.hsva.a * 255).toString(16);\n                }\n                this.setColorFromString(value, true, false);\n            }\n            this.directiveInstance.inputChanged({\n                input: 'hex',\n                valid: valid,\n                value: value,\n                color: this.outputColor\n            });\n        }\n    }\n    onRedInput(value) {\n        const rgba = this.service.hsvaToRgba(this.hsva);\n        const valid = !isNaN(value.v) && value.v >= 0 && value.v <= value.rg;\n        if (valid) {\n            rgba.r = value.v / value.rg;\n            this.hsva = this.service.rgbaToHsva(rgba);\n            this.sliderH = this.hsva.h;\n            this.updateColorPicker();\n        }\n        this.directiveInstance.inputChanged({\n            input: 'red',\n            valid: valid,\n            value: rgba.r,\n            color: this.outputColor\n        });\n    }\n    onBlueInput(value) {\n        const rgba = this.service.hsvaToRgba(this.hsva);\n        const valid = !isNaN(value.v) && value.v >= 0 && value.v <= value.rg;\n        if (valid) {\n            rgba.b = value.v / value.rg;\n            this.hsva = this.service.rgbaToHsva(rgba);\n            this.sliderH = this.hsva.h;\n            this.updateColorPicker();\n        }\n        this.directiveInstance.inputChanged({\n            input: 'blue',\n            valid: valid,\n            value: rgba.b,\n            color: this.outputColor\n        });\n    }\n    onGreenInput(value) {\n        const rgba = this.service.hsvaToRgba(this.hsva);\n        const valid = !isNaN(value.v) && value.v >= 0 && value.v <= value.rg;\n        if (valid) {\n            rgba.g = value.v / value.rg;\n            this.hsva = this.service.rgbaToHsva(rgba);\n            this.sliderH = this.hsva.h;\n            this.updateColorPicker();\n        }\n        this.directiveInstance.inputChanged({\n            input: 'green',\n            valid: valid,\n            value: rgba.g,\n            color: this.outputColor\n        });\n    }\n    onHueInput(value) {\n        const valid = !isNaN(value.v) && value.v >= 0 && value.v <= value.rg;\n        if (valid) {\n            this.hsva.h = value.v / value.rg;\n            this.sliderH = this.hsva.h;\n            this.updateColorPicker();\n        }\n        this.directiveInstance.inputChanged({\n            input: 'hue',\n            valid: valid,\n            value: this.hsva.h,\n            color: this.outputColor\n        });\n    }\n    onValueInput(value) {\n        const valid = !isNaN(value.v) && value.v >= 0 && value.v <= value.rg;\n        if (valid) {\n            this.hsva.v = value.v / value.rg;\n            this.updateColorPicker();\n        }\n        this.directiveInstance.inputChanged({\n            input: 'value',\n            valid: valid,\n            value: this.hsva.v,\n            color: this.outputColor\n        });\n    }\n    onAlphaInput(value) {\n        const valid = !isNaN(value.v) && value.v >= 0 && value.v <= value.rg;\n        if (valid) {\n            this.hsva.a = value.v / value.rg;\n            this.updateColorPicker();\n        }\n        this.directiveInstance.inputChanged({\n            input: 'alpha',\n            valid: valid,\n            value: this.hsva.a,\n            color: this.outputColor\n        });\n    }\n    onLightnessInput(value) {\n        const hsla = this.service.hsva2hsla(this.hsva);\n        const valid = !isNaN(value.v) && value.v >= 0 && value.v <= value.rg;\n        if (valid) {\n            hsla.l = value.v / value.rg;\n            this.hsva = this.service.hsla2hsva(hsla);\n            this.sliderH = this.hsva.h;\n            this.updateColorPicker();\n        }\n        this.directiveInstance.inputChanged({\n            input: 'lightness',\n            valid: valid,\n            value: hsla.l,\n            color: this.outputColor\n        });\n    }\n    onSaturationInput(value) {\n        const hsla = this.service.hsva2hsla(this.hsva);\n        const valid = !isNaN(value.v) && value.v >= 0 && value.v <= value.rg;\n        if (valid) {\n            hsla.s = value.v / value.rg;\n            this.hsva = this.service.hsla2hsva(hsla);\n            this.sliderH = this.hsva.h;\n            this.updateColorPicker();\n        }\n        this.directiveInstance.inputChanged({\n            input: 'saturation',\n            valid: valid,\n            value: hsla.s,\n            color: this.outputColor\n        });\n    }\n    onCyanInput(value) {\n        const valid = !isNaN(value.v) && value.v >= 0 && value.v <= value.rg;\n        if (valid) {\n            this.cmyk.c = value.v;\n            this.updateColorPicker(false, true, true);\n        }\n        this.directiveInstance.inputChanged({\n            input: 'cyan',\n            valid: true,\n            value: this.cmyk.c,\n            color: this.outputColor\n        });\n    }\n    onMagentaInput(value) {\n        const valid = !isNaN(value.v) && value.v >= 0 && value.v <= value.rg;\n        if (valid) {\n            this.cmyk.m = value.v;\n            this.updateColorPicker(false, true, true);\n        }\n        this.directiveInstance.inputChanged({\n            input: 'magenta',\n            valid: true,\n            value: this.cmyk.m,\n            color: this.outputColor\n        });\n    }\n    onYellowInput(value) {\n        const valid = !isNaN(value.v) && value.v >= 0 && value.v <= value.rg;\n        if (valid) {\n            this.cmyk.y = value.v;\n            this.updateColorPicker(false, true, true);\n        }\n        this.directiveInstance.inputChanged({\n            input: 'yellow',\n            valid: true,\n            value: this.cmyk.y,\n            color: this.outputColor\n        });\n    }\n    onBlackInput(value) {\n        const valid = !isNaN(value.v) && value.v >= 0 && value.v <= value.rg;\n        if (valid) {\n            this.cmyk.k = value.v;\n            this.updateColorPicker(false, true, true);\n        }\n        this.directiveInstance.inputChanged({\n            input: 'black',\n            valid: true,\n            value: this.cmyk.k,\n            color: this.outputColor\n        });\n    }\n    onAddPresetColor(event, value) {\n        event.stopPropagation();\n        if (!this.cpPresetColors.filter((color) => (color === value)).length) {\n            this.cpPresetColors = this.cpPresetColors.concat(value);\n            this.directiveInstance.presetColorsChanged(this.cpPresetColors);\n        }\n    }\n    onRemovePresetColor(event, value) {\n        event.stopPropagation();\n        this.cpPresetColors = this.cpPresetColors.filter((color) => (color !== value));\n        this.directiveInstance.presetColorsChanged(this.cpPresetColors);\n    }\n    // Private helper functions for the color picker dialog status\n    openColorPicker() {\n        if (!this.show) {\n            this.show = true;\n            this.hidden = true;\n            setTimeout(() => {\n                this.hidden = false;\n                this.setDialogPosition();\n                this.cdRef.detectChanges();\n            }, 0);\n            this.directiveInstance.stateChanged(true);\n            if (!this.isIE10) {\n                // The change detection should be run on `mousedown` event only when the condition\n                // is met within the `onMouseDown` method.\n                this.ngZone.runOutsideAngular(() => {\n                    // There's no sense to add both event listeners on touch devices since the `touchstart`\n                    // event is handled earlier than `mousedown`, so we'll get 2 change detections and the\n                    // second one will be unnecessary.\n                    if (SUPPORTS_TOUCH) {\n                        document.addEventListener('touchstart', this.listenerMouseDown);\n                    }\n                    else {\n                        document.addEventListener('mousedown', this.listenerMouseDown);\n                    }\n                });\n            }\n            window.addEventListener('resize', this.listenerResize);\n        }\n    }\n    closeColorPicker() {\n        if (this.show) {\n            this.show = false;\n            this.directiveInstance.stateChanged(false);\n            if (!this.isIE10) {\n                if (SUPPORTS_TOUCH) {\n                    document.removeEventListener('touchstart', this.listenerMouseDown);\n                }\n                else {\n                    document.removeEventListener('mousedown', this.listenerMouseDown);\n                }\n            }\n            window.removeEventListener('resize', this.listenerResize);\n            if (!this.cdRef['destroyed']) {\n                this.cdRef.detectChanges();\n            }\n        }\n    }\n    updateColorPicker(emit = true, update = true, cmykInput = false) {\n        if (this.sliderDimMax) {\n            if (this.cpColorMode === 2) {\n                this.hsva.s = 0;\n            }\n            let hue, hsla, rgba;\n            const lastOutput = this.outputColor;\n            hsla = this.service.hsva2hsla(this.hsva);\n            if (!this.cpCmykEnabled) {\n                rgba = this.service.denormalizeRGBA(this.service.hsvaToRgba(this.hsva));\n            }\n            else {\n                if (!cmykInput) {\n                    rgba = this.service.hsvaToRgba(this.hsva);\n                    this.cmyk = this.service.denormalizeCMYK(this.service.rgbaToCmyk(rgba));\n                }\n                else {\n                    rgba = this.service.cmykToRgb(this.service.normalizeCMYK(this.cmyk));\n                    this.hsva = this.service.rgbaToHsva(rgba);\n                }\n                rgba = this.service.denormalizeRGBA(rgba);\n                this.sliderH = this.hsva.h;\n            }\n            hue = this.service.denormalizeRGBA(this.service.hsvaToRgba(new Hsva(this.sliderH || this.hsva.h, 1, 1, 1)));\n            if (update) {\n                this.hslaText = new Hsla(Math.round((hsla.h) * 360), Math.round(hsla.s * 100), Math.round(hsla.l * 100), Math.round(hsla.a * 100) / 100);\n                this.rgbaText = new Rgba(rgba.r, rgba.g, rgba.b, Math.round(rgba.a * 100) / 100);\n                if (this.cpCmykEnabled) {\n                    this.cmykText = new Cmyk(this.cmyk.c, this.cmyk.m, this.cmyk.y, this.cmyk.k, Math.round(this.cmyk.a * 100) / 100);\n                }\n                const allowHex8 = this.cpAlphaChannel === 'always';\n                this.hexText = this.service.rgbaToHex(rgba, allowHex8);\n                this.hexAlpha = this.rgbaText.a;\n            }\n            if (this.cpOutputFormat === 'auto') {\n                if (this.format !== ColorFormats.RGBA && this.format !== ColorFormats.CMYK && this.format !== ColorFormats.HSLA) {\n                    if (this.hsva.a < 1) {\n                        this.format = this.hsva.a < 1 ? ColorFormats.RGBA : ColorFormats.HEX;\n                    }\n                }\n            }\n            this.hueSliderColor = 'rgb(' + hue.r + ',' + hue.g + ',' + hue.b + ')';\n            this.alphaSliderColor = 'rgb(' + rgba.r + ',' + rgba.g + ',' + rgba.b + ')';\n            this.outputColor = this.service.outputFormat(this.hsva, this.cpOutputFormat, this.cpAlphaChannel);\n            this.selectedColor = this.service.outputFormat(this.hsva, 'rgba', null);\n            if (this.format !== ColorFormats.CMYK) {\n                this.cmykColor = '';\n            }\n            else {\n                if (this.cpAlphaChannel === 'always' || this.cpAlphaChannel === 'enabled' ||\n                    this.cpAlphaChannel === 'forced') {\n                    const alpha = Math.round(this.cmyk.a * 100) / 100;\n                    this.cmykColor = `cmyka(${this.cmyk.c},${this.cmyk.m},${this.cmyk.y},${this.cmyk.k},${alpha})`;\n                }\n                else {\n                    this.cmykColor = `cmyk(${this.cmyk.c},${this.cmyk.m},${this.cmyk.y},${this.cmyk.k})`;\n                }\n            }\n            this.slider = new SliderPosition((this.sliderH || this.hsva.h) * this.sliderDimMax.h - 8, this.hsva.s * this.sliderDimMax.s - 8, (1 - this.hsva.v) * this.sliderDimMax.v - 8, this.hsva.a * this.sliderDimMax.a - 8);\n            if (emit && lastOutput !== this.outputColor) {\n                if (this.cpCmykEnabled) {\n                    this.directiveInstance.cmykChanged(this.cmykColor);\n                }\n                this.directiveInstance.colorChanged(this.outputColor);\n            }\n        }\n    }\n    // Private helper functions for the color picker dialog positioning\n    setDialogPosition() {\n        if (this.cpDialogDisplay === 'inline') {\n            this.position = 'relative';\n        }\n        else {\n            let position = 'static', transform = '', style;\n            let parentNode = null, transformNode = null;\n            let node = this.directiveElementRef.nativeElement.parentNode;\n            const dialogHeight = this.dialogElement.nativeElement.offsetHeight;\n            while (node !== null && node.tagName !== 'HTML') {\n                style = window.getComputedStyle(node);\n                position = style.getPropertyValue('position');\n                transform = style.getPropertyValue('transform');\n                if (position !== 'static' && parentNode === null) {\n                    parentNode = node;\n                }\n                if (transform && transform !== 'none' && transformNode === null) {\n                    transformNode = node;\n                }\n                if (position === 'fixed') {\n                    parentNode = transformNode;\n                    break;\n                }\n                node = node.parentNode;\n            }\n            const boxDirective = this.createDialogBox(this.directiveElementRef.nativeElement, (position !== 'fixed'));\n            if (this.useRootViewContainer || (position === 'fixed' &&\n                (!parentNode || parentNode instanceof HTMLUnknownElement))) {\n                this.top = boxDirective.top;\n                this.left = boxDirective.left;\n            }\n            else {\n                if (parentNode === null) {\n                    parentNode = node;\n                }\n                const boxParent = this.createDialogBox(parentNode, (position !== 'fixed'));\n                this.top = boxDirective.top - boxParent.top;\n                this.left = boxDirective.left - boxParent.left;\n            }\n            if (position === 'fixed') {\n                this.position = 'fixed';\n            }\n            let usePosition = this.cpPosition;\n            const dialogBounds = this.dialogElement.nativeElement.getBoundingClientRect();\n            if (this.cpPosition === 'auto') {\n                const triggerBounds = this.cpTriggerElement.nativeElement.getBoundingClientRect();\n                usePosition = calculateAutoPositioning(dialogBounds, triggerBounds);\n            }\n            this.arrowTop = usePosition === 'top'\n                ? dialogHeight - 1\n                : undefined;\n            this.cpArrowPosition = undefined;\n            switch (usePosition) {\n                case 'top':\n                    this.top -= dialogHeight + this.dialogArrowSize;\n                    this.left += this.cpPositionOffset / 100 * boxDirective.width - this.dialogArrowOffset;\n                    break;\n                case 'bottom':\n                    this.top += boxDirective.height + this.dialogArrowSize;\n                    this.left += this.cpPositionOffset / 100 * boxDirective.width - this.dialogArrowOffset;\n                    break;\n                case 'top-left':\n                case 'left-top':\n                    this.top -= dialogHeight - boxDirective.height + boxDirective.height * this.cpPositionOffset / 100;\n                    this.left -= this.cpWidth + this.dialogArrowSize - 2 - this.dialogArrowOffset;\n                    break;\n                case 'top-right':\n                case 'right-top':\n                    this.top -= dialogHeight - boxDirective.height + boxDirective.height * this.cpPositionOffset / 100;\n                    this.left += boxDirective.width + this.dialogArrowSize - 2 - this.dialogArrowOffset;\n                    break;\n                case 'left':\n                case 'bottom-left':\n                case 'left-bottom':\n                    this.top += boxDirective.height * this.cpPositionOffset / 100 - this.dialogArrowOffset;\n                    this.left -= this.cpWidth + this.dialogArrowSize - 2;\n                    break;\n                case 'right':\n                case 'bottom-right':\n                case 'right-bottom':\n                default:\n                    this.top += boxDirective.height * this.cpPositionOffset / 100 - this.dialogArrowOffset;\n                    this.left += boxDirective.width + this.dialogArrowSize - 2;\n                    break;\n            }\n            const windowInnerHeight = window.innerHeight;\n            const windowInnerWidth = window.innerWidth;\n            const elRefClientRect = this.elRef.nativeElement.getBoundingClientRect();\n            const bottom = this.top + dialogBounds.height;\n            if (bottom > windowInnerHeight) {\n                this.top = windowInnerHeight - dialogBounds.height;\n                this.cpArrowPosition = elRefClientRect.x / 2 - 20;\n            }\n            const right = this.left + dialogBounds.width;\n            if (right > windowInnerWidth) {\n                this.left = windowInnerWidth - dialogBounds.width;\n                this.cpArrowPosition = elRefClientRect.x / 2 - 20;\n            }\n            this.cpUsePosition = usePosition;\n        }\n    }\n    // Private helper functions for the color picker dialog positioning and opening\n    isDescendant(parent, child) {\n        let node = child.parentNode;\n        while (node !== null) {\n            if (node === parent) {\n                return true;\n            }\n            node = node.parentNode;\n        }\n        return false;\n    }\n    createDialogBox(element, offset) {\n        const { top, left } = element.getBoundingClientRect();\n        return {\n            top: top + (offset ? window.pageYOffset : 0),\n            left: left + (offset ? window.pageXOffset : 0),\n            width: element.offsetWidth,\n            height: element.offsetHeight\n        };\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.2\", ngImport: i0, type: ColorPickerComponent, deps: [{ token: i0.NgZone }, { token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: DOCUMENT }, { token: PLATFORM_ID }, { token: ColorPickerService }], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.0.2\", type: ColorPickerComponent, selector: \"color-picker\", host: { listeners: { \"document:keyup.esc\": \"handleEsc($event)\", \"document:keyup.enter\": \"handleEnter($event)\" } }, viewQueries: [{ propertyName: \"dialogElement\", first: true, predicate: [\"dialogPopup\"], descendants: true, static: true }, { propertyName: \"hueSlider\", first: true, predicate: [\"hueSlider\"], descendants: true, static: true }, { propertyName: \"alphaSlider\", first: true, predicate: [\"alphaSlider\"], descendants: true, static: true }], ngImport: i0, template: \"<div #dialogPopup class=\\\"color-picker\\\" [class.open]=\\\"show\\\" [style.display]=\\\"!show ? 'none' : 'block'\\\" [style.visibility]=\\\"hidden ? 'hidden' : 'visible'\\\" [style.top.px]=\\\"top\\\" [style.left.px]=\\\"left\\\" [style.position]=\\\"position\\\" [style.height.px]=\\\"cpHeight\\\" [style.width.px]=\\\"cpWidth\\\" (click)=\\\"$event.stopPropagation()\\\">\\n  <div *ngIf=\\\"cpDialogDisplay === 'popup'\\\" [style.left]=\\\"cpArrowPosition\\\" class=\\\"arrow arrow-{{cpUsePosition}}\\\" [style.top.px]=\\\"arrowTop\\\"></div>\\n\\n  <div *ngIf=\\\"(cpColorMode ||\\u00A01) === 1\\\" class=\\\"saturation-lightness\\\" [slider] [rgX]=\\\"1\\\" [rgY]=\\\"1\\\" [style.background-color]=\\\"hueSliderColor\\\" (newValue)=\\\"onColorChange($event)\\\" (dragStart)=\\\"onDragStart('saturation-lightness')\\\" (dragEnd)=\\\"onDragEnd('saturation-lightness')\\\">\\n    <div class=\\\"cursor\\\" [style.top.px]=\\\"slider?.v\\\" [style.left.px]=\\\"slider?.s\\\"></div>\\n  </div>\\n\\n  <div class=\\\"hue-alpha box\\\">\\n    <div class=\\\"left\\\">\\n      <div class=\\\"selected-color-background\\\"></div>\\n\\n      <div class=\\\"selected-color\\\" [style.background-color]=\\\"selectedColor\\\" [style.cursor]=\\\"eyeDropperSupported && cpEyeDropper ? 'pointer' : null\\\" (click)=\\\"eyeDropperSupported && cpEyeDropper && onEyeDropper()\\\">\\n        <svg *ngIf=\\\"eyeDropperSupported && cpEyeDropper\\\" class=\\\"eyedropper-icon\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" height=\\\"24px\\\" viewBox=\\\"0 0 24 24\\\" width=\\\"24px\\\" fill=\\\"#000000\\\"><path d=\\\"M0 0h24v24H0V0z\\\" fill=\\\"none\\\"/><path d=\\\"M17.66 5.41l.92.92-2.69 2.69-.92-.92 2.69-2.69M17.67 3c-.26 0-.51.1-.71.29l-3.12 3.12-1.93-1.91-1.41 1.41 1.42 1.42L3 16.25V21h4.75l8.92-8.92 1.42 1.42 1.41-1.41-1.92-1.92 3.12-3.12c.4-.4.4-1.03.01-1.42l-2.34-2.34c-.2-.19-.45-.29-.7-.29zM6.92 19L5 17.08l8.06-8.06 1.92 1.92L6.92 19z\\\"/></svg>\\n      </div>\\n\\n      <button *ngIf=\\\"cpAddColorButton\\\" type=\\\"button\\\" class=\\\"{{cpAddColorButtonClass}}\\\" [disabled]=\\\"cpPresetColors && cpPresetColors.length >= cpMaxPresetColorsLength\\\" (click)=\\\"onAddPresetColor($event, selectedColor)\\\">\\n        {{cpAddColorButtonText}}\\n      </button>\\n    </div>\\n\\n    <div class=\\\"right\\\">\\n      <div *ngIf=\\\"cpAlphaChannel==='disabled'\\\" style=\\\"height: 16px;\\\"></div>\\n\\n      <div #hueSlider class=\\\"hue\\\" [slider] [rgX]=\\\"1\\\" [style.display]=\\\"(cpColorMode ||\\u00A01) === 1 ? 'block' : 'none'\\\" (newValue)=\\\"onHueChange($event)\\\" (dragStart)=\\\"onDragStart('hue')\\\" (dragEnd)=\\\"onDragEnd('hue')\\\">\\n        <div class=\\\"cursor\\\" [style.left.px]=\\\"slider?.h\\\"></div>\\n      </div>\\n\\n      <div #valueSlider class=\\\"value\\\" [slider] [rgX]=\\\"1\\\" [style.display]=\\\"(cpColorMode ||\\u00A01) === 2 ? 'block': 'none'\\\" (newValue)=\\\"onValueChange($event)\\\" (dragStart)=\\\"onDragStart('value')\\\" (dragEnd)=\\\"onDragEnd('value')\\\">\\n        <div class=\\\"cursor\\\" [style.right.px]=\\\"slider?.v\\\"></div>\\n      </div>\\n\\n      <div #alphaSlider class=\\\"alpha\\\" [slider] [rgX]=\\\"1\\\" [style.display]=\\\"cpAlphaChannel === 'disabled' ? 'none' : 'block'\\\" [style.background-color]=\\\"alphaSliderColor\\\" (newValue)=\\\"onAlphaChange($event)\\\" (dragStart)=\\\"onDragStart('alpha')\\\" (dragEnd)=\\\"onDragEnd('alpha')\\\">\\n        <div class=\\\"cursor\\\" [style.left.px]=\\\"slider?.a\\\"></div>\\n      </div>\\n    </div>\\n  </div>\\n\\n  <div *ngIf=\\\"!cpDisableInput && (cpColorMode ||\\u00A01) === 1\\\" class=\\\"cmyk-text\\\" [style.display]=\\\"format !== 3 ? 'none' : 'block'\\\">\\n    <div class=\\\"box\\\">\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"100\\\" [text] [rg]=\\\"100\\\" [value]=\\\"cmykText?.c\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onCyanInput($event)\\\" />\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"100\\\" [text] [rg]=\\\"100\\\" [value]=\\\"cmykText?.m\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onMagentaInput($event)\\\" />\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"100\\\" [text] [rg]=\\\"100\\\" [value]=\\\"cmykText?.y\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onYellowInput($event)\\\" />\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"100\\\" [text] [rg]=\\\"100\\\" [value]=\\\"cmykText?.k\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onBlackInput($event)\\\" />\\n      <input *ngIf=\\\"cpAlphaChannel!=='disabled'\\\" type=\\\"number\\\" pattern=\\\"[0-9]+([\\\\.,][0-9]{1,2})?\\\" min=\\\"0\\\" max=\\\"1\\\" step=\\\"0.1\\\" [text] [rg]=\\\"1\\\" [value]=\\\"cmykText?.a\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onAlphaInput($event)\\\" />\\n    </div>\\n\\n     <div class=\\\"box\\\">\\n      <div>C</div><div>M</div><div>Y</div><div>K</div><div *ngIf=\\\"cpAlphaChannel!=='disabled'\\\" >A</div>\\n    </div>\\n  </div>\\n\\n  <div *ngIf=\\\"!cpDisableInput && (cpColorMode ||\\u00A01) === 1 \\\" class=\\\"hsla-text\\\" [style.display]=\\\"format !== 2 ? 'none' : 'block'\\\">\\n    <div class=\\\"box\\\">\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"360\\\" [text] [rg]=\\\"360\\\" [value]=\\\"hslaText?.h\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onHueInput($event)\\\" />\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"100\\\" [text] [rg]=\\\"100\\\" [value]=\\\"hslaText?.s\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onSaturationInput($event)\\\" />\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"100\\\" [text] [rg]=\\\"100\\\" [value]=\\\"hslaText?.l\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onLightnessInput($event)\\\" />\\n      <input *ngIf=\\\"cpAlphaChannel!=='disabled'\\\" type=\\\"number\\\" pattern=\\\"[0-9]+([\\\\.,][0-9]{1,2})?\\\" min=\\\"0\\\" max=\\\"1\\\" step=\\\"0.1\\\" [text] [rg]=\\\"1\\\" [value]=\\\"hslaText?.a\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onAlphaInput($event)\\\" />\\n    </div>\\n\\n    <div class=\\\"box\\\">\\n      <div>H</div><div>S</div><div>L</div><div *ngIf=\\\"cpAlphaChannel!=='disabled'\\\">A</div>\\n    </div>\\n  </div>\\n\\n  <div *ngIf=\\\"!cpDisableInput && (cpColorMode ||\\u00A01) === 1 \\\" [style.display]=\\\"format !== 1 ? 'none' : 'block'\\\" class=\\\"rgba-text\\\">\\n    <div class=\\\"box\\\">\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"255\\\" [text] [rg]=\\\"255\\\" [value]=\\\"rgbaText?.r\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onRedInput($event)\\\" />\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"255\\\" [text] [rg]=\\\"255\\\" [value]=\\\"rgbaText?.g\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onGreenInput($event)\\\" />\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"255\\\" [text] [rg]=\\\"255\\\" [value]=\\\"rgbaText?.b\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onBlueInput($event)\\\" />\\n      <input *ngIf=\\\"cpAlphaChannel!=='disabled'\\\" type=\\\"number\\\" pattern=\\\"[0-9]+([\\\\.,][0-9]{1,2})?\\\" min=\\\"0\\\" max=\\\"1\\\" step=\\\"0.1\\\" [text] [rg]=\\\"1\\\" [value]=\\\"rgbaText?.a\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onAlphaInput($event)\\\" />\\n    </div>\\n\\n    <div class=\\\"box\\\">\\n      <div>R</div><div>G</div><div>B</div><div *ngIf=\\\"cpAlphaChannel!=='disabled'\\\" >A</div>\\n    </div>\\n  </div>\\n\\n  <div *ngIf=\\\"!cpDisableInput && (cpColorMode ||\\u00A01) === 1\\\" class=\\\"hex-text\\\" [class.hex-alpha]=\\\"cpAlphaChannel==='forced'\\\"\\n    [style.display]=\\\"format !== 0 ? 'none' : 'block'\\\">\\n    <div class=\\\"box\\\">\\n      <input [text] [value]=\\\"hexText\\\" (blur)=\\\"onHexInput(null)\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onHexInput($event)\\\"/>\\n      <input *ngIf=\\\"cpAlphaChannel==='forced'\\\" type=\\\"number\\\" pattern=\\\"[0-9]+([\\\\.,][0-9]{1,2})?\\\" min=\\\"0\\\" max=\\\"1\\\" step=\\\"0.1\\\" [text] [rg]=\\\"1\\\" [value]=\\\"hexAlpha\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onAlphaInput($event)\\\"/>\\n    </div>\\n\\n    <div class=\\\"box\\\">\\n      <div>Hex</div>\\n      <div *ngIf=\\\"cpAlphaChannel==='forced'\\\">A</div>\\n    </div>\\n  </div>\\n\\n  <div *ngIf=\\\"!cpDisableInput && (cpColorMode ||\\u00A01) === 2\\\" class=\\\"value-text\\\">\\n    <div class=\\\"box\\\">\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"100\\\" [text] [rg]=\\\"100\\\" [value]=\\\"hslaText?.l\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onValueInput($event)\\\" />\\n      <input *ngIf=\\\"cpAlphaChannel!=='disabled'\\\" type=\\\"number\\\" pattern=\\\"[0-9]+([\\\\.,][0-9]{1,2})?\\\" min=\\\"0\\\" max=\\\"1\\\" step=\\\"0.1\\\"  [text] [rg]=\\\"1\\\" [value]=\\\"hslaText?.a\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onAlphaInput($event)\\\" />\\n    </div>\\n\\n    <div class=\\\"box\\\">\\n      <div>V</div><div>A</div>\\n    </div>\\n  </div>\\n\\n  <div *ngIf=\\\"!cpDisableInput && (cpColorMode ||\\u00A01) === 1\\\" class=\\\"type-policy\\\">\\n    <span class=\\\"type-policy-arrow\\\" (click)=\\\"onFormatToggle(-1)\\\"></span>\\n    <span class=\\\"type-policy-arrow\\\" (click)=\\\"onFormatToggle(1)\\\"></span>\\n  </div>\\n\\n  <div *ngIf=\\\"cpPresetColors?.length || cpAddColorButton\\\" class=\\\"preset-area\\\">\\n    <hr>\\n\\n    <div class=\\\"preset-label\\\">{{cpPresetLabel}}</div>\\n\\n    <div *ngIf=\\\"cpPresetColors?.length\\\" class=\\\"{{cpPresetColorsClass}}\\\">\\n      <div *ngFor=\\\"let color of cpPresetColors\\\" class=\\\"preset-color\\\" [style.backgroundColor]=\\\"color\\\" (click)=\\\"setColorFromString(color)\\\">\\n        <span *ngIf=\\\"cpAddColorButton\\\" class=\\\"{{cpRemoveColorButtonClass}}\\\" (click)=\\\"onRemovePresetColor($event, color)\\\"></span>\\n      </div>\\n    </div>\\n\\n    <div *ngIf=\\\"!cpPresetColors?.length && cpAddColorButton\\\" class=\\\"{{cpPresetEmptyMessageClass}}\\\">{{cpPresetEmptyMessage}}</div>\\n  </div>\\n\\n  <div *ngIf=\\\"cpOKButton || cpCancelButton\\\" class=\\\"button-area\\\">\\n    <button *ngIf=\\\"cpCancelButton\\\" type=\\\"button\\\" class=\\\"{{cpCancelButtonClass}}\\\" (click)=\\\"onCancelColor($event)\\\">{{cpCancelButtonText}}</button>\\n\\n    <button *ngIf=\\\"cpOKButton\\\" type=\\\"button\\\" class=\\\"{{cpOKButtonClass}}\\\" (click)=\\\"onAcceptColor($event)\\\">{{cpOKButtonText}}</button>\\n  </div>\\n\\n  <div class=\\\"extra-template\\\" *ngIf=\\\"cpExtraTemplate\\\">\\n    <ng-container *ngTemplateOutlet=\\\"cpExtraTemplate\\\"></ng-container>\\n  </div>\\n</div>\\n\", styles: [\".color-picker{position:absolute;z-index:1000;width:230px;height:auto;border:#777 solid 1px;cursor:default;-webkit-user-select:none;user-select:none;background-color:#fff}.color-picker *{box-sizing:border-box;margin:0;font-size:11px}.color-picker input{width:0;height:26px;min-width:0;font-size:13px;text-align:center;color:#000}.color-picker input:invalid,.color-picker input:-moz-ui-invalid,.color-picker input:-moz-submit-invalid{box-shadow:none}.color-picker input::-webkit-inner-spin-button,.color-picker input::-webkit-outer-spin-button{margin:0;-webkit-appearance:none}.color-picker .arrow{position:absolute;z-index:999999;width:0;height:0;border-style:solid}.color-picker .arrow.arrow-top{left:8px;border-width:10px 5px;border-color:#777 rgba(0,0,0,0) rgba(0,0,0,0) rgba(0,0,0,0)}.color-picker .arrow.arrow-bottom{top:-20px;left:8px;border-width:10px 5px;border-color:rgba(0,0,0,0) rgba(0,0,0,0) #777 rgba(0,0,0,0)}.color-picker .arrow.arrow-top-left,.color-picker .arrow.arrow-left-top{right:-21px;bottom:8px;border-width:5px 10px;border-color:rgba(0,0,0,0) rgba(0,0,0,0) rgba(0,0,0,0) #777}.color-picker .arrow.arrow-top-right,.color-picker .arrow.arrow-right-top{bottom:8px;left:-20px;border-width:5px 10px;border-color:rgba(0,0,0,0) #777 rgba(0,0,0,0) rgba(0,0,0,0)}.color-picker .arrow.arrow-left,.color-picker .arrow.arrow-left-bottom,.color-picker .arrow.arrow-bottom-left{top:8px;right:-21px;border-width:5px 10px;border-color:rgba(0,0,0,0) rgba(0,0,0,0) rgba(0,0,0,0) #777}.color-picker .arrow.arrow-right,.color-picker .arrow.arrow-right-bottom,.color-picker .arrow.arrow-bottom-right{top:8px;left:-20px;border-width:5px 10px;border-color:rgba(0,0,0,0) #777 rgba(0,0,0,0) rgba(0,0,0,0)}.color-picker .cursor{position:relative;width:16px;height:16px;border:#222 solid 2px;border-radius:50%;cursor:default}.color-picker .box{display:flex;padding:4px 8px}.color-picker .left{position:relative;padding:16px 8px}.color-picker .right{flex:1 1 auto;padding:12px 8px}.color-picker .button-area{padding:0 16px 16px;text-align:right}.color-picker .button-area button{margin-left:8px}.color-picker .preset-area{padding:4px 15px}.color-picker .preset-area .preset-label{overflow:hidden;width:100%;padding:4px;font-size:11px;white-space:nowrap;text-align:left;text-overflow:ellipsis;color:#555}.color-picker .preset-area .preset-color{position:relative;display:inline-block;width:18px;height:18px;margin:4px 6px 8px;border:#a9a9a9 solid 1px;border-radius:25%;cursor:pointer}.color-picker .preset-area .preset-empty-message{min-height:18px;margin-top:4px;margin-bottom:8px;font-style:italic;text-align:center}.color-picker .hex-text{width:100%;padding:4px 8px;font-size:11px}.color-picker .hex-text .box{padding:0 24px 8px 8px}.color-picker .hex-text .box div{float:left;flex:1 1 auto;text-align:center;color:#555;clear:left}.color-picker .hex-text .box input{flex:1 1 auto;padding:1px;border:#a9a9a9 solid 1px}.color-picker .hex-alpha .box div:first-child,.color-picker .hex-alpha .box input:first-child{flex-grow:3;margin-right:8px}.color-picker .cmyk-text,.color-picker .hsla-text,.color-picker .rgba-text,.color-picker .value-text{width:100%;padding:4px 8px;font-size:11px}.color-picker .cmyk-text .box,.color-picker .hsla-text .box,.color-picker .rgba-text .box{padding:0 24px 8px 8px}.color-picker .value-text .box{padding:0 8px 8px}.color-picker .cmyk-text .box div,.color-picker .hsla-text .box div,.color-picker .rgba-text .box div,.color-picker .value-text .box div{flex:1 1 auto;margin-right:8px;text-align:center;color:#555}.color-picker .cmyk-text .box div:last-child,.color-picker .hsla-text .box div:last-child,.color-picker .rgba-text .box div:last-child,.color-picker .value-text .box div:last-child{margin-right:0}.color-picker .cmyk-text .box input,.color-picker .hsla-text .box input,.color-picker .rgba-text .box input,.color-picker .value-text .box input{float:left;flex:1;padding:1px;margin:0 8px 0 0;border:#a9a9a9 solid 1px}.color-picker .cmyk-text .box input:last-child,.color-picker .hsla-text .box input:last-child,.color-picker .rgba-text .box input:last-child,.color-picker .value-text .box input:last-child{margin-right:0}.color-picker .hue-alpha{align-items:center;margin-bottom:3px}.color-picker .hue{direction:ltr;width:100%;height:16px;margin-bottom:16px;border:none;cursor:pointer;background-size:100% 100%;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJYAAAAQCAYAAAD06IYnAAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH4AIWDwkUFWbCCAAAAFxJREFUaN7t0kEKg0AQAME2x83/n2qu5qCgD1iDhCoYdpnbQC9bbY1qVO/jvc6k3ad91s7/7F1/csgPrujuQ17BDYSFsBAWwgJhISyEBcJCWAgLhIWwEBYIi2f7Ar/1TCgFH2X9AAAAAElFTkSuQmCC)}.color-picker .value{direction:rtl;width:100%;height:16px;margin-bottom:16px;border:none;cursor:pointer;background-size:100% 100%;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJYAAAAQCAYAAAD06IYnAAACTklEQVR42u3SYUcrABhA4U2SkmRJMmWSJklKJiWZZpKUJJskKUmaTFImKZOUzMySpGRmliRNJilJSpKSJEtmSpIpmWmSdO736/6D+x7OP3gUCoWCv1cqlSQlJZGcnExKSgqpqamkpaWRnp5ORkYGmZmZqFQqsrKyyM7OJicnh9zcXNRqNXl5eeTn56PRaCgoKKCwsJCioiK0Wi3FxcWUlJRQWlpKWVkZ5eXlVFRUUFlZiU6no6qqiurqampqaqitraWurg69Xk99fT0GgwGj0UhDQwONjY00NTXR3NxMS0sLra2ttLW10d7ejslkwmw209HRQWdnJ11dXXR3d9PT00Nvby99fX309/czMDDA4OAgFouFoaEhrFYrw8PDjIyMMDo6ytjYGDabjfHxcSYmJpicnGRqagq73c709DQzMzPMzs4yNzfH/Pw8DocDp9OJy+XC7XazsLDA4uIiS0tLLC8vs7KywurqKmtra3g8HrxeLz6fD7/fz/r6OhsbG2xubrK1tcX29jaBQICdnR2CwSC7u7vs7e2xv7/PwcEBh4eHHB0dcXx8zMnJCaenp5ydnXF+fs7FxQWXl5dcXV1xfX3Nzc0Nt7e33N3dEQqFuL+/5+HhgXA4TCQS4fHxkaenJ56fn3l5eeH19ZVoNMrb2xvv7+98fHwQi8WIx+N8fn6SSCT4+vri+/ubn58ffn9/+VcKgSWwBJbAElgCS2AJLIElsASWwBJYAktgCSyBJbAElsASWAJLYAksgSWwBJbAElgCS2AJLIElsP4/WH8AmJ5Z6jHS4h8AAAAASUVORK5CYII=)}.color-picker .alpha{direction:ltr;width:100%;height:16px;border:none;cursor:pointer;background-size:100% 100%;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJYAAAAQCAYAAAD06IYnAAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH4AIWDwYQlZMa3gAAAWVJREFUaN7tmEGO6jAQRCsOArHgBpyAJYGjcGocxAm4A2IHpmoWE0eBH+ezmFlNvU06shJ3W6VEelWMUQAIIF9f6qZpimsA1LYtS2uF51/u27YVAFZVRUkEoGHdPV/sIcbIEIIkUdI/9Xa7neyv61+SWFUVAVCSct00TWn2fv6u3+Ecfd3tXzy/0+nEUu+SPjo/kqzrmiQpScN6v98XewfA8/lMkiLJ2WxGSUopcT6fM6U0NX9/frfbjev1WtfrlZfLhYfDQQHG/AIOlnGwjINlHCxjHCzjYJm/TJWdCwquJXseFFzGwDNNeiKMOJTO8xQdDQaeB29+K9efeLaBo9J7vdvtJj1RjFFjfiv7qv95tjx/7leSQgh93e1ffMeIp6O+YQjho/N791t1XVOSSI7N//K+4/GoxWLBx+PB5/Op5XLJ+/3OlJJWqxU3m83ovv5iGf8KjYNlHCxjHCzjYBkHy5gf5gusvQU7U37jTAAAAABJRU5ErkJggg==)}.color-picker .type-policy{position:absolute;top:218px;right:12px;width:16px;height:24px;background-size:8px 16px;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAgCAYAAAAffCjxAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAACewAAAnsB01CO3AAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAIASURBVEiJ7ZY9axRRFIafsxMStrLQJpAgpBFhi+C9w1YSo00I6RZ/g9vZpBf/QOr4GyRgkSKNSrAadsZqQGwCkuAWyRZJsySwvhZ7N/vhzrgbLH3Ld8597jlzz50zJokyxXH8DqDVar0qi6v8BbItqSGpEcfxdlmsFWXkvX8AfAVWg3UKPEnT9GKujMzsAFgZsVaCN1VTQd77XUnrgE1kv+6935268WRpzrnHZvYRWC7YvC3pRZZl3wozqtVqiyH9IgjAspkd1Gq1xUJQtVrdB9ZKIAOthdg/Qc65LUk7wNIMoCVJO865rYFhkqjX6/d7vV4GPJwBMqofURS5JEk6FYBer/eeYb/Mo9WwFnPOvQbeAvfuAAK4BN4sAJtAG/gJIElmNuiJyba3EGNmZiPeZuEVmVell/Y/6N+CzDn3AXhEOOo7Hv/3BeAz8IzQkMPnJbuPx1wC+yYJ7/0nYIP5S/0FHKdp+rwCEEXRS/rf5Hl1Gtb2M0iSpCOpCZzPATmX1EySpHMLAsiy7MjMDoHrGSDXZnaYZdnRwBh7J91utwmczAA6CbG3GgPleX4jqUH/a1CktqRGnuc3hSCAMB32gKspkCtgb3KCQMmkjeP4WNJThrNNZval1WptTIsv7JtQ4tmIdRa8qSoEpWl6YWZNoAN0zKxZNPehpLSBZv2t+Q0CJ9lLnARQLAAAAABJRU5ErkJggg==);background-repeat:no-repeat;background-position:center}.color-picker .type-policy .type-policy-arrow{display:block;width:100%;height:50%}.color-picker .selected-color{position:absolute;top:16px;left:8px;width:40px;height:40px;border:1px solid #a9a9a9;border-radius:50%}.color-picker .selected-color-background{width:40px;height:40px;border-radius:50%;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAh0lEQVRYR+2W0QlAMQgD60zdfwOdqa8TmI/wQMr5K0I5bZLIzLOa2nt37VVVbd+dDx5obgCC3KBLwJ2ff4PnVidkf+ucIhw80HQaCLo3DMH3CRK3iFsmAWVl6hPNDwt8EvNE5q+YuEXcMgkonVM6SdyCoEvAnZ8v1Hjx817MilmxSUB5rdLJDycZgUAZUch/AAAAAElFTkSuQmCC)}.color-picker .saturation-lightness{direction:ltr;width:100%;height:130px;border:none;cursor:pointer;touch-action:manipulation;background-size:100% 100%;background-image:url(data:image/png;base64,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)}.color-picker .cp-add-color-button-class{position:absolute;display:inline;padding:0;margin:3px -3px;border:0;cursor:pointer;background:transparent}.color-picker .cp-add-color-button-class:hover{text-decoration:underline}.color-picker .cp-add-color-button-class:disabled{cursor:not-allowed;color:#999}.color-picker .cp-add-color-button-class:disabled:hover{text-decoration:none}.color-picker .cp-remove-color-button-class{position:absolute;top:-5px;right:-5px;display:block;width:10px;height:10px;border-radius:50%;cursor:pointer;text-align:center;background:#fff;box-shadow:1px 1px 5px #333}.color-picker .cp-remove-color-button-class:before{content:\\\"x\\\";position:relative;bottom:3.5px;display:inline-block;font-size:10px}.color-picker .eyedropper-icon{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);fill:#fff;mix-blend-mode:exclusion}\\n\"], dependencies: [{ kind: \"directive\", type: i2.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i2.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i2.NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: TextDirective, selector: \"[text]\", inputs: [\"rg\", \"text\"], outputs: [\"newValue\"] }, { kind: \"directive\", type: SliderDirective, selector: \"[slider]\", inputs: [\"rgX\", \"rgY\", \"slider\"], outputs: [\"dragEnd\", \"dragStart\", \"newValue\"] }], encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.2\", ngImport: i0, type: ColorPickerComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'color-picker', encapsulation: ViewEncapsulation.None, template: \"<div #dialogPopup class=\\\"color-picker\\\" [class.open]=\\\"show\\\" [style.display]=\\\"!show ? 'none' : 'block'\\\" [style.visibility]=\\\"hidden ? 'hidden' : 'visible'\\\" [style.top.px]=\\\"top\\\" [style.left.px]=\\\"left\\\" [style.position]=\\\"position\\\" [style.height.px]=\\\"cpHeight\\\" [style.width.px]=\\\"cpWidth\\\" (click)=\\\"$event.stopPropagation()\\\">\\n  <div *ngIf=\\\"cpDialogDisplay === 'popup'\\\" [style.left]=\\\"cpArrowPosition\\\" class=\\\"arrow arrow-{{cpUsePosition}}\\\" [style.top.px]=\\\"arrowTop\\\"></div>\\n\\n  <div *ngIf=\\\"(cpColorMode ||\\u00A01) === 1\\\" class=\\\"saturation-lightness\\\" [slider] [rgX]=\\\"1\\\" [rgY]=\\\"1\\\" [style.background-color]=\\\"hueSliderColor\\\" (newValue)=\\\"onColorChange($event)\\\" (dragStart)=\\\"onDragStart('saturation-lightness')\\\" (dragEnd)=\\\"onDragEnd('saturation-lightness')\\\">\\n    <div class=\\\"cursor\\\" [style.top.px]=\\\"slider?.v\\\" [style.left.px]=\\\"slider?.s\\\"></div>\\n  </div>\\n\\n  <div class=\\\"hue-alpha box\\\">\\n    <div class=\\\"left\\\">\\n      <div class=\\\"selected-color-background\\\"></div>\\n\\n      <div class=\\\"selected-color\\\" [style.background-color]=\\\"selectedColor\\\" [style.cursor]=\\\"eyeDropperSupported && cpEyeDropper ? 'pointer' : null\\\" (click)=\\\"eyeDropperSupported && cpEyeDropper && onEyeDropper()\\\">\\n        <svg *ngIf=\\\"eyeDropperSupported && cpEyeDropper\\\" class=\\\"eyedropper-icon\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" height=\\\"24px\\\" viewBox=\\\"0 0 24 24\\\" width=\\\"24px\\\" fill=\\\"#000000\\\"><path d=\\\"M0 0h24v24H0V0z\\\" fill=\\\"none\\\"/><path d=\\\"M17.66 5.41l.92.92-2.69 2.69-.92-.92 2.69-2.69M17.67 3c-.26 0-.51.1-.71.29l-3.12 3.12-1.93-1.91-1.41 1.41 1.42 1.42L3 16.25V21h4.75l8.92-8.92 1.42 1.42 1.41-1.41-1.92-1.92 3.12-3.12c.4-.4.4-1.03.01-1.42l-2.34-2.34c-.2-.19-.45-.29-.7-.29zM6.92 19L5 17.08l8.06-8.06 1.92 1.92L6.92 19z\\\"/></svg>\\n      </div>\\n\\n      <button *ngIf=\\\"cpAddColorButton\\\" type=\\\"button\\\" class=\\\"{{cpAddColorButtonClass}}\\\" [disabled]=\\\"cpPresetColors && cpPresetColors.length >= cpMaxPresetColorsLength\\\" (click)=\\\"onAddPresetColor($event, selectedColor)\\\">\\n        {{cpAddColorButtonText}}\\n      </button>\\n    </div>\\n\\n    <div class=\\\"right\\\">\\n      <div *ngIf=\\\"cpAlphaChannel==='disabled'\\\" style=\\\"height: 16px;\\\"></div>\\n\\n      <div #hueSlider class=\\\"hue\\\" [slider] [rgX]=\\\"1\\\" [style.display]=\\\"(cpColorMode ||\\u00A01) === 1 ? 'block' : 'none'\\\" (newValue)=\\\"onHueChange($event)\\\" (dragStart)=\\\"onDragStart('hue')\\\" (dragEnd)=\\\"onDragEnd('hue')\\\">\\n        <div class=\\\"cursor\\\" [style.left.px]=\\\"slider?.h\\\"></div>\\n      </div>\\n\\n      <div #valueSlider class=\\\"value\\\" [slider] [rgX]=\\\"1\\\" [style.display]=\\\"(cpColorMode ||\\u00A01) === 2 ? 'block': 'none'\\\" (newValue)=\\\"onValueChange($event)\\\" (dragStart)=\\\"onDragStart('value')\\\" (dragEnd)=\\\"onDragEnd('value')\\\">\\n        <div class=\\\"cursor\\\" [style.right.px]=\\\"slider?.v\\\"></div>\\n      </div>\\n\\n      <div #alphaSlider class=\\\"alpha\\\" [slider] [rgX]=\\\"1\\\" [style.display]=\\\"cpAlphaChannel === 'disabled' ? 'none' : 'block'\\\" [style.background-color]=\\\"alphaSliderColor\\\" (newValue)=\\\"onAlphaChange($event)\\\" (dragStart)=\\\"onDragStart('alpha')\\\" (dragEnd)=\\\"onDragEnd('alpha')\\\">\\n        <div class=\\\"cursor\\\" [style.left.px]=\\\"slider?.a\\\"></div>\\n      </div>\\n    </div>\\n  </div>\\n\\n  <div *ngIf=\\\"!cpDisableInput && (cpColorMode ||\\u00A01) === 1\\\" class=\\\"cmyk-text\\\" [style.display]=\\\"format !== 3 ? 'none' : 'block'\\\">\\n    <div class=\\\"box\\\">\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"100\\\" [text] [rg]=\\\"100\\\" [value]=\\\"cmykText?.c\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onCyanInput($event)\\\" />\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"100\\\" [text] [rg]=\\\"100\\\" [value]=\\\"cmykText?.m\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onMagentaInput($event)\\\" />\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"100\\\" [text] [rg]=\\\"100\\\" [value]=\\\"cmykText?.y\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onYellowInput($event)\\\" />\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"100\\\" [text] [rg]=\\\"100\\\" [value]=\\\"cmykText?.k\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onBlackInput($event)\\\" />\\n      <input *ngIf=\\\"cpAlphaChannel!=='disabled'\\\" type=\\\"number\\\" pattern=\\\"[0-9]+([\\\\.,][0-9]{1,2})?\\\" min=\\\"0\\\" max=\\\"1\\\" step=\\\"0.1\\\" [text] [rg]=\\\"1\\\" [value]=\\\"cmykText?.a\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onAlphaInput($event)\\\" />\\n    </div>\\n\\n     <div class=\\\"box\\\">\\n      <div>C</div><div>M</div><div>Y</div><div>K</div><div *ngIf=\\\"cpAlphaChannel!=='disabled'\\\" >A</div>\\n    </div>\\n  </div>\\n\\n  <div *ngIf=\\\"!cpDisableInput && (cpColorMode ||\\u00A01) === 1 \\\" class=\\\"hsla-text\\\" [style.display]=\\\"format !== 2 ? 'none' : 'block'\\\">\\n    <div class=\\\"box\\\">\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"360\\\" [text] [rg]=\\\"360\\\" [value]=\\\"hslaText?.h\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onHueInput($event)\\\" />\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"100\\\" [text] [rg]=\\\"100\\\" [value]=\\\"hslaText?.s\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onSaturationInput($event)\\\" />\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"100\\\" [text] [rg]=\\\"100\\\" [value]=\\\"hslaText?.l\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onLightnessInput($event)\\\" />\\n      <input *ngIf=\\\"cpAlphaChannel!=='disabled'\\\" type=\\\"number\\\" pattern=\\\"[0-9]+([\\\\.,][0-9]{1,2})?\\\" min=\\\"0\\\" max=\\\"1\\\" step=\\\"0.1\\\" [text] [rg]=\\\"1\\\" [value]=\\\"hslaText?.a\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onAlphaInput($event)\\\" />\\n    </div>\\n\\n    <div class=\\\"box\\\">\\n      <div>H</div><div>S</div><div>L</div><div *ngIf=\\\"cpAlphaChannel!=='disabled'\\\">A</div>\\n    </div>\\n  </div>\\n\\n  <div *ngIf=\\\"!cpDisableInput && (cpColorMode ||\\u00A01) === 1 \\\" [style.display]=\\\"format !== 1 ? 'none' : 'block'\\\" class=\\\"rgba-text\\\">\\n    <div class=\\\"box\\\">\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"255\\\" [text] [rg]=\\\"255\\\" [value]=\\\"rgbaText?.r\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onRedInput($event)\\\" />\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"255\\\" [text] [rg]=\\\"255\\\" [value]=\\\"rgbaText?.g\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onGreenInput($event)\\\" />\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"255\\\" [text] [rg]=\\\"255\\\" [value]=\\\"rgbaText?.b\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onBlueInput($event)\\\" />\\n      <input *ngIf=\\\"cpAlphaChannel!=='disabled'\\\" type=\\\"number\\\" pattern=\\\"[0-9]+([\\\\.,][0-9]{1,2})?\\\" min=\\\"0\\\" max=\\\"1\\\" step=\\\"0.1\\\" [text] [rg]=\\\"1\\\" [value]=\\\"rgbaText?.a\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onAlphaInput($event)\\\" />\\n    </div>\\n\\n    <div class=\\\"box\\\">\\n      <div>R</div><div>G</div><div>B</div><div *ngIf=\\\"cpAlphaChannel!=='disabled'\\\" >A</div>\\n    </div>\\n  </div>\\n\\n  <div *ngIf=\\\"!cpDisableInput && (cpColorMode ||\\u00A01) === 1\\\" class=\\\"hex-text\\\" [class.hex-alpha]=\\\"cpAlphaChannel==='forced'\\\"\\n    [style.display]=\\\"format !== 0 ? 'none' : 'block'\\\">\\n    <div class=\\\"box\\\">\\n      <input [text] [value]=\\\"hexText\\\" (blur)=\\\"onHexInput(null)\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onHexInput($event)\\\"/>\\n      <input *ngIf=\\\"cpAlphaChannel==='forced'\\\" type=\\\"number\\\" pattern=\\\"[0-9]+([\\\\.,][0-9]{1,2})?\\\" min=\\\"0\\\" max=\\\"1\\\" step=\\\"0.1\\\" [text] [rg]=\\\"1\\\" [value]=\\\"hexAlpha\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onAlphaInput($event)\\\"/>\\n    </div>\\n\\n    <div class=\\\"box\\\">\\n      <div>Hex</div>\\n      <div *ngIf=\\\"cpAlphaChannel==='forced'\\\">A</div>\\n    </div>\\n  </div>\\n\\n  <div *ngIf=\\\"!cpDisableInput && (cpColorMode ||\\u00A01) === 2\\\" class=\\\"value-text\\\">\\n    <div class=\\\"box\\\">\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"100\\\" [text] [rg]=\\\"100\\\" [value]=\\\"hslaText?.l\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onValueInput($event)\\\" />\\n      <input *ngIf=\\\"cpAlphaChannel!=='disabled'\\\" type=\\\"number\\\" pattern=\\\"[0-9]+([\\\\.,][0-9]{1,2})?\\\" min=\\\"0\\\" max=\\\"1\\\" step=\\\"0.1\\\"  [text] [rg]=\\\"1\\\" [value]=\\\"hslaText?.a\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onAlphaInput($event)\\\" />\\n    </div>\\n\\n    <div class=\\\"box\\\">\\n      <div>V</div><div>A</div>\\n    </div>\\n  </div>\\n\\n  <div *ngIf=\\\"!cpDisableInput && (cpColorMode ||\\u00A01) === 1\\\" class=\\\"type-policy\\\">\\n    <span class=\\\"type-policy-arrow\\\" (click)=\\\"onFormatToggle(-1)\\\"></span>\\n    <span class=\\\"type-policy-arrow\\\" (click)=\\\"onFormatToggle(1)\\\"></span>\\n  </div>\\n\\n  <div *ngIf=\\\"cpPresetColors?.length || cpAddColorButton\\\" class=\\\"preset-area\\\">\\n    <hr>\\n\\n    <div class=\\\"preset-label\\\">{{cpPresetLabel}}</div>\\n\\n    <div *ngIf=\\\"cpPresetColors?.length\\\" class=\\\"{{cpPresetColorsClass}}\\\">\\n      <div *ngFor=\\\"let color of cpPresetColors\\\" class=\\\"preset-color\\\" [style.backgroundColor]=\\\"color\\\" (click)=\\\"setColorFromString(color)\\\">\\n        <span *ngIf=\\\"cpAddColorButton\\\" class=\\\"{{cpRemoveColorButtonClass}}\\\" (click)=\\\"onRemovePresetColor($event, color)\\\"></span>\\n      </div>\\n    </div>\\n\\n    <div *ngIf=\\\"!cpPresetColors?.length && cpAddColorButton\\\" class=\\\"{{cpPresetEmptyMessageClass}}\\\">{{cpPresetEmptyMessage}}</div>\\n  </div>\\n\\n  <div *ngIf=\\\"cpOKButton || cpCancelButton\\\" class=\\\"button-area\\\">\\n    <button *ngIf=\\\"cpCancelButton\\\" type=\\\"button\\\" class=\\\"{{cpCancelButtonClass}}\\\" (click)=\\\"onCancelColor($event)\\\">{{cpCancelButtonText}}</button>\\n\\n    <button *ngIf=\\\"cpOKButton\\\" type=\\\"button\\\" class=\\\"{{cpOKButtonClass}}\\\" (click)=\\\"onAcceptColor($event)\\\">{{cpOKButtonText}}</button>\\n  </div>\\n\\n  <div class=\\\"extra-template\\\" *ngIf=\\\"cpExtraTemplate\\\">\\n    <ng-container *ngTemplateOutlet=\\\"cpExtraTemplate\\\"></ng-container>\\n  </div>\\n</div>\\n\", styles: [\".color-picker{position:absolute;z-index:1000;width:230px;height:auto;border:#777 solid 1px;cursor:default;-webkit-user-select:none;user-select:none;background-color:#fff}.color-picker *{box-sizing:border-box;margin:0;font-size:11px}.color-picker input{width:0;height:26px;min-width:0;font-size:13px;text-align:center;color:#000}.color-picker input:invalid,.color-picker input:-moz-ui-invalid,.color-picker input:-moz-submit-invalid{box-shadow:none}.color-picker input::-webkit-inner-spin-button,.color-picker input::-webkit-outer-spin-button{margin:0;-webkit-appearance:none}.color-picker .arrow{position:absolute;z-index:999999;width:0;height:0;border-style:solid}.color-picker .arrow.arrow-top{left:8px;border-width:10px 5px;border-color:#777 rgba(0,0,0,0) rgba(0,0,0,0) rgba(0,0,0,0)}.color-picker .arrow.arrow-bottom{top:-20px;left:8px;border-width:10px 5px;border-color:rgba(0,0,0,0) rgba(0,0,0,0) #777 rgba(0,0,0,0)}.color-picker .arrow.arrow-top-left,.color-picker .arrow.arrow-left-top{right:-21px;bottom:8px;border-width:5px 10px;border-color:rgba(0,0,0,0) rgba(0,0,0,0) rgba(0,0,0,0) #777}.color-picker .arrow.arrow-top-right,.color-picker .arrow.arrow-right-top{bottom:8px;left:-20px;border-width:5px 10px;border-color:rgba(0,0,0,0) #777 rgba(0,0,0,0) rgba(0,0,0,0)}.color-picker .arrow.arrow-left,.color-picker .arrow.arrow-left-bottom,.color-picker .arrow.arrow-bottom-left{top:8px;right:-21px;border-width:5px 10px;border-color:rgba(0,0,0,0) rgba(0,0,0,0) rgba(0,0,0,0) #777}.color-picker .arrow.arrow-right,.color-picker .arrow.arrow-right-bottom,.color-picker .arrow.arrow-bottom-right{top:8px;left:-20px;border-width:5px 10px;border-color:rgba(0,0,0,0) #777 rgba(0,0,0,0) rgba(0,0,0,0)}.color-picker .cursor{position:relative;width:16px;height:16px;border:#222 solid 2px;border-radius:50%;cursor:default}.color-picker .box{display:flex;padding:4px 8px}.color-picker .left{position:relative;padding:16px 8px}.color-picker .right{flex:1 1 auto;padding:12px 8px}.color-picker .button-area{padding:0 16px 16px;text-align:right}.color-picker .button-area button{margin-left:8px}.color-picker .preset-area{padding:4px 15px}.color-picker .preset-area .preset-label{overflow:hidden;width:100%;padding:4px;font-size:11px;white-space:nowrap;text-align:left;text-overflow:ellipsis;color:#555}.color-picker .preset-area .preset-color{position:relative;display:inline-block;width:18px;height:18px;margin:4px 6px 8px;border:#a9a9a9 solid 1px;border-radius:25%;cursor:pointer}.color-picker .preset-area .preset-empty-message{min-height:18px;margin-top:4px;margin-bottom:8px;font-style:italic;text-align:center}.color-picker .hex-text{width:100%;padding:4px 8px;font-size:11px}.color-picker .hex-text .box{padding:0 24px 8px 8px}.color-picker .hex-text .box div{float:left;flex:1 1 auto;text-align:center;color:#555;clear:left}.color-picker .hex-text .box input{flex:1 1 auto;padding:1px;border:#a9a9a9 solid 1px}.color-picker .hex-alpha .box div:first-child,.color-picker .hex-alpha .box input:first-child{flex-grow:3;margin-right:8px}.color-picker .cmyk-text,.color-picker .hsla-text,.color-picker .rgba-text,.color-picker .value-text{width:100%;padding:4px 8px;font-size:11px}.color-picker .cmyk-text .box,.color-picker .hsla-text .box,.color-picker .rgba-text .box{padding:0 24px 8px 8px}.color-picker .value-text .box{padding:0 8px 8px}.color-picker .cmyk-text .box div,.color-picker .hsla-text .box div,.color-picker .rgba-text .box div,.color-picker .value-text .box div{flex:1 1 auto;margin-right:8px;text-align:center;color:#555}.color-picker .cmyk-text .box div:last-child,.color-picker .hsla-text .box div:last-child,.color-picker .rgba-text .box div:last-child,.color-picker .value-text .box div:last-child{margin-right:0}.color-picker .cmyk-text .box input,.color-picker .hsla-text .box input,.color-picker .rgba-text .box input,.color-picker .value-text .box input{float:left;flex:1;padding:1px;margin:0 8px 0 0;border:#a9a9a9 solid 1px}.color-picker .cmyk-text .box input:last-child,.color-picker .hsla-text .box input:last-child,.color-picker .rgba-text .box input:last-child,.color-picker .value-text .box input:last-child{margin-right:0}.color-picker .hue-alpha{align-items:center;margin-bottom:3px}.color-picker .hue{direction:ltr;width:100%;height:16px;margin-bottom:16px;border:none;cursor:pointer;background-size:100% 100%;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJYAAAAQCAYAAAD06IYnAAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH4AIWDwkUFWbCCAAAAFxJREFUaN7t0kEKg0AQAME2x83/n2qu5qCgD1iDhCoYdpnbQC9bbY1qVO/jvc6k3ad91s7/7F1/csgPrujuQ17BDYSFsBAWwgJhISyEBcJCWAgLhIWwEBYIi2f7Ar/1TCgFH2X9AAAAAElFTkSuQmCC)}.color-picker .value{direction:rtl;width:100%;height:16px;margin-bottom:16px;border:none;cursor:pointer;background-size:100% 100%;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJYAAAAQCAYAAAD06IYnAAACTklEQVR42u3SYUcrABhA4U2SkmRJMmWSJklKJiWZZpKUJJskKUmaTFImKZOUzMySpGRmliRNJilJSpKSJEtmSpIpmWmSdO736/6D+x7OP3gUCoWCv1cqlSQlJZGcnExKSgqpqamkpaWRnp5ORkYGmZmZqFQqsrKyyM7OJicnh9zcXNRqNXl5eeTn56PRaCgoKKCwsJCioiK0Wi3FxcWUlJRQWlpKWVkZ5eXlVFRUUFlZiU6no6qqiurqampqaqitraWurg69Xk99fT0GgwGj0UhDQwONjY00NTXR3NxMS0sLra2ttLW10d7ejslkwmw209HRQWdnJ11dXXR3d9PT00Nvby99fX309/czMDDA4OAgFouFoaEhrFYrw8PDjIyMMDo6ytjYGDabjfHxcSYmJpicnGRqagq73c709DQzMzPMzs4yNzfH/Pw8DocDp9OJy+XC7XazsLDA4uIiS0tLLC8vs7KywurqKmtra3g8HrxeLz6fD7/fz/r6OhsbG2xubrK1tcX29jaBQICdnR2CwSC7u7vs7e2xv7/PwcEBh4eHHB0dcXx8zMnJCaenp5ydnXF+fs7FxQWXl5dcXV1xfX3Nzc0Nt7e33N3dEQqFuL+/5+HhgXA4TCQS4fHxkaenJ56fn3l5eeH19ZVoNMrb2xvv7+98fHwQi8WIx+N8fn6SSCT4+vri+/ubn58ffn9/+VcKgSWwBJbAElgCS2AJLIElsASWwBJYAktgCSyBJbAElsASWAJLYAksgSWwBJbAElgCS2AJLIElsP4/WH8AmJ5Z6jHS4h8AAAAASUVORK5CYII=)}.color-picker .alpha{direction:ltr;width:100%;height:16px;border:none;cursor:pointer;background-size:100% 100%;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJYAAAAQCAYAAAD06IYnAAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH4AIWDwYQlZMa3gAAAWVJREFUaN7tmEGO6jAQRCsOArHgBpyAJYGjcGocxAm4A2IHpmoWE0eBH+ezmFlNvU06shJ3W6VEelWMUQAIIF9f6qZpimsA1LYtS2uF51/u27YVAFZVRUkEoGHdPV/sIcbIEIIkUdI/9Xa7neyv61+SWFUVAVCSct00TWn2fv6u3+Ecfd3tXzy/0+nEUu+SPjo/kqzrmiQpScN6v98XewfA8/lMkiLJ2WxGSUopcT6fM6U0NX9/frfbjev1WtfrlZfLhYfDQQHG/AIOlnGwjINlHCxjHCzjYJm/TJWdCwquJXseFFzGwDNNeiKMOJTO8xQdDQaeB29+K9efeLaBo9J7vdvtJj1RjFFjfiv7qv95tjx/7leSQgh93e1ffMeIp6O+YQjho/N791t1XVOSSI7N//K+4/GoxWLBx+PB5/Op5XLJ+/3OlJJWqxU3m83ovv5iGf8KjYNlHCxjHCzjYBkHy5gf5gusvQU7U37jTAAAAABJRU5ErkJggg==)}.color-picker .type-policy{position:absolute;top:218px;right:12px;width:16px;height:24px;background-size:8px 16px;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAgCAYAAAAffCjxAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAACewAAAnsB01CO3AAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAIASURBVEiJ7ZY9axRRFIafsxMStrLQJpAgpBFhi+C9w1YSo00I6RZ/g9vZpBf/QOr4GyRgkSKNSrAadsZqQGwCkuAWyRZJsySwvhZ7N/vhzrgbLH3Ld8597jlzz50zJokyxXH8DqDVar0qi6v8BbItqSGpEcfxdlmsFWXkvX8AfAVWg3UKPEnT9GKujMzsAFgZsVaCN1VTQd77XUnrgE1kv+6935268WRpzrnHZvYRWC7YvC3pRZZl3wozqtVqiyH9IgjAspkd1Gq1xUJQtVrdB9ZKIAOthdg/Qc65LUk7wNIMoCVJO865rYFhkqjX6/d7vV4GPJwBMqofURS5JEk6FYBer/eeYb/Mo9WwFnPOvQbeAvfuAAK4BN4sAJtAG/gJIElmNuiJyba3EGNmZiPeZuEVmVell/Y/6N+CzDn3AXhEOOo7Hv/3BeAz8IzQkMPnJbuPx1wC+yYJ7/0nYIP5S/0FHKdp+rwCEEXRS/rf5Hl1Gtb2M0iSpCOpCZzPATmX1EySpHMLAsiy7MjMDoHrGSDXZnaYZdnRwBh7J91utwmczAA6CbG3GgPleX4jqUH/a1CktqRGnuc3hSCAMB32gKspkCtgb3KCQMmkjeP4WNJThrNNZval1WptTIsv7JtQ4tmIdRa8qSoEpWl6YWZNoAN0zKxZNPehpLSBZv2t+Q0CJ9lLnARQLAAAAABJRU5ErkJggg==);background-repeat:no-repeat;background-position:center}.color-picker .type-policy .type-policy-arrow{display:block;width:100%;height:50%}.color-picker .selected-color{position:absolute;top:16px;left:8px;width:40px;height:40px;border:1px solid #a9a9a9;border-radius:50%}.color-picker .selected-color-background{width:40px;height:40px;border-radius:50%;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAh0lEQVRYR+2W0QlAMQgD60zdfwOdqa8TmI/wQMr5K0I5bZLIzLOa2nt37VVVbd+dDx5obgCC3KBLwJ2ff4PnVidkf+ucIhw80HQaCLo3DMH3CRK3iFsmAWVl6hPNDwt8EvNE5q+YuEXcMgkonVM6SdyCoEvAnZ8v1Hjx817MilmxSUB5rdLJDycZgUAZUch/AAAAAElFTkSuQmCC)}.color-picker .saturation-lightness{direction:ltr;width:100%;height:130px;border:none;cursor:pointer;touch-action:manipulation;background-size:100% 100%;background-image:url(data:image/png;base64,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)}.color-picker .cp-add-color-button-class{position:absolute;display:inline;padding:0;margin:3px -3px;border:0;cursor:pointer;background:transparent}.color-picker .cp-add-color-button-class:hover{text-decoration:underline}.color-picker .cp-add-color-button-class:disabled{cursor:not-allowed;color:#999}.color-picker .cp-add-color-button-class:disabled:hover{text-decoration:none}.color-picker .cp-remove-color-button-class{position:absolute;top:-5px;right:-5px;display:block;width:10px;height:10px;border-radius:50%;cursor:pointer;text-align:center;background:#fff;box-shadow:1px 1px 5px #333}.color-picker .cp-remove-color-button-class:before{content:\\\"x\\\";position:relative;bottom:3.5px;display:inline-block;font-size:10px}.color-picker .eyedropper-icon{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);fill:#fff;mix-blend-mode:exclusion}\\n\"] }]\n        }], ctorParameters: () => [{ type: i0.NgZone }, { type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: Document, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }, { type: ColorPickerService }], propDecorators: { dialogElement: [{\n                type: ViewChild,\n                args: ['dialogPopup', { static: true }]\n            }], hueSlider: [{\n                type: ViewChild,\n                args: ['hueSlider', { static: true }]\n            }], alphaSlider: [{\n                type: ViewChild,\n                args: ['alphaSlider', { static: true }]\n            }], handleEsc: [{\n                type: HostListener,\n                args: ['document:keyup.esc', ['$event']]\n            }], handleEnter: [{\n                type: HostListener,\n                args: ['document:keyup.enter', ['$event']]\n            }] } });\n\n// Caretaker note: we have still left the `typeof` condition in order to avoid\n// creating a breaking change for projects that still use the View Engine.\n// The `ngDevMode` is always available when Ivy is enabled.\n// This will be evaluated during compilation into `const NG_DEV_MODE = false`,\n// thus Terser will be able to tree-shake `console.warn` calls.\nconst NG_DEV_MODE = typeof ngDevMode === 'undefined' || !!ngDevMode;\nclass ColorPickerDirective {\n    injector;\n    cfr;\n    appRef;\n    vcRef;\n    elRef;\n    _service;\n    dialog;\n    dialogCreated = false;\n    ignoreChanges = false;\n    cmpRef;\n    viewAttachedToAppRef = false;\n    colorPicker;\n    cpWidth = '230px';\n    cpHeight = 'auto';\n    cpToggle = false;\n    cpDisabled = false;\n    cpIgnoredElements = [];\n    cpFallbackColor = '';\n    cpColorMode = 'color';\n    cpCmykEnabled = false;\n    cpOutputFormat = 'auto';\n    cpAlphaChannel = 'enabled';\n    cpDisableInput = false;\n    cpDialogDisplay = 'popup';\n    cpSaveClickOutside = true;\n    cpCloseClickOutside = true;\n    cpUseRootViewContainer = false;\n    cpPosition = 'auto';\n    cpPositionOffset = '0%';\n    cpPositionRelativeToArrow = false;\n    cpOKButton = false;\n    cpOKButtonText = 'OK';\n    cpOKButtonClass = 'cp-ok-button-class';\n    cpCancelButton = false;\n    cpCancelButtonText = 'Cancel';\n    cpCancelButtonClass = 'cp-cancel-button-class';\n    cpEyeDropper = false;\n    cpPresetLabel = 'Preset colors';\n    cpPresetColors;\n    cpPresetColorsClass = 'cp-preset-colors-class';\n    cpMaxPresetColorsLength = 6;\n    cpPresetEmptyMessage = 'No colors added';\n    cpPresetEmptyMessageClass = 'preset-empty-message';\n    cpAddColorButton = false;\n    cpAddColorButtonText = 'Add color';\n    cpAddColorButtonClass = 'cp-add-color-button-class';\n    cpRemoveColorButtonClass = 'cp-remove-color-button-class';\n    cpArrowPosition = 0;\n    cpExtraTemplate;\n    cpInputChange = new EventEmitter(true);\n    cpToggleChange = new EventEmitter(true);\n    cpSliderChange = new EventEmitter(true);\n    cpSliderDragEnd = new EventEmitter(true);\n    cpSliderDragStart = new EventEmitter(true);\n    colorPickerOpen = new EventEmitter(true);\n    colorPickerClose = new EventEmitter(true);\n    colorPickerCancel = new EventEmitter(true);\n    colorPickerSelect = new EventEmitter(true);\n    colorPickerChange = new EventEmitter(false);\n    cpCmykColorChange = new EventEmitter(true);\n    cpPresetColorsChange = new EventEmitter(true);\n    handleClick() {\n        this.inputFocus();\n    }\n    handleFocus() {\n        this.inputFocus();\n    }\n    handleInput(event) {\n        this.inputChange(event);\n    }\n    constructor(injector, cfr, appRef, vcRef, elRef, _service) {\n        this.injector = injector;\n        this.cfr = cfr;\n        this.appRef = appRef;\n        this.vcRef = vcRef;\n        this.elRef = elRef;\n        this._service = _service;\n    }\n    ngOnDestroy() {\n        if (this.cmpRef != null) {\n            if (this.viewAttachedToAppRef) {\n                this.appRef.detachView(this.cmpRef.hostView);\n            }\n            this.cmpRef.destroy();\n            this.cmpRef = null;\n            this.dialog = null;\n        }\n    }\n    ngOnChanges(changes) {\n        if (changes.cpToggle && !this.cpDisabled) {\n            if (changes.cpToggle.currentValue) {\n                this.openDialog();\n            }\n            else if (!changes.cpToggle.currentValue) {\n                this.closeDialog();\n            }\n        }\n        if (changes.colorPicker) {\n            if (this.dialog && !this.ignoreChanges) {\n                if (this.cpDialogDisplay === 'inline') {\n                    this.dialog.setInitialColor(changes.colorPicker.currentValue);\n                }\n                this.dialog.setColorFromString(changes.colorPicker.currentValue, false);\n                if (this.cpUseRootViewContainer && this.cpDialogDisplay !== 'inline') {\n                    this.cmpRef.changeDetectorRef.detectChanges();\n                }\n            }\n            this.ignoreChanges = false;\n        }\n        if (changes.cpPresetLabel || changes.cpPresetColors) {\n            if (this.dialog) {\n                this.dialog.setPresetConfig(this.cpPresetLabel, this.cpPresetColors);\n            }\n        }\n    }\n    openDialog() {\n        if (!this.dialogCreated) {\n            let vcRef = this.vcRef;\n            this.dialogCreated = true;\n            this.viewAttachedToAppRef = false;\n            if (this.cpUseRootViewContainer && this.cpDialogDisplay !== 'inline') {\n                const classOfRootComponent = this.appRef.componentTypes[0];\n                const appInstance = this.injector.get(classOfRootComponent, Injector.NULL);\n                if (appInstance !== Injector.NULL) {\n                    vcRef = appInstance.vcRef || appInstance.viewContainerRef || this.vcRef;\n                    if (NG_DEV_MODE && vcRef === this.vcRef) {\n                        console.warn('You are using cpUseRootViewContainer, ' +\n                            'but the root component is not exposing viewContainerRef!' +\n                            'Please expose it by adding \\'public vcRef: ViewContainerRef\\' to the constructor.');\n                    }\n                }\n                else {\n                    this.viewAttachedToAppRef = true;\n                }\n            }\n            const compFactory = this.cfr.resolveComponentFactory(ColorPickerComponent);\n            if (this.viewAttachedToAppRef) {\n                this.cmpRef = compFactory.create(this.injector);\n                this.appRef.attachView(this.cmpRef.hostView);\n                document.body.appendChild(this.cmpRef.hostView.rootNodes[0]);\n            }\n            else {\n                const injector = Injector.create({\n                    providers: [],\n                    // We shouldn't use `vcRef.parentInjector` since it's been deprecated long time ago and might be removed\n                    // in newer Angular versions: https://github.com/angular/angular/pull/25174.\n                    parent: vcRef.injector,\n                });\n                this.cmpRef = vcRef.createComponent(compFactory, 0, injector, []);\n            }\n            this.cmpRef.instance.setupDialog(this, this.elRef, this.colorPicker, this.cpWidth, this.cpHeight, this.cpDialogDisplay, this.cpFallbackColor, this.cpColorMode, this.cpCmykEnabled, this.cpAlphaChannel, this.cpOutputFormat, this.cpDisableInput, this.cpIgnoredElements, this.cpSaveClickOutside, this.cpCloseClickOutside, this.cpUseRootViewContainer, this.cpPosition, this.cpPositionOffset, this.cpPositionRelativeToArrow, this.cpPresetLabel, this.cpPresetColors, this.cpPresetColorsClass, this.cpMaxPresetColorsLength, this.cpPresetEmptyMessage, this.cpPresetEmptyMessageClass, this.cpOKButton, this.cpOKButtonClass, this.cpOKButtonText, this.cpCancelButton, this.cpCancelButtonClass, this.cpCancelButtonText, this.cpAddColorButton, this.cpAddColorButtonClass, this.cpAddColorButtonText, this.cpRemoveColorButtonClass, this.cpEyeDropper, this.elRef, this.cpExtraTemplate);\n            this.dialog = this.cmpRef.instance;\n            if (this.vcRef !== vcRef) {\n                this.cmpRef.changeDetectorRef.detectChanges();\n            }\n        }\n        else if (this.dialog) {\n            this.dialog.openDialog(this.colorPicker);\n        }\n    }\n    closeDialog() {\n        if (this.dialog && this.cpDialogDisplay === 'popup') {\n            this.dialog.closeDialog();\n        }\n    }\n    cmykChanged(value) {\n        this.cpCmykColorChange.emit(value);\n    }\n    stateChanged(state) {\n        this.cpToggleChange.emit(state);\n        if (state) {\n            this.colorPickerOpen.emit(this.colorPicker);\n        }\n        else {\n            this.colorPickerClose.emit(this.colorPicker);\n        }\n    }\n    colorChanged(value, ignore = true) {\n        this.ignoreChanges = ignore;\n        this.colorPickerChange.emit(value);\n    }\n    colorSelected(value) {\n        this.colorPickerSelect.emit(value);\n    }\n    colorCanceled() {\n        this.colorPickerCancel.emit();\n    }\n    inputFocus() {\n        const element = this.elRef.nativeElement;\n        const ignored = this.cpIgnoredElements.filter((item) => item === element);\n        if (!this.cpDisabled && !ignored.length) {\n            if (typeof document !== 'undefined' && element === document.activeElement) {\n                this.openDialog();\n            }\n            else if (!this.dialog || !this.dialog.show) {\n                this.openDialog();\n            }\n            else {\n                this.closeDialog();\n            }\n        }\n    }\n    inputChange(event) {\n        if (this.dialog) {\n            this.dialog.setColorFromString(event.target.value, true);\n        }\n        else {\n            this.colorPicker = event.target.value;\n            this.colorPickerChange.emit(this.colorPicker);\n        }\n    }\n    inputChanged(event) {\n        this.cpInputChange.emit(event);\n    }\n    sliderChanged(event) {\n        this.cpSliderChange.emit(event);\n    }\n    sliderDragEnd(event) {\n        this.cpSliderDragEnd.emit(event);\n    }\n    sliderDragStart(event) {\n        this.cpSliderDragStart.emit(event);\n    }\n    presetColorsChanged(value) {\n        this.cpPresetColorsChange.emit(value);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.2\", ngImport: i0, type: ColorPickerDirective, deps: [{ token: i0.Injector }, { token: i0.ComponentFactoryResolver }, { token: i0.ApplicationRef }, { token: i0.ViewContainerRef }, { token: i0.ElementRef }, { token: ColorPickerService }], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.0.2\", type: ColorPickerDirective, selector: \"[colorPicker]\", inputs: { colorPicker: \"colorPicker\", cpWidth: \"cpWidth\", cpHeight: \"cpHeight\", cpToggle: \"cpToggle\", cpDisabled: \"cpDisabled\", cpIgnoredElements: \"cpIgnoredElements\", cpFallbackColor: \"cpFallbackColor\", cpColorMode: \"cpColorMode\", cpCmykEnabled: \"cpCmykEnabled\", cpOutputFormat: \"cpOutputFormat\", cpAlphaChannel: \"cpAlphaChannel\", cpDisableInput: \"cpDisableInput\", cpDialogDisplay: \"cpDialogDisplay\", cpSaveClickOutside: \"cpSaveClickOutside\", cpCloseClickOutside: \"cpCloseClickOutside\", cpUseRootViewContainer: \"cpUseRootViewContainer\", cpPosition: \"cpPosition\", cpPositionOffset: \"cpPositionOffset\", cpPositionRelativeToArrow: \"cpPositionRelativeToArrow\", cpOKButton: \"cpOKButton\", cpOKButtonText: \"cpOKButtonText\", cpOKButtonClass: \"cpOKButtonClass\", cpCancelButton: \"cpCancelButton\", cpCancelButtonText: \"cpCancelButtonText\", cpCancelButtonClass: \"cpCancelButtonClass\", cpEyeDropper: \"cpEyeDropper\", cpPresetLabel: \"cpPresetLabel\", cpPresetColors: \"cpPresetColors\", cpPresetColorsClass: \"cpPresetColorsClass\", cpMaxPresetColorsLength: \"cpMaxPresetColorsLength\", cpPresetEmptyMessage: \"cpPresetEmptyMessage\", cpPresetEmptyMessageClass: \"cpPresetEmptyMessageClass\", cpAddColorButton: \"cpAddColorButton\", cpAddColorButtonText: \"cpAddColorButtonText\", cpAddColorButtonClass: \"cpAddColorButtonClass\", cpRemoveColorButtonClass: \"cpRemoveColorButtonClass\", cpArrowPosition: \"cpArrowPosition\", cpExtraTemplate: \"cpExtraTemplate\" }, outputs: { cpInputChange: \"cpInputChange\", cpToggleChange: \"cpToggleChange\", cpSliderChange: \"cpSliderChange\", cpSliderDragEnd: \"cpSliderDragEnd\", cpSliderDragStart: \"cpSliderDragStart\", colorPickerOpen: \"colorPickerOpen\", colorPickerClose: \"colorPickerClose\", colorPickerCancel: \"colorPickerCancel\", colorPickerSelect: \"colorPickerSelect\", colorPickerChange: \"colorPickerChange\", cpCmykColorChange: \"cpCmykColorChange\", cpPresetColorsChange: \"cpPresetColorsChange\" }, host: { listeners: { \"click\": \"handleClick()\", \"focus\": \"handleFocus()\", \"input\": \"handleInput($event)\" } }, exportAs: [\"ngxColorPicker\"], usesOnChanges: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.2\", ngImport: i0, type: ColorPickerDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[colorPicker]',\n                    exportAs: 'ngxColorPicker'\n                }]\n        }], ctorParameters: () => [{ type: i0.Injector }, { type: i0.ComponentFactoryResolver }, { type: i0.ApplicationRef }, { type: i0.ViewContainerRef }, { type: i0.ElementRef }, { type: ColorPickerService }], propDecorators: { colorPicker: [{\n                type: Input\n            }], cpWidth: [{\n                type: Input\n            }], cpHeight: [{\n                type: Input\n            }], cpToggle: [{\n                type: Input\n            }], cpDisabled: [{\n                type: Input\n            }], cpIgnoredElements: [{\n                type: Input\n            }], cpFallbackColor: [{\n                type: Input\n            }], cpColorMode: [{\n                type: Input\n            }], cpCmykEnabled: [{\n                type: Input\n            }], cpOutputFormat: [{\n                type: Input\n            }], cpAlphaChannel: [{\n                type: Input\n            }], cpDisableInput: [{\n                type: Input\n            }], cpDialogDisplay: [{\n                type: Input\n            }], cpSaveClickOutside: [{\n                type: Input\n            }], cpCloseClickOutside: [{\n                type: Input\n            }], cpUseRootViewContainer: [{\n                type: Input\n            }], cpPosition: [{\n                type: Input\n            }], cpPositionOffset: [{\n                type: Input\n            }], cpPositionRelativeToArrow: [{\n                type: Input\n            }], cpOKButton: [{\n                type: Input\n            }], cpOKButtonText: [{\n                type: Input\n            }], cpOKButtonClass: [{\n                type: Input\n            }], cpCancelButton: [{\n                type: Input\n            }], cpCancelButtonText: [{\n                type: Input\n            }], cpCancelButtonClass: [{\n                type: Input\n            }], cpEyeDropper: [{\n                type: Input\n            }], cpPresetLabel: [{\n                type: Input\n            }], cpPresetColors: [{\n                type: Input\n            }], cpPresetColorsClass: [{\n                type: Input\n            }], cpMaxPresetColorsLength: [{\n                type: Input\n            }], cpPresetEmptyMessage: [{\n                type: Input\n            }], cpPresetEmptyMessageClass: [{\n                type: Input\n            }], cpAddColorButton: [{\n                type: Input\n            }], cpAddColorButtonText: [{\n                type: Input\n            }], cpAddColorButtonClass: [{\n                type: Input\n            }], cpRemoveColorButtonClass: [{\n                type: Input\n            }], cpArrowPosition: [{\n                type: Input\n            }], cpExtraTemplate: [{\n                type: Input\n            }], cpInputChange: [{\n                type: Output\n            }], cpToggleChange: [{\n                type: Output\n            }], cpSliderChange: [{\n                type: Output\n            }], cpSliderDragEnd: [{\n                type: Output\n            }], cpSliderDragStart: [{\n                type: Output\n            }], colorPickerOpen: [{\n                type: Output\n            }], colorPickerClose: [{\n                type: Output\n            }], colorPickerCancel: [{\n                type: Output\n            }], colorPickerSelect: [{\n                type: Output\n            }], colorPickerChange: [{\n                type: Output\n            }], cpCmykColorChange: [{\n                type: Output\n            }], cpPresetColorsChange: [{\n                type: Output\n            }], handleClick: [{\n                type: HostListener,\n                args: ['click']\n            }], handleFocus: [{\n                type: HostListener,\n                args: ['focus']\n            }], handleInput: [{\n                type: HostListener,\n                args: ['input', ['$event']]\n            }] } });\n\nclass ColorPickerModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.2\", ngImport: i0, type: ColorPickerModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.0.2\", ngImport: i0, type: ColorPickerModule, declarations: [ColorPickerComponent, ColorPickerDirective, TextDirective, SliderDirective], imports: [CommonModule], exports: [ColorPickerDirective] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.0.2\", ngImport: i0, type: ColorPickerModule, providers: [ColorPickerService], imports: [CommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.2\", ngImport: i0, type: ColorPickerModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule],\n                    exports: [ColorPickerDirective],\n                    providers: [ColorPickerService],\n                    declarations: [ColorPickerComponent, ColorPickerDirective, TextDirective, SliderDirective]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Cmyk, ColorPickerComponent, ColorPickerDirective, ColorPickerModule, ColorPickerService, Hsla, Hsva, Rgba, SliderDirective, TextDirective };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,YAAY,EAAEC,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,YAAY,EAAEC,UAAU,EAAEC,WAAW,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,eAAe;AAClL,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,iBAAiB,EAAEC,QAAQ,EAAEC,YAAY,QAAQ,iBAAiB;AAAC,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,oCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA2IiBtB,EAAE,CAAAwB,SAAA,SAkqC++B,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAG,MAAA,GAlqCl/BzB,EAAE,CAAA0B,aAAA;IAAF1B,EAAE,CAAA2B,sBAAA,iBAAAF,MAAA,CAAAG,aAAA,IAkqC48B,CAAC;IAlqC/8B5B,EAAE,CAAA6B,WAAA,SAAAJ,MAAA,CAAAK,eAkqCo6B,CAAC,QAAAL,MAAA,CAAAM,QAAA,MAAmE,CAAC;EAAA;AAAA;AAAA,SAAAC,oCAAAV,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAW,GAAA,GAlqC3+BjC,EAAE,CAAAkC,gBAAA;IAAFlC,EAAE,CAAAmC,cAAA,aAkqCuxC,CAAC;IAlqC1xCnC,EAAE,CAAAoC,UAAA,sBAAAC,4DAAAC,MAAA;MAAFtC,EAAE,CAAAuC,aAAA,CAAAN,GAAA;MAAA,MAAAR,MAAA,GAAFzB,EAAE,CAAA0B,aAAA;MAAA,OAAF1B,EAAE,CAAAwC,WAAA,CAkqC4pCf,MAAA,CAAAgB,aAAA,CAAAH,MAAoB,CAAC;IAAA,CAAC,CAAC,uBAAAI,6DAAA;MAlqCrrC1C,EAAE,CAAAuC,aAAA,CAAAN,GAAA;MAAA,MAAAR,MAAA,GAAFzB,EAAE,CAAA0B,aAAA;MAAA,OAAF1B,EAAE,CAAAwC,WAAA,CAkqCksCf,MAAA,CAAAkB,WAAA,CAAY,sBAAsB,CAAC;IAAA,CAAC,CAAC,qBAAAC,2DAAA;MAlqCzuC5C,EAAE,CAAAuC,aAAA,CAAAN,GAAA;MAAA,MAAAR,MAAA,GAAFzB,EAAE,CAAA0B,aAAA;MAAA,OAAF1B,EAAE,CAAAwC,WAAA,CAkqCovCf,MAAA,CAAAoB,SAAA,CAAU,sBAAsB,CAAC;IAAA,CAAC,CAAC;IAlqCzxC7C,EAAE,CAAAwB,SAAA,aAkqCo3C,CAAC;IAlqCv3CxB,EAAE,CAAA8C,YAAA,CAkqC83C,CAAC;EAAA;EAAA,IAAAxB,EAAA;IAAA,MAAAG,MAAA,GAlqCj4CzB,EAAE,CAAA0B,aAAA;IAAF1B,EAAE,CAAA6B,WAAA,qBAAAJ,MAAA,CAAAsB,cAkqC6oC,CAAC;IAlqChpC/C,EAAE,CAAAgD,UAAA,SAkqCqlC,CAAC,SAAW,CAAC;IAlqCpmChD,EAAE,CAAAiD,SAAA,CAkqC+0C,CAAC;IAlqCl1CjD,EAAE,CAAA6B,WAAA,QAAAJ,MAAA,CAAAyB,MAAA,kBAAAzB,MAAA,CAAAyB,MAAA,CAAAC,CAAA,MAkqC+0C,CAAC,SAAA1B,MAAA,CAAAyB,MAAA,kBAAAzB,MAAA,CAAAyB,MAAA,CAAAE,CAAA,MAA6B,CAAC;EAAA;AAAA;AAAA,SAAAC,yCAAA/B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlqCh3CtB,EAAE,CAAAsD,cAAA;IAAFtD,EAAE,CAAAmC,cAAA,aAkqCm5D,CAAC;IAlqCt5DnC,EAAE,CAAAwB,SAAA,cAkqC87D,CAAC,cAAuS,CAAC;IAlqCzuExB,EAAE,CAAA8C,YAAA,CAkqC4uE,CAAC;EAAA;AAAA;AAAA,SAAAS,uCAAAjC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAkC,GAAA,GAlqC/uExD,EAAE,CAAAkC,gBAAA;IAAFlC,EAAE,CAAAmC,cAAA,gBAkqCi+E,CAAC;IAlqCp+EnC,EAAE,CAAAoC,UAAA,mBAAAqB,+DAAAnB,MAAA;MAAFtC,EAAE,CAAAuC,aAAA,CAAAiB,GAAA;MAAA,MAAA/B,MAAA,GAAFzB,EAAE,CAAA0B,aAAA;MAAA,OAAF1B,EAAE,CAAAwC,WAAA,CAkqCw7Ef,MAAA,CAAAiC,gBAAA,CAAApB,MAAA,EAAAb,MAAA,CAAAkC,aAAsC,CAAC;IAAA,CAAC,CAAC;IAlqCn+E3D,EAAE,CAAA4D,MAAA,EAkqC2gF,CAAC;IAlqC9gF5D,EAAE,CAAA8C,YAAA,CAkqCohF,CAAC;EAAA;EAAA,IAAAxB,EAAA;IAAA,MAAAG,MAAA,GAlqCvhFzB,EAAE,CAAA0B,aAAA;IAAF1B,EAAE,CAAA6D,UAAA,CAAApC,MAAA,CAAAqC,qBAkqC01E,CAAC;IAlqC71E9D,EAAE,CAAAgD,UAAA,aAAAvB,MAAA,CAAAsC,cAAA,IAAAtC,MAAA,CAAAsC,cAAA,CAAAC,MAAA,IAAAvC,MAAA,CAAAwC,uBAkqC46E,CAAC;IAlqC/6EjE,EAAE,CAAAiD,SAAA,CAkqC2gF,CAAC;IAlqC9gFjD,EAAE,CAAAkE,kBAAA,MAAAzC,MAAA,CAAA0C,oBAAA,KAkqC2gF,CAAC;EAAA;AAAA;AAAA,SAAAC,qCAAA9C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlqC9gFtB,EAAE,CAAAwB,SAAA,aAkqC8oF,CAAC;EAAA;AAAA;AAAA,SAAA6C,6CAAA/C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAgD,GAAA,GAlqCjpFtE,EAAE,CAAAkC,gBAAA;IAAFlC,EAAE,CAAAmC,cAAA,eAkqCs0J,CAAC;IAlqCz0JnC,EAAE,CAAAoC,UAAA,yBAAAmC,0EAAAjC,MAAA;MAAFtC,EAAE,CAAAuC,aAAA,CAAA+B,GAAA;MAAA,MAAA7C,MAAA,GAAFzB,EAAE,CAAA0B,aAAA;MAAA,OAAF1B,EAAE,CAAAwC,WAAA,CAkqCywJf,MAAA,CAAA+C,aAAA,CAAAlC,MAAoB,CAAC;IAAA,CAAC,CAAC,sBAAAmC,uEAAAnC,MAAA;MAlqClyJtC,EAAE,CAAAuC,aAAA,CAAA+B,GAAA;MAAA,MAAA7C,MAAA,GAAFzB,EAAE,CAAA0B,aAAA;MAAA,OAAF1B,EAAE,CAAAwC,WAAA,CAkqC8yJf,MAAA,CAAAiD,YAAA,CAAApC,MAAmB,CAAC;IAAA,CAAC,CAAC;IAlqCt0JtC,EAAE,CAAA8C,YAAA,CAkqCs0J,CAAC;EAAA;EAAA,IAAAxB,EAAA;IAAA,MAAAG,MAAA,GAlqCz0JzB,EAAE,CAAA0B,aAAA;IAAF1B,EAAE,CAAAgD,UAAA,QAkqC+tJ,CAAC,UAAAvB,MAAA,CAAAkD,QAAA,kBAAAlD,MAAA,CAAAkD,QAAA,CAAAC,CAAuB,CAAC;EAAA;AAAA;AAAA,SAAAC,4CAAAvD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlqC1vJtB,EAAE,CAAAmC,cAAA,SAkqCk9J,CAAC;IAlqCr9JnC,EAAE,CAAA4D,MAAA,OAkqCm9J,CAAC;IAlqCt9J5D,EAAE,CAAA8C,YAAA,CAkqCy9J,CAAC;EAAA;AAAA;AAAA,SAAAgC,qCAAAxD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAyD,GAAA,GAlqC59J/E,EAAE,CAAAkC,gBAAA;IAAFlC,EAAE,CAAAmC,cAAA,aAkqC+xH,CAAC,aAAwB,CAAC,eAAgM,CAAC;IAlqC5/HnC,EAAE,CAAAoC,UAAA,yBAAA4C,kEAAA1C,MAAA;MAAFtC,EAAE,CAAAuC,aAAA,CAAAwC,GAAA;MAAA,MAAAtD,MAAA,GAAFzB,EAAE,CAAA0B,aAAA;MAAA,OAAF1B,EAAE,CAAAwC,WAAA,CAkqC67Hf,MAAA,CAAA+C,aAAA,CAAAlC,MAAoB,CAAC;IAAA,CAAC,CAAC,sBAAA2C,+DAAA3C,MAAA;MAlqCt9HtC,EAAE,CAAAuC,aAAA,CAAAwC,GAAA;MAAA,MAAAtD,MAAA,GAAFzB,EAAE,CAAA0B,aAAA;MAAA,OAAF1B,EAAE,CAAAwC,WAAA,CAkqCk+Hf,MAAA,CAAAyD,WAAA,CAAA5C,MAAkB,CAAC;IAAA,CAAC,CAAC;IAlqCz/HtC,EAAE,CAAA8C,YAAA,CAkqCy/H,CAAC;IAlqC5/H9C,EAAE,CAAAmC,cAAA,eAkqC6rI,CAAC;IAlqChsInC,EAAE,CAAAoC,UAAA,yBAAA+C,kEAAA7C,MAAA;MAAFtC,EAAE,CAAAuC,aAAA,CAAAwC,GAAA;MAAA,MAAAtD,MAAA,GAAFzB,EAAE,CAAA0B,aAAA;MAAA,OAAF1B,EAAE,CAAAwC,WAAA,CAkqC8nIf,MAAA,CAAA+C,aAAA,CAAAlC,MAAoB,CAAC;IAAA,CAAC,CAAC,sBAAA8C,+DAAA9C,MAAA;MAlqCvpItC,EAAE,CAAAuC,aAAA,CAAAwC,GAAA;MAAA,MAAAtD,MAAA,GAAFzB,EAAE,CAAA0B,aAAA;MAAA,OAAF1B,EAAE,CAAAwC,WAAA,CAkqCmqIf,MAAA,CAAA4D,cAAA,CAAA/C,MAAqB,CAAC;IAAA,CAAC,CAAC;IAlqC7rItC,EAAE,CAAA8C,YAAA,CAkqC6rI,CAAC;IAlqChsI9C,EAAE,CAAAmC,cAAA,eAkqCg4I,CAAC;IAlqCn4InC,EAAE,CAAAoC,UAAA,yBAAAkD,kEAAAhD,MAAA;MAAFtC,EAAE,CAAAuC,aAAA,CAAAwC,GAAA;MAAA,MAAAtD,MAAA,GAAFzB,EAAE,CAAA0B,aAAA;MAAA,OAAF1B,EAAE,CAAAwC,WAAA,CAkqCk0If,MAAA,CAAA+C,aAAA,CAAAlC,MAAoB,CAAC;IAAA,CAAC,CAAC,sBAAAiD,+DAAAjD,MAAA;MAlqC31ItC,EAAE,CAAAuC,aAAA,CAAAwC,GAAA;MAAA,MAAAtD,MAAA,GAAFzB,EAAE,CAAA0B,aAAA;MAAA,OAAF1B,EAAE,CAAAwC,WAAA,CAkqCu2If,MAAA,CAAA+D,aAAA,CAAAlD,MAAoB,CAAC;IAAA,CAAC,CAAC;IAlqCh4ItC,EAAE,CAAA8C,YAAA,CAkqCg4I,CAAC;IAlqCn4I9C,EAAE,CAAAmC,cAAA,eAkqCkkJ,CAAC;IAlqCrkJnC,EAAE,CAAAoC,UAAA,yBAAAqD,kEAAAnD,MAAA;MAAFtC,EAAE,CAAAuC,aAAA,CAAAwC,GAAA;MAAA,MAAAtD,MAAA,GAAFzB,EAAE,CAAA0B,aAAA;MAAA,OAAF1B,EAAE,CAAAwC,WAAA,CAkqCqgJf,MAAA,CAAA+C,aAAA,CAAAlC,MAAoB,CAAC;IAAA,CAAC,CAAC,sBAAAoD,+DAAApD,MAAA;MAlqC9hJtC,EAAE,CAAAuC,aAAA,CAAAwC,GAAA;MAAA,MAAAtD,MAAA,GAAFzB,EAAE,CAAA0B,aAAA;MAAA,OAAF1B,EAAE,CAAAwC,WAAA,CAkqC0iJf,MAAA,CAAAkE,YAAA,CAAArD,MAAmB,CAAC;IAAA,CAAC,CAAC;IAlqClkJtC,EAAE,CAAA8C,YAAA,CAkqCkkJ,CAAC;IAlqCrkJ9C,EAAE,CAAA4F,UAAA,IAAAvB,4CAAA,mBAkqCs0J,CAAC;IAlqCz0JrE,EAAE,CAAA8C,YAAA,CAkqCk1J,CAAC;IAlqCr1J9C,EAAE,CAAAmC,cAAA,aAkqC82J,CAAC,SAAY,CAAC;IAlqC93JnC,EAAE,CAAA4D,MAAA,OAkqC43J,CAAC;IAlqC/3J5D,EAAE,CAAA8C,YAAA,CAkqCk4J,CAAC;IAlqCr4J9C,EAAE,CAAAmC,cAAA,UAkqCu4J,CAAC;IAlqC14JnC,EAAE,CAAA4D,MAAA,QAkqCw4J,CAAC;IAlqC34J5D,EAAE,CAAA8C,YAAA,CAkqC84J,CAAC;IAlqCj5J9C,EAAE,CAAAmC,cAAA,UAkqCm5J,CAAC;IAlqCt5JnC,EAAE,CAAA4D,MAAA,QAkqCo5J,CAAC;IAlqCv5J5D,EAAE,CAAA8C,YAAA,CAkqC05J,CAAC;IAlqC75J9C,EAAE,CAAAmC,cAAA,UAkqC+5J,CAAC;IAlqCl6JnC,EAAE,CAAA4D,MAAA,QAkqCg6J,CAAC;IAlqCn6J5D,EAAE,CAAA8C,YAAA,CAkqCs6J,CAAC;IAlqCz6J9C,EAAE,CAAA4F,UAAA,KAAAf,2CAAA,iBAkqCk9J,CAAC;IAlqCr9J7E,EAAE,CAAA8C,YAAA,CAkqCq+J,CAAC,CAAS,CAAC;EAAA;EAAA,IAAAxB,EAAA;IAAA,MAAAG,MAAA,GAlqCl/JzB,EAAE,CAAA0B,aAAA;IAAF1B,EAAE,CAAA6B,WAAA,YAAAJ,MAAA,CAAAoE,MAAA,yBAkqC8xH,CAAC;IAlqCjyH7F,EAAE,CAAAiD,SAAA,EAkqCm5H,CAAC;IAlqCt5HjD,EAAE,CAAAgD,UAAA,UAkqCm5H,CAAC,UAAAvB,MAAA,CAAAkD,QAAA,kBAAAlD,MAAA,CAAAkD,QAAA,CAAAmB,CAAuB,CAAC;IAlqC96H9F,EAAE,CAAAiD,SAAA,CAkqColI,CAAC;IAlqCvlIjD,EAAE,CAAAgD,UAAA,UAkqColI,CAAC,UAAAvB,MAAA,CAAAkD,QAAA,kBAAAlD,MAAA,CAAAkD,QAAA,CAAAoB,CAAuB,CAAC;IAlqC/mI/F,EAAE,CAAAiD,SAAA,CAkqCwxI,CAAC;IAlqC3xIjD,EAAE,CAAAgD,UAAA,UAkqCwxI,CAAC,UAAAvB,MAAA,CAAAkD,QAAA,kBAAAlD,MAAA,CAAAkD,QAAA,CAAAqB,CAAuB,CAAC;IAlqCnzIhG,EAAE,CAAAiD,SAAA,CAkqC29I,CAAC;IAlqC99IjD,EAAE,CAAAgD,UAAA,UAkqC29I,CAAC,UAAAvB,MAAA,CAAAkD,QAAA,kBAAAlD,MAAA,CAAAkD,QAAA,CAAAsB,CAAuB,CAAC;IAlqCt/IjG,EAAE,CAAAiD,SAAA,CAkqConJ,CAAC;IAlqCvnJjD,EAAE,CAAAgD,UAAA,SAAAvB,MAAA,CAAAyE,cAAA,eAkqConJ,CAAC;IAlqCvnJlG,EAAE,CAAAiD,SAAA,GAkqC88J,CAAC;IAlqCj9JjD,EAAE,CAAAgD,UAAA,SAAAvB,MAAA,CAAAyE,cAAA,eAkqC88J,CAAC;EAAA;AAAA;AAAA,SAAAC,6CAAA7E,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA8E,GAAA,GAlqCj9JpG,EAAE,CAAAkC,gBAAA;IAAFlC,EAAE,CAAAmC,cAAA,eAkqCw+L,CAAC;IAlqC3+LnC,EAAE,CAAAoC,UAAA,yBAAAiE,0EAAA/D,MAAA;MAAFtC,EAAE,CAAAuC,aAAA,CAAA6D,GAAA;MAAA,MAAA3E,MAAA,GAAFzB,EAAE,CAAA0B,aAAA;MAAA,OAAF1B,EAAE,CAAAwC,WAAA,CAkqC26Lf,MAAA,CAAA+C,aAAA,CAAAlC,MAAoB,CAAC;IAAA,CAAC,CAAC,sBAAAgE,uEAAAhE,MAAA;MAlqCp8LtC,EAAE,CAAAuC,aAAA,CAAA6D,GAAA;MAAA,MAAA3E,MAAA,GAAFzB,EAAE,CAAA0B,aAAA;MAAA,OAAF1B,EAAE,CAAAwC,WAAA,CAkqCg9Lf,MAAA,CAAAiD,YAAA,CAAApC,MAAmB,CAAC;IAAA,CAAC,CAAC;IAlqCx+LtC,EAAE,CAAA8C,YAAA,CAkqCw+L,CAAC;EAAA;EAAA,IAAAxB,EAAA;IAAA,MAAAG,MAAA,GAlqC3+LzB,EAAE,CAAA0B,aAAA;IAAF1B,EAAE,CAAAgD,UAAA,QAkqCi4L,CAAC,UAAAvB,MAAA,CAAA8E,QAAA,kBAAA9E,MAAA,CAAA8E,QAAA,CAAA3B,CAAuB,CAAC;EAAA;AAAA;AAAA,SAAA4B,4CAAAlF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlqC55LtB,EAAE,CAAAmC,cAAA,SAkqCsmM,CAAC;IAlqCzmMnC,EAAE,CAAA4D,MAAA,OAkqCumM,CAAC;IAlqC1mM5D,EAAE,CAAA8C,YAAA,CAkqC6mM,CAAC;EAAA;AAAA;AAAA,SAAA2D,qCAAAnF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAoF,GAAA,GAlqChnM1G,EAAE,CAAAkC,gBAAA;IAAFlC,EAAE,CAAAmC,cAAA,aAkqC8nK,CAAC,aAAwB,CAAC,eAA+L,CAAC;IAlqC11KnC,EAAE,CAAAoC,UAAA,yBAAAuE,kEAAArE,MAAA;MAAFtC,EAAE,CAAAuC,aAAA,CAAAmE,GAAA;MAAA,MAAAjF,MAAA,GAAFzB,EAAE,CAAA0B,aAAA;MAAA,OAAF1B,EAAE,CAAAwC,WAAA,CAkqC4xKf,MAAA,CAAA+C,aAAA,CAAAlC,MAAoB,CAAC;IAAA,CAAC,CAAC,sBAAAsE,+DAAAtE,MAAA;MAlqCrzKtC,EAAE,CAAAuC,aAAA,CAAAmE,GAAA;MAAA,MAAAjF,MAAA,GAAFzB,EAAE,CAAA0B,aAAA;MAAA,OAAF1B,EAAE,CAAAwC,WAAA,CAkqCi0Kf,MAAA,CAAAoF,UAAA,CAAAvE,MAAiB,CAAC;IAAA,CAAC,CAAC;IAlqCv1KtC,EAAE,CAAA8C,YAAA,CAkqCu1K,CAAC;IAlqC11K9C,EAAE,CAAAmC,cAAA,eAkqC8hL,CAAC;IAlqCjiLnC,EAAE,CAAAoC,UAAA,yBAAA0E,kEAAAxE,MAAA;MAAFtC,EAAE,CAAAuC,aAAA,CAAAmE,GAAA;MAAA,MAAAjF,MAAA,GAAFzB,EAAE,CAAA0B,aAAA;MAAA,OAAF1B,EAAE,CAAAwC,WAAA,CAkqC49Kf,MAAA,CAAA+C,aAAA,CAAAlC,MAAoB,CAAC;IAAA,CAAC,CAAC,sBAAAyE,+DAAAzE,MAAA;MAlqCr/KtC,EAAE,CAAAuC,aAAA,CAAAmE,GAAA;MAAA,MAAAjF,MAAA,GAAFzB,EAAE,CAAA0B,aAAA;MAAA,OAAF1B,EAAE,CAAAwC,WAAA,CAkqCigLf,MAAA,CAAAuF,iBAAA,CAAA1E,MAAwB,CAAC;IAAA,CAAC,CAAC;IAlqC9hLtC,EAAE,CAAA8C,YAAA,CAkqC8hL,CAAC;IAlqCjiL9C,EAAE,CAAAmC,cAAA,eAkqCouL,CAAC;IAlqCvuLnC,EAAE,CAAAoC,UAAA,yBAAA6E,kEAAA3E,MAAA;MAAFtC,EAAE,CAAAuC,aAAA,CAAAmE,GAAA;MAAA,MAAAjF,MAAA,GAAFzB,EAAE,CAAA0B,aAAA;MAAA,OAAF1B,EAAE,CAAAwC,WAAA,CAkqCmqLf,MAAA,CAAA+C,aAAA,CAAAlC,MAAoB,CAAC;IAAA,CAAC,CAAC,sBAAA4E,+DAAA5E,MAAA;MAlqC5rLtC,EAAE,CAAAuC,aAAA,CAAAmE,GAAA;MAAA,MAAAjF,MAAA,GAAFzB,EAAE,CAAA0B,aAAA;MAAA,OAAF1B,EAAE,CAAAwC,WAAA,CAkqCwsLf,MAAA,CAAA0F,gBAAA,CAAA7E,MAAuB,CAAC;IAAA,CAAC,CAAC;IAlqCpuLtC,EAAE,CAAA8C,YAAA,CAkqCouL,CAAC;IAlqCvuL9C,EAAE,CAAA4F,UAAA,IAAAO,4CAAA,mBAkqCw+L,CAAC;IAlqC3+LnG,EAAE,CAAA8C,YAAA,CAkqCo/L,CAAC;IAlqCv/L9C,EAAE,CAAAmC,cAAA,aAkqC+gM,CAAC,SAAY,CAAC;IAlqC/hMnC,EAAE,CAAA4D,MAAA,OAkqC6hM,CAAC;IAlqChiM5D,EAAE,CAAA8C,YAAA,CAkqCmiM,CAAC;IAlqCtiM9C,EAAE,CAAAmC,cAAA,SAkqCwiM,CAAC;IAlqC3iMnC,EAAE,CAAA4D,MAAA,QAkqCyiM,CAAC;IAlqC5iM5D,EAAE,CAAA8C,YAAA,CAkqC+iM,CAAC;IAlqCljM9C,EAAE,CAAAmC,cAAA,UAkqCojM,CAAC;IAlqCvjMnC,EAAE,CAAA4D,MAAA,QAkqCqjM,CAAC;IAlqCxjM5D,EAAE,CAAA8C,YAAA,CAkqC2jM,CAAC;IAlqC9jM9C,EAAE,CAAA4F,UAAA,KAAAY,2CAAA,iBAkqCsmM,CAAC;IAlqCzmMxG,EAAE,CAAA8C,YAAA,CAkqCynM,CAAC,CAAS,CAAC;EAAA;EAAA,IAAAxB,EAAA;IAAA,MAAAG,MAAA,GAlqCtoMzB,EAAE,CAAA0B,aAAA;IAAF1B,EAAE,CAAA6B,WAAA,YAAAJ,MAAA,CAAAoE,MAAA,yBAkqC6nK,CAAC;IAlqChoK7F,EAAE,CAAAiD,SAAA,EAkqCkvK,CAAC;IAlqCrvKjD,EAAE,CAAAgD,UAAA,UAkqCkvK,CAAC,UAAAvB,MAAA,CAAA8E,QAAA,kBAAA9E,MAAA,CAAA8E,QAAA,CAAAa,CAAuB,CAAC;IAlqC7wKpH,EAAE,CAAAiD,SAAA,CAkqCk7K,CAAC;IAlqCr7KjD,EAAE,CAAAgD,UAAA,UAkqCk7K,CAAC,UAAAvB,MAAA,CAAA8E,QAAA,kBAAA9E,MAAA,CAAA8E,QAAA,CAAAnD,CAAuB,CAAC;IAlqC78KpD,EAAE,CAAAiD,SAAA,CAkqCynL,CAAC;IAlqC5nLjD,EAAE,CAAAgD,UAAA,UAkqCynL,CAAC,UAAAvB,MAAA,CAAA8E,QAAA,kBAAA9E,MAAA,CAAA8E,QAAA,CAAAc,CAAuB,CAAC;IAlqCppLrH,EAAE,CAAAiD,SAAA,CAkqCsxL,CAAC;IAlqCzxLjD,EAAE,CAAAgD,UAAA,SAAAvB,MAAA,CAAAyE,cAAA,eAkqCsxL,CAAC;IAlqCzxLlG,EAAE,CAAAiD,SAAA,EAkqCmmM,CAAC;IAlqCtmMjD,EAAE,CAAAgD,UAAA,SAAAvB,MAAA,CAAAyE,cAAA,eAkqCmmM,CAAC;EAAA;AAAA;AAAA,SAAAoB,6CAAAhG,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAiG,IAAA,GAlqCtmMvH,EAAE,CAAAkC,gBAAA;IAAFlC,EAAE,CAAAmC,cAAA,eAkqCknO,CAAC;IAlqCrnOnC,EAAE,CAAAoC,UAAA,yBAAAoF,0EAAAlF,MAAA;MAAFtC,EAAE,CAAAuC,aAAA,CAAAgF,IAAA;MAAA,MAAA9F,MAAA,GAAFzB,EAAE,CAAA0B,aAAA;MAAA,OAAF1B,EAAE,CAAAwC,WAAA,CAkqCqjOf,MAAA,CAAA+C,aAAA,CAAAlC,MAAoB,CAAC;IAAA,CAAC,CAAC,sBAAAmF,uEAAAnF,MAAA;MAlqC9kOtC,EAAE,CAAAuC,aAAA,CAAAgF,IAAA;MAAA,MAAA9F,MAAA,GAAFzB,EAAE,CAAA0B,aAAA;MAAA,OAAF1B,EAAE,CAAAwC,WAAA,CAkqC0lOf,MAAA,CAAAiD,YAAA,CAAApC,MAAmB,CAAC;IAAA,CAAC,CAAC;IAlqClnOtC,EAAE,CAAA8C,YAAA,CAkqCknO,CAAC;EAAA;EAAA,IAAAxB,EAAA;IAAA,MAAAG,MAAA,GAlqCrnOzB,EAAE,CAAA0B,aAAA;IAAF1B,EAAE,CAAAgD,UAAA,QAkqC2gO,CAAC,UAAAvB,MAAA,CAAAiG,QAAA,kBAAAjG,MAAA,CAAAiG,QAAA,CAAA9C,CAAuB,CAAC;EAAA;AAAA;AAAA,SAAA+C,4CAAArG,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlqCtiOtB,EAAE,CAAAmC,cAAA,SAkqCivO,CAAC;IAlqCpvOnC,EAAE,CAAA4D,MAAA,OAkqCkvO,CAAC;IAlqCrvO5D,EAAE,CAAA8C,YAAA,CAkqCwvO,CAAC;EAAA;AAAA;AAAA,SAAA8E,qCAAAtG,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAuG,GAAA,GAlqC3vO7H,EAAE,CAAAkC,gBAAA;IAAFlC,EAAE,CAAAmC,cAAA,aAkqCkxM,CAAC,aAAwB,CAAC,eAA+L,CAAC;IAlqC9+MnC,EAAE,CAAAoC,UAAA,yBAAA0F,kEAAAxF,MAAA;MAAFtC,EAAE,CAAAuC,aAAA,CAAAsF,GAAA;MAAA,MAAApG,MAAA,GAAFzB,EAAE,CAAA0B,aAAA;MAAA,OAAF1B,EAAE,CAAAwC,WAAA,CAkqCg7Mf,MAAA,CAAA+C,aAAA,CAAAlC,MAAoB,CAAC;IAAA,CAAC,CAAC,sBAAAyF,+DAAAzF,MAAA;MAlqCz8MtC,EAAE,CAAAuC,aAAA,CAAAsF,GAAA;MAAA,MAAApG,MAAA,GAAFzB,EAAE,CAAA0B,aAAA;MAAA,OAAF1B,EAAE,CAAAwC,WAAA,CAkqCq9Mf,MAAA,CAAAuG,UAAA,CAAA1F,MAAiB,CAAC;IAAA,CAAC,CAAC;IAlqC3+MtC,EAAE,CAAA8C,YAAA,CAkqC2+M,CAAC;IAlqC9+M9C,EAAE,CAAAmC,cAAA,eAkqC6qN,CAAC;IAlqChrNnC,EAAE,CAAAoC,UAAA,yBAAA6F,kEAAA3F,MAAA;MAAFtC,EAAE,CAAAuC,aAAA,CAAAsF,GAAA;MAAA,MAAApG,MAAA,GAAFzB,EAAE,CAAA0B,aAAA;MAAA,OAAF1B,EAAE,CAAAwC,WAAA,CAkqCgnNf,MAAA,CAAA+C,aAAA,CAAAlC,MAAoB,CAAC;IAAA,CAAC,CAAC,sBAAA4F,+DAAA5F,MAAA;MAlqCzoNtC,EAAE,CAAAuC,aAAA,CAAAsF,GAAA;MAAA,MAAApG,MAAA,GAAFzB,EAAE,CAAA0B,aAAA;MAAA,OAAF1B,EAAE,CAAAwC,WAAA,CAkqCqpNf,MAAA,CAAA0G,YAAA,CAAA7F,MAAmB,CAAC;IAAA,CAAC,CAAC;IAlqC7qNtC,EAAE,CAAA8C,YAAA,CAkqC6qN,CAAC;IAlqChrN9C,EAAE,CAAAmC,cAAA,eAkqC82N,CAAC;IAlqCj3NnC,EAAE,CAAAoC,UAAA,yBAAAgG,kEAAA9F,MAAA;MAAFtC,EAAE,CAAAuC,aAAA,CAAAsF,GAAA;MAAA,MAAApG,MAAA,GAAFzB,EAAE,CAAA0B,aAAA;MAAA,OAAF1B,EAAE,CAAAwC,WAAA,CAkqCkzNf,MAAA,CAAA+C,aAAA,CAAAlC,MAAoB,CAAC;IAAA,CAAC,CAAC,sBAAA+F,+DAAA/F,MAAA;MAlqC30NtC,EAAE,CAAAuC,aAAA,CAAAsF,GAAA;MAAA,MAAApG,MAAA,GAAFzB,EAAE,CAAA0B,aAAA;MAAA,OAAF1B,EAAE,CAAAwC,WAAA,CAkqCu1Nf,MAAA,CAAA6G,WAAA,CAAAhG,MAAkB,CAAC;IAAA,CAAC,CAAC;IAlqC92NtC,EAAE,CAAA8C,YAAA,CAkqC82N,CAAC;IAlqCj3N9C,EAAE,CAAA4F,UAAA,IAAA0B,4CAAA,mBAkqCknO,CAAC;IAlqCrnOtH,EAAE,CAAA8C,YAAA,CAkqC8nO,CAAC;IAlqCjoO9C,EAAE,CAAAmC,cAAA,aAkqCypO,CAAC,SAAY,CAAC;IAlqCzqOnC,EAAE,CAAA4D,MAAA,OAkqCuqO,CAAC;IAlqC1qO5D,EAAE,CAAA8C,YAAA,CAkqC6qO,CAAC;IAlqChrO9C,EAAE,CAAAmC,cAAA,SAkqCkrO,CAAC;IAlqCrrOnC,EAAE,CAAA4D,MAAA,QAkqCmrO,CAAC;IAlqCtrO5D,EAAE,CAAA8C,YAAA,CAkqCyrO,CAAC;IAlqC5rO9C,EAAE,CAAAmC,cAAA,UAkqC8rO,CAAC;IAlqCjsOnC,EAAE,CAAA4D,MAAA,QAkqC+rO,CAAC;IAlqClsO5D,EAAE,CAAA8C,YAAA,CAkqCqsO,CAAC;IAlqCxsO9C,EAAE,CAAA4F,UAAA,KAAA+B,2CAAA,iBAkqCivO,CAAC;IAlqCpvO3H,EAAE,CAAA8C,YAAA,CAkqCowO,CAAC,CAAS,CAAC;EAAA;EAAA,IAAAxB,EAAA;IAAA,MAAAG,MAAA,GAlqCjxOzB,EAAE,CAAA0B,aAAA;IAAF1B,EAAE,CAAA6B,WAAA,YAAAJ,MAAA,CAAAoE,MAAA,yBAkqC6vM,CAAC;IAlqChwM7F,EAAE,CAAAiD,SAAA,EAkqCs4M,CAAC;IAlqCz4MjD,EAAE,CAAAgD,UAAA,UAkqCs4M,CAAC,UAAAvB,MAAA,CAAAiG,QAAA,kBAAAjG,MAAA,CAAAiG,QAAA,CAAAa,CAAuB,CAAC;IAlqCj6MvI,EAAE,CAAAiD,SAAA,CAkqCskN,CAAC;IAlqCzkNjD,EAAE,CAAAgD,UAAA,UAkqCskN,CAAC,UAAAvB,MAAA,CAAAiG,QAAA,kBAAAjG,MAAA,CAAAiG,QAAA,CAAAc,CAAuB,CAAC;IAlqCjmNxI,EAAE,CAAAiD,SAAA,CAkqCwwN,CAAC;IAlqC3wNjD,EAAE,CAAAgD,UAAA,UAkqCwwN,CAAC,UAAAvB,MAAA,CAAAiG,QAAA,kBAAAjG,MAAA,CAAAiG,QAAA,CAAAe,CAAuB,CAAC;IAlqCnyNzI,EAAE,CAAAiD,SAAA,CAkqCg6N,CAAC;IAlqCn6NjD,EAAE,CAAAgD,UAAA,SAAAvB,MAAA,CAAAyE,cAAA,eAkqCg6N,CAAC;IAlqCn6NlG,EAAE,CAAAiD,SAAA,EAkqC6uO,CAAC;IAlqChvOjD,EAAE,CAAAgD,UAAA,SAAAvB,MAAA,CAAAyE,cAAA,eAkqC6uO,CAAC;EAAA;AAAA;AAAA,SAAAwC,6CAAApH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAqH,IAAA,GAlqChvO3I,EAAE,CAAAkC,gBAAA;IAAFlC,EAAE,CAAAmC,cAAA,eAkqCw3P,CAAC;IAlqC33PnC,EAAE,CAAAoC,UAAA,yBAAAwG,0EAAAtG,MAAA;MAAFtC,EAAE,CAAAuC,aAAA,CAAAoG,IAAA;MAAA,MAAAlH,MAAA,GAAFzB,EAAE,CAAA0B,aAAA;MAAA,OAAF1B,EAAE,CAAAwC,WAAA,CAkqC4zPf,MAAA,CAAA+C,aAAA,CAAAlC,MAAoB,CAAC;IAAA,CAAC,CAAC,sBAAAuG,uEAAAvG,MAAA;MAlqCr1PtC,EAAE,CAAAuC,aAAA,CAAAoG,IAAA;MAAA,MAAAlH,MAAA,GAAFzB,EAAE,CAAA0B,aAAA;MAAA,OAAF1B,EAAE,CAAAwC,WAAA,CAkqCi2Pf,MAAA,CAAAiD,YAAA,CAAApC,MAAmB,CAAC;IAAA,CAAC,CAAC;IAlqCz3PtC,EAAE,CAAA8C,YAAA,CAkqCw3P,CAAC;EAAA;EAAA,IAAAxB,EAAA;IAAA,MAAAG,MAAA,GAlqC33PzB,EAAE,CAAA0B,aAAA;IAAF1B,EAAE,CAAAgD,UAAA,QAkqCqxP,CAAC,UAAAvB,MAAA,CAAAqH,QAAoB,CAAC;EAAA;AAAA;AAAA,SAAAC,2CAAAzH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlqC7yPtB,EAAE,CAAAmC,cAAA,SAkqCs+P,CAAC;IAlqCz+PnC,EAAE,CAAA4D,MAAA,OAkqCu+P,CAAC;IAlqC1+P5D,EAAE,CAAA8C,YAAA,CAkqC6+P,CAAC;EAAA;AAAA;AAAA,SAAAkG,qCAAA1H,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA2H,IAAA,GAlqCh/PjJ,EAAE,CAAAkC,gBAAA;IAAFlC,EAAE,CAAAmC,cAAA,aAkqCg9O,CAAC,aAAwB,CAAC,eAAgJ,CAAC;IAlqC7nPnC,EAAE,CAAAoC,UAAA,kBAAA8G,2DAAA;MAAFlJ,EAAE,CAAAuC,aAAA,CAAA0G,IAAA;MAAA,MAAAxH,MAAA,GAAFzB,EAAE,CAAA0B,aAAA;MAAA,OAAF1B,EAAE,CAAAwC,WAAA,CAkqC6hPf,MAAA,CAAA0H,UAAA,CAAW,IAAI,CAAC;IAAA,CAAC,CAAC,yBAAAC,kEAAA9G,MAAA;MAlqCjjPtC,EAAE,CAAAuC,aAAA,CAAA0G,IAAA;MAAA,MAAAxH,MAAA,GAAFzB,EAAE,CAAA0B,aAAA;MAAA,OAAF1B,EAAE,CAAAwC,WAAA,CAkqCgkPf,MAAA,CAAA+C,aAAA,CAAAlC,MAAoB,CAAC;IAAA,CAAC,CAAC,sBAAA+G,+DAAA/G,MAAA;MAlqCzlPtC,EAAE,CAAAuC,aAAA,CAAA0G,IAAA;MAAA,MAAAxH,MAAA,GAAFzB,EAAE,CAAA0B,aAAA;MAAA,OAAF1B,EAAE,CAAAwC,WAAA,CAkqCqmPf,MAAA,CAAA0H,UAAA,CAAA7G,MAAiB,CAAC;IAAA,CAAC,CAAC;IAlqC3nPtC,EAAE,CAAA8C,YAAA,CAkqC0nP,CAAC;IAlqC7nP9C,EAAE,CAAA4F,UAAA,IAAA8C,4CAAA,mBAkqCw3P,CAAC;IAlqC33P1I,EAAE,CAAA8C,YAAA,CAkqCo4P,CAAC;IAlqCv4P9C,EAAE,CAAAmC,cAAA,aAkqC+5P,CAAC,SAAY,CAAC;IAlqC/6PnC,EAAE,CAAA4D,MAAA,SAkqC+6P,CAAC;IAlqCl7P5D,EAAE,CAAA8C,YAAA,CAkqCq7P,CAAC;IAlqCx7P9C,EAAE,CAAA4F,UAAA,IAAAmD,0CAAA,iBAkqCs+P,CAAC;IAlqCz+P/I,EAAE,CAAA8C,YAAA,CAkqCy/P,CAAC,CAAS,CAAC;EAAA;EAAA,IAAAxB,EAAA;IAAA,MAAAG,MAAA,GAlqCtgQzB,EAAE,CAAA0B,aAAA;IAAF1B,EAAE,CAAA6B,WAAA,YAAAJ,MAAA,CAAAoE,MAAA,yBAkqC+8O,CAAC;IAlqCl9O7F,EAAE,CAAAsJ,WAAA,cAAA7H,MAAA,CAAAyE,cAAA,aAkqCs5O,CAAC;IAlqCz5OlG,EAAE,CAAAiD,SAAA,EAkqCkhP,CAAC;IAlqCrhPjD,EAAE,CAAAgD,UAAA,UAAAvB,MAAA,CAAA8H,OAkqCkhP,CAAC;IAlqCrhPvJ,EAAE,CAAAiD,SAAA,CAkqC0qP,CAAC;IAlqC7qPjD,EAAE,CAAAgD,UAAA,SAAAvB,MAAA,CAAAyE,cAAA,aAkqC0qP,CAAC;IAlqC7qPlG,EAAE,CAAAiD,SAAA,EAkqCm+P,CAAC;IAlqCt+PjD,EAAE,CAAAgD,UAAA,SAAAvB,MAAA,CAAAyE,cAAA,aAkqCm+P,CAAC;EAAA;AAAA;AAAA,SAAAsD,6CAAAlI,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAmI,IAAA,GAlqCt+PzJ,EAAE,CAAAkC,gBAAA;IAAFlC,EAAE,CAAAmC,cAAA,eAkqC8jR,CAAC;IAlqCjkRnC,EAAE,CAAAoC,UAAA,yBAAAsH,0EAAApH,MAAA;MAAFtC,EAAE,CAAAuC,aAAA,CAAAkH,IAAA;MAAA,MAAAhI,MAAA,GAAFzB,EAAE,CAAA0B,aAAA;MAAA,OAAF1B,EAAE,CAAAwC,WAAA,CAkqCigRf,MAAA,CAAA+C,aAAA,CAAAlC,MAAoB,CAAC;IAAA,CAAC,CAAC,sBAAAqH,uEAAArH,MAAA;MAlqC1hRtC,EAAE,CAAAuC,aAAA,CAAAkH,IAAA;MAAA,MAAAhI,MAAA,GAAFzB,EAAE,CAAA0B,aAAA;MAAA,OAAF1B,EAAE,CAAAwC,WAAA,CAkqCsiRf,MAAA,CAAAiD,YAAA,CAAApC,MAAmB,CAAC;IAAA,CAAC,CAAC;IAlqC9jRtC,EAAE,CAAA8C,YAAA,CAkqC8jR,CAAC;EAAA;EAAA,IAAAxB,EAAA;IAAA,MAAAG,MAAA,GAlqCjkRzB,EAAE,CAAA0B,aAAA;IAAF1B,EAAE,CAAAgD,UAAA,QAkqCu9Q,CAAC,UAAAvB,MAAA,CAAA8E,QAAA,kBAAA9E,MAAA,CAAA8E,QAAA,CAAA3B,CAAuB,CAAC;EAAA;AAAA;AAAA,SAAAgF,qCAAAtI,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAuI,IAAA,GAlqCl/Q7J,EAAE,CAAAkC,gBAAA;IAAFlC,EAAE,CAAAmC,cAAA,aAkqC8lQ,CAAC,aAAwB,CAAC,eAAiM,CAAC;IAlqC5zQnC,EAAE,CAAAoC,UAAA,yBAAA0H,kEAAAxH,MAAA;MAAFtC,EAAE,CAAAuC,aAAA,CAAAsH,IAAA;MAAA,MAAApI,MAAA,GAAFzB,EAAE,CAAA0B,aAAA;MAAA,OAAF1B,EAAE,CAAAwC,WAAA,CAkqC4vQf,MAAA,CAAA+C,aAAA,CAAAlC,MAAoB,CAAC;IAAA,CAAC,CAAC,sBAAAyH,+DAAAzH,MAAA;MAlqCrxQtC,EAAE,CAAAuC,aAAA,CAAAsH,IAAA;MAAA,MAAApI,MAAA,GAAFzB,EAAE,CAAA0B,aAAA;MAAA,OAAF1B,EAAE,CAAAwC,WAAA,CAkqCiyQf,MAAA,CAAAuI,YAAA,CAAA1H,MAAmB,CAAC;IAAA,CAAC,CAAC;IAlqCzzQtC,EAAE,CAAA8C,YAAA,CAkqCyzQ,CAAC;IAlqC5zQ9C,EAAE,CAAA4F,UAAA,IAAA4D,4CAAA,mBAkqC8jR,CAAC;IAlqCjkRxJ,EAAE,CAAA8C,YAAA,CAkqC0kR,CAAC;IAlqC7kR9C,EAAE,CAAAmC,cAAA,aAkqCqmR,CAAC,SAAY,CAAC;IAlqCrnRnC,EAAE,CAAA4D,MAAA,OAkqCmnR,CAAC;IAlqCtnR5D,EAAE,CAAA8C,YAAA,CAkqCynR,CAAC;IAlqC5nR9C,EAAE,CAAAmC,cAAA,SAkqC8nR,CAAC;IAlqCjoRnC,EAAE,CAAA4D,MAAA,OAkqC+nR,CAAC;IAlqCloR5D,EAAE,CAAA8C,YAAA,CAkqCqoR,CAAC,CAAW,CAAC,CAAS,CAAC;EAAA;EAAA,IAAAxB,EAAA;IAAA,MAAAG,MAAA,GAlqC9pRzB,EAAE,CAAA0B,aAAA;IAAF1B,EAAE,CAAAiD,SAAA,EAkqCktQ,CAAC;IAlqCrtQjD,EAAE,CAAAgD,UAAA,UAkqCktQ,CAAC,UAAAvB,MAAA,CAAA8E,QAAA,kBAAA9E,MAAA,CAAA8E,QAAA,CAAAc,CAAuB,CAAC;IAlqC7uQrH,EAAE,CAAAiD,SAAA,CAkqC22Q,CAAC;IAlqC92QjD,EAAE,CAAAgD,UAAA,SAAAvB,MAAA,CAAAyE,cAAA,eAkqC22Q,CAAC;EAAA;AAAA;AAAA,SAAA+D,qCAAA3I,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA4I,IAAA,GAlqC92QlK,EAAE,CAAAkC,gBAAA;IAAFlC,EAAE,CAAAmC,cAAA,aAkqCuvR,CAAC,cAAsE,CAAC;IAlqCj0RnC,EAAE,CAAAoC,UAAA,mBAAA+H,2DAAA;MAAFnK,EAAE,CAAAuC,aAAA,CAAA2H,IAAA;MAAA,MAAAzI,MAAA,GAAFzB,EAAE,CAAA0B,aAAA;MAAA,OAAF1B,EAAE,CAAAwC,WAAA,CAkqC0yRf,MAAA,CAAA2I,cAAA,EAAgB,CAAC,CAAC;IAAA,CAAC,CAAC;IAlqCh0RpK,EAAE,CAAA8C,YAAA,CAkqCq0R,CAAC;IAlqCx0R9C,EAAE,CAAAmC,cAAA,cAkqC24R,CAAC;IAlqC94RnC,EAAE,CAAAoC,UAAA,mBAAAiI,2DAAA;MAAFrK,EAAE,CAAAuC,aAAA,CAAA2H,IAAA;MAAA,MAAAzI,MAAA,GAAFzB,EAAE,CAAA0B,aAAA;MAAA,OAAF1B,EAAE,CAAAwC,WAAA,CAkqCw3Rf,MAAA,CAAA2I,cAAA,CAAe,CAAC,CAAC;IAAA,CAAC,CAAC;IAlqC74RpK,EAAE,CAAA8C,YAAA,CAkqCk5R,CAAC,CAAS,CAAC;EAAA;AAAA;AAAA,SAAAwH,wDAAAhJ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAiJ,IAAA,GAlqC/5RvK,EAAE,CAAAkC,gBAAA;IAAFlC,EAAE,CAAAmC,cAAA,cAkqC25S,CAAC;IAlqC95SnC,EAAE,CAAAoC,UAAA,mBAAAoI,8EAAAlI,MAAA;MAAFtC,EAAE,CAAAuC,aAAA,CAAAgI,IAAA;MAAA,MAAAE,SAAA,GAAFzK,EAAE,CAAA0B,aAAA,GAAAgJ,SAAA;MAAA,MAAAjJ,MAAA,GAAFzB,EAAE,CAAA0B,aAAA;MAAA,OAAF1B,EAAE,CAAAwC,WAAA,CAkqCu3Sf,MAAA,CAAAkJ,mBAAA,CAAArI,MAAA,EAAAmI,SAAiC,CAAC;IAAA,CAAC,CAAC;IAlqC75SzK,EAAE,CAAA8C,YAAA,CAkqCk6S,CAAC;EAAA;EAAA,IAAAxB,EAAA;IAAA,MAAAG,MAAA,GAlqCr6SzB,EAAE,CAAA0B,aAAA;IAAF1B,EAAE,CAAA6D,UAAA,CAAApC,MAAA,CAAAmJ,wBAkqC22S,CAAC;EAAA;AAAA;AAAA,SAAAC,iDAAAvJ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAwJ,IAAA,GAlqC92S9K,EAAE,CAAAkC,gBAAA;IAAFlC,EAAE,CAAAmC,cAAA,aAkqC0xS,CAAC;IAlqC7xSnC,EAAE,CAAAoC,UAAA,mBAAA2I,sEAAA;MAAA,MAAAN,SAAA,GAAFzK,EAAE,CAAAuC,aAAA,CAAAuI,IAAA,EAAAJ,SAAA;MAAA,MAAAjJ,MAAA,GAAFzB,EAAE,CAAA0B,aAAA;MAAA,OAAF1B,EAAE,CAAAwC,WAAA,CAkqC+vSf,MAAA,CAAAuJ,kBAAA,CAAAP,SAAwB,CAAC;IAAA,CAAC,CAAC;IAlqC5xSzK,EAAE,CAAA4F,UAAA,IAAA0E,uDAAA,kBAkqC25S,CAAC;IAlqC95StK,EAAE,CAAA8C,YAAA,CAkqCg7S,CAAC;EAAA;EAAA,IAAAxB,EAAA;IAAA,MAAAmJ,SAAA,GAAAlJ,GAAA,CAAAmJ,SAAA;IAAA,MAAAjJ,MAAA,GAlqCn7SzB,EAAE,CAAA0B,aAAA;IAAF1B,EAAE,CAAA6B,WAAA,qBAAA4I,SAkqCmvS,CAAC;IAlqCtvSzK,EAAE,CAAAiD,SAAA,CAkqCk0S,CAAC;IAlqCr0SjD,EAAE,CAAAgD,UAAA,SAAAvB,MAAA,CAAAwJ,gBAkqCk0S,CAAC;EAAA;AAAA;AAAA,SAAAC,2CAAA5J,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlqCr0StB,EAAE,CAAAmC,cAAA,SAkqCuoS,CAAC;IAlqC1oSnC,EAAE,CAAA4F,UAAA,IAAAiF,gDAAA,iBAkqC0xS,CAAC;IAlqC7xS7K,EAAE,CAAA8C,YAAA,CAkqC47S,CAAC;EAAA;EAAA,IAAAxB,EAAA;IAAA,MAAAG,MAAA,GAlqC/7SzB,EAAE,CAAA0B,aAAA;IAAF1B,EAAE,CAAA6D,UAAA,CAAApC,MAAA,CAAA0J,mBAkqCsoS,CAAC;IAlqCzoSnL,EAAE,CAAAiD,SAAA,CAkqCwrS,CAAC;IAlqC3rSjD,EAAE,CAAAgD,UAAA,YAAAvB,MAAA,CAAAsC,cAkqCwrS,CAAC;EAAA;AAAA;AAAA,SAAAqH,2CAAA9J,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlqC3rStB,EAAE,CAAAmC,cAAA,SAkqCuiT,CAAC;IAlqC1iTnC,EAAE,CAAA4D,MAAA,EAkqC+jT,CAAC;IAlqClkT5D,EAAE,CAAA8C,YAAA,CAkqCqkT,CAAC;EAAA;EAAA,IAAAxB,EAAA;IAAA,MAAAG,MAAA,GAlqCxkTzB,EAAE,CAAA0B,aAAA;IAAF1B,EAAE,CAAA6D,UAAA,CAAApC,MAAA,CAAA4J,yBAkqCsiT,CAAC;IAlqCziTrL,EAAE,CAAAiD,SAAA,CAkqC+jT,CAAC;IAlqClkTjD,EAAE,CAAAsL,iBAAA,CAAA7J,MAAA,CAAA8J,oBAkqC+jT,CAAC;EAAA;AAAA;AAAA,SAAAC,qCAAAlK,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlqClkTtB,EAAE,CAAAmC,cAAA,aAkqCk/R,CAAC;IAlqCr/RnC,EAAE,CAAAwB,SAAA,QAkqC4/R,CAAC;IAlqC//RxB,EAAE,CAAAmC,cAAA,aAkqCgiS,CAAC;IAlqCniSnC,EAAE,CAAA4D,MAAA,EAkqCijS,CAAC;IAlqCpjS5D,EAAE,CAAA8C,YAAA,CAkqCujS,CAAC;IAlqC1jS9C,EAAE,CAAA4F,UAAA,IAAAsF,0CAAA,iBAkqCuoS,CAAC,IAAAE,0CAAA,iBAA+Z,CAAC;IAlqC1iTpL,EAAE,CAAA8C,YAAA,CAkqC+kT,CAAC;EAAA;EAAA,IAAAxB,EAAA;IAAA,MAAAG,MAAA,GAlqCllTzB,EAAE,CAAA0B,aAAA;IAAF1B,EAAE,CAAAiD,SAAA,EAkqCijS,CAAC;IAlqCpjSjD,EAAE,CAAAsL,iBAAA,CAAA7J,MAAA,CAAAgK,aAkqCijS,CAAC;IAlqCpjSzL,EAAE,CAAAiD,SAAA,CAkqCkmS,CAAC;IAlqCrmSjD,EAAE,CAAAgD,UAAA,SAAAvB,MAAA,CAAAsC,cAAA,kBAAAtC,MAAA,CAAAsC,cAAA,CAAAC,MAkqCkmS,CAAC;IAlqCrmShE,EAAE,CAAAiD,SAAA,CAkqC4/S,CAAC;IAlqC//SjD,EAAE,CAAAgD,UAAA,WAAAvB,MAAA,CAAAsC,cAAA,kBAAAtC,MAAA,CAAAsC,cAAA,CAAAC,MAAA,KAAAvC,MAAA,CAAAwJ,gBAkqC4/S,CAAC;EAAA;AAAA;AAAA,SAAAS,8CAAApK,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAqK,IAAA,GAlqC//S3L,EAAE,CAAAkC,gBAAA;IAAFlC,EAAE,CAAAmC,cAAA,gBAkqCkxT,CAAC;IAlqCrxTnC,EAAE,CAAAoC,UAAA,mBAAAwJ,sEAAAtJ,MAAA;MAAFtC,EAAE,CAAAuC,aAAA,CAAAoJ,IAAA;MAAA,MAAAlK,MAAA,GAAFzB,EAAE,CAAA0B,aAAA;MAAA,OAAF1B,EAAE,CAAAwC,WAAA,CAkqC2vTf,MAAA,CAAAoK,aAAA,CAAAvJ,MAAoB,CAAC;IAAA,CAAC,CAAC;IAlqCpxTtC,EAAE,CAAA4D,MAAA,EAkqCwyT,CAAC;IAlqC3yT5D,EAAE,CAAA8C,YAAA,CAkqCizT,CAAC;EAAA;EAAA,IAAAxB,EAAA;IAAA,MAAAG,MAAA,GAlqCpzTzB,EAAE,CAAA0B,aAAA;IAAF1B,EAAE,CAAA6D,UAAA,CAAApC,MAAA,CAAAqK,mBAkqC+uT,CAAC;IAlqClvT9L,EAAE,CAAAiD,SAAA,CAkqCwyT,CAAC;IAlqC3yTjD,EAAE,CAAAsL,iBAAA,CAAA7J,MAAA,CAAAsK,kBAkqCwyT,CAAC;EAAA;AAAA;AAAA,SAAAC,8CAAA1K,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA2K,IAAA,GAlqC3yTjM,EAAE,CAAAkC,gBAAA;IAAFlC,EAAE,CAAAmC,cAAA,gBAkqCs6T,CAAC;IAlqCz6TnC,EAAE,CAAAoC,UAAA,mBAAA8J,sEAAA5J,MAAA;MAAFtC,EAAE,CAAAuC,aAAA,CAAA0J,IAAA;MAAA,MAAAxK,MAAA,GAAFzB,EAAE,CAAA0B,aAAA;MAAA,OAAF1B,EAAE,CAAAwC,WAAA,CAkqC+4Tf,MAAA,CAAA+C,aAAA,CAAAlC,MAAoB,CAAC;IAAA,CAAC,CAAC;IAlqCx6TtC,EAAE,CAAA4D,MAAA,EAkqCw7T,CAAC;IAlqC37T5D,EAAE,CAAA8C,YAAA,CAkqCi8T,CAAC;EAAA;EAAA,IAAAxB,EAAA;IAAA,MAAAG,MAAA,GAlqCp8TzB,EAAE,CAAA0B,aAAA;IAAF1B,EAAE,CAAA6D,UAAA,CAAApC,MAAA,CAAA0K,eAkqCm4T,CAAC;IAlqCt4TnM,EAAE,CAAAiD,SAAA,CAkqCw7T,CAAC;IAlqC37TjD,EAAE,CAAAsL,iBAAA,CAAA7J,MAAA,CAAA2K,cAkqCw7T,CAAC;EAAA;AAAA;AAAA,SAAAC,qCAAA/K,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlqC37TtB,EAAE,CAAAmC,cAAA,aAkqCupT,CAAC;IAlqC1pTnC,EAAE,CAAA4F,UAAA,IAAA8F,6CAAA,oBAkqCkxT,CAAC,IAAAM,6CAAA,oBAAmJ,CAAC;IAlqCz6ThM,EAAE,CAAA8C,YAAA,CAkqC28T,CAAC;EAAA;EAAA,IAAAxB,EAAA;IAAA,MAAAG,MAAA,GAlqC98TzB,EAAE,CAAA0B,aAAA;IAAF1B,EAAE,CAAAiD,SAAA,CAkqC2rT,CAAC;IAlqC9rTjD,EAAE,CAAAgD,UAAA,SAAAvB,MAAA,CAAA6K,cAkqC2rT,CAAC;IAlqC9rTtM,EAAE,CAAAiD,SAAA,CAkqCm1T,CAAC;IAlqCt1TjD,EAAE,CAAAgD,UAAA,SAAAvB,MAAA,CAAA8K,UAkqCm1T,CAAC;EAAA;AAAA;AAAA,SAAAC,oDAAAlL,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlqCt1TtB,EAAE,CAAAyM,kBAAA,EAkqCklU,CAAC;EAAA;AAAA;AAAA,SAAAC,qCAAApL,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlqCrlUtB,EAAE,CAAAmC,cAAA,aAkqCygU,CAAC;IAlqC5gUnC,EAAE,CAAA4F,UAAA,IAAA4G,mDAAA,0BAkqCmkU,CAAC;IAlqCtkUxM,EAAE,CAAA8C,YAAA,CAkqC4lU,CAAC;EAAA;EAAA,IAAAxB,EAAA;IAAA,MAAAG,MAAA,GAlqC/lUzB,EAAE,CAAA0B,aAAA;IAAF1B,EAAE,CAAAiD,SAAA,CAkqCgkU,CAAC;IAlqCnkUjD,EAAE,CAAAgD,UAAA,qBAAAvB,MAAA,CAAAkL,eAkqCgkU,CAAC;EAAA;AAAA;AA3yChqU,IAAIC,YAAY;AAChB,CAAC,UAAUA,YAAY,EAAE;EACrBA,YAAY,CAACA,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK;EAC7CA,YAAY,CAACA,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EAC/CA,YAAY,CAACA,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EAC/CA,YAAY,CAACA,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;AACnD,CAAC,EAAEA,YAAY,KAAKA,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC;AACvC,MAAMC,IAAI,CAAC;EACPtE,CAAC;EACDC,CAAC;EACDC,CAAC;EACD7D,CAAC;EACDkI,WAAWA,CAACvE,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE7D,CAAC,EAAE;IACpB,IAAI,CAAC2D,CAAC,GAAGA,CAAC;IACV,IAAI,CAACC,CAAC,GAAGA,CAAC;IACV,IAAI,CAACC,CAAC,GAAGA,CAAC;IACV,IAAI,CAAC7D,CAAC,GAAGA,CAAC;EACd;AACJ;AACA,MAAMmI,IAAI,CAAC;EACP3F,CAAC;EACDhE,CAAC;EACDD,CAAC;EACDyB,CAAC;EACDkI,WAAWA,CAAC1F,CAAC,EAAEhE,CAAC,EAAED,CAAC,EAAEyB,CAAC,EAAE;IACpB,IAAI,CAACwC,CAAC,GAAGA,CAAC;IACV,IAAI,CAAChE,CAAC,GAAGA,CAAC;IACV,IAAI,CAACD,CAAC,GAAGA,CAAC;IACV,IAAI,CAACyB,CAAC,GAAGA,CAAC;EACd;AACJ;AACA,MAAMoI,IAAI,CAAC;EACP5F,CAAC;EACDhE,CAAC;EACDiE,CAAC;EACDzC,CAAC;EACDkI,WAAWA,CAAC1F,CAAC,EAAEhE,CAAC,EAAEiE,CAAC,EAAEzC,CAAC,EAAE;IACpB,IAAI,CAACwC,CAAC,GAAGA,CAAC;IACV,IAAI,CAAChE,CAAC,GAAGA,CAAC;IACV,IAAI,CAACiE,CAAC,GAAGA,CAAC;IACV,IAAI,CAACzC,CAAC,GAAGA,CAAC;EACd;AACJ;AACA,MAAMqI,IAAI,CAAC;EACPnH,CAAC;EACDC,CAAC;EACDC,CAAC;EACDC,CAAC;EACDrB,CAAC;EACDkI,WAAWA,CAAChH,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAErB,CAAC,GAAG,CAAC,EAAE;IAC3B,IAAI,CAACkB,CAAC,GAAGA,CAAC;IACV,IAAI,CAACC,CAAC,GAAGA,CAAC;IACV,IAAI,CAACC,CAAC,GAAGA,CAAC;IACV,IAAI,CAACC,CAAC,GAAGA,CAAC;IACV,IAAI,CAACrB,CAAC,GAAGA,CAAC;EACd;AACJ;AAEA,SAASsI,wBAAwBA,CAACC,QAAQ,EAAEC,eAAe,EAAE;EACzD;EACA,IAAIC,YAAY,GAAG,OAAO;EAC1B,IAAIC,YAAY,GAAG,QAAQ;EAC3B;EACA,MAAM;IAAEC,MAAM;IAAEC;EAAM,CAAC,GAAGL,QAAQ;EAClC,MAAM;IAAEM,GAAG;IAAEC;EAAK,CAAC,GAAGN,eAAe;EACrC,MAAMO,MAAM,GAAGF,GAAG,GAAGL,eAAe,CAACG,MAAM;EAC3C,MAAMK,KAAK,GAAGF,IAAI,GAAGN,eAAe,CAACI,KAAK;EAC1C,MAAMK,YAAY,GAAGJ,GAAG,GAAGF,MAAM,GAAG,CAAC;EACrC,MAAMO,eAAe,GAAGH,MAAM,GAAGJ,MAAM,IAAIQ,MAAM,CAACC,WAAW,IAAIC,QAAQ,CAACC,eAAe,CAACC,YAAY,CAAC;EACvG,MAAMC,aAAa,GAAGV,IAAI,GAAGF,KAAK,GAAG,CAAC;EACtC,MAAMa,cAAc,GAAGT,KAAK,GAAGJ,KAAK,IAAIO,MAAM,CAACO,UAAU,IAAIL,QAAQ,CAACC,eAAe,CAACK,WAAW,CAAC;EAClG,MAAMC,YAAY,GAAGX,YAAY,IAAIC,eAAe,IAAIM,aAAa,IAAIC,cAAc;EACvF;EACA,IAAIP,eAAe,EAAE;IACjBR,YAAY,GAAG,KAAK;EACxB;EACA,IAAIO,YAAY,EAAE;IACdP,YAAY,GAAG,QAAQ;EAC3B;EACA,IAAIc,aAAa,EAAE;IACff,YAAY,GAAG,OAAO;EAC1B;EACA,IAAIgB,cAAc,EAAE;IAChBhB,YAAY,GAAG,MAAM;EACzB;EACA;EACA,IAAImB,YAAY,EAAE;IACd,MAAMC,QAAQ,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,CAAC;IACnD,OAAOA,QAAQ,CAACC,MAAM,CAAC,CAACC,IAAI,EAAEC,IAAI,KAAKzB,QAAQ,CAACwB,IAAI,CAAC,GAAGxB,QAAQ,CAACyB,IAAI,CAAC,GAAGD,IAAI,GAAGC,IAAI,CAAC;EACzF;EACA,IAAKR,aAAa,IAAIC,cAAc,EAAG;IACnC,IAAIR,YAAY,EAAE;MACd,OAAO,QAAQ;IACnB;IACA,IAAIC,eAAe,EAAE;MACjB,OAAO,KAAK;IAChB;IACA,OAAOL,GAAG,GAAGE,MAAM,GAAG,KAAK,GAAG,QAAQ;EAC1C;EACA,IAAKE,YAAY,IAAIC,eAAe,EAAG;IACnC,IAAIM,aAAa,EAAE;MACf,OAAO,OAAO;IAClB;IACA,IAAIC,cAAc,EAAE;MAChB,OAAO,MAAM;IACjB;IACA,OAAOX,IAAI,GAAGE,KAAK,GAAG,MAAM,GAAG,OAAO;EAC1C;EACA,OAAO,GAAGN,YAAY,IAAID,YAAY,EAAE;AAC5C;AACA,SAASwB,QAAQA,CAAA,EAAG;EAChB,IAAIC,EAAE,GAAG,EAAE;EACX,IAAI,OAAOC,SAAS,KAAK,WAAW,EAAE;IAClCD,EAAE,GAAGC,SAAS,CAACC,SAAS,CAACC,WAAW,CAAC,CAAC;EAC1C;EACA,MAAMC,IAAI,GAAGJ,EAAE,CAACK,OAAO,CAAC,OAAO,CAAC;EAChC,IAAID,IAAI,GAAG,CAAC,EAAE;IACV;IACA,OAAOE,QAAQ,CAACN,EAAE,CAACO,SAAS,CAACH,IAAI,GAAG,CAAC,EAAEJ,EAAE,CAACK,OAAO,CAAC,GAAG,EAAED,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC;EACtE;EACA;EACA,OAAO,KAAK;AAChB;AACA,MAAMI,aAAa,CAAC;EAChBC,EAAE;EACFC,IAAI;EACJC,QAAQ,GAAG,IAAIxP,YAAY,CAAC,CAAC;EAC7ByP,WAAWA,CAACC,KAAK,EAAE;IACf,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAAM,CAACD,KAAK;IAChC,IAAI,IAAI,CAACL,EAAE,KAAKO,SAAS,EAAE;MACvB,IAAI,CAACL,QAAQ,CAACM,IAAI,CAACH,KAAK,CAAC;IAC7B,CAAC,MACI;MACD,MAAMI,OAAO,GAAGC,UAAU,CAACL,KAAK,CAAC;MACjC,IAAI,CAACH,QAAQ,CAACM,IAAI,CAAC;QAAE5M,CAAC,EAAE6M,OAAO;QAAET,EAAE,EAAE,IAAI,CAACA;MAAG,CAAC,CAAC;IACnD;EACJ;EACA,OAAOW,IAAI,YAAAC,sBAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwFd,aAAa;EAAA;EAChH,OAAOe,IAAI,kBAD8ErQ,EAAE,CAAAsQ,iBAAA;IAAAC,IAAA,EACJjB,aAAa;IAAAkB,SAAA;IAAAC,YAAA,WAAAC,2BAAApP,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QADXtB,EAAE,CAAAoC,UAAA,mBAAAuO,uCAAArO,MAAA;UAAA,OACJf,GAAA,CAAAmO,WAAA,CAAApN,MAAkB,CAAC;QAAA,CAAP,CAAC;MAAA;IAAA;IAAAsO,MAAA;MAAArB,EAAA;MAAAC,IAAA;IAAA;IAAAqB,OAAA;MAAApB,QAAA;IAAA;EAAA;AACxG;AACA;EAAA,QAAAqB,SAAA,oBAAAA,SAAA,KAH6F9Q,EAAE,CAAA+Q,iBAAA,CAGJzB,aAAa,EAAc,CAAC;IAC3GiB,IAAI,EAAErQ,SAAS;IACf8Q,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,QAAkB;IAAE1B,EAAE,EAAE,CAAC;MACnBgB,IAAI,EAAEpQ;IACV,CAAC,CAAC;IAAEqP,IAAI,EAAE,CAAC;MACPe,IAAI,EAAEpQ;IACV,CAAC,CAAC;IAAEsP,QAAQ,EAAE,CAAC;MACXc,IAAI,EAAEnQ;IACV,CAAC,CAAC;IAAEsP,WAAW,EAAE,CAAC;MACda,IAAI,EAAElQ,YAAY;MAClB2Q,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC;IAC9B,CAAC;EAAE,CAAC;AAAA;AAChB,MAAME,eAAe,CAAC;EAClBC,KAAK;EACLC,YAAY;EACZC,YAAY;EACZC,GAAG;EACHC,GAAG;EACHrO,MAAM;EACNsO,OAAO,GAAG,IAAIvR,YAAY,CAAC,CAAC;EAC5BwR,SAAS,GAAG,IAAIxR,YAAY,CAAC,CAAC;EAC9BwP,QAAQ,GAAG,IAAIxP,YAAY,CAAC,CAAC;EAC7ByR,SAASA,CAAC/B,KAAK,EAAE;IACb,IAAI,CAACgC,KAAK,CAAChC,KAAK,CAAC;EACrB;EACAiC,UAAUA,CAACjC,KAAK,EAAE;IACd,IAAI,CAACgC,KAAK,CAAChC,KAAK,CAAC;EACrB;EACA7C,WAAWA,CAACqE,KAAK,EAAE;IACf,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,YAAY,GAAIzB,KAAK,IAAK,IAAI,CAACkC,IAAI,CAAClC,KAAK,CAAC;IAC/C,IAAI,CAAC0B,YAAY,GAAG,MAAM,IAAI,CAACS,IAAI,CAAC,CAAC;EACzC;EACAD,IAAIA,CAAClC,KAAK,EAAE;IACRA,KAAK,CAACoC,cAAc,CAAC,CAAC;IACtB,IAAI,CAACC,SAAS,CAACrC,KAAK,CAAC;EACzB;EACAgC,KAAKA,CAAChC,KAAK,EAAE;IACT,IAAI,CAACqC,SAAS,CAACrC,KAAK,CAAC;IACrBA,KAAK,CAACsC,eAAe,CAAC,CAAC;IACvBhE,QAAQ,CAACiE,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACb,YAAY,CAAC;IACvDpD,QAAQ,CAACiE,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAACb,YAAY,CAAC;IACxDpD,QAAQ,CAACiE,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACd,YAAY,CAAC;IACzDnD,QAAQ,CAACiE,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACd,YAAY,CAAC;IACzD,IAAI,CAACK,SAAS,CAAC1B,IAAI,CAAC,CAAC;EACzB;EACA+B,IAAIA,CAAA,EAAG;IACH7D,QAAQ,CAACkE,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAACd,YAAY,CAAC;IAC1DpD,QAAQ,CAACkE,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAACd,YAAY,CAAC;IAC3DpD,QAAQ,CAACkE,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACf,YAAY,CAAC;IAC5DnD,QAAQ,CAACkE,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACf,YAAY,CAAC;IAC5D,IAAI,CAACI,OAAO,CAACzB,IAAI,CAAC,CAAC;EACvB;EACAqC,IAAIA,CAACzC,KAAK,EAAE;IACR,MAAM0C,QAAQ,GAAG,IAAI,CAAClB,KAAK,CAACmB,aAAa,CAACC,qBAAqB,CAAC,CAAC;IACjE,MAAMC,KAAK,GAAI7C,KAAK,CAAC6C,KAAK,KAAK1C,SAAS,GAAIH,KAAK,CAAC6C,KAAK,GAAG7C,KAAK,CAAC8C,OAAO,CAAC,CAAC,CAAC,CAACD,KAAK;IAChF,OAAOA,KAAK,GAAGH,QAAQ,CAAC3E,IAAI,GAAGK,MAAM,CAAC2E,WAAW;EACrD;EACAC,IAAIA,CAAChD,KAAK,EAAE;IACR,MAAM0C,QAAQ,GAAG,IAAI,CAAClB,KAAK,CAACmB,aAAa,CAACC,qBAAqB,CAAC,CAAC;IACjE,MAAMK,KAAK,GAAIjD,KAAK,CAACiD,KAAK,KAAK9C,SAAS,GAAIH,KAAK,CAACiD,KAAK,GAAGjD,KAAK,CAAC8C,OAAO,CAAC,CAAC,CAAC,CAACG,KAAK;IAChF,OAAOA,KAAK,GAAGP,QAAQ,CAAC5E,GAAG,GAAGM,MAAM,CAAC8E,WAAW;EACpD;EACAb,SAASA,CAACrC,KAAK,EAAE;IACb,MAAMnC,KAAK,GAAG,IAAI,CAAC2D,KAAK,CAACmB,aAAa,CAACQ,WAAW;IAClD,MAAMvF,MAAM,GAAG,IAAI,CAAC4D,KAAK,CAACmB,aAAa,CAACS,YAAY;IACpD,MAAMC,CAAC,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,IAAI,CAACf,IAAI,CAACzC,KAAK,CAAC,EAAEnC,KAAK,CAAC,CAAC;IACxD,MAAMxH,CAAC,GAAGiN,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,IAAI,CAACR,IAAI,CAAChD,KAAK,CAAC,EAAEpC,MAAM,CAAC,CAAC;IACzD,IAAI,IAAI,CAAC+D,GAAG,KAAKxB,SAAS,IAAI,IAAI,CAACyB,GAAG,KAAKzB,SAAS,EAAE;MAClD,IAAI,CAACL,QAAQ,CAACM,IAAI,CAAC;QAAE3M,CAAC,EAAE4P,CAAC,GAAGxF,KAAK;QAAErK,CAAC,EAAG,CAAC,GAAG6C,CAAC,GAAGuH,MAAO;QAAE+D,GAAG,EAAE,IAAI,CAACA,GAAG;QAAEC,GAAG,EAAE,IAAI,CAACA;MAAI,CAAC,CAAC;IAC3F,CAAC,MACI,IAAI,IAAI,CAACD,GAAG,KAAKxB,SAAS,IAAI,IAAI,CAACyB,GAAG,KAAKzB,SAAS,EAAE;MACvD,IAAI,CAACL,QAAQ,CAACM,IAAI,CAAC;QAAE5M,CAAC,EAAE6C,CAAC,GAAGuH,MAAM;QAAEgE,GAAG,EAAE,IAAI,CAACA;MAAI,CAAC,CAAC;IACxD,CAAC,MACI,IAAI,IAAI,CAACD,GAAG,KAAKxB,SAAS,IAAI,IAAI,CAACyB,GAAG,KAAKzB,SAAS,EAAE;MACvD,IAAI,CAACL,QAAQ,CAACM,IAAI,CAAC;QAAE5M,CAAC,EAAE6P,CAAC,GAAGxF,KAAK;QAAE8D,GAAG,EAAE,IAAI,CAACA;MAAI,CAAC,CAAC;IACvD;EACJ;EACA,OAAOpB,IAAI,YAAAkD,wBAAAhD,iBAAA;IAAA,YAAAA,iBAAA,IAAwFc,eAAe,EApFzBlR,EAAE,CAAAqT,iBAAA,CAoFyCrT,EAAE,CAACsT,UAAU;EAAA;EACjJ,OAAOjD,IAAI,kBArF8ErQ,EAAE,CAAAsQ,iBAAA;IAAAC,IAAA,EAqFJW,eAAe;IAAAV,SAAA;IAAAC,YAAA,WAAA8C,6BAAAjS,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QArFbtB,EAAE,CAAAoC,UAAA,uBAAAoR,6CAAAlR,MAAA;UAAA,OAqFJf,GAAA,CAAAmQ,SAAA,CAAApP,MAAgB,CAAC;QAAA,CAAH,CAAC,wBAAAmR,8CAAAnR,MAAA;UAAA,OAAff,GAAA,CAAAqQ,UAAA,CAAAtP,MAAiB,CAAC;QAAA,CAAJ,CAAC;MAAA;IAAA;IAAAsO,MAAA;MAAAU,GAAA;MAAAC,GAAA;MAAArO,MAAA;IAAA;IAAA2N,OAAA;MAAAW,OAAA;MAAAC,SAAA;MAAAhC,QAAA;IAAA;EAAA;AAC1G;AACA;EAAA,QAAAqB,SAAA,oBAAAA,SAAA,KAvF6F9Q,EAAE,CAAA+Q,iBAAA,CAuFJG,eAAe,EAAc,CAAC;IAC7GX,IAAI,EAAErQ,SAAS;IACf8Q,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEV,IAAI,EAAEvQ,EAAE,CAACsT;EAAW,CAAC,CAAC,EAAkB;IAAEhC,GAAG,EAAE,CAAC;MACrEf,IAAI,EAAEpQ;IACV,CAAC,CAAC;IAAEoR,GAAG,EAAE,CAAC;MACNhB,IAAI,EAAEpQ;IACV,CAAC,CAAC;IAAE+C,MAAM,EAAE,CAAC;MACTqN,IAAI,EAAEpQ;IACV,CAAC,CAAC;IAAEqR,OAAO,EAAE,CAAC;MACVjB,IAAI,EAAEnQ;IACV,CAAC,CAAC;IAAEqR,SAAS,EAAE,CAAC;MACZlB,IAAI,EAAEnQ;IACV,CAAC,CAAC;IAAEqP,QAAQ,EAAE,CAAC;MACXc,IAAI,EAAEnQ;IACV,CAAC,CAAC;IAAEsR,SAAS,EAAE,CAAC;MACZnB,IAAI,EAAElQ,YAAY;MAClB2Q,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC;IAClC,CAAC,CAAC;IAAEY,UAAU,EAAE,CAAC;MACbrB,IAAI,EAAElQ,YAAY;MAClB2Q,IAAI,EAAE,CAAC,YAAY,EAAE,CAAC,QAAQ,CAAC;IACnC,CAAC;EAAE,CAAC;AAAA;AAChB,MAAM0C,cAAc,CAAC;EACjBtM,CAAC;EACDhE,CAAC;EACDD,CAAC;EACDyB,CAAC;EACDkI,WAAWA,CAAC1F,CAAC,EAAEhE,CAAC,EAAED,CAAC,EAAEyB,CAAC,EAAE;IACpB,IAAI,CAACwC,CAAC,GAAGA,CAAC;IACV,IAAI,CAAChE,CAAC,GAAGA,CAAC;IACV,IAAI,CAACD,CAAC,GAAGA,CAAC;IACV,IAAI,CAACyB,CAAC,GAAGA,CAAC;EACd;AACJ;AACA,MAAM+O,eAAe,CAAC;EAClBvM,CAAC;EACDhE,CAAC;EACDD,CAAC;EACDyB,CAAC;EACDkI,WAAWA,CAAC1F,CAAC,EAAEhE,CAAC,EAAED,CAAC,EAAEyB,CAAC,EAAE;IACpB,IAAI,CAACwC,CAAC,GAAGA,CAAC;IACV,IAAI,CAAChE,CAAC,GAAGA,CAAC;IACV,IAAI,CAACD,CAAC,GAAGA,CAAC;IACV,IAAI,CAACyB,CAAC,GAAGA,CAAC;EACd;AACJ;AAEA,MAAMgP,kBAAkB,CAAC;EACrBC,MAAM,GAAG,IAAI;EACbC,SAASA,CAACD,MAAM,EAAE;IACd,IAAI,IAAI,CAACA,MAAM,IAAI,IAAI,CAACA,MAAM,KAAKA,MAAM,IAAI,IAAI,CAACA,MAAM,CAACE,eAAe,KAAK,QAAQ,EAAE;MACnF,IAAI,CAACF,MAAM,CAACG,WAAW,CAAC,CAAC;IAC7B;IACA,IAAI,CAACH,MAAM,GAAGA,MAAM;EACxB;EACAI,SAASA,CAACC,IAAI,EAAE;IACZ,MAAM9M,CAAC,GAAG8M,IAAI,CAAC9M,CAAC;MAAEhE,CAAC,GAAG8Q,IAAI,CAAC9Q,CAAC;MAAED,CAAC,GAAG+Q,IAAI,CAAC/Q,CAAC;MAAEyB,CAAC,GAAGsP,IAAI,CAACtP,CAAC;IACpD,IAAIzB,CAAC,KAAK,CAAC,EAAE;MACT,OAAO,IAAI6J,IAAI,CAAC5F,CAAC,EAAE,CAAC,EAAE,CAAC,EAAExC,CAAC,CAAC;IAC/B,CAAC,MACI,IAAIxB,CAAC,KAAK,CAAC,IAAID,CAAC,KAAK,CAAC,EAAE;MACzB,OAAO,IAAI6J,IAAI,CAAC5F,CAAC,EAAE,CAAC,EAAE,CAAC,EAAExC,CAAC,CAAC;IAC/B,CAAC,MACI;MACD,MAAMyC,CAAC,GAAGlE,CAAC,IAAI,CAAC,GAAGC,CAAC,CAAC,GAAG,CAAC;MACzB,OAAO,IAAI4J,IAAI,CAAC5F,CAAC,EAAEjE,CAAC,GAAGC,CAAC,IAAI,CAAC,GAAG6P,IAAI,CAACkB,GAAG,CAAC,CAAC,GAAG9M,CAAC,GAAG,CAAC,CAAC,CAAC,EAAEA,CAAC,EAAEzC,CAAC,CAAC;IAC/D;EACJ;EACAwP,SAASA,CAACC,IAAI,EAAE;IACZ,MAAMjN,CAAC,GAAG6L,IAAI,CAACE,GAAG,CAACkB,IAAI,CAACjN,CAAC,EAAE,CAAC,CAAC;MAAEhE,CAAC,GAAG6P,IAAI,CAACE,GAAG,CAACkB,IAAI,CAACjR,CAAC,EAAE,CAAC,CAAC;IACtD,MAAMiE,CAAC,GAAG4L,IAAI,CAACE,GAAG,CAACkB,IAAI,CAAChN,CAAC,EAAE,CAAC,CAAC;MAAEzC,CAAC,GAAGqO,IAAI,CAACE,GAAG,CAACkB,IAAI,CAACzP,CAAC,EAAE,CAAC,CAAC;IACtD,IAAIyC,CAAC,KAAK,CAAC,EAAE;MACT,OAAO,IAAI0F,IAAI,CAAC3F,CAAC,EAAE,CAAC,EAAE,CAAC,EAAExC,CAAC,CAAC;IAC/B,CAAC,MACI;MACD,MAAMzB,CAAC,GAAGkE,CAAC,GAAGjE,CAAC,IAAI,CAAC,GAAG6P,IAAI,CAACkB,GAAG,CAAC,CAAC,GAAG9M,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;MAC/C,OAAO,IAAI0F,IAAI,CAAC3F,CAAC,EAAE,CAAC,IAAIjE,CAAC,GAAGkE,CAAC,CAAC,GAAGlE,CAAC,EAAEA,CAAC,EAAEyB,CAAC,CAAC;IAC7C;EACJ;EACA0P,UAAUA,CAACJ,IAAI,EAAE;IACb,IAAI3L,CAAC,EAAEC,CAAC,EAAEC,CAAC;IACX,MAAMrB,CAAC,GAAG8M,IAAI,CAAC9M,CAAC;MAAEhE,CAAC,GAAG8Q,IAAI,CAAC9Q,CAAC;MAAED,CAAC,GAAG+Q,IAAI,CAAC/Q,CAAC;MAAEyB,CAAC,GAAGsP,IAAI,CAACtP,CAAC;IACpD,MAAM2P,CAAC,GAAGtB,IAAI,CAACuB,KAAK,CAACpN,CAAC,GAAG,CAAC,CAAC;IAC3B,MAAMqN,CAAC,GAAGrN,CAAC,GAAG,CAAC,GAAGmN,CAAC;IACnB,MAAMG,CAAC,GAAGvR,CAAC,IAAI,CAAC,GAAGC,CAAC,CAAC;IACrB,MAAMuR,CAAC,GAAGxR,CAAC,IAAI,CAAC,GAAGsR,CAAC,GAAGrR,CAAC,CAAC;IACzB,MAAMwR,CAAC,GAAGzR,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAGsR,CAAC,IAAIrR,CAAC,CAAC;IAC/B,QAAQmR,CAAC,GAAG,CAAC;MACT,KAAK,CAAC;QACFhM,CAAC,GAAGpF,CAAC,EAAEqF,CAAC,GAAGoM,CAAC,EAAEnM,CAAC,GAAGiM,CAAC;QACnB;MACJ,KAAK,CAAC;QACFnM,CAAC,GAAGoM,CAAC,EAAEnM,CAAC,GAAGrF,CAAC,EAAEsF,CAAC,GAAGiM,CAAC;QACnB;MACJ,KAAK,CAAC;QACFnM,CAAC,GAAGmM,CAAC,EAAElM,CAAC,GAAGrF,CAAC,EAAEsF,CAAC,GAAGmM,CAAC;QACnB;MACJ,KAAK,CAAC;QACFrM,CAAC,GAAGmM,CAAC,EAAElM,CAAC,GAAGmM,CAAC,EAAElM,CAAC,GAAGtF,CAAC;QACnB;MACJ,KAAK,CAAC;QACFoF,CAAC,GAAGqM,CAAC,EAAEpM,CAAC,GAAGkM,CAAC,EAAEjM,CAAC,GAAGtF,CAAC;QACnB;MACJ,KAAK,CAAC;QACFoF,CAAC,GAAGpF,CAAC,EAAEqF,CAAC,GAAGkM,CAAC,EAAEjM,CAAC,GAAGkM,CAAC;QACnB;MACJ;QACIpM,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,CAAC;IAC3B;IACA,OAAO,IAAIoE,IAAI,CAACtE,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE7D,CAAC,CAAC;EAC/B;EACAiQ,SAASA,CAACC,IAAI,EAAE;IACZ,MAAMvM,CAAC,GAAG,CAAC,CAAC,GAAGuM,IAAI,CAAChP,CAAC,KAAK,CAAC,GAAGgP,IAAI,CAAC7O,CAAC,CAAC;IACrC,MAAMuC,CAAC,GAAG,CAAC,CAAC,GAAGsM,IAAI,CAAC/O,CAAC,KAAK,CAAC,GAAG+O,IAAI,CAAC7O,CAAC,CAAC;IACrC,MAAMwC,CAAC,GAAG,CAAC,CAAC,GAAGqM,IAAI,CAAC9O,CAAC,KAAK,CAAC,GAAG8O,IAAI,CAAC7O,CAAC,CAAC;IACrC,OAAO,IAAI4G,IAAI,CAACtE,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEqM,IAAI,CAAClQ,CAAC,CAAC;EACpC;EACAmQ,UAAUA,CAACC,IAAI,EAAE;IACb,MAAM/O,CAAC,GAAG,CAAC,GAAGgN,IAAI,CAACC,GAAG,CAAC8B,IAAI,CAACzM,CAAC,EAAEyM,IAAI,CAACxM,CAAC,EAAEwM,IAAI,CAACvM,CAAC,CAAC;IAC9C,IAAIxC,CAAC,KAAK,CAAC,EAAE;MACT,OAAO,IAAIgH,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE+H,IAAI,CAACpQ,CAAC,CAAC;IACvC,CAAC,MACI;MACD,MAAMkB,CAAC,GAAG,CAAC,CAAC,GAAGkP,IAAI,CAACzM,CAAC,GAAGtC,CAAC,KAAK,CAAC,GAAGA,CAAC,CAAC;MACpC,MAAMF,CAAC,GAAG,CAAC,CAAC,GAAGiP,IAAI,CAACxM,CAAC,GAAGvC,CAAC,KAAK,CAAC,GAAGA,CAAC,CAAC;MACpC,MAAMD,CAAC,GAAG,CAAC,CAAC,GAAGgP,IAAI,CAACvM,CAAC,GAAGxC,CAAC,KAAK,CAAC,GAAGA,CAAC,CAAC;MACpC,OAAO,IAAIgH,IAAI,CAACnH,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE+O,IAAI,CAACpQ,CAAC,CAAC;IACvC;EACJ;EACAqQ,UAAUA,CAACD,IAAI,EAAE;IACb,IAAI5N,CAAC,EAAEhE,CAAC;IACR,MAAMmF,CAAC,GAAG0K,IAAI,CAACE,GAAG,CAAC6B,IAAI,CAACzM,CAAC,EAAE,CAAC,CAAC;MAAEC,CAAC,GAAGyK,IAAI,CAACE,GAAG,CAAC6B,IAAI,CAACxM,CAAC,EAAE,CAAC,CAAC;IACtD,MAAMC,CAAC,GAAGwK,IAAI,CAACE,GAAG,CAAC6B,IAAI,CAACvM,CAAC,EAAE,CAAC,CAAC;MAAE7D,CAAC,GAAGqO,IAAI,CAACE,GAAG,CAAC6B,IAAI,CAACpQ,CAAC,EAAE,CAAC,CAAC;IACtD,MAAMsO,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC3K,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;MAAE0K,GAAG,GAAGF,IAAI,CAACE,GAAG,CAAC5K,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;IACtD,MAAMtF,CAAC,GAAG+P,GAAG;MAAEgC,CAAC,GAAGhC,GAAG,GAAGC,GAAG;IAC5B/P,CAAC,GAAI8P,GAAG,KAAK,CAAC,GAAI,CAAC,GAAGgC,CAAC,GAAGhC,GAAG;IAC7B,IAAIA,GAAG,KAAKC,GAAG,EAAE;MACb/L,CAAC,GAAG,CAAC;IACT,CAAC,MACI;MACD,QAAQ8L,GAAG;QACP,KAAK3K,CAAC;UACFnB,CAAC,GAAG,CAACoB,CAAC,GAAGC,CAAC,IAAIyM,CAAC,IAAI1M,CAAC,GAAGC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;UACjC;QACJ,KAAKD,CAAC;UACFpB,CAAC,GAAG,CAACqB,CAAC,GAAGF,CAAC,IAAI2M,CAAC,GAAG,CAAC;UACnB;QACJ,KAAKzM,CAAC;UACFrB,CAAC,GAAG,CAACmB,CAAC,GAAGC,CAAC,IAAI0M,CAAC,GAAG,CAAC;UACnB;QACJ;UACI9N,CAAC,GAAG,CAAC;MACb;MACAA,CAAC,IAAI,CAAC;IACV;IACA,OAAO,IAAI2F,IAAI,CAAC3F,CAAC,EAAEhE,CAAC,EAAED,CAAC,EAAEyB,CAAC,CAAC;EAC/B;EACAuQ,SAASA,CAACH,IAAI,EAAEI,SAAS,EAAE;IACvB;IACA,IAAIC,GAAG,GAAG,GAAG,GAAG,CAAE,CAAC,IAAI,EAAE,GAAKL,IAAI,CAACzM,CAAC,IAAI,EAAG,GAAIyM,IAAI,CAACxM,CAAC,IAAI,CAAE,GAAGwM,IAAI,CAACvM,CAAC,EAAE6M,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,CAAC;IAC5F,IAAIH,SAAS,EAAE;MACXC,GAAG,IAAI,CAAE,CAAC,IAAI,CAAC,GAAIpC,IAAI,CAACuC,KAAK,CAACR,IAAI,CAACpQ,CAAC,GAAG,GAAG,CAAC,EAAE0Q,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,CAAC;IACvE;IACA;IACA,OAAOF,GAAG;EACd;EACAI,aAAaA,CAACX,IAAI,EAAE;IAChB,OAAO,IAAI7H,IAAI,CAAC6H,IAAI,CAAChP,CAAC,GAAG,GAAG,EAAEgP,IAAI,CAAC/O,CAAC,GAAG,GAAG,EAAE+O,IAAI,CAAC9O,CAAC,GAAG,GAAG,EAAE8O,IAAI,CAAC7O,CAAC,GAAG,GAAG,EAAE6O,IAAI,CAAClQ,CAAC,CAAC;EACnF;EACA8Q,eAAeA,CAACZ,IAAI,EAAE;IAClB,OAAO,IAAI7H,IAAI,CAACgG,IAAI,CAACuB,KAAK,CAACM,IAAI,CAAChP,CAAC,GAAG,GAAG,CAAC,EAAEmN,IAAI,CAACuB,KAAK,CAACM,IAAI,CAAC/O,CAAC,GAAG,GAAG,CAAC,EAAEkN,IAAI,CAACuB,KAAK,CAACM,IAAI,CAAC9O,CAAC,GAAG,GAAG,CAAC,EAAEiN,IAAI,CAACuB,KAAK,CAACM,IAAI,CAAC7O,CAAC,GAAG,GAAG,CAAC,EAAE6O,IAAI,CAAClQ,CAAC,CAAC;EACnI;EACA+Q,eAAeA,CAACX,IAAI,EAAE;IAClB,OAAO,IAAInI,IAAI,CAACoG,IAAI,CAACuC,KAAK,CAACR,IAAI,CAACzM,CAAC,GAAG,GAAG,CAAC,EAAE0K,IAAI,CAACuC,KAAK,CAACR,IAAI,CAACxM,CAAC,GAAG,GAAG,CAAC,EAAEyK,IAAI,CAACuC,KAAK,CAACR,IAAI,CAACvM,CAAC,GAAG,GAAG,CAAC,EAAEuM,IAAI,CAACpQ,CAAC,CAAC;EACzG;EACAgR,YAAYA,CAACC,WAAW,GAAG,EAAE,EAAET,SAAS,GAAG,KAAK,EAAE;IAC9C,IAAIlB,IAAI,GAAG,IAAI;IACf2B,WAAW,GAAG,CAACA,WAAW,IAAI,EAAE,EAAE5G,WAAW,CAAC,CAAC;IAC/C,MAAM6G,aAAa,GAAG,CAClB;MACIC,EAAE,EAAE,2FAA2F;MAC/FC,KAAK,EAAE,SAAAA,CAAUC,UAAU,EAAE;QACzB,OAAO,IAAIpJ,IAAI,CAACuC,QAAQ,CAAC6G,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,EAAE7G,QAAQ,CAAC6G,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,EAAE7G,QAAQ,CAAC6G,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,EAAEC,KAAK,CAACjG,UAAU,CAACgG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAGhG,UAAU,CAACgG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9L;IACJ,CAAC,EAAE;MACCF,EAAE,EAAE,yFAAyF;MAC7FC,KAAK,EAAE,SAAAA,CAAUC,UAAU,EAAE;QACzB,OAAO,IAAIjJ,IAAI,CAACoC,QAAQ,CAAC6G,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,EAAE7G,QAAQ,CAAC6G,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,EAAE7G,QAAQ,CAAC6G,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,EAAEC,KAAK,CAACjG,UAAU,CAACgG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAGhG,UAAU,CAACgG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9L;IACJ,CAAC,CACJ;IACD,IAAIb,SAAS,EAAE;MACXU,aAAa,CAACK,IAAI,CAAC;QACfJ,EAAE,EAAE,qEAAqE;QACzEC,KAAK,EAAE,SAAAA,CAAUC,UAAU,EAAE;UACzB,OAAO,IAAIpJ,IAAI,CAACuC,QAAQ,CAAC6G,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,EAAE7G,QAAQ,CAAC6G,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,EAAE7G,QAAQ,CAAC6G,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,EAAE7G,QAAQ,CAAC6G,UAAU,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC;QACvK;MACJ,CAAC,CAAC;IACN,CAAC,MACI;MACDH,aAAa,CAACK,IAAI,CAAC;QACfJ,EAAE,EAAE,oDAAoD;QACxDC,KAAK,EAAE,SAAAA,CAAUC,UAAU,EAAE;UACzB,OAAO,IAAIpJ,IAAI,CAACuC,QAAQ,CAAC6G,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,EAAE7G,QAAQ,CAAC6G,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,EAAE7G,QAAQ,CAAC6G,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC;QAC/H;MACJ,CAAC,CAAC;IACN;IACAH,aAAa,CAACK,IAAI,CAAC;MACfJ,EAAE,EAAE,2CAA2C;MAC/CC,KAAK,EAAE,SAAAA,CAAUC,UAAU,EAAE;QACzB,OAAO,IAAIpJ,IAAI,CAACuC,QAAQ,CAAC6G,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,EAAE7G,QAAQ,CAAC6G,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,EAAE7G,QAAQ,CAAC6G,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC;MAC/K;IACJ,CAAC,CAAC;IACF,KAAK,MAAMG,GAAG,IAAIN,aAAa,EAAE;MAC7B,IAAIA,aAAa,CAACO,cAAc,CAACD,GAAG,CAAC,EAAE;QACnC,MAAME,MAAM,GAAGR,aAAa,CAACM,GAAG,CAAC;QACjC,MAAMG,KAAK,GAAGD,MAAM,CAACP,EAAE,CAACS,IAAI,CAACX,WAAW,CAAC;UAAEY,KAAK,GAAGF,KAAK,IAAID,MAAM,CAACN,KAAK,CAACO,KAAK,CAAC;QAC/E,IAAIE,KAAK,EAAE;UACP,IAAIA,KAAK,YAAY5J,IAAI,EAAE;YACvBqH,IAAI,GAAG,IAAI,CAACe,UAAU,CAACwB,KAAK,CAAC;UACjC,CAAC,MACI,IAAIA,KAAK,YAAYzJ,IAAI,EAAE;YAC5BkH,IAAI,GAAG,IAAI,CAACE,SAAS,CAACqC,KAAK,CAAC;UAChC;UACA,OAAOvC,IAAI;QACf;MACJ;IACJ;IACA,OAAOA,IAAI;EACf;EACAwC,YAAYA,CAACxC,IAAI,EAAEwC,YAAY,EAAEC,YAAY,EAAE;IAC3C,IAAID,YAAY,KAAK,MAAM,EAAE;MACzBA,YAAY,GAAGxC,IAAI,CAACtP,CAAC,GAAG,CAAC,GAAG,MAAM,GAAG,KAAK;IAC9C;IACA,QAAQ8R,YAAY;MAChB,KAAK,MAAM;QACP,MAAMrC,IAAI,GAAG,IAAI,CAACJ,SAAS,CAACC,IAAI,CAAC;QACjC,MAAM3N,QAAQ,GAAG,IAAIyG,IAAI,CAACiG,IAAI,CAACuC,KAAK,CAAEnB,IAAI,CAACjN,CAAC,GAAI,GAAG,CAAC,EAAE6L,IAAI,CAACuC,KAAK,CAACnB,IAAI,CAACjR,CAAC,GAAG,GAAG,CAAC,EAAE6P,IAAI,CAACuC,KAAK,CAACnB,IAAI,CAAChN,CAAC,GAAG,GAAG,CAAC,EAAE4L,IAAI,CAACuC,KAAK,CAACnB,IAAI,CAACzP,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;QACzI,IAAIsP,IAAI,CAACtP,CAAC,GAAG,CAAC,IAAI+R,YAAY,KAAK,QAAQ,EAAE;UACzC,OAAO,OAAO,GAAGpQ,QAAQ,CAACa,CAAC,GAAG,GAAG,GAAGb,QAAQ,CAACnD,CAAC,GAAG,IAAI,GAAGmD,QAAQ,CAACc,CAAC,GAAG,IAAI,GACrEd,QAAQ,CAAC3B,CAAC,GAAG,GAAG;QACxB,CAAC,MACI;UACD,OAAO,MAAM,GAAG2B,QAAQ,CAACa,CAAC,GAAG,GAAG,GAAGb,QAAQ,CAACnD,CAAC,GAAG,IAAI,GAAGmD,QAAQ,CAACc,CAAC,GAAG,IAAI;QAC5E;MACJ,KAAK,MAAM;QACP,MAAM2N,IAAI,GAAG,IAAI,CAACW,eAAe,CAAC,IAAI,CAACrB,UAAU,CAACJ,IAAI,CAAC,CAAC;QACxD,IAAIA,IAAI,CAACtP,CAAC,GAAG,CAAC,IAAI+R,YAAY,KAAK,QAAQ,EAAE;UACzC,OAAO,OAAO,GAAG3B,IAAI,CAACzM,CAAC,GAAG,GAAG,GAAGyM,IAAI,CAACxM,CAAC,GAAG,GAAG,GAAGwM,IAAI,CAACvM,CAAC,GAAG,GAAG,GACvDwK,IAAI,CAACuC,KAAK,CAACR,IAAI,CAACpQ,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG;QAC5C,CAAC,MACI;UACD,OAAO,MAAM,GAAGoQ,IAAI,CAACzM,CAAC,GAAG,GAAG,GAAGyM,IAAI,CAACxM,CAAC,GAAG,GAAG,GAAGwM,IAAI,CAACvM,CAAC,GAAG,GAAG;QAC9D;MACJ;QACI,MAAM2M,SAAS,GAAIuB,YAAY,KAAK,QAAQ,IAAIA,YAAY,KAAK,QAAS;QAC1E,OAAO,IAAI,CAACxB,SAAS,CAAC,IAAI,CAACQ,eAAe,CAAC,IAAI,CAACrB,UAAU,CAACJ,IAAI,CAAC,CAAC,EAAEkB,SAAS,CAAC;IACrF;EACJ;EACA,OAAOlF,IAAI,YAAA0G,2BAAAxG,iBAAA;IAAA,YAAAA,iBAAA,IAAwFwD,kBAAkB;EAAA;EACrH,OAAOiD,KAAK,kBA7V6E7W,EAAE,CAAA8W,kBAAA;IAAAC,KAAA,EA6VYnD,kBAAkB;IAAAoD,OAAA,EAAlBpD,kBAAkB,CAAA1D;EAAA;AAC7H;AACA;EAAA,QAAAY,SAAA,oBAAAA,SAAA,KA/V6F9Q,EAAE,CAAA+Q,iBAAA,CA+VJ6C,kBAAkB,EAAc,CAAC;IAChHrD,IAAI,EAAEjQ;EACV,CAAC,CAAC;AAAA;;AAEV;AACA;AACA,MAAM2W,cAAc,GAAG,OAAOlJ,MAAM,KAAK,WAAW,IAAI,cAAc,IAAIA,MAAM;AAChF,MAAMmJ,oBAAoB,CAAC;EACvBC,MAAM;EACNhG,KAAK;EACLiG,KAAK;EACLnJ,QAAQ;EACRoJ,UAAU;EACVC,OAAO;EACPC,MAAM,GAAG,KAAK;EACdzC,IAAI;EACJZ,IAAI;EACJ1G,KAAK;EACLD,MAAM;EACNiK,SAAS;EACTC,WAAW;EACXC,YAAY;EACZC,aAAa;EACbC,cAAc;EACdC,iBAAiB;EACjBC,iBAAiB;EACjBC,OAAO;EACPC,YAAY;EACZC,mBAAmB;EACnBC,eAAe,GAAG,EAAE;EACpBC,iBAAiB,GAAG,EAAE;EACtBC,iBAAiB,GAAG,CAChBxL,YAAY,CAACyL,GAAG,EAChBzL,YAAY,CAAC0L,IAAI,EACjB1L,YAAY,CAAC2L,IAAI,EACjB3L,YAAY,CAAC4L,IAAI,CACpB;EACDC,oBAAoB,GAAG,KAAK;EAC5BC,IAAI;EACJC,MAAM;EACNlL,GAAG;EACHC,IAAI;EACJ2E,QAAQ;EACRxM,MAAM;EACN3C,MAAM;EACNqG,OAAO;EACPT,QAAQ;EACRnE,QAAQ;EACR4B,QAAQ;EACRmB,QAAQ;EACR3F,QAAQ;EACR4B,aAAa;EACbZ,cAAc;EACd6V,gBAAgB;EAChBC,OAAO;EACPC,QAAQ;EACRC,WAAW;EACXC,aAAa;EACb9S,cAAc;EACd+S,cAAc;EACdC,cAAc;EACdnF,eAAe;EACfoF,iBAAiB;EACjBC,kBAAkB;EAClBC,mBAAmB;EACnBC,UAAU;EACV1X,aAAa;EACb2X,gBAAgB;EAChBhN,UAAU;EACVH,cAAc;EACdD,eAAe;EACfG,cAAc;EACdP,kBAAkB;EAClBD,mBAAmB;EACnB0N,YAAY;EACZC,mBAAmB;EACnBhO,aAAa;EACb1H,cAAc;EACdoH,mBAAmB;EACnBlH,uBAAuB;EACvBsH,oBAAoB;EACpBF,yBAAyB;EACzBJ,gBAAgB;EAChB9G,oBAAoB;EACpBL,qBAAqB;EACrB8G,wBAAwB;EACxB9I,eAAe;EACf4X,gBAAgB;EAChB/M,eAAe;EACfgN,aAAa;EACbC,SAAS;EACTC,WAAW;EACXC,SAASA,CAACnK,KAAK,EAAE;IACb,IAAI,IAAI,CAAC+I,IAAI,IAAI,IAAI,CAAC3E,eAAe,KAAK,OAAO,EAAE;MAC/C,IAAI,CAAClI,aAAa,CAAC8D,KAAK,CAAC;IAC7B;EACJ;EACAoK,WAAWA,CAACpK,KAAK,EAAE;IACf,IAAI,IAAI,CAAC+I,IAAI,IAAI,IAAI,CAAC3E,eAAe,KAAK,OAAO,EAAE;MAC/C,IAAI,CAACvP,aAAa,CAACmL,KAAK,CAAC;IAC7B;EACJ;EACA7C,WAAWA,CAACqK,MAAM,EAAEhG,KAAK,EAAEiG,KAAK,EAAEnJ,QAAQ,EAAEoJ,UAAU,EAAEC,OAAO,EAAE;IAC7D,IAAI,CAACH,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAChG,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACiG,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACnJ,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACoJ,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACmC,mBAAmB,GAAG1Y,iBAAiB,CAAC,IAAI,CAACsW,UAAU,CAAC,IAAI,YAAY,IAAI,IAAI,CAACpJ,QAAQ,CAAC+L,WAAW;EAC9G;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC/W,MAAM,GAAG,IAAIwQ,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5C,MAAMwG,QAAQ,GAAG,IAAI,CAACN,SAAS,CAACtH,aAAa,CAACQ,WAAW,IAAI,GAAG;IAChE,MAAMqH,UAAU,GAAG,IAAI,CAACN,WAAW,CAACvH,aAAa,CAACQ,WAAW,IAAI,GAAG;IACpE,IAAI,CAACkF,YAAY,GAAG,IAAIrE,eAAe,CAACuG,QAAQ,EAAE,IAAI,CAACrB,OAAO,EAAE,GAAG,EAAEsB,UAAU,CAAC;IAChF,IAAI,IAAI,CAACnB,aAAa,EAAE;MACpB,IAAI,CAACnT,MAAM,GAAG+G,YAAY,CAAC4L,IAAI;IACnC,CAAC,MACI,IAAI,IAAI,CAACS,cAAc,KAAK,MAAM,EAAE;MACrC,IAAI,CAACpT,MAAM,GAAG+G,YAAY,CAAC0L,IAAI;IACnC,CAAC,MACI,IAAI,IAAI,CAACW,cAAc,KAAK,MAAM,EAAE;MACrC,IAAI,CAACpT,MAAM,GAAG+G,YAAY,CAAC2L,IAAI;IACnC,CAAC,MACI;MACD,IAAI,CAAC1S,MAAM,GAAG+G,YAAY,CAACyL,GAAG;IAClC;IACA,IAAI,CAACR,iBAAiB,GAAIlI,KAAK,IAAK;MAAE,IAAI,CAACyK,WAAW,CAACzK,KAAK,CAAC;IAAE,CAAC;IAChE,IAAI,CAACiI,cAAc,GAAG,MAAM;MAAE,IAAI,CAACyC,QAAQ,CAAC,CAAC;IAAE,CAAC;IAChD,IAAI,CAACC,UAAU,CAAC,IAAI,CAAC5C,YAAY,EAAE,KAAK,CAAC;EAC7C;EACA6C,WAAWA,CAAA,EAAG;IACV,IAAI,CAACvG,WAAW,CAAC,CAAC;EACtB;EACAwG,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAAC3B,OAAO,KAAK,GAAG,IAAI,IAAI,CAAC9E,eAAe,KAAK,QAAQ,EAAE;MAC3D,MAAMmG,QAAQ,GAAG,IAAI,CAACN,SAAS,CAACtH,aAAa,CAACQ,WAAW,IAAI,GAAG;MAChE,MAAMqH,UAAU,GAAG,IAAI,CAACN,WAAW,CAACvH,aAAa,CAACQ,WAAW,IAAI,GAAG;MACpE,IAAI,CAACkF,YAAY,GAAG,IAAIrE,eAAe,CAACuG,QAAQ,EAAE,IAAI,CAACrB,OAAO,EAAE,GAAG,EAAEsB,UAAU,CAAC;MAChF,IAAI,CAACM,iBAAiB,CAAC,KAAK,CAAC;MAC7B,IAAI,CAACrD,KAAK,CAACsD,aAAa,CAAC,CAAC;IAC9B;EACJ;EACAJ,UAAUA,CAAC7D,KAAK,EAAE1G,IAAI,GAAG,IAAI,EAAE;IAC3B,IAAI,CAACuH,OAAO,CAACxD,SAAS,CAAC,IAAI,CAAC;IAC5B,IAAI,CAAC,IAAI,CAACtG,KAAK,EAAE;MACb,IAAI,CAACqL,OAAO,GAAG,IAAI,CAACZ,mBAAmB,CAAC3F,aAAa,CAACQ,WAAW;IACrE;IACA,IAAI,CAAC,IAAI,CAACvF,MAAM,EAAE;MACd,IAAI,CAACA,MAAM,GAAG,GAAG;IACrB;IACA,IAAI,CAACoN,eAAe,CAAClE,KAAK,CAAC;IAC3B,IAAI,CAACzL,kBAAkB,CAACyL,KAAK,EAAE1G,IAAI,CAAC;IACpC,IAAI,CAAC6K,eAAe,CAAC,CAAC;EAC1B;EACA5G,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC6G,gBAAgB,CAAC,CAAC;EAC3B;EACAC,WAAWA,CAACC,QAAQ,EAAEC,UAAU,EAAEvE,KAAK,EAAEoC,OAAO,EAAEC,QAAQ,EAAE/E,eAAe,EAAEkH,eAAe,EAAElC,WAAW,EAAEC,aAAa,EAAE9S,cAAc,EAAE+S,cAAc,EAAEC,cAAc,EAAEC,iBAAiB,EAAEC,kBAAkB,EAAEC,mBAAmB,EAAE6B,sBAAsB,EAAE5B,UAAU,EAAEC,gBAAgB,EAAE4B,yBAAyB,EAAE1P,aAAa,EAAE1H,cAAc,EAAEoH,mBAAmB,EAAElH,uBAAuB,EAAEsH,oBAAoB,EAAEF,yBAAyB,EAAEkB,UAAU,EAAEJ,eAAe,EAAEC,cAAc,EAAEE,cAAc,EAAER,mBAAmB,EAAEC,kBAAkB,EAAEd,gBAAgB,EAAEnH,qBAAqB,EAAEK,oBAAoB,EAAEyG,wBAAwB,EAAE4O,YAAY,EAAEE,gBAAgB,EAAE/M,eAAe,EAAE;IACjqB,IAAI,CAACgO,eAAe,CAAClE,KAAK,CAAC;IAC3B,IAAI,CAAC2E,YAAY,CAACrC,WAAW,CAAC;IAC9B,IAAI,CAACxB,MAAM,GAAI1I,QAAQ,CAAC,CAAC,KAAK,EAAG;IACjC,IAAI,CAACiJ,iBAAiB,GAAGiD,QAAQ;IACjC,IAAI,CAAC9C,mBAAmB,GAAG+C,UAAU;IACrC,IAAI,CAAC9B,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACF,aAAa,GAAGA,aAAa;IAClC,IAAI,CAAC9S,cAAc,GAAGA,cAAc;IACpC,IAAI,CAAC+S,cAAc,GAAGA,cAAc;IACpC,IAAI,CAAClF,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACoF,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACZ,oBAAoB,GAAGyC,sBAAsB;IAClD,IAAI,CAAC1N,KAAK,GAAG,IAAI,CAACqL,OAAO,GAAGzJ,QAAQ,CAACyJ,OAAO,EAAE,EAAE,CAAC;IACjD,IAAI,CAACtL,MAAM,GAAG,IAAI,CAACuL,QAAQ,GAAG1J,QAAQ,CAAC0J,QAAQ,EAAE,EAAE,CAAC;IACpD,IAAI,CAACQ,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,gBAAgB,GAAGnK,QAAQ,CAACmK,gBAAgB,EAAE,EAAE,CAAC;IACtD,IAAI,CAAChN,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACH,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACD,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACG,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACP,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACD,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAAC0N,YAAY,GAAGA,YAAY;IAChC,IAAI,CAAC7B,aAAa,GAAGsD,eAAe,IAAI,MAAM;IAC9C,IAAI,CAACI,eAAe,CAAC5P,aAAa,EAAE1H,cAAc,CAAC;IACnD,IAAI,CAACoH,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAAClH,uBAAuB,GAAGA,uBAAuB;IACtD,IAAI,CAACsH,oBAAoB,GAAGA,oBAAoB;IAChD,IAAI,CAACF,yBAAyB,GAAGA,yBAAyB;IAC1D,IAAI,CAACJ,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAAC9G,oBAAoB,GAAGA,oBAAoB;IAChD,IAAI,CAACL,qBAAqB,GAAGA,qBAAqB;IAClD,IAAI,CAAC8G,wBAAwB,GAAGA,wBAAwB;IACxD,IAAI,CAAC8O,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAAC/M,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACwO,yBAAyB,EAAE;MAC5B,IAAI,CAAChD,iBAAiB,GAAG,CAAC;IAC9B;IACA,IAAIpE,eAAe,KAAK,QAAQ,EAAE;MAC9B,IAAI,CAACmE,eAAe,GAAG,CAAC;MACxB,IAAI,CAACC,iBAAiB,GAAG,CAAC;IAC9B;IACA,IAAIc,cAAc,KAAK,KAAK,IACxB/S,cAAc,KAAK,QAAQ,IAAIA,cAAc,KAAK,QAAQ,EAAE;MAC5D,IAAI,CAACA,cAAc,GAAG,UAAU;IACpC;EACJ;EACAkV,YAAYA,CAACE,IAAI,EAAE;IACf,QAAQA,IAAI,CAAChG,QAAQ,CAAC,CAAC,CAACiG,WAAW,CAAC,CAAC;MACjC,KAAK,GAAG;MACR,KAAK,GAAG;MACR,KAAK,OAAO;QACR,IAAI,CAACxC,WAAW,GAAG,CAAC;QACpB;MACJ,KAAK,GAAG;MACR,KAAK,GAAG;MACR,KAAK,WAAW;QACZ,IAAI,CAACA,WAAW,GAAG,CAAC;QACpB;MACJ,KAAK,GAAG;MACR,KAAK,GAAG;MACR,KAAK,SAAS;QACV,IAAI,CAACA,WAAW,GAAG,CAAC;QACpB;MACJ;QACI,IAAI,CAACA,WAAW,GAAG,CAAC;IAC5B;EACJ;EACA4B,eAAeA,CAAClE,KAAK,EAAE;IACnB,IAAI,CAACiB,YAAY,GAAGjB,KAAK;EAC7B;EACA4E,eAAeA,CAAC5P,aAAa,EAAE1H,cAAc,EAAE;IAC3C,IAAI,CAAC0H,aAAa,GAAGA,aAAa;IAClC,IAAI,CAAC1H,cAAc,GAAGA,cAAc;EACxC;EACAiH,kBAAkBA,CAAC4E,KAAK,EAAEG,IAAI,GAAG,IAAI,EAAEyL,MAAM,GAAG,IAAI,EAAE;IAClD,IAAItH,IAAI;IACR,IAAI,IAAI,CAAChO,cAAc,KAAK,QAAQ,IAAI,IAAI,CAACA,cAAc,KAAK,QAAQ,EAAE;MACtEgO,IAAI,GAAG,IAAI,CAACoD,OAAO,CAAC1B,YAAY,CAAChG,KAAK,EAAE,IAAI,CAAC;MAC7C,IAAI,CAACsE,IAAI,IAAI,CAAC,IAAI,CAACA,IAAI,EAAE;QACrBA,IAAI,GAAG,IAAI,CAACoD,OAAO,CAAC1B,YAAY,CAAChG,KAAK,EAAE,KAAK,CAAC;MAClD;IACJ,CAAC,MACI;MACDsE,IAAI,GAAG,IAAI,CAACoD,OAAO,CAAC1B,YAAY,CAAChG,KAAK,EAAE,KAAK,CAAC;IAClD;IACA,IAAI,CAACsE,IAAI,IAAI,CAAC,IAAI,CAACA,IAAI,EAAE;MACrBA,IAAI,GAAG,IAAI,CAACoD,OAAO,CAAC1B,YAAY,CAAC,IAAI,CAAC+B,aAAa,EAAE,KAAK,CAAC;IAC/D;IACA,IAAIzD,IAAI,EAAE;MACN,IAAI,CAACA,IAAI,GAAGA,IAAI;MAChB,IAAI,CAAC6D,OAAO,GAAG,IAAI,CAAC7D,IAAI,CAAC9M,CAAC;MAC1B,IAAI,IAAI,CAAC6R,cAAc,KAAK,KAAK,IAAI,IAAI,CAAC/S,cAAc,KAAK,UAAU,EAAE;QACrE,IAAI,CAACgO,IAAI,CAACtP,CAAC,GAAG,CAAC;MACnB;MACA,IAAI,CAAC6V,iBAAiB,CAAC1K,IAAI,EAAEyL,MAAM,CAAC;IACxC;EACJ;EACAnB,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAAChI,QAAQ,KAAK,OAAO,EAAE;MAC3B,IAAI,CAACoJ,iBAAiB,CAAC,CAAC;IAC5B,CAAC,MACI,IAAI,IAAI,CAAC1H,eAAe,KAAK,QAAQ,EAAE;MACxC,IAAI,CAAC8G,gBAAgB,CAAC,CAAC;IAC3B;EACJ;EACAhY,SAASA,CAACK,MAAM,EAAE;IACd,IAAI,CAAC4U,iBAAiB,CAAC4D,aAAa,CAAC;MAAExY,MAAM,EAAEA,MAAM;MAAEuT,KAAK,EAAE,IAAI,CAACgB;IAAY,CAAC,CAAC;EACrF;EACA9U,WAAWA,CAACO,MAAM,EAAE;IAChB,IAAI,CAAC4U,iBAAiB,CAAC6D,eAAe,CAAC;MAAEzY,MAAM,EAAEA,MAAM;MAAEuT,KAAK,EAAE,IAAI,CAACgB;IAAY,CAAC,CAAC;EACvF;EACA2C,WAAWA,CAACzK,KAAK,EAAE;IACf,IAAI,IAAI,CAAC+I,IAAI,IACT,CAAC,IAAI,CAACnB,MAAM,IACZ,IAAI,CAACxD,eAAe,KAAK,OAAO,IAChCpE,KAAK,CAACE,MAAM,KAAK,IAAI,CAACoI,mBAAmB,CAAC3F,aAAa,IACvD,CAAC,IAAI,CAACsJ,YAAY,CAAC,IAAI,CAACzK,KAAK,CAACmB,aAAa,EAAE3C,KAAK,CAACE,MAAM,CAAC,IAC1D,CAAC,IAAI,CAAC+L,YAAY,CAAC,IAAI,CAAC3D,mBAAmB,CAAC3F,aAAa,EAAE3C,KAAK,CAACE,MAAM,CAAC,IACxE,IAAI,CAACsJ,iBAAiB,CAAC0C,MAAM,CAAEC,IAAI,IAAKA,IAAI,KAAKnM,KAAK,CAACE,MAAM,CAAC,CAAC7L,MAAM,KAAK,CAAC,EAAE;MAC7E,IAAI,CAACmT,MAAM,CAAC4E,GAAG,CAAC,MAAM;QAClB,IAAI,IAAI,CAAC3C,kBAAkB,EAAE;UACzB,IAAI,CAACtB,iBAAiB,CAACkE,aAAa,CAAC,IAAI,CAACvE,WAAW,CAAC;QAC1D,CAAC,MACI;UACD,IAAI,CAACvD,IAAI,GAAG,IAAI;UAChB,IAAI,CAAClJ,kBAAkB,CAAC,IAAI,CAAC0M,YAAY,EAAE,KAAK,CAAC;UACjD,IAAI,IAAI,CAACsB,aAAa,EAAE;YACpB,IAAI,CAAClB,iBAAiB,CAACmE,WAAW,CAAC,IAAI,CAACzE,SAAS,CAAC;UACtD;UACA,IAAI,CAACM,iBAAiB,CAACoE,YAAY,CAAC,IAAI,CAACxE,YAAY,CAAC;UACtD,IAAI,CAACI,iBAAiB,CAACqE,aAAa,CAAC,CAAC;QAC1C;QACA,IAAI,IAAI,CAAC9C,mBAAmB,EAAE;UAC1B,IAAI,CAACwB,gBAAgB,CAAC,CAAC;QAC3B;MACJ,CAAC,CAAC;IACN;EACJ;EACArW,aAAaA,CAACmL,KAAK,EAAE;IACjBA,KAAK,CAACsC,eAAe,CAAC,CAAC;IACvB,IAAI,IAAI,CAACwF,WAAW,EAAE;MAClB,IAAI,CAACK,iBAAiB,CAACkE,aAAa,CAAC,IAAI,CAACvE,WAAW,CAAC;IAC1D;IACA,IAAI,IAAI,CAAC1D,eAAe,KAAK,OAAO,EAAE;MAClC,IAAI,CAAC8G,gBAAgB,CAAC,CAAC;IAC3B;EACJ;EACAhP,aAAaA,CAAC8D,KAAK,EAAE;IACjB,IAAI,CAACuE,IAAI,GAAG,IAAI;IAChBvE,KAAK,CAACsC,eAAe,CAAC,CAAC;IACvB,IAAI,CAAC6F,iBAAiB,CAACqE,aAAa,CAAC,CAAC;IACtC,IAAI,CAACnR,kBAAkB,CAAC,IAAI,CAAC0M,YAAY,EAAE,IAAI,CAAC;IAChD,IAAI,IAAI,CAAC3D,eAAe,KAAK,OAAO,EAAE;MAClC,IAAI,IAAI,CAACiF,aAAa,EAAE;QACpB,IAAI,CAAClB,iBAAiB,CAACmE,WAAW,CAAC,IAAI,CAACzE,SAAS,CAAC;MACtD;MACA,IAAI,CAACM,iBAAiB,CAACoE,YAAY,CAAC,IAAI,CAACxE,YAAY,EAAE,IAAI,CAAC;MAC5D,IAAI,CAACmD,gBAAgB,CAAC,CAAC;IAC3B;EACJ;EACAuB,YAAYA,CAAA,EAAG;IACX,IAAI,CAAC,IAAI,CAAC3C,mBAAmB,EACzB;IACJ,MAAM4C,UAAU,GAAG,IAAItO,MAAM,CAACuO,UAAU,CAAC,CAAC;IAC1CD,UAAU,CAACE,IAAI,CAAC,CAAC,CAACC,IAAI,CAAEC,gBAAgB,IAAK;MACzC,IAAI,CAACzR,kBAAkB,CAACyR,gBAAgB,CAACC,OAAO,EAAE,IAAI,CAAC;IAC3D,CAAC,CAAC;EACN;EACAtS,cAAcA,CAACuS,MAAM,EAAE;IACnB,MAAMC,gBAAgB,GAAG,IAAI,CAACxE,iBAAiB,CAACpU,MAAM,IACjD,IAAI,CAACgV,aAAa,GAAG,CAAC,GAAG,CAAC,CAAC;IAChC,MAAM6D,UAAU,GAAG,CAAE,CAAC,IAAI,CAACzE,iBAAiB,CAACjJ,OAAO,CAAC,IAAI,CAACtJ,MAAM,CAAC,GAAG8W,MAAM,IACtEC,gBAAgB,GAAIA,gBAAgB,IAAIA,gBAAgB;IAC5D,IAAI,CAAC/W,MAAM,GAAG,IAAI,CAACuS,iBAAiB,CAACyE,UAAU,CAAC;EACpD;EACApa,aAAaA,CAACmN,KAAK,EAAE;IACjB,IAAI,CAACsE,IAAI,CAAC9Q,CAAC,GAAGwM,KAAK,CAACxM,CAAC,GAAGwM,KAAK,CAAC0B,GAAG;IACjC,IAAI,CAAC4C,IAAI,CAAC/Q,CAAC,GAAGyM,KAAK,CAACzM,CAAC,GAAGyM,KAAK,CAAC2B,GAAG;IACjC,IAAI,CAACkJ,iBAAiB,CAAC,CAAC;IACxB,IAAI,CAAC3C,iBAAiB,CAACgF,aAAa,CAAC;MACjC5Z,MAAM,EAAE,WAAW;MACnB0M,KAAK,EAAE,IAAI,CAACsE,IAAI,CAAC/Q,CAAC;MAClBsT,KAAK,EAAE,IAAI,CAACgB;IAChB,CAAC,CAAC;IACF,IAAI,CAACK,iBAAiB,CAACgF,aAAa,CAAC;MACjC5Z,MAAM,EAAE,YAAY;MACpB0M,KAAK,EAAE,IAAI,CAACsE,IAAI,CAAC9Q,CAAC;MAClBqT,KAAK,EAAE,IAAI,CAACgB;IAChB,CAAC,CAAC;EACN;EACAsF,WAAWA,CAACnN,KAAK,EAAE;IACf,IAAI,CAACsE,IAAI,CAAC9M,CAAC,GAAGwI,KAAK,CAACzM,CAAC,GAAGyM,KAAK,CAAC0B,GAAG;IACjC,IAAI,CAACyG,OAAO,GAAG,IAAI,CAAC7D,IAAI,CAAC9M,CAAC;IAC1B,IAAI,CAACqT,iBAAiB,CAAC,CAAC;IACxB,IAAI,CAAC3C,iBAAiB,CAACgF,aAAa,CAAC;MACjC5Z,MAAM,EAAE,KAAK;MACb0M,KAAK,EAAE,IAAI,CAACsE,IAAI,CAAC9M,CAAC;MAClBqP,KAAK,EAAE,IAAI,CAACgB;IAChB,CAAC,CAAC;EACN;EACAuF,aAAaA,CAACpN,KAAK,EAAE;IACjB,IAAI,CAACsE,IAAI,CAAC/Q,CAAC,GAAGyM,KAAK,CAACzM,CAAC,GAAGyM,KAAK,CAAC0B,GAAG;IACjC,IAAI,CAACmJ,iBAAiB,CAAC,CAAC;IACxB,IAAI,CAAC3C,iBAAiB,CAACgF,aAAa,CAAC;MACjC5Z,MAAM,EAAE,OAAO;MACf0M,KAAK,EAAE,IAAI,CAACsE,IAAI,CAAC/Q,CAAC;MAClBsT,KAAK,EAAE,IAAI,CAACgB;IAChB,CAAC,CAAC;EACN;EACAwF,aAAaA,CAACrN,KAAK,EAAE;IACjB,IAAI,CAACsE,IAAI,CAACtP,CAAC,GAAGgL,KAAK,CAACzM,CAAC,GAAGyM,KAAK,CAAC0B,GAAG;IACjC,IAAI,CAACmJ,iBAAiB,CAAC,CAAC;IACxB,IAAI,CAAC3C,iBAAiB,CAACgF,aAAa,CAAC;MACjC5Z,MAAM,EAAE,OAAO;MACf0M,KAAK,EAAE,IAAI,CAACsE,IAAI,CAACtP,CAAC;MAClB6R,KAAK,EAAE,IAAI,CAACgB;IAChB,CAAC,CAAC;EACN;EACAtO,UAAUA,CAACyG,KAAK,EAAE;IACd,IAAIA,KAAK,KAAK,IAAI,EAAE;MAChB,IAAI,CAAC6K,iBAAiB,CAAC,CAAC;IAC5B,CAAC,MACI;MACD,IAAI7K,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QAC3BA,KAAK,GAAG,GAAG,GAAGA,KAAK;MACvB;MACA,IAAIsN,QAAQ,GAAG,gCAAgC;MAC/C,IAAI,IAAI,CAAChX,cAAc,KAAK,QAAQ,EAAE;QAClCgX,QAAQ,GAAG,4CAA4C;MAC3D;MACA,MAAMC,KAAK,GAAGD,QAAQ,CAACE,IAAI,CAACxN,KAAK,CAAC;MAClC,IAAIuN,KAAK,EAAE;QACP,IAAIvN,KAAK,CAAC5L,MAAM,GAAG,CAAC,EAAE;UAClB4L,KAAK,GAAG,GAAG,GAAGA,KAAK,CAACP,SAAS,CAAC,CAAC,CAAC,CAC3BgO,KAAK,CAAC,EAAE,CAAC,CACTC,GAAG,CAACxX,CAAC,IAAIA,CAAC,GAAGA,CAAC,CAAC,CACfyX,IAAI,CAAC,EAAE,CAAC;QACjB;QACA,IAAI,IAAI,CAACrX,cAAc,KAAK,QAAQ,EAAE;UAClC0J,KAAK,IAAIqD,IAAI,CAACuC,KAAK,CAAC,IAAI,CAACtB,IAAI,CAACtP,CAAC,GAAG,GAAG,CAAC,CAAC0Q,QAAQ,CAAC,EAAE,CAAC;QACvD;QACA,IAAI,CAACtK,kBAAkB,CAAC4E,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC;MAC/C;MACA,IAAI,CAACkI,iBAAiB,CAAC0F,YAAY,CAAC;QAChCC,KAAK,EAAE,KAAK;QACZN,KAAK,EAAEA,KAAK;QACZvN,KAAK,EAAEA,KAAK;QACZ6G,KAAK,EAAE,IAAI,CAACgB;MAChB,CAAC,CAAC;IACN;EACJ;EACAzP,UAAUA,CAAC4H,KAAK,EAAE;IACd,MAAMoF,IAAI,GAAG,IAAI,CAACsC,OAAO,CAAChD,UAAU,CAAC,IAAI,CAACJ,IAAI,CAAC;IAC/C,MAAMiJ,KAAK,GAAG,CAACjH,KAAK,CAACtG,KAAK,CAACzM,CAAC,CAAC,IAAIyM,KAAK,CAACzM,CAAC,IAAI,CAAC,IAAIyM,KAAK,CAACzM,CAAC,IAAIyM,KAAK,CAACL,EAAE;IACpE,IAAI4N,KAAK,EAAE;MACPnI,IAAI,CAACzM,CAAC,GAAGqH,KAAK,CAACzM,CAAC,GAAGyM,KAAK,CAACL,EAAE;MAC3B,IAAI,CAAC2E,IAAI,GAAG,IAAI,CAACoD,OAAO,CAACrC,UAAU,CAACD,IAAI,CAAC;MACzC,IAAI,CAAC+C,OAAO,GAAG,IAAI,CAAC7D,IAAI,CAAC9M,CAAC;MAC1B,IAAI,CAACqT,iBAAiB,CAAC,CAAC;IAC5B;IACA,IAAI,CAAC3C,iBAAiB,CAAC0F,YAAY,CAAC;MAChCC,KAAK,EAAE,KAAK;MACZN,KAAK,EAAEA,KAAK;MACZvN,KAAK,EAAEoF,IAAI,CAACzM,CAAC;MACbkO,KAAK,EAAE,IAAI,CAACgB;IAChB,CAAC,CAAC;EACN;EACAnP,WAAWA,CAACsH,KAAK,EAAE;IACf,MAAMoF,IAAI,GAAG,IAAI,CAACsC,OAAO,CAAChD,UAAU,CAAC,IAAI,CAACJ,IAAI,CAAC;IAC/C,MAAMiJ,KAAK,GAAG,CAACjH,KAAK,CAACtG,KAAK,CAACzM,CAAC,CAAC,IAAIyM,KAAK,CAACzM,CAAC,IAAI,CAAC,IAAIyM,KAAK,CAACzM,CAAC,IAAIyM,KAAK,CAACL,EAAE;IACpE,IAAI4N,KAAK,EAAE;MACPnI,IAAI,CAACvM,CAAC,GAAGmH,KAAK,CAACzM,CAAC,GAAGyM,KAAK,CAACL,EAAE;MAC3B,IAAI,CAAC2E,IAAI,GAAG,IAAI,CAACoD,OAAO,CAACrC,UAAU,CAACD,IAAI,CAAC;MACzC,IAAI,CAAC+C,OAAO,GAAG,IAAI,CAAC7D,IAAI,CAAC9M,CAAC;MAC1B,IAAI,CAACqT,iBAAiB,CAAC,CAAC;IAC5B;IACA,IAAI,CAAC3C,iBAAiB,CAAC0F,YAAY,CAAC;MAChCC,KAAK,EAAE,MAAM;MACbN,KAAK,EAAEA,KAAK;MACZvN,KAAK,EAAEoF,IAAI,CAACvM,CAAC;MACbgO,KAAK,EAAE,IAAI,CAACgB;IAChB,CAAC,CAAC;EACN;EACAtP,YAAYA,CAACyH,KAAK,EAAE;IAChB,MAAMoF,IAAI,GAAG,IAAI,CAACsC,OAAO,CAAChD,UAAU,CAAC,IAAI,CAACJ,IAAI,CAAC;IAC/C,MAAMiJ,KAAK,GAAG,CAACjH,KAAK,CAACtG,KAAK,CAACzM,CAAC,CAAC,IAAIyM,KAAK,CAACzM,CAAC,IAAI,CAAC,IAAIyM,KAAK,CAACzM,CAAC,IAAIyM,KAAK,CAACL,EAAE;IACpE,IAAI4N,KAAK,EAAE;MACPnI,IAAI,CAACxM,CAAC,GAAGoH,KAAK,CAACzM,CAAC,GAAGyM,KAAK,CAACL,EAAE;MAC3B,IAAI,CAAC2E,IAAI,GAAG,IAAI,CAACoD,OAAO,CAACrC,UAAU,CAACD,IAAI,CAAC;MACzC,IAAI,CAAC+C,OAAO,GAAG,IAAI,CAAC7D,IAAI,CAAC9M,CAAC;MAC1B,IAAI,CAACqT,iBAAiB,CAAC,CAAC;IAC5B;IACA,IAAI,CAAC3C,iBAAiB,CAAC0F,YAAY,CAAC;MAChCC,KAAK,EAAE,OAAO;MACdN,KAAK,EAAEA,KAAK;MACZvN,KAAK,EAAEoF,IAAI,CAACxM,CAAC;MACbiO,KAAK,EAAE,IAAI,CAACgB;IAChB,CAAC,CAAC;EACN;EACA5Q,UAAUA,CAAC+I,KAAK,EAAE;IACd,MAAMuN,KAAK,GAAG,CAACjH,KAAK,CAACtG,KAAK,CAACzM,CAAC,CAAC,IAAIyM,KAAK,CAACzM,CAAC,IAAI,CAAC,IAAIyM,KAAK,CAACzM,CAAC,IAAIyM,KAAK,CAACL,EAAE;IACpE,IAAI4N,KAAK,EAAE;MACP,IAAI,CAACjJ,IAAI,CAAC9M,CAAC,GAAGwI,KAAK,CAACzM,CAAC,GAAGyM,KAAK,CAACL,EAAE;MAChC,IAAI,CAACwI,OAAO,GAAG,IAAI,CAAC7D,IAAI,CAAC9M,CAAC;MAC1B,IAAI,CAACqT,iBAAiB,CAAC,CAAC;IAC5B;IACA,IAAI,CAAC3C,iBAAiB,CAAC0F,YAAY,CAAC;MAChCC,KAAK,EAAE,KAAK;MACZN,KAAK,EAAEA,KAAK;MACZvN,KAAK,EAAE,IAAI,CAACsE,IAAI,CAAC9M,CAAC;MAClBqP,KAAK,EAAE,IAAI,CAACgB;IAChB,CAAC,CAAC;EACN;EACAzN,YAAYA,CAAC4F,KAAK,EAAE;IAChB,MAAMuN,KAAK,GAAG,CAACjH,KAAK,CAACtG,KAAK,CAACzM,CAAC,CAAC,IAAIyM,KAAK,CAACzM,CAAC,IAAI,CAAC,IAAIyM,KAAK,CAACzM,CAAC,IAAIyM,KAAK,CAACL,EAAE;IACpE,IAAI4N,KAAK,EAAE;MACP,IAAI,CAACjJ,IAAI,CAAC/Q,CAAC,GAAGyM,KAAK,CAACzM,CAAC,GAAGyM,KAAK,CAACL,EAAE;MAChC,IAAI,CAACkL,iBAAiB,CAAC,CAAC;IAC5B;IACA,IAAI,CAAC3C,iBAAiB,CAAC0F,YAAY,CAAC;MAChCC,KAAK,EAAE,OAAO;MACdN,KAAK,EAAEA,KAAK;MACZvN,KAAK,EAAE,IAAI,CAACsE,IAAI,CAAC/Q,CAAC;MAClBsT,KAAK,EAAE,IAAI,CAACgB;IAChB,CAAC,CAAC;EACN;EACA/S,YAAYA,CAACkL,KAAK,EAAE;IAChB,MAAMuN,KAAK,GAAG,CAACjH,KAAK,CAACtG,KAAK,CAACzM,CAAC,CAAC,IAAIyM,KAAK,CAACzM,CAAC,IAAI,CAAC,IAAIyM,KAAK,CAACzM,CAAC,IAAIyM,KAAK,CAACL,EAAE;IACpE,IAAI4N,KAAK,EAAE;MACP,IAAI,CAACjJ,IAAI,CAACtP,CAAC,GAAGgL,KAAK,CAACzM,CAAC,GAAGyM,KAAK,CAACL,EAAE;MAChC,IAAI,CAACkL,iBAAiB,CAAC,CAAC;IAC5B;IACA,IAAI,CAAC3C,iBAAiB,CAAC0F,YAAY,CAAC;MAChCC,KAAK,EAAE,OAAO;MACdN,KAAK,EAAEA,KAAK;MACZvN,KAAK,EAAE,IAAI,CAACsE,IAAI,CAACtP,CAAC;MAClB6R,KAAK,EAAE,IAAI,CAACgB;IAChB,CAAC,CAAC;EACN;EACAtQ,gBAAgBA,CAACyI,KAAK,EAAE;IACpB,MAAMyE,IAAI,GAAG,IAAI,CAACiD,OAAO,CAACrD,SAAS,CAAC,IAAI,CAACC,IAAI,CAAC;IAC9C,MAAMiJ,KAAK,GAAG,CAACjH,KAAK,CAACtG,KAAK,CAACzM,CAAC,CAAC,IAAIyM,KAAK,CAACzM,CAAC,IAAI,CAAC,IAAIyM,KAAK,CAACzM,CAAC,IAAIyM,KAAK,CAACL,EAAE;IACpE,IAAI4N,KAAK,EAAE;MACP9I,IAAI,CAAChN,CAAC,GAAGuI,KAAK,CAACzM,CAAC,GAAGyM,KAAK,CAACL,EAAE;MAC3B,IAAI,CAAC2E,IAAI,GAAG,IAAI,CAACoD,OAAO,CAAClD,SAAS,CAACC,IAAI,CAAC;MACxC,IAAI,CAAC0D,OAAO,GAAG,IAAI,CAAC7D,IAAI,CAAC9M,CAAC;MAC1B,IAAI,CAACqT,iBAAiB,CAAC,CAAC;IAC5B;IACA,IAAI,CAAC3C,iBAAiB,CAAC0F,YAAY,CAAC;MAChCC,KAAK,EAAE,WAAW;MAClBN,KAAK,EAAEA,KAAK;MACZvN,KAAK,EAAEyE,IAAI,CAAChN,CAAC;MACboP,KAAK,EAAE,IAAI,CAACgB;IAChB,CAAC,CAAC;EACN;EACAzQ,iBAAiBA,CAAC4I,KAAK,EAAE;IACrB,MAAMyE,IAAI,GAAG,IAAI,CAACiD,OAAO,CAACrD,SAAS,CAAC,IAAI,CAACC,IAAI,CAAC;IAC9C,MAAMiJ,KAAK,GAAG,CAACjH,KAAK,CAACtG,KAAK,CAACzM,CAAC,CAAC,IAAIyM,KAAK,CAACzM,CAAC,IAAI,CAAC,IAAIyM,KAAK,CAACzM,CAAC,IAAIyM,KAAK,CAACL,EAAE;IACpE,IAAI4N,KAAK,EAAE;MACP9I,IAAI,CAACjR,CAAC,GAAGwM,KAAK,CAACzM,CAAC,GAAGyM,KAAK,CAACL,EAAE;MAC3B,IAAI,CAAC2E,IAAI,GAAG,IAAI,CAACoD,OAAO,CAAClD,SAAS,CAACC,IAAI,CAAC;MACxC,IAAI,CAAC0D,OAAO,GAAG,IAAI,CAAC7D,IAAI,CAAC9M,CAAC;MAC1B,IAAI,CAACqT,iBAAiB,CAAC,CAAC;IAC5B;IACA,IAAI,CAAC3C,iBAAiB,CAAC0F,YAAY,CAAC;MAChCC,KAAK,EAAE,YAAY;MACnBN,KAAK,EAAEA,KAAK;MACZvN,KAAK,EAAEyE,IAAI,CAACjR,CAAC;MACbqT,KAAK,EAAE,IAAI,CAACgB;IAChB,CAAC,CAAC;EACN;EACAvS,WAAWA,CAAC0K,KAAK,EAAE;IACf,MAAMuN,KAAK,GAAG,CAACjH,KAAK,CAACtG,KAAK,CAACzM,CAAC,CAAC,IAAIyM,KAAK,CAACzM,CAAC,IAAI,CAAC,IAAIyM,KAAK,CAACzM,CAAC,IAAIyM,KAAK,CAACL,EAAE;IACpE,IAAI4N,KAAK,EAAE;MACP,IAAI,CAACrI,IAAI,CAAChP,CAAC,GAAG8J,KAAK,CAACzM,CAAC;MACrB,IAAI,CAACsX,iBAAiB,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;IAC7C;IACA,IAAI,CAAC3C,iBAAiB,CAAC0F,YAAY,CAAC;MAChCC,KAAK,EAAE,MAAM;MACbN,KAAK,EAAE,IAAI;MACXvN,KAAK,EAAE,IAAI,CAACkF,IAAI,CAAChP,CAAC;MAClB2Q,KAAK,EAAE,IAAI,CAACgB;IAChB,CAAC,CAAC;EACN;EACApS,cAAcA,CAACuK,KAAK,EAAE;IAClB,MAAMuN,KAAK,GAAG,CAACjH,KAAK,CAACtG,KAAK,CAACzM,CAAC,CAAC,IAAIyM,KAAK,CAACzM,CAAC,IAAI,CAAC,IAAIyM,KAAK,CAACzM,CAAC,IAAIyM,KAAK,CAACL,EAAE;IACpE,IAAI4N,KAAK,EAAE;MACP,IAAI,CAACrI,IAAI,CAAC/O,CAAC,GAAG6J,KAAK,CAACzM,CAAC;MACrB,IAAI,CAACsX,iBAAiB,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;IAC7C;IACA,IAAI,CAAC3C,iBAAiB,CAAC0F,YAAY,CAAC;MAChCC,KAAK,EAAE,SAAS;MAChBN,KAAK,EAAE,IAAI;MACXvN,KAAK,EAAE,IAAI,CAACkF,IAAI,CAAC/O,CAAC;MAClB0Q,KAAK,EAAE,IAAI,CAACgB;IAChB,CAAC,CAAC;EACN;EACAjS,aAAaA,CAACoK,KAAK,EAAE;IACjB,MAAMuN,KAAK,GAAG,CAACjH,KAAK,CAACtG,KAAK,CAACzM,CAAC,CAAC,IAAIyM,KAAK,CAACzM,CAAC,IAAI,CAAC,IAAIyM,KAAK,CAACzM,CAAC,IAAIyM,KAAK,CAACL,EAAE;IACpE,IAAI4N,KAAK,EAAE;MACP,IAAI,CAACrI,IAAI,CAAC9O,CAAC,GAAG4J,KAAK,CAACzM,CAAC;MACrB,IAAI,CAACsX,iBAAiB,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;IAC7C;IACA,IAAI,CAAC3C,iBAAiB,CAAC0F,YAAY,CAAC;MAChCC,KAAK,EAAE,QAAQ;MACfN,KAAK,EAAE,IAAI;MACXvN,KAAK,EAAE,IAAI,CAACkF,IAAI,CAAC9O,CAAC;MAClByQ,KAAK,EAAE,IAAI,CAACgB;IAChB,CAAC,CAAC;EACN;EACA9R,YAAYA,CAACiK,KAAK,EAAE;IAChB,MAAMuN,KAAK,GAAG,CAACjH,KAAK,CAACtG,KAAK,CAACzM,CAAC,CAAC,IAAIyM,KAAK,CAACzM,CAAC,IAAI,CAAC,IAAIyM,KAAK,CAACzM,CAAC,IAAIyM,KAAK,CAACL,EAAE;IACpE,IAAI4N,KAAK,EAAE;MACP,IAAI,CAACrI,IAAI,CAAC7O,CAAC,GAAG2J,KAAK,CAACzM,CAAC;MACrB,IAAI,CAACsX,iBAAiB,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;IAC7C;IACA,IAAI,CAAC3C,iBAAiB,CAAC0F,YAAY,CAAC;MAChCC,KAAK,EAAE,OAAO;MACdN,KAAK,EAAE,IAAI;MACXvN,KAAK,EAAE,IAAI,CAACkF,IAAI,CAAC7O,CAAC;MAClBwQ,KAAK,EAAE,IAAI,CAACgB;IAChB,CAAC,CAAC;EACN;EACA/T,gBAAgBA,CAACiM,KAAK,EAAEC,KAAK,EAAE;IAC3BD,KAAK,CAACsC,eAAe,CAAC,CAAC;IACvB,IAAI,CAAC,IAAI,CAAClO,cAAc,CAAC8X,MAAM,CAAEpF,KAAK,IAAMA,KAAK,KAAK7G,KAAM,CAAC,CAAC5L,MAAM,EAAE;MAClE,IAAI,CAACD,cAAc,GAAG,IAAI,CAACA,cAAc,CAAC2Z,MAAM,CAAC9N,KAAK,CAAC;MACvD,IAAI,CAACkI,iBAAiB,CAAC6F,mBAAmB,CAAC,IAAI,CAAC5Z,cAAc,CAAC;IACnE;EACJ;EACA4G,mBAAmBA,CAACgF,KAAK,EAAEC,KAAK,EAAE;IAC9BD,KAAK,CAACsC,eAAe,CAAC,CAAC;IACvB,IAAI,CAAClO,cAAc,GAAG,IAAI,CAACA,cAAc,CAAC8X,MAAM,CAAEpF,KAAK,IAAMA,KAAK,KAAK7G,KAAM,CAAC;IAC9E,IAAI,CAACkI,iBAAiB,CAAC6F,mBAAmB,CAAC,IAAI,CAAC5Z,cAAc,CAAC;EACnE;EACA;EACA6W,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC,IAAI,CAAClC,IAAI,EAAE;MACZ,IAAI,CAACA,IAAI,GAAG,IAAI;MAChB,IAAI,CAACC,MAAM,GAAG,IAAI;MAClBiF,UAAU,CAAC,MAAM;QACb,IAAI,CAACjF,MAAM,GAAG,KAAK;QACnB,IAAI,CAAC8C,iBAAiB,CAAC,CAAC;QACxB,IAAI,CAACrE,KAAK,CAACsD,aAAa,CAAC,CAAC;MAC9B,CAAC,EAAE,CAAC,CAAC;MACL,IAAI,CAAC5C,iBAAiB,CAAC+F,YAAY,CAAC,IAAI,CAAC;MACzC,IAAI,CAAC,IAAI,CAACtG,MAAM,EAAE;QACd;QACA;QACA,IAAI,CAACJ,MAAM,CAAC2G,iBAAiB,CAAC,MAAM;UAChC;UACA;UACA;UACA,IAAI7G,cAAc,EAAE;YAChBhJ,QAAQ,CAACiE,gBAAgB,CAAC,YAAY,EAAE,IAAI,CAAC2F,iBAAiB,CAAC;UACnE,CAAC,MACI;YACD5J,QAAQ,CAACiE,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC2F,iBAAiB,CAAC;UAClE;QACJ,CAAC,CAAC;MACN;MACA9J,MAAM,CAACmE,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC0F,cAAc,CAAC;IAC1D;EACJ;EACAiD,gBAAgBA,CAAA,EAAG;IACf,IAAI,IAAI,CAACnC,IAAI,EAAE;MACX,IAAI,CAACA,IAAI,GAAG,KAAK;MACjB,IAAI,CAACZ,iBAAiB,CAAC+F,YAAY,CAAC,KAAK,CAAC;MAC1C,IAAI,CAAC,IAAI,CAACtG,MAAM,EAAE;QACd,IAAIN,cAAc,EAAE;UAChBhJ,QAAQ,CAACkE,mBAAmB,CAAC,YAAY,EAAE,IAAI,CAAC0F,iBAAiB,CAAC;QACtE,CAAC,MACI;UACD5J,QAAQ,CAACkE,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC0F,iBAAiB,CAAC;QACrE;MACJ;MACA9J,MAAM,CAACoE,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACyF,cAAc,CAAC;MACzD,IAAI,CAAC,IAAI,CAACR,KAAK,CAAC,WAAW,CAAC,EAAE;QAC1B,IAAI,CAACA,KAAK,CAACsD,aAAa,CAAC,CAAC;MAC9B;IACJ;EACJ;EACAD,iBAAiBA,CAAC1K,IAAI,GAAG,IAAI,EAAEyL,MAAM,GAAG,IAAI,EAAEuC,SAAS,GAAG,KAAK,EAAE;IAC7D,IAAI,IAAI,CAAC/F,YAAY,EAAE;MACnB,IAAI,IAAI,CAACe,WAAW,KAAK,CAAC,EAAE;QACxB,IAAI,CAAC7E,IAAI,CAAC9Q,CAAC,GAAG,CAAC;MACnB;MACA,IAAI4a,GAAG,EAAE3J,IAAI,EAAEW,IAAI;MACnB,MAAMiJ,UAAU,GAAG,IAAI,CAACxG,WAAW;MACnCpD,IAAI,GAAG,IAAI,CAACiD,OAAO,CAACrD,SAAS,CAAC,IAAI,CAACC,IAAI,CAAC;MACxC,IAAI,CAAC,IAAI,CAAC8E,aAAa,EAAE;QACrBhE,IAAI,GAAG,IAAI,CAACsC,OAAO,CAAC3B,eAAe,CAAC,IAAI,CAAC2B,OAAO,CAAChD,UAAU,CAAC,IAAI,CAACJ,IAAI,CAAC,CAAC;MAC3E,CAAC,MACI;QACD,IAAI,CAAC6J,SAAS,EAAE;UACZ/I,IAAI,GAAG,IAAI,CAACsC,OAAO,CAAChD,UAAU,CAAC,IAAI,CAACJ,IAAI,CAAC;UACzC,IAAI,CAACY,IAAI,GAAG,IAAI,CAACwC,OAAO,CAAC5B,eAAe,CAAC,IAAI,CAAC4B,OAAO,CAACvC,UAAU,CAACC,IAAI,CAAC,CAAC;QAC3E,CAAC,MACI;UACDA,IAAI,GAAG,IAAI,CAACsC,OAAO,CAACzC,SAAS,CAAC,IAAI,CAACyC,OAAO,CAAC7B,aAAa,CAAC,IAAI,CAACX,IAAI,CAAC,CAAC;UACpE,IAAI,CAACZ,IAAI,GAAG,IAAI,CAACoD,OAAO,CAACrC,UAAU,CAACD,IAAI,CAAC;QAC7C;QACAA,IAAI,GAAG,IAAI,CAACsC,OAAO,CAAC3B,eAAe,CAACX,IAAI,CAAC;QACzC,IAAI,CAAC+C,OAAO,GAAG,IAAI,CAAC7D,IAAI,CAAC9M,CAAC;MAC9B;MACA4W,GAAG,GAAG,IAAI,CAAC1G,OAAO,CAAC3B,eAAe,CAAC,IAAI,CAAC2B,OAAO,CAAChD,UAAU,CAAC,IAAIvH,IAAI,CAAC,IAAI,CAACgL,OAAO,IAAI,IAAI,CAAC7D,IAAI,CAAC9M,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC3G,IAAIoU,MAAM,EAAE;QACR,IAAI,CAACjV,QAAQ,GAAG,IAAIyG,IAAI,CAACiG,IAAI,CAACuC,KAAK,CAAEnB,IAAI,CAACjN,CAAC,GAAI,GAAG,CAAC,EAAE6L,IAAI,CAACuC,KAAK,CAACnB,IAAI,CAACjR,CAAC,GAAG,GAAG,CAAC,EAAE6P,IAAI,CAACuC,KAAK,CAACnB,IAAI,CAAChN,CAAC,GAAG,GAAG,CAAC,EAAE4L,IAAI,CAACuC,KAAK,CAACnB,IAAI,CAACzP,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;QACxI,IAAI,CAAC8C,QAAQ,GAAG,IAAImF,IAAI,CAACmI,IAAI,CAACzM,CAAC,EAAEyM,IAAI,CAACxM,CAAC,EAAEwM,IAAI,CAACvM,CAAC,EAAEwK,IAAI,CAACuC,KAAK,CAACR,IAAI,CAACpQ,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;QAChF,IAAI,IAAI,CAACoU,aAAa,EAAE;UACpB,IAAI,CAACrU,QAAQ,GAAG,IAAIsI,IAAI,CAAC,IAAI,CAAC6H,IAAI,CAAChP,CAAC,EAAE,IAAI,CAACgP,IAAI,CAAC/O,CAAC,EAAE,IAAI,CAAC+O,IAAI,CAAC9O,CAAC,EAAE,IAAI,CAAC8O,IAAI,CAAC7O,CAAC,EAAEgN,IAAI,CAACuC,KAAK,CAAC,IAAI,CAACV,IAAI,CAAClQ,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;QACrH;QACA,MAAMwQ,SAAS,GAAG,IAAI,CAAClP,cAAc,KAAK,QAAQ;QAClD,IAAI,CAACqD,OAAO,GAAG,IAAI,CAAC+N,OAAO,CAACnC,SAAS,CAACH,IAAI,EAAEI,SAAS,CAAC;QACtD,IAAI,CAACtM,QAAQ,GAAG,IAAI,CAACpB,QAAQ,CAAC9C,CAAC;MACnC;MACA,IAAI,IAAI,CAACqU,cAAc,KAAK,MAAM,EAAE;QAChC,IAAI,IAAI,CAACpT,MAAM,KAAK+G,YAAY,CAAC0L,IAAI,IAAI,IAAI,CAACzS,MAAM,KAAK+G,YAAY,CAAC4L,IAAI,IAAI,IAAI,CAAC3S,MAAM,KAAK+G,YAAY,CAAC2L,IAAI,EAAE;UAC7G,IAAI,IAAI,CAACrE,IAAI,CAACtP,CAAC,GAAG,CAAC,EAAE;YACjB,IAAI,CAACiB,MAAM,GAAG,IAAI,CAACqO,IAAI,CAACtP,CAAC,GAAG,CAAC,GAAGgI,YAAY,CAAC0L,IAAI,GAAG1L,YAAY,CAACyL,GAAG;UACxE;QACJ;MACJ;MACA,IAAI,CAACtV,cAAc,GAAG,MAAM,GAAGib,GAAG,CAACzV,CAAC,GAAG,GAAG,GAAGyV,GAAG,CAACxV,CAAC,GAAG,GAAG,GAAGwV,GAAG,CAACvV,CAAC,GAAG,GAAG;MACtE,IAAI,CAACmQ,gBAAgB,GAAG,MAAM,GAAG5D,IAAI,CAACzM,CAAC,GAAG,GAAG,GAAGyM,IAAI,CAACxM,CAAC,GAAG,GAAG,GAAGwM,IAAI,CAACvM,CAAC,GAAG,GAAG;MAC3E,IAAI,CAACgP,WAAW,GAAG,IAAI,CAACH,OAAO,CAACZ,YAAY,CAAC,IAAI,CAACxC,IAAI,EAAE,IAAI,CAAC+E,cAAc,EAAE,IAAI,CAAC/S,cAAc,CAAC;MACjG,IAAI,CAACvC,aAAa,GAAG,IAAI,CAAC2T,OAAO,CAACZ,YAAY,CAAC,IAAI,CAACxC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC;MACvE,IAAI,IAAI,CAACrO,MAAM,KAAK+G,YAAY,CAAC4L,IAAI,EAAE;QACnC,IAAI,CAAChB,SAAS,GAAG,EAAE;MACvB,CAAC,MACI;QACD,IAAI,IAAI,CAACtR,cAAc,KAAK,QAAQ,IAAI,IAAI,CAACA,cAAc,KAAK,SAAS,IACrE,IAAI,CAACA,cAAc,KAAK,QAAQ,EAAE;UAClC,MAAMgY,KAAK,GAAGjL,IAAI,CAACuC,KAAK,CAAC,IAAI,CAACV,IAAI,CAAClQ,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG;UACjD,IAAI,CAAC4S,SAAS,GAAG,SAAS,IAAI,CAAC1C,IAAI,CAAChP,CAAC,IAAI,IAAI,CAACgP,IAAI,CAAC/O,CAAC,IAAI,IAAI,CAAC+O,IAAI,CAAC9O,CAAC,IAAI,IAAI,CAAC8O,IAAI,CAAC7O,CAAC,IAAIiY,KAAK,GAAG;QAClG,CAAC,MACI;UACD,IAAI,CAAC1G,SAAS,GAAG,QAAQ,IAAI,CAAC1C,IAAI,CAAChP,CAAC,IAAI,IAAI,CAACgP,IAAI,CAAC/O,CAAC,IAAI,IAAI,CAAC+O,IAAI,CAAC9O,CAAC,IAAI,IAAI,CAAC8O,IAAI,CAAC7O,CAAC,GAAG;QACxF;MACJ;MACA,IAAI,CAAC/C,MAAM,GAAG,IAAIwQ,cAAc,CAAC,CAAC,IAAI,CAACqE,OAAO,IAAI,IAAI,CAAC7D,IAAI,CAAC9M,CAAC,IAAI,IAAI,CAAC4Q,YAAY,CAAC5Q,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC8M,IAAI,CAAC9Q,CAAC,GAAG,IAAI,CAAC4U,YAAY,CAAC5U,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC8Q,IAAI,CAAC/Q,CAAC,IAAI,IAAI,CAAC6U,YAAY,CAAC7U,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC+Q,IAAI,CAACtP,CAAC,GAAG,IAAI,CAACoT,YAAY,CAACpT,CAAC,GAAG,CAAC,CAAC;MACpN,IAAImL,IAAI,IAAIkO,UAAU,KAAK,IAAI,CAACxG,WAAW,EAAE;QACzC,IAAI,IAAI,CAACuB,aAAa,EAAE;UACpB,IAAI,CAAClB,iBAAiB,CAACmE,WAAW,CAAC,IAAI,CAACzE,SAAS,CAAC;QACtD;QACA,IAAI,CAACM,iBAAiB,CAACoE,YAAY,CAAC,IAAI,CAACzE,WAAW,CAAC;MACzD;IACJ;EACJ;EACA;EACAgE,iBAAiBA,CAAA,EAAG;IAChB,IAAI,IAAI,CAAC1H,eAAe,KAAK,QAAQ,EAAE;MACnC,IAAI,CAAC1B,QAAQ,GAAG,UAAU;IAC9B,CAAC,MACI;MACD,IAAIA,QAAQ,GAAG,QAAQ;QAAE8L,SAAS,GAAG,EAAE;QAAEC,KAAK;MAC9C,IAAIC,UAAU,GAAG,IAAI;QAAEC,aAAa,GAAG,IAAI;MAC3C,IAAIC,IAAI,GAAG,IAAI,CAACtG,mBAAmB,CAAC3F,aAAa,CAAC+L,UAAU;MAC5D,MAAMG,YAAY,GAAG,IAAI,CAAC7E,aAAa,CAACrH,aAAa,CAACS,YAAY;MAClE,OAAOwL,IAAI,KAAK,IAAI,IAAIA,IAAI,CAACE,OAAO,KAAK,MAAM,EAAE;QAC7CL,KAAK,GAAGrQ,MAAM,CAAC2Q,gBAAgB,CAACH,IAAI,CAAC;QACrClM,QAAQ,GAAG+L,KAAK,CAACO,gBAAgB,CAAC,UAAU,CAAC;QAC7CR,SAAS,GAAGC,KAAK,CAACO,gBAAgB,CAAC,WAAW,CAAC;QAC/C,IAAItM,QAAQ,KAAK,QAAQ,IAAIgM,UAAU,KAAK,IAAI,EAAE;UAC9CA,UAAU,GAAGE,IAAI;QACrB;QACA,IAAIJ,SAAS,IAAIA,SAAS,KAAK,MAAM,IAAIG,aAAa,KAAK,IAAI,EAAE;UAC7DA,aAAa,GAAGC,IAAI;QACxB;QACA,IAAIlM,QAAQ,KAAK,OAAO,EAAE;UACtBgM,UAAU,GAAGC,aAAa;UAC1B;QACJ;QACAC,IAAI,GAAGA,IAAI,CAACF,UAAU;MAC1B;MACA,MAAMO,YAAY,GAAG,IAAI,CAACC,eAAe,CAAC,IAAI,CAAC5G,mBAAmB,CAAC3F,aAAa,EAAGD,QAAQ,KAAK,OAAQ,CAAC;MACzG,IAAI,IAAI,CAACoG,oBAAoB,IAAKpG,QAAQ,KAAK,OAAO,KACjD,CAACgM,UAAU,IAAIA,UAAU,YAAYS,kBAAkB,CAAE,EAAE;QAC5D,IAAI,CAACrR,GAAG,GAAGmR,YAAY,CAACnR,GAAG;QAC3B,IAAI,CAACC,IAAI,GAAGkR,YAAY,CAAClR,IAAI;MACjC,CAAC,MACI;QACD,IAAI2Q,UAAU,KAAK,IAAI,EAAE;UACrBA,UAAU,GAAGE,IAAI;QACrB;QACA,MAAMQ,SAAS,GAAG,IAAI,CAACF,eAAe,CAACR,UAAU,EAAGhM,QAAQ,KAAK,OAAQ,CAAC;QAC1E,IAAI,CAAC5E,GAAG,GAAGmR,YAAY,CAACnR,GAAG,GAAGsR,SAAS,CAACtR,GAAG;QAC3C,IAAI,CAACC,IAAI,GAAGkR,YAAY,CAAClR,IAAI,GAAGqR,SAAS,CAACrR,IAAI;MAClD;MACA,IAAI2E,QAAQ,KAAK,OAAO,EAAE;QACtB,IAAI,CAACA,QAAQ,GAAG,OAAO;MAC3B;MACA,IAAI2M,WAAW,GAAG,IAAI,CAAC1F,UAAU;MACjC,MAAM2F,YAAY,GAAG,IAAI,CAACtF,aAAa,CAACrH,aAAa,CAACC,qBAAqB,CAAC,CAAC;MAC7E,IAAI,IAAI,CAAC+G,UAAU,KAAK,MAAM,EAAE;QAC5B,MAAM4F,aAAa,GAAG,IAAI,CAACxF,gBAAgB,CAACpH,aAAa,CAACC,qBAAqB,CAAC,CAAC;QACjFyM,WAAW,GAAG9R,wBAAwB,CAAC+R,YAAY,EAAEC,aAAa,CAAC;MACvE;MACA,IAAI,CAACnd,QAAQ,GAAGid,WAAW,KAAK,KAAK,GAC/BR,YAAY,GAAG,CAAC,GAChB1O,SAAS;MACf,IAAI,CAAChO,eAAe,GAAGgO,SAAS;MAChC,QAAQkP,WAAW;QACf,KAAK,KAAK;UACN,IAAI,CAACvR,GAAG,IAAI+Q,YAAY,GAAG,IAAI,CAACtG,eAAe;UAC/C,IAAI,CAACxK,IAAI,IAAI,IAAI,CAAC6L,gBAAgB,GAAG,GAAG,GAAGqF,YAAY,CAACpR,KAAK,GAAG,IAAI,CAAC2K,iBAAiB;UACtF;QACJ,KAAK,QAAQ;UACT,IAAI,CAAC1K,GAAG,IAAImR,YAAY,CAACrR,MAAM,GAAG,IAAI,CAAC2K,eAAe;UACtD,IAAI,CAACxK,IAAI,IAAI,IAAI,CAAC6L,gBAAgB,GAAG,GAAG,GAAGqF,YAAY,CAACpR,KAAK,GAAG,IAAI,CAAC2K,iBAAiB;UACtF;QACJ,KAAK,UAAU;QACf,KAAK,UAAU;UACX,IAAI,CAAC1K,GAAG,IAAI+Q,YAAY,GAAGI,YAAY,CAACrR,MAAM,GAAGqR,YAAY,CAACrR,MAAM,GAAG,IAAI,CAACgM,gBAAgB,GAAG,GAAG;UAClG,IAAI,CAAC7L,IAAI,IAAI,IAAI,CAACmL,OAAO,GAAG,IAAI,CAACX,eAAe,GAAG,CAAC,GAAG,IAAI,CAACC,iBAAiB;UAC7E;QACJ,KAAK,WAAW;QAChB,KAAK,WAAW;UACZ,IAAI,CAAC1K,GAAG,IAAI+Q,YAAY,GAAGI,YAAY,CAACrR,MAAM,GAAGqR,YAAY,CAACrR,MAAM,GAAG,IAAI,CAACgM,gBAAgB,GAAG,GAAG;UAClG,IAAI,CAAC7L,IAAI,IAAIkR,YAAY,CAACpR,KAAK,GAAG,IAAI,CAAC0K,eAAe,GAAG,CAAC,GAAG,IAAI,CAACC,iBAAiB;UACnF;QACJ,KAAK,MAAM;QACX,KAAK,aAAa;QAClB,KAAK,aAAa;UACd,IAAI,CAAC1K,GAAG,IAAImR,YAAY,CAACrR,MAAM,GAAG,IAAI,CAACgM,gBAAgB,GAAG,GAAG,GAAG,IAAI,CAACpB,iBAAiB;UACtF,IAAI,CAACzK,IAAI,IAAI,IAAI,CAACmL,OAAO,GAAG,IAAI,CAACX,eAAe,GAAG,CAAC;UACpD;QACJ,KAAK,OAAO;QACZ,KAAK,cAAc;QACnB,KAAK,cAAc;QACnB;UACI,IAAI,CAACzK,GAAG,IAAImR,YAAY,CAACrR,MAAM,GAAG,IAAI,CAACgM,gBAAgB,GAAG,GAAG,GAAG,IAAI,CAACpB,iBAAiB;UACtF,IAAI,CAACzK,IAAI,IAAIkR,YAAY,CAACpR,KAAK,GAAG,IAAI,CAAC0K,eAAe,GAAG,CAAC;UAC1D;MACR;MACA,MAAMiH,iBAAiB,GAAGpR,MAAM,CAACC,WAAW;MAC5C,MAAMoR,gBAAgB,GAAGrR,MAAM,CAACO,UAAU;MAC1C,MAAM+Q,eAAe,GAAG,IAAI,CAAClO,KAAK,CAACmB,aAAa,CAACC,qBAAqB,CAAC,CAAC;MACxE,MAAM5E,MAAM,GAAG,IAAI,CAACF,GAAG,GAAGwR,YAAY,CAAC1R,MAAM;MAC7C,IAAII,MAAM,GAAGwR,iBAAiB,EAAE;QAC5B,IAAI,CAAC1R,GAAG,GAAG0R,iBAAiB,GAAGF,YAAY,CAAC1R,MAAM;QAClD,IAAI,CAACzL,eAAe,GAAGud,eAAe,CAACrM,CAAC,GAAG,CAAC,GAAG,EAAE;MACrD;MACA,MAAMpF,KAAK,GAAG,IAAI,CAACF,IAAI,GAAGuR,YAAY,CAACzR,KAAK;MAC5C,IAAII,KAAK,GAAGwR,gBAAgB,EAAE;QAC1B,IAAI,CAAC1R,IAAI,GAAG0R,gBAAgB,GAAGH,YAAY,CAACzR,KAAK;QACjD,IAAI,CAAC1L,eAAe,GAAGud,eAAe,CAACrM,CAAC,GAAG,CAAC,GAAG,EAAE;MACrD;MACA,IAAI,CAACpR,aAAa,GAAGod,WAAW;IACpC;EACJ;EACA;EACApD,YAAYA,CAAC0D,MAAM,EAAEC,KAAK,EAAE;IACxB,IAAIhB,IAAI,GAAGgB,KAAK,CAAClB,UAAU;IAC3B,OAAOE,IAAI,KAAK,IAAI,EAAE;MAClB,IAAIA,IAAI,KAAKe,MAAM,EAAE;QACjB,OAAO,IAAI;MACf;MACAf,IAAI,GAAGA,IAAI,CAACF,UAAU;IAC1B;IACA,OAAO,KAAK;EAChB;EACAQ,eAAeA,CAACW,OAAO,EAAEC,MAAM,EAAE;IAC7B,MAAM;MAAEhS,GAAG;MAAEC;IAAK,CAAC,GAAG8R,OAAO,CAACjN,qBAAqB,CAAC,CAAC;IACrD,OAAO;MACH9E,GAAG,EAAEA,GAAG,IAAIgS,MAAM,GAAG1R,MAAM,CAAC8E,WAAW,GAAG,CAAC,CAAC;MAC5CnF,IAAI,EAAEA,IAAI,IAAI+R,MAAM,GAAG1R,MAAM,CAAC2E,WAAW,GAAG,CAAC,CAAC;MAC9ClF,KAAK,EAAEgS,OAAO,CAAC1M,WAAW;MAC1BvF,MAAM,EAAEiS,OAAO,CAACzM;IACpB,CAAC;EACL;EACA,OAAO7C,IAAI,YAAAwP,6BAAAtP,iBAAA;IAAA,YAAAA,iBAAA,IAAwF8G,oBAAoB,EAjqC9BlX,EAAE,CAAAqT,iBAAA,CAiqC8CrT,EAAE,CAAC2f,MAAM,GAjqCzD3f,EAAE,CAAAqT,iBAAA,CAiqCoErT,EAAE,CAACsT,UAAU,GAjqCnFtT,EAAE,CAAAqT,iBAAA,CAiqC8FrT,EAAE,CAAC4f,iBAAiB,GAjqCpH5f,EAAE,CAAAqT,iBAAA,CAiqC+HrS,QAAQ,GAjqCzIhB,EAAE,CAAAqT,iBAAA,CAiqCoJ9S,WAAW,GAjqCjKP,EAAE,CAAAqT,iBAAA,CAiqC4KO,kBAAkB;EAAA;EACzR,OAAOiM,IAAI,kBAlqC8E7f,EAAE,CAAA8f,iBAAA;IAAAvP,IAAA,EAkqCJ2G,oBAAoB;IAAA1G,SAAA;IAAAuP,SAAA,WAAAC,2BAAA1e,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAlqClBtB,EAAE,CAAAigB,WAAA,CAAA/e,GAAA;QAAFlB,EAAE,CAAAigB,WAAA,CAAA9e,GAAA;QAAFnB,EAAE,CAAAigB,WAAA,CAAA7e,GAAA;MAAA;MAAA,IAAAE,EAAA;QAAA,IAAA4e,EAAA;QAAFlgB,EAAE,CAAAmgB,cAAA,CAAAD,EAAA,GAAFlgB,EAAE,CAAAogB,WAAA,QAAA7e,GAAA,CAAAoY,aAAA,GAAAuG,EAAA,CAAAG,KAAA;QAAFrgB,EAAE,CAAAmgB,cAAA,CAAAD,EAAA,GAAFlgB,EAAE,CAAAogB,WAAA,QAAA7e,GAAA,CAAAqY,SAAA,GAAAsG,EAAA,CAAAG,KAAA;QAAFrgB,EAAE,CAAAmgB,cAAA,CAAAD,EAAA,GAAFlgB,EAAE,CAAAogB,WAAA,QAAA7e,GAAA,CAAAsY,WAAA,GAAAqG,EAAA,CAAAG,KAAA;MAAA;IAAA;IAAA5P,YAAA,WAAA6P,kCAAAhf,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFtB,EAAE,CAAAoC,UAAA,uBAAAme,kDAAAje,MAAA;UAAA,OAkqCJf,GAAA,CAAAuY,SAAA,CAAAxX,MAAgB,CAAC;QAAA,UAlqCftC,EAAE,CAAAwgB,iBAkqCe,CAAC,yBAAAC,oDAAAne,MAAA;UAAA,OAApBf,GAAA,CAAAwY,WAAA,CAAAzX,MAAkB,CAAC;QAAA,UAlqCjBtC,EAAE,CAAAwgB,iBAkqCe,CAAC;MAAA;IAAA;IAAAE,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,8BAAAxf,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAA,MAAAyf,GAAA,GAlqClB/gB,EAAE,CAAAkC,gBAAA;QAAFlC,EAAE,CAAAmC,cAAA,eAkqCq1B,CAAC;QAlqCx1BnC,EAAE,CAAAoC,UAAA,mBAAA4e,mDAAA1e,MAAA;UAAFtC,EAAE,CAAAuC,aAAA,CAAAwe,GAAA;UAAA,OAAF/gB,EAAE,CAAAwC,WAAA,CAkqC2zBF,MAAA,CAAA2P,eAAA,CAAuB,CAAC;QAAA,CAAC,CAAC;QAlqCv1BjS,EAAE,CAAA4F,UAAA,IAAAvE,mCAAA,gBAkqCy+B,CAAC,IAAAW,mCAAA,gBAA6S,CAAC;QAlqC1xChC,EAAE,CAAAmC,cAAA,YAkqCi6C,CAAC,YAAyB,CAAC;QAlqC97CnC,EAAE,CAAAwB,SAAA,YAkqCk/C,CAAC;QAlqCr/CxB,EAAE,CAAAmC,cAAA,aAkqCitD,CAAC;QAlqCptDnC,EAAE,CAAAoC,UAAA,mBAAA6e,mDAAA;UAAFjhB,EAAE,CAAAuC,aAAA,CAAAwe,GAAA;UAAA,OAAF/gB,EAAE,CAAAwC,WAAA,CAAAjB,GAAA,CAAAkY,mBAAA,IAAAlY,GAAA,CAAAiY,YAAA,IAkqCisDjY,GAAA,CAAA6a,YAAA,CAAa,CAAC;QAAA,CAAC,CAAC;QAlqCntDpc,EAAE,CAAA4F,UAAA,IAAAvC,wCAAA,iBAkqCm5D,CAAC;QAlqCt5DrD,EAAE,CAAA8C,YAAA,CAkqC0vE,CAAC;QAlqC7vE9C,EAAE,CAAA4F,UAAA,IAAArC,sCAAA,oBAkqCi+E,CAAC;QAlqCp+EvD,EAAE,CAAA8C,YAAA,CAkqCgiF,CAAC;QAlqCniF9C,EAAE,CAAAmC,cAAA,cAkqC6jF,CAAC;QAlqChkFnC,EAAE,CAAA4F,UAAA,KAAAxB,oCAAA,iBAkqCwoF,CAAC;QAlqC3oFpE,EAAE,CAAAmC,cAAA,iBAkqCq3F,CAAC;QAlqCx3FnC,EAAE,CAAAoC,UAAA,sBAAA8e,uDAAA5e,MAAA;UAAFtC,EAAE,CAAAuC,aAAA,CAAAwe,GAAA;UAAA,OAAF/gB,EAAE,CAAAwC,WAAA,CAkqC8xFjB,GAAA,CAAAwb,WAAA,CAAAza,MAAkB,CAAC;QAAA,CAAC,CAAC,uBAAA6e,wDAAA;UAlqCrzFnhB,EAAE,CAAAuC,aAAA,CAAAwe,GAAA;UAAA,OAAF/gB,EAAE,CAAAwC,WAAA,CAkqCk0FjB,GAAA,CAAAoB,WAAA,CAAY,KAAK,CAAC;QAAA,CAAC,CAAC,qBAAAye,sDAAA;UAlqCx1FphB,EAAE,CAAAuC,aAAA,CAAAwe,GAAA;UAAA,OAAF/gB,EAAE,CAAAwC,WAAA,CAkqCm2FjB,GAAA,CAAAsB,SAAA,CAAU,KAAK,CAAC;QAAA,CAAC,CAAC;QAlqCv3F7C,EAAE,CAAAwB,SAAA,cAkqCy7F,CAAC;QAlqC57FxB,EAAE,CAAA8C,YAAA,CAkqCu8F,CAAC;QAlqC18F9C,EAAE,CAAAmC,cAAA,iBAkqCurG,CAAC;QAlqC1rGnC,EAAE,CAAAoC,UAAA,sBAAAif,uDAAA/e,MAAA;UAAFtC,EAAE,CAAAuC,aAAA,CAAAwe,GAAA;UAAA,OAAF/gB,EAAE,CAAAwC,WAAA,CAkqC0lGjB,GAAA,CAAAyb,aAAA,CAAA1a,MAAoB,CAAC;QAAA,CAAC,CAAC,uBAAAgf,wDAAA;UAlqCnnGthB,EAAE,CAAAuC,aAAA,CAAAwe,GAAA;UAAA,OAAF/gB,EAAE,CAAAwC,WAAA,CAkqCgoGjB,GAAA,CAAAoB,WAAA,CAAY,OAAO,CAAC;QAAA,CAAC,CAAC,qBAAA4e,sDAAA;UAlqCxpGvhB,EAAE,CAAAuC,aAAA,CAAAwe,GAAA;UAAA,OAAF/gB,EAAE,CAAAwC,WAAA,CAkqCmqGjB,GAAA,CAAAsB,SAAA,CAAU,OAAO,CAAC;QAAA,CAAC,CAAC;QAlqCzrG7C,EAAE,CAAAwB,SAAA,cAkqC4vG,CAAC;QAlqC/vGxB,EAAE,CAAA8C,YAAA,CAkqC0wG,CAAC;QAlqC7wG9C,EAAE,CAAAmC,cAAA,iBAkqCyiH,CAAC;QAlqC5iHnC,EAAE,CAAAoC,UAAA,sBAAAof,uDAAAlf,MAAA;UAAFtC,EAAE,CAAAuC,aAAA,CAAAwe,GAAA;UAAA,OAAF/gB,EAAE,CAAAwC,WAAA,CAkqC48GjB,GAAA,CAAA0b,aAAA,CAAA3a,MAAoB,CAAC;QAAA,CAAC,CAAC,uBAAAmf,wDAAA;UAlqCr+GzhB,EAAE,CAAAuC,aAAA,CAAAwe,GAAA;UAAA,OAAF/gB,EAAE,CAAAwC,WAAA,CAkqCk/GjB,GAAA,CAAAoB,WAAA,CAAY,OAAO,CAAC;QAAA,CAAC,CAAC,qBAAA+e,sDAAA;UAlqC1gH1hB,EAAE,CAAAuC,aAAA,CAAAwe,GAAA;UAAA,OAAF/gB,EAAE,CAAAwC,WAAA,CAkqCqhHjB,GAAA,CAAAsB,SAAA,CAAU,OAAO,CAAC;QAAA,CAAC,CAAC;QAlqC3iH7C,EAAE,CAAAwB,SAAA,cAkqC6mH,CAAC;QAlqChnHxB,EAAE,CAAA8C,YAAA,CAkqC2nH,CAAC,CAAW,CAAC,CAAS,CAAC;QAlqCppH9C,EAAE,CAAA4F,UAAA,KAAAd,oCAAA,mBAkqC+xH,CAAC,KAAA2B,oCAAA,mBAA81C,CAAC,KAAAmB,oCAAA,mBAAmpC,CAAC,KAAAoB,oCAAA,iBAA6rC,CAAC,KAAAY,oCAAA,iBAA6oB,CAAC,KAAAK,oCAAA,iBAAwpB,CAAC,KAAAuB,oCAAA,iBAA0P,CAAC,KAAAa,oCAAA,iBAAoqB,CAAC,KAAAK,oCAAA,iBAAiX,CAAC;QAlqC5gU1M,EAAE,CAAA8C,YAAA,CAkqComU,CAAC;MAAA;MAAA,IAAAxB,EAAA;QAlqCvmUtB,EAAE,CAAA6B,WAAA,aAAAN,GAAA,CAAAmX,IAAA,mBAkqCgnB,CAAC,eAAAnX,GAAA,CAAAoX,MAAA,uBAAoD,CAAC,QAAApX,GAAA,CAAAkM,GAAA,MAAsB,CAAC,SAAAlM,GAAA,CAAAmM,IAAA,MAAwB,CAAC,aAAAnM,GAAA,CAAA8Q,QAA6B,CAAC,WAAA9Q,GAAA,CAAAuX,QAAA,MAA8B,CAAC,UAAAvX,GAAA,CAAAsX,OAAA,MAA4B,CAAC;QAlqClzB7Y,EAAE,CAAAsJ,WAAA,SAAA/H,GAAA,CAAAmX,IAkqCmkB,CAAC;QAlqCtkB1Y,EAAE,CAAAiD,SAAA,EAkqCi4B,CAAC;QAlqCp4BjD,EAAE,CAAAgD,UAAA,SAAAzB,GAAA,CAAAwS,eAAA,YAkqCi4B,CAAC;QAlqCp4B/T,EAAE,CAAAiD,SAAA,CAkqC0hC,CAAC;QAlqC7hCjD,EAAE,CAAAgD,UAAA,UAAAzB,GAAA,CAAAwX,WAAA,YAkqC0hC,CAAC;QAlqC7hC/Y,EAAE,CAAAiD,SAAA,EAkqCokD,CAAC;QAlqCvkDjD,EAAE,CAAA6B,WAAA,qBAAAN,GAAA,CAAAoC,aAkqCokD,CAAC,WAAApC,GAAA,CAAAkY,mBAAA,IAAAlY,GAAA,CAAAiY,YAAA,mBAAyE,CAAC;QAlqCjpDxZ,EAAE,CAAAiD,SAAA,CAkqC2wD,CAAC;QAlqC9wDjD,EAAE,CAAAgD,UAAA,SAAAzB,GAAA,CAAAkY,mBAAA,IAAAlY,GAAA,CAAAiY,YAkqC2wD,CAAC;QAlqC9wDxZ,EAAE,CAAAiD,SAAA,CAkqCoyE,CAAC;QAlqCvyEjD,EAAE,CAAAgD,UAAA,SAAAzB,GAAA,CAAA0J,gBAkqCoyE,CAAC;QAlqCvyEjL,EAAE,CAAAiD,SAAA,EAkqC6mF,CAAC;QAlqChnFjD,EAAE,CAAAgD,UAAA,SAAAzB,GAAA,CAAA2E,cAAA,eAkqC6mF,CAAC;QAlqChnFlG,EAAE,CAAAiD,SAAA,CAkqC+wF,CAAC;QAlqClxFjD,EAAE,CAAA6B,WAAA,aAAAN,GAAA,CAAAwX,WAAA,+BAkqC+wF,CAAC;QAlqClxF/Y,EAAE,CAAAgD,UAAA,SAkqC0sF,CAAC;QAlqC7sFhD,EAAE,CAAAiD,SAAA,EAkqCk7F,CAAC;QAlqCr7FjD,EAAE,CAAA6B,WAAA,SAAAN,GAAA,CAAA2B,MAAA,kBAAA3B,GAAA,CAAA2B,MAAA,CAAAkE,CAAA,MAkqCk7F,CAAC;QAlqCr7FpH,EAAE,CAAAiD,SAAA,CAkqC2kG,CAAC;QAlqC9kGjD,EAAE,CAAA6B,WAAA,aAAAN,GAAA,CAAAwX,WAAA,+BAkqC2kG,CAAC;QAlqC9kG/Y,EAAE,CAAAgD,UAAA,SAkqCugG,CAAC;QAlqC1gGhD,EAAE,CAAAiD,SAAA,EAkqCqvG,CAAC;QAlqCxvGjD,EAAE,CAAA6B,WAAA,UAAAN,GAAA,CAAA2B,MAAA,kBAAA3B,GAAA,CAAA2B,MAAA,CAAAC,CAAA,MAkqCqvG,CAAC;QAlqCxvGnD,EAAE,CAAAiD,SAAA,CAkqC+4G,CAAC;QAlqCl5GjD,EAAE,CAAA6B,WAAA,YAAAN,GAAA,CAAA2E,cAAA,kCAkqC+4G,CAAC,qBAAA3E,GAAA,CAAAqX,gBAA6C,CAAC;QAlqCh8G5Y,EAAE,CAAAgD,UAAA,SAkqC00G,CAAC;QAlqC70GhD,EAAE,CAAAiD,SAAA,EAkqCsmH,CAAC;QAlqCzmHjD,EAAE,CAAA6B,WAAA,SAAAN,GAAA,CAAA2B,MAAA,kBAAA3B,GAAA,CAAA2B,MAAA,CAAA0B,CAAA,MAkqCsmH,CAAC;QAlqCzmH5E,EAAE,CAAAiD,SAAA,CAkqC+sH,CAAC;QAlqCltHjD,EAAE,CAAAgD,UAAA,UAAAzB,GAAA,CAAA2X,cAAA,KAAA3X,GAAA,CAAAwX,WAAA,YAkqC+sH,CAAC;QAlqCltH/Y,EAAE,CAAAiD,SAAA,CAkqC6iK,CAAC;QAlqChjKjD,EAAE,CAAAgD,UAAA,UAAAzB,GAAA,CAAA2X,cAAA,KAAA3X,GAAA,CAAAwX,WAAA,YAkqC6iK,CAAC;QAlqChjK/Y,EAAE,CAAAiD,SAAA,CAkqCisM,CAAC;QAlqCpsMjD,EAAE,CAAAgD,UAAA,UAAAzB,GAAA,CAAA2X,cAAA,KAAA3X,GAAA,CAAAwX,WAAA,YAkqCisM,CAAC;QAlqCpsM/Y,EAAE,CAAAiD,SAAA,CAkqC40O,CAAC;QAlqC/0OjD,EAAE,CAAAgD,UAAA,UAAAzB,GAAA,CAAA2X,cAAA,KAAA3X,GAAA,CAAAwX,WAAA,YAkqC40O,CAAC;QAlqC/0O/Y,EAAE,CAAAiD,SAAA,CAkqCikQ,CAAC;QAlqCpkQjD,EAAE,CAAAgD,UAAA,UAAAzB,GAAA,CAAA2X,cAAA,KAAA3X,GAAA,CAAAwX,WAAA,YAkqCikQ,CAAC;QAlqCpkQ/Y,EAAE,CAAAiD,SAAA,CAkqCytR,CAAC;QAlqC5tRjD,EAAE,CAAAgD,UAAA,UAAAzB,GAAA,CAAA2X,cAAA,KAAA3X,GAAA,CAAAwX,WAAA,YAkqCytR,CAAC;QAlqC5tR/Y,EAAE,CAAAiD,SAAA,CAkqCy9R,CAAC;QAlqC59RjD,EAAE,CAAAgD,UAAA,UAAAzB,GAAA,CAAAwC,cAAA,kBAAAxC,GAAA,CAAAwC,cAAA,CAAAC,MAAA,KAAAzC,GAAA,CAAA0J,gBAkqCy9R,CAAC;QAlqC59RjL,EAAE,CAAAiD,SAAA,CAkqC8nT,CAAC;QAlqCjoTjD,EAAE,CAAAgD,UAAA,SAAAzB,GAAA,CAAAgL,UAAA,IAAAhL,GAAA,CAAA+K,cAkqC8nT,CAAC;QAlqCjoTtM,EAAE,CAAAiD,SAAA,CAkqCsgU,CAAC;QAlqCzgUjD,EAAE,CAAAgD,UAAA,SAAAzB,GAAA,CAAAoL,eAkqCsgU,CAAC;MAAA;IAAA;IAAAgV,YAAA,GAAmnyB7gB,EAAE,CAAC8gB,OAAO,EAAmH9gB,EAAE,CAAC+gB,IAAI,EAA6F/gB,EAAE,CAACghB,gBAAgB,EAAoJxS,aAAa,EAAkG4B,eAAe;IAAA6Q,MAAA;IAAAC,aAAA;EAAA;AAC/tnC;AACA;EAAA,QAAAlR,SAAA,oBAAAA,SAAA,KApqC6F9Q,EAAE,CAAA+Q,iBAAA,CAoqCJmG,oBAAoB,EAAc,CAAC;IAClH3G,IAAI,EAAE/P,SAAS;IACfwQ,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,cAAc;MAAE+Q,aAAa,EAAEvhB,iBAAiB,CAACwhB,IAAI;MAAEpB,QAAQ,EAAE,mmTAAmmT;MAAEkB,MAAM,EAAE,CAAC,09xBAA09xB;IAAE,CAAC;EACnqlC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAExR,IAAI,EAAEvQ,EAAE,CAAC2f;EAAO,CAAC,EAAE;IAAEpP,IAAI,EAAEvQ,EAAE,CAACsT;EAAW,CAAC,EAAE;IAAE/C,IAAI,EAAEvQ,EAAE,CAAC4f;EAAkB,CAAC,EAAE;IAAErP,IAAI,EAAE2R,QAAQ;IAAEC,UAAU,EAAE,CAAC;MAC5H5R,IAAI,EAAE7P,MAAM;MACZsQ,IAAI,EAAE,CAAChQ,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAEuP,IAAI,EAAET,SAAS;IAAEqS,UAAU,EAAE,CAAC;MAClC5R,IAAI,EAAE7P,MAAM;MACZsQ,IAAI,EAAE,CAACzQ,WAAW;IACtB,CAAC;EAAE,CAAC,EAAE;IAAEgQ,IAAI,EAAEqD;EAAmB,CAAC,CAAC,EAAkB;IAAE+F,aAAa,EAAE,CAAC;MACvEpJ,IAAI,EAAE5P,SAAS;MACfqQ,IAAI,EAAE,CAAC,aAAa,EAAE;QAAEoR,MAAM,EAAE;MAAK,CAAC;IAC1C,CAAC,CAAC;IAAExI,SAAS,EAAE,CAAC;MACZrJ,IAAI,EAAE5P,SAAS;MACfqQ,IAAI,EAAE,CAAC,WAAW,EAAE;QAAEoR,MAAM,EAAE;MAAK,CAAC;IACxC,CAAC,CAAC;IAAEvI,WAAW,EAAE,CAAC;MACdtJ,IAAI,EAAE5P,SAAS;MACfqQ,IAAI,EAAE,CAAC,aAAa,EAAE;QAAEoR,MAAM,EAAE;MAAK,CAAC;IAC1C,CAAC,CAAC;IAAEtI,SAAS,EAAE,CAAC;MACZvJ,IAAI,EAAElQ,YAAY;MAClB2Q,IAAI,EAAE,CAAC,oBAAoB,EAAE,CAAC,QAAQ,CAAC;IAC3C,CAAC,CAAC;IAAE+I,WAAW,EAAE,CAAC;MACdxJ,IAAI,EAAElQ,YAAY;MAClB2Q,IAAI,EAAE,CAAC,sBAAsB,EAAE,CAAC,QAAQ,CAAC;IAC7C,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA,MAAMqR,WAAW,GAAG,OAAOvR,SAAS,KAAK,WAAW,IAAI,CAAC,CAACA,SAAS;AACnE,MAAMwR,oBAAoB,CAAC;EACvBC,QAAQ;EACRC,GAAG;EACHC,MAAM;EACNC,KAAK;EACLvR,KAAK;EACLwR,QAAQ;EACRC,MAAM;EACNC,aAAa,GAAG,KAAK;EACrBC,aAAa,GAAG,KAAK;EACrBC,MAAM;EACNC,oBAAoB,GAAG,KAAK;EAC5BC,WAAW;EACXpK,OAAO,GAAG,OAAO;EACjBC,QAAQ,GAAG,MAAM;EACjBoK,QAAQ,GAAG,KAAK;EAChBC,UAAU,GAAG,KAAK;EAClBhK,iBAAiB,GAAG,EAAE;EACtB8B,eAAe,GAAG,EAAE;EACpBlC,WAAW,GAAG,OAAO;EACrBC,aAAa,GAAG,KAAK;EACrBC,cAAc,GAAG,MAAM;EACvB/S,cAAc,GAAG,SAAS;EAC1BgT,cAAc,GAAG,KAAK;EACtBnF,eAAe,GAAG,OAAO;EACzBqF,kBAAkB,GAAG,IAAI;EACzBC,mBAAmB,GAAG,IAAI;EAC1B6B,sBAAsB,GAAG,KAAK;EAC9B5B,UAAU,GAAG,MAAM;EACnBC,gBAAgB,GAAG,IAAI;EACvB4B,yBAAyB,GAAG,KAAK;EACjC5O,UAAU,GAAG,KAAK;EAClBH,cAAc,GAAG,IAAI;EACrBD,eAAe,GAAG,oBAAoB;EACtCG,cAAc,GAAG,KAAK;EACtBP,kBAAkB,GAAG,QAAQ;EAC7BD,mBAAmB,GAAG,wBAAwB;EAC9C0N,YAAY,GAAG,KAAK;EACpB/N,aAAa,GAAG,eAAe;EAC/B1H,cAAc;EACdoH,mBAAmB,GAAG,wBAAwB;EAC9ClH,uBAAuB,GAAG,CAAC;EAC3BsH,oBAAoB,GAAG,iBAAiB;EACxCF,yBAAyB,GAAG,sBAAsB;EAClDJ,gBAAgB,GAAG,KAAK;EACxB9G,oBAAoB,GAAG,WAAW;EAClCL,qBAAqB,GAAG,2BAA2B;EACnD8G,wBAAwB,GAAG,8BAA8B;EACzD9I,eAAe,GAAG,CAAC;EACnB6K,eAAe;EACfyW,aAAa,GAAG,IAAInjB,YAAY,CAAC,IAAI,CAAC;EACtCojB,cAAc,GAAG,IAAIpjB,YAAY,CAAC,IAAI,CAAC;EACvCqjB,cAAc,GAAG,IAAIrjB,YAAY,CAAC,IAAI,CAAC;EACvCsjB,eAAe,GAAG,IAAItjB,YAAY,CAAC,IAAI,CAAC;EACxCujB,iBAAiB,GAAG,IAAIvjB,YAAY,CAAC,IAAI,CAAC;EAC1CwjB,eAAe,GAAG,IAAIxjB,YAAY,CAAC,IAAI,CAAC;EACxCyjB,gBAAgB,GAAG,IAAIzjB,YAAY,CAAC,IAAI,CAAC;EACzC0jB,iBAAiB,GAAG,IAAI1jB,YAAY,CAAC,IAAI,CAAC;EAC1C2jB,iBAAiB,GAAG,IAAI3jB,YAAY,CAAC,IAAI,CAAC;EAC1C4jB,iBAAiB,GAAG,IAAI5jB,YAAY,CAAC,KAAK,CAAC;EAC3C6jB,iBAAiB,GAAG,IAAI7jB,YAAY,CAAC,IAAI,CAAC;EAC1C8jB,oBAAoB,GAAG,IAAI9jB,YAAY,CAAC,IAAI,CAAC;EAC7C+jB,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,UAAU,CAAC,CAAC;EACrB;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACD,UAAU,CAAC,CAAC;EACrB;EACAE,WAAWA,CAACxU,KAAK,EAAE;IACf,IAAI,CAACD,WAAW,CAACC,KAAK,CAAC;EAC3B;EACA7C,WAAWA,CAACyV,QAAQ,EAAEC,GAAG,EAAEC,MAAM,EAAEC,KAAK,EAAEvR,KAAK,EAAEwR,QAAQ,EAAE;IACvD,IAAI,CAACJ,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACvR,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACwR,QAAQ,GAAGA,QAAQ;EAC5B;EACApI,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACwI,MAAM,IAAI,IAAI,EAAE;MACrB,IAAI,IAAI,CAACC,oBAAoB,EAAE;QAC3B,IAAI,CAACP,MAAM,CAAC2B,UAAU,CAAC,IAAI,CAACrB,MAAM,CAACsB,QAAQ,CAAC;MAChD;MACA,IAAI,CAACtB,MAAM,CAACuB,OAAO,CAAC,CAAC;MACrB,IAAI,CAACvB,MAAM,GAAG,IAAI;MAClB,IAAI,CAACH,MAAM,GAAG,IAAI;IACtB;EACJ;EACA2B,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAIA,OAAO,CAACtB,QAAQ,IAAI,CAAC,IAAI,CAACC,UAAU,EAAE;MACtC,IAAIqB,OAAO,CAACtB,QAAQ,CAACuB,YAAY,EAAE;QAC/B,IAAI,CAACnK,UAAU,CAAC,CAAC;MACrB,CAAC,MACI,IAAI,CAACkK,OAAO,CAACtB,QAAQ,CAACuB,YAAY,EAAE;QACrC,IAAI,CAACzQ,WAAW,CAAC,CAAC;MACtB;IACJ;IACA,IAAIwQ,OAAO,CAACvB,WAAW,EAAE;MACrB,IAAI,IAAI,CAACL,MAAM,IAAI,CAAC,IAAI,CAACE,aAAa,EAAE;QACpC,IAAI,IAAI,CAAC/O,eAAe,KAAK,QAAQ,EAAE;UACnC,IAAI,CAAC6O,MAAM,CAACjI,eAAe,CAAC6J,OAAO,CAACvB,WAAW,CAACwB,YAAY,CAAC;QACjE;QACA,IAAI,CAAC7B,MAAM,CAAC5X,kBAAkB,CAACwZ,OAAO,CAACvB,WAAW,CAACwB,YAAY,EAAE,KAAK,CAAC;QACvE,IAAI,IAAI,CAACvJ,sBAAsB,IAAI,IAAI,CAACnH,eAAe,KAAK,QAAQ,EAAE;UAClE,IAAI,CAACgP,MAAM,CAAC2B,iBAAiB,CAAChK,aAAa,CAAC,CAAC;QACjD;MACJ;MACA,IAAI,CAACoI,aAAa,GAAG,KAAK;IAC9B;IACA,IAAI0B,OAAO,CAAC/Y,aAAa,IAAI+Y,OAAO,CAACzgB,cAAc,EAAE;MACjD,IAAI,IAAI,CAAC6e,MAAM,EAAE;QACb,IAAI,CAACA,MAAM,CAACvH,eAAe,CAAC,IAAI,CAAC5P,aAAa,EAAE,IAAI,CAAC1H,cAAc,CAAC;MACxE;IACJ;EACJ;EACAuW,UAAUA,CAAA,EAAG;IACT,IAAI,CAAC,IAAI,CAACuI,aAAa,EAAE;MACrB,IAAIH,KAAK,GAAG,IAAI,CAACA,KAAK;MACtB,IAAI,CAACG,aAAa,GAAG,IAAI;MACzB,IAAI,CAACG,oBAAoB,GAAG,KAAK;MACjC,IAAI,IAAI,CAAC9H,sBAAsB,IAAI,IAAI,CAACnH,eAAe,KAAK,QAAQ,EAAE;QAClE,MAAM4Q,oBAAoB,GAAG,IAAI,CAAClC,MAAM,CAACmC,cAAc,CAAC,CAAC,CAAC;QAC1D,MAAMC,WAAW,GAAG,IAAI,CAACtC,QAAQ,CAACuC,GAAG,CAACH,oBAAoB,EAAE/jB,QAAQ,CAACmkB,IAAI,CAAC;QAC1E,IAAIF,WAAW,KAAKjkB,QAAQ,CAACmkB,IAAI,EAAE;UAC/BrC,KAAK,GAAGmC,WAAW,CAACnC,KAAK,IAAImC,WAAW,CAACG,gBAAgB,IAAI,IAAI,CAACtC,KAAK;UACvE,IAAIL,WAAW,IAAIK,KAAK,KAAK,IAAI,CAACA,KAAK,EAAE;YACrCuC,OAAO,CAACC,IAAI,CAAC,wCAAwC,GACjD,0DAA0D,GAC1D,mFAAmF,CAAC;UAC5F;QACJ,CAAC,MACI;UACD,IAAI,CAAClC,oBAAoB,GAAG,IAAI;QACpC;MACJ;MACA,MAAMmC,WAAW,GAAG,IAAI,CAAC3C,GAAG,CAAC4C,uBAAuB,CAAClO,oBAAoB,CAAC;MAC1E,IAAI,IAAI,CAAC8L,oBAAoB,EAAE;QAC3B,IAAI,CAACD,MAAM,GAAGoC,WAAW,CAACE,MAAM,CAAC,IAAI,CAAC9C,QAAQ,CAAC;QAC/C,IAAI,CAACE,MAAM,CAAC6C,UAAU,CAAC,IAAI,CAACvC,MAAM,CAACsB,QAAQ,CAAC;QAC5CpW,QAAQ,CAACsX,IAAI,CAACC,WAAW,CAAC,IAAI,CAACzC,MAAM,CAACsB,QAAQ,CAACoB,SAAS,CAAC,CAAC,CAAC,CAAC;MAChE,CAAC,MACI;QACD,MAAMlD,QAAQ,GAAG3hB,QAAQ,CAACykB,MAAM,CAAC;UAC7BK,SAAS,EAAE,EAAE;UACb;UACA;UACApG,MAAM,EAAEoD,KAAK,CAACH;QAClB,CAAC,CAAC;QACF,IAAI,CAACQ,MAAM,GAAGL,KAAK,CAACiD,eAAe,CAACR,WAAW,EAAE,CAAC,EAAE5C,QAAQ,EAAE,EAAE,CAAC;MACrE;MACA,IAAI,CAACQ,MAAM,CAAChI,QAAQ,CAACD,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC3J,KAAK,EAAE,IAAI,CAAC8R,WAAW,EAAE,IAAI,CAACpK,OAAO,EAAE,IAAI,CAACC,QAAQ,EAAE,IAAI,CAAC/E,eAAe,EAAE,IAAI,CAACkH,eAAe,EAAE,IAAI,CAAClC,WAAW,EAAE,IAAI,CAACC,aAAa,EAAE,IAAI,CAAC9S,cAAc,EAAE,IAAI,CAAC+S,cAAc,EAAE,IAAI,CAACC,cAAc,EAAE,IAAI,CAACC,iBAAiB,EAAE,IAAI,CAACC,kBAAkB,EAAE,IAAI,CAACC,mBAAmB,EAAE,IAAI,CAAC6B,sBAAsB,EAAE,IAAI,CAAC5B,UAAU,EAAE,IAAI,CAACC,gBAAgB,EAAE,IAAI,CAAC4B,yBAAyB,EAAE,IAAI,CAAC1P,aAAa,EAAE,IAAI,CAAC1H,cAAc,EAAE,IAAI,CAACoH,mBAAmB,EAAE,IAAI,CAAClH,uBAAuB,EAAE,IAAI,CAACsH,oBAAoB,EAAE,IAAI,CAACF,yBAAyB,EAAE,IAAI,CAACkB,UAAU,EAAE,IAAI,CAACJ,eAAe,EAAE,IAAI,CAACC,cAAc,EAAE,IAAI,CAACE,cAAc,EAAE,IAAI,CAACR,mBAAmB,EAAE,IAAI,CAACC,kBAAkB,EAAE,IAAI,CAACd,gBAAgB,EAAE,IAAI,CAACnH,qBAAqB,EAAE,IAAI,CAACK,oBAAoB,EAAE,IAAI,CAACyG,wBAAwB,EAAE,IAAI,CAAC4O,YAAY,EAAE,IAAI,CAACrI,KAAK,EAAE,IAAI,CAACxE,eAAe,CAAC;MACp2B,IAAI,CAACiW,MAAM,GAAG,IAAI,CAACG,MAAM,CAAChI,QAAQ;MAClC,IAAI,IAAI,CAAC2H,KAAK,KAAKA,KAAK,EAAE;QACtB,IAAI,CAACK,MAAM,CAAC2B,iBAAiB,CAAChK,aAAa,CAAC,CAAC;MACjD;IACJ,CAAC,MACI,IAAI,IAAI,CAACkI,MAAM,EAAE;MAClB,IAAI,CAACA,MAAM,CAACtI,UAAU,CAAC,IAAI,CAAC2I,WAAW,CAAC;IAC5C;EACJ;EACAjP,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAAC4O,MAAM,IAAI,IAAI,CAAC7O,eAAe,KAAK,OAAO,EAAE;MACjD,IAAI,CAAC6O,MAAM,CAAC5O,WAAW,CAAC,CAAC;IAC7B;EACJ;EACAiI,WAAWA,CAACrM,KAAK,EAAE;IACf,IAAI,CAACkU,iBAAiB,CAAC/T,IAAI,CAACH,KAAK,CAAC;EACtC;EACAiO,YAAYA,CAAC+H,KAAK,EAAE;IAChB,IAAI,CAACvC,cAAc,CAACtT,IAAI,CAAC6V,KAAK,CAAC;IAC/B,IAAIA,KAAK,EAAE;MACP,IAAI,CAACnC,eAAe,CAAC1T,IAAI,CAAC,IAAI,CAACkT,WAAW,CAAC;IAC/C,CAAC,MACI;MACD,IAAI,CAACS,gBAAgB,CAAC3T,IAAI,CAAC,IAAI,CAACkT,WAAW,CAAC;IAChD;EACJ;EACA/G,YAAYA,CAACtM,KAAK,EAAEiW,MAAM,GAAG,IAAI,EAAE;IAC/B,IAAI,CAAC/C,aAAa,GAAG+C,MAAM;IAC3B,IAAI,CAAChC,iBAAiB,CAAC9T,IAAI,CAACH,KAAK,CAAC;EACtC;EACAoM,aAAaA,CAACpM,KAAK,EAAE;IACjB,IAAI,CAACgU,iBAAiB,CAAC7T,IAAI,CAACH,KAAK,CAAC;EACtC;EACAuM,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACwH,iBAAiB,CAAC5T,IAAI,CAAC,CAAC;EACjC;EACAkU,UAAUA,CAAA,EAAG;IACT,MAAMzE,OAAO,GAAG,IAAI,CAACrO,KAAK,CAACmB,aAAa;IACxC,MAAMwT,OAAO,GAAG,IAAI,CAAC3M,iBAAiB,CAAC0C,MAAM,CAAEC,IAAI,IAAKA,IAAI,KAAK0D,OAAO,CAAC;IACzE,IAAI,CAAC,IAAI,CAAC2D,UAAU,IAAI,CAAC2C,OAAO,CAAC9hB,MAAM,EAAE;MACrC,IAAI,OAAOiK,QAAQ,KAAK,WAAW,IAAIuR,OAAO,KAAKvR,QAAQ,CAAC8X,aAAa,EAAE;QACvE,IAAI,CAACzL,UAAU,CAAC,CAAC;MACrB,CAAC,MACI,IAAI,CAAC,IAAI,CAACsI,MAAM,IAAI,CAAC,IAAI,CAACA,MAAM,CAAClK,IAAI,EAAE;QACxC,IAAI,CAAC4B,UAAU,CAAC,CAAC;MACrB,CAAC,MACI;QACD,IAAI,CAACtG,WAAW,CAAC,CAAC;MACtB;IACJ;EACJ;EACAtE,WAAWA,CAACC,KAAK,EAAE;IACf,IAAI,IAAI,CAACiT,MAAM,EAAE;MACb,IAAI,CAACA,MAAM,CAAC5X,kBAAkB,CAAC2E,KAAK,CAACE,MAAM,CAACD,KAAK,EAAE,IAAI,CAAC;IAC5D,CAAC,MACI;MACD,IAAI,CAACqT,WAAW,GAAGtT,KAAK,CAACE,MAAM,CAACD,KAAK;MACrC,IAAI,CAACiU,iBAAiB,CAAC9T,IAAI,CAAC,IAAI,CAACkT,WAAW,CAAC;IACjD;EACJ;EACAzF,YAAYA,CAAC7N,KAAK,EAAE;IAChB,IAAI,CAACyT,aAAa,CAACrT,IAAI,CAACJ,KAAK,CAAC;EAClC;EACAmN,aAAaA,CAACnN,KAAK,EAAE;IACjB,IAAI,CAAC2T,cAAc,CAACvT,IAAI,CAACJ,KAAK,CAAC;EACnC;EACA+L,aAAaA,CAAC/L,KAAK,EAAE;IACjB,IAAI,CAAC4T,eAAe,CAACxT,IAAI,CAACJ,KAAK,CAAC;EACpC;EACAgM,eAAeA,CAAChM,KAAK,EAAE;IACnB,IAAI,CAAC6T,iBAAiB,CAACzT,IAAI,CAACJ,KAAK,CAAC;EACtC;EACAgO,mBAAmBA,CAAC/N,KAAK,EAAE;IACvB,IAAI,CAACmU,oBAAoB,CAAChU,IAAI,CAACH,KAAK,CAAC;EACzC;EACA,OAAOM,IAAI,YAAA8V,6BAAA5V,iBAAA;IAAA,YAAAA,iBAAA,IAAwFkS,oBAAoB,EAv6C9BtiB,EAAE,CAAAqT,iBAAA,CAu6C8CrT,EAAE,CAACY,QAAQ,GAv6C3DZ,EAAE,CAAAqT,iBAAA,CAu6CsErT,EAAE,CAACimB,wBAAwB,GAv6CnGjmB,EAAE,CAAAqT,iBAAA,CAu6C8GrT,EAAE,CAACkmB,cAAc,GAv6CjIlmB,EAAE,CAAAqT,iBAAA,CAu6C4IrT,EAAE,CAACmmB,gBAAgB,GAv6CjKnmB,EAAE,CAAAqT,iBAAA,CAu6C4KrT,EAAE,CAACsT,UAAU,GAv6C3LtT,EAAE,CAAAqT,iBAAA,CAu6CsMO,kBAAkB;EAAA;EACnT,OAAOvD,IAAI,kBAx6C8ErQ,EAAE,CAAAsQ,iBAAA;IAAAC,IAAA,EAw6CJ+R,oBAAoB;IAAA9R,SAAA;IAAAC,YAAA,WAAA2V,kCAAA9kB,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAx6ClBtB,EAAE,CAAAoC,UAAA,mBAAAikB,8CAAA;UAAA,OAw6CJ9kB,GAAA,CAAAyiB,WAAA,CAAY,CAAC;QAAA,CAAM,CAAC,mBAAAsC,8CAAA;UAAA,OAApB/kB,GAAA,CAAA2iB,WAAA,CAAY,CAAC;QAAA,CAAM,CAAC,mBAAAqC,8CAAAjkB,MAAA;UAAA,OAApBf,GAAA,CAAA4iB,WAAA,CAAA7hB,MAAkB,CAAC;QAAA,EAAC;MAAA;IAAA;IAAAsO,MAAA;MAAAqS,WAAA;MAAApK,OAAA;MAAAC,QAAA;MAAAoK,QAAA;MAAAC,UAAA;MAAAhK,iBAAA;MAAA8B,eAAA;MAAAlC,WAAA;MAAAC,aAAA;MAAAC,cAAA;MAAA/S,cAAA;MAAAgT,cAAA;MAAAnF,eAAA;MAAAqF,kBAAA;MAAAC,mBAAA;MAAA6B,sBAAA;MAAA5B,UAAA;MAAAC,gBAAA;MAAA4B,yBAAA;MAAA5O,UAAA;MAAAH,cAAA;MAAAD,eAAA;MAAAG,cAAA;MAAAP,kBAAA;MAAAD,mBAAA;MAAA0N,YAAA;MAAA/N,aAAA;MAAA1H,cAAA;MAAAoH,mBAAA;MAAAlH,uBAAA;MAAAsH,oBAAA;MAAAF,yBAAA;MAAAJ,gBAAA;MAAA9G,oBAAA;MAAAL,qBAAA;MAAA8G,wBAAA;MAAA9I,eAAA;MAAA6K,eAAA;IAAA;IAAAkE,OAAA;MAAAuS,aAAA;MAAAC,cAAA;MAAAC,cAAA;MAAAC,eAAA;MAAAC,iBAAA;MAAAC,eAAA;MAAAC,gBAAA;MAAAC,iBAAA;MAAAC,iBAAA;MAAAC,iBAAA;MAAAC,iBAAA;MAAAC,oBAAA;IAAA;IAAAyC,QAAA;IAAAC,QAAA,GAx6ClBzmB,EAAE,CAAA0mB,oBAAA;EAAA;AAy6C/F;AACA;EAAA,QAAA5V,SAAA,oBAAAA,SAAA,KA16C6F9Q,EAAE,CAAA+Q,iBAAA,CA06CJuR,oBAAoB,EAAc,CAAC;IAClH/R,IAAI,EAAErQ,SAAS;IACf8Q,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,eAAe;MACzBuV,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEjW,IAAI,EAAEvQ,EAAE,CAACY;EAAS,CAAC,EAAE;IAAE2P,IAAI,EAAEvQ,EAAE,CAACimB;EAAyB,CAAC,EAAE;IAAE1V,IAAI,EAAEvQ,EAAE,CAACkmB;EAAe,CAAC,EAAE;IAAE3V,IAAI,EAAEvQ,EAAE,CAACmmB;EAAiB,CAAC,EAAE;IAAE5V,IAAI,EAAEvQ,EAAE,CAACsT;EAAW,CAAC,EAAE;IAAE/C,IAAI,EAAEqD;EAAmB,CAAC,CAAC,EAAkB;IAAEqP,WAAW,EAAE,CAAC;MACrO1S,IAAI,EAAEpQ;IACV,CAAC,CAAC;IAAE0Y,OAAO,EAAE,CAAC;MACVtI,IAAI,EAAEpQ;IACV,CAAC,CAAC;IAAE2Y,QAAQ,EAAE,CAAC;MACXvI,IAAI,EAAEpQ;IACV,CAAC,CAAC;IAAE+iB,QAAQ,EAAE,CAAC;MACX3S,IAAI,EAAEpQ;IACV,CAAC,CAAC;IAAEgjB,UAAU,EAAE,CAAC;MACb5S,IAAI,EAAEpQ;IACV,CAAC,CAAC;IAAEgZ,iBAAiB,EAAE,CAAC;MACpB5I,IAAI,EAAEpQ;IACV,CAAC,CAAC;IAAE8a,eAAe,EAAE,CAAC;MAClB1K,IAAI,EAAEpQ;IACV,CAAC,CAAC;IAAE4Y,WAAW,EAAE,CAAC;MACdxI,IAAI,EAAEpQ;IACV,CAAC,CAAC;IAAE6Y,aAAa,EAAE,CAAC;MAChBzI,IAAI,EAAEpQ;IACV,CAAC,CAAC;IAAE8Y,cAAc,EAAE,CAAC;MACjB1I,IAAI,EAAEpQ;IACV,CAAC,CAAC;IAAE+F,cAAc,EAAE,CAAC;MACjBqK,IAAI,EAAEpQ;IACV,CAAC,CAAC;IAAE+Y,cAAc,EAAE,CAAC;MACjB3I,IAAI,EAAEpQ;IACV,CAAC,CAAC;IAAE4T,eAAe,EAAE,CAAC;MAClBxD,IAAI,EAAEpQ;IACV,CAAC,CAAC;IAAEiZ,kBAAkB,EAAE,CAAC;MACrB7I,IAAI,EAAEpQ;IACV,CAAC,CAAC;IAAEkZ,mBAAmB,EAAE,CAAC;MACtB9I,IAAI,EAAEpQ;IACV,CAAC,CAAC;IAAE+a,sBAAsB,EAAE,CAAC;MACzB3K,IAAI,EAAEpQ;IACV,CAAC,CAAC;IAAEmZ,UAAU,EAAE,CAAC;MACb/I,IAAI,EAAEpQ;IACV,CAAC,CAAC;IAAEoZ,gBAAgB,EAAE,CAAC;MACnBhJ,IAAI,EAAEpQ;IACV,CAAC,CAAC;IAAEgb,yBAAyB,EAAE,CAAC;MAC5B5K,IAAI,EAAEpQ;IACV,CAAC,CAAC;IAAEoM,UAAU,EAAE,CAAC;MACbgE,IAAI,EAAEpQ;IACV,CAAC,CAAC;IAAEiM,cAAc,EAAE,CAAC;MACjBmE,IAAI,EAAEpQ;IACV,CAAC,CAAC;IAAEgM,eAAe,EAAE,CAAC;MAClBoE,IAAI,EAAEpQ;IACV,CAAC,CAAC;IAAEmM,cAAc,EAAE,CAAC;MACjBiE,IAAI,EAAEpQ;IACV,CAAC,CAAC;IAAE4L,kBAAkB,EAAE,CAAC;MACrBwE,IAAI,EAAEpQ;IACV,CAAC,CAAC;IAAE2L,mBAAmB,EAAE,CAAC;MACtByE,IAAI,EAAEpQ;IACV,CAAC,CAAC;IAAEqZ,YAAY,EAAE,CAAC;MACfjJ,IAAI,EAAEpQ;IACV,CAAC,CAAC;IAAEsL,aAAa,EAAE,CAAC;MAChB8E,IAAI,EAAEpQ;IACV,CAAC,CAAC;IAAE4D,cAAc,EAAE,CAAC;MACjBwM,IAAI,EAAEpQ;IACV,CAAC,CAAC;IAAEgL,mBAAmB,EAAE,CAAC;MACtBoF,IAAI,EAAEpQ;IACV,CAAC,CAAC;IAAE8D,uBAAuB,EAAE,CAAC;MAC1BsM,IAAI,EAAEpQ;IACV,CAAC,CAAC;IAAEoL,oBAAoB,EAAE,CAAC;MACvBgF,IAAI,EAAEpQ;IACV,CAAC,CAAC;IAAEkL,yBAAyB,EAAE,CAAC;MAC5BkF,IAAI,EAAEpQ;IACV,CAAC,CAAC;IAAE8K,gBAAgB,EAAE,CAAC;MACnBsF,IAAI,EAAEpQ;IACV,CAAC,CAAC;IAAEgE,oBAAoB,EAAE,CAAC;MACvBoM,IAAI,EAAEpQ;IACV,CAAC,CAAC;IAAE2D,qBAAqB,EAAE,CAAC;MACxByM,IAAI,EAAEpQ;IACV,CAAC,CAAC;IAAEyK,wBAAwB,EAAE,CAAC;MAC3B2F,IAAI,EAAEpQ;IACV,CAAC,CAAC;IAAE2B,eAAe,EAAE,CAAC;MAClByO,IAAI,EAAEpQ;IACV,CAAC,CAAC;IAAEwM,eAAe,EAAE,CAAC;MAClB4D,IAAI,EAAEpQ;IACV,CAAC,CAAC;IAAEijB,aAAa,EAAE,CAAC;MAChB7S,IAAI,EAAEnQ;IACV,CAAC,CAAC;IAAEijB,cAAc,EAAE,CAAC;MACjB9S,IAAI,EAAEnQ;IACV,CAAC,CAAC;IAAEkjB,cAAc,EAAE,CAAC;MACjB/S,IAAI,EAAEnQ;IACV,CAAC,CAAC;IAAEmjB,eAAe,EAAE,CAAC;MAClBhT,IAAI,EAAEnQ;IACV,CAAC,CAAC;IAAEojB,iBAAiB,EAAE,CAAC;MACpBjT,IAAI,EAAEnQ;IACV,CAAC,CAAC;IAAEqjB,eAAe,EAAE,CAAC;MAClBlT,IAAI,EAAEnQ;IACV,CAAC,CAAC;IAAEsjB,gBAAgB,EAAE,CAAC;MACnBnT,IAAI,EAAEnQ;IACV,CAAC,CAAC;IAAEujB,iBAAiB,EAAE,CAAC;MACpBpT,IAAI,EAAEnQ;IACV,CAAC,CAAC;IAAEwjB,iBAAiB,EAAE,CAAC;MACpBrT,IAAI,EAAEnQ;IACV,CAAC,CAAC;IAAEyjB,iBAAiB,EAAE,CAAC;MACpBtT,IAAI,EAAEnQ;IACV,CAAC,CAAC;IAAE0jB,iBAAiB,EAAE,CAAC;MACpBvT,IAAI,EAAEnQ;IACV,CAAC,CAAC;IAAE2jB,oBAAoB,EAAE,CAAC;MACvBxT,IAAI,EAAEnQ;IACV,CAAC,CAAC;IAAE4jB,WAAW,EAAE,CAAC;MACdzT,IAAI,EAAElQ,YAAY;MAClB2Q,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAEkT,WAAW,EAAE,CAAC;MACd3T,IAAI,EAAElQ,YAAY;MAClB2Q,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAEmT,WAAW,EAAE,CAAC;MACd5T,IAAI,EAAElQ,YAAY;MAClB2Q,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC;IAC9B,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM2V,iBAAiB,CAAC;EACpB,OAAOzW,IAAI,YAAA0W,0BAAAxW,iBAAA;IAAA,YAAAA,iBAAA,IAAwFuW,iBAAiB;EAAA;EACpH,OAAOE,IAAI,kBAjiD8E7mB,EAAE,CAAA8mB,gBAAA;IAAAvW,IAAA,EAiiDSoW;EAAiB;EACrH,OAAOI,IAAI,kBAliD8E/mB,EAAE,CAAAgnB,gBAAA;IAAAtB,SAAA,EAkiDuC,CAAC9R,kBAAkB,CAAC;IAAAqT,OAAA,GAAYhmB,YAAY;EAAA;AAClL;AACA;EAAA,QAAA6P,SAAA,oBAAAA,SAAA,KApiD6F9Q,EAAE,CAAA+Q,iBAAA,CAoiDJ4V,iBAAiB,EAAc,CAAC;IAC/GpW,IAAI,EAAE1P,QAAQ;IACdmQ,IAAI,EAAE,CAAC;MACCiW,OAAO,EAAE,CAAChmB,YAAY,CAAC;MACvBimB,OAAO,EAAE,CAAC5E,oBAAoB,CAAC;MAC/BoD,SAAS,EAAE,CAAC9R,kBAAkB,CAAC;MAC/BuT,YAAY,EAAE,CAACjQ,oBAAoB,EAAEoL,oBAAoB,EAAEhT,aAAa,EAAE4B,eAAe;IAC7F,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASjE,IAAI,EAAEiK,oBAAoB,EAAEoL,oBAAoB,EAAEqE,iBAAiB,EAAE/S,kBAAkB,EAAE5G,IAAI,EAAED,IAAI,EAAEF,IAAI,EAAEqE,eAAe,EAAE5B,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}