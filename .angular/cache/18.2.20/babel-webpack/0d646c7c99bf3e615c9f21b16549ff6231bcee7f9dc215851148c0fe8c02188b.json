{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FlexLayoutModule } from '@angular/flex-layout';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSlideToggleModule } from '@angular/material/slide-toggle';\nimport { RouterModule } from '@angular/router';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { MatDynamicFormModule, SwitcheryModule, SwuiChipsAutocompleteModule, SwuiControlMessagesModule, SwuiSelectModule } from '@skywind-group/lib-swui';\nimport { TrimInputValueModule } from 'src/app/common/directives/trim-input-value/trim-input-value.module';\nimport { JurisdictionService } from '../../../../../../common/services/jurisdiction.service';\nimport { EntityEditFormComponent } from './form.component';\nimport { MatEntityEditDialogComponent } from './mat-entity-edit-dialog.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/material/dialog\";\nimport * as i3 from \"@angular/material/button\";\nimport * as i4 from \"@ngx-translate/core\";\nexport const matModules = [MatDialogModule, MatCardModule, MatButtonModule, MatFormFieldModule, MatInputModule, MatCheckboxModule, FlexLayoutModule, MatSlideToggleModule, SwuiChipsAutocompleteModule];\nexport class MatEntityEditDialogModule {\n  static {\n    this.ɵfac = function MatEntityEditDialogModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatEntityEditDialogModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: MatEntityEditDialogModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [JurisdictionService],\n      imports: [CommonModule, TranslateModule, FormsModule, ReactiveFormsModule, RouterModule, SwuiControlMessagesModule, SwitcheryModule, matModules, MatDynamicFormModule, SwuiSelectModule, TrimInputValueModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(MatEntityEditDialogModule, {\n    declarations: [MatEntityEditDialogComponent, EntityEditFormComponent],\n    imports: [CommonModule, TranslateModule, FormsModule, ReactiveFormsModule, RouterModule, SwuiControlMessagesModule, SwitcheryModule, MatDialogModule, MatCardModule, MatButtonModule, MatFormFieldModule, MatInputModule, MatCheckboxModule, FlexLayoutModule, MatSlideToggleModule, SwuiChipsAutocompleteModule, MatDynamicFormModule, SwuiSelectModule, TrimInputValueModule]\n  });\n})();\ni0.ɵɵsetComponentScope(MatEntityEditDialogComponent, [i1.NgIf, i2.MatDialogClose, i2.MatDialogTitle, i2.MatDialogActions, i2.MatDialogContent, i3.MatButton, EntityEditFormComponent], [i1.AsyncPipe, i4.TranslatePipe]);", "map": {"version": 3, "names": ["CommonModule", "FlexLayoutModule", "FormsModule", "ReactiveFormsModule", "MatButtonModule", "MatCardModule", "MatCheckboxModule", "MatDialogModule", "MatFormFieldModule", "MatInputModule", "MatSlideToggleModule", "RouterModule", "TranslateModule", "MatDynamicFormModule", "SwitcheryModule", "SwuiChipsAutocompleteModule", "SwuiControlMessagesModule", "SwuiSelectModule", "TrimInputValueModule", "JurisdictionService", "EntityEditFormComponent", "MatEntityEditDialogComponent", "matModules", "MatEntityEditDialogModule", "imports", "declarations", "i1", "NgIf", "i2", "MatDialogClose", "MatDialogTitle", "MatDialogActions", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "i3", "MatButton", "AsyncPipe", "i4", "TranslatePipe"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/mat-business-structure/dialogs/mat-entity-edit-dialog/mat-entity-edit-dialog.module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { FlexLayoutModule } from '@angular/flex-layout';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSlideToggleModule } from '@angular/material/slide-toggle';\nimport { RouterModule } from '@angular/router';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { MatDynamicFormModule, SwitcheryModule, SwuiChipsAutocompleteModule, SwuiControlMessagesModule, SwuiSelectModule } from '@skywind-group/lib-swui';\nimport { TrimInputValueModule } from 'src/app/common/directives/trim-input-value/trim-input-value.module';\nimport { JurisdictionService } from '../../../../../../common/services/jurisdiction.service';\nimport { EntityEditFormComponent } from './form.component';\nimport { MatEntityEditDialogComponent } from './mat-entity-edit-dialog.component';\n\nexport const matModules = [\n  MatDialogModule,\n  MatCardModule,\n  MatButtonModule,\n  MatFormFieldModule,\n  MatInputModule,\n  MatCheckboxModule,\n  FlexLayoutModule,\n  MatSlideToggleModule,\n  SwuiChipsAutocompleteModule,\n];\n\n@NgModule({\n    imports: [\n        CommonModule,\n        TranslateModule,\n        FormsModule,\n        ReactiveFormsModule,\n        RouterModule,\n\n        SwuiControlMessagesModule,\n        SwitcheryModule,\n\n      ...matModules,\n      MatDynamicFormModule,\n      SwuiSelectModule,\n      TrimInputValueModule\n    ],\n  declarations: [\n    MatEntityEditDialogComponent,\n    EntityEditFormComponent,\n  ],\n  providers: [JurisdictionService]\n})\nexport class MatEntityEditDialogModule {\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,oBAAoB,EAAEC,eAAe,EAAEC,2BAA2B,EAAEC,yBAAyB,EAAEC,gBAAgB,QAAQ,yBAAyB;AACzJ,SAASC,oBAAoB,QAAQ,oEAAoE;AACzG,SAASC,mBAAmB,QAAQ,wDAAwD;AAC5F,SAASC,uBAAuB,QAAQ,kBAAkB;AAC1D,SAASC,4BAA4B,QAAQ,oCAAoC;;;;;;AAEjF,OAAO,MAAMC,UAAU,GAAG,CACxBf,eAAe,EACfF,aAAa,EACbD,eAAe,EACfI,kBAAkB,EAClBC,cAAc,EACdH,iBAAiB,EACjBL,gBAAgB,EAChBS,oBAAoB,EACpBK,2BAA2B,CAC5B;AAwBD,OAAM,MAAOQ,yBAAyB;;;uCAAzBA,yBAAyB;IAAA;EAAA;;;YAAzBA;IAAyB;EAAA;;;iBAFzB,CAACJ,mBAAmB,CAAC;MAAAK,OAAA,GAlB1BxB,YAAY,EACZY,eAAe,EACfV,WAAW,EACXC,mBAAmB,EACnBQ,YAAY,EAEZK,yBAAyB,EACzBF,eAAe,EAEdQ,UAAU,EACbT,oBAAoB,EACpBI,gBAAgB,EAChBC,oBAAoB;IAAA;EAAA;;;2EAQbK,yBAAyB;IAAAE,YAAA,GALlCJ,4BAA4B,EAC5BD,uBAAuB;IAAAI,OAAA,GAhBnBxB,YAAY,EACZY,eAAe,EACfV,WAAW,EACXC,mBAAmB,EACnBQ,YAAY,EAEZK,yBAAyB,EACzBF,eAAe,EApBrBP,eAAe,EACfF,aAAa,EACbD,eAAe,EACfI,kBAAkB,EAClBC,cAAc,EACdH,iBAAiB,EACjBL,gBAAgB,EAChBS,oBAAoB,EACpBK,2BAA2B,EAevBF,oBAAoB,EACpBI,gBAAgB,EAChBC,oBAAoB;EAAA;AAAA;uBAGtBG,4BAA4B,GAAAK,EAAA,CAAAC,IAAA,EAAAC,EAAA,CAAAC,cAAA,EAAAD,EAAA,CAAAE,cAAA,EAAAF,EAAA,CAAAG,gBAAA,EAAAH,EAAA,CAAAI,gBAAA,EAAAC,EAAA,CAAAC,SAAA,EAC5Bd,uBAAuB,IAAAM,EAAA,CAAAS,SAAA,EAAAC,EAAA,CAAAC,aAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}