{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, '__esModule', {\n  value: true\n});\nfunction getDef(f, d) {\n  if (typeof f === 'undefined') {\n    return typeof d === 'undefined' ? f : d;\n  }\n  return f;\n}\nfunction boolean(func, def) {\n  func = getDef(func, def);\n  if (typeof func === 'function') {\n    return function f() {\n      for (var _len = arguments.length, args = Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      return !!func.apply(this, args);\n    };\n  }\n  return !!func ? function () {\n    return true;\n  } : function () {\n    return false;\n  };\n}\nfunction integer(func, def) {\n  func = getDef(func, def);\n  if (typeof func === 'function') {\n    return function f() {\n      for (var _len2 = arguments.length, args = Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        args[_key2] = arguments[_key2];\n      }\n      var n = parseInt(func.apply(this, args), 10);\n      return n != n ? 0 : n;\n    };\n  }\n  func = parseInt(func, 10);\n  return func != func ? function () {\n    return 0;\n  } : function () {\n    return func;\n  };\n}\nfunction string(func, def) {\n  func = getDef(func, def);\n  if (typeof func === 'function') {\n    return function f() {\n      for (var _len3 = arguments.length, args = Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n        args[_key3] = arguments[_key3];\n      }\n      return '' + func.apply(this, args);\n    };\n  }\n  func = '' + func;\n  return function () {\n    return func;\n  };\n}\nexports.boolean = boolean;\nexports.integer = integer;\nexports.string = string;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "getDef", "f", "d", "boolean", "func", "def", "_len", "arguments", "length", "args", "Array", "_key", "apply", "integer", "_len2", "_key2", "n", "parseInt", "string", "_len3", "_key3"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/node_modules/type-func/dist/bundle.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, '__esModule', { value: true });\n\nfunction getDef(f, d) {\n    if (typeof f === 'undefined') {\n        return typeof d === 'undefined' ? f : d;\n    }\n\n    return f;\n}\nfunction boolean(func, def) {\n\n    func = getDef(func, def);\n\n    if (typeof func === 'function') {\n        return function f() {\n            for (var _len = arguments.length, args = Array(_len), _key = 0; _key < _len; _key++) {\n                args[_key] = arguments[_key];\n            }\n\n            return !!func.apply(this, args);\n        };\n    }\n\n    return !!func ? function () {\n        return true;\n    } : function () {\n        return false;\n    };\n}\n\nfunction integer(func, def) {\n\n    func = getDef(func, def);\n\n    if (typeof func === 'function') {\n        return function f() {\n            for (var _len2 = arguments.length, args = Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n                args[_key2] = arguments[_key2];\n            }\n\n            var n = parseInt(func.apply(this, args), 10);\n            return n != n ? 0 : n;\n        };\n    }\n\n    func = parseInt(func, 10);\n\n    return func != func ? function () {\n        return 0;\n    } : function () {\n        return func;\n    };\n}\n\nfunction string(func, def) {\n\n    func = getDef(func, def);\n\n    if (typeof func === 'function') {\n        return function f() {\n            for (var _len3 = arguments.length, args = Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n                args[_key3] = arguments[_key3];\n            }\n\n            return '' + func.apply(this, args);\n        };\n    }\n\n    func = '' + func;\n\n    return function () {\n        return func;\n    };\n}\n\nexports.boolean = boolean;\nexports.integer = integer;\nexports.string = string;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAE7D,SAASC,MAAMA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAClB,IAAI,OAAOD,CAAC,KAAK,WAAW,EAAE;IAC1B,OAAO,OAAOC,CAAC,KAAK,WAAW,GAAGD,CAAC,GAAGC,CAAC;EAC3C;EAEA,OAAOD,CAAC;AACZ;AACA,SAASE,OAAOA,CAACC,IAAI,EAAEC,GAAG,EAAE;EAExBD,IAAI,GAAGJ,MAAM,CAACI,IAAI,EAAEC,GAAG,CAAC;EAExB,IAAI,OAAOD,IAAI,KAAK,UAAU,EAAE;IAC5B,OAAO,SAASH,CAACA,CAAA,EAAG;MAChB,KAAK,IAAIK,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAGC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;QACjFF,IAAI,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;MAChC;MAEA,OAAO,CAAC,CAACP,IAAI,CAACQ,KAAK,CAAC,IAAI,EAAEH,IAAI,CAAC;IACnC,CAAC;EACL;EAEA,OAAO,CAAC,CAACL,IAAI,GAAG,YAAY;IACxB,OAAO,IAAI;EACf,CAAC,GAAG,YAAY;IACZ,OAAO,KAAK;EAChB,CAAC;AACL;AAEA,SAASS,OAAOA,CAACT,IAAI,EAAEC,GAAG,EAAE;EAExBD,IAAI,GAAGJ,MAAM,CAACI,IAAI,EAAEC,GAAG,CAAC;EAExB,IAAI,OAAOD,IAAI,KAAK,UAAU,EAAE;IAC5B,OAAO,SAASH,CAACA,CAAA,EAAG;MAChB,KAAK,IAAIa,KAAK,GAAGP,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAGC,KAAK,CAACI,KAAK,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;QACvFN,IAAI,CAACM,KAAK,CAAC,GAAGR,SAAS,CAACQ,KAAK,CAAC;MAClC;MAEA,IAAIC,CAAC,GAAGC,QAAQ,CAACb,IAAI,CAACQ,KAAK,CAAC,IAAI,EAAEH,IAAI,CAAC,EAAE,EAAE,CAAC;MAC5C,OAAOO,CAAC,IAAIA,CAAC,GAAG,CAAC,GAAGA,CAAC;IACzB,CAAC;EACL;EAEAZ,IAAI,GAAGa,QAAQ,CAACb,IAAI,EAAE,EAAE,CAAC;EAEzB,OAAOA,IAAI,IAAIA,IAAI,GAAG,YAAY;IAC9B,OAAO,CAAC;EACZ,CAAC,GAAG,YAAY;IACZ,OAAOA,IAAI;EACf,CAAC;AACL;AAEA,SAASc,MAAMA,CAACd,IAAI,EAAEC,GAAG,EAAE;EAEvBD,IAAI,GAAGJ,MAAM,CAACI,IAAI,EAAEC,GAAG,CAAC;EAExB,IAAI,OAAOD,IAAI,KAAK,UAAU,EAAE;IAC5B,OAAO,SAASH,CAACA,CAAA,EAAG;MAChB,KAAK,IAAIkB,KAAK,GAAGZ,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAGC,KAAK,CAACS,KAAK,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;QACvFX,IAAI,CAACW,KAAK,CAAC,GAAGb,SAAS,CAACa,KAAK,CAAC;MAClC;MAEA,OAAO,EAAE,GAAGhB,IAAI,CAACQ,KAAK,CAAC,IAAI,EAAEH,IAAI,CAAC;IACtC,CAAC;EACL;EAEAL,IAAI,GAAG,EAAE,GAAGA,IAAI;EAEhB,OAAO,YAAY;IACf,OAAOA,IAAI;EACf,CAAC;AACL;AAEAN,OAAO,CAACK,OAAO,GAAGA,OAAO;AACzBL,OAAO,CAACe,OAAO,GAAGA,OAAO;AACzBf,OAAO,CAACoB,MAAM,GAAGA,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}