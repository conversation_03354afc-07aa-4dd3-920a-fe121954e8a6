{"ast": null, "code": "import { Subject } from 'rxjs';\nimport { filter } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nexport class GamesRefreshService {\n  constructor() {\n    this._refresh = new Subject();\n  }\n  refresh(tab) {\n    this._refresh.next(tab);\n  }\n  listen(tab) {\n    return this._refresh.pipe(filter(tabName => tab === tabName));\n  }\n  static {\n    this.ɵfac = function GamesRefreshService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || GamesRefreshService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: GamesRefreshService,\n      factory: GamesRefreshService.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "filter", "GamesRefreshService", "constructor", "_refresh", "refresh", "tab", "next", "listen", "pipe", "tabName", "factory", "ɵfac"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-games/games-refresh.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { Observable, Subject } from 'rxjs';\nimport { filter } from 'rxjs/operators';\n\ntype TabName = 'general' | 'jp-info';\n\n@Injectable()\nexport class GamesRefreshService {\n  private _refresh = new Subject<TabName>();\n\n  refresh( tab: TabName ): void {\n    this._refresh.next(tab);\n  }\n\n  listen( tab: TabName ): Observable<TabName> {\n    return this._refresh.pipe(filter(tabName => tab === tabName));\n  }\n}\n"], "mappings": "AACA,SAAqBA,OAAO,QAAQ,MAAM;AAC1C,SAASC,MAAM,QAAQ,gBAAgB;;AAKvC,OAAM,MAAOC,mBAAmB;EADhCC,YAAA;IAEU,KAAAC,QAAQ,GAAG,IAAIJ,OAAO,EAAW;;EAEzCK,OAAOA,CAAEC,GAAY;IACnB,IAAI,CAACF,QAAQ,CAACG,IAAI,CAACD,GAAG,CAAC;EACzB;EAEAE,MAAMA,CAAEF,GAAY;IAClB,OAAO,IAAI,CAACF,QAAQ,CAACK,IAAI,CAACR,MAAM,CAACS,OAAO,IAAIJ,GAAG,KAAKI,OAAO,CAAC,CAAC;EAC/D;;;uCATWR,mBAAmB;IAAA;EAAA;;;aAAnBA,mBAAmB;MAAAS,OAAA,EAAnBT,mBAAmB,CAAAU;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}