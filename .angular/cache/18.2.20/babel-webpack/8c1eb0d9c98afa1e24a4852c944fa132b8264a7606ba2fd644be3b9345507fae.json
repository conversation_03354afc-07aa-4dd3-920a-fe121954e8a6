{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FlexModule } from '@angular/flex-layout';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiGridModule, SwuiSchemaTopFilterModule } from '@skywind-group/lib-swui';\nimport { BoConfirmationModule } from '../../../../common/components/bo-confirmation/bo-confirmation.module';\nimport { DownloadCsvModule } from '../../../../common/components/download-csv/download-csv.module';\nimport { GameService } from '../../../../common/services/game.service';\nimport { GameNotifyModule } from '../game-notify/game-notify.module';\nimport { IframeViewModalModule } from '../internal-game-history/modals/iframe-view-modal/iframe-view-modal.module';\nimport { GameHistoryBrokenComponent } from './game-history-broken.component';\nimport { GameHistoryBrokenService } from './game-history-broken.service';\nimport * as i0 from \"@angular/core\";\nexport class GameHistoryBrokenModule {\n  static {\n    this.ɵfac = function GameHistoryBrokenModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || GameHistoryBrokenModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: GameHistoryBrokenModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [GameHistoryBrokenService, GameService],\n      imports: [CommonModule, SwuiGridModule, FlexModule, MatIconModule, MatButtonModule, MatDialogModule, TranslateModule, BoConfirmationModule, GameNotifyModule, MatTooltipModule, SwuiSchemaTopFilterModule, IframeViewModalModule, MatProgressSpinnerModule, DownloadCsvModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(GameHistoryBrokenModule, {\n    declarations: [GameHistoryBrokenComponent],\n    imports: [CommonModule, SwuiGridModule, FlexModule, MatIconModule, MatButtonModule, MatDialogModule, TranslateModule, BoConfirmationModule, GameNotifyModule, MatTooltipModule, SwuiSchemaTopFilterModule, IframeViewModalModule, MatProgressSpinnerModule, DownloadCsvModule],\n    exports: [GameHistoryBrokenComponent]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FlexModule", "MatButtonModule", "MatDialogModule", "MatIconModule", "MatProgressSpinnerModule", "MatTooltipModule", "TranslateModule", "SwuiGridModule", "SwuiSchemaTopFilterModule", "BoConfirmationModule", "DownloadCsvModule", "GameService", "GameNotifyModule", "IframeViewModalModule", "GameHistoryBrokenComponent", "GameHistoryBrokenService", "GameHistoryBrokenModule", "imports", "declarations", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/gamehistory/components/game-history-broken/game-history-broken.module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { FlexModule } from '@angular/flex-layout';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiGridModule, SwuiSchemaTopFilterModule } from '@skywind-group/lib-swui';\n\nimport { BoConfirmationModule } from '../../../../common/components/bo-confirmation/bo-confirmation.module';\nimport { DownloadCsvModule } from '../../../../common/components/download-csv/download-csv.module';\nimport { GameService } from '../../../../common/services/game.service';\nimport { GameNotifyModule } from '../game-notify/game-notify.module';\nimport { IframeViewModalComponent } from '../internal-game-history/modals/iframe-view-modal/iframe-view-modal.component';\nimport { IframeViewModalModule } from '../internal-game-history/modals/iframe-view-modal/iframe-view-modal.module';\nimport { GameHistoryBrokenComponent } from './game-history-broken.component';\nimport { GameHistoryBrokenService } from './game-history-broken.service';\n\n\n@NgModule({\n  declarations: [\n    GameHistoryBrokenComponent\n  ],\n  exports: [\n    GameHistoryBrokenComponent\n  ],\n    imports: [\n        CommonModule,\n        SwuiGridModule,\n        FlexModule,\n        MatIconModule,\n        MatButtonModule,\n        MatDialogModule,\n        TranslateModule,\n        BoConfirmationModule,\n        GameNotifyModule,\n        MatTooltipModule,\n        SwuiSchemaTopFilterModule,\n        IframeViewModalModule,\n        MatProgressSpinnerModule,\n        DownloadCsvModule,\n    ],\n  providers: [\n    GameHistoryBrokenService,\n    GameService,\n  ],\n})\nexport class GameHistoryBrokenModule {\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,UAAU,QAAQ,sBAAsB;AACjD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,cAAc,EAAEC,yBAAyB,QAAQ,yBAAyB;AAEnF,SAASC,oBAAoB,QAAQ,sEAAsE;AAC3G,SAASC,iBAAiB,QAAQ,gEAAgE;AAClG,SAASC,WAAW,QAAQ,0CAA0C;AACtE,SAASC,gBAAgB,QAAQ,mCAAmC;AAEpE,SAASC,qBAAqB,QAAQ,4EAA4E;AAClH,SAASC,0BAA0B,QAAQ,iCAAiC;AAC5E,SAASC,wBAAwB,QAAQ,+BAA+B;;AA+BxE,OAAM,MAAOC,uBAAuB;;;uCAAvBA,uBAAuB;IAAA;EAAA;;;YAAvBA;IAAuB;EAAA;;;iBALvB,CACTD,wBAAwB,EACxBJ,WAAW,CACZ;MAAAM,OAAA,GAlBKlB,YAAY,EACZQ,cAAc,EACdP,UAAU,EACVG,aAAa,EACbF,eAAe,EACfC,eAAe,EACfI,eAAe,EACfG,oBAAoB,EACpBG,gBAAgB,EAChBP,gBAAgB,EAChBG,yBAAyB,EACzBK,qBAAqB,EACrBT,wBAAwB,EACxBM,iBAAiB;IAAA;EAAA;;;2EAOZM,uBAAuB;IAAAE,YAAA,GA1BhCJ,0BAA0B;IAAAG,OAAA,GAMtBlB,YAAY,EACZQ,cAAc,EACdP,UAAU,EACVG,aAAa,EACbF,eAAe,EACfC,eAAe,EACfI,eAAe,EACfG,oBAAoB,EACpBG,gBAAgB,EAChBP,gBAAgB,EAChBG,yBAAyB,EACzBK,qBAAqB,EACrBT,wBAAwB,EACxBM,iBAAiB;IAAAS,OAAA,GAhBrBL,0BAA0B;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}