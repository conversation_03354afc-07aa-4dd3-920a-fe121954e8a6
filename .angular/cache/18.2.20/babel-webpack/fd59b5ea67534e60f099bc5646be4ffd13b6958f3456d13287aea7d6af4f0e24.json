{"ast": null, "code": "'use strict';\n\nvar atoa = require('atoa');\nvar debounce = require('./debounce');\nmodule.exports = function emitter(thing, options) {\n  var opts = options || {};\n  var evt = {};\n  if (thing === undefined) {\n    thing = {};\n  }\n  thing.on = function (type, fn) {\n    if (!evt[type]) {\n      evt[type] = [fn];\n    } else {\n      evt[type].push(fn);\n    }\n    return thing;\n  };\n  thing.once = function (type, fn) {\n    fn._once = true; // thing.off(fn) still works!\n    thing.on(type, fn);\n    return thing;\n  };\n  thing.off = function (type, fn) {\n    var c = arguments.length;\n    if (c === 1) {\n      delete evt[type];\n    } else if (c === 0) {\n      evt = {};\n    } else {\n      var et = evt[type];\n      if (!et) {\n        return thing;\n      }\n      et.splice(et.indexOf(fn), 1);\n    }\n    return thing;\n  };\n  thing.emit = function () {\n    var args = atoa(arguments);\n    return thing.emitterSnapshot(args.shift()).apply(this, args);\n  };\n  thing.emitterSnapshot = function (type) {\n    var et = (evt[type] || []).slice(0);\n    return function () {\n      var args = atoa(arguments);\n      var ctx = this || thing;\n      if (type === 'error' && opts.throws !== false && !et.length) {\n        throw args.length === 1 ? args[0] : args;\n      }\n      et.forEach(function emitter(listen) {\n        if (opts.async) {\n          debounce(listen, args, ctx);\n        } else {\n          listen.apply(ctx, args);\n        }\n        if (listen._once) {\n          thing.off(type, listen);\n        }\n      });\n      return thing;\n    };\n  };\n  return thing;\n};", "map": {"version": 3, "names": ["atoa", "require", "debounce", "module", "exports", "emitter", "thing", "options", "opts", "evt", "undefined", "on", "type", "fn", "push", "once", "_once", "off", "c", "arguments", "length", "et", "splice", "indexOf", "emit", "args", "emitterSnap<PERSON>", "shift", "apply", "slice", "ctx", "throws", "for<PERSON>ach", "listen", "async"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/node_modules/contra/emitter.js"], "sourcesContent": ["'use strict';\n\nvar atoa = require('atoa');\nvar debounce = require('./debounce');\n\nmodule.exports = function emitter (thing, options) {\n  var opts = options || {};\n  var evt = {};\n  if (thing === undefined) { thing = {}; }\n  thing.on = function (type, fn) {\n    if (!evt[type]) {\n      evt[type] = [fn];\n    } else {\n      evt[type].push(fn);\n    }\n    return thing;\n  };\n  thing.once = function (type, fn) {\n    fn._once = true; // thing.off(fn) still works!\n    thing.on(type, fn);\n    return thing;\n  };\n  thing.off = function (type, fn) {\n    var c = arguments.length;\n    if (c === 1) {\n      delete evt[type];\n    } else if (c === 0) {\n      evt = {};\n    } else {\n      var et = evt[type];\n      if (!et) { return thing; }\n      et.splice(et.indexOf(fn), 1);\n    }\n    return thing;\n  };\n  thing.emit = function () {\n    var args = atoa(arguments);\n    return thing.emitterSnapshot(args.shift()).apply(this, args);\n  };\n  thing.emitterSnapshot = function (type) {\n    var et = (evt[type] || []).slice(0);\n    return function () {\n      var args = atoa(arguments);\n      var ctx = this || thing;\n      if (type === 'error' && opts.throws !== false && !et.length) { throw args.length === 1 ? args[0] : args; }\n      et.forEach(function emitter (listen) {\n        if (opts.async) { debounce(listen, args, ctx); } else { listen.apply(ctx, args); }\n        if (listen._once) { thing.off(type, listen); }\n      });\n      return thing;\n    };\n  };\n  return thing;\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,IAAI,GAAGC,OAAO,CAAC,MAAM,CAAC;AAC1B,IAAIC,QAAQ,GAAGD,OAAO,CAAC,YAAY,CAAC;AAEpCE,MAAM,CAACC,OAAO,GAAG,SAASC,OAAOA,CAAEC,KAAK,EAAEC,OAAO,EAAE;EACjD,IAAIC,IAAI,GAAGD,OAAO,IAAI,CAAC,CAAC;EACxB,IAAIE,GAAG,GAAG,CAAC,CAAC;EACZ,IAAIH,KAAK,KAAKI,SAAS,EAAE;IAAEJ,KAAK,GAAG,CAAC,CAAC;EAAE;EACvCA,KAAK,CAACK,EAAE,GAAG,UAAUC,IAAI,EAAEC,EAAE,EAAE;IAC7B,IAAI,CAACJ,GAAG,CAACG,IAAI,CAAC,EAAE;MACdH,GAAG,CAACG,IAAI,CAAC,GAAG,CAACC,EAAE,CAAC;IAClB,CAAC,MAAM;MACLJ,GAAG,CAACG,IAAI,CAAC,CAACE,IAAI,CAACD,EAAE,CAAC;IACpB;IACA,OAAOP,KAAK;EACd,CAAC;EACDA,KAAK,CAACS,IAAI,GAAG,UAAUH,IAAI,EAAEC,EAAE,EAAE;IAC/BA,EAAE,CAACG,KAAK,GAAG,IAAI,CAAC,CAAC;IACjBV,KAAK,CAACK,EAAE,CAACC,IAAI,EAAEC,EAAE,CAAC;IAClB,OAAOP,KAAK;EACd,CAAC;EACDA,KAAK,CAACW,GAAG,GAAG,UAAUL,IAAI,EAAEC,EAAE,EAAE;IAC9B,IAAIK,CAAC,GAAGC,SAAS,CAACC,MAAM;IACxB,IAAIF,CAAC,KAAK,CAAC,EAAE;MACX,OAAOT,GAAG,CAACG,IAAI,CAAC;IAClB,CAAC,MAAM,IAAIM,CAAC,KAAK,CAAC,EAAE;MAClBT,GAAG,GAAG,CAAC,CAAC;IACV,CAAC,MAAM;MACL,IAAIY,EAAE,GAAGZ,GAAG,CAACG,IAAI,CAAC;MAClB,IAAI,CAACS,EAAE,EAAE;QAAE,OAAOf,KAAK;MAAE;MACzBe,EAAE,CAACC,MAAM,CAACD,EAAE,CAACE,OAAO,CAACV,EAAE,CAAC,EAAE,CAAC,CAAC;IAC9B;IACA,OAAOP,KAAK;EACd,CAAC;EACDA,KAAK,CAACkB,IAAI,GAAG,YAAY;IACvB,IAAIC,IAAI,GAAGzB,IAAI,CAACmB,SAAS,CAAC;IAC1B,OAAOb,KAAK,CAACoB,eAAe,CAACD,IAAI,CAACE,KAAK,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,IAAI,EAAEH,IAAI,CAAC;EAC9D,CAAC;EACDnB,KAAK,CAACoB,eAAe,GAAG,UAAUd,IAAI,EAAE;IACtC,IAAIS,EAAE,GAAG,CAACZ,GAAG,CAACG,IAAI,CAAC,IAAI,EAAE,EAAEiB,KAAK,CAAC,CAAC,CAAC;IACnC,OAAO,YAAY;MACjB,IAAIJ,IAAI,GAAGzB,IAAI,CAACmB,SAAS,CAAC;MAC1B,IAAIW,GAAG,GAAG,IAAI,IAAIxB,KAAK;MACvB,IAAIM,IAAI,KAAK,OAAO,IAAIJ,IAAI,CAACuB,MAAM,KAAK,KAAK,IAAI,CAACV,EAAE,CAACD,MAAM,EAAE;QAAE,MAAMK,IAAI,CAACL,MAAM,KAAK,CAAC,GAAGK,IAAI,CAAC,CAAC,CAAC,GAAGA,IAAI;MAAE;MACzGJ,EAAE,CAACW,OAAO,CAAC,SAAS3B,OAAOA,CAAE4B,MAAM,EAAE;QACnC,IAAIzB,IAAI,CAAC0B,KAAK,EAAE;UAAEhC,QAAQ,CAAC+B,MAAM,EAAER,IAAI,EAAEK,GAAG,CAAC;QAAE,CAAC,MAAM;UAAEG,MAAM,CAACL,KAAK,CAACE,GAAG,EAAEL,IAAI,CAAC;QAAE;QACjF,IAAIQ,MAAM,CAACjB,KAAK,EAAE;UAAEV,KAAK,CAACW,GAAG,CAACL,IAAI,EAAEqB,MAAM,CAAC;QAAE;MAC/C,CAAC,CAAC;MACF,OAAO3B,KAAK;IACd,CAAC;EACH,CAAC;EACD,OAAOA,KAAK;AACd,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}