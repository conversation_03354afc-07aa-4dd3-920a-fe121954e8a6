{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { StatusConfirmComponent } from './status-confirm.component';\nimport * as i0 from \"@angular/core\";\nexport const matModules = [MatDialogModule, MatButtonModule];\nexport class StatusConfirmModule {\n  static {\n    this.ɵfac = function StatusConfirmModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || StatusConfirmModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: StatusConfirmModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, TranslateModule, matModules]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(StatusConfirmModule, {\n    declarations: [StatusConfirmComponent],\n    imports: [CommonModule, TranslateModule, MatDialogModule, MatButtonModule],\n    exports: [StatusConfirmComponent]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "TranslateModule", "MatButtonModule", "MatDialogModule", "StatusConfirmComponent", "matModules", "StatusConfirmModule", "declarations", "imports", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/mat-business-structure/dialogs/status-confirm/status-confirm.module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { TranslateModule } from '@ngx-translate/core';\n\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { StatusConfirmComponent } from './status-confirm.component';\n\nexport const matModules = [\n  MatDialogModule,\n  MatButtonModule,\n];\n\n@NgModule({\n  imports: [\n    CommonModule,\n    TranslateModule,\n    ...matModules,\n  ],\n  declarations: [StatusConfirmComponent],\n  exports: [StatusConfirmComponent]\n})\nexport class StatusConfirmModule {\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,eAAe,QAAQ,qBAAqB;AAErD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,sBAAsB,QAAQ,4BAA4B;;AAEnE,OAAO,MAAMC,UAAU,GAAG,CACxBF,eAAe,EACfD,eAAe,CAChB;AAWD,OAAM,MAAOI,mBAAmB;;;uCAAnBA,mBAAmB;IAAA;EAAA;;;YAAnBA;IAAmB;EAAA;;;gBAP5BN,YAAY,EACZC,eAAe,EACZI,UAAU;IAAA;EAAA;;;2EAKJC,mBAAmB;IAAAC,YAAA,GAHfH,sBAAsB;IAAAI,OAAA,GAJnCR,YAAY,EACZC,eAAe,EAPjBE,eAAe,EACfD,eAAe;IAAAO,OAAA,GAULL,sBAAsB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}