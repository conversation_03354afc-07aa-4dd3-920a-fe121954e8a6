{"ast": null, "code": "import { transformBlack, transformColumn, transformCorner, transformDozen, transformEven, transformHigh, transformLine, transformLow, transformOdd, transformRed, transformSplit, transformStraight, transformStreet } from './transformers';\nconst mappings = {\n  straight: transformStraight,\n  split: transformSplit,\n  street: transformStreet,\n  corner: transformCorner,\n  line: transformLine,\n  column: transformColumn,\n  dozen: transformDozen,\n  red: transformRed,\n  black: transformBlack,\n  low: transformLow,\n  high: transformHigh,\n  odd: transformOdd,\n  even: transformEven\n};\nconst transformBets = originalBets => {\n  let transformedBets = [];\n  Object.keys(originalBets).forEach(betType => {\n    transformedBets = [...transformedBets, ...(mappings[betType] ? mappings[betType](originalBets[betType]) : [])];\n  });\n  return transformedBets;\n};\nexport const transformHistoryResponse = originalDetails => {\n  return {\n    betStates: transformBets(originalDetails.payload.table.bets),\n    request: 'play',\n    roundEnded: originalDetails.roundEnded,\n    stopPosition: originalDetails.payload.ballPosition,\n    totalBet: 0,\n    totalWin: originalDetails.totalWin\n  };\n};\nexport default transformHistoryResponse;", "map": {"version": 3, "names": ["transformBlack", "transformColumn", "<PERSON><PERSON><PERSON><PERSON>", "transformDozen", "transformEven", "transformHigh", "transformLine", "transformLow", "transformOdd", "transformRed", "transformSplit", "transformStraight", "transformStreet", "mappings", "straight", "split", "street", "corner", "line", "column", "dozen", "red", "black", "low", "high", "odd", "even", "transformBets", "originalBets", "transformedBets", "Object", "keys", "for<PERSON>ach", "betType", "transformHistoryResponse", "originalDetails", "betStates", "payload", "table", "bets", "request", "roundEnded", "stopPosition", "ballPosition", "totalBet", "totalWin"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/gamehistory/components/internal-game-history/spin-details/sw_live_roulette_transform/transform.ts"], "sourcesContent": ["import { IOriginalBets, IOriginalDetails, ITransformedBet, ITransformedDetails } from './interfaces';\nimport {\n  transformBlack, transformColumn, transformCorner, transformDozen, transformEven, transformHigh, transformLine, transformLow, transformOdd,\n  transformRed, transformSplit, transformStraight, transformStreet,\n} from './transformers';\n\nconst mappings = {\n  straight: transformStraight,\n  split: transformSplit,\n  street: transformStreet,\n  corner: transformCorner,\n  line: transformLine,\n  column: transformColumn,\n  dozen: transformDozen,\n  red: transformRed,\n  black: transformBlack,\n  low: transformLow,\n  high: transformHigh,\n  odd: transformOdd,\n  even: transformEven\n};\n\nconst transformBets = ( originalBets: IOriginalBets ): ITransformedBet[] => {\n  let transformedBets: ITransformedBet[] = [];\n\n  Object.keys(originalBets).forEach(( betType ) => {\n    transformedBets = [...transformedBets, ...(mappings[betType] ? mappings[betType](originalBets[betType]) : [])];\n  });\n\n  return transformedBets;\n};\n\nexport const transformHistoryResponse = ( originalDetails: IOriginalDetails ): ITransformedDetails => {\n\n  return {\n    betStates: transformBets(originalDetails.payload.table.bets),\n    request: 'play',\n    roundEnded: originalDetails.roundEnded,\n    stopPosition: originalDetails.payload.ballPosition,\n    totalBet: 0,\n    totalWin: originalDetails.totalWin\n  };\n};\n\nexport default transformHistoryResponse;\n"], "mappings": "AACA,SACEA,cAAc,EAAEC,eAAe,EAAEC,eAAe,EAAEC,cAAc,EAAEC,aAAa,EAAEC,aAAa,EAAEC,aAAa,EAAEC,YAAY,EAAEC,YAAY,EACzIC,YAAY,EAAEC,cAAc,EAAEC,iBAAiB,EAAEC,eAAe,QAC3D,gBAAgB;AAEvB,MAAMC,QAAQ,GAAG;EACfC,QAAQ,EAAEH,iBAAiB;EAC3BI,KAAK,EAAEL,cAAc;EACrBM,MAAM,EAAEJ,eAAe;EACvBK,MAAM,EAAEf,eAAe;EACvBgB,IAAI,EAAEZ,aAAa;EACnBa,MAAM,EAAElB,eAAe;EACvBmB,KAAK,EAAEjB,cAAc;EACrBkB,GAAG,EAAEZ,YAAY;EACjBa,KAAK,EAAEtB,cAAc;EACrBuB,GAAG,EAAEhB,YAAY;EACjBiB,IAAI,EAAEnB,aAAa;EACnBoB,GAAG,EAAEjB,YAAY;EACjBkB,IAAI,EAAEtB;CACP;AAED,MAAMuB,aAAa,GAAKC,YAA2B,IAAwB;EACzE,IAAIC,eAAe,GAAsB,EAAE;EAE3CC,MAAM,CAACC,IAAI,CAACH,YAAY,CAAC,CAACI,OAAO,CAAGC,OAAO,IAAK;IAC9CJ,eAAe,GAAG,CAAC,GAAGA,eAAe,EAAE,IAAIhB,QAAQ,CAACoB,OAAO,CAAC,GAAGpB,QAAQ,CAACoB,OAAO,CAAC,CAACL,YAAY,CAACK,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;EAChH,CAAC,CAAC;EAEF,OAAOJ,eAAe;AACxB,CAAC;AAED,OAAO,MAAMK,wBAAwB,GAAKC,eAAiC,IAA0B;EAEnG,OAAO;IACLC,SAAS,EAAET,aAAa,CAACQ,eAAe,CAACE,OAAO,CAACC,KAAK,CAACC,IAAI,CAAC;IAC5DC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAEN,eAAe,CAACM,UAAU;IACtCC,YAAY,EAAEP,eAAe,CAACE,OAAO,CAACM,YAAY;IAClDC,QAAQ,EAAE,CAAC;IACXC,QAAQ,EAAEV,eAAe,CAACU;GAC3B;AACH,CAAC;AAED,eAAeX,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}