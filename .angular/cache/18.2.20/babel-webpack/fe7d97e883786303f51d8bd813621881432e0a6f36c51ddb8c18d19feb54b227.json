{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FlexLayoutModule } from '@angular/flex-layout';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiCurrencySymbolModule, SwuiNotificationsModule } from '@skywind-group/lib-swui';\nimport { ControlMessagesModule } from '../../../../../common/components/control-messages/control-messages.module';\nimport { BaIfAllowedModule } from '../../../../../common/directives/baIfAllowed/baIfAllowed.module';\nimport { CustomerBalanceDialogComponent } from './customer-balance-dialog/customer-balance-dialog.component';\nimport { CustomerBalanceComponent } from './customer-balance.component';\nimport { PipesModule } from '../../../../../common/pipes/pipes.module';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@skywind-group/lib-swui\";\nexport class CustomerBalanceModule {\n  static {\n    this.ɵfac = function CustomerBalanceModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CustomerBalanceModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: CustomerBalanceModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, ReactiveFormsModule, ControlMessagesModule, BaIfAllowedModule, TranslateModule, SwuiCurrencySymbolModule, SwuiNotificationsModule.forRoot(), MatButtonModule, MatIconModule, MatFormFieldModule, MatInputModule, MatSnackBarModule, FlexLayoutModule, PipesModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(CustomerBalanceModule, {\n    declarations: [CustomerBalanceComponent, CustomerBalanceDialogComponent],\n    imports: [CommonModule, ReactiveFormsModule, ControlMessagesModule, BaIfAllowedModule, TranslateModule, SwuiCurrencySymbolModule, i1.SwuiNotificationsModule, MatButtonModule, MatIconModule, MatFormFieldModule, MatInputModule, MatSnackBarModule, FlexLayoutModule, PipesModule],\n    exports: [CustomerBalanceComponent]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FlexLayoutModule", "ReactiveFormsModule", "MatButtonModule", "MatFormFieldModule", "MatIconModule", "MatInputModule", "MatSnackBarModule", "TranslateModule", "SwuiCurrencySymbolModule", "SwuiNotificationsModule", "ControlMessagesModule", "BaIfAllowedModule", "CustomerBalanceDialogComponent", "CustomerBalanceComponent", "PipesModule", "CustomerBalanceModule", "forRoot", "declarations", "imports", "i1", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/player/components/customer-page/customer-balance/customer-balance.module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { FlexLayoutModule } from '@angular/flex-layout';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiCurrencySymbolModule, SwuiNotificationsModule } from '@skywind-group/lib-swui';\nimport { ControlMessagesModule } from '../../../../../common/components/control-messages/control-messages.module';\nimport { BaIfAllowedModule } from '../../../../../common/directives/baIfAllowed/baIfAllowed.module';\n\nimport { CustomerBalanceDialogComponent } from './customer-balance-dialog/customer-balance-dialog.component';\nimport { CustomerBalanceComponent } from './customer-balance.component';\nimport { PipesModule } from '../../../../../common/pipes/pipes.module';\n\n\n@NgModule({\n  declarations: [\n    CustomerBalanceComponent,\n    CustomerBalanceDialogComponent,\n  ],\n  exports: [CustomerBalanceComponent],\n    imports: [\n        CommonModule,\n        ReactiveFormsModule,\n        ControlMessagesModule,\n        BaIfAllowedModule,\n        TranslateModule,\n        SwuiCurrencySymbolModule,\n        SwuiNotificationsModule.forRoot(),\n        MatButtonModule,\n        MatIconModule,\n        MatFormFieldModule,\n        MatInputModule,\n        MatSnackBarModule,\n        FlexLayoutModule,\n        PipesModule,\n\n    ],\n})\n\nexport class CustomerBalanceModule {\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,wBAAwB,EAAEC,uBAAuB,QAAQ,yBAAyB;AAC3F,SAASC,qBAAqB,QAAQ,2EAA2E;AACjH,SAASC,iBAAiB,QAAQ,iEAAiE;AAEnG,SAASC,8BAA8B,QAAQ,6DAA6D;AAC5G,SAASC,wBAAwB,QAAQ,8BAA8B;AACvE,SAASC,WAAW,QAAQ,0CAA0C;;;AA4BtE,OAAM,MAAOC,qBAAqB;;;uCAArBA,qBAAqB;IAAA;EAAA;;;YAArBA;IAAqB;EAAA;;;gBAlB1BhB,YAAY,EACZE,mBAAmB,EACnBS,qBAAqB,EACrBC,iBAAiB,EACjBJ,eAAe,EACfC,wBAAwB,EACxBC,uBAAuB,CAACO,OAAO,EAAE,EACjCd,eAAe,EACfE,aAAa,EACbD,kBAAkB,EAClBE,cAAc,EACdC,iBAAiB,EACjBN,gBAAgB,EAChBc,WAAW;IAAA;EAAA;;;2EAKNC,qBAAqB;IAAAE,YAAA,GAvB9BJ,wBAAwB,EACxBD,8BAA8B;IAAAM,OAAA,GAI1BnB,YAAY,EACZE,mBAAmB,EACnBS,qBAAqB,EACrBC,iBAAiB,EACjBJ,eAAe,EACfC,wBAAwB,EAAAW,EAAA,CAAAV,uBAAA,EAExBP,eAAe,EACfE,aAAa,EACbD,kBAAkB,EAClBE,cAAc,EACdC,iBAAiB,EACjBN,gBAAgB,EAChBc,WAAW;IAAAM,OAAA,GAfPP,wBAAwB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}