{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { TrimInputValueModule } from '../../../../../../common/directives/trim-input-value/trim-input-value.module';\nimport { MatLanguageDialogComponent } from './mat-language-dialog.component';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { FormsModule } from '@angular/forms';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport * as i0 from \"@angular/core\";\nexport const matModules = [MatTableModule, MatFormFieldModule, MatInputModule, MatButtonModule, MatDialogModule, MatCheckboxModule];\nexport class MatLanguageDialogModule {\n  static {\n    this.ɵfac = function MatLanguageDialogModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatLanguageDialogModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: MatLanguageDialogModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, TranslateModule, FormsModule, matModules, TrimInputValueModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(MatLanguageDialogModule, {\n    declarations: [MatLanguageDialogComponent],\n    imports: [CommonModule, TranslateModule, FormsModule, MatTableModule, MatFormFieldModule, MatInputModule, MatButtonModule, MatDialogModule, MatCheckboxModule, TrimInputValueModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "TrimInputValueModule", "MatLanguageDialogComponent", "TranslateModule", "FormsModule", "MatFormFieldModule", "MatTableModule", "MatCheckboxModule", "MatDialogModule", "MatInputModule", "MatButtonModule", "matModules", "MatLanguageDialogModule", "declarations", "imports"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/mat-business-structure/dialogs/mat-language-dialog/mat-language-dialog.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { TrimInputValueModule } from '../../../../../../common/directives/trim-input-value/trim-input-value.module';\nimport { MatLanguageDialogComponent } from './mat-language-dialog.component';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { FormsModule } from '@angular/forms';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\n\nexport const matModules = [\n  MatTableModule,\n  MatFormFieldModule,\n  MatInputModule,\n  MatButtonModule,\n  MatDialogModule,\n  MatCheckboxModule,\n];\n\n@NgModule({\n    imports: [\n        CommonModule,\n        TranslateModule,\n        FormsModule,\n        ...matModules,\n        TrimInputValueModule,\n    ],\n  declarations: [\n    MatLanguageDialogComponent,\n  ],\n})\nexport class MatLanguageDialogModule {\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,oBAAoB,QAAQ,8EAA8E;AACnH,SAASC,0BAA0B,QAAQ,iCAAiC;AAC5E,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;;AAE1D,OAAO,MAAMC,UAAU,GAAG,CACxBL,cAAc,EACdD,kBAAkB,EAClBI,cAAc,EACdC,eAAe,EACfF,eAAe,EACfD,iBAAiB,CAClB;AAcD,OAAM,MAAOK,uBAAuB;;;uCAAvBA,uBAAuB;IAAA;EAAA;;;YAAvBA;IAAuB;EAAA;;;gBAV5BZ,YAAY,EACZG,eAAe,EACfC,WAAW,EACRO,UAAU,EACbV,oBAAoB;IAAA;EAAA;;;2EAMfW,uBAAuB;IAAAC,YAAA,GAHhCX,0BAA0B;IAAAY,OAAA,GAPtBd,YAAY,EACZG,eAAe,EACfC,WAAW,EAZjBE,cAAc,EACdD,kBAAkB,EAClBI,cAAc,EACdC,eAAe,EACfF,eAAe,EACfD,iBAAiB,EASXN,oBAAoB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}