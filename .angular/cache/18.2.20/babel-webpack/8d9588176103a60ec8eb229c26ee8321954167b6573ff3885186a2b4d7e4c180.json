{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { BehaviorSubject } from 'rxjs';\nimport { take } from 'rxjs/operators';\nimport { tagClassMap } from '../../../app.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/labels.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"ngx-bootstrap/dropdown\";\nimport * as i5 from \"../../directives/trim-input-value/trim-input-value.component\";\nimport * as i6 from \"@ngx-translate/core\";\nimport * as i7 from \"./bo-labels-group.pipe\";\nconst _c0 = [\"textInput\"];\nconst _c1 = a0 => ({\n  \"max-height\": a0\n});\nfunction BoLabelsGroupComponent_ng_container_5_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\")(1, \"div\", 17)(2, \"span\", 18);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function BoLabelsGroupComponent_ng_container_5_li_1_Template_button_click_4_listener($event) {\n      const label_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.onRemoveLabelClick(label_r3, $event));\n    });\n    i0.ɵɵelement(5, \"i\", 15);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const label_r3 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.getLabelClass(label_r3));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", label_r3.title, \" \");\n  }\n}\nfunction BoLabelsGroupComponent_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, BoLabelsGroupComponent_ng_container_5_li_1_Template, 6, 2, \"li\", 16);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.processedSelectedLabels);\n  }\n}\nfunction BoLabelsGroupComponent_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 20);\n    i0.ɵɵelement(1, \"span\", 21);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BoLabelsGroupComponent_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function BoLabelsGroupComponent_button_9_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onSelectBtnClick());\n    });\n    i0.ɵɵelement(1, \"span\", 23);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BoLabelsGroupComponent_ul_10_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 27)(1, \"a\", 28);\n    i0.ɵɵlistener(\"keydown\", function BoLabelsGroupComponent_ul_10_li_1_Template_a_keydown_1_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.handleAddItemKeydown($event));\n    })(\"click\", function BoLabelsGroupComponent_ul_10_li_1_Template_a_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.onAddClick($event));\n    });\n    i0.ɵɵelementStart(2, \"span\", 29);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 30);\n    i0.ɵɵtext(5, \"Add new label\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.searchText, \" \");\n  }\n}\nfunction BoLabelsGroupComponent_ul_10_ng_container_2_ng_container_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"a\", 31);\n    i0.ɵɵlistener(\"keydown\", function BoLabelsGroupComponent_ul_10_ng_container_2_ng_container_1_ng_container_2_Template_a_keydown_1_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r8 = i0.ɵɵnextContext();\n      const label_r10 = ctx_r8.$implicit;\n      const labelIndex_r11 = ctx_r8.index;\n      const ctx_r3 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r3.handleDropdownItemKeydown(label_r10, $event, labelIndex_r11));\n    })(\"click\", function BoLabelsGroupComponent_ul_10_ng_container_2_ng_container_1_ng_container_2_Template_a_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const label_r10 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r3.onDropdownItemClick(label_r10, $event));\n    });\n    i0.ɵɵelementStart(2, \"span\", 32);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const label_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.getLabelClass(label_r10));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", label_r10.title, \" \");\n  }\n}\nfunction BoLabelsGroupComponent_ul_10_ng_container_2_ng_container_1_ng_template_3_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"li\", 33)(2, \"span\");\n    i0.ɵɵtext(3, \"No matches...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction BoLabelsGroupComponent_ul_10_ng_container_2_ng_container_1_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, BoLabelsGroupComponent_ul_10_ng_container_2_ng_container_1_ng_template_3_ng_container_0_Template, 4, 0, \"ng-container\", 7);\n  }\n  if (rf & 2) {\n    const label_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngIf\", label_r10 === -1 && ctx_r3.isCreateNewLabelAllowed === false);\n  }\n}\nfunction BoLabelsGroupComponent_ul_10_ng_container_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"li\", 27);\n    i0.ɵɵtemplate(2, BoLabelsGroupComponent_ul_10_ng_container_2_ng_container_1_ng_container_2_Template, 4, 2, \"ng-container\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, BoLabelsGroupComponent_ul_10_ng_container_2_ng_container_1_ng_template_3_Template, 1, 1, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const label_r10 = ctx.$implicit;\n    const noMatches_r12 = i0.ɵɵreference(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", label_r10 !== -1)(\"ngIfElse\", noMatches_r12);\n  }\n}\nfunction BoLabelsGroupComponent_ul_10_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, BoLabelsGroupComponent_ul_10_ng_container_2_ng_container_1_Template, 5, 2, \"ng-container\", 16);\n    i0.ɵɵpipe(2, \"boLabelsGroupFilter\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind2(2, 1, ctx_r3.processedAvailableLabels, ctx_r3.searchText));\n  }\n}\nfunction BoLabelsGroupComponent_ul_10_ng_template_3_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"li\", 34)(2, \"span\");\n    i0.ɵɵtext(3, \"No items...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction BoLabelsGroupComponent_ul_10_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, BoLabelsGroupComponent_ul_10_ng_template_3_ng_container_0_Template, 4, 0, \"ng-container\", 7);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.processedAvailableLabels.length === 0);\n  }\n}\nfunction BoLabelsGroupComponent_ul_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\", 24);\n    i0.ɵɵtemplate(1, BoLabelsGroupComponent_ul_10_li_1_Template, 6, 1, \"li\", 25)(2, BoLabelsGroupComponent_ul_10_ng_container_2_Template, 3, 4, \"ng-container\", 26)(3, BoLabelsGroupComponent_ul_10_ng_template_3_Template, 1, 1, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const noItems_r13 = i0.ɵɵreference(4);\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(4, _c1, ctx_r3.processedDropdownHeight));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isNewBtnVisible(ctx_r3.searchText));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.processedAvailableLabels.length > 0)(\"ngIfElse\", noItems_r13);\n  }\n}\nexport class BoLabelsGroupComponent {\n  set labels(value) {\n    if (value) {\n      this._labels = value;\n      this.availableLabels.next(value);\n    }\n  }\n  get labels() {\n    return this._labels;\n  }\n  set selected(value) {\n    if (value) {\n      this.selectedLabels.next(value);\n    }\n  }\n  constructor(elementRef, labelsService) {\n    this.elementRef = elementRef;\n    this.labelsService = labelsService;\n    this.isCreateNewLabelAllowed = true;\n    this.isEditDisabled = false;\n    this.select = new EventEmitter();\n    this.searchText = null;\n    this.isEditable = false;\n    this.processedAvailableLabels = [];\n    this.processedSelectedLabels = [];\n    this.processedDropdownHeight = 'auto';\n    this.dropdownStatus = {\n      isopen: false\n    };\n    this.availableLabels = new BehaviorSubject([]);\n    this.selectedLabels = new BehaviorSubject([]);\n  }\n  ngOnInit() {\n    this.availableLabels.subscribe(labels => {\n      if (labels) {\n        this.filterAvailables(labels);\n      }\n    });\n    this.selectedLabels.subscribe(selected => {\n      if (selected) {\n        this.processedSelectedLabels = selected;\n      }\n    });\n    this.setDropdownHeight();\n  }\n  onEditClick() {\n    if (this.isEditDisabled) return;\n    if (this.isEditable === false) {\n      this.createRevertSelectedArray();\n    }\n    this.setIsEditableTrue();\n    this.setInputFocus();\n  }\n  onSelectBtnClick() {\n    this.setInputFocus();\n  }\n  onSaveClick(dropdown) {\n    this.select.emit(Array.from(this.processedSelectedLabels));\n    this.clearSearch();\n    dropdown.hide();\n    this.setIsEditableFalse();\n  }\n  onCancelClick(dropdown) {\n    this.setIsEditableFalse();\n    this.clearSearch();\n    this.revertSelected();\n    this.filterAvailables(this.availableLabels.getValue());\n    dropdown.hide();\n  }\n  onDropdownItemClick(label, event) {\n    event.preventDefault();\n    this.addToSelected(label);\n    this.removeFromAvailable(label);\n    this.clearSearch();\n  }\n  onAddClick(event) {\n    event.preventDefault();\n    this.createNewLabel();\n    this.clearSearch();\n  }\n  onRemoveLabelClick(label, event) {\n    event.preventDefault();\n    this.removeFromSelected(label);\n    this.filterAvailables(this.availableLabels.getValue());\n  }\n  handleDropdownItemKeydown(label, event, labelIndex) {\n    event.preventDefault();\n    let dropdownItemArray = this.elementRef.nativeElement.querySelectorAll('.bo-labels .dropdown-item');\n    let goNext = function () {\n      if (dropdownItemArray[labelIndex + 1]) {\n        dropdownItemArray[labelIndex + 1].focus();\n      } else {\n        dropdownItemArray[0].focus();\n      }\n    };\n    let goPrev = function () {\n      if (dropdownItemArray[labelIndex - 1]) {\n        dropdownItemArray[labelIndex - 1].focus();\n      } else {\n        dropdownItemArray[dropdownItemArray.length - 1].focus();\n      }\n    };\n    if (event.key === 'Enter') {\n      this.onDropdownItemClick(label, event);\n      goNext();\n    } else if (event.key === 'ArrowDown') {\n      goNext();\n    } else if (event.key === 'ArrowUp') {\n      goPrev();\n    }\n  }\n  handleInputKeydown(dropdown, event) {\n    dropdown.show();\n    if (event.key === 'ArrowDown') {\n      setTimeout(() => {\n        let dropdownItem = this.elementRef.nativeElement.querySelector('.dropdown-item');\n        if (dropdownItem) {\n          dropdownItem.focus();\n        }\n      }, 0);\n    } else if (event.key === 'Enter') {\n      event.preventDefault();\n    }\n  }\n  handleAddItemKeydown(event) {\n    if (event.key === 'Enter') {\n      this.onAddClick(event);\n      this.setInputFocus();\n    }\n  }\n  filterAvailables(labels) {\n    if (labels && this.processedSelectedLabels) {\n      this.processedAvailableLabels = labels.filter(label => {\n        return !this.processedSelectedLabels.find(el => el.title === label.title);\n      });\n    }\n  }\n  createRevertSelectedArray() {\n    this.revertedSelectedArray = [...this.selectedLabels.getValue()];\n  }\n  revertSelected() {\n    this.selected = [...this.revertedSelectedArray];\n  }\n  addToSelected(label) {\n    if (label) {\n      this.selectedLabels.next(this.selectedLabels.getValue().concat([label]));\n    }\n  }\n  removeFromAvailable(label) {\n    let availableLabelsArr = this.processedAvailableLabels;\n    if (label) {\n      let labelIndex = availableLabelsArr.indexOf(label);\n      if (labelIndex > -1) {\n        availableLabelsArr.splice(labelIndex, 1);\n      }\n    }\n  }\n  removeFromSelected(label) {\n    let labelsArray = this.selectedLabels.getValue();\n    let labelToRemove = labelsArray.find(x => x.title === label.title);\n    let indexLabelToRemove = labelsArray.indexOf(labelToRemove);\n    if (indexLabelToRemove !== -1) {\n      labelsArray.splice(indexLabelToRemove, 1);\n    }\n  }\n  createNewLabel() {\n    this.labelsService.addLabel({\n      groupId: this.groupId,\n      title: this.searchText\n    }).pipe(take(1)).subscribe(label => {\n      this.addToSelected({\n        id: label.id,\n        title: label.title\n      });\n      this.labels = [...this.labels, label];\n    });\n  }\n  setIsEditableTrue() {\n    this.isEditable = true;\n  }\n  setIsEditableFalse() {\n    this.isEditable = false;\n  }\n  setInputFocus() {\n    let onElement = this.elementRef.nativeElement.querySelector('#textInput');\n    setTimeout(() => onElement.focus(), 0);\n  }\n  getLabelClass(item) {\n    return tagClassMap[item.group];\n  }\n  setDropdownHeight() {\n    let value = this.dropdownHeight;\n    if (typeof value === 'number') {\n      this.processedDropdownHeight = value + 'px';\n    }\n  }\n  changeDropdownStatus(value) {\n    this.dropdownStatus.isopen = value;\n  }\n  clearSearch() {\n    this.searchText = '';\n  }\n  isNewBtnVisible(searchText) {\n    return this.isCreateNewLabelAllowed && searchText && !this.processedAvailableLabels.find(label => label.title === searchText) && !this.processedSelectedLabels.find(label => label.title === searchText);\n  }\n  static {\n    this.ɵfac = function BoLabelsGroupComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || BoLabelsGroupComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.LabelsService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: BoLabelsGroupComponent,\n      selectors: [[\"bo-labels-group\"]],\n      viewQuery: function BoLabelsGroupComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.textInput = _t.first);\n        }\n      },\n      inputs: {\n        dropdownHeight: \"dropdownHeight\",\n        isCreateNewLabelAllowed: \"isCreateNewLabelAllowed\",\n        isEditDisabled: \"isEditDisabled\",\n        groupId: \"groupId\",\n        labels: \"labels\",\n        selected: \"selected\"\n      },\n      outputs: {\n        select: \"select\"\n      },\n      decls: 16,\n      vars: 16,\n      consts: [[\"dropdown\", \"bs-dropdown\"], [\"noItems\", \"\"], [\"noMatches\", \"\"], [1, \"bo-labels\"], [\"dropdown\", \"\", 1, \"bo-labels__inner\", 3, \"click\", \"isOpenChange\", \"isDisabled\", \"isOpen\"], [1, \"bo-labels__selected\"], [1, \"bo-labels__selected-list\", \"label-list\"], [4, \"ngIf\"], [\"trimValue\", \"\", \"type\", \"text\", \"id\", \"textInput\", \"autocomplete\", \"off\", 1, \"bo-labels__input\", 3, \"ngModelChange\", \"keydown\", \"disabled\", \"placeholder\", \"ngModel\"], [\"type\", \"button\", \"class\", \"bo-labels__btn bo-labels__btn--edit btn btn-default\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"bo-labels__btn bo-labels__btn--select btn btn-xs btn-default dropdown-toggle\", \"dropdownToggle\", \"\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"bo-labels__dropdown dropdown-menu\", \"role\", \"menu\", \"id\", \"dropdownMenu\", 3, \"ngStyle\", 4, \"dropdownMenu\"], [1, \"bo-labels__actions\"], [\"type\", \"button\", 1, \"bo-labels__btn\", \"btn\", \"btn-xs\", \"btn-default\", 3, \"click\"], [1, \"icon-checkmark3\"], [1, \"icon-cross2\"], [4, \"ngFor\", \"ngForOf\"], [1, \"editable-item\"], [1, \"editable-item__label\", \"label\", \"label-xs\", \"label-striped\", 3, \"ngClass\"], [1, \"editable-item__remove\", 3, \"click\"], [\"type\", \"button\", 1, \"bo-labels__btn\", \"bo-labels__btn--edit\", \"btn\", \"btn-default\"], [1, \"icon-pencil4\"], [\"type\", \"button\", \"dropdownToggle\", \"\", 1, \"bo-labels__btn\", \"bo-labels__btn--select\", \"btn\", \"btn-xs\", \"btn-default\", \"dropdown-toggle\", 3, \"click\"], [1, \"caret\"], [\"role\", \"menu\", \"id\", \"dropdownMenu\", 1, \"bo-labels__dropdown\", \"dropdown-menu\", 3, \"ngStyle\"], [\"role\", \"menuitem\", 4, \"ngIf\"], [4, \"ngIf\", \"ngIfElse\"], [\"role\", \"menuitem\"], [\"tabindex\", \"-1\", 1, \"bo-labels__add\", \"dropdown-item\", 3, \"keydown\", \"click\"], [1, \"label\", \"label-xs\", \"label-striped\"], [1, \"ml-10\"], [\"tabindex\", \"-1\", 1, \"dropdown-item\", 3, \"keydown\", \"click\"], [1, \"label\", \"label-xs\", \"label-striped\", 3, \"ngClass\"], [\"tabindex\", \"-1\", 1, \"bo-labels__noitems\", \"text-muted\"], [1, \"bo-labels__noitems\", \"text-muted\"]],\n      template: function BoLabelsGroupComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4, 0);\n          i0.ɵɵlistener(\"click\", function BoLabelsGroupComponent_Template_div_click_1_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onEditClick());\n          })(\"isOpenChange\", function BoLabelsGroupComponent_Template_div_isOpenChange_1_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.changeDropdownStatus($event));\n          });\n          i0.ɵɵelementStart(3, \"div\", 5)(4, \"ul\", 6);\n          i0.ɵɵtemplate(5, BoLabelsGroupComponent_ng_container_5_Template, 2, 1, \"ng-container\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"input\", 8);\n          i0.ɵɵpipe(7, \"translate\");\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function BoLabelsGroupComponent_Template_input_ngModelChange_6_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchText, $event) || (ctx.searchText = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"keydown\", function BoLabelsGroupComponent_Template_input_keydown_6_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            const dropdown_r5 = i0.ɵɵreference(2);\n            return i0.ɵɵresetView(ctx.handleInputKeydown(dropdown_r5, $event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(8, BoLabelsGroupComponent_button_8_Template, 2, 0, \"button\", 9)(9, BoLabelsGroupComponent_button_9_Template, 2, 0, \"button\", 10)(10, BoLabelsGroupComponent_ul_10_Template, 5, 6, \"ul\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 12)(12, \"button\", 13);\n          i0.ɵɵlistener(\"click\", function BoLabelsGroupComponent_Template_button_click_12_listener() {\n            i0.ɵɵrestoreView(_r1);\n            const dropdown_r5 = i0.ɵɵreference(2);\n            return i0.ɵɵresetView(ctx.onSaveClick(dropdown_r5));\n          });\n          i0.ɵɵelement(13, \"span\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"button\", 13);\n          i0.ɵɵlistener(\"click\", function BoLabelsGroupComponent_Template_button_click_14_listener() {\n            i0.ɵɵrestoreView(_r1);\n            const dropdown_r5 = i0.ɵɵreference(2);\n            return i0.ɵɵresetView(ctx.onCancelClick(dropdown_r5));\n          });\n          i0.ɵɵelement(15, \"span\", 15);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"bo-labels--editable\", ctx.isEditable);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"disabled\", ctx.isEditDisabled);\n          i0.ɵɵproperty(\"isDisabled\", ctx.isEditDisabled)(\"isOpen\", ctx.dropdownStatus.isopen);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.processedSelectedLabels.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"show\", ctx.processedSelectedLabels.length === 0);\n          i0.ɵɵpropertyInterpolate(\"placeholder\", ctx.processedSelectedLabels.length === 0 ? i0.ɵɵpipeBind1(7, 14, \"ENTITY_SETUP.GAMES.MODALS.noLabels\") : \"\");\n          i0.ɵɵproperty(\"disabled\", ctx.isEditDisabled);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchText);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isEditable);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isEditable);\n        }\n      },\n      dependencies: [i2.NgClass, i2.NgForOf, i2.NgIf, i2.NgStyle, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgModel, i4.BsDropdownMenuDirective, i4.BsDropdownToggleDirective, i4.BsDropdownDirective, i5.TrimInputValueComponent, i6.TranslatePipe, i7.BoLabelsGroupPipe],\n      styles: [\"[_nghost-%COMP%] {\\n  display: flex;\\n}\\n\\n.bo-labels[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: inline-block;\\n  min-height: 28px;\\n  min-width: 180px;\\n  box-sizing: border-box;\\n}\\n.bo-labels__selected[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  height: 100%;\\n  width: 100%;\\n}\\n.bo-labels__input[_ngcontent-%COMP%] {\\n  display: none;\\n  width: 100%;\\n  padding: 3px;\\n  box-shadow: none;\\n  border: none;\\n  border-radius: 3px;\\n}\\n.bo-labels__input[_ngcontent-%COMP%]:focus {\\n  outline: none !important;\\n  box-shadow: none !important;\\n}\\n.bo-labels__input[_ngcontent-%COMP%]:disabled {\\n  background-color: #fff !important;\\n}\\n.bo-labels__input[_ngcontent-%COMP%]   .show[_ngcontent-%COMP%] {\\n  display: block;\\n}\\n.bo-labels__btn[_ngcontent-%COMP%] {\\n  width: 38px;\\n  padding: 0;\\n}\\n.bo-labels__btn--edit[_ngcontent-%COMP%] {\\n  display: none;\\n  position: absolute;\\n  top: 0;\\n  right: -1px;\\n  height: 100%;\\n}\\n.bo-labels__btn--select[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  right: -1px;\\n  height: 100%;\\n}\\n.bo-labels__btn[_ngcontent-%COMP%]:focus {\\n  outline: none !important;\\n  box-shadow: none !important;\\n}\\n.bo-labels__selected-list[_ngcontent-%COMP%] {\\n  display: block;\\n  width: 100%;\\n  margin: 0;\\n  padding: 0;\\n}\\n.bo-labels__nolabels[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  width: 100%;\\n  height: 30px;\\n  margin: -1px 0;\\n  padding-left: 12px;\\n  border-radius: 3px;\\n}\\n.bo-labels__noitems[_ngcontent-%COMP%] {\\n  padding-left: 3px;\\n}\\n.bo-labels__dropdown[_ngcontent-%COMP%] {\\n  top: calc(100% - 3px);\\n  width: calc(100% + 1px);\\n  height: auto;\\n  overflow-y: auto;\\n}\\n.bo-labels__dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%] {\\n  padding-left: 3px;\\n  padding-right: 3px;\\n}\\n.bo-labels__dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]:hover, .bo-labels__dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]:focus {\\n  background-color: #e1e1e1;\\n}\\n.bo-labels__add[_ngcontent-%COMP%] {\\n  text-transform: uppercase;\\n  font-weight: 600;\\n  font-size: 10px;\\n  line-height: 21px;\\n  background-color: #e1e1e1;\\n}\\n.bo-labels__actions[_ngcontent-%COMP%] {\\n  display: none;\\n  position: absolute;\\n  right: -1px;\\n  top: 100%;\\n  z-index: 2;\\n  margin-top: -1px;\\n  padding: 3px;\\n  border: 1px solid #ddd;\\n  border-radius: 3px;\\n  border-top-left-radius: 0;\\n  background-color: #fff;\\n}\\n.bo-labels__inner[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding-right: 38px;\\n  border: 1px solid transparent;\\n  border-radius: 3px;\\n}\\n.bo-labels__inner[_ngcontent-%COMP%]:not(.disabled):hover {\\n  border-color: #ddd;\\n}\\n.bo-labels__inner[_ngcontent-%COMP%]:not(.disabled):hover   .bo-labels__btn--edit[_ngcontent-%COMP%] {\\n  display: block;\\n}\\n.bo-labels__inner[_ngcontent-%COMP%]:not(.disabled):hover   .bo-labels__nolabels[_ngcontent-%COMP%] {\\n  border-color: transparent;\\n}\\n.bo-labels__nomatches[_ngcontent-%COMP%] {\\n  padding-left: 3px;\\n  padding-right: 3px;\\n}\\n.bo-labels--editable[_ngcontent-%COMP%] {\\n  display: block;\\n  width: 100%;\\n}\\n.bo-labels--editable[_ngcontent-%COMP%]   .bo-labels__inner[_ngcontent-%COMP%] {\\n  border-color: #ddd;\\n}\\n.bo-labels--editable[_ngcontent-%COMP%]   .bo-labels__input[_ngcontent-%COMP%] {\\n  display: block;\\n}\\n.bo-labels--editable[_ngcontent-%COMP%]   .bo-labels__actions[_ngcontent-%COMP%] {\\n  display: block;\\n}\\n.bo-labels--editable[_ngcontent-%COMP%]   .bo-labels__nolabels[_ngcontent-%COMP%] {\\n  border-color: transparent;\\n}\\n.bo-labels--editable[_ngcontent-%COMP%]   .editable-item__remove[_ngcontent-%COMP%] {\\n  visibility: visible;\\n  margin-left: 0;\\n}\\n.bo-labels--editable[_ngcontent-%COMP%]   .editable-item__remove[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  top: 0;\\n}\\n.bo-labels--editable[_ngcontent-%COMP%]   .editable-item[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 0 2px 1px rgba(38, 50, 56, 0.31);\\n}\\n.bo-labels--editable[_ngcontent-%COMP%]   .editable-item[_ngcontent-%COMP%]:hover   .editable-item__remove[_ngcontent-%COMP%] {\\n  color: #333;\\n}\\n\\n.editable-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  transition: box-shadow 0.3s ease-in-out;\\n}\\n.editable-item__remove[_ngcontent-%COMP%] {\\n  visibility: hidden;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 22px;\\n  height: 22px;\\n  margin-left: -21px;\\n  color: #9b9b9b;\\n  background-color: #fafafa;\\n  border: none;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.editable-item__remove[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n}\\n.editable-item__label[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 1;\\n  font-size: 11px;\\n}\\n\\n.label-list[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding: 0;\\n  list-style: none;\\n}\\n.label-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  float: left;\\n  margin: 0;\\n  padding: 3px;\\n}\\n.label-list[_ngcontent-%COMP%]:after {\\n  content: \\\"\\\";\\n  display: table;\\n  clear: both;\\n}\\n\\n.grid-header__filter[_nghost-%COMP%]   .bo-labels__input[_ngcontent-%COMP%], .grid-header__filter   [_nghost-%COMP%]   .bo-labels__input[_ngcontent-%COMP%] {\\n  min-height: 30px !important;\\n}\\n.grid-header__filter[_nghost-%COMP%]   .bo-labels__inner[_ngcontent-%COMP%], .grid-header__filter   [_nghost-%COMP%]   .bo-labels__inner[_ngcontent-%COMP%] {\\n  border-color: #ddd;\\n}\\n.grid-header__filter[_nghost-%COMP%]   .label-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%], .grid-header__filter   [_nghost-%COMP%]   .label-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  padding-top: 4px;\\n  padding-bottom: 4px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "BehaviorSubject", "take", "tagClassMap", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "BoLabelsGroupComponent_ng_container_5_li_1_Template_button_click_4_listener", "$event", "label_r3", "ɵɵrestoreView", "_r2", "$implicit", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "onRemoveLabelClick", "ɵɵelement", "ɵɵadvance", "ɵɵproperty", "getLabelClass", "ɵɵtextInterpolate1", "title", "ɵɵelementContainerStart", "ɵɵtemplate", "BoLabelsGroupComponent_ng_container_5_li_1_Template", "processedSelectedLabels", "BoLabelsGroupComponent_button_9_Template_button_click_0_listener", "_r6", "onSelectBtnClick", "BoLabelsGroupComponent_ul_10_li_1_Template_a_keydown_1_listener", "_r7", "handleAddItemKeydown", "BoLabelsGroupComponent_ul_10_li_1_Template_a_click_1_listener", "onAddClick", "searchText", "BoLabelsGroupComponent_ul_10_ng_container_2_ng_container_1_ng_container_2_Template_a_keydown_1_listener", "_r8", "ctx_r8", "label_r10", "labelIndex_r11", "index", "handleDropdownItemKeydown", "BoLabelsGroupComponent_ul_10_ng_container_2_ng_container_1_ng_container_2_Template_a_click_1_listener", "onDropdownItemClick", "BoLabelsGroupComponent_ul_10_ng_container_2_ng_container_1_ng_template_3_ng_container_0_Template", "isCreateNewLabelAllowed", "BoLabelsGroupComponent_ul_10_ng_container_2_ng_container_1_ng_container_2_Template", "BoLabelsGroupComponent_ul_10_ng_container_2_ng_container_1_ng_template_3_Template", "ɵɵtemplateRefExtractor", "noMatches_r12", "BoLabelsGroupComponent_ul_10_ng_container_2_ng_container_1_Template", "ɵɵpipeBind2", "processedAvailableLabels", "BoLabelsGroupComponent_ul_10_ng_template_3_ng_container_0_Template", "length", "BoLabelsGroupComponent_ul_10_li_1_Template", "BoLabelsGroupComponent_ul_10_ng_container_2_Template", "BoLabelsGroupComponent_ul_10_ng_template_3_Template", "ɵɵpureFunction1", "_c1", "processedDropdownHeight", "isNewBtnVisible", "noItems_r13", "BoLabelsGroupComponent", "labels", "value", "_labels", "availableLabels", "next", "selected", "<PERSON><PERSON><PERSON><PERSON>", "constructor", "elementRef", "labelsService", "isEditDisabled", "select", "isEditable", "dropdownStatus", "isopen", "ngOnInit", "subscribe", "filterAvailables", "setDropdownHeight", "onEditClick", "createRevertSelectedArray", "setIsEditableTrue", "setInputFocus", "onSaveClick", "dropdown", "emit", "Array", "from", "clearSearch", "hide", "setIsEditableFalse", "onCancelClick", "revertSelected", "getValue", "label", "event", "preventDefault", "addToSelected", "removeFromAvailable", "createNewLabel", "removeFromSelected", "labelIndex", "dropdownItemArray", "nativeElement", "querySelectorAll", "goNext", "focus", "goPrev", "key", "handleInputKeydown", "show", "setTimeout", "dropdownItem", "querySelector", "filter", "find", "el", "revertedSelectedArray", "concat", "availableLabelsArr", "indexOf", "splice", "labelsArray", "labelToRemove", "x", "indexLabelToRemove", "addLabel", "groupId", "pipe", "id", "onElement", "item", "group", "dropdownHeight", "changeDropdownStatus", "ɵɵdirectiveInject", "ElementRef", "i1", "LabelsService", "selectors", "viewQuery", "BoLabelsGroupComponent_Query", "rf", "ctx", "BoLabelsGroupComponent_Template_div_click_1_listener", "_r1", "BoLabelsGroupComponent_Template_div_isOpenChange_1_listener", "BoLabelsGroupComponent_ng_container_5_Template", "ɵɵtwoWayListener", "BoLabelsGroupComponent_Template_input_ngModelChange_6_listener", "ɵɵtwoWayBindingSet", "BoLabelsGroupComponent_Template_input_keydown_6_listener", "dropdown_r5", "ɵɵreference", "BoLabelsGroupComponent_button_8_Template", "BoLabelsGroupComponent_button_9_Template", "BoLabelsGroupComponent_ul_10_Template", "BoLabelsGroupComponent_Template_button_click_12_listener", "BoLabelsGroupComponent_Template_button_click_14_listener", "ɵɵclassProp", "ɵɵpropertyInterpolate", "ɵɵpipeBind1", "ɵɵtwoWayProperty"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/common/components/bo-labels-group/bo-labels-group.component.ts", "/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/common/components/bo-labels-group/bo-labels-group.component.html"], "sourcesContent": ["import { Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';\nimport { BehaviorSubject } from 'rxjs';\nimport { take } from 'rxjs/operators';\nimport { tagClassMap } from '../../../app.constants';\nimport { LabelsService } from '../../services/labels.service';\nimport { Label } from '../../typings';\n\n\n@Component({\n  selector: 'bo-labels-group',\n  templateUrl: 'bo-labels-group.component.html',\n  styleUrls: ['./bo-labels-group.component.scss'],\n})\n\nexport class BoLabelsGroupComponent implements OnInit {\n\n  @ViewChild('textInput') textInput: ElementRef;\n  @Input() dropdownHeight: any;\n  @Input() isCreateNewLabelAllowed = true;\n  @Input() isEditDisabled = false;\n  @Input() groupId: string;\n\n  @Output() select: EventEmitter<Label[]> = new EventEmitter();\n\n  public searchText: string = null;\n  public isEditable = false;\n  public processedAvailableLabels = [];\n  public processedSelectedLabels = [];\n  public processedDropdownHeight = 'auto';\n  public dropdownStatus: { isopen: boolean; } = { isopen: false };\n\n  private availableLabels = new BehaviorSubject<Label[]>([]);\n  private selectedLabels = new BehaviorSubject<Label[]>([]);\n  private revertedSelectedArray: Label[];\n\n  @Input()\n  set labels(value: Label[]) {\n    if (value) {\n      this._labels = value;\n      this.availableLabels.next(value);\n    }\n  }\n\n  get labels(): Label[] {\n    return this._labels;\n  }\n\n  @Input()\n  set selected(value: Label[]) {\n    if (value) {\n      this.selectedLabels.next(value);\n    }\n  }\n\n  private _labels: Label[];\n\n  constructor(private elementRef: ElementRef,\n    private labelsService: LabelsService,\n  ) {\n  }\n\n  ngOnInit(): void {\n    this.availableLabels.subscribe((labels) => {\n      if (labels) {\n        this.filterAvailables(labels);\n      }\n    });\n    this.selectedLabels.subscribe((selected) => {\n      if (selected) {\n        this.processedSelectedLabels = selected;\n      }\n    });\n    this.setDropdownHeight();\n  }\n\n  onEditClick() {\n    if (this.isEditDisabled) return;\n\n    if (this.isEditable === false) {\n      this.createRevertSelectedArray();\n    }\n    this.setIsEditableTrue();\n    this.setInputFocus();\n  }\n\n  onSelectBtnClick() {\n    this.setInputFocus();\n  }\n\n  onSaveClick(dropdown) {\n    this.select.emit(Array.from(this.processedSelectedLabels));\n    this.clearSearch();\n    dropdown.hide();\n    this.setIsEditableFalse();\n  }\n\n  onCancelClick(dropdown) {\n    this.setIsEditableFalse();\n    this.clearSearch();\n    this.revertSelected();\n    this.filterAvailables(this.availableLabels.getValue());\n    dropdown.hide();\n  }\n\n  onDropdownItemClick(label, event) {\n    event.preventDefault();\n    this.addToSelected(label);\n    this.removeFromAvailable(label);\n    this.clearSearch();\n  }\n\n  onAddClick(event) {\n    event.preventDefault();\n    this.createNewLabel();\n    this.clearSearch();\n  }\n\n  onRemoveLabelClick(label, event) {\n    event.preventDefault();\n    this.removeFromSelected(label);\n    this.filterAvailables(this.availableLabels.getValue());\n  }\n\n  handleDropdownItemKeydown(label: Label, event: KeyboardEvent, labelIndex): void {\n    event.preventDefault();\n    let dropdownItemArray = this.elementRef.nativeElement.querySelectorAll('.bo-labels .dropdown-item') as any;\n    let goNext = function () {\n      if (dropdownItemArray[labelIndex + 1]) {\n        dropdownItemArray[labelIndex + 1].focus();\n      } else {\n        dropdownItemArray[0].focus();\n      }\n    };\n    let goPrev = function () {\n      if (dropdownItemArray[labelIndex - 1]) {\n        dropdownItemArray[labelIndex - 1].focus();\n      } else {\n        dropdownItemArray[dropdownItemArray.length - 1].focus();\n      }\n    };\n    if (event.key === 'Enter') {\n      this.onDropdownItemClick(label, event);\n      goNext();\n    } else if (event.key === 'ArrowDown') {\n      goNext();\n    } else if (event.key === 'ArrowUp') {\n      goPrev();\n    }\n  }\n\n  handleInputKeydown(dropdown, event: KeyboardEvent) {\n    dropdown.show();\n    if (event.key === 'ArrowDown') {\n      setTimeout(() => {\n        let dropdownItem = this.elementRef.nativeElement.querySelector('.dropdown-item') as any;\n        if (dropdownItem) {\n          dropdownItem.focus();\n        }\n      }, 0);\n    } else if (event.key === 'Enter') {\n      event.preventDefault();\n    }\n  }\n\n  handleAddItemKeydown(event) {\n    if (event.key === 'Enter') {\n      this.onAddClick(event);\n      this.setInputFocus();\n    }\n  }\n\n  filterAvailables(labels) {\n    if (labels && this.processedSelectedLabels) {\n      this.processedAvailableLabels = labels.filter(label => {\n        return !this.processedSelectedLabels.find(el => el.title === label.title);\n      });\n    }\n  }\n\n  createRevertSelectedArray() {\n    this.revertedSelectedArray = [...this.selectedLabels.getValue()];\n  }\n\n  revertSelected() {\n    this.selected = [...this.revertedSelectedArray];\n  }\n\n  addToSelected(label) {\n    if (label) {\n      this.selectedLabels.next(this.selectedLabels.getValue().concat([label]));\n    }\n  }\n\n  removeFromAvailable(label) {\n    let availableLabelsArr = this.processedAvailableLabels;\n    if (label) {\n      let labelIndex = availableLabelsArr.indexOf(label);\n      if (labelIndex > -1) {\n        availableLabelsArr.splice(labelIndex, 1);\n      }\n    }\n  }\n\n  removeFromSelected(label) {\n    let labelsArray = this.selectedLabels.getValue();\n    let labelToRemove = labelsArray.find(x => x.title === label.title);\n    let indexLabelToRemove = labelsArray.indexOf(labelToRemove);\n    if (indexLabelToRemove !== -1) {\n      labelsArray.splice(indexLabelToRemove, 1);\n    }\n  }\n\n  createNewLabel() {\n    this.labelsService.addLabel({ groupId: this.groupId, title: this.searchText })\n      .pipe(\n        take(1),\n      )\n      .subscribe(\n        (label: Label) => {\n          this.addToSelected({ id: label.id, title: label.title });\n          this.labels = [...this.labels, label];\n        }\n      );\n  }\n\n  setIsEditableTrue() {\n    this.isEditable = true;\n  }\n\n  setIsEditableFalse() {\n    this.isEditable = false;\n  }\n\n  setInputFocus() {\n    let onElement = this.elementRef.nativeElement.querySelector('#textInput') as any;\n    setTimeout(() => onElement.focus(), 0);\n  }\n\n  getLabelClass(item: { group: string; }): string {\n    return tagClassMap[item.group];\n  }\n\n  setDropdownHeight() {\n    let value = this.dropdownHeight;\n    if (typeof (value) === 'number') {\n      this.processedDropdownHeight = value + 'px';\n    }\n  }\n\n  changeDropdownStatus(value: boolean): void {\n    this.dropdownStatus.isopen = value;\n  }\n\n  clearSearch() {\n    this.searchText = '';\n  }\n\n  isNewBtnVisible(searchText: string): boolean {\n    return this.isCreateNewLabelAllowed\n      && searchText\n      && !this.processedAvailableLabels.find(label => label.title === searchText)\n      && !this.processedSelectedLabels.find(label => label.title === searchText);\n  }\n\n}\n", "<div\n  class=\"bo-labels\"\n  [class.bo-labels--editable]=\"isEditable\">\n  <div class=\"bo-labels__inner\"\n       [class.disabled]=\"isEditDisabled\"\n       [isDisabled]=\"isEditDisabled\"\n       dropdown\n       #dropdown=\"bs-dropdown\"\n       [isOpen]=\"dropdownStatus.isopen\"\n       (click)=\"onEditClick()\"\n       (isOpenChange)=\"changeDropdownStatus($event)\">\n    <div class=\"bo-labels__selected\">\n      <ul class=\"bo-labels__selected-list label-list\">\n        <ng-container *ngIf=\"processedSelectedLabels.length > 0\">\n          <li *ngFor=\"let label of processedSelectedLabels\">\n            <div class=\"editable-item\">\n              <span\n                class=\"editable-item__label label label-xs label-striped\"\n                [ngClass]=\"getLabelClass(label)\">\n                {{label.title}}\n              </span>\n              <button\n                class=\"editable-item__remove\"\n                (click)=\"onRemoveLabelClick(label, $event)\">\n                <i class=\"icon-cross2\"></i>\n              </button>\n            </div>\n          </li>\n        </ng-container>\n      </ul>\n    </div>\n    <input\n      trimValue\n      type=\"text\"\n      class=\"bo-labels__input\"\n      [disabled]=\"isEditDisabled\"\n      [class.show]=\"processedSelectedLabels.length === 0\"\n      placeholder=\"{{ processedSelectedLabels.length === 0 ? ('ENTITY_SETUP.GAMES.MODALS.noLabels' | translate) : '' }}\"\n      id=\"textInput\"\n      autocomplete=\"off\"\n      [(ngModel)]=\"searchText\"\n      (keydown)=\"handleInputKeydown(dropdown, $event)\">\n    <button\n      type=\"button\"\n      class=\"bo-labels__btn bo-labels__btn--edit btn btn-default\"\n      *ngIf=\"!isEditable\">\n      <span class=\"icon-pencil4\"></span>\n    </button>\n    <button\n      type=\"button\"\n      class=\"bo-labels__btn bo-labels__btn--select btn btn-xs btn-default dropdown-toggle\"\n      *ngIf=\"isEditable\"\n      dropdownToggle\n      (click)=\"onSelectBtnClick()\">\n      <span class=\"caret\"></span>\n    </button>\n    <ul\n      class=\"bo-labels__dropdown dropdown-menu\"\n      role=\"menu\"\n      *dropdownMenu\n      id=\"dropdownMenu\"\n      [ngStyle]=\"{'max-height': processedDropdownHeight}\">\n      <li role=\"menuitem\" *ngIf=\"isNewBtnVisible(searchText)\">\n        <a\n          class=\"bo-labels__add dropdown-item\"\n          tabindex=\"-1\"\n          (keydown)=\"handleAddItemKeydown($event)\"\n          (click)=\"onAddClick($event)\">\n          <span class=\"label label-xs label-striped\">\n            {{searchText}}\n          </span>\n          <span class=\"ml-10\">Add new label</span>\n        </a>\n      </li>\n      <ng-container *ngIf=\"processedAvailableLabels.length > 0; else noItems\">\n        <ng-container *ngFor=\"let label of processedAvailableLabels | boLabelsGroupFilter : searchText;\n          let labelIndex = index\">\n          <li role=\"menuitem\">\n            <ng-container *ngIf=\"label !== -1; else noMatches\">\n              <a\n                class=\"dropdown-item\"\n                tabindex=\"-1\"\n                (keydown)=\"handleDropdownItemKeydown(label, $event, labelIndex)\"\n                (click)=\"onDropdownItemClick(label, $event)\">\n            <span\n              class=\"label label-xs label-striped\"\n              [ngClass]=\"getLabelClass(label)\">\n              {{label.title}}\n            </span>\n              </a>\n            </ng-container>\n          </li>\n          <ng-template #noMatches>\n            <ng-container *ngIf=\"label === -1 && isCreateNewLabelAllowed === false\">\n              <li class=\"bo-labels__noitems text-muted\" tabindex=\"-1\">\n                <span>No matches...</span>\n              </li>\n            </ng-container>\n          </ng-template>\n        </ng-container>\n      </ng-container>\n      <ng-template #noItems>\n        <ng-container *ngIf=\"processedAvailableLabels.length === 0\">\n          <li class=\"bo-labels__noitems text-muted\">\n            <span>No items...</span>\n          </li>\n        </ng-container>\n      </ng-template>\n    </ul>\n  </div>\n  <div class=\"bo-labels__actions\">\n    <button\n      type=\"button\"\n      class=\"bo-labels__btn btn btn-xs btn-default\"\n      (click)=\"onSaveClick(dropdown)\">\n      <span class=\"icon-checkmark3\"></span>\n    </button>\n    <button\n      type=\"button\"\n      class=\"bo-labels__btn btn btn-xs btn-default\"\n      (click)=\"onCancelClick(dropdown)\">\n      <span class=\"icon-cross2\"></span>\n    </button>\n  </div>\n</div>\n\n\n\n"], "mappings": "AAAA,SAAgCA,YAAY,QAA0C,eAAe;AACrG,SAASC,eAAe,QAAQ,MAAM;AACtC,SAASC,IAAI,QAAQ,gBAAgB;AACrC,SAASC,WAAW,QAAQ,wBAAwB;;;;;;;;;;;;;;;;ICatCC,EAFJ,CAAAC,cAAA,SAAkD,cACrB,eAGU;IACjCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,iBAE8C;IAA5CD,EAAA,CAAAI,UAAA,mBAAAC,4EAAAC,MAAA;MAAA,MAAAC,QAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAG,kBAAA,CAAAP,QAAA,EAAAD,MAAA,CAAiC;IAAA,EAAC;IAC3CN,EAAA,CAAAe,SAAA,YAA2B;IAGjCf,EAFI,CAAAG,YAAA,EAAS,EACL,EACH;;;;;IATCH,EAAA,CAAAgB,SAAA,GAAgC;IAAhChB,EAAA,CAAAiB,UAAA,YAAAN,MAAA,CAAAO,aAAA,CAAAX,QAAA,EAAgC;IAChCP,EAAA,CAAAgB,SAAA,EACF;IADEhB,EAAA,CAAAmB,kBAAA,MAAAZ,QAAA,CAAAa,KAAA,MACF;;;;;IAPNpB,EAAA,CAAAqB,uBAAA,GAAyD;IACvDrB,EAAA,CAAAsB,UAAA,IAAAC,mDAAA,iBAAkD;;;;;IAA5BvB,EAAA,CAAAgB,SAAA,EAA0B;IAA1BhB,EAAA,CAAAiB,UAAA,YAAAN,MAAA,CAAAa,uBAAA,CAA0B;;;;;IA4BtDxB,EAAA,CAAAC,cAAA,iBAGsB;IACpBD,EAAA,CAAAe,SAAA,eAAkC;IACpCf,EAAA,CAAAG,YAAA,EAAS;;;;;;IACTH,EAAA,CAAAC,cAAA,iBAK+B;IAA7BD,EAAA,CAAAI,UAAA,mBAAAqB,iEAAA;MAAAzB,EAAA,CAAAQ,aAAA,CAAAkB,GAAA;MAAA,MAAAf,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAgB,gBAAA,EAAkB;IAAA,EAAC;IAC5B3B,EAAA,CAAAe,SAAA,eAA2B;IAC7Bf,EAAA,CAAAG,YAAA,EAAS;;;;;;IAQLH,EADF,CAAAC,cAAA,aAAwD,YAKvB;IAA7BD,EADA,CAAAI,UAAA,qBAAAwB,gEAAAtB,MAAA;MAAAN,EAAA,CAAAQ,aAAA,CAAAqB,GAAA;MAAA,MAAAlB,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAAWF,MAAA,CAAAmB,oBAAA,CAAAxB,MAAA,CAA4B;IAAA,EAAC,mBAAAyB,8DAAAzB,MAAA;MAAAN,EAAA,CAAAQ,aAAA,CAAAqB,GAAA;MAAA,MAAAlB,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAC/BF,MAAA,CAAAqB,UAAA,CAAA1B,MAAA,CAAkB;IAAA,EAAC;IAC5BN,EAAA,CAAAC,cAAA,eAA2C;IACzCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,eAAoB;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAErCF,EAFqC,CAAAG,YAAA,EAAO,EACtC,EACD;;;;IAJCH,EAAA,CAAAgB,SAAA,GACF;IADEhB,EAAA,CAAAmB,kBAAA,MAAAR,MAAA,CAAAsB,UAAA,MACF;;;;;;IAQEjC,EAAA,CAAAqB,uBAAA,GAAmD;IACjDrB,EAAA,CAAAC,cAAA,YAI+C;IAA7CD,EADA,CAAAI,UAAA,qBAAA8B,wGAAA5B,MAAA;MAAAN,EAAA,CAAAQ,aAAA,CAAA2B,GAAA;MAAA,MAAAC,MAAA,GAAApC,EAAA,CAAAY,aAAA;MAAA,MAAAyB,SAAA,GAAAD,MAAA,CAAA1B,SAAA;MAAA,MAAA4B,cAAA,GAAAF,MAAA,CAAAG,KAAA;MAAA,MAAA5B,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAAWF,MAAA,CAAA6B,yBAAA,CAAAH,SAAA,EAAA/B,MAAA,EAAAgC,cAAA,CAAoD;IAAA,EAAC,mBAAAG,sGAAAnC,MAAA;MAAAN,EAAA,CAAAQ,aAAA,CAAA2B,GAAA;MAAA,MAAAE,SAAA,GAAArC,EAAA,CAAAY,aAAA,GAAAF,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CACvDF,MAAA,CAAA+B,mBAAA,CAAAL,SAAA,EAAA/B,MAAA,CAAkC;IAAA,EAAC;IAChDN,EAAA,CAAAC,cAAA,eAEmC;IACjCD,EAAA,CAAAE,MAAA,GACF;IACEF,EADF,CAAAG,YAAA,EAAO,EACD;;;;;;IAHJH,EAAA,CAAAgB,SAAA,GAAgC;IAAhChB,EAAA,CAAAiB,UAAA,YAAAN,MAAA,CAAAO,aAAA,CAAAmB,SAAA,EAAgC;IAChCrC,EAAA,CAAAgB,SAAA,EACF;IADEhB,EAAA,CAAAmB,kBAAA,MAAAkB,SAAA,CAAAjB,KAAA,MACF;;;;;IAKApB,EAAA,CAAAqB,uBAAA,GAAwE;IAEpErB,EADF,CAAAC,cAAA,aAAwD,WAChD;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IACrBF,EADqB,CAAAG,YAAA,EAAO,EACvB;;;;;;IAHPH,EAAA,CAAAsB,UAAA,IAAAqB,gGAAA,0BAAwE;;;;;IAAzD3C,EAAA,CAAAiB,UAAA,SAAAoB,SAAA,WAAA1B,MAAA,CAAAiC,uBAAA,WAAuD;;;;;IAlB1E5C,EAAA,CAAAqB,uBAAA,GAC0B;IACxBrB,EAAA,CAAAC,cAAA,aAAoB;IAClBD,EAAA,CAAAsB,UAAA,IAAAuB,kFAAA,2BAAmD;IAarD7C,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAsB,UAAA,IAAAwB,iFAAA,gCAAA9C,EAAA,CAAA+C,sBAAA,CAAwB;;;;;;IAdP/C,EAAA,CAAAgB,SAAA,GAAoB;IAAAhB,EAApB,CAAAiB,UAAA,SAAAoB,SAAA,QAAoB,aAAAW,aAAA,CAAc;;;;;IAJvDhD,EAAA,CAAAqB,uBAAA,GAAwE;IACtErB,EAAA,CAAAsB,UAAA,IAAA2B,mEAAA,2BAC0B;;;;;;IADMjD,EAAA,CAAAgB,SAAA,EAC9B;IAD8BhB,EAAA,CAAAiB,UAAA,YAAAjB,EAAA,CAAAkD,WAAA,OAAAvC,MAAA,CAAAwC,wBAAA,EAAAxC,MAAA,CAAAsB,UAAA,EAC9B;;;;;IA0BFjC,EAAA,CAAAqB,uBAAA,GAA4D;IAExDrB,EADF,CAAAC,cAAA,aAA0C,WAClC;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IACnBF,EADmB,CAAAG,YAAA,EAAO,EACrB;;;;;;IAHPH,EAAA,CAAAsB,UAAA,IAAA8B,kEAAA,0BAA4D;;;;IAA7CpD,EAAA,CAAAiB,UAAA,SAAAN,MAAA,CAAAwC,wBAAA,CAAAE,MAAA,OAA2C;;;;;IA9C9DrD,EAAA,CAAAC,cAAA,aAKsD;IAwCpDD,EAvCA,CAAAsB,UAAA,IAAAgC,0CAAA,iBAAwD,IAAAC,oDAAA,2BAYgB,IAAAC,mDAAA,gCAAAxD,EAAA,CAAA+C,sBAAA,CA2BlD;IAOxB/C,EAAA,CAAAG,YAAA,EAAK;;;;;IA/CHH,EAAA,CAAAiB,UAAA,YAAAjB,EAAA,CAAAyD,eAAA,IAAAC,GAAA,EAAA/C,MAAA,CAAAgD,uBAAA,EAAmD;IAC9B3D,EAAA,CAAAgB,SAAA,EAAiC;IAAjChB,EAAA,CAAAiB,UAAA,SAAAN,MAAA,CAAAiD,eAAA,CAAAjD,MAAA,CAAAsB,UAAA,EAAiC;IAYvCjC,EAAA,CAAAgB,SAAA,EAA2C;IAAAhB,EAA3C,CAAAiB,UAAA,SAAAN,MAAA,CAAAwC,wBAAA,CAAAE,MAAA,KAA2C,aAAAQ,WAAA,CAAY;;;AD5D5E,OAAM,MAAOC,sBAAsB;EAqBjC,IACIC,MAAMA,CAACC,KAAc;IACvB,IAAIA,KAAK,EAAE;MACT,IAAI,CAACC,OAAO,GAAGD,KAAK;MACpB,IAAI,CAACE,eAAe,CAACC,IAAI,CAACH,KAAK,CAAC;IAClC;EACF;EAEA,IAAID,MAAMA,CAAA;IACR,OAAO,IAAI,CAACE,OAAO;EACrB;EAEA,IACIG,QAAQA,CAACJ,KAAc;IACzB,IAAIA,KAAK,EAAE;MACT,IAAI,CAACK,cAAc,CAACF,IAAI,CAACH,KAAK,CAAC;IACjC;EACF;EAIAM,YAAoBC,UAAsB,EAChCC,aAA4B;IADlB,KAAAD,UAAU,GAAVA,UAAU;IACpB,KAAAC,aAAa,GAAbA,aAAa;IAvCd,KAAA5B,uBAAuB,GAAG,IAAI;IAC9B,KAAA6B,cAAc,GAAG,KAAK;IAGrB,KAAAC,MAAM,GAA0B,IAAI9E,YAAY,EAAE;IAErD,KAAAqC,UAAU,GAAW,IAAI;IACzB,KAAA0C,UAAU,GAAG,KAAK;IAClB,KAAAxB,wBAAwB,GAAG,EAAE;IAC7B,KAAA3B,uBAAuB,GAAG,EAAE;IAC5B,KAAAmC,uBAAuB,GAAG,MAAM;IAChC,KAAAiB,cAAc,GAAyB;MAAEC,MAAM,EAAE;IAAK,CAAE;IAEvD,KAAAX,eAAe,GAAG,IAAIrE,eAAe,CAAU,EAAE,CAAC;IAClD,KAAAwE,cAAc,GAAG,IAAIxE,eAAe,CAAU,EAAE,CAAC;EA2BzD;EAEAiF,QAAQA,CAAA;IACN,IAAI,CAACZ,eAAe,CAACa,SAAS,CAAEhB,MAAM,IAAI;MACxC,IAAIA,MAAM,EAAE;QACV,IAAI,CAACiB,gBAAgB,CAACjB,MAAM,CAAC;MAC/B;IACF,CAAC,CAAC;IACF,IAAI,CAACM,cAAc,CAACU,SAAS,CAAEX,QAAQ,IAAI;MACzC,IAAIA,QAAQ,EAAE;QACZ,IAAI,CAAC5C,uBAAuB,GAAG4C,QAAQ;MACzC;IACF,CAAC,CAAC;IACF,IAAI,CAACa,iBAAiB,EAAE;EAC1B;EAEAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACT,cAAc,EAAE;IAEzB,IAAI,IAAI,CAACE,UAAU,KAAK,KAAK,EAAE;MAC7B,IAAI,CAACQ,yBAAyB,EAAE;IAClC;IACA,IAAI,CAACC,iBAAiB,EAAE;IACxB,IAAI,CAACC,aAAa,EAAE;EACtB;EAEA1D,gBAAgBA,CAAA;IACd,IAAI,CAAC0D,aAAa,EAAE;EACtB;EAEAC,WAAWA,CAACC,QAAQ;IAClB,IAAI,CAACb,MAAM,CAACc,IAAI,CAACC,KAAK,CAACC,IAAI,CAAC,IAAI,CAAClE,uBAAuB,CAAC,CAAC;IAC1D,IAAI,CAACmE,WAAW,EAAE;IAClBJ,QAAQ,CAACK,IAAI,EAAE;IACf,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEAC,aAAaA,CAACP,QAAQ;IACpB,IAAI,CAACM,kBAAkB,EAAE;IACzB,IAAI,CAACF,WAAW,EAAE;IAClB,IAAI,CAACI,cAAc,EAAE;IACrB,IAAI,CAACf,gBAAgB,CAAC,IAAI,CAACd,eAAe,CAAC8B,QAAQ,EAAE,CAAC;IACtDT,QAAQ,CAACK,IAAI,EAAE;EACjB;EAEAlD,mBAAmBA,CAACuD,KAAK,EAAEC,KAAK;IAC9BA,KAAK,CAACC,cAAc,EAAE;IACtB,IAAI,CAACC,aAAa,CAACH,KAAK,CAAC;IACzB,IAAI,CAACI,mBAAmB,CAACJ,KAAK,CAAC;IAC/B,IAAI,CAACN,WAAW,EAAE;EACpB;EAEA3D,UAAUA,CAACkE,KAAK;IACdA,KAAK,CAACC,cAAc,EAAE;IACtB,IAAI,CAACG,cAAc,EAAE;IACrB,IAAI,CAACX,WAAW,EAAE;EACpB;EAEA7E,kBAAkBA,CAACmF,KAAK,EAAEC,KAAK;IAC7BA,KAAK,CAACC,cAAc,EAAE;IACtB,IAAI,CAACI,kBAAkB,CAACN,KAAK,CAAC;IAC9B,IAAI,CAACjB,gBAAgB,CAAC,IAAI,CAACd,eAAe,CAAC8B,QAAQ,EAAE,CAAC;EACxD;EAEAxD,yBAAyBA,CAACyD,KAAY,EAAEC,KAAoB,EAAEM,UAAU;IACtEN,KAAK,CAACC,cAAc,EAAE;IACtB,IAAIM,iBAAiB,GAAG,IAAI,CAAClC,UAAU,CAACmC,aAAa,CAACC,gBAAgB,CAAC,2BAA2B,CAAQ;IAC1G,IAAIC,MAAM,GAAG,SAAAA,CAAA;MACX,IAAIH,iBAAiB,CAACD,UAAU,GAAG,CAAC,CAAC,EAAE;QACrCC,iBAAiB,CAACD,UAAU,GAAG,CAAC,CAAC,CAACK,KAAK,EAAE;MAC3C,CAAC,MAAM;QACLJ,iBAAiB,CAAC,CAAC,CAAC,CAACI,KAAK,EAAE;MAC9B;IACF,CAAC;IACD,IAAIC,MAAM,GAAG,SAAAA,CAAA;MACX,IAAIL,iBAAiB,CAACD,UAAU,GAAG,CAAC,CAAC,EAAE;QACrCC,iBAAiB,CAACD,UAAU,GAAG,CAAC,CAAC,CAACK,KAAK,EAAE;MAC3C,CAAC,MAAM;QACLJ,iBAAiB,CAACA,iBAAiB,CAACpD,MAAM,GAAG,CAAC,CAAC,CAACwD,KAAK,EAAE;MACzD;IACF,CAAC;IACD,IAAIX,KAAK,CAACa,GAAG,KAAK,OAAO,EAAE;MACzB,IAAI,CAACrE,mBAAmB,CAACuD,KAAK,EAAEC,KAAK,CAAC;MACtCU,MAAM,EAAE;IACV,CAAC,MAAM,IAAIV,KAAK,CAACa,GAAG,KAAK,WAAW,EAAE;MACpCH,MAAM,EAAE;IACV,CAAC,MAAM,IAAIV,KAAK,CAACa,GAAG,KAAK,SAAS,EAAE;MAClCD,MAAM,EAAE;IACV;EACF;EAEAE,kBAAkBA,CAACzB,QAAQ,EAAEW,KAAoB;IAC/CX,QAAQ,CAAC0B,IAAI,EAAE;IACf,IAAIf,KAAK,CAACa,GAAG,KAAK,WAAW,EAAE;MAC7BG,UAAU,CAAC,MAAK;QACd,IAAIC,YAAY,GAAG,IAAI,CAAC5C,UAAU,CAACmC,aAAa,CAACU,aAAa,CAAC,gBAAgB,CAAQ;QACvF,IAAID,YAAY,EAAE;UAChBA,YAAY,CAACN,KAAK,EAAE;QACtB;MACF,CAAC,EAAE,CAAC,CAAC;IACP,CAAC,MAAM,IAAIX,KAAK,CAACa,GAAG,KAAK,OAAO,EAAE;MAChCb,KAAK,CAACC,cAAc,EAAE;IACxB;EACF;EAEArE,oBAAoBA,CAACoE,KAAK;IACxB,IAAIA,KAAK,CAACa,GAAG,KAAK,OAAO,EAAE;MACzB,IAAI,CAAC/E,UAAU,CAACkE,KAAK,CAAC;MACtB,IAAI,CAACb,aAAa,EAAE;IACtB;EACF;EAEAL,gBAAgBA,CAACjB,MAAM;IACrB,IAAIA,MAAM,IAAI,IAAI,CAACvC,uBAAuB,EAAE;MAC1C,IAAI,CAAC2B,wBAAwB,GAAGY,MAAM,CAACsD,MAAM,CAACpB,KAAK,IAAG;QACpD,OAAO,CAAC,IAAI,CAACzE,uBAAuB,CAAC8F,IAAI,CAACC,EAAE,IAAIA,EAAE,CAACnG,KAAK,KAAK6E,KAAK,CAAC7E,KAAK,CAAC;MAC3E,CAAC,CAAC;IACJ;EACF;EAEA+D,yBAAyBA,CAAA;IACvB,IAAI,CAACqC,qBAAqB,GAAG,CAAC,GAAG,IAAI,CAACnD,cAAc,CAAC2B,QAAQ,EAAE,CAAC;EAClE;EAEAD,cAAcA,CAAA;IACZ,IAAI,CAAC3B,QAAQ,GAAG,CAAC,GAAG,IAAI,CAACoD,qBAAqB,CAAC;EACjD;EAEApB,aAAaA,CAACH,KAAK;IACjB,IAAIA,KAAK,EAAE;MACT,IAAI,CAAC5B,cAAc,CAACF,IAAI,CAAC,IAAI,CAACE,cAAc,CAAC2B,QAAQ,EAAE,CAACyB,MAAM,CAAC,CAACxB,KAAK,CAAC,CAAC,CAAC;IAC1E;EACF;EAEAI,mBAAmBA,CAACJ,KAAK;IACvB,IAAIyB,kBAAkB,GAAG,IAAI,CAACvE,wBAAwB;IACtD,IAAI8C,KAAK,EAAE;MACT,IAAIO,UAAU,GAAGkB,kBAAkB,CAACC,OAAO,CAAC1B,KAAK,CAAC;MAClD,IAAIO,UAAU,GAAG,CAAC,CAAC,EAAE;QACnBkB,kBAAkB,CAACE,MAAM,CAACpB,UAAU,EAAE,CAAC,CAAC;MAC1C;IACF;EACF;EAEAD,kBAAkBA,CAACN,KAAK;IACtB,IAAI4B,WAAW,GAAG,IAAI,CAACxD,cAAc,CAAC2B,QAAQ,EAAE;IAChD,IAAI8B,aAAa,GAAGD,WAAW,CAACP,IAAI,CAACS,CAAC,IAAIA,CAAC,CAAC3G,KAAK,KAAK6E,KAAK,CAAC7E,KAAK,CAAC;IAClE,IAAI4G,kBAAkB,GAAGH,WAAW,CAACF,OAAO,CAACG,aAAa,CAAC;IAC3D,IAAIE,kBAAkB,KAAK,CAAC,CAAC,EAAE;MAC7BH,WAAW,CAACD,MAAM,CAACI,kBAAkB,EAAE,CAAC,CAAC;IAC3C;EACF;EAEA1B,cAAcA,CAAA;IACZ,IAAI,CAAC9B,aAAa,CAACyD,QAAQ,CAAC;MAAEC,OAAO,EAAE,IAAI,CAACA,OAAO;MAAE9G,KAAK,EAAE,IAAI,CAACa;IAAU,CAAE,CAAC,CAC3EkG,IAAI,CACHrI,IAAI,CAAC,CAAC,CAAC,CACR,CACAiF,SAAS,CACPkB,KAAY,IAAI;MACf,IAAI,CAACG,aAAa,CAAC;QAAEgC,EAAE,EAAEnC,KAAK,CAACmC,EAAE;QAAEhH,KAAK,EAAE6E,KAAK,CAAC7E;MAAK,CAAE,CAAC;MACxD,IAAI,CAAC2C,MAAM,GAAG,CAAC,GAAG,IAAI,CAACA,MAAM,EAAEkC,KAAK,CAAC;IACvC,CAAC,CACF;EACL;EAEAb,iBAAiBA,CAAA;IACf,IAAI,CAACT,UAAU,GAAG,IAAI;EACxB;EAEAkB,kBAAkBA,CAAA;IAChB,IAAI,CAAClB,UAAU,GAAG,KAAK;EACzB;EAEAU,aAAaA,CAAA;IACX,IAAIgD,SAAS,GAAG,IAAI,CAAC9D,UAAU,CAACmC,aAAa,CAACU,aAAa,CAAC,YAAY,CAAQ;IAChFF,UAAU,CAAC,MAAMmB,SAAS,CAACxB,KAAK,EAAE,EAAE,CAAC,CAAC;EACxC;EAEA3F,aAAaA,CAACoH,IAAwB;IACpC,OAAOvI,WAAW,CAACuI,IAAI,CAACC,KAAK,CAAC;EAChC;EAEAtD,iBAAiBA,CAAA;IACf,IAAIjB,KAAK,GAAG,IAAI,CAACwE,cAAc;IAC/B,IAAI,OAAQxE,KAAM,KAAK,QAAQ,EAAE;MAC/B,IAAI,CAACL,uBAAuB,GAAGK,KAAK,GAAG,IAAI;IAC7C;EACF;EAEAyE,oBAAoBA,CAACzE,KAAc;IACjC,IAAI,CAACY,cAAc,CAACC,MAAM,GAAGb,KAAK;EACpC;EAEA2B,WAAWA,CAAA;IACT,IAAI,CAAC1D,UAAU,GAAG,EAAE;EACtB;EAEA2B,eAAeA,CAAC3B,UAAkB;IAChC,OAAO,IAAI,CAACW,uBAAuB,IAC9BX,UAAU,IACV,CAAC,IAAI,CAACkB,wBAAwB,CAACmE,IAAI,CAACrB,KAAK,IAAIA,KAAK,CAAC7E,KAAK,KAAKa,UAAU,CAAC,IACxE,CAAC,IAAI,CAACT,uBAAuB,CAAC8F,IAAI,CAACrB,KAAK,IAAIA,KAAK,CAAC7E,KAAK,KAAKa,UAAU,CAAC;EAC9E;;;uCAxPW6B,sBAAsB,EAAA9D,EAAA,CAAA0I,iBAAA,CAAA1I,EAAA,CAAA2I,UAAA,GAAA3I,EAAA,CAAA0I,iBAAA,CAAAE,EAAA,CAAAC,aAAA;IAAA;EAAA;;;YAAtB/E,sBAAsB;MAAAgF,SAAA;MAAAC,SAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;UCXjCjJ,EAHF,CAAAC,cAAA,aAE2C,gBAQU;UAA9CD,EADA,CAAAI,UAAA,mBAAA+I,qDAAA;YAAAnJ,EAAA,CAAAQ,aAAA,CAAA4I,GAAA;YAAA,OAAApJ,EAAA,CAAAa,WAAA,CAASqI,GAAA,CAAAhE,WAAA,EAAa;UAAA,EAAC,0BAAAmE,4DAAA/I,MAAA;YAAAN,EAAA,CAAAQ,aAAA,CAAA4I,GAAA;YAAA,OAAApJ,EAAA,CAAAa,WAAA,CACPqI,GAAA,CAAAT,oBAAA,CAAAnI,MAAA,CAA4B;UAAA,EAAC;UAE9CN,EADF,CAAAC,cAAA,aAAiC,YACiB;UAC9CD,EAAA,CAAAsB,UAAA,IAAAgI,8CAAA,0BAAyD;UAiB7DtJ,EADE,CAAAG,YAAA,EAAK,EACD;UACNH,EAAA,CAAAC,cAAA,eAUmD;;UADjDD,EAAA,CAAAuJ,gBAAA,2BAAAC,+DAAAlJ,MAAA;YAAAN,EAAA,CAAAQ,aAAA,CAAA4I,GAAA;YAAApJ,EAAA,CAAAyJ,kBAAA,CAAAP,GAAA,CAAAjH,UAAA,EAAA3B,MAAA,MAAA4I,GAAA,CAAAjH,UAAA,GAAA3B,MAAA;YAAA,OAAAN,EAAA,CAAAa,WAAA,CAAAP,MAAA;UAAA,EAAwB;UACxBN,EAAA,CAAAI,UAAA,qBAAAsJ,yDAAApJ,MAAA;YAAAN,EAAA,CAAAQ,aAAA,CAAA4I,GAAA;YAAA,MAAAO,WAAA,GAAA3J,EAAA,CAAA4J,WAAA;YAAA,OAAA5J,EAAA,CAAAa,WAAA,CAAWqI,GAAA,CAAAlC,kBAAA,CAAA2C,WAAA,EAAArJ,MAAA,CAAoC;UAAA,EAAC;UAVlDN,EAAA,CAAAG,YAAA,EAUmD;UAenDH,EAdA,CAAAsB,UAAA,IAAAuI,wCAAA,oBAGsB,IAAAC,wCAAA,qBAQS,KAAAC,qCAAA,iBAQuB;UAgDxD/J,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAAgC,kBAII;UAAhCD,EAAA,CAAAI,UAAA,mBAAA4J,yDAAA;YAAAhK,EAAA,CAAAQ,aAAA,CAAA4I,GAAA;YAAA,MAAAO,WAAA,GAAA3J,EAAA,CAAA4J,WAAA;YAAA,OAAA5J,EAAA,CAAAa,WAAA,CAASqI,GAAA,CAAA5D,WAAA,CAAAqE,WAAA,CAAqB;UAAA,EAAC;UAC/B3J,EAAA,CAAAe,SAAA,gBAAqC;UACvCf,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAGoC;UAAlCD,EAAA,CAAAI,UAAA,mBAAA6J,yDAAA;YAAAjK,EAAA,CAAAQ,aAAA,CAAA4I,GAAA;YAAA,MAAAO,WAAA,GAAA3J,EAAA,CAAA4J,WAAA;YAAA,OAAA5J,EAAA,CAAAa,WAAA,CAASqI,GAAA,CAAApD,aAAA,CAAA6D,WAAA,CAAuB;UAAA,EAAC;UACjC3J,EAAA,CAAAe,SAAA,gBAAiC;UAGvCf,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;UA1HJH,EAAA,CAAAkK,WAAA,wBAAAhB,GAAA,CAAAvE,UAAA,CAAwC;UAEnC3E,EAAA,CAAAgB,SAAA,EAAiC;UAAjChB,EAAA,CAAAkK,WAAA,aAAAhB,GAAA,CAAAzE,cAAA,CAAiC;UAIjCzE,EAHA,CAAAiB,UAAA,eAAAiI,GAAA,CAAAzE,cAAA,CAA6B,WAAAyE,GAAA,CAAAtE,cAAA,CAAAC,MAAA,CAGG;UAKhB7E,EAAA,CAAAgB,SAAA,GAAwC;UAAxChB,EAAA,CAAAiB,UAAA,SAAAiI,GAAA,CAAA1H,uBAAA,CAAA6B,MAAA,KAAwC;UAuBzDrD,EAAA,CAAAgB,SAAA,EAAmD;UAAnDhB,EAAA,CAAAkK,WAAA,SAAAhB,GAAA,CAAA1H,uBAAA,CAAA6B,MAAA,OAAmD;UACnDrD,EAAA,CAAAmK,qBAAA,gBAAAjB,GAAA,CAAA1H,uBAAA,CAAA6B,MAAA,SAAArD,EAAA,CAAAoK,WAAA,mDAAkH;UAFlHpK,EAAA,CAAAiB,UAAA,aAAAiI,GAAA,CAAAzE,cAAA,CAA2B;UAK3BzE,EAAA,CAAAqK,gBAAA,YAAAnB,GAAA,CAAAjH,UAAA,CAAwB;UAKvBjC,EAAA,CAAAgB,SAAA,GAAiB;UAAjBhB,EAAA,CAAAiB,UAAA,UAAAiI,GAAA,CAAAvE,UAAA,CAAiB;UAMjB3E,EAAA,CAAAgB,SAAA,EAAgB;UAAhBhB,EAAA,CAAAiB,UAAA,SAAAiI,GAAA,CAAAvE,UAAA,CAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}