{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FlexModule } from '@angular/flex-layout';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSlideToggleModule } from '@angular/material/slide-toggle';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiControlMessagesModule, SwuiDatePickerModule, SwuiGridModule, SwuiSchemaTopFilterModule } from '@skywind-group/lib-swui';\nimport { ControlMessagesModule } from '../../../../../common/components/control-messages/control-messages.module';\nimport { TrimInputValueModule } from '../../../../../common/directives/trim-input-value/trim-input-value.module';\nimport { TestPlayersService } from '../../../../../common/services/test-players.service';\nimport { TabTestPlayersComponent } from './tab-test-players.component';\nimport { TabTestPlayersRoutingModule } from './tab-test-players.routing';\nimport { TestPlayersDialogComponent } from './test-players-dialog/test-players-dialog.component';\nimport { TestPlayersMerchantComponent } from './test-players-merchant.component';\nimport { TestPlayersComponent } from './test-players.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nexport class TabTestPlayersModule {\n  static {\n    this.ɵfac = function TabTestPlayersModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TabTestPlayersModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: TabTestPlayersModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [TestPlayersService],\n      imports: [TranslateModule.forChild(), TabTestPlayersRoutingModule, SwuiGridModule, MatButtonModule, SwuiSchemaTopFilterModule, CommonModule, MatDialogModule, ReactiveFormsModule, FlexModule, MatFormFieldModule, MatInputModule, SwuiControlMessagesModule, SwuiDatePickerModule, MatIconModule, MatCardModule, MatSlideToggleModule, ControlMessagesModule, MatTooltipModule, TrimInputValueModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(TabTestPlayersModule, {\n    declarations: [TabTestPlayersComponent, TestPlayersComponent, TestPlayersMerchantComponent, TestPlayersDialogComponent],\n    imports: [i1.TranslateModule, TabTestPlayersRoutingModule, SwuiGridModule, MatButtonModule, SwuiSchemaTopFilterModule, CommonModule, MatDialogModule, ReactiveFormsModule, FlexModule, MatFormFieldModule, MatInputModule, SwuiControlMessagesModule, SwuiDatePickerModule, MatIconModule, MatCardModule, MatSlideToggleModule, ControlMessagesModule, MatTooltipModule, TrimInputValueModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FlexModule", "ReactiveFormsModule", "MatButtonModule", "MatCardModule", "MatDialogModule", "MatFormFieldModule", "MatIconModule", "MatInputModule", "MatSlideToggleModule", "MatTooltipModule", "TranslateModule", "SwuiControlMessagesModule", "SwuiDatePickerModule", "SwuiGridModule", "SwuiSchemaTopFilterModule", "ControlMessagesModule", "TrimInputValueModule", "TestPlayersService", "TabTestPlayersComponent", "TabTestPlayersRoutingModule", "TestPlayersDialogComponent", "TestPlayersMerchantComponent", "TestPlayersComponent", "TabTestPlayersModule", "imports", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "i1"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-test-players/tab-test-players.module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { FlexModule } from '@angular/flex-layout';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSlideToggleModule } from '@angular/material/slide-toggle';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiControlMessagesModule, SwuiDatePickerModule, SwuiGridModule, SwuiSchemaTopFilterModule } from '@skywind-group/lib-swui';\nimport { ControlMessagesModule } from '../../../../../common/components/control-messages/control-messages.module';\nimport { TrimInputValueModule } from '../../../../../common/directives/trim-input-value/trim-input-value.module';\nimport { TestPlayersService } from '../../../../../common/services/test-players.service';\nimport { TabTestPlayersComponent } from './tab-test-players.component';\nimport { TabTestPlayersRoutingModule } from './tab-test-players.routing';\nimport { TestPlayersDialogComponent } from './test-players-dialog/test-players-dialog.component';\nimport { TestPlayersMerchantComponent } from './test-players-merchant.component';\nimport { TestPlayersComponent } from './test-players.component';\n\n@NgModule({\n    imports: [\n        TranslateModule.forChild(),\n        TabTestPlayersRoutingModule,\n        SwuiGridModule,\n        MatButtonModule,\n        SwuiSchemaTopFilterModule,\n        CommonModule,\n        MatDialogModule,\n        ReactiveFormsModule,\n        FlexModule,\n        MatFormFieldModule,\n        MatInputModule,\n        SwuiControlMessagesModule,\n        SwuiDatePickerModule,\n        MatIconModule,\n        MatCardModule,\n        MatSlideToggleModule,\n        ControlMessagesModule,\n        MatTooltipModule,\n        TrimInputValueModule,\n    ],\n  declarations: [\n    TabTestPlayersComponent,\n    TestPlayersComponent,\n    TestPlayersMerchantComponent,\n    TestPlayersDialogComponent,\n  ],\n  providers: [\n    TestPlayersService,\n  ],\n})\nexport class TabTestPlayersModule {\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,UAAU,QAAQ,sBAAsB;AACjD,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,yBAAyB,EAAEC,oBAAoB,EAAEC,cAAc,EAAEC,yBAAyB,QAAQ,yBAAyB;AACpI,SAASC,qBAAqB,QAAQ,2EAA2E;AACjH,SAASC,oBAAoB,QAAQ,2EAA2E;AAChH,SAASC,kBAAkB,QAAQ,qDAAqD;AACxF,SAASC,uBAAuB,QAAQ,8BAA8B;AACtE,SAASC,2BAA2B,QAAQ,4BAA4B;AACxE,SAASC,0BAA0B,QAAQ,qDAAqD;AAChG,SAASC,4BAA4B,QAAQ,mCAAmC;AAChF,SAASC,oBAAoB,QAAQ,0BAA0B;;;AAkC/D,OAAM,MAAOC,oBAAoB;;;uCAApBA,oBAAoB;IAAA;EAAA;;;YAApBA;IAAoB;EAAA;;;iBAJpB,CACTN,kBAAkB,CACnB;MAAAO,OAAA,GA5BKd,eAAe,CAACe,QAAQ,EAAE,EAC1BN,2BAA2B,EAC3BN,cAAc,EACdX,eAAe,EACfY,yBAAyB,EACzBf,YAAY,EACZK,eAAe,EACfH,mBAAmB,EACnBD,UAAU,EACVK,kBAAkB,EAClBE,cAAc,EACdI,yBAAyB,EACzBC,oBAAoB,EACpBN,aAAa,EACbH,aAAa,EACbK,oBAAoB,EACpBO,qBAAqB,EACrBN,gBAAgB,EAChBO,oBAAoB;IAAA;EAAA;;;2EAYfO,oBAAoB;IAAAG,YAAA,GAT7BR,uBAAuB,EACvBI,oBAAoB,EACpBD,4BAA4B,EAC5BD,0BAA0B;IAAAI,OAAA,GAAAG,EAAA,CAAAjB,eAAA,EAvBtBS,2BAA2B,EAC3BN,cAAc,EACdX,eAAe,EACfY,yBAAyB,EACzBf,YAAY,EACZK,eAAe,EACfH,mBAAmB,EACnBD,UAAU,EACVK,kBAAkB,EAClBE,cAAc,EACdI,yBAAyB,EACzBC,oBAAoB,EACpBN,aAAa,EACbH,aAAa,EACbK,oBAAoB,EACpBO,qBAAqB,EACrBN,gBAAgB,EAChBO,oBAAoB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}