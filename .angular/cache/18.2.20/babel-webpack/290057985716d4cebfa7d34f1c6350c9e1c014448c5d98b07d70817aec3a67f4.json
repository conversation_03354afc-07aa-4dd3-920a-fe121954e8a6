{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { FormControl, Validators } from '@angular/forms';\nimport { ValidationService } from '../../../../../../common/services/validation.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"../../../../../../common/components/control-messages/control-messages.component\";\nimport * as i4 from \"../../../../../../common/directives/trim-input-value/trim-input-value.component\";\nimport * as i5 from \"@ngx-translate/core\";\nfunction SetupGameFormComponent_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"label\", 3);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 4);\n    i0.ɵɵelement(5, \"input\", 16)(6, \"control-messages\", 10);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(3, 4, \"ENTITY_SETUP.GAMES.MODALS.aamsCode\"), \":\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"formControl\", ctx_r0.aamsCode);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"control\", ctx_r0.aamsCode)(\"forceShow\", ctx_r0.submitted);\n  }\n}\nfunction SetupGameFormComponent_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"label\", 3);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 17);\n    i0.ɵɵelement(5, \"input\", 18);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(3, 2, \"ENTITY_SETUP.GAMES.MODALS.mustWinJackpotBundled\"), \":\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"formControl\", ctx_r0.mustWinJackpotBundled);\n  }\n}\nexport class SetupGameFormComponent {\n  set game(value) {\n    const gameAlreadySet = this._game && this._game.code === value.code;\n    if (!value || gameAlreadySet) return;\n    this._game = value;\n    let settings;\n    if ('settings' in value && value.settings !== null && typeof value.settings === 'object') {\n      settings = JSON.stringify(value.settings, null, '  ');\n    }\n    const formValue = {\n      status: value.status,\n      externalGameId: value.externalGameId,\n      settings\n    };\n    this.form.patchValue(formValue);\n  }\n  get game() {\n    return this._game;\n  }\n  constructor(fb) {\n    this.fb = fb;\n    this.gameSubmit = new EventEmitter();\n    this.aamsCode = new FormControl();\n    this.mustWinJackpotBundled = new FormControl();\n    this.submitted = false;\n    this.isItalianRegulation = false;\n    this.initForm();\n  }\n  ngOnInit() {\n    this.isItalianRegulation = this.entity?.jurisdiction && this.entity?.jurisdiction[0] ? this.entity.jurisdiction[0].code === 'IT' : false;\n    if (this.isItalianRegulation) {\n      this.aamsCode.setValidators(Validators.required);\n    }\n  }\n  submitChanges(event) {\n    event.preventDefault();\n    this.submitted = true;\n    if (this.form.valid && this.aamsCode.valid) {\n      let data = JSON.parse(JSON.stringify(this.form.value));\n      if (!data.externalGameId) {\n        delete data.externalGameId;\n      }\n      if ('settings' in data) {\n        // settings will be as string because of textarea value\n        if (data.settings !== 'null' && data.settings !== '' && typeof data.settings === 'string') {\n          Object.assign(data, {\n            settings: JSON.parse(data.settings)\n          });\n        } else {\n          delete data.settings;\n        }\n      }\n      if (this.isItalianRegulation) {\n        if (data.settings) {\n          data.settings.aamsCode = this.aamsCode.value.toString();\n          data.settings.mustWinJackpotBundled = this.mustWinJackpotBundled.value;\n        } else {\n          data.settings = {\n            aamsCode: this.aamsCode.value.toString(),\n            mustWinJackpotBundled: this.mustWinJackpotBundled.value\n          };\n        }\n        if (!this.mustWinJackpotBundled.value) {\n          delete data.settings.mustWinJackpotBundled;\n        }\n      }\n      this.gameSubmit.emit(data);\n    }\n  }\n  get externalGameIdControl() {\n    return this.form?.get('externalGameId');\n  }\n  initForm() {\n    this.form = this.fb.group({\n      status: ['', Validators.required],\n      settings: ['', ValidationService.IfNotEmpty(ValidationService.JSONValidator)],\n      externalGameId: ['']\n    });\n  }\n  static {\n    this.ɵfac = function SetupGameFormComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SetupGameFormComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SetupGameFormComponent,\n      selectors: [[\"setup-game-form\"]],\n      inputs: {\n        entity: \"entity\",\n        game: \"game\"\n      },\n      outputs: {\n        gameSubmit: \"gameSubmit\"\n      },\n      decls: 41,\n      vars: 33,\n      consts: [[1, \"panel\", 3, \"formGroup\"], [1, \"panel-body\", \"no-padding-bottom\"], [1, \"form-group\"], [1, \"col-lg-3\", \"control-label\"], [1, \"col-lg-9\"], [\"formControlName\", \"status\", 1, \"select\", \"form-control\"], [\"value\", \"\", \"disabled\", \"\", \"selected\", \"\", \"hidden\", \"\"], [\"value\", \"test\"], [\"value\", \"normal\"], [\"value\", \"suspended\"], [1, \"validation-error-label\", 3, \"control\", \"forceShow\"], [\"formControlName\", \"settings\", 1, \"form-control\", 2, \"max-width\", \"100%\", \"min-width\", \"100%\"], [\"trimValue\", \"\", \"type\", \"text\", \"formControlName\", \"externalGameId\", 1, \"form-control\"], [\"class\", \"form-group\", 4, \"ngIf\"], [1, \"panel-footer\", \"text-right\"], [1, \"btn\", \"bg-slate-400\", \"mr-20\", 3, \"click\"], [\"type\", \"number\", \"min\", \"0\", 1, \"form-control\", 3, \"formControl\"], [1, \"col-lg-1\", 2, \"display\", \"flex\", \"align-items\", \"center\", \"height\", \"30px\"], [\"type\", \"checkbox\", 3, \"formControl\"]],\n      template: function SetupGameFormComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"label\", 3);\n          i0.ɵɵtext(4);\n          i0.ɵɵpipe(5, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 4)(7, \"select\", 5)(8, \"option\", 6);\n          i0.ɵɵtext(9);\n          i0.ɵɵpipe(10, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"option\", 7);\n          i0.ɵɵtext(12);\n          i0.ɵɵpipe(13, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"option\", 8);\n          i0.ɵɵtext(15);\n          i0.ɵɵpipe(16, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"option\", 9);\n          i0.ɵɵtext(18);\n          i0.ɵɵpipe(19, \"translate\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(20, \"control-messages\", 10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(21, \"div\", 2)(22, \"label\", 3);\n          i0.ɵɵtext(23);\n          i0.ɵɵpipe(24, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"div\", 4);\n          i0.ɵɵelement(26, \"textarea\", 11)(27, \"control-messages\", 10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(28, \"div\", 2)(29, \"label\", 3);\n          i0.ɵɵtext(30);\n          i0.ɵɵpipe(31, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"div\", 4);\n          i0.ɵɵelement(33, \"input\", 12)(34, \"control-messages\", 10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(35, SetupGameFormComponent_div_35_Template, 7, 6, \"div\", 13)(36, SetupGameFormComponent_div_36_Template, 6, 4, \"div\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"div\", 14)(38, \"button\", 15);\n          i0.ɵɵlistener(\"click\", function SetupGameFormComponent_Template_button_click_38_listener($event) {\n            return ctx.submitChanges($event);\n          });\n          i0.ɵɵtext(39);\n          i0.ɵɵpipe(40, \"translate\");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"formGroup\", ctx.form);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(5, 17, \"ENTITY_SETUP.GAMES.MODALS.selectStatus\"), \":\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(10, 19, \"ALL.pleaseSelect\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(13, 21, \"ALL.test\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(16, 23, \"ALL.active\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(19, 25, \"ALL.inactive\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"control\", ctx.form.get(\"status\"))(\"forceShow\", ctx.submitted);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(24, 27, \"ALL.settings\"), \":\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"control\", ctx.form.get(\"settings\"))(\"forceShow\", ctx.submitted);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(31, 29, \"ENTITY_SETUP.GAMES.MODALS.externalGameId\"), \": \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"control\", ctx.externalGameIdControl)(\"forceShow\", ctx.submitted);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isItalianRegulation);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isItalianRegulation);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(40, 31, \"DIALOG.save\"));\n        }\n      },\n      dependencies: [i2.NgIf, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.CheckboxControlValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MinValidator, i1.FormControlDirective, i1.FormGroupDirective, i1.FormControlName, i3.ControlMessagesComponent, i4.TrimInputValueComponent, i5.TranslatePipe],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "FormControl", "Validators", "ValidationService", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "ɵɵproperty", "ctx_r0", "aamsCode", "submitted", "mustWinJackpotBundled", "SetupGameFormComponent", "game", "value", "gameAlreadySet", "_game", "code", "settings", "JSON", "stringify", "formValue", "status", "externalGameId", "form", "patchValue", "constructor", "fb", "gameSubmit", "isItalianRegulation", "initForm", "ngOnInit", "entity", "jurisdiction", "setValidators", "required", "submitChanges", "event", "preventDefault", "valid", "data", "parse", "Object", "assign", "toString", "emit", "externalGameIdControl", "get", "group", "IfNotEmpty", "JSONValidator", "ɵɵdirectiveInject", "i1", "FormBuilder", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "SetupGameFormComponent_Template", "rf", "ctx", "ɵɵtemplate", "SetupGameFormComponent_div_35_Template", "SetupGameFormComponent_div_36_Template", "ɵɵlistener", "SetupGameFormComponent_Template_button_click_38_listener", "$event", "ɵɵtextInterpolate"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-games/manage-games/setup-game-form.component.ts", "/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-games/manage-games/setup-game-form.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';\nimport { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';\nimport { ValidationService } from '../../../../../../common/services/validation.service';\nimport { Game } from '../../../../../../common/typings';\nimport { Entity } from '../../../../../../common/models/entity.model';\n\n@Component({\n  selector: 'setup-game-form',\n  templateUrl: './setup-game-form.component.html',\n})\nexport class SetupGameFormComponent implements OnInit {\n\n  @Input() public entity: Entity;\n\n  @Output() public gameSubmit: EventEmitter<Game> = new EventEmitter();\n\n  public form: FormGroup;\n  public aamsCode = new FormControl();\n  public mustWinJackpotBundled = new FormControl();\n\n  public submitted: boolean = false;\n\n  public isItalianRegulation = false;\n\n  private _game: Game;\n  @Input()\n  set game( value: Game ) {\n    const gameAlreadySet = this._game && this._game.code === value.code;\n    if (!value || gameAlreadySet) return;\n\n    this._game = value;\n    let settings;\n\n    if ('settings' in value && value.settings !== null && typeof value.settings === 'object') {\n      settings = JSON.stringify(value.settings, null, '  ');\n    }\n\n    const formValue = {\n      status: value.status,\n      externalGameId: value.externalGameId,\n      settings,\n    };\n\n    this.form.patchValue(formValue);\n  }\n\n  get game(): Game {\n    return this._game;\n  }\n\n  constructor( public fb: FormBuilder,\n  ) {\n    this.initForm();\n  }\n\n  ngOnInit() {\n    this.isItalianRegulation = this.entity?.jurisdiction && this.entity?.jurisdiction[0]\n      ? this.entity.jurisdiction[0].code === 'IT'\n      : false;\n\n    if (this.isItalianRegulation) {\n      this.aamsCode.setValidators(Validators.required);\n    }\n  }\n\n  public submitChanges( event: Event ) {\n    event.preventDefault();\n\n    this.submitted = true;\n\n    if (this.form.valid && this.aamsCode.valid) {\n      let data = JSON.parse(JSON.stringify(this.form.value));\n\n      if (!data.externalGameId) {\n        delete data.externalGameId;\n      }\n\n      if ('settings' in data) {\n        // settings will be as string because of textarea value\n        if (data.settings !== 'null' && data.settings !== '' && typeof data.settings === 'string') {\n          Object.assign(data, { settings: JSON.parse(data.settings) });\n        } else {\n          delete data.settings;\n        }\n      }\n\n      if (this.isItalianRegulation) {\n        if (data.settings) {\n          data.settings.aamsCode = this.aamsCode.value.toString();\n          data.settings.mustWinJackpotBundled = this.mustWinJackpotBundled.value;\n        } else {\n          data.settings = { aamsCode: this.aamsCode.value.toString(), mustWinJackpotBundled: this.mustWinJackpotBundled.value };\n        }\n\n        if (!this.mustWinJackpotBundled.value) {\n          delete data.settings.mustWinJackpotBundled;\n        }\n      }\n\n      this.gameSubmit.emit(data);\n    }\n  }\n\n  get externalGameIdControl(): FormControl {\n    return this.form?.get('externalGameId') as FormControl;\n  }\n\n  private initForm() {\n    this.form = this.fb.group({\n      status: ['', Validators.required],\n      settings: ['', ValidationService.IfNotEmpty(ValidationService.JSONValidator)],\n      externalGameId: [''],\n    });\n  }\n}\n", "<div class=\"panel\" [formGroup]=\"form\">\n  <div class=\"panel-body no-padding-bottom\">\n    <div class=\"form-group\">\n      <label class=\"col-lg-3 control-label\">{{'ENTITY_SETUP.GAMES.MODALS.selectStatus' | translate}}:</label>\n      <div class=\"col-lg-9\">\n        <select class=\"select form-control\" formControlName=\"status\">\n          <option value=\"\" disabled selected hidden>{{'ALL.pleaseSelect' | translate}}</option>\n          <option value=\"test\">{{'ALL.test' | translate}}</option>\n          <option value=\"normal\">{{'ALL.active' | translate}}</option>\n          <option value=\"suspended\">{{'ALL.inactive' | translate}}</option>\n        </select>\n        <control-messages class=\"validation-error-label\" [control]=\"form.get('status')\"\n                          [forceShow]=\"submitted\"></control-messages>\n      </div>\n    </div>\n    <div class=\"form-group\">\n      <label class=\"col-lg-3 control-label\">{{'ALL.settings' | translate}}:</label>\n      <div class=\"col-lg-9\">\n        <textarea style=\"max-width: 100%; min-width: 100%\" formControlName=\"settings\" class=\"form-control\"></textarea>\n        <control-messages class=\"validation-error-label\" [control]=\"form.get('settings')\"\n                          [forceShow]=\"submitted\"></control-messages>\n      </div>\n    </div>\n    <div class=\"form-group\">\n      <label class=\"col-lg-3 control-label\">\n        {{ 'ENTITY_SETUP.GAMES.MODALS.externalGameId' | translate }}:\n      </label>\n      <div class=\"col-lg-9\">\n        <input class=\"form-control\"\n               trimValue\n               type=\"text\"\n               formControlName=\"externalGameId\"/>\n        <control-messages class=\"validation-error-label\"\n                          [control]=\"externalGameIdControl\"\n                          [forceShow]=\"submitted\">\n        </control-messages>\n      </div>\n    </div>\n    <div *ngIf=\"isItalianRegulation\" class=\"form-group\">\n      <label class=\"col-lg-3 control-label\">{{'ENTITY_SETUP.GAMES.MODALS.aamsCode' | translate}}:</label>\n      <div class=\"col-lg-9\">\n        <input class=\"form-control\" type=\"number\" min=\"0\" [formControl]=\"aamsCode\"/>\n        <control-messages class=\"validation-error-label\" [control]=\"aamsCode\"\n                          [forceShow]=\"submitted\"></control-messages>\n      </div>\n    </div>\n    <div *ngIf=\"isItalianRegulation\" class=\"form-group\">\n      <label class=\"col-lg-3 control-label\">{{'ENTITY_SETUP.GAMES.MODALS.mustWinJackpotBundled' | translate}}:</label>\n      <div class=\"col-lg-1\" style=\"display: flex; align-items: center; height: 30px\">\n        <input type=\"checkbox\" [formControl]=\"mustWinJackpotBundled\"/>\n      </div>\n    </div>\n  </div>\n  <div class=\"panel-footer text-right\">\n    <button class=\"btn bg-slate-400 mr-20\" (click)=\"submitChanges($event)\">{{'DIALOG.save' | translate}}</button>\n  </div>\n</div>\n"], "mappings": "AAAA,SAAoBA,YAAY,QAA+B,eAAe;AAC9E,SAAsBC,WAAW,EAAaC,UAAU,QAAQ,gBAAgB;AAChF,SAASC,iBAAiB,QAAQ,sDAAsD;;;;;;;;;ICqClFC,EADF,CAAAC,cAAA,aAAoD,eACZ;IAAAD,EAAA,CAAAE,MAAA,GAAqD;;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACnGH,EAAA,CAAAC,cAAA,aAAsB;IAEpBD,EADA,CAAAI,SAAA,gBAA4E,2BAEf;IAEjEJ,EADE,CAAAG,YAAA,EAAM,EACF;;;;IANkCH,EAAA,CAAAK,SAAA,GAAqD;IAArDL,EAAA,CAAAM,kBAAA,KAAAN,EAAA,CAAAO,WAAA,kDAAqD;IAEvCP,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAAQ,UAAA,gBAAAC,MAAA,CAAAC,QAAA,CAAwB;IACzBV,EAAA,CAAAK,SAAA,EAAoB;IACnDL,EAD+B,CAAAQ,UAAA,YAAAC,MAAA,CAAAC,QAAA,CAAoB,cAAAD,MAAA,CAAAE,SAAA,CAC5B;;;;;IAI3CX,EADF,CAAAC,cAAA,aAAoD,eACZ;IAAAD,EAAA,CAAAE,MAAA,GAAkE;;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAChHH,EAAA,CAAAC,cAAA,cAA+E;IAC7ED,EAAA,CAAAI,SAAA,gBAA8D;IAElEJ,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAJkCH,EAAA,CAAAK,SAAA,GAAkE;IAAlEL,EAAA,CAAAM,kBAAA,KAAAN,EAAA,CAAAO,WAAA,+DAAkE;IAE/EP,EAAA,CAAAK,SAAA,GAAqC;IAArCL,EAAA,CAAAQ,UAAA,gBAAAC,MAAA,CAAAG,qBAAA,CAAqC;;;ADvCpE,OAAM,MAAOC,sBAAsB;EAejC,IACIC,IAAIA,CAAEC,KAAW;IACnB,MAAMC,cAAc,GAAG,IAAI,CAACC,KAAK,IAAI,IAAI,CAACA,KAAK,CAACC,IAAI,KAAKH,KAAK,CAACG,IAAI;IACnE,IAAI,CAACH,KAAK,IAAIC,cAAc,EAAE;IAE9B,IAAI,CAACC,KAAK,GAAGF,KAAK;IAClB,IAAII,QAAQ;IAEZ,IAAI,UAAU,IAAIJ,KAAK,IAAIA,KAAK,CAACI,QAAQ,KAAK,IAAI,IAAI,OAAOJ,KAAK,CAACI,QAAQ,KAAK,QAAQ,EAAE;MACxFA,QAAQ,GAAGC,IAAI,CAACC,SAAS,CAACN,KAAK,CAACI,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC;IACvD;IAEA,MAAMG,SAAS,GAAG;MAChBC,MAAM,EAAER,KAAK,CAACQ,MAAM;MACpBC,cAAc,EAAET,KAAK,CAACS,cAAc;MACpCL;KACD;IAED,IAAI,CAACM,IAAI,CAACC,UAAU,CAACJ,SAAS,CAAC;EACjC;EAEA,IAAIR,IAAIA,CAAA;IACN,OAAO,IAAI,CAACG,KAAK;EACnB;EAEAU,YAAoBC,EAAe;IAAf,KAAAA,EAAE,GAAFA,EAAE;IApCL,KAAAC,UAAU,GAAuB,IAAIjC,YAAY,EAAE;IAG7D,KAAAc,QAAQ,GAAG,IAAIb,WAAW,EAAE;IAC5B,KAAAe,qBAAqB,GAAG,IAAIf,WAAW,EAAE;IAEzC,KAAAc,SAAS,GAAY,KAAK;IAE1B,KAAAmB,mBAAmB,GAAG,KAAK;IA8BhC,IAAI,CAACC,QAAQ,EAAE;EACjB;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACF,mBAAmB,GAAG,IAAI,CAACG,MAAM,EAAEC,YAAY,IAAI,IAAI,CAACD,MAAM,EAAEC,YAAY,CAAC,CAAC,CAAC,GAChF,IAAI,CAACD,MAAM,CAACC,YAAY,CAAC,CAAC,CAAC,CAAChB,IAAI,KAAK,IAAI,GACzC,KAAK;IAET,IAAI,IAAI,CAACY,mBAAmB,EAAE;MAC5B,IAAI,CAACpB,QAAQ,CAACyB,aAAa,CAACrC,UAAU,CAACsC,QAAQ,CAAC;IAClD;EACF;EAEOC,aAAaA,CAAEC,KAAY;IAChCA,KAAK,CAACC,cAAc,EAAE;IAEtB,IAAI,CAAC5B,SAAS,GAAG,IAAI;IAErB,IAAI,IAAI,CAACc,IAAI,CAACe,KAAK,IAAI,IAAI,CAAC9B,QAAQ,CAAC8B,KAAK,EAAE;MAC1C,IAAIC,IAAI,GAAGrB,IAAI,CAACsB,KAAK,CAACtB,IAAI,CAACC,SAAS,CAAC,IAAI,CAACI,IAAI,CAACV,KAAK,CAAC,CAAC;MAEtD,IAAI,CAAC0B,IAAI,CAACjB,cAAc,EAAE;QACxB,OAAOiB,IAAI,CAACjB,cAAc;MAC5B;MAEA,IAAI,UAAU,IAAIiB,IAAI,EAAE;QACtB;QACA,IAAIA,IAAI,CAACtB,QAAQ,KAAK,MAAM,IAAIsB,IAAI,CAACtB,QAAQ,KAAK,EAAE,IAAI,OAAOsB,IAAI,CAACtB,QAAQ,KAAK,QAAQ,EAAE;UACzFwB,MAAM,CAACC,MAAM,CAACH,IAAI,EAAE;YAAEtB,QAAQ,EAAEC,IAAI,CAACsB,KAAK,CAACD,IAAI,CAACtB,QAAQ;UAAC,CAAE,CAAC;QAC9D,CAAC,MAAM;UACL,OAAOsB,IAAI,CAACtB,QAAQ;QACtB;MACF;MAEA,IAAI,IAAI,CAACW,mBAAmB,EAAE;QAC5B,IAAIW,IAAI,CAACtB,QAAQ,EAAE;UACjBsB,IAAI,CAACtB,QAAQ,CAACT,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACK,KAAK,CAAC8B,QAAQ,EAAE;UACvDJ,IAAI,CAACtB,QAAQ,CAACP,qBAAqB,GAAG,IAAI,CAACA,qBAAqB,CAACG,KAAK;QACxE,CAAC,MAAM;UACL0B,IAAI,CAACtB,QAAQ,GAAG;YAAET,QAAQ,EAAE,IAAI,CAACA,QAAQ,CAACK,KAAK,CAAC8B,QAAQ,EAAE;YAAEjC,qBAAqB,EAAE,IAAI,CAACA,qBAAqB,CAACG;UAAK,CAAE;QACvH;QAEA,IAAI,CAAC,IAAI,CAACH,qBAAqB,CAACG,KAAK,EAAE;UACrC,OAAO0B,IAAI,CAACtB,QAAQ,CAACP,qBAAqB;QAC5C;MACF;MAEA,IAAI,CAACiB,UAAU,CAACiB,IAAI,CAACL,IAAI,CAAC;IAC5B;EACF;EAEA,IAAIM,qBAAqBA,CAAA;IACvB,OAAO,IAAI,CAACtB,IAAI,EAAEuB,GAAG,CAAC,gBAAgB,CAAgB;EACxD;EAEQjB,QAAQA,CAAA;IACd,IAAI,CAACN,IAAI,GAAG,IAAI,CAACG,EAAE,CAACqB,KAAK,CAAC;MACxB1B,MAAM,EAAE,CAAC,EAAE,EAAEzB,UAAU,CAACsC,QAAQ,CAAC;MACjCjB,QAAQ,EAAE,CAAC,EAAE,EAAEpB,iBAAiB,CAACmD,UAAU,CAACnD,iBAAiB,CAACoD,aAAa,CAAC,CAAC;MAC7E3B,cAAc,EAAE,CAAC,EAAE;KACpB,CAAC;EACJ;;;uCAvGWX,sBAAsB,EAAAb,EAAA,CAAAoD,iBAAA,CAAAC,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAtBzC,sBAAsB;MAAA0C,SAAA;MAAAC,MAAA;QAAAvB,MAAA;QAAAnB,IAAA;MAAA;MAAA2C,OAAA;QAAA5B,UAAA;MAAA;MAAA6B,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP7B/D,EAHN,CAAAC,cAAA,aAAsC,aACM,aAChB,eACgB;UAAAD,EAAA,CAAAE,MAAA,GAAyD;;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAGnGH,EAFJ,CAAAC,cAAA,aAAsB,gBACyC,gBACjB;UAAAD,EAAA,CAAAE,MAAA,GAAkC;;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACrFH,EAAA,CAAAC,cAAA,iBAAqB;UAAAD,EAAA,CAAAE,MAAA,IAA0B;;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACxDH,EAAA,CAAAC,cAAA,iBAAuB;UAAAD,EAAA,CAAAE,MAAA,IAA4B;;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC5DH,EAAA,CAAAC,cAAA,iBAA0B;UAAAD,EAAA,CAAAE,MAAA,IAA8B;;UAC1DF,EAD0D,CAAAG,YAAA,EAAS,EAC1D;UACTH,EAAA,CAAAI,SAAA,4BAC6D;UAEjEJ,EADE,CAAAG,YAAA,EAAM,EACF;UAEJH,EADF,CAAAC,cAAA,cAAwB,gBACgB;UAAAD,EAAA,CAAAE,MAAA,IAA+B;;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC7EH,EAAA,CAAAC,cAAA,cAAsB;UAEpBD,EADA,CAAAI,SAAA,oBAA8G,4BAEjD;UAEjEJ,EADE,CAAAG,YAAA,EAAM,EACF;UAEJH,EADF,CAAAC,cAAA,cAAwB,gBACgB;UACpCD,EAAA,CAAAE,MAAA,IACF;;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,cAAsB;UAKpBD,EAJA,CAAAI,SAAA,iBAGyC,4BAItB;UAEvBJ,EADE,CAAAG,YAAA,EAAM,EACF;UASNH,EARA,CAAAiE,UAAA,KAAAC,sCAAA,kBAAoD,KAAAC,sCAAA,kBAQA;UAMtDnE,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAAqC,kBACoC;UAAhCD,EAAA,CAAAoE,UAAA,mBAAAC,yDAAAC,MAAA;YAAA,OAASN,GAAA,CAAA3B,aAAA,CAAAiC,MAAA,CAAqB;UAAA,EAAC;UAACtE,EAAA,CAAAE,MAAA,IAA6B;;UAExGF,EAFwG,CAAAG,YAAA,EAAS,EACzG,EACF;;;UAxDaH,EAAA,CAAAQ,UAAA,cAAAwD,GAAA,CAAAvC,IAAA,CAAkB;UAGOzB,EAAA,CAAAK,SAAA,GAAyD;UAAzDL,EAAA,CAAAM,kBAAA,KAAAN,EAAA,CAAAO,WAAA,uDAAyD;UAGjDP,EAAA,CAAAK,SAAA,GAAkC;UAAlCL,EAAA,CAAAuE,iBAAA,CAAAvE,EAAA,CAAAO,WAAA,6BAAkC;UACvDP,EAAA,CAAAK,SAAA,GAA0B;UAA1BL,EAAA,CAAAuE,iBAAA,CAAAvE,EAAA,CAAAO,WAAA,qBAA0B;UACxBP,EAAA,CAAAK,SAAA,GAA4B;UAA5BL,EAAA,CAAAuE,iBAAA,CAAAvE,EAAA,CAAAO,WAAA,uBAA4B;UACzBP,EAAA,CAAAK,SAAA,GAA8B;UAA9BL,EAAA,CAAAuE,iBAAA,CAAAvE,EAAA,CAAAO,WAAA,yBAA8B;UAETP,EAAA,CAAAK,SAAA,GAA8B;UAC7DL,EAD+B,CAAAQ,UAAA,YAAAwD,GAAA,CAAAvC,IAAA,CAAAuB,GAAA,WAA8B,cAAAgB,GAAA,CAAArD,SAAA,CACtC;UAILX,EAAA,CAAAK,SAAA,GAA+B;UAA/BL,EAAA,CAAAM,kBAAA,KAAAN,EAAA,CAAAO,WAAA,8BAA+B;UAGlBP,EAAA,CAAAK,SAAA,GAAgC;UAC/DL,EAD+B,CAAAQ,UAAA,YAAAwD,GAAA,CAAAvC,IAAA,CAAAuB,GAAA,aAAgC,cAAAgB,GAAA,CAAArD,SAAA,CACxC;UAKzCX,EAAA,CAAAK,SAAA,GACF;UADEL,EAAA,CAAAM,kBAAA,MAAAN,EAAA,CAAAO,WAAA,2DACF;UAOoBP,EAAA,CAAAK,SAAA,GAAiC;UACjCL,EADA,CAAAQ,UAAA,YAAAwD,GAAA,CAAAjB,qBAAA,CAAiC,cAAAiB,GAAA,CAAArD,SAAA,CACV;UAIvCX,EAAA,CAAAK,SAAA,EAAyB;UAAzBL,EAAA,CAAAQ,UAAA,SAAAwD,GAAA,CAAAlC,mBAAA,CAAyB;UAQzB9B,EAAA,CAAAK,SAAA,EAAyB;UAAzBL,EAAA,CAAAQ,UAAA,SAAAwD,GAAA,CAAAlC,mBAAA,CAAyB;UAQwC9B,EAAA,CAAAK,SAAA,GAA6B;UAA7BL,EAAA,CAAAuE,iBAAA,CAAAvE,EAAA,CAAAO,WAAA,wBAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}