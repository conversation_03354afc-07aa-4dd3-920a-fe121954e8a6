{"ast": null, "code": "var _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) {\n  return typeof obj;\n} : function (obj) {\n  return obj && typeof Symbol === \"function\" && obj.constructor === Symbol ? \"symbol\" : typeof obj;\n};\n\n/**\n * Returns `true` if provided input is Element.\n * @name isElement\n * @param {*} [input]\n * @returns {boolean}\n */\nexport default function (input) {\n  return input != null && (typeof input === 'undefined' ? 'undefined' : _typeof(input)) === 'object' && input.nodeType === 1 && _typeof(input.style) === 'object' && _typeof(input.ownerDocument) === 'object';\n}", "map": {"version": 3, "names": ["_typeof", "Symbol", "iterator", "obj", "constructor", "input", "nodeType", "style", "ownerDocument"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/node_modules/iselement/module/index.js"], "sourcesContent": ["var _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol ? \"symbol\" : typeof obj; };\n\n/**\n * Returns `true` if provided input is Element.\n * @name isElement\n * @param {*} [input]\n * @returns {boolean}\n */\nexport default function (input) {\n  return input != null && (typeof input === 'undefined' ? 'undefined' : _typeof(input)) === 'object' && input.nodeType === 1 && _typeof(input.style) === 'object' && _typeof(input.ownerDocument) === 'object';\n}"], "mappings": "AAAA,IAAIA,OAAO,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAI,OAAOA,MAAM,CAACC,QAAQ,KAAK,QAAQ,GAAG,UAAUC,GAAG,EAAE;EAAE,OAAO,OAAOA,GAAG;AAAE,CAAC,GAAG,UAAUA,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAI,OAAOF,MAAM,KAAK,UAAU,IAAIE,GAAG,CAACC,WAAW,KAAKH,MAAM,GAAG,QAAQ,GAAG,OAAOE,GAAG;AAAE,CAAC;;AAEhP;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,UAAUE,KAAK,EAAE;EAC9B,OAAOA,KAAK,IAAI,IAAI,IAAI,CAAC,OAAOA,KAAK,KAAK,WAAW,GAAG,WAAW,GAAGL,OAAO,CAACK,KAAK,CAAC,MAAM,QAAQ,IAAIA,KAAK,CAACC,QAAQ,KAAK,CAAC,IAAIN,OAAO,CAACK,KAAK,CAACE,KAAK,CAAC,KAAK,QAAQ,IAAIP,OAAO,CAACK,KAAK,CAACG,aAAa,CAAC,KAAK,QAAQ;AAC9M", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}