{"ast": null, "code": "import { FormsModule } from '@angular/forms';\nimport { CommonModule } from '@angular/common';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { TrimInputValueModule } from '../../../../../../common/directives/trim-input-value/trim-input-value.module';\nimport { MatCurrencyDialogComponent } from './mat-currency-dialog.component';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport * as i0 from \"@angular/core\";\nexport const matModules = [MatTableModule, MatFormFieldModule, MatInputModule, MatButtonModule, MatDialogModule, MatCheckboxModule];\nexport class MatCurrencyDialogModule {\n  static {\n    this.ɵfac = function MatCurrencyDialogModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatCurrencyDialogModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: MatCurrencyDialogModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, TranslateModule, FormsModule, matModules, TrimInputValueModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(MatCurrencyDialogModule, {\n    declarations: [MatCurrencyDialogComponent],\n    imports: [CommonModule, TranslateModule, FormsModule, MatTableModule, MatFormFieldModule, MatInputModule, MatButtonModule, MatDialogModule, MatCheckboxModule, TrimInputValueModule]\n  });\n})();", "map": {"version": 3, "names": ["FormsModule", "CommonModule", "TranslateModule", "TrimInputValueModule", "MatCurrencyDialogComponent", "MatFormFieldModule", "MatTableModule", "MatCheckboxModule", "MatDialogModule", "MatInputModule", "MatButtonModule", "matModules", "MatCurrencyDialogModule", "declarations", "imports"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/mat-business-structure/dialogs/mat-currency-dialog/mat-currency-dialog.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { FormsModule } from '@angular/forms';\nimport { CommonModule } from '@angular/common';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { TrimInputValueModule } from '../../../../../../common/directives/trim-input-value/trim-input-value.module';\n\nimport { MatCurrencyDialogComponent } from './mat-currency-dialog.component';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\n\n\nexport const matModules = [\n  MatTableModule,\n  MatFormFieldModule,\n  MatInputModule,\n  MatButtonModule,\n  MatDialogModule,\n  MatCheckboxModule,\n];\n\n@NgModule({\n    imports: [\n        CommonModule,\n        TranslateModule,\n        FormsModule,\n        ...matModules,\n        TrimInputValueModule,\n    ],\n  declarations: [\n    MatCurrencyDialogComponent,\n  ],\n})\nexport class MatCurrencyDialogModule {\n}\n"], "mappings": "AACA,SAASA,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,oBAAoB,QAAQ,8EAA8E;AAEnH,SAASC,0BAA0B,QAAQ,iCAAiC;AAC5E,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;;AAG1D,OAAO,MAAMC,UAAU,GAAG,CACxBL,cAAc,EACdD,kBAAkB,EAClBI,cAAc,EACdC,eAAe,EACfF,eAAe,EACfD,iBAAiB,CAClB;AAcD,OAAM,MAAOK,uBAAuB;;;uCAAvBA,uBAAuB;IAAA;EAAA;;;YAAvBA;IAAuB;EAAA;;;gBAV5BX,YAAY,EACZC,eAAe,EACfF,WAAW,EACRW,UAAU,EACbR,oBAAoB;IAAA;EAAA;;;2EAMfS,uBAAuB;IAAAC,YAAA,GAHhCT,0BAA0B;IAAAU,OAAA,GAPtBb,YAAY,EACZC,eAAe,EACfF,WAAW,EAZjBM,cAAc,EACdD,kBAAkB,EAClBI,cAAc,EACdC,eAAe,EACfF,eAAe,EACfD,iBAAiB,EASXJ,oBAAoB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}