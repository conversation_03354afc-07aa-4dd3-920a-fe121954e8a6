{"ast": null, "code": "import { ScrollingModule } from '@angular/cdk/scrolling';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatRadioModule } from '@angular/material/radio';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatSortModule } from '@angular/material/sort';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiInputModule, SwuiPagePanelModule } from '@skywind-group/lib-swui';\nimport { TableVirtualScrollModule } from 'ng-table-virtual-scroll';\nimport { ControlMessagesModule } from '../../../../common/components/control-messages/control-messages.module';\nimport { TrimInputValueModule } from '../../../../common/directives/trim-input-value/trim-input-value.module';\nimport { DynamicDomainsResolver } from '../../../../common/services/resolvers/dynamic-domains.resolver';\nimport { DynamicEntitydomainResolver } from '../../../../common/services/resolvers/dynamic-entitydomain.resolver';\nimport { StaticDomainsResolver } from '../../../../common/services/resolvers/static-domains.resolver';\nimport { StaticEntityDomainResolver } from '../../../../common/services/resolvers/static-entity-domain-resolver.service';\nimport { StructureResolver } from '../../../../common/services/resolvers/structure.resolver';\nimport { DomainsManagementService } from '../../../domains-management/domains-management.service';\nimport { ActionEditComponent } from './action-edit/action-edit.component';\nimport { EntityBulkActionsComponent } from './entity-bulk-actions.component';\nimport { EntityBulkActionsRoutingModule } from './entity-bulk-actions.routing';\nimport { EntityLinkerComponent } from './entity-linker/entity-linker.component';\nimport { SwitchDomainComponent } from './switch-domain/switch-domain.component';\nimport { DomainsListComponent } from './switch-domain/domains-list/domains-list.component';\nimport * as i0 from \"@angular/core\";\nexport class EntityBulkActionsModule {\n  static {\n    this.ɵfac = function EntityBulkActionsModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || EntityBulkActionsModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: EntityBulkActionsModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [StructureResolver, DynamicDomainsResolver, StaticDomainsResolver, DynamicEntitydomainResolver, StaticEntityDomainResolver, DomainsManagementService],\n      imports: [CommonModule, ReactiveFormsModule, EntityBulkActionsRoutingModule, TranslateModule, SwuiPagePanelModule, MatCardModule, MatTabsModule, ScrollingModule, MatSelectModule, MatIconModule, ControlMessagesModule, SwuiInputModule, MatTableModule, MatDialogModule, MatButtonModule, MatRadioModule, MatProgressSpinnerModule, MatInputModule, MatSortModule, MatCheckboxModule, MatTooltipModule, TableVirtualScrollModule, TrimInputValueModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(EntityBulkActionsModule, {\n    declarations: [EntityBulkActionsComponent, ActionEditComponent, EntityLinkerComponent, SwitchDomainComponent, DomainsListComponent],\n    imports: [CommonModule, ReactiveFormsModule, EntityBulkActionsRoutingModule, TranslateModule, SwuiPagePanelModule, MatCardModule, MatTabsModule, ScrollingModule, MatSelectModule, MatIconModule, ControlMessagesModule, SwuiInputModule, MatTableModule, MatDialogModule, MatButtonModule, MatRadioModule, MatProgressSpinnerModule, MatInputModule, MatSortModule, MatCheckboxModule, MatTooltipModule, TableVirtualScrollModule, TrimInputValueModule],\n    exports: [EntityBulkActionsComponent]\n  });\n})();", "map": {"version": 3, "names": ["ScrollingModule", "CommonModule", "ReactiveFormsModule", "MatButtonModule", "MatCardModule", "MatCheckboxModule", "MatDialogModule", "MatIconModule", "MatInputModule", "MatProgressSpinnerModule", "MatRadioModule", "MatSelectModule", "MatSortModule", "MatTableModule", "MatTabsModule", "MatTooltipModule", "TranslateModule", "SwuiInputModule", "SwuiPagePanelModule", "TableVirtualScrollModule", "ControlMessagesModule", "TrimInputValueModule", "DynamicDomainsResolver", "DynamicEntitydomainResolver", "StaticDomainsResolver", "StaticEntityDomainResolver", "StructureResolver", "DomainsManagementService", "ActionEditComponent", "EntityBulkActionsComponent", "EntityBulkActionsRoutingModule", "EntityLinkerComponent", "SwitchDomainComponent", "DomainsListComponent", "EntityBulkActionsModule", "imports", "declarations", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entity-bulk-actions/entity-bulk-actions.module.ts"], "sourcesContent": ["import { ScrollingModule } from '@angular/cdk/scrolling';\nimport { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatRadioModule } from '@angular/material/radio';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatSortModule } from '@angular/material/sort';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiInputModule, SwuiPagePanelModule } from '@skywind-group/lib-swui';\nimport { TableVirtualScrollModule } from 'ng-table-virtual-scroll';\n\nimport { ControlMessagesModule } from '../../../../common/components/control-messages/control-messages.module';\nimport { TrimInputValueModule } from '../../../../common/directives/trim-input-value/trim-input-value.module';\nimport { DynamicDomainsResolver } from '../../../../common/services/resolvers/dynamic-domains.resolver';\nimport { DynamicEntitydomainResolver } from '../../../../common/services/resolvers/dynamic-entitydomain.resolver';\nimport { StaticDomainsResolver } from '../../../../common/services/resolvers/static-domains.resolver';\nimport { StaticEntityDomainResolver } from '../../../../common/services/resolvers/static-entity-domain-resolver.service';\nimport { StructureResolver } from '../../../../common/services/resolvers/structure.resolver';\nimport { DomainsManagementService } from '../../../domains-management/domains-management.service';\nimport { ActionEditComponent } from './action-edit/action-edit.component';\nimport { EntityBulkActionsComponent } from './entity-bulk-actions.component';\nimport { EntityBulkActionsRoutingModule } from './entity-bulk-actions.routing';\nimport { EntityLinkerComponent } from './entity-linker/entity-linker.component';\nimport { SwitchDomainComponent } from './switch-domain/switch-domain.component';\nimport { DomainsListComponent } from './switch-domain/domains-list/domains-list.component';\n\n@NgModule({\n    imports: [\n        CommonModule,\n        ReactiveFormsModule,\n        EntityBulkActionsRoutingModule,\n        TranslateModule,\n        SwuiPagePanelModule,\n        MatCardModule,\n        MatTabsModule,\n        ScrollingModule,\n        MatSelectModule,\n        MatIconModule,\n        ControlMessagesModule,\n        SwuiInputModule,\n        MatTableModule,\n        MatDialogModule,\n        MatButtonModule,\n        MatRadioModule,\n        MatProgressSpinnerModule,\n        MatInputModule,\n        MatSortModule,\n        MatCheckboxModule,\n        MatTooltipModule,\n        TableVirtualScrollModule,\n        TrimInputValueModule\n    ],\n  exports: [EntityBulkActionsComponent],\n  declarations: [\n    EntityBulkActionsComponent,\n    ActionEditComponent,\n    EntityLinkerComponent,\n    SwitchDomainComponent,\n    DomainsListComponent\n  ],\n  providers: [\n    StructureResolver,\n    DynamicDomainsResolver,\n    StaticDomainsResolver,\n    DynamicEntitydomainResolver,\n    StaticEntityDomainResolver,\n    DomainsManagementService,\n  ],\n})\nexport class EntityBulkActionsModule {\n}\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,wBAAwB;AACxD,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,eAAe,EAAEC,mBAAmB,QAAQ,yBAAyB;AAC9E,SAASC,wBAAwB,QAAQ,yBAAyB;AAElE,SAASC,qBAAqB,QAAQ,wEAAwE;AAC9G,SAASC,oBAAoB,QAAQ,wEAAwE;AAC7G,SAASC,sBAAsB,QAAQ,gEAAgE;AACvG,SAASC,2BAA2B,QAAQ,qEAAqE;AACjH,SAASC,qBAAqB,QAAQ,+DAA+D;AACrG,SAASC,0BAA0B,QAAQ,6EAA6E;AACxH,SAASC,iBAAiB,QAAQ,0DAA0D;AAC5F,SAASC,wBAAwB,QAAQ,wDAAwD;AACjG,SAASC,mBAAmB,QAAQ,qCAAqC;AACzE,SAASC,0BAA0B,QAAQ,iCAAiC;AAC5E,SAASC,8BAA8B,QAAQ,+BAA+B;AAC9E,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,oBAAoB,QAAQ,qDAAqD;;AA6C1F,OAAM,MAAOC,uBAAuB;;;uCAAvBA,uBAAuB;IAAA;EAAA;;;YAAvBA;IAAuB;EAAA;;;iBATvB,CACTR,iBAAiB,EACjBJ,sBAAsB,EACtBE,qBAAqB,EACrBD,2BAA2B,EAC3BE,0BAA0B,EAC1BE,wBAAwB,CACzB;MAAAQ,OAAA,GAvCKlC,YAAY,EACZC,mBAAmB,EACnB4B,8BAA8B,EAC9Bd,eAAe,EACfE,mBAAmB,EACnBd,aAAa,EACbU,aAAa,EACbd,eAAe,EACfW,eAAe,EACfJ,aAAa,EACba,qBAAqB,EACrBH,eAAe,EACfJ,cAAc,EACdP,eAAe,EACfH,eAAe,EACfO,cAAc,EACdD,wBAAwB,EACxBD,cAAc,EACdI,aAAa,EACbP,iBAAiB,EACjBU,gBAAgB,EAChBI,wBAAwB,EACxBE,oBAAoB;IAAA;EAAA;;;2EAmBfa,uBAAuB;IAAAE,YAAA,GAfhCP,0BAA0B,EAC1BD,mBAAmB,EACnBG,qBAAqB,EACrBC,qBAAqB,EACrBC,oBAAoB;IAAAE,OAAA,GA9BhBlC,YAAY,EACZC,mBAAmB,EACnB4B,8BAA8B,EAC9Bd,eAAe,EACfE,mBAAmB,EACnBd,aAAa,EACbU,aAAa,EACbd,eAAe,EACfW,eAAe,EACfJ,aAAa,EACba,qBAAqB,EACrBH,eAAe,EACfJ,cAAc,EACdP,eAAe,EACfH,eAAe,EACfO,cAAc,EACdD,wBAAwB,EACxBD,cAAc,EACdI,aAAa,EACbP,iBAAiB,EACjBU,gBAAgB,EAChBI,wBAAwB,EACxBE,oBAAoB;IAAAgB,OAAA,GAEhBR,0BAA0B;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}