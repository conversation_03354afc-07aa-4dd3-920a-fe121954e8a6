{"ast": null, "code": "import * as i2 from '@angular/cdk/a11y';\nimport { SelectionModel } from '@angular/cdk/collections';\nimport { RIGHT_ARROW, DOWN_ARROW, LEFT_ARROW, UP_ARROW, ENTER, SPACE } from '@angular/cdk/keycodes';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, forwardRef, EventEmitter, booleanAttribute, Directive, Optional, Inject, ContentChildren, Input, Output, Component, ViewEncapsulation, ChangeDetectionStrategy, Attribute, ViewChild, NgModule } from '@angular/core';\nimport * as i1 from '@angular/cdk/bidi';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { MatR<PERSON>ple, MatPseudoCheckbox, MatCommonModule, MatRippleModule } from '@angular/material/core';\n\n/**\n * Injection token that can be used to configure the\n * default options for all button toggles within an app.\n */\nconst _c0 = [\"button\"];\nconst _c1 = [\"*\"];\nfunction MatButtonToggle_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-pseudo-checkbox\", 3);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.disabled);\n  }\n}\nfunction MatButtonToggle_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-pseudo-checkbox\", 3);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.disabled);\n  }\n}\nconst MAT_BUTTON_TOGGLE_DEFAULT_OPTIONS = new InjectionToken('MAT_BUTTON_TOGGLE_DEFAULT_OPTIONS', {\n  providedIn: 'root',\n  factory: MAT_BUTTON_TOGGLE_GROUP_DEFAULT_OPTIONS_FACTORY\n});\nfunction MAT_BUTTON_TOGGLE_GROUP_DEFAULT_OPTIONS_FACTORY() {\n  return {\n    hideSingleSelectionIndicator: false,\n    hideMultipleSelectionIndicator: false,\n    disabledInteractive: false\n  };\n}\n/**\n * Injection token that can be used to reference instances of `MatButtonToggleGroup`.\n * It serves as alternative token to the actual `MatButtonToggleGroup` class which\n * could cause unnecessary retention of the class and its component metadata.\n */\nconst MAT_BUTTON_TOGGLE_GROUP = new InjectionToken('MatButtonToggleGroup');\n/**\n * Provider Expression that allows mat-button-toggle-group to register as a ControlValueAccessor.\n * This allows it to support [(ngModel)].\n * @docs-private\n */\nconst MAT_BUTTON_TOGGLE_GROUP_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => MatButtonToggleGroup),\n  multi: true\n};\n// Counter used to generate unique IDs.\nlet uniqueIdCounter = 0;\n/** Change event object emitted by button toggle. */\nclass MatButtonToggleChange {\n  constructor(/** The button toggle that emits the event. */\n  source, /** The value assigned to the button toggle. */\n  value) {\n    this.source = source;\n    this.value = value;\n  }\n}\n/** Exclusive selection button toggle group that behaves like a radio-button group. */\nclass MatButtonToggleGroup {\n  /** `name` attribute for the underlying `input` element. */\n  get name() {\n    return this._name;\n  }\n  set name(value) {\n    this._name = value;\n    this._markButtonsForCheck();\n  }\n  /** Value of the toggle group. */\n  get value() {\n    const selected = this._selectionModel ? this._selectionModel.selected : [];\n    if (this.multiple) {\n      return selected.map(toggle => toggle.value);\n    }\n    return selected[0] ? selected[0].value : undefined;\n  }\n  set value(newValue) {\n    this._setSelectionByValue(newValue);\n    this.valueChange.emit(this.value);\n  }\n  /** Selected button toggles in the group. */\n  get selected() {\n    const selected = this._selectionModel ? this._selectionModel.selected : [];\n    return this.multiple ? selected : selected[0] || null;\n  }\n  /** Whether multiple button toggles can be selected. */\n  get multiple() {\n    return this._multiple;\n  }\n  set multiple(value) {\n    this._multiple = value;\n    this._markButtonsForCheck();\n  }\n  /** Whether multiple button toggle group is disabled. */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    this._disabled = value;\n    this._markButtonsForCheck();\n  }\n  /** Whether buttons in the group should be interactive while they're disabled. */\n  get disabledInteractive() {\n    return this._disabledInteractive;\n  }\n  set disabledInteractive(value) {\n    this._disabledInteractive = value;\n    this._markButtonsForCheck();\n  }\n  /** The layout direction of the toggle button group. */\n  get dir() {\n    return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n  }\n  /** Whether checkmark indicator for single-selection button toggle groups is hidden. */\n  get hideSingleSelectionIndicator() {\n    return this._hideSingleSelectionIndicator;\n  }\n  set hideSingleSelectionIndicator(value) {\n    this._hideSingleSelectionIndicator = value;\n    this._markButtonsForCheck();\n  }\n  /** Whether checkmark indicator for multiple-selection button toggle groups is hidden. */\n  get hideMultipleSelectionIndicator() {\n    return this._hideMultipleSelectionIndicator;\n  }\n  set hideMultipleSelectionIndicator(value) {\n    this._hideMultipleSelectionIndicator = value;\n    this._markButtonsForCheck();\n  }\n  constructor(_changeDetector, defaultOptions, _dir) {\n    this._changeDetector = _changeDetector;\n    this._dir = _dir;\n    this._multiple = false;\n    this._disabled = false;\n    this._disabledInteractive = false;\n    /**\n     * The method to be called in order to update ngModel.\n     * Now `ngModel` binding is not supported in multiple selection mode.\n     */\n    this._controlValueAccessorChangeFn = () => {};\n    /** onTouch function registered via registerOnTouch (ControlValueAccessor). */\n    this._onTouched = () => {};\n    this._name = `mat-button-toggle-group-${uniqueIdCounter++}`;\n    /**\n     * Event that emits whenever the value of the group changes.\n     * Used to facilitate two-way data binding.\n     * @docs-private\n     */\n    this.valueChange = new EventEmitter();\n    /** Event emitted when the group's value changes. */\n    this.change = new EventEmitter();\n    this.appearance = defaultOptions && defaultOptions.appearance ? defaultOptions.appearance : 'standard';\n    this.hideSingleSelectionIndicator = defaultOptions?.hideSingleSelectionIndicator ?? false;\n    this.hideMultipleSelectionIndicator = defaultOptions?.hideMultipleSelectionIndicator ?? false;\n  }\n  ngOnInit() {\n    this._selectionModel = new SelectionModel(this.multiple, undefined, false);\n  }\n  ngAfterContentInit() {\n    this._selectionModel.select(...this._buttonToggles.filter(toggle => toggle.checked));\n    if (!this.multiple) {\n      this._initializeTabIndex();\n    }\n  }\n  /**\n   * Sets the model value. Implemented as part of ControlValueAccessor.\n   * @param value Value to be set to the model.\n   */\n  writeValue(value) {\n    this.value = value;\n    this._changeDetector.markForCheck();\n  }\n  // Implemented as part of ControlValueAccessor.\n  registerOnChange(fn) {\n    this._controlValueAccessorChangeFn = fn;\n  }\n  // Implemented as part of ControlValueAccessor.\n  registerOnTouched(fn) {\n    this._onTouched = fn;\n  }\n  // Implemented as part of ControlValueAccessor.\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n  }\n  /** Handle keydown event calling to single-select button toggle. */\n  _keydown(event) {\n    if (this.multiple || this.disabled) {\n      return;\n    }\n    const target = event.target;\n    const buttonId = target.id;\n    const index = this._buttonToggles.toArray().findIndex(toggle => {\n      return toggle.buttonId === buttonId;\n    });\n    let nextButton = null;\n    switch (event.keyCode) {\n      case SPACE:\n      case ENTER:\n        nextButton = this._buttonToggles.get(index) || null;\n        break;\n      case UP_ARROW:\n        nextButton = this._getNextButton(index, -1);\n        break;\n      case LEFT_ARROW:\n        nextButton = this._getNextButton(index, this.dir === 'ltr' ? -1 : 1);\n        break;\n      case DOWN_ARROW:\n        nextButton = this._getNextButton(index, 1);\n        break;\n      case RIGHT_ARROW:\n        nextButton = this._getNextButton(index, this.dir === 'ltr' ? 1 : -1);\n        break;\n      default:\n        return;\n    }\n    if (nextButton) {\n      event.preventDefault();\n      nextButton._onButtonClick();\n      nextButton.focus();\n    }\n  }\n  /** Dispatch change event with current selection and group value. */\n  _emitChangeEvent(toggle) {\n    const event = new MatButtonToggleChange(toggle, this.value);\n    this._rawValue = event.value;\n    this._controlValueAccessorChangeFn(event.value);\n    this.change.emit(event);\n  }\n  /**\n   * Syncs a button toggle's selected state with the model value.\n   * @param toggle Toggle to be synced.\n   * @param select Whether the toggle should be selected.\n   * @param isUserInput Whether the change was a result of a user interaction.\n   * @param deferEvents Whether to defer emitting the change events.\n   */\n  _syncButtonToggle(toggle, select, isUserInput = false, deferEvents = false) {\n    // Deselect the currently-selected toggle, if we're in single-selection\n    // mode and the button being toggled isn't selected at the moment.\n    if (!this.multiple && this.selected && !toggle.checked) {\n      this.selected.checked = false;\n    }\n    if (this._selectionModel) {\n      if (select) {\n        this._selectionModel.select(toggle);\n      } else {\n        this._selectionModel.deselect(toggle);\n      }\n    } else {\n      deferEvents = true;\n    }\n    // We need to defer in some cases in order to avoid \"changed after checked errors\", however\n    // the side-effect is that we may end up updating the model value out of sequence in others\n    // The `deferEvents` flag allows us to decide whether to do it on a case-by-case basis.\n    if (deferEvents) {\n      Promise.resolve().then(() => this._updateModelValue(toggle, isUserInput));\n    } else {\n      this._updateModelValue(toggle, isUserInput);\n    }\n  }\n  /** Checks whether a button toggle is selected. */\n  _isSelected(toggle) {\n    return this._selectionModel && this._selectionModel.isSelected(toggle);\n  }\n  /** Determines whether a button toggle should be checked on init. */\n  _isPrechecked(toggle) {\n    if (typeof this._rawValue === 'undefined') {\n      return false;\n    }\n    if (this.multiple && Array.isArray(this._rawValue)) {\n      return this._rawValue.some(value => toggle.value != null && value === toggle.value);\n    }\n    return toggle.value === this._rawValue;\n  }\n  /** Initializes the tabindex attribute using the radio pattern. */\n  _initializeTabIndex() {\n    this._buttonToggles.forEach(toggle => {\n      toggle.tabIndex = -1;\n    });\n    if (this.selected) {\n      this.selected.tabIndex = 0;\n    } else {\n      for (let i = 0; i < this._buttonToggles.length; i++) {\n        const toggle = this._buttonToggles.get(i);\n        if (!toggle.disabled) {\n          toggle.tabIndex = 0;\n          break;\n        }\n      }\n    }\n    this._markButtonsForCheck();\n  }\n  /** Obtain the subsequent toggle to which the focus shifts. */\n  _getNextButton(startIndex, offset) {\n    const items = this._buttonToggles;\n    for (let i = 1; i <= items.length; i++) {\n      const index = (startIndex + offset * i + items.length) % items.length;\n      const item = items.get(index);\n      if (item && !item.disabled) {\n        return item;\n      }\n    }\n    return null;\n  }\n  /** Updates the selection state of the toggles in the group based on a value. */\n  _setSelectionByValue(value) {\n    this._rawValue = value;\n    if (!this._buttonToggles) {\n      return;\n    }\n    if (this.multiple && value) {\n      if (!Array.isArray(value) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error('Value must be an array in multiple-selection mode.');\n      }\n      this._clearSelection();\n      value.forEach(currentValue => this._selectValue(currentValue));\n    } else {\n      this._clearSelection();\n      this._selectValue(value);\n    }\n  }\n  /** Clears the selected toggles. */\n  _clearSelection() {\n    this._selectionModel.clear();\n    this._buttonToggles.forEach(toggle => {\n      toggle.checked = false;\n      // If the button toggle is in single select mode, initialize the tabIndex.\n      if (!this.multiple) {\n        toggle.tabIndex = -1;\n      }\n    });\n  }\n  /** Selects a value if there's a toggle that corresponds to it. */\n  _selectValue(value) {\n    const correspondingOption = this._buttonToggles.find(toggle => {\n      return toggle.value != null && toggle.value === value;\n    });\n    if (correspondingOption) {\n      correspondingOption.checked = true;\n      this._selectionModel.select(correspondingOption);\n      if (!this.multiple) {\n        // If the button toggle is in single select mode, reset the tabIndex.\n        correspondingOption.tabIndex = 0;\n      }\n    }\n  }\n  /** Syncs up the group's value with the model and emits the change event. */\n  _updateModelValue(toggle, isUserInput) {\n    // Only emit the change event for user input.\n    if (isUserInput) {\n      this._emitChangeEvent(toggle);\n    }\n    // Note: we emit this one no matter whether it was a user interaction, because\n    // it is used by Angular to sync up the two-way data binding.\n    this.valueChange.emit(this.value);\n  }\n  /** Marks all of the child button toggles to be checked. */\n  _markButtonsForCheck() {\n    this._buttonToggles?.forEach(toggle => toggle._markForCheck());\n  }\n  static {\n    this.ɵfac = function MatButtonToggleGroup_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatButtonToggleGroup)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MAT_BUTTON_TOGGLE_DEFAULT_OPTIONS, 8), i0.ɵɵdirectiveInject(i1.Directionality, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatButtonToggleGroup,\n      selectors: [[\"mat-button-toggle-group\"]],\n      contentQueries: function MatButtonToggleGroup_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MatButtonToggle, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._buttonToggles = _t);\n        }\n      },\n      hostAttrs: [1, \"mat-button-toggle-group\"],\n      hostVars: 6,\n      hostBindings: function MatButtonToggleGroup_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown\", function MatButtonToggleGroup_keydown_HostBindingHandler($event) {\n            return ctx._keydown($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"role\", ctx.multiple ? \"group\" : \"radiogroup\")(\"aria-disabled\", ctx.disabled);\n          i0.ɵɵclassProp(\"mat-button-toggle-vertical\", ctx.vertical)(\"mat-button-toggle-group-appearance-standard\", ctx.appearance === \"standard\");\n        }\n      },\n      inputs: {\n        appearance: \"appearance\",\n        name: \"name\",\n        vertical: [2, \"vertical\", \"vertical\", booleanAttribute],\n        value: \"value\",\n        multiple: [2, \"multiple\", \"multiple\", booleanAttribute],\n        disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n        disabledInteractive: [2, \"disabledInteractive\", \"disabledInteractive\", booleanAttribute],\n        hideSingleSelectionIndicator: [2, \"hideSingleSelectionIndicator\", \"hideSingleSelectionIndicator\", booleanAttribute],\n        hideMultipleSelectionIndicator: [2, \"hideMultipleSelectionIndicator\", \"hideMultipleSelectionIndicator\", booleanAttribute]\n      },\n      outputs: {\n        valueChange: \"valueChange\",\n        change: \"change\"\n      },\n      exportAs: [\"matButtonToggleGroup\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([MAT_BUTTON_TOGGLE_GROUP_VALUE_ACCESSOR, {\n        provide: MAT_BUTTON_TOGGLE_GROUP,\n        useExisting: MatButtonToggleGroup\n      }]), i0.ɵɵInputTransformsFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatButtonToggleGroup, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-button-toggle-group',\n      providers: [MAT_BUTTON_TOGGLE_GROUP_VALUE_ACCESSOR, {\n        provide: MAT_BUTTON_TOGGLE_GROUP,\n        useExisting: MatButtonToggleGroup\n      }],\n      host: {\n        'class': 'mat-button-toggle-group',\n        '(keydown)': '_keydown($event)',\n        '[attr.role]': \"multiple ? 'group' : 'radiogroup'\",\n        '[attr.aria-disabled]': 'disabled',\n        '[class.mat-button-toggle-vertical]': 'vertical',\n        '[class.mat-button-toggle-group-appearance-standard]': 'appearance === \"standard\"'\n      },\n      exportAs: 'matButtonToggleGroup',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [MAT_BUTTON_TOGGLE_DEFAULT_OPTIONS]\n    }]\n  }, {\n    type: i1.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    _buttonToggles: [{\n      type: ContentChildren,\n      args: [forwardRef(() => MatButtonToggle), {\n        // Note that this would technically pick up toggles\n        // from nested groups, but that's not a case that we support.\n        descendants: true\n      }]\n    }],\n    appearance: [{\n      type: Input\n    }],\n    name: [{\n      type: Input\n    }],\n    vertical: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    value: [{\n      type: Input\n    }],\n    valueChange: [{\n      type: Output\n    }],\n    multiple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disabledInteractive: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    change: [{\n      type: Output\n    }],\n    hideSingleSelectionIndicator: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    hideMultipleSelectionIndicator: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n/** Single button inside of a toggle group. */\nclass MatButtonToggle {\n  /** Unique ID for the underlying `button` element. */\n  get buttonId() {\n    return `${this.id}-button`;\n  }\n  /** Tabindex of the toggle. */\n  get tabIndex() {\n    return this._tabIndex;\n  }\n  set tabIndex(value) {\n    this._tabIndex = value;\n    this._markForCheck();\n  }\n  /** The appearance style of the button. */\n  get appearance() {\n    return this.buttonToggleGroup ? this.buttonToggleGroup.appearance : this._appearance;\n  }\n  set appearance(value) {\n    this._appearance = value;\n  }\n  /** Whether the button is checked. */\n  get checked() {\n    return this.buttonToggleGroup ? this.buttonToggleGroup._isSelected(this) : this._checked;\n  }\n  set checked(value) {\n    if (value !== this._checked) {\n      this._checked = value;\n      if (this.buttonToggleGroup) {\n        this.buttonToggleGroup._syncButtonToggle(this, this._checked);\n      }\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  /** Whether the button is disabled. */\n  get disabled() {\n    return this._disabled || this.buttonToggleGroup && this.buttonToggleGroup.disabled;\n  }\n  set disabled(value) {\n    this._disabled = value;\n  }\n  /** Whether the button should remain interactive when it is disabled. */\n  get disabledInteractive() {\n    return this._disabledInteractive || this.buttonToggleGroup !== null && this.buttonToggleGroup.disabledInteractive;\n  }\n  set disabledInteractive(value) {\n    this._disabledInteractive = value;\n  }\n  constructor(toggleGroup, _changeDetectorRef, _elementRef, _focusMonitor, defaultTabIndex, defaultOptions) {\n    this._changeDetectorRef = _changeDetectorRef;\n    this._elementRef = _elementRef;\n    this._focusMonitor = _focusMonitor;\n    this._checked = false;\n    /**\n     * Users can specify the `aria-labelledby` attribute which will be forwarded to the input element\n     */\n    this.ariaLabelledby = null;\n    this._disabled = false;\n    /** Event emitted when the group value changes. */\n    this.change = new EventEmitter();\n    const parsedTabIndex = Number(defaultTabIndex);\n    this.tabIndex = parsedTabIndex || parsedTabIndex === 0 ? parsedTabIndex : null;\n    this.buttonToggleGroup = toggleGroup;\n    this.appearance = defaultOptions && defaultOptions.appearance ? defaultOptions.appearance : 'standard';\n    this.disabledInteractive = defaultOptions?.disabledInteractive ?? false;\n  }\n  ngOnInit() {\n    const group = this.buttonToggleGroup;\n    this.id = this.id || `mat-button-toggle-${uniqueIdCounter++}`;\n    if (group) {\n      if (group._isPrechecked(this)) {\n        this.checked = true;\n      } else if (group._isSelected(this) !== this._checked) {\n        // As side effect of the circular dependency between the toggle group and the button,\n        // we may end up in a state where the button is supposed to be checked on init, but it\n        // isn't, because the checked value was assigned too early. This can happen when Ivy\n        // assigns the static input value before the `ngOnInit` has run.\n        group._syncButtonToggle(this, this._checked);\n      }\n    }\n  }\n  ngAfterViewInit() {\n    this._focusMonitor.monitor(this._elementRef, true);\n  }\n  ngOnDestroy() {\n    const group = this.buttonToggleGroup;\n    this._focusMonitor.stopMonitoring(this._elementRef);\n    // Remove the toggle from the selection once it's destroyed. Needs to happen\n    // on the next tick in order to avoid \"changed after checked\" errors.\n    if (group && group._isSelected(this)) {\n      group._syncButtonToggle(this, false, false, true);\n    }\n  }\n  /** Focuses the button. */\n  focus(options) {\n    this._buttonElement.nativeElement.focus(options);\n  }\n  /** Checks the button toggle due to an interaction with the underlying native button. */\n  _onButtonClick() {\n    if (this.disabled) {\n      return;\n    }\n    const newChecked = this.isSingleSelector() ? true : !this._checked;\n    if (newChecked !== this._checked) {\n      this._checked = newChecked;\n      if (this.buttonToggleGroup) {\n        this.buttonToggleGroup._syncButtonToggle(this, this._checked, true);\n        this.buttonToggleGroup._onTouched();\n      }\n    }\n    if (this.isSingleSelector()) {\n      const focusable = this.buttonToggleGroup._buttonToggles.find(toggle => {\n        return toggle.tabIndex === 0;\n      });\n      // Modify the tabindex attribute of the last focusable button toggle to -1.\n      if (focusable) {\n        focusable.tabIndex = -1;\n      }\n      // Modify the tabindex attribute of the presently selected button toggle to 0.\n      this.tabIndex = 0;\n    }\n    // Emit a change event when it's the single selector\n    this.change.emit(new MatButtonToggleChange(this, this.value));\n  }\n  /**\n   * Marks the button toggle as needing checking for change detection.\n   * This method is exposed because the parent button toggle group will directly\n   * update bound properties of the radio button.\n   */\n  _markForCheck() {\n    // When the group value changes, the button will not be notified.\n    // Use `markForCheck` to explicit update button toggle's status.\n    this._changeDetectorRef.markForCheck();\n  }\n  /** Gets the name that should be assigned to the inner DOM node. */\n  _getButtonName() {\n    if (this.isSingleSelector()) {\n      return this.buttonToggleGroup.name;\n    }\n    return this.name || null;\n  }\n  /** Whether the toggle is in single selection mode. */\n  isSingleSelector() {\n    return this.buttonToggleGroup && !this.buttonToggleGroup.multiple;\n  }\n  static {\n    this.ɵfac = function MatButtonToggle_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatButtonToggle)(i0.ɵɵdirectiveInject(MAT_BUTTON_TOGGLE_GROUP, 8), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i2.FocusMonitor), i0.ɵɵinjectAttribute('tabindex'), i0.ɵɵdirectiveInject(MAT_BUTTON_TOGGLE_DEFAULT_OPTIONS, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatButtonToggle,\n      selectors: [[\"mat-button-toggle\"]],\n      viewQuery: function MatButtonToggle_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._buttonElement = _t.first);\n        }\n      },\n      hostAttrs: [\"role\", \"presentation\", 1, \"mat-button-toggle\"],\n      hostVars: 14,\n      hostBindings: function MatButtonToggle_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"focus\", function MatButtonToggle_focus_HostBindingHandler() {\n            return ctx.focus();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"aria-label\", null)(\"aria-labelledby\", null)(\"id\", ctx.id)(\"name\", null);\n          i0.ɵɵclassProp(\"mat-button-toggle-standalone\", !ctx.buttonToggleGroup)(\"mat-button-toggle-checked\", ctx.checked)(\"mat-button-toggle-disabled\", ctx.disabled)(\"mat-button-toggle-disabled-interactive\", ctx.disabledInteractive)(\"mat-button-toggle-appearance-standard\", ctx.appearance === \"standard\");\n        }\n      },\n      inputs: {\n        ariaLabel: [0, \"aria-label\", \"ariaLabel\"],\n        ariaLabelledby: [0, \"aria-labelledby\", \"ariaLabelledby\"],\n        id: \"id\",\n        name: \"name\",\n        value: \"value\",\n        tabIndex: \"tabIndex\",\n        disableRipple: [2, \"disableRipple\", \"disableRipple\", booleanAttribute],\n        appearance: \"appearance\",\n        checked: [2, \"checked\", \"checked\", booleanAttribute],\n        disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n        disabledInteractive: [2, \"disabledInteractive\", \"disabledInteractive\", booleanAttribute]\n      },\n      outputs: {\n        change: \"change\"\n      },\n      exportAs: [\"matButtonToggle\"],\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c1,\n      decls: 8,\n      vars: 14,\n      consts: [[\"button\", \"\"], [\"type\", \"button\", 1, \"mat-button-toggle-button\", \"mat-focus-indicator\", 3, \"click\", \"id\", \"disabled\"], [1, \"mat-button-toggle-label-content\"], [\"state\", \"checked\", \"aria-hidden\", \"true\", \"appearance\", \"minimal\", 1, \"mat-mdc-option-pseudo-checkbox\", 3, \"disabled\"], [1, \"mat-button-toggle-focus-overlay\"], [\"matRipple\", \"\", 1, \"mat-button-toggle-ripple\", 3, \"matRippleTrigger\", \"matRippleDisabled\"]],\n      template: function MatButtonToggle_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"button\", 1, 0);\n          i0.ɵɵlistener(\"click\", function MatButtonToggle_Template_button_click_0_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx._onButtonClick());\n          });\n          i0.ɵɵelementStart(2, \"span\", 2);\n          i0.ɵɵtemplate(3, MatButtonToggle_Conditional_3_Template, 1, 1, \"mat-pseudo-checkbox\", 3)(4, MatButtonToggle_Conditional_4_Template, 1, 1, \"mat-pseudo-checkbox\", 3);\n          i0.ɵɵprojection(5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(6, \"span\", 4)(7, \"span\", 5);\n        }\n        if (rf & 2) {\n          const button_r3 = i0.ɵɵreference(1);\n          i0.ɵɵproperty(\"id\", ctx.buttonId)(\"disabled\", ctx.disabled && !ctx.disabledInteractive || null);\n          i0.ɵɵattribute(\"role\", ctx.isSingleSelector() ? \"radio\" : \"button\")(\"tabindex\", ctx.disabled && !ctx.disabledInteractive ? -1 : ctx.tabIndex)(\"aria-pressed\", !ctx.isSingleSelector() ? ctx.checked : null)(\"aria-checked\", ctx.isSingleSelector() ? ctx.checked : null)(\"name\", ctx._getButtonName())(\"aria-label\", ctx.ariaLabel)(\"aria-labelledby\", ctx.ariaLabelledby)(\"aria-disabled\", ctx.disabled && ctx.disabledInteractive ? \"true\" : null);\n          i0.ɵɵadvance(3);\n          i0.ɵɵconditional(ctx.buttonToggleGroup && ctx.checked && !ctx.buttonToggleGroup.multiple && !ctx.buttonToggleGroup.hideSingleSelectionIndicator ? 3 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.buttonToggleGroup && ctx.checked && ctx.buttonToggleGroup.multiple && !ctx.buttonToggleGroup.hideMultipleSelectionIndicator ? 4 : -1);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"matRippleTrigger\", button_r3)(\"matRippleDisabled\", ctx.disableRipple || ctx.disabled);\n        }\n      },\n      dependencies: [MatRipple, MatPseudoCheckbox],\n      styles: [\".mat-button-toggle-standalone,.mat-button-toggle-group{position:relative;display:inline-flex;flex-direction:row;white-space:nowrap;overflow:hidden;-webkit-tap-highlight-color:rgba(0,0,0,0);transform:translateZ(0);border-radius:var(--mat-legacy-button-toggle-shape)}.mat-button-toggle-standalone:not([class*=mat-elevation-z]),.mat-button-toggle-group:not([class*=mat-elevation-z]){box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12)}.cdk-high-contrast-active .mat-button-toggle-standalone,.cdk-high-contrast-active .mat-button-toggle-group{outline:solid 1px}.mat-button-toggle-standalone.mat-button-toggle-appearance-standard,.mat-button-toggle-group-appearance-standard{border-radius:var(--mat-standard-button-toggle-shape, var(--mat-app-corner-full));border:solid 1px var(--mat-standard-button-toggle-divider-color, var(--mat-app-outline))}.mat-button-toggle-standalone.mat-button-toggle-appearance-standard .mat-pseudo-checkbox,.mat-button-toggle-group-appearance-standard .mat-pseudo-checkbox{--mat-minimal-pseudo-checkbox-selected-checkmark-color: var(--mat-standard-button-toggle-selected-state-text-color, var(--mat-app-on-secondary-container))}.mat-button-toggle-standalone.mat-button-toggle-appearance-standard:not([class*=mat-elevation-z]),.mat-button-toggle-group-appearance-standard:not([class*=mat-elevation-z]){box-shadow:none}.cdk-high-contrast-active .mat-button-toggle-standalone.mat-button-toggle-appearance-standard,.cdk-high-contrast-active .mat-button-toggle-group-appearance-standard{outline:0}.mat-button-toggle-vertical{flex-direction:column}.mat-button-toggle-vertical .mat-button-toggle-label-content{display:block}.mat-button-toggle{white-space:nowrap;position:relative;color:var(--mat-legacy-button-toggle-text-color);font-family:var(--mat-legacy-button-toggle-label-text-font);font-size:var(--mat-legacy-button-toggle-label-text-size);line-height:var(--mat-legacy-button-toggle-label-text-line-height);font-weight:var(--mat-legacy-button-toggle-label-text-weight);letter-spacing:var(--mat-legacy-button-toggle-label-text-tracking);--mat-minimal-pseudo-checkbox-selected-checkmark-color: var(--mat-legacy-button-toggle-selected-state-text-color)}.mat-button-toggle.cdk-keyboard-focused .mat-button-toggle-focus-overlay{opacity:var(--mat-legacy-button-toggle-focus-state-layer-opacity)}.mat-button-toggle .mat-icon svg{vertical-align:top}.mat-button-toggle .mat-pseudo-checkbox{margin-right:12px}[dir=rtl] .mat-button-toggle .mat-pseudo-checkbox{margin-right:0;margin-left:12px}.mat-button-toggle-checked{color:var(--mat-legacy-button-toggle-selected-state-text-color);background-color:var(--mat-legacy-button-toggle-selected-state-background-color)}.mat-button-toggle-disabled{pointer-events:none;color:var(--mat-legacy-button-toggle-disabled-state-text-color);background-color:var(--mat-legacy-button-toggle-disabled-state-background-color);--mat-minimal-pseudo-checkbox-disabled-selected-checkmark-color: var(--mat-legacy-button-toggle-disabled-state-text-color)}.mat-button-toggle-disabled.mat-button-toggle-checked{background-color:var(--mat-legacy-button-toggle-disabled-selected-state-background-color)}.mat-button-toggle-disabled-interactive{pointer-events:auto}.mat-button-toggle-appearance-standard{color:var(--mat-standard-button-toggle-text-color, var(--mat-app-on-surface));background-color:var(--mat-standard-button-toggle-background-color);font-family:var(--mat-standard-button-toggle-label-text-font, var(--mat-app-label-large-font));font-size:var(--mat-standard-button-toggle-label-text-size, var(--mat-app-label-large-size));line-height:var(--mat-standard-button-toggle-label-text-line-height, var(--mat-app-label-large-line-height));font-weight:var(--mat-standard-button-toggle-label-text-weight, var(--mat-app-label-large-weight));letter-spacing:var(--mat-standard-button-toggle-label-text-tracking, var(--mat-app-label-large-tracking))}.mat-button-toggle-group-appearance-standard .mat-button-toggle-appearance-standard+.mat-button-toggle-appearance-standard{border-left:solid 1px var(--mat-standard-button-toggle-divider-color, var(--mat-app-outline))}[dir=rtl] .mat-button-toggle-group-appearance-standard .mat-button-toggle-appearance-standard+.mat-button-toggle-appearance-standard{border-left:none;border-right:solid 1px var(--mat-standard-button-toggle-divider-color, var(--mat-app-outline))}.mat-button-toggle-group-appearance-standard.mat-button-toggle-vertical .mat-button-toggle-appearance-standard+.mat-button-toggle-appearance-standard{border-left:none;border-right:none;border-top:solid 1px var(--mat-standard-button-toggle-divider-color, var(--mat-app-outline))}.mat-button-toggle-appearance-standard.mat-button-toggle-checked{color:var(--mat-standard-button-toggle-selected-state-text-color, var(--mat-app-on-secondary-container));background-color:var(--mat-standard-button-toggle-selected-state-background-color, var(--mat-app-secondary-container))}.mat-button-toggle-appearance-standard.mat-button-toggle-disabled{color:var(--mat-standard-button-toggle-disabled-state-text-color);background-color:var(--mat-standard-button-toggle-disabled-state-background-color)}.mat-button-toggle-appearance-standard.mat-button-toggle-disabled .mat-pseudo-checkbox{--mat-minimal-pseudo-checkbox-disabled-selected-checkmark-color: var(--mat-standard-button-toggle-disabled-selected-state-text-color)}.mat-button-toggle-appearance-standard.mat-button-toggle-disabled.mat-button-toggle-checked{color:var(--mat-standard-button-toggle-disabled-selected-state-text-color);background-color:var(--mat-standard-button-toggle-disabled-selected-state-background-color)}.mat-button-toggle-appearance-standard .mat-button-toggle-focus-overlay{background-color:var(--mat-standard-button-toggle-state-layer-color, var(--mat-app-on-surface))}.mat-button-toggle-appearance-standard:hover .mat-button-toggle-focus-overlay{opacity:var(--mat-standard-button-toggle-hover-state-layer-opacity, var(--mat-app-hover-state-layer-opacity))}.mat-button-toggle-appearance-standard.cdk-keyboard-focused .mat-button-toggle-focus-overlay{opacity:var(--mat-standard-button-toggle-focus-state-layer-opacity, var(--mat-app-focus-state-layer-opacity))}@media(hover: none){.mat-button-toggle-appearance-standard:hover .mat-button-toggle-focus-overlay{display:none}}.mat-button-toggle-label-content{-webkit-user-select:none;user-select:none;display:inline-block;padding:0 16px;line-height:var(--mat-legacy-button-toggle-height);position:relative}.mat-button-toggle-appearance-standard .mat-button-toggle-label-content{padding:0 12px;line-height:var(--mat-standard-button-toggle-height)}.mat-button-toggle-label-content>*{vertical-align:middle}.mat-button-toggle-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:inherit;pointer-events:none;opacity:0;background-color:var(--mat-legacy-button-toggle-state-layer-color)}.cdk-high-contrast-active .mat-button-toggle-checked .mat-button-toggle-focus-overlay{border-bottom:solid 500px;opacity:.5;height:0}.cdk-high-contrast-active .mat-button-toggle-checked:hover .mat-button-toggle-focus-overlay{opacity:.6}.cdk-high-contrast-active .mat-button-toggle-checked.mat-button-toggle-appearance-standard .mat-button-toggle-focus-overlay{border-bottom:solid 500px}.mat-button-toggle .mat-button-toggle-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-button-toggle-button{border:0;background:none;color:inherit;padding:0;margin:0;font:inherit;outline:none;width:100%;cursor:pointer}.mat-button-toggle-disabled .mat-button-toggle-button{cursor:default}.mat-button-toggle-button::-moz-focus-inner{border:0}.mat-button-toggle-standalone.mat-button-toggle-appearance-standard{--mat-focus-indicator-border-radius:var(--mat-standard-button-toggle-shape, var(--mat-app-corner-full))}.mat-button-toggle-group-appearance-standard .mat-button-toggle:last-of-type .mat-button-toggle-button::before{border-top-right-radius:var(--mat-standard-button-toggle-shape, var(--mat-app-corner-full));border-bottom-right-radius:var(--mat-standard-button-toggle-shape, var(--mat-app-corner-full))}.mat-button-toggle-group-appearance-standard .mat-button-toggle:first-of-type .mat-button-toggle-button::before{border-top-left-radius:var(--mat-standard-button-toggle-shape, var(--mat-app-corner-full));border-bottom-left-radius:var(--mat-standard-button-toggle-shape, var(--mat-app-corner-full))}\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatButtonToggle, [{\n    type: Component,\n    args: [{\n      selector: 'mat-button-toggle',\n      encapsulation: ViewEncapsulation.None,\n      exportAs: 'matButtonToggle',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        '[class.mat-button-toggle-standalone]': '!buttonToggleGroup',\n        '[class.mat-button-toggle-checked]': 'checked',\n        '[class.mat-button-toggle-disabled]': 'disabled',\n        '[class.mat-button-toggle-disabled-interactive]': 'disabledInteractive',\n        '[class.mat-button-toggle-appearance-standard]': 'appearance === \"standard\"',\n        'class': 'mat-button-toggle',\n        '[attr.aria-label]': 'null',\n        '[attr.aria-labelledby]': 'null',\n        '[attr.id]': 'id',\n        '[attr.name]': 'null',\n        '(focus)': 'focus()',\n        'role': 'presentation'\n      },\n      standalone: true,\n      imports: [MatRipple, MatPseudoCheckbox],\n      template: \"<button #button class=\\\"mat-button-toggle-button mat-focus-indicator\\\"\\n        type=\\\"button\\\"\\n        [id]=\\\"buttonId\\\"\\n        [attr.role]=\\\"isSingleSelector() ? 'radio' : 'button'\\\"\\n        [attr.tabindex]=\\\"disabled && !disabledInteractive ? -1 : tabIndex\\\"\\n        [attr.aria-pressed]=\\\"!isSingleSelector() ? checked : null\\\"\\n        [attr.aria-checked]=\\\"isSingleSelector() ? checked : null\\\"\\n        [disabled]=\\\"(disabled && !disabledInteractive) || null\\\"\\n        [attr.name]=\\\"_getButtonName()\\\"\\n        [attr.aria-label]=\\\"ariaLabel\\\"\\n        [attr.aria-labelledby]=\\\"ariaLabelledby\\\"\\n        [attr.aria-disabled]=\\\"disabled && disabledInteractive ? 'true' : null\\\"\\n        (click)=\\\"_onButtonClick()\\\">\\n  <span class=\\\"mat-button-toggle-label-content\\\">\\n    <!-- Render checkmark at the beginning for single-selection. -->\\n    @if (buttonToggleGroup && checked && !buttonToggleGroup.multiple && !buttonToggleGroup.hideSingleSelectionIndicator) {\\n      <mat-pseudo-checkbox\\n          class=\\\"mat-mdc-option-pseudo-checkbox\\\"\\n          [disabled]=\\\"disabled\\\"\\n          state=\\\"checked\\\"\\n          aria-hidden=\\\"true\\\"\\n          appearance=\\\"minimal\\\"></mat-pseudo-checkbox>\\n    }\\n    <!-- Render checkmark at the beginning for multiple-selection. -->\\n    @if (buttonToggleGroup && checked && buttonToggleGroup.multiple && !buttonToggleGroup.hideMultipleSelectionIndicator) {\\n      <mat-pseudo-checkbox\\n          class=\\\"mat-mdc-option-pseudo-checkbox\\\"\\n          [disabled]=\\\"disabled\\\"\\n          state=\\\"checked\\\"\\n          aria-hidden=\\\"true\\\"\\n          appearance=\\\"minimal\\\"></mat-pseudo-checkbox>\\n    }\\n    <ng-content></ng-content>\\n  </span>\\n</button>\\n\\n<span class=\\\"mat-button-toggle-focus-overlay\\\"></span>\\n<span class=\\\"mat-button-toggle-ripple\\\" matRipple\\n     [matRippleTrigger]=\\\"button\\\"\\n     [matRippleDisabled]=\\\"this.disableRipple || this.disabled\\\">\\n</span>\\n\",\n      styles: [\".mat-button-toggle-standalone,.mat-button-toggle-group{position:relative;display:inline-flex;flex-direction:row;white-space:nowrap;overflow:hidden;-webkit-tap-highlight-color:rgba(0,0,0,0);transform:translateZ(0);border-radius:var(--mat-legacy-button-toggle-shape)}.mat-button-toggle-standalone:not([class*=mat-elevation-z]),.mat-button-toggle-group:not([class*=mat-elevation-z]){box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12)}.cdk-high-contrast-active .mat-button-toggle-standalone,.cdk-high-contrast-active .mat-button-toggle-group{outline:solid 1px}.mat-button-toggle-standalone.mat-button-toggle-appearance-standard,.mat-button-toggle-group-appearance-standard{border-radius:var(--mat-standard-button-toggle-shape, var(--mat-app-corner-full));border:solid 1px var(--mat-standard-button-toggle-divider-color, var(--mat-app-outline))}.mat-button-toggle-standalone.mat-button-toggle-appearance-standard .mat-pseudo-checkbox,.mat-button-toggle-group-appearance-standard .mat-pseudo-checkbox{--mat-minimal-pseudo-checkbox-selected-checkmark-color: var(--mat-standard-button-toggle-selected-state-text-color, var(--mat-app-on-secondary-container))}.mat-button-toggle-standalone.mat-button-toggle-appearance-standard:not([class*=mat-elevation-z]),.mat-button-toggle-group-appearance-standard:not([class*=mat-elevation-z]){box-shadow:none}.cdk-high-contrast-active .mat-button-toggle-standalone.mat-button-toggle-appearance-standard,.cdk-high-contrast-active .mat-button-toggle-group-appearance-standard{outline:0}.mat-button-toggle-vertical{flex-direction:column}.mat-button-toggle-vertical .mat-button-toggle-label-content{display:block}.mat-button-toggle{white-space:nowrap;position:relative;color:var(--mat-legacy-button-toggle-text-color);font-family:var(--mat-legacy-button-toggle-label-text-font);font-size:var(--mat-legacy-button-toggle-label-text-size);line-height:var(--mat-legacy-button-toggle-label-text-line-height);font-weight:var(--mat-legacy-button-toggle-label-text-weight);letter-spacing:var(--mat-legacy-button-toggle-label-text-tracking);--mat-minimal-pseudo-checkbox-selected-checkmark-color: var(--mat-legacy-button-toggle-selected-state-text-color)}.mat-button-toggle.cdk-keyboard-focused .mat-button-toggle-focus-overlay{opacity:var(--mat-legacy-button-toggle-focus-state-layer-opacity)}.mat-button-toggle .mat-icon svg{vertical-align:top}.mat-button-toggle .mat-pseudo-checkbox{margin-right:12px}[dir=rtl] .mat-button-toggle .mat-pseudo-checkbox{margin-right:0;margin-left:12px}.mat-button-toggle-checked{color:var(--mat-legacy-button-toggle-selected-state-text-color);background-color:var(--mat-legacy-button-toggle-selected-state-background-color)}.mat-button-toggle-disabled{pointer-events:none;color:var(--mat-legacy-button-toggle-disabled-state-text-color);background-color:var(--mat-legacy-button-toggle-disabled-state-background-color);--mat-minimal-pseudo-checkbox-disabled-selected-checkmark-color: var(--mat-legacy-button-toggle-disabled-state-text-color)}.mat-button-toggle-disabled.mat-button-toggle-checked{background-color:var(--mat-legacy-button-toggle-disabled-selected-state-background-color)}.mat-button-toggle-disabled-interactive{pointer-events:auto}.mat-button-toggle-appearance-standard{color:var(--mat-standard-button-toggle-text-color, var(--mat-app-on-surface));background-color:var(--mat-standard-button-toggle-background-color);font-family:var(--mat-standard-button-toggle-label-text-font, var(--mat-app-label-large-font));font-size:var(--mat-standard-button-toggle-label-text-size, var(--mat-app-label-large-size));line-height:var(--mat-standard-button-toggle-label-text-line-height, var(--mat-app-label-large-line-height));font-weight:var(--mat-standard-button-toggle-label-text-weight, var(--mat-app-label-large-weight));letter-spacing:var(--mat-standard-button-toggle-label-text-tracking, var(--mat-app-label-large-tracking))}.mat-button-toggle-group-appearance-standard .mat-button-toggle-appearance-standard+.mat-button-toggle-appearance-standard{border-left:solid 1px var(--mat-standard-button-toggle-divider-color, var(--mat-app-outline))}[dir=rtl] .mat-button-toggle-group-appearance-standard .mat-button-toggle-appearance-standard+.mat-button-toggle-appearance-standard{border-left:none;border-right:solid 1px var(--mat-standard-button-toggle-divider-color, var(--mat-app-outline))}.mat-button-toggle-group-appearance-standard.mat-button-toggle-vertical .mat-button-toggle-appearance-standard+.mat-button-toggle-appearance-standard{border-left:none;border-right:none;border-top:solid 1px var(--mat-standard-button-toggle-divider-color, var(--mat-app-outline))}.mat-button-toggle-appearance-standard.mat-button-toggle-checked{color:var(--mat-standard-button-toggle-selected-state-text-color, var(--mat-app-on-secondary-container));background-color:var(--mat-standard-button-toggle-selected-state-background-color, var(--mat-app-secondary-container))}.mat-button-toggle-appearance-standard.mat-button-toggle-disabled{color:var(--mat-standard-button-toggle-disabled-state-text-color);background-color:var(--mat-standard-button-toggle-disabled-state-background-color)}.mat-button-toggle-appearance-standard.mat-button-toggle-disabled .mat-pseudo-checkbox{--mat-minimal-pseudo-checkbox-disabled-selected-checkmark-color: var(--mat-standard-button-toggle-disabled-selected-state-text-color)}.mat-button-toggle-appearance-standard.mat-button-toggle-disabled.mat-button-toggle-checked{color:var(--mat-standard-button-toggle-disabled-selected-state-text-color);background-color:var(--mat-standard-button-toggle-disabled-selected-state-background-color)}.mat-button-toggle-appearance-standard .mat-button-toggle-focus-overlay{background-color:var(--mat-standard-button-toggle-state-layer-color, var(--mat-app-on-surface))}.mat-button-toggle-appearance-standard:hover .mat-button-toggle-focus-overlay{opacity:var(--mat-standard-button-toggle-hover-state-layer-opacity, var(--mat-app-hover-state-layer-opacity))}.mat-button-toggle-appearance-standard.cdk-keyboard-focused .mat-button-toggle-focus-overlay{opacity:var(--mat-standard-button-toggle-focus-state-layer-opacity, var(--mat-app-focus-state-layer-opacity))}@media(hover: none){.mat-button-toggle-appearance-standard:hover .mat-button-toggle-focus-overlay{display:none}}.mat-button-toggle-label-content{-webkit-user-select:none;user-select:none;display:inline-block;padding:0 16px;line-height:var(--mat-legacy-button-toggle-height);position:relative}.mat-button-toggle-appearance-standard .mat-button-toggle-label-content{padding:0 12px;line-height:var(--mat-standard-button-toggle-height)}.mat-button-toggle-label-content>*{vertical-align:middle}.mat-button-toggle-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:inherit;pointer-events:none;opacity:0;background-color:var(--mat-legacy-button-toggle-state-layer-color)}.cdk-high-contrast-active .mat-button-toggle-checked .mat-button-toggle-focus-overlay{border-bottom:solid 500px;opacity:.5;height:0}.cdk-high-contrast-active .mat-button-toggle-checked:hover .mat-button-toggle-focus-overlay{opacity:.6}.cdk-high-contrast-active .mat-button-toggle-checked.mat-button-toggle-appearance-standard .mat-button-toggle-focus-overlay{border-bottom:solid 500px}.mat-button-toggle .mat-button-toggle-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-button-toggle-button{border:0;background:none;color:inherit;padding:0;margin:0;font:inherit;outline:none;width:100%;cursor:pointer}.mat-button-toggle-disabled .mat-button-toggle-button{cursor:default}.mat-button-toggle-button::-moz-focus-inner{border:0}.mat-button-toggle-standalone.mat-button-toggle-appearance-standard{--mat-focus-indicator-border-radius:var(--mat-standard-button-toggle-shape, var(--mat-app-corner-full))}.mat-button-toggle-group-appearance-standard .mat-button-toggle:last-of-type .mat-button-toggle-button::before{border-top-right-radius:var(--mat-standard-button-toggle-shape, var(--mat-app-corner-full));border-bottom-right-radius:var(--mat-standard-button-toggle-shape, var(--mat-app-corner-full))}.mat-button-toggle-group-appearance-standard .mat-button-toggle:first-of-type .mat-button-toggle-button::before{border-top-left-radius:var(--mat-standard-button-toggle-shape, var(--mat-app-corner-full));border-bottom-left-radius:var(--mat-standard-button-toggle-shape, var(--mat-app-corner-full))}\"]\n    }]\n  }], () => [{\n    type: MatButtonToggleGroup,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [MAT_BUTTON_TOGGLE_GROUP]\n    }]\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i2.FocusMonitor\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Attribute,\n      args: ['tabindex']\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [MAT_BUTTON_TOGGLE_DEFAULT_OPTIONS]\n    }]\n  }], {\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    ariaLabelledby: [{\n      type: Input,\n      args: ['aria-labelledby']\n    }],\n    _buttonElement: [{\n      type: ViewChild,\n      args: ['button']\n    }],\n    id: [{\n      type: Input\n    }],\n    name: [{\n      type: Input\n    }],\n    value: [{\n      type: Input\n    }],\n    tabIndex: [{\n      type: Input\n    }],\n    disableRipple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    appearance: [{\n      type: Input\n    }],\n    checked: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disabledInteractive: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    change: [{\n      type: Output\n    }]\n  });\n})();\nclass MatButtonToggleModule {\n  static {\n    this.ɵfac = function MatButtonToggleModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatButtonToggleModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatButtonToggleModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatCommonModule, MatRippleModule, MatButtonToggle, MatCommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatButtonToggleModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, MatRippleModule, MatButtonToggleGroup, MatButtonToggle],\n      exports: [MatCommonModule, MatButtonToggleGroup, MatButtonToggle]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_BUTTON_TOGGLE_DEFAULT_OPTIONS, MAT_BUTTON_TOGGLE_GROUP, MAT_BUTTON_TOGGLE_GROUP_DEFAULT_OPTIONS_FACTORY, MAT_BUTTON_TOGGLE_GROUP_VALUE_ACCESSOR, MatButtonToggle, MatButtonToggleChange, MatButtonToggleGroup, MatButtonToggleModule };", "map": {"version": 3, "names": ["i2", "SelectionModel", "RIGHT_ARROW", "DOWN_ARROW", "LEFT_ARROW", "UP_ARROW", "ENTER", "SPACE", "i0", "InjectionToken", "forwardRef", "EventEmitter", "booleanAttribute", "Directive", "Optional", "Inject", "ContentChildren", "Input", "Output", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Attribute", "ViewChild", "NgModule", "i1", "NG_VALUE_ACCESSOR", "<PERSON><PERSON><PERSON><PERSON>", "MatPseudoCheckbox", "MatCommonModule", "MatRippleModule", "_c0", "_c1", "MatButtonToggle_Conditional_3_Template", "rf", "ctx", "ɵɵelement", "ctx_r1", "ɵɵnextContext", "ɵɵproperty", "disabled", "MatButtonToggle_Conditional_4_Template", "MAT_BUTTON_TOGGLE_DEFAULT_OPTIONS", "providedIn", "factory", "MAT_BUTTON_TOGGLE_GROUP_DEFAULT_OPTIONS_FACTORY", "hideSingleSelectionIndicator", "hideMultipleSelectionIndicator", "disabledInteractive", "MAT_BUTTON_TOGGLE_GROUP", "MAT_BUTTON_TOGGLE_GROUP_VALUE_ACCESSOR", "provide", "useExisting", "MatButtonToggleGroup", "multi", "uniqueIdCounter", "MatButtonToggleChange", "constructor", "source", "value", "name", "_name", "_markButtonsForCheck", "selected", "_selectionModel", "multiple", "map", "toggle", "undefined", "newValue", "_setSelectionByValue", "valueChange", "emit", "_multiple", "_disabled", "_disabledInteractive", "dir", "_dir", "_hideSingleSelectionIndicator", "_hideMultipleSelectionIndicator", "_changeDetector", "defaultOptions", "_controlValueAccessorChangeFn", "_onTouched", "change", "appearance", "ngOnInit", "ngAfterContentInit", "select", "_buttonToggles", "filter", "checked", "_initializeTabIndex", "writeValue", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "isDisabled", "_keydown", "event", "target", "buttonId", "id", "index", "toArray", "findIndex", "nextButton", "keyCode", "get", "_getNextButton", "preventDefault", "_onButtonClick", "focus", "_emitChangeEvent", "_rawValue", "_syncButtonToggle", "isUserInput", "deferEvents", "deselect", "Promise", "resolve", "then", "_updateModelValue", "_isSelected", "isSelected", "_isPrechecked", "Array", "isArray", "some", "for<PERSON>ach", "tabIndex", "i", "length", "startIndex", "offset", "items", "item", "ngDevMode", "Error", "_clearSelection", "currentValue", "_selectValue", "clear", "correspondingOption", "find", "_mark<PERSON><PERSON><PERSON><PERSON><PERSON>", "ɵfac", "MatButtonToggleGroup_Factory", "__ngFactoryType__", "ɵɵdirectiveInject", "ChangeDetectorRef", "Directionality", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "contentQueries", "MatButtonToggleGroup_ContentQueries", "dirIndex", "ɵɵcontentQuery", "Mat<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "hostAttrs", "hostVars", "hostBindings", "MatButtonToggleGroup_HostBindings", "ɵɵlistener", "MatButtonToggleGroup_keydown_HostBindingHandler", "$event", "ɵɵattribute", "ɵɵclassProp", "vertical", "inputs", "outputs", "exportAs", "standalone", "features", "ɵɵProvidersFeature", "ɵɵInputTransformsFeature", "ɵsetClassMetadata", "args", "selector", "providers", "host", "decorators", "descendants", "transform", "_tabIndex", "buttonToggleGroup", "_appearance", "_checked", "_changeDetectorRef", "toggleGroup", "_elementRef", "_focusMonitor", "defaultTabIndex", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parsedTabIndex", "Number", "group", "ngAfterViewInit", "monitor", "ngOnDestroy", "stopMonitoring", "options", "_buttonElement", "nativeElement", "newChecked", "isSingleSelector", "focusable", "_getButtonName", "MatButtonToggle_Factory", "ElementRef", "FocusMonitor", "ɵɵinjectAttribute", "ɵcmp", "ɵɵdefineComponent", "viewQuery", "MatButtonToggle_Query", "ɵɵviewQuery", "first", "MatButtonToggle_HostBindings", "MatButtonToggle_focus_HostBindingHandler", "aria<PERSON><PERSON><PERSON>", "disable<PERSON><PERSON><PERSON>", "ɵɵStandaloneFeature", "ngContentSelectors", "decls", "vars", "consts", "template", "MatButtonToggle_Template", "_r1", "ɵɵgetCurrentView", "ɵɵprojectionDef", "ɵɵelementStart", "Mat<PERSON><PERSON><PERSON>Toggle_Template_button_click_0_listener", "ɵɵrestoreView", "ɵɵresetView", "ɵɵtemplate", "ɵɵprojection", "ɵɵelementEnd", "button_r3", "ɵɵreference", "ɵɵadvance", "ɵɵconditional", "dependencies", "styles", "encapsulation", "changeDetection", "None", "OnPush", "imports", "MatButtonToggleModule", "MatButtonToggleModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/node_modules/@angular/material/fesm2022/button-toggle.mjs"], "sourcesContent": ["import * as i2 from '@angular/cdk/a11y';\nimport { SelectionModel } from '@angular/cdk/collections';\nimport { RIGHT_ARROW, DOWN_ARROW, LEFT_ARROW, UP_ARROW, ENTER, SPACE } from '@angular/cdk/keycodes';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, forwardRef, EventEmitter, booleanAttribute, Directive, Optional, Inject, ContentChildren, Input, Output, Component, ViewEncapsulation, ChangeDetectionStrategy, Attribute, ViewChild, NgModule } from '@angular/core';\nimport * as i1 from '@angular/cdk/bidi';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { MatR<PERSON><PERSON>, MatPseudoCheckbox, MatCommonModule, MatRippleModule } from '@angular/material/core';\n\n/**\n * Injection token that can be used to configure the\n * default options for all button toggles within an app.\n */\nconst MAT_BUTTON_TOGGLE_DEFAULT_OPTIONS = new InjectionToken('MAT_BUTTON_TOGGLE_DEFAULT_OPTIONS', {\n    providedIn: 'root',\n    factory: MAT_BUTTON_TOGGLE_GROUP_DEFAULT_OPTIONS_FACTORY,\n});\nfunction MAT_BUTTON_TOGGLE_GROUP_DEFAULT_OPTIONS_FACTORY() {\n    return {\n        hideSingleSelectionIndicator: false,\n        hideMultipleSelectionIndicator: false,\n        disabledInteractive: false,\n    };\n}\n/**\n * Injection token that can be used to reference instances of `MatButtonToggleGroup`.\n * It serves as alternative token to the actual `MatButtonToggleGroup` class which\n * could cause unnecessary retention of the class and its component metadata.\n */\nconst MAT_BUTTON_TOGGLE_GROUP = new InjectionToken('MatButtonToggleGroup');\n/**\n * Provider Expression that allows mat-button-toggle-group to register as a ControlValueAccessor.\n * This allows it to support [(ngModel)].\n * @docs-private\n */\nconst MAT_BUTTON_TOGGLE_GROUP_VALUE_ACCESSOR = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => MatButtonToggleGroup),\n    multi: true,\n};\n// Counter used to generate unique IDs.\nlet uniqueIdCounter = 0;\n/** Change event object emitted by button toggle. */\nclass MatButtonToggleChange {\n    constructor(\n    /** The button toggle that emits the event. */\n    source, \n    /** The value assigned to the button toggle. */\n    value) {\n        this.source = source;\n        this.value = value;\n    }\n}\n/** Exclusive selection button toggle group that behaves like a radio-button group. */\nclass MatButtonToggleGroup {\n    /** `name` attribute for the underlying `input` element. */\n    get name() {\n        return this._name;\n    }\n    set name(value) {\n        this._name = value;\n        this._markButtonsForCheck();\n    }\n    /** Value of the toggle group. */\n    get value() {\n        const selected = this._selectionModel ? this._selectionModel.selected : [];\n        if (this.multiple) {\n            return selected.map(toggle => toggle.value);\n        }\n        return selected[0] ? selected[0].value : undefined;\n    }\n    set value(newValue) {\n        this._setSelectionByValue(newValue);\n        this.valueChange.emit(this.value);\n    }\n    /** Selected button toggles in the group. */\n    get selected() {\n        const selected = this._selectionModel ? this._selectionModel.selected : [];\n        return this.multiple ? selected : selected[0] || null;\n    }\n    /** Whether multiple button toggles can be selected. */\n    get multiple() {\n        return this._multiple;\n    }\n    set multiple(value) {\n        this._multiple = value;\n        this._markButtonsForCheck();\n    }\n    /** Whether multiple button toggle group is disabled. */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(value) {\n        this._disabled = value;\n        this._markButtonsForCheck();\n    }\n    /** Whether buttons in the group should be interactive while they're disabled. */\n    get disabledInteractive() {\n        return this._disabledInteractive;\n    }\n    set disabledInteractive(value) {\n        this._disabledInteractive = value;\n        this._markButtonsForCheck();\n    }\n    /** The layout direction of the toggle button group. */\n    get dir() {\n        return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n    }\n    /** Whether checkmark indicator for single-selection button toggle groups is hidden. */\n    get hideSingleSelectionIndicator() {\n        return this._hideSingleSelectionIndicator;\n    }\n    set hideSingleSelectionIndicator(value) {\n        this._hideSingleSelectionIndicator = value;\n        this._markButtonsForCheck();\n    }\n    /** Whether checkmark indicator for multiple-selection button toggle groups is hidden. */\n    get hideMultipleSelectionIndicator() {\n        return this._hideMultipleSelectionIndicator;\n    }\n    set hideMultipleSelectionIndicator(value) {\n        this._hideMultipleSelectionIndicator = value;\n        this._markButtonsForCheck();\n    }\n    constructor(_changeDetector, defaultOptions, _dir) {\n        this._changeDetector = _changeDetector;\n        this._dir = _dir;\n        this._multiple = false;\n        this._disabled = false;\n        this._disabledInteractive = false;\n        /**\n         * The method to be called in order to update ngModel.\n         * Now `ngModel` binding is not supported in multiple selection mode.\n         */\n        this._controlValueAccessorChangeFn = () => { };\n        /** onTouch function registered via registerOnTouch (ControlValueAccessor). */\n        this._onTouched = () => { };\n        this._name = `mat-button-toggle-group-${uniqueIdCounter++}`;\n        /**\n         * Event that emits whenever the value of the group changes.\n         * Used to facilitate two-way data binding.\n         * @docs-private\n         */\n        this.valueChange = new EventEmitter();\n        /** Event emitted when the group's value changes. */\n        this.change = new EventEmitter();\n        this.appearance =\n            defaultOptions && defaultOptions.appearance ? defaultOptions.appearance : 'standard';\n        this.hideSingleSelectionIndicator = defaultOptions?.hideSingleSelectionIndicator ?? false;\n        this.hideMultipleSelectionIndicator = defaultOptions?.hideMultipleSelectionIndicator ?? false;\n    }\n    ngOnInit() {\n        this._selectionModel = new SelectionModel(this.multiple, undefined, false);\n    }\n    ngAfterContentInit() {\n        this._selectionModel.select(...this._buttonToggles.filter(toggle => toggle.checked));\n        if (!this.multiple) {\n            this._initializeTabIndex();\n        }\n    }\n    /**\n     * Sets the model value. Implemented as part of ControlValueAccessor.\n     * @param value Value to be set to the model.\n     */\n    writeValue(value) {\n        this.value = value;\n        this._changeDetector.markForCheck();\n    }\n    // Implemented as part of ControlValueAccessor.\n    registerOnChange(fn) {\n        this._controlValueAccessorChangeFn = fn;\n    }\n    // Implemented as part of ControlValueAccessor.\n    registerOnTouched(fn) {\n        this._onTouched = fn;\n    }\n    // Implemented as part of ControlValueAccessor.\n    setDisabledState(isDisabled) {\n        this.disabled = isDisabled;\n    }\n    /** Handle keydown event calling to single-select button toggle. */\n    _keydown(event) {\n        if (this.multiple || this.disabled) {\n            return;\n        }\n        const target = event.target;\n        const buttonId = target.id;\n        const index = this._buttonToggles.toArray().findIndex(toggle => {\n            return toggle.buttonId === buttonId;\n        });\n        let nextButton = null;\n        switch (event.keyCode) {\n            case SPACE:\n            case ENTER:\n                nextButton = this._buttonToggles.get(index) || null;\n                break;\n            case UP_ARROW:\n                nextButton = this._getNextButton(index, -1);\n                break;\n            case LEFT_ARROW:\n                nextButton = this._getNextButton(index, this.dir === 'ltr' ? -1 : 1);\n                break;\n            case DOWN_ARROW:\n                nextButton = this._getNextButton(index, 1);\n                break;\n            case RIGHT_ARROW:\n                nextButton = this._getNextButton(index, this.dir === 'ltr' ? 1 : -1);\n                break;\n            default:\n                return;\n        }\n        if (nextButton) {\n            event.preventDefault();\n            nextButton._onButtonClick();\n            nextButton.focus();\n        }\n    }\n    /** Dispatch change event with current selection and group value. */\n    _emitChangeEvent(toggle) {\n        const event = new MatButtonToggleChange(toggle, this.value);\n        this._rawValue = event.value;\n        this._controlValueAccessorChangeFn(event.value);\n        this.change.emit(event);\n    }\n    /**\n     * Syncs a button toggle's selected state with the model value.\n     * @param toggle Toggle to be synced.\n     * @param select Whether the toggle should be selected.\n     * @param isUserInput Whether the change was a result of a user interaction.\n     * @param deferEvents Whether to defer emitting the change events.\n     */\n    _syncButtonToggle(toggle, select, isUserInput = false, deferEvents = false) {\n        // Deselect the currently-selected toggle, if we're in single-selection\n        // mode and the button being toggled isn't selected at the moment.\n        if (!this.multiple && this.selected && !toggle.checked) {\n            this.selected.checked = false;\n        }\n        if (this._selectionModel) {\n            if (select) {\n                this._selectionModel.select(toggle);\n            }\n            else {\n                this._selectionModel.deselect(toggle);\n            }\n        }\n        else {\n            deferEvents = true;\n        }\n        // We need to defer in some cases in order to avoid \"changed after checked errors\", however\n        // the side-effect is that we may end up updating the model value out of sequence in others\n        // The `deferEvents` flag allows us to decide whether to do it on a case-by-case basis.\n        if (deferEvents) {\n            Promise.resolve().then(() => this._updateModelValue(toggle, isUserInput));\n        }\n        else {\n            this._updateModelValue(toggle, isUserInput);\n        }\n    }\n    /** Checks whether a button toggle is selected. */\n    _isSelected(toggle) {\n        return this._selectionModel && this._selectionModel.isSelected(toggle);\n    }\n    /** Determines whether a button toggle should be checked on init. */\n    _isPrechecked(toggle) {\n        if (typeof this._rawValue === 'undefined') {\n            return false;\n        }\n        if (this.multiple && Array.isArray(this._rawValue)) {\n            return this._rawValue.some(value => toggle.value != null && value === toggle.value);\n        }\n        return toggle.value === this._rawValue;\n    }\n    /** Initializes the tabindex attribute using the radio pattern. */\n    _initializeTabIndex() {\n        this._buttonToggles.forEach(toggle => {\n            toggle.tabIndex = -1;\n        });\n        if (this.selected) {\n            this.selected.tabIndex = 0;\n        }\n        else {\n            for (let i = 0; i < this._buttonToggles.length; i++) {\n                const toggle = this._buttonToggles.get(i);\n                if (!toggle.disabled) {\n                    toggle.tabIndex = 0;\n                    break;\n                }\n            }\n        }\n        this._markButtonsForCheck();\n    }\n    /** Obtain the subsequent toggle to which the focus shifts. */\n    _getNextButton(startIndex, offset) {\n        const items = this._buttonToggles;\n        for (let i = 1; i <= items.length; i++) {\n            const index = (startIndex + offset * i + items.length) % items.length;\n            const item = items.get(index);\n            if (item && !item.disabled) {\n                return item;\n            }\n        }\n        return null;\n    }\n    /** Updates the selection state of the toggles in the group based on a value. */\n    _setSelectionByValue(value) {\n        this._rawValue = value;\n        if (!this._buttonToggles) {\n            return;\n        }\n        if (this.multiple && value) {\n            if (!Array.isArray(value) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throw Error('Value must be an array in multiple-selection mode.');\n            }\n            this._clearSelection();\n            value.forEach((currentValue) => this._selectValue(currentValue));\n        }\n        else {\n            this._clearSelection();\n            this._selectValue(value);\n        }\n    }\n    /** Clears the selected toggles. */\n    _clearSelection() {\n        this._selectionModel.clear();\n        this._buttonToggles.forEach(toggle => {\n            toggle.checked = false;\n            // If the button toggle is in single select mode, initialize the tabIndex.\n            if (!this.multiple) {\n                toggle.tabIndex = -1;\n            }\n        });\n    }\n    /** Selects a value if there's a toggle that corresponds to it. */\n    _selectValue(value) {\n        const correspondingOption = this._buttonToggles.find(toggle => {\n            return toggle.value != null && toggle.value === value;\n        });\n        if (correspondingOption) {\n            correspondingOption.checked = true;\n            this._selectionModel.select(correspondingOption);\n            if (!this.multiple) {\n                // If the button toggle is in single select mode, reset the tabIndex.\n                correspondingOption.tabIndex = 0;\n            }\n        }\n    }\n    /** Syncs up the group's value with the model and emits the change event. */\n    _updateModelValue(toggle, isUserInput) {\n        // Only emit the change event for user input.\n        if (isUserInput) {\n            this._emitChangeEvent(toggle);\n        }\n        // Note: we emit this one no matter whether it was a user interaction, because\n        // it is used by Angular to sync up the two-way data binding.\n        this.valueChange.emit(this.value);\n    }\n    /** Marks all of the child button toggles to be checked. */\n    _markButtonsForCheck() {\n        this._buttonToggles?.forEach(toggle => toggle._markForCheck());\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: MatButtonToggleGroup, deps: [{ token: i0.ChangeDetectorRef }, { token: MAT_BUTTON_TOGGLE_DEFAULT_OPTIONS, optional: true }, { token: i1.Directionality, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"18.2.0-next.2\", type: MatButtonToggleGroup, isStandalone: true, selector: \"mat-button-toggle-group\", inputs: { appearance: \"appearance\", name: \"name\", vertical: [\"vertical\", \"vertical\", booleanAttribute], value: \"value\", multiple: [\"multiple\", \"multiple\", booleanAttribute], disabled: [\"disabled\", \"disabled\", booleanAttribute], disabledInteractive: [\"disabledInteractive\", \"disabledInteractive\", booleanAttribute], hideSingleSelectionIndicator: [\"hideSingleSelectionIndicator\", \"hideSingleSelectionIndicator\", booleanAttribute], hideMultipleSelectionIndicator: [\"hideMultipleSelectionIndicator\", \"hideMultipleSelectionIndicator\", booleanAttribute] }, outputs: { valueChange: \"valueChange\", change: \"change\" }, host: { listeners: { \"keydown\": \"_keydown($event)\" }, properties: { \"attr.role\": \"multiple ? 'group' : 'radiogroup'\", \"attr.aria-disabled\": \"disabled\", \"class.mat-button-toggle-vertical\": \"vertical\", \"class.mat-button-toggle-group-appearance-standard\": \"appearance === \\\"standard\\\"\" }, classAttribute: \"mat-button-toggle-group\" }, providers: [\n            MAT_BUTTON_TOGGLE_GROUP_VALUE_ACCESSOR,\n            { provide: MAT_BUTTON_TOGGLE_GROUP, useExisting: MatButtonToggleGroup },\n        ], queries: [{ propertyName: \"_buttonToggles\", predicate: i0.forwardRef(() => MatButtonToggle), descendants: true }], exportAs: [\"matButtonToggleGroup\"], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: MatButtonToggleGroup, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-button-toggle-group',\n                    providers: [\n                        MAT_BUTTON_TOGGLE_GROUP_VALUE_ACCESSOR,\n                        { provide: MAT_BUTTON_TOGGLE_GROUP, useExisting: MatButtonToggleGroup },\n                    ],\n                    host: {\n                        'class': 'mat-button-toggle-group',\n                        '(keydown)': '_keydown($event)',\n                        '[attr.role]': \"multiple ? 'group' : 'radiogroup'\",\n                        '[attr.aria-disabled]': 'disabled',\n                        '[class.mat-button-toggle-vertical]': 'vertical',\n                        '[class.mat-button-toggle-group-appearance-standard]': 'appearance === \"standard\"',\n                    },\n                    exportAs: 'matButtonToggleGroup',\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.ChangeDetectorRef }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_BUTTON_TOGGLE_DEFAULT_OPTIONS]\n                }] }, { type: i1.Directionality, decorators: [{\n                    type: Optional\n                }] }], propDecorators: { _buttonToggles: [{\n                type: ContentChildren,\n                args: [forwardRef(() => MatButtonToggle), {\n                        // Note that this would technically pick up toggles\n                        // from nested groups, but that's not a case that we support.\n                        descendants: true,\n                    }]\n            }], appearance: [{\n                type: Input\n            }], name: [{\n                type: Input\n            }], vertical: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], value: [{\n                type: Input\n            }], valueChange: [{\n                type: Output\n            }], multiple: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], disabledInteractive: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], change: [{\n                type: Output\n            }], hideSingleSelectionIndicator: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], hideMultipleSelectionIndicator: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }] } });\n/** Single button inside of a toggle group. */\nclass MatButtonToggle {\n    /** Unique ID for the underlying `button` element. */\n    get buttonId() {\n        return `${this.id}-button`;\n    }\n    /** Tabindex of the toggle. */\n    get tabIndex() {\n        return this._tabIndex;\n    }\n    set tabIndex(value) {\n        this._tabIndex = value;\n        this._markForCheck();\n    }\n    /** The appearance style of the button. */\n    get appearance() {\n        return this.buttonToggleGroup ? this.buttonToggleGroup.appearance : this._appearance;\n    }\n    set appearance(value) {\n        this._appearance = value;\n    }\n    /** Whether the button is checked. */\n    get checked() {\n        return this.buttonToggleGroup ? this.buttonToggleGroup._isSelected(this) : this._checked;\n    }\n    set checked(value) {\n        if (value !== this._checked) {\n            this._checked = value;\n            if (this.buttonToggleGroup) {\n                this.buttonToggleGroup._syncButtonToggle(this, this._checked);\n            }\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    /** Whether the button is disabled. */\n    get disabled() {\n        return this._disabled || (this.buttonToggleGroup && this.buttonToggleGroup.disabled);\n    }\n    set disabled(value) {\n        this._disabled = value;\n    }\n    /** Whether the button should remain interactive when it is disabled. */\n    get disabledInteractive() {\n        return (this._disabledInteractive ||\n            (this.buttonToggleGroup !== null && this.buttonToggleGroup.disabledInteractive));\n    }\n    set disabledInteractive(value) {\n        this._disabledInteractive = value;\n    }\n    constructor(toggleGroup, _changeDetectorRef, _elementRef, _focusMonitor, defaultTabIndex, defaultOptions) {\n        this._changeDetectorRef = _changeDetectorRef;\n        this._elementRef = _elementRef;\n        this._focusMonitor = _focusMonitor;\n        this._checked = false;\n        /**\n         * Users can specify the `aria-labelledby` attribute which will be forwarded to the input element\n         */\n        this.ariaLabelledby = null;\n        this._disabled = false;\n        /** Event emitted when the group value changes. */\n        this.change = new EventEmitter();\n        const parsedTabIndex = Number(defaultTabIndex);\n        this.tabIndex = parsedTabIndex || parsedTabIndex === 0 ? parsedTabIndex : null;\n        this.buttonToggleGroup = toggleGroup;\n        this.appearance =\n            defaultOptions && defaultOptions.appearance ? defaultOptions.appearance : 'standard';\n        this.disabledInteractive = defaultOptions?.disabledInteractive ?? false;\n    }\n    ngOnInit() {\n        const group = this.buttonToggleGroup;\n        this.id = this.id || `mat-button-toggle-${uniqueIdCounter++}`;\n        if (group) {\n            if (group._isPrechecked(this)) {\n                this.checked = true;\n            }\n            else if (group._isSelected(this) !== this._checked) {\n                // As side effect of the circular dependency between the toggle group and the button,\n                // we may end up in a state where the button is supposed to be checked on init, but it\n                // isn't, because the checked value was assigned too early. This can happen when Ivy\n                // assigns the static input value before the `ngOnInit` has run.\n                group._syncButtonToggle(this, this._checked);\n            }\n        }\n    }\n    ngAfterViewInit() {\n        this._focusMonitor.monitor(this._elementRef, true);\n    }\n    ngOnDestroy() {\n        const group = this.buttonToggleGroup;\n        this._focusMonitor.stopMonitoring(this._elementRef);\n        // Remove the toggle from the selection once it's destroyed. Needs to happen\n        // on the next tick in order to avoid \"changed after checked\" errors.\n        if (group && group._isSelected(this)) {\n            group._syncButtonToggle(this, false, false, true);\n        }\n    }\n    /** Focuses the button. */\n    focus(options) {\n        this._buttonElement.nativeElement.focus(options);\n    }\n    /** Checks the button toggle due to an interaction with the underlying native button. */\n    _onButtonClick() {\n        if (this.disabled) {\n            return;\n        }\n        const newChecked = this.isSingleSelector() ? true : !this._checked;\n        if (newChecked !== this._checked) {\n            this._checked = newChecked;\n            if (this.buttonToggleGroup) {\n                this.buttonToggleGroup._syncButtonToggle(this, this._checked, true);\n                this.buttonToggleGroup._onTouched();\n            }\n        }\n        if (this.isSingleSelector()) {\n            const focusable = this.buttonToggleGroup._buttonToggles.find(toggle => {\n                return toggle.tabIndex === 0;\n            });\n            // Modify the tabindex attribute of the last focusable button toggle to -1.\n            if (focusable) {\n                focusable.tabIndex = -1;\n            }\n            // Modify the tabindex attribute of the presently selected button toggle to 0.\n            this.tabIndex = 0;\n        }\n        // Emit a change event when it's the single selector\n        this.change.emit(new MatButtonToggleChange(this, this.value));\n    }\n    /**\n     * Marks the button toggle as needing checking for change detection.\n     * This method is exposed because the parent button toggle group will directly\n     * update bound properties of the radio button.\n     */\n    _markForCheck() {\n        // When the group value changes, the button will not be notified.\n        // Use `markForCheck` to explicit update button toggle's status.\n        this._changeDetectorRef.markForCheck();\n    }\n    /** Gets the name that should be assigned to the inner DOM node. */\n    _getButtonName() {\n        if (this.isSingleSelector()) {\n            return this.buttonToggleGroup.name;\n        }\n        return this.name || null;\n    }\n    /** Whether the toggle is in single selection mode. */\n    isSingleSelector() {\n        return this.buttonToggleGroup && !this.buttonToggleGroup.multiple;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: MatButtonToggle, deps: [{ token: MAT_BUTTON_TOGGLE_GROUP, optional: true }, { token: i0.ChangeDetectorRef }, { token: i0.ElementRef }, { token: i2.FocusMonitor }, { token: 'tabindex', attribute: true }, { token: MAT_BUTTON_TOGGLE_DEFAULT_OPTIONS, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"18.2.0-next.2\", type: MatButtonToggle, isStandalone: true, selector: \"mat-button-toggle\", inputs: { ariaLabel: [\"aria-label\", \"ariaLabel\"], ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"], id: \"id\", name: \"name\", value: \"value\", tabIndex: \"tabIndex\", disableRipple: [\"disableRipple\", \"disableRipple\", booleanAttribute], appearance: \"appearance\", checked: [\"checked\", \"checked\", booleanAttribute], disabled: [\"disabled\", \"disabled\", booleanAttribute], disabledInteractive: [\"disabledInteractive\", \"disabledInteractive\", booleanAttribute] }, outputs: { change: \"change\" }, host: { attributes: { \"role\": \"presentation\" }, listeners: { \"focus\": \"focus()\" }, properties: { \"class.mat-button-toggle-standalone\": \"!buttonToggleGroup\", \"class.mat-button-toggle-checked\": \"checked\", \"class.mat-button-toggle-disabled\": \"disabled\", \"class.mat-button-toggle-disabled-interactive\": \"disabledInteractive\", \"class.mat-button-toggle-appearance-standard\": \"appearance === \\\"standard\\\"\", \"attr.aria-label\": \"null\", \"attr.aria-labelledby\": \"null\", \"attr.id\": \"id\", \"attr.name\": \"null\" }, classAttribute: \"mat-button-toggle\" }, viewQueries: [{ propertyName: \"_buttonElement\", first: true, predicate: [\"button\"], descendants: true }], exportAs: [\"matButtonToggle\"], ngImport: i0, template: \"<button #button class=\\\"mat-button-toggle-button mat-focus-indicator\\\"\\n        type=\\\"button\\\"\\n        [id]=\\\"buttonId\\\"\\n        [attr.role]=\\\"isSingleSelector() ? 'radio' : 'button'\\\"\\n        [attr.tabindex]=\\\"disabled && !disabledInteractive ? -1 : tabIndex\\\"\\n        [attr.aria-pressed]=\\\"!isSingleSelector() ? checked : null\\\"\\n        [attr.aria-checked]=\\\"isSingleSelector() ? checked : null\\\"\\n        [disabled]=\\\"(disabled && !disabledInteractive) || null\\\"\\n        [attr.name]=\\\"_getButtonName()\\\"\\n        [attr.aria-label]=\\\"ariaLabel\\\"\\n        [attr.aria-labelledby]=\\\"ariaLabelledby\\\"\\n        [attr.aria-disabled]=\\\"disabled && disabledInteractive ? 'true' : null\\\"\\n        (click)=\\\"_onButtonClick()\\\">\\n  <span class=\\\"mat-button-toggle-label-content\\\">\\n    <!-- Render checkmark at the beginning for single-selection. -->\\n    @if (buttonToggleGroup && checked && !buttonToggleGroup.multiple && !buttonToggleGroup.hideSingleSelectionIndicator) {\\n      <mat-pseudo-checkbox\\n          class=\\\"mat-mdc-option-pseudo-checkbox\\\"\\n          [disabled]=\\\"disabled\\\"\\n          state=\\\"checked\\\"\\n          aria-hidden=\\\"true\\\"\\n          appearance=\\\"minimal\\\"></mat-pseudo-checkbox>\\n    }\\n    <!-- Render checkmark at the beginning for multiple-selection. -->\\n    @if (buttonToggleGroup && checked && buttonToggleGroup.multiple && !buttonToggleGroup.hideMultipleSelectionIndicator) {\\n      <mat-pseudo-checkbox\\n          class=\\\"mat-mdc-option-pseudo-checkbox\\\"\\n          [disabled]=\\\"disabled\\\"\\n          state=\\\"checked\\\"\\n          aria-hidden=\\\"true\\\"\\n          appearance=\\\"minimal\\\"></mat-pseudo-checkbox>\\n    }\\n    <ng-content></ng-content>\\n  </span>\\n</button>\\n\\n<span class=\\\"mat-button-toggle-focus-overlay\\\"></span>\\n<span class=\\\"mat-button-toggle-ripple\\\" matRipple\\n     [matRippleTrigger]=\\\"button\\\"\\n     [matRippleDisabled]=\\\"this.disableRipple || this.disabled\\\">\\n</span>\\n\", styles: [\".mat-button-toggle-standalone,.mat-button-toggle-group{position:relative;display:inline-flex;flex-direction:row;white-space:nowrap;overflow:hidden;-webkit-tap-highlight-color:rgba(0,0,0,0);transform:translateZ(0);border-radius:var(--mat-legacy-button-toggle-shape)}.mat-button-toggle-standalone:not([class*=mat-elevation-z]),.mat-button-toggle-group:not([class*=mat-elevation-z]){box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12)}.cdk-high-contrast-active .mat-button-toggle-standalone,.cdk-high-contrast-active .mat-button-toggle-group{outline:solid 1px}.mat-button-toggle-standalone.mat-button-toggle-appearance-standard,.mat-button-toggle-group-appearance-standard{border-radius:var(--mat-standard-button-toggle-shape, var(--mat-app-corner-full));border:solid 1px var(--mat-standard-button-toggle-divider-color, var(--mat-app-outline))}.mat-button-toggle-standalone.mat-button-toggle-appearance-standard .mat-pseudo-checkbox,.mat-button-toggle-group-appearance-standard .mat-pseudo-checkbox{--mat-minimal-pseudo-checkbox-selected-checkmark-color: var(--mat-standard-button-toggle-selected-state-text-color, var(--mat-app-on-secondary-container))}.mat-button-toggle-standalone.mat-button-toggle-appearance-standard:not([class*=mat-elevation-z]),.mat-button-toggle-group-appearance-standard:not([class*=mat-elevation-z]){box-shadow:none}.cdk-high-contrast-active .mat-button-toggle-standalone.mat-button-toggle-appearance-standard,.cdk-high-contrast-active .mat-button-toggle-group-appearance-standard{outline:0}.mat-button-toggle-vertical{flex-direction:column}.mat-button-toggle-vertical .mat-button-toggle-label-content{display:block}.mat-button-toggle{white-space:nowrap;position:relative;color:var(--mat-legacy-button-toggle-text-color);font-family:var(--mat-legacy-button-toggle-label-text-font);font-size:var(--mat-legacy-button-toggle-label-text-size);line-height:var(--mat-legacy-button-toggle-label-text-line-height);font-weight:var(--mat-legacy-button-toggle-label-text-weight);letter-spacing:var(--mat-legacy-button-toggle-label-text-tracking);--mat-minimal-pseudo-checkbox-selected-checkmark-color: var(--mat-legacy-button-toggle-selected-state-text-color)}.mat-button-toggle.cdk-keyboard-focused .mat-button-toggle-focus-overlay{opacity:var(--mat-legacy-button-toggle-focus-state-layer-opacity)}.mat-button-toggle .mat-icon svg{vertical-align:top}.mat-button-toggle .mat-pseudo-checkbox{margin-right:12px}[dir=rtl] .mat-button-toggle .mat-pseudo-checkbox{margin-right:0;margin-left:12px}.mat-button-toggle-checked{color:var(--mat-legacy-button-toggle-selected-state-text-color);background-color:var(--mat-legacy-button-toggle-selected-state-background-color)}.mat-button-toggle-disabled{pointer-events:none;color:var(--mat-legacy-button-toggle-disabled-state-text-color);background-color:var(--mat-legacy-button-toggle-disabled-state-background-color);--mat-minimal-pseudo-checkbox-disabled-selected-checkmark-color: var(--mat-legacy-button-toggle-disabled-state-text-color)}.mat-button-toggle-disabled.mat-button-toggle-checked{background-color:var(--mat-legacy-button-toggle-disabled-selected-state-background-color)}.mat-button-toggle-disabled-interactive{pointer-events:auto}.mat-button-toggle-appearance-standard{color:var(--mat-standard-button-toggle-text-color, var(--mat-app-on-surface));background-color:var(--mat-standard-button-toggle-background-color);font-family:var(--mat-standard-button-toggle-label-text-font, var(--mat-app-label-large-font));font-size:var(--mat-standard-button-toggle-label-text-size, var(--mat-app-label-large-size));line-height:var(--mat-standard-button-toggle-label-text-line-height, var(--mat-app-label-large-line-height));font-weight:var(--mat-standard-button-toggle-label-text-weight, var(--mat-app-label-large-weight));letter-spacing:var(--mat-standard-button-toggle-label-text-tracking, var(--mat-app-label-large-tracking))}.mat-button-toggle-group-appearance-standard .mat-button-toggle-appearance-standard+.mat-button-toggle-appearance-standard{border-left:solid 1px var(--mat-standard-button-toggle-divider-color, var(--mat-app-outline))}[dir=rtl] .mat-button-toggle-group-appearance-standard .mat-button-toggle-appearance-standard+.mat-button-toggle-appearance-standard{border-left:none;border-right:solid 1px var(--mat-standard-button-toggle-divider-color, var(--mat-app-outline))}.mat-button-toggle-group-appearance-standard.mat-button-toggle-vertical .mat-button-toggle-appearance-standard+.mat-button-toggle-appearance-standard{border-left:none;border-right:none;border-top:solid 1px var(--mat-standard-button-toggle-divider-color, var(--mat-app-outline))}.mat-button-toggle-appearance-standard.mat-button-toggle-checked{color:var(--mat-standard-button-toggle-selected-state-text-color, var(--mat-app-on-secondary-container));background-color:var(--mat-standard-button-toggle-selected-state-background-color, var(--mat-app-secondary-container))}.mat-button-toggle-appearance-standard.mat-button-toggle-disabled{color:var(--mat-standard-button-toggle-disabled-state-text-color);background-color:var(--mat-standard-button-toggle-disabled-state-background-color)}.mat-button-toggle-appearance-standard.mat-button-toggle-disabled .mat-pseudo-checkbox{--mat-minimal-pseudo-checkbox-disabled-selected-checkmark-color: var(--mat-standard-button-toggle-disabled-selected-state-text-color)}.mat-button-toggle-appearance-standard.mat-button-toggle-disabled.mat-button-toggle-checked{color:var(--mat-standard-button-toggle-disabled-selected-state-text-color);background-color:var(--mat-standard-button-toggle-disabled-selected-state-background-color)}.mat-button-toggle-appearance-standard .mat-button-toggle-focus-overlay{background-color:var(--mat-standard-button-toggle-state-layer-color, var(--mat-app-on-surface))}.mat-button-toggle-appearance-standard:hover .mat-button-toggle-focus-overlay{opacity:var(--mat-standard-button-toggle-hover-state-layer-opacity, var(--mat-app-hover-state-layer-opacity))}.mat-button-toggle-appearance-standard.cdk-keyboard-focused .mat-button-toggle-focus-overlay{opacity:var(--mat-standard-button-toggle-focus-state-layer-opacity, var(--mat-app-focus-state-layer-opacity))}@media(hover: none){.mat-button-toggle-appearance-standard:hover .mat-button-toggle-focus-overlay{display:none}}.mat-button-toggle-label-content{-webkit-user-select:none;user-select:none;display:inline-block;padding:0 16px;line-height:var(--mat-legacy-button-toggle-height);position:relative}.mat-button-toggle-appearance-standard .mat-button-toggle-label-content{padding:0 12px;line-height:var(--mat-standard-button-toggle-height)}.mat-button-toggle-label-content>*{vertical-align:middle}.mat-button-toggle-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:inherit;pointer-events:none;opacity:0;background-color:var(--mat-legacy-button-toggle-state-layer-color)}.cdk-high-contrast-active .mat-button-toggle-checked .mat-button-toggle-focus-overlay{border-bottom:solid 500px;opacity:.5;height:0}.cdk-high-contrast-active .mat-button-toggle-checked:hover .mat-button-toggle-focus-overlay{opacity:.6}.cdk-high-contrast-active .mat-button-toggle-checked.mat-button-toggle-appearance-standard .mat-button-toggle-focus-overlay{border-bottom:solid 500px}.mat-button-toggle .mat-button-toggle-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-button-toggle-button{border:0;background:none;color:inherit;padding:0;margin:0;font:inherit;outline:none;width:100%;cursor:pointer}.mat-button-toggle-disabled .mat-button-toggle-button{cursor:default}.mat-button-toggle-button::-moz-focus-inner{border:0}.mat-button-toggle-standalone.mat-button-toggle-appearance-standard{--mat-focus-indicator-border-radius:var(--mat-standard-button-toggle-shape, var(--mat-app-corner-full))}.mat-button-toggle-group-appearance-standard .mat-button-toggle:last-of-type .mat-button-toggle-button::before{border-top-right-radius:var(--mat-standard-button-toggle-shape, var(--mat-app-corner-full));border-bottom-right-radius:var(--mat-standard-button-toggle-shape, var(--mat-app-corner-full))}.mat-button-toggle-group-appearance-standard .mat-button-toggle:first-of-type .mat-button-toggle-button::before{border-top-left-radius:var(--mat-standard-button-toggle-shape, var(--mat-app-corner-full));border-bottom-left-radius:var(--mat-standard-button-toggle-shape, var(--mat-app-corner-full))}\"], dependencies: [{ kind: \"directive\", type: MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }, { kind: \"component\", type: MatPseudoCheckbox, selector: \"mat-pseudo-checkbox\", inputs: [\"state\", \"disabled\", \"appearance\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: MatButtonToggle, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-button-toggle', encapsulation: ViewEncapsulation.None, exportAs: 'matButtonToggle', changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        '[class.mat-button-toggle-standalone]': '!buttonToggleGroup',\n                        '[class.mat-button-toggle-checked]': 'checked',\n                        '[class.mat-button-toggle-disabled]': 'disabled',\n                        '[class.mat-button-toggle-disabled-interactive]': 'disabledInteractive',\n                        '[class.mat-button-toggle-appearance-standard]': 'appearance === \"standard\"',\n                        'class': 'mat-button-toggle',\n                        '[attr.aria-label]': 'null',\n                        '[attr.aria-labelledby]': 'null',\n                        '[attr.id]': 'id',\n                        '[attr.name]': 'null',\n                        '(focus)': 'focus()',\n                        'role': 'presentation',\n                    }, standalone: true, imports: [MatRipple, MatPseudoCheckbox], template: \"<button #button class=\\\"mat-button-toggle-button mat-focus-indicator\\\"\\n        type=\\\"button\\\"\\n        [id]=\\\"buttonId\\\"\\n        [attr.role]=\\\"isSingleSelector() ? 'radio' : 'button'\\\"\\n        [attr.tabindex]=\\\"disabled && !disabledInteractive ? -1 : tabIndex\\\"\\n        [attr.aria-pressed]=\\\"!isSingleSelector() ? checked : null\\\"\\n        [attr.aria-checked]=\\\"isSingleSelector() ? checked : null\\\"\\n        [disabled]=\\\"(disabled && !disabledInteractive) || null\\\"\\n        [attr.name]=\\\"_getButtonName()\\\"\\n        [attr.aria-label]=\\\"ariaLabel\\\"\\n        [attr.aria-labelledby]=\\\"ariaLabelledby\\\"\\n        [attr.aria-disabled]=\\\"disabled && disabledInteractive ? 'true' : null\\\"\\n        (click)=\\\"_onButtonClick()\\\">\\n  <span class=\\\"mat-button-toggle-label-content\\\">\\n    <!-- Render checkmark at the beginning for single-selection. -->\\n    @if (buttonToggleGroup && checked && !buttonToggleGroup.multiple && !buttonToggleGroup.hideSingleSelectionIndicator) {\\n      <mat-pseudo-checkbox\\n          class=\\\"mat-mdc-option-pseudo-checkbox\\\"\\n          [disabled]=\\\"disabled\\\"\\n          state=\\\"checked\\\"\\n          aria-hidden=\\\"true\\\"\\n          appearance=\\\"minimal\\\"></mat-pseudo-checkbox>\\n    }\\n    <!-- Render checkmark at the beginning for multiple-selection. -->\\n    @if (buttonToggleGroup && checked && buttonToggleGroup.multiple && !buttonToggleGroup.hideMultipleSelectionIndicator) {\\n      <mat-pseudo-checkbox\\n          class=\\\"mat-mdc-option-pseudo-checkbox\\\"\\n          [disabled]=\\\"disabled\\\"\\n          state=\\\"checked\\\"\\n          aria-hidden=\\\"true\\\"\\n          appearance=\\\"minimal\\\"></mat-pseudo-checkbox>\\n    }\\n    <ng-content></ng-content>\\n  </span>\\n</button>\\n\\n<span class=\\\"mat-button-toggle-focus-overlay\\\"></span>\\n<span class=\\\"mat-button-toggle-ripple\\\" matRipple\\n     [matRippleTrigger]=\\\"button\\\"\\n     [matRippleDisabled]=\\\"this.disableRipple || this.disabled\\\">\\n</span>\\n\", styles: [\".mat-button-toggle-standalone,.mat-button-toggle-group{position:relative;display:inline-flex;flex-direction:row;white-space:nowrap;overflow:hidden;-webkit-tap-highlight-color:rgba(0,0,0,0);transform:translateZ(0);border-radius:var(--mat-legacy-button-toggle-shape)}.mat-button-toggle-standalone:not([class*=mat-elevation-z]),.mat-button-toggle-group:not([class*=mat-elevation-z]){box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12)}.cdk-high-contrast-active .mat-button-toggle-standalone,.cdk-high-contrast-active .mat-button-toggle-group{outline:solid 1px}.mat-button-toggle-standalone.mat-button-toggle-appearance-standard,.mat-button-toggle-group-appearance-standard{border-radius:var(--mat-standard-button-toggle-shape, var(--mat-app-corner-full));border:solid 1px var(--mat-standard-button-toggle-divider-color, var(--mat-app-outline))}.mat-button-toggle-standalone.mat-button-toggle-appearance-standard .mat-pseudo-checkbox,.mat-button-toggle-group-appearance-standard .mat-pseudo-checkbox{--mat-minimal-pseudo-checkbox-selected-checkmark-color: var(--mat-standard-button-toggle-selected-state-text-color, var(--mat-app-on-secondary-container))}.mat-button-toggle-standalone.mat-button-toggle-appearance-standard:not([class*=mat-elevation-z]),.mat-button-toggle-group-appearance-standard:not([class*=mat-elevation-z]){box-shadow:none}.cdk-high-contrast-active .mat-button-toggle-standalone.mat-button-toggle-appearance-standard,.cdk-high-contrast-active .mat-button-toggle-group-appearance-standard{outline:0}.mat-button-toggle-vertical{flex-direction:column}.mat-button-toggle-vertical .mat-button-toggle-label-content{display:block}.mat-button-toggle{white-space:nowrap;position:relative;color:var(--mat-legacy-button-toggle-text-color);font-family:var(--mat-legacy-button-toggle-label-text-font);font-size:var(--mat-legacy-button-toggle-label-text-size);line-height:var(--mat-legacy-button-toggle-label-text-line-height);font-weight:var(--mat-legacy-button-toggle-label-text-weight);letter-spacing:var(--mat-legacy-button-toggle-label-text-tracking);--mat-minimal-pseudo-checkbox-selected-checkmark-color: var(--mat-legacy-button-toggle-selected-state-text-color)}.mat-button-toggle.cdk-keyboard-focused .mat-button-toggle-focus-overlay{opacity:var(--mat-legacy-button-toggle-focus-state-layer-opacity)}.mat-button-toggle .mat-icon svg{vertical-align:top}.mat-button-toggle .mat-pseudo-checkbox{margin-right:12px}[dir=rtl] .mat-button-toggle .mat-pseudo-checkbox{margin-right:0;margin-left:12px}.mat-button-toggle-checked{color:var(--mat-legacy-button-toggle-selected-state-text-color);background-color:var(--mat-legacy-button-toggle-selected-state-background-color)}.mat-button-toggle-disabled{pointer-events:none;color:var(--mat-legacy-button-toggle-disabled-state-text-color);background-color:var(--mat-legacy-button-toggle-disabled-state-background-color);--mat-minimal-pseudo-checkbox-disabled-selected-checkmark-color: var(--mat-legacy-button-toggle-disabled-state-text-color)}.mat-button-toggle-disabled.mat-button-toggle-checked{background-color:var(--mat-legacy-button-toggle-disabled-selected-state-background-color)}.mat-button-toggle-disabled-interactive{pointer-events:auto}.mat-button-toggle-appearance-standard{color:var(--mat-standard-button-toggle-text-color, var(--mat-app-on-surface));background-color:var(--mat-standard-button-toggle-background-color);font-family:var(--mat-standard-button-toggle-label-text-font, var(--mat-app-label-large-font));font-size:var(--mat-standard-button-toggle-label-text-size, var(--mat-app-label-large-size));line-height:var(--mat-standard-button-toggle-label-text-line-height, var(--mat-app-label-large-line-height));font-weight:var(--mat-standard-button-toggle-label-text-weight, var(--mat-app-label-large-weight));letter-spacing:var(--mat-standard-button-toggle-label-text-tracking, var(--mat-app-label-large-tracking))}.mat-button-toggle-group-appearance-standard .mat-button-toggle-appearance-standard+.mat-button-toggle-appearance-standard{border-left:solid 1px var(--mat-standard-button-toggle-divider-color, var(--mat-app-outline))}[dir=rtl] .mat-button-toggle-group-appearance-standard .mat-button-toggle-appearance-standard+.mat-button-toggle-appearance-standard{border-left:none;border-right:solid 1px var(--mat-standard-button-toggle-divider-color, var(--mat-app-outline))}.mat-button-toggle-group-appearance-standard.mat-button-toggle-vertical .mat-button-toggle-appearance-standard+.mat-button-toggle-appearance-standard{border-left:none;border-right:none;border-top:solid 1px var(--mat-standard-button-toggle-divider-color, var(--mat-app-outline))}.mat-button-toggle-appearance-standard.mat-button-toggle-checked{color:var(--mat-standard-button-toggle-selected-state-text-color, var(--mat-app-on-secondary-container));background-color:var(--mat-standard-button-toggle-selected-state-background-color, var(--mat-app-secondary-container))}.mat-button-toggle-appearance-standard.mat-button-toggle-disabled{color:var(--mat-standard-button-toggle-disabled-state-text-color);background-color:var(--mat-standard-button-toggle-disabled-state-background-color)}.mat-button-toggle-appearance-standard.mat-button-toggle-disabled .mat-pseudo-checkbox{--mat-minimal-pseudo-checkbox-disabled-selected-checkmark-color: var(--mat-standard-button-toggle-disabled-selected-state-text-color)}.mat-button-toggle-appearance-standard.mat-button-toggle-disabled.mat-button-toggle-checked{color:var(--mat-standard-button-toggle-disabled-selected-state-text-color);background-color:var(--mat-standard-button-toggle-disabled-selected-state-background-color)}.mat-button-toggle-appearance-standard .mat-button-toggle-focus-overlay{background-color:var(--mat-standard-button-toggle-state-layer-color, var(--mat-app-on-surface))}.mat-button-toggle-appearance-standard:hover .mat-button-toggle-focus-overlay{opacity:var(--mat-standard-button-toggle-hover-state-layer-opacity, var(--mat-app-hover-state-layer-opacity))}.mat-button-toggle-appearance-standard.cdk-keyboard-focused .mat-button-toggle-focus-overlay{opacity:var(--mat-standard-button-toggle-focus-state-layer-opacity, var(--mat-app-focus-state-layer-opacity))}@media(hover: none){.mat-button-toggle-appearance-standard:hover .mat-button-toggle-focus-overlay{display:none}}.mat-button-toggle-label-content{-webkit-user-select:none;user-select:none;display:inline-block;padding:0 16px;line-height:var(--mat-legacy-button-toggle-height);position:relative}.mat-button-toggle-appearance-standard .mat-button-toggle-label-content{padding:0 12px;line-height:var(--mat-standard-button-toggle-height)}.mat-button-toggle-label-content>*{vertical-align:middle}.mat-button-toggle-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:inherit;pointer-events:none;opacity:0;background-color:var(--mat-legacy-button-toggle-state-layer-color)}.cdk-high-contrast-active .mat-button-toggle-checked .mat-button-toggle-focus-overlay{border-bottom:solid 500px;opacity:.5;height:0}.cdk-high-contrast-active .mat-button-toggle-checked:hover .mat-button-toggle-focus-overlay{opacity:.6}.cdk-high-contrast-active .mat-button-toggle-checked.mat-button-toggle-appearance-standard .mat-button-toggle-focus-overlay{border-bottom:solid 500px}.mat-button-toggle .mat-button-toggle-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-button-toggle-button{border:0;background:none;color:inherit;padding:0;margin:0;font:inherit;outline:none;width:100%;cursor:pointer}.mat-button-toggle-disabled .mat-button-toggle-button{cursor:default}.mat-button-toggle-button::-moz-focus-inner{border:0}.mat-button-toggle-standalone.mat-button-toggle-appearance-standard{--mat-focus-indicator-border-radius:var(--mat-standard-button-toggle-shape, var(--mat-app-corner-full))}.mat-button-toggle-group-appearance-standard .mat-button-toggle:last-of-type .mat-button-toggle-button::before{border-top-right-radius:var(--mat-standard-button-toggle-shape, var(--mat-app-corner-full));border-bottom-right-radius:var(--mat-standard-button-toggle-shape, var(--mat-app-corner-full))}.mat-button-toggle-group-appearance-standard .mat-button-toggle:first-of-type .mat-button-toggle-button::before{border-top-left-radius:var(--mat-standard-button-toggle-shape, var(--mat-app-corner-full));border-bottom-left-radius:var(--mat-standard-button-toggle-shape, var(--mat-app-corner-full))}\"] }]\n        }], ctorParameters: () => [{ type: MatButtonToggleGroup, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_BUTTON_TOGGLE_GROUP]\n                }] }, { type: i0.ChangeDetectorRef }, { type: i0.ElementRef }, { type: i2.FocusMonitor }, { type: undefined, decorators: [{\n                    type: Attribute,\n                    args: ['tabindex']\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_BUTTON_TOGGLE_DEFAULT_OPTIONS]\n                }] }], propDecorators: { ariaLabel: [{\n                type: Input,\n                args: ['aria-label']\n            }], ariaLabelledby: [{\n                type: Input,\n                args: ['aria-labelledby']\n            }], _buttonElement: [{\n                type: ViewChild,\n                args: ['button']\n            }], id: [{\n                type: Input\n            }], name: [{\n                type: Input\n            }], value: [{\n                type: Input\n            }], tabIndex: [{\n                type: Input\n            }], disableRipple: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], appearance: [{\n                type: Input\n            }], checked: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], disabledInteractive: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], change: [{\n                type: Output\n            }] } });\n\nclass MatButtonToggleModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: MatButtonToggleModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: MatButtonToggleModule, imports: [MatCommonModule, MatRippleModule, MatButtonToggleGroup, MatButtonToggle], exports: [MatCommonModule, MatButtonToggleGroup, MatButtonToggle] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: MatButtonToggleModule, imports: [MatCommonModule, MatRippleModule, MatButtonToggle, MatCommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: MatButtonToggleModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule, MatRippleModule, MatButtonToggleGroup, MatButtonToggle],\n                    exports: [MatCommonModule, MatButtonToggleGroup, MatButtonToggle],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_BUTTON_TOGGLE_DEFAULT_OPTIONS, MAT_BUTTON_TOGGLE_GROUP, MAT_BUTTON_TOGGLE_GROUP_DEFAULT_OPTIONS_FACTORY, MAT_BUTTON_TOGGLE_GROUP_VALUE_ACCESSOR, MatButtonToggle, MatButtonToggleChange, MatButtonToggleGroup, MatButtonToggleModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,mBAAmB;AACvC,SAASC,cAAc,QAAQ,0BAA0B;AACzD,SAASC,WAAW,EAAEC,UAAU,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,KAAK,QAAQ,uBAAuB;AACnG,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,UAAU,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,eAAe,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AAC9O,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,SAASC,iBAAiB,QAAQ,gBAAgB;AAClD,SAASC,SAAS,EAAEC,iBAAiB,EAAEC,eAAe,EAAEC,eAAe,QAAQ,wBAAwB;;AAEvG;AACA;AACA;AACA;AAHA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,uCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA+V2G1B,EAAE,CAAA4B,SAAA,4BAyNu5E,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAG,MAAA,GAzN15E7B,EAAE,CAAA8B,aAAA;IAAF9B,EAAE,CAAA+B,UAAA,aAAAF,MAAA,CAAAG,QAyNiyE,CAAC;EAAA;AAAA;AAAA,SAAAC,uCAAAP,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAzNpyE1B,EAAE,CAAA4B,SAAA,4BAyN40F,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAG,MAAA,GAzN/0F7B,EAAE,CAAA8B,aAAA;IAAF9B,EAAE,CAAA+B,UAAA,aAAAF,MAAA,CAAAG,QAyNstF,CAAC;EAAA;AAAA;AApjBp0F,MAAME,iCAAiC,GAAG,IAAIjC,cAAc,CAAC,mCAAmC,EAAE;EAC9FkC,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEC;AACb,CAAC,CAAC;AACF,SAASA,+CAA+CA,CAAA,EAAG;EACvD,OAAO;IACHC,4BAA4B,EAAE,KAAK;IACnCC,8BAA8B,EAAE,KAAK;IACrCC,mBAAmB,EAAE;EACzB,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,uBAAuB,GAAG,IAAIxC,cAAc,CAAC,sBAAsB,CAAC;AAC1E;AACA;AACA;AACA;AACA;AACA,MAAMyC,sCAAsC,GAAG;EAC3CC,OAAO,EAAEzB,iBAAiB;EAC1B0B,WAAW,EAAE1C,UAAU,CAAC,MAAM2C,oBAAoB,CAAC;EACnDC,KAAK,EAAE;AACX,CAAC;AACD;AACA,IAAIC,eAAe,GAAG,CAAC;AACvB;AACA,MAAMC,qBAAqB,CAAC;EACxBC,WAAWA,CACX;EACAC,MAAM,EACN;EACAC,KAAK,EAAE;IACH,IAAI,CAACD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,KAAK,GAAGA,KAAK;EACtB;AACJ;AACA;AACA,MAAMN,oBAAoB,CAAC;EACvB;EACA,IAAIO,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACC,KAAK;EACrB;EACA,IAAID,IAAIA,CAACD,KAAK,EAAE;IACZ,IAAI,CAACE,KAAK,GAAGF,KAAK;IAClB,IAAI,CAACG,oBAAoB,CAAC,CAAC;EAC/B;EACA;EACA,IAAIH,KAAKA,CAAA,EAAG;IACR,MAAMI,QAAQ,GAAG,IAAI,CAACC,eAAe,GAAG,IAAI,CAACA,eAAe,CAACD,QAAQ,GAAG,EAAE;IAC1E,IAAI,IAAI,CAACE,QAAQ,EAAE;MACf,OAAOF,QAAQ,CAACG,GAAG,CAACC,MAAM,IAAIA,MAAM,CAACR,KAAK,CAAC;IAC/C;IACA,OAAOI,QAAQ,CAAC,CAAC,CAAC,GAAGA,QAAQ,CAAC,CAAC,CAAC,CAACJ,KAAK,GAAGS,SAAS;EACtD;EACA,IAAIT,KAAKA,CAACU,QAAQ,EAAE;IAChB,IAAI,CAACC,oBAAoB,CAACD,QAAQ,CAAC;IACnC,IAAI,CAACE,WAAW,CAACC,IAAI,CAAC,IAAI,CAACb,KAAK,CAAC;EACrC;EACA;EACA,IAAII,QAAQA,CAAA,EAAG;IACX,MAAMA,QAAQ,GAAG,IAAI,CAACC,eAAe,GAAG,IAAI,CAACA,eAAe,CAACD,QAAQ,GAAG,EAAE;IAC1E,OAAO,IAAI,CAACE,QAAQ,GAAGF,QAAQ,GAAGA,QAAQ,CAAC,CAAC,CAAC,IAAI,IAAI;EACzD;EACA;EACA,IAAIE,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACQ,SAAS;EACzB;EACA,IAAIR,QAAQA,CAACN,KAAK,EAAE;IAChB,IAAI,CAACc,SAAS,GAAGd,KAAK;IACtB,IAAI,CAACG,oBAAoB,CAAC,CAAC;EAC/B;EACA;EACA,IAAItB,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACkC,SAAS;EACzB;EACA,IAAIlC,QAAQA,CAACmB,KAAK,EAAE;IAChB,IAAI,CAACe,SAAS,GAAGf,KAAK;IACtB,IAAI,CAACG,oBAAoB,CAAC,CAAC;EAC/B;EACA;EACA,IAAId,mBAAmBA,CAAA,EAAG;IACtB,OAAO,IAAI,CAAC2B,oBAAoB;EACpC;EACA,IAAI3B,mBAAmBA,CAACW,KAAK,EAAE;IAC3B,IAAI,CAACgB,oBAAoB,GAAGhB,KAAK;IACjC,IAAI,CAACG,oBAAoB,CAAC,CAAC;EAC/B;EACA;EACA,IAAIc,GAAGA,CAAA,EAAG;IACN,OAAO,IAAI,CAACC,IAAI,IAAI,IAAI,CAACA,IAAI,CAAClB,KAAK,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK;EACjE;EACA;EACA,IAAIb,4BAA4BA,CAAA,EAAG;IAC/B,OAAO,IAAI,CAACgC,6BAA6B;EAC7C;EACA,IAAIhC,4BAA4BA,CAACa,KAAK,EAAE;IACpC,IAAI,CAACmB,6BAA6B,GAAGnB,KAAK;IAC1C,IAAI,CAACG,oBAAoB,CAAC,CAAC;EAC/B;EACA;EACA,IAAIf,8BAA8BA,CAAA,EAAG;IACjC,OAAO,IAAI,CAACgC,+BAA+B;EAC/C;EACA,IAAIhC,8BAA8BA,CAACY,KAAK,EAAE;IACtC,IAAI,CAACoB,+BAA+B,GAAGpB,KAAK;IAC5C,IAAI,CAACG,oBAAoB,CAAC,CAAC;EAC/B;EACAL,WAAWA,CAACuB,eAAe,EAAEC,cAAc,EAAEJ,IAAI,EAAE;IAC/C,IAAI,CAACG,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACH,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACJ,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,oBAAoB,GAAG,KAAK;IACjC;AACR;AACA;AACA;IACQ,IAAI,CAACO,6BAA6B,GAAG,MAAM,CAAE,CAAC;IAC9C;IACA,IAAI,CAACC,UAAU,GAAG,MAAM,CAAE,CAAC;IAC3B,IAAI,CAACtB,KAAK,GAAG,2BAA2BN,eAAe,EAAE,EAAE;IAC3D;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACgB,WAAW,GAAG,IAAI5D,YAAY,CAAC,CAAC;IACrC;IACA,IAAI,CAACyE,MAAM,GAAG,IAAIzE,YAAY,CAAC,CAAC;IAChC,IAAI,CAAC0E,UAAU,GACXJ,cAAc,IAAIA,cAAc,CAACI,UAAU,GAAGJ,cAAc,CAACI,UAAU,GAAG,UAAU;IACxF,IAAI,CAACvC,4BAA4B,GAAGmC,cAAc,EAAEnC,4BAA4B,IAAI,KAAK;IACzF,IAAI,CAACC,8BAA8B,GAAGkC,cAAc,EAAElC,8BAA8B,IAAI,KAAK;EACjG;EACAuC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACtB,eAAe,GAAG,IAAI/D,cAAc,CAAC,IAAI,CAACgE,QAAQ,EAAEG,SAAS,EAAE,KAAK,CAAC;EAC9E;EACAmB,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACvB,eAAe,CAACwB,MAAM,CAAC,GAAG,IAAI,CAACC,cAAc,CAACC,MAAM,CAACvB,MAAM,IAAIA,MAAM,CAACwB,OAAO,CAAC,CAAC;IACpF,IAAI,CAAC,IAAI,CAAC1B,QAAQ,EAAE;MAChB,IAAI,CAAC2B,mBAAmB,CAAC,CAAC;IAC9B;EACJ;EACA;AACJ;AACA;AACA;EACIC,UAAUA,CAAClC,KAAK,EAAE;IACd,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACqB,eAAe,CAACc,YAAY,CAAC,CAAC;EACvC;EACA;EACAC,gBAAgBA,CAACC,EAAE,EAAE;IACjB,IAAI,CAACd,6BAA6B,GAAGc,EAAE;EAC3C;EACA;EACAC,iBAAiBA,CAACD,EAAE,EAAE;IAClB,IAAI,CAACb,UAAU,GAAGa,EAAE;EACxB;EACA;EACAE,gBAAgBA,CAACC,UAAU,EAAE;IACzB,IAAI,CAAC3D,QAAQ,GAAG2D,UAAU;EAC9B;EACA;EACAC,QAAQA,CAACC,KAAK,EAAE;IACZ,IAAI,IAAI,CAACpC,QAAQ,IAAI,IAAI,CAACzB,QAAQ,EAAE;MAChC;IACJ;IACA,MAAM8D,MAAM,GAAGD,KAAK,CAACC,MAAM;IAC3B,MAAMC,QAAQ,GAAGD,MAAM,CAACE,EAAE;IAC1B,MAAMC,KAAK,GAAG,IAAI,CAAChB,cAAc,CAACiB,OAAO,CAAC,CAAC,CAACC,SAAS,CAACxC,MAAM,IAAI;MAC5D,OAAOA,MAAM,CAACoC,QAAQ,KAAKA,QAAQ;IACvC,CAAC,CAAC;IACF,IAAIK,UAAU,GAAG,IAAI;IACrB,QAAQP,KAAK,CAACQ,OAAO;MACjB,KAAKtG,KAAK;MACV,KAAKD,KAAK;QACNsG,UAAU,GAAG,IAAI,CAACnB,cAAc,CAACqB,GAAG,CAACL,KAAK,CAAC,IAAI,IAAI;QACnD;MACJ,KAAKpG,QAAQ;QACTuG,UAAU,GAAG,IAAI,CAACG,cAAc,CAACN,KAAK,EAAE,CAAC,CAAC,CAAC;QAC3C;MACJ,KAAKrG,UAAU;QACXwG,UAAU,GAAG,IAAI,CAACG,cAAc,CAACN,KAAK,EAAE,IAAI,CAAC7B,GAAG,KAAK,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;QACpE;MACJ,KAAKzE,UAAU;QACXyG,UAAU,GAAG,IAAI,CAACG,cAAc,CAACN,KAAK,EAAE,CAAC,CAAC;QAC1C;MACJ,KAAKvG,WAAW;QACZ0G,UAAU,GAAG,IAAI,CAACG,cAAc,CAACN,KAAK,EAAE,IAAI,CAAC7B,GAAG,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QACpE;MACJ;QACI;IACR;IACA,IAAIgC,UAAU,EAAE;MACZP,KAAK,CAACW,cAAc,CAAC,CAAC;MACtBJ,UAAU,CAACK,cAAc,CAAC,CAAC;MAC3BL,UAAU,CAACM,KAAK,CAAC,CAAC;IACtB;EACJ;EACA;EACAC,gBAAgBA,CAAChD,MAAM,EAAE;IACrB,MAAMkC,KAAK,GAAG,IAAI7C,qBAAqB,CAACW,MAAM,EAAE,IAAI,CAACR,KAAK,CAAC;IAC3D,IAAI,CAACyD,SAAS,GAAGf,KAAK,CAAC1C,KAAK;IAC5B,IAAI,CAACuB,6BAA6B,CAACmB,KAAK,CAAC1C,KAAK,CAAC;IAC/C,IAAI,CAACyB,MAAM,CAACZ,IAAI,CAAC6B,KAAK,CAAC;EAC3B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIgB,iBAAiBA,CAAClD,MAAM,EAAEqB,MAAM,EAAE8B,WAAW,GAAG,KAAK,EAAEC,WAAW,GAAG,KAAK,EAAE;IACxE;IACA;IACA,IAAI,CAAC,IAAI,CAACtD,QAAQ,IAAI,IAAI,CAACF,QAAQ,IAAI,CAACI,MAAM,CAACwB,OAAO,EAAE;MACpD,IAAI,CAAC5B,QAAQ,CAAC4B,OAAO,GAAG,KAAK;IACjC;IACA,IAAI,IAAI,CAAC3B,eAAe,EAAE;MACtB,IAAIwB,MAAM,EAAE;QACR,IAAI,CAACxB,eAAe,CAACwB,MAAM,CAACrB,MAAM,CAAC;MACvC,CAAC,MACI;QACD,IAAI,CAACH,eAAe,CAACwD,QAAQ,CAACrD,MAAM,CAAC;MACzC;IACJ,CAAC,MACI;MACDoD,WAAW,GAAG,IAAI;IACtB;IACA;IACA;IACA;IACA,IAAIA,WAAW,EAAE;MACbE,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM,IAAI,CAACC,iBAAiB,CAACzD,MAAM,EAAEmD,WAAW,CAAC,CAAC;IAC7E,CAAC,MACI;MACD,IAAI,CAACM,iBAAiB,CAACzD,MAAM,EAAEmD,WAAW,CAAC;IAC/C;EACJ;EACA;EACAO,WAAWA,CAAC1D,MAAM,EAAE;IAChB,OAAO,IAAI,CAACH,eAAe,IAAI,IAAI,CAACA,eAAe,CAAC8D,UAAU,CAAC3D,MAAM,CAAC;EAC1E;EACA;EACA4D,aAAaA,CAAC5D,MAAM,EAAE;IAClB,IAAI,OAAO,IAAI,CAACiD,SAAS,KAAK,WAAW,EAAE;MACvC,OAAO,KAAK;IAChB;IACA,IAAI,IAAI,CAACnD,QAAQ,IAAI+D,KAAK,CAACC,OAAO,CAAC,IAAI,CAACb,SAAS,CAAC,EAAE;MAChD,OAAO,IAAI,CAACA,SAAS,CAACc,IAAI,CAACvE,KAAK,IAAIQ,MAAM,CAACR,KAAK,IAAI,IAAI,IAAIA,KAAK,KAAKQ,MAAM,CAACR,KAAK,CAAC;IACvF;IACA,OAAOQ,MAAM,CAACR,KAAK,KAAK,IAAI,CAACyD,SAAS;EAC1C;EACA;EACAxB,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACH,cAAc,CAAC0C,OAAO,CAAChE,MAAM,IAAI;MAClCA,MAAM,CAACiE,QAAQ,GAAG,CAAC,CAAC;IACxB,CAAC,CAAC;IACF,IAAI,IAAI,CAACrE,QAAQ,EAAE;MACf,IAAI,CAACA,QAAQ,CAACqE,QAAQ,GAAG,CAAC;IAC9B,CAAC,MACI;MACD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC5C,cAAc,CAAC6C,MAAM,EAAED,CAAC,EAAE,EAAE;QACjD,MAAMlE,MAAM,GAAG,IAAI,CAACsB,cAAc,CAACqB,GAAG,CAACuB,CAAC,CAAC;QACzC,IAAI,CAAClE,MAAM,CAAC3B,QAAQ,EAAE;UAClB2B,MAAM,CAACiE,QAAQ,GAAG,CAAC;UACnB;QACJ;MACJ;IACJ;IACA,IAAI,CAACtE,oBAAoB,CAAC,CAAC;EAC/B;EACA;EACAiD,cAAcA,CAACwB,UAAU,EAAEC,MAAM,EAAE;IAC/B,MAAMC,KAAK,GAAG,IAAI,CAAChD,cAAc;IACjC,KAAK,IAAI4C,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAII,KAAK,CAACH,MAAM,EAAED,CAAC,EAAE,EAAE;MACpC,MAAM5B,KAAK,GAAG,CAAC8B,UAAU,GAAGC,MAAM,GAAGH,CAAC,GAAGI,KAAK,CAACH,MAAM,IAAIG,KAAK,CAACH,MAAM;MACrE,MAAMI,IAAI,GAAGD,KAAK,CAAC3B,GAAG,CAACL,KAAK,CAAC;MAC7B,IAAIiC,IAAI,IAAI,CAACA,IAAI,CAAClG,QAAQ,EAAE;QACxB,OAAOkG,IAAI;MACf;IACJ;IACA,OAAO,IAAI;EACf;EACA;EACApE,oBAAoBA,CAACX,KAAK,EAAE;IACxB,IAAI,CAACyD,SAAS,GAAGzD,KAAK;IACtB,IAAI,CAAC,IAAI,CAAC8B,cAAc,EAAE;MACtB;IACJ;IACA,IAAI,IAAI,CAACxB,QAAQ,IAAIN,KAAK,EAAE;MACxB,IAAI,CAACqE,KAAK,CAACC,OAAO,CAACtE,KAAK,CAAC,KAAK,OAAOgF,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QAC1E,MAAMC,KAAK,CAAC,oDAAoD,CAAC;MACrE;MACA,IAAI,CAACC,eAAe,CAAC,CAAC;MACtBlF,KAAK,CAACwE,OAAO,CAAEW,YAAY,IAAK,IAAI,CAACC,YAAY,CAACD,YAAY,CAAC,CAAC;IACpE,CAAC,MACI;MACD,IAAI,CAACD,eAAe,CAAC,CAAC;MACtB,IAAI,CAACE,YAAY,CAACpF,KAAK,CAAC;IAC5B;EACJ;EACA;EACAkF,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC7E,eAAe,CAACgF,KAAK,CAAC,CAAC;IAC5B,IAAI,CAACvD,cAAc,CAAC0C,OAAO,CAAChE,MAAM,IAAI;MAClCA,MAAM,CAACwB,OAAO,GAAG,KAAK;MACtB;MACA,IAAI,CAAC,IAAI,CAAC1B,QAAQ,EAAE;QAChBE,MAAM,CAACiE,QAAQ,GAAG,CAAC,CAAC;MACxB;IACJ,CAAC,CAAC;EACN;EACA;EACAW,YAAYA,CAACpF,KAAK,EAAE;IAChB,MAAMsF,mBAAmB,GAAG,IAAI,CAACxD,cAAc,CAACyD,IAAI,CAAC/E,MAAM,IAAI;MAC3D,OAAOA,MAAM,CAACR,KAAK,IAAI,IAAI,IAAIQ,MAAM,CAACR,KAAK,KAAKA,KAAK;IACzD,CAAC,CAAC;IACF,IAAIsF,mBAAmB,EAAE;MACrBA,mBAAmB,CAACtD,OAAO,GAAG,IAAI;MAClC,IAAI,CAAC3B,eAAe,CAACwB,MAAM,CAACyD,mBAAmB,CAAC;MAChD,IAAI,CAAC,IAAI,CAAChF,QAAQ,EAAE;QAChB;QACAgF,mBAAmB,CAACb,QAAQ,GAAG,CAAC;MACpC;IACJ;EACJ;EACA;EACAR,iBAAiBA,CAACzD,MAAM,EAAEmD,WAAW,EAAE;IACnC;IACA,IAAIA,WAAW,EAAE;MACb,IAAI,CAACH,gBAAgB,CAAChD,MAAM,CAAC;IACjC;IACA;IACA;IACA,IAAI,CAACI,WAAW,CAACC,IAAI,CAAC,IAAI,CAACb,KAAK,CAAC;EACrC;EACA;EACAG,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAAC2B,cAAc,EAAE0C,OAAO,CAAChE,MAAM,IAAIA,MAAM,CAACgF,aAAa,CAAC,CAAC,CAAC;EAClE;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,6BAAAC,iBAAA;MAAA,YAAAA,iBAAA,IAA+FjG,oBAAoB,EAA9B7C,EAAE,CAAA+I,iBAAA,CAA8C/I,EAAE,CAACgJ,iBAAiB,GAApEhJ,EAAE,CAAA+I,iBAAA,CAA+E7G,iCAAiC,MAAlHlC,EAAE,CAAA+I,iBAAA,CAA6I9H,EAAE,CAACgI,cAAc;IAAA,CAA4D;EAAE;EACrU;IAAS,IAAI,CAACC,IAAI,kBADqFlJ,EAAE,CAAAmJ,iBAAA;MAAAC,IAAA,EACJvG,oBAAoB;MAAAwG,SAAA;MAAAC,cAAA,WAAAC,oCAAA7H,EAAA,EAAAC,GAAA,EAAA6H,QAAA;QAAA,IAAA9H,EAAA;UADlB1B,EAAE,CAAAyJ,cAAA,CAAAD,QAAA,EAIvBE,eAAe;QAAA;QAAA,IAAAhI,EAAA;UAAA,IAAAiI,EAAA;UAJM3J,EAAE,CAAA4J,cAAA,CAAAD,EAAA,GAAF3J,EAAE,CAAA6J,WAAA,QAAAlI,GAAA,CAAAsD,cAAA,GAAA0E,EAAA;QAAA;MAAA;MAAAG,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,kCAAAvI,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF1B,EAAE,CAAAkK,UAAA,qBAAAC,gDAAAC,MAAA;YAAA,OACJzI,GAAA,CAAAiE,QAAA,CAAAwE,MAAe,CAAC;UAAA,CAAG,CAAC;QAAA;QAAA,IAAA1I,EAAA;UADlB1B,EAAE,CAAAqK,WAAA,SAAA1I,GAAA,CAAA8B,QAAA,GACO,OAAO,GAAG,YAAY,mBAAA9B,GAAA,CAAAK,QAAA;UAD/BhC,EAAE,CAAAsK,WAAA,+BAAA3I,GAAA,CAAA4I,QACe,CAAC,gDAAA5I,GAAA,CAAAkD,UAAA,KAAL,UAAI,CAAC;QAAA;MAAA;MAAA2F,MAAA;QAAA3F,UAAA;QAAAzB,IAAA;QAAAmH,QAAA,8BAAgJnK,gBAAgB;QAAA+C,KAAA;QAAAM,QAAA,8BAAsDrD,gBAAgB;QAAA4B,QAAA,8BAAsC5B,gBAAgB;QAAAoC,mBAAA,oDAAuEpC,gBAAgB;QAAAkC,4BAAA,sEAAkGlC,gBAAgB;QAAAmC,8BAAA,0EAAwGnC,gBAAgB;MAAA;MAAAqK,OAAA;QAAA1G,WAAA;QAAAa,MAAA;MAAA;MAAA8F,QAAA;MAAAC,UAAA;MAAAC,QAAA,GAD/mB5K,EAAE,CAAA6K,kBAAA,CACmgC,CACpmCnI,sCAAsC,EACtC;QAAEC,OAAO,EAAEF,uBAAuB;QAAEG,WAAW,EAAEC;MAAqB,CAAC,CAC1E,GAJkG7C,EAAE,CAAA8K,wBAAA;IAAA,EAIoE;EAAE;AACnL;AACA;EAAA,QAAA3C,SAAA,oBAAAA,SAAA,KAN2GnI,EAAE,CAAA+K,iBAAA,CAMXlI,oBAAoB,EAAc,CAAC;IACzHuG,IAAI,EAAE/I,SAAS;IACf2K,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,yBAAyB;MACnCC,SAAS,EAAE,CACPxI,sCAAsC,EACtC;QAAEC,OAAO,EAAEF,uBAAuB;QAAEG,WAAW,EAAEC;MAAqB,CAAC,CAC1E;MACDsI,IAAI,EAAE;QACF,OAAO,EAAE,yBAAyB;QAClC,WAAW,EAAE,kBAAkB;QAC/B,aAAa,EAAE,mCAAmC;QAClD,sBAAsB,EAAE,UAAU;QAClC,oCAAoC,EAAE,UAAU;QAChD,qDAAqD,EAAE;MAC3D,CAAC;MACDT,QAAQ,EAAE,sBAAsB;MAChCC,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEvB,IAAI,EAAEpJ,EAAE,CAACgJ;EAAkB,CAAC,EAAE;IAAEI,IAAI,EAAExF,SAAS;IAAEwH,UAAU,EAAE,CAAC;MAC/EhC,IAAI,EAAE9I;IACV,CAAC,EAAE;MACC8I,IAAI,EAAE7I,MAAM;MACZyK,IAAI,EAAE,CAAC9I,iCAAiC;IAC5C,CAAC;EAAE,CAAC,EAAE;IAAEkH,IAAI,EAAEnI,EAAE,CAACgI,cAAc;IAAEmC,UAAU,EAAE,CAAC;MAC1ChC,IAAI,EAAE9I;IACV,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAE2E,cAAc,EAAE,CAAC;MAC1CmE,IAAI,EAAE5I,eAAe;MACrBwK,IAAI,EAAE,CAAC9K,UAAU,CAAC,MAAMwJ,eAAe,CAAC,EAAE;QAClC;QACA;QACA2B,WAAW,EAAE;MACjB,CAAC;IACT,CAAC,CAAC;IAAExG,UAAU,EAAE,CAAC;MACbuE,IAAI,EAAE3I;IACV,CAAC,CAAC;IAAE2C,IAAI,EAAE,CAAC;MACPgG,IAAI,EAAE3I;IACV,CAAC,CAAC;IAAE8J,QAAQ,EAAE,CAAC;MACXnB,IAAI,EAAE3I,KAAK;MACXuK,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAElL;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE+C,KAAK,EAAE,CAAC;MACRiG,IAAI,EAAE3I;IACV,CAAC,CAAC;IAAEsD,WAAW,EAAE,CAAC;MACdqF,IAAI,EAAE1I;IACV,CAAC,CAAC;IAAE+C,QAAQ,EAAE,CAAC;MACX2F,IAAI,EAAE3I,KAAK;MACXuK,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAElL;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE4B,QAAQ,EAAE,CAAC;MACXoH,IAAI,EAAE3I,KAAK;MACXuK,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAElL;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEoC,mBAAmB,EAAE,CAAC;MACtB4G,IAAI,EAAE3I,KAAK;MACXuK,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAElL;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEwE,MAAM,EAAE,CAAC;MACTwE,IAAI,EAAE1I;IACV,CAAC,CAAC;IAAE4B,4BAA4B,EAAE,CAAC;MAC/B8G,IAAI,EAAE3I,KAAK;MACXuK,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAElL;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEmC,8BAA8B,EAAE,CAAC;MACjC6G,IAAI,EAAE3I,KAAK;MACXuK,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAElL;MAAiB,CAAC;IAC1C,CAAC;EAAE,CAAC;AAAA;AAChB;AACA,MAAMsJ,eAAe,CAAC;EAClB;EACA,IAAI3D,QAAQA,CAAA,EAAG;IACX,OAAO,GAAG,IAAI,CAACC,EAAE,SAAS;EAC9B;EACA;EACA,IAAI4B,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC2D,SAAS;EACzB;EACA,IAAI3D,QAAQA,CAACzE,KAAK,EAAE;IAChB,IAAI,CAACoI,SAAS,GAAGpI,KAAK;IACtB,IAAI,CAACwF,aAAa,CAAC,CAAC;EACxB;EACA;EACA,IAAI9D,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAAC2G,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAAC3G,UAAU,GAAG,IAAI,CAAC4G,WAAW;EACxF;EACA,IAAI5G,UAAUA,CAAC1B,KAAK,EAAE;IAClB,IAAI,CAACsI,WAAW,GAAGtI,KAAK;EAC5B;EACA;EACA,IAAIgC,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACqG,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACnE,WAAW,CAAC,IAAI,CAAC,GAAG,IAAI,CAACqE,QAAQ;EAC5F;EACA,IAAIvG,OAAOA,CAAChC,KAAK,EAAE;IACf,IAAIA,KAAK,KAAK,IAAI,CAACuI,QAAQ,EAAE;MACzB,IAAI,CAACA,QAAQ,GAAGvI,KAAK;MACrB,IAAI,IAAI,CAACqI,iBAAiB,EAAE;QACxB,IAAI,CAACA,iBAAiB,CAAC3E,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC6E,QAAQ,CAAC;MACjE;MACA,IAAI,CAACC,kBAAkB,CAACrG,YAAY,CAAC,CAAC;IAC1C;EACJ;EACA;EACA,IAAItD,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACkC,SAAS,IAAK,IAAI,CAACsH,iBAAiB,IAAI,IAAI,CAACA,iBAAiB,CAACxJ,QAAS;EACxF;EACA,IAAIA,QAAQA,CAACmB,KAAK,EAAE;IAChB,IAAI,CAACe,SAAS,GAAGf,KAAK;EAC1B;EACA;EACA,IAAIX,mBAAmBA,CAAA,EAAG;IACtB,OAAQ,IAAI,CAAC2B,oBAAoB,IAC5B,IAAI,CAACqH,iBAAiB,KAAK,IAAI,IAAI,IAAI,CAACA,iBAAiB,CAAChJ,mBAAoB;EACvF;EACA,IAAIA,mBAAmBA,CAACW,KAAK,EAAE;IAC3B,IAAI,CAACgB,oBAAoB,GAAGhB,KAAK;EACrC;EACAF,WAAWA,CAAC2I,WAAW,EAAED,kBAAkB,EAAEE,WAAW,EAAEC,aAAa,EAAEC,eAAe,EAAEtH,cAAc,EAAE;IACtG,IAAI,CAACkH,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACE,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACJ,QAAQ,GAAG,KAAK;IACrB;AACR;AACA;IACQ,IAAI,CAACM,cAAc,GAAG,IAAI;IAC1B,IAAI,CAAC9H,SAAS,GAAG,KAAK;IACtB;IACA,IAAI,CAACU,MAAM,GAAG,IAAIzE,YAAY,CAAC,CAAC;IAChC,MAAM8L,cAAc,GAAGC,MAAM,CAACH,eAAe,CAAC;IAC9C,IAAI,CAACnE,QAAQ,GAAGqE,cAAc,IAAIA,cAAc,KAAK,CAAC,GAAGA,cAAc,GAAG,IAAI;IAC9E,IAAI,CAACT,iBAAiB,GAAGI,WAAW;IACpC,IAAI,CAAC/G,UAAU,GACXJ,cAAc,IAAIA,cAAc,CAACI,UAAU,GAAGJ,cAAc,CAACI,UAAU,GAAG,UAAU;IACxF,IAAI,CAACrC,mBAAmB,GAAGiC,cAAc,EAAEjC,mBAAmB,IAAI,KAAK;EAC3E;EACAsC,QAAQA,CAAA,EAAG;IACP,MAAMqH,KAAK,GAAG,IAAI,CAACX,iBAAiB;IACpC,IAAI,CAACxF,EAAE,GAAG,IAAI,CAACA,EAAE,IAAI,qBAAqBjD,eAAe,EAAE,EAAE;IAC7D,IAAIoJ,KAAK,EAAE;MACP,IAAIA,KAAK,CAAC5E,aAAa,CAAC,IAAI,CAAC,EAAE;QAC3B,IAAI,CAACpC,OAAO,GAAG,IAAI;MACvB,CAAC,MACI,IAAIgH,KAAK,CAAC9E,WAAW,CAAC,IAAI,CAAC,KAAK,IAAI,CAACqE,QAAQ,EAAE;QAChD;QACA;QACA;QACA;QACAS,KAAK,CAACtF,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC6E,QAAQ,CAAC;MAChD;IACJ;EACJ;EACAU,eAAeA,CAAA,EAAG;IACd,IAAI,CAACN,aAAa,CAACO,OAAO,CAAC,IAAI,CAACR,WAAW,EAAE,IAAI,CAAC;EACtD;EACAS,WAAWA,CAAA,EAAG;IACV,MAAMH,KAAK,GAAG,IAAI,CAACX,iBAAiB;IACpC,IAAI,CAACM,aAAa,CAACS,cAAc,CAAC,IAAI,CAACV,WAAW,CAAC;IACnD;IACA;IACA,IAAIM,KAAK,IAAIA,KAAK,CAAC9E,WAAW,CAAC,IAAI,CAAC,EAAE;MAClC8E,KAAK,CAACtF,iBAAiB,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC;IACrD;EACJ;EACA;EACAH,KAAKA,CAAC8F,OAAO,EAAE;IACX,IAAI,CAACC,cAAc,CAACC,aAAa,CAAChG,KAAK,CAAC8F,OAAO,CAAC;EACpD;EACA;EACA/F,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAACzE,QAAQ,EAAE;MACf;IACJ;IACA,MAAM2K,UAAU,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC,IAAI,CAAClB,QAAQ;IAClE,IAAIiB,UAAU,KAAK,IAAI,CAACjB,QAAQ,EAAE;MAC9B,IAAI,CAACA,QAAQ,GAAGiB,UAAU;MAC1B,IAAI,IAAI,CAACnB,iBAAiB,EAAE;QACxB,IAAI,CAACA,iBAAiB,CAAC3E,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC6E,QAAQ,EAAE,IAAI,CAAC;QACnE,IAAI,CAACF,iBAAiB,CAAC7G,UAAU,CAAC,CAAC;MACvC;IACJ;IACA,IAAI,IAAI,CAACiI,gBAAgB,CAAC,CAAC,EAAE;MACzB,MAAMC,SAAS,GAAG,IAAI,CAACrB,iBAAiB,CAACvG,cAAc,CAACyD,IAAI,CAAC/E,MAAM,IAAI;QACnE,OAAOA,MAAM,CAACiE,QAAQ,KAAK,CAAC;MAChC,CAAC,CAAC;MACF;MACA,IAAIiF,SAAS,EAAE;QACXA,SAAS,CAACjF,QAAQ,GAAG,CAAC,CAAC;MAC3B;MACA;MACA,IAAI,CAACA,QAAQ,GAAG,CAAC;IACrB;IACA;IACA,IAAI,CAAChD,MAAM,CAACZ,IAAI,CAAC,IAAIhB,qBAAqB,CAAC,IAAI,EAAE,IAAI,CAACG,KAAK,CAAC,CAAC;EACjE;EACA;AACJ;AACA;AACA;AACA;EACIwF,aAAaA,CAAA,EAAG;IACZ;IACA;IACA,IAAI,CAACgD,kBAAkB,CAACrG,YAAY,CAAC,CAAC;EAC1C;EACA;EACAwH,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAACF,gBAAgB,CAAC,CAAC,EAAE;MACzB,OAAO,IAAI,CAACpB,iBAAiB,CAACpI,IAAI;IACtC;IACA,OAAO,IAAI,CAACA,IAAI,IAAI,IAAI;EAC5B;EACA;EACAwJ,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAI,CAACpB,iBAAiB,IAAI,CAAC,IAAI,CAACA,iBAAiB,CAAC/H,QAAQ;EACrE;EACA;IAAS,IAAI,CAACmF,IAAI,YAAAmE,wBAAAjE,iBAAA;MAAA,YAAAA,iBAAA,IAA+FY,eAAe,EAxNzB1J,EAAE,CAAA+I,iBAAA,CAwNyCtG,uBAAuB,MAxNlEzC,EAAE,CAAA+I,iBAAA,CAwN6F/I,EAAE,CAACgJ,iBAAiB,GAxNnHhJ,EAAE,CAAA+I,iBAAA,CAwN8H/I,EAAE,CAACgN,UAAU,GAxN7IhN,EAAE,CAAA+I,iBAAA,CAwNwJvJ,EAAE,CAACyN,YAAY,GAxNzKjN,EAAE,CAAAkN,iBAAA,CAwNoL,UAAU,GAxNhMlN,EAAE,CAAA+I,iBAAA,CAwN4N7G,iCAAiC;IAAA,CAA4D;EAAE;EACpa;IAAS,IAAI,CAACiL,IAAI,kBAzNqFnN,EAAE,CAAAoN,iBAAA;MAAAhE,IAAA,EAyNJM,eAAe;MAAAL,SAAA;MAAAgE,SAAA,WAAAC,sBAAA5L,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAzNb1B,EAAE,CAAAuN,WAAA,CAAAhM,GAAA;QAAA;QAAA,IAAAG,EAAA;UAAA,IAAAiI,EAAA;UAAF3J,EAAE,CAAA4J,cAAA,CAAAD,EAAA,GAAF3J,EAAE,CAAA6J,WAAA,QAAAlI,GAAA,CAAA8K,cAAA,GAAA9C,EAAA,CAAA6D,KAAA;QAAA;MAAA;MAAA1D,SAAA,WAyNqkB,cAAc;MAAAC,QAAA;MAAAC,YAAA,WAAAyD,6BAAA/L,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAzNrlB1B,EAAE,CAAAkK,UAAA,mBAAAwD,yCAAA;YAAA,OAyNJ/L,GAAA,CAAA+E,KAAA,CAAM,CAAC;UAAA,CAAO,CAAC;QAAA;QAAA,IAAAhF,EAAA;UAzNb1B,EAAE,CAAAqK,WAAA,eAyNJ,IAAI,qBAAJ,IAAI,QAAA1I,GAAA,CAAAqE,EAAA,UAAJ,IAAI;UAzNFhG,EAAE,CAAAsK,WAAA,kCAAA3I,GAAA,CAAA6J,iBAyNU,CAAC,8BAAA7J,GAAA,CAAAwD,OAAD,CAAC,+BAAAxD,GAAA,CAAAK,QAAD,CAAC,2CAAAL,GAAA,CAAAa,mBAAD,CAAC,0CAAAb,GAAA,CAAAkD,UAAA,eAAD,CAAC;QAAA;MAAA;MAAA2F,MAAA;QAAAmD,SAAA;QAAA3B,cAAA;QAAAhG,EAAA;QAAA5C,IAAA;QAAAD,KAAA;QAAAyE,QAAA;QAAAgG,aAAA,wCAA8QxN,gBAAgB;QAAAyE,UAAA;QAAAM,OAAA,4BAA6D/E,gBAAgB;QAAA4B,QAAA,8BAAsC5B,gBAAgB;QAAAoC,mBAAA,oDAAuEpC,gBAAgB;MAAA;MAAAqK,OAAA;QAAA7F,MAAA;MAAA;MAAA8F,QAAA;MAAAC,UAAA;MAAAC,QAAA,GAzNrgB5K,EAAE,CAAA8K,wBAAA,EAAF9K,EAAE,CAAA6N,mBAAA;MAAAC,kBAAA,EAAAtM,GAAA;MAAAuM,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAzM,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAA,MAAA0M,GAAA,GAAFpO,EAAE,CAAAqO,gBAAA;UAAFrO,EAAE,CAAAsO,eAAA;UAAFtO,EAAE,CAAAuO,cAAA,kBAyNw7D,CAAC;UAzN37DvO,EAAE,CAAAkK,UAAA,mBAAAsE,iDAAA;YAAFxO,EAAE,CAAAyO,aAAA,CAAAL,GAAA;YAAA,OAAFpO,EAAE,CAAA0O,WAAA,CAyNs6D/M,GAAA,CAAA8E,cAAA,CAAe,CAAC;UAAA,CAAC,CAAC;UAzN17DzG,EAAE,CAAAuO,cAAA,aAyN4+D,CAAC;UAzN/+DvO,EAAE,CAAA2O,UAAA,IAAAlN,sCAAA,gCAyN8qE,CAAC,IAAAQ,sCAAA,gCAAob,CAAC;UAzNtmFjC,EAAE,CAAA4O,YAAA,EAyNk3F,CAAC;UAzNr3F5O,EAAE,CAAA6O,YAAA,CAyN63F,CAAC,CAAU,CAAC;UAzN34F7O,EAAE,CAAA4B,SAAA,aAyNm8F,CAAC,aAAmK,CAAC;QAAA;QAAA,IAAAF,EAAA;UAAA,MAAAoN,SAAA,GAzN1mG9O,EAAE,CAAA+O,WAAA;UAAF/O,EAAE,CAAA+B,UAAA,OAAAJ,GAAA,CAAAoE,QAyN41C,CAAC,aAAApE,GAAA,CAAAK,QAAA,KAAAL,GAAA,CAAAa,mBAAA,QAA4V,CAAC;UAzN5rDxC,EAAE,CAAAqK,WAAA,SAAA1I,GAAA,CAAAiL,gBAAA,qCAAAjL,GAAA,CAAAK,QAAA,KAAAL,GAAA,CAAAa,mBAAA,QAAAb,GAAA,CAAAiG,QAAA,mBAAAjG,GAAA,CAAAiL,gBAAA,KAAAjL,GAAA,CAAAwD,OAAA,yBAAAxD,GAAA,CAAAiL,gBAAA,KAAAjL,GAAA,CAAAwD,OAAA,iBAAAxD,GAAA,CAAAmL,cAAA,kBAAAnL,GAAA,CAAAgM,SAAA,qBAAAhM,GAAA,CAAAqK,cAAA,mBAAArK,GAAA,CAAAK,QAAA,IAAAL,GAAA,CAAAa,mBAAA;UAAFxC,EAAE,CAAAgP,SAAA,EAyN85E,CAAC;UAzNj6EhP,EAAE,CAAAiP,aAAA,CAAAtN,GAAA,CAAA6J,iBAAA,IAAA7J,GAAA,CAAAwD,OAAA,KAAAxD,GAAA,CAAA6J,iBAAA,CAAA/H,QAAA,KAAA9B,GAAA,CAAA6J,iBAAA,CAAAlJ,4BAAA,SAyN85E,CAAC;UAzNj6EtC,EAAE,CAAAgP,SAAA,CAyNm1F,CAAC;UAzNt1FhP,EAAE,CAAAiP,aAAA,CAAAtN,GAAA,CAAA6J,iBAAA,IAAA7J,GAAA,CAAAwD,OAAA,IAAAxD,GAAA,CAAA6J,iBAAA,CAAA/H,QAAA,KAAA9B,GAAA,CAAA6J,iBAAA,CAAAjJ,8BAAA,SAyNm1F,CAAC;UAzNt1FvC,EAAE,CAAAgP,SAAA,EAyN2hG,CAAC;UAzN9hGhP,EAAE,CAAA+B,UAAA,qBAAA+M,SAyN2hG,CAAC,sBAAAnN,GAAA,CAAAiM,aAAA,IAAAjM,GAAA,CAAAK,QAAiE,CAAC;QAAA;MAAA;MAAAkN,YAAA,GAAs0Q/N,SAAS,EAAwPC,iBAAiB;MAAA+N,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAAkL;EAAE;AACv9X;AACA;EAAA,QAAAlH,SAAA,oBAAAA,SAAA,KA3N2GnI,EAAE,CAAA+K,iBAAA,CA2NXrB,eAAe,EAAc,CAAC;IACpHN,IAAI,EAAEzI,SAAS;IACfqK,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,mBAAmB;MAAEmE,aAAa,EAAExO,iBAAiB,CAAC0O,IAAI;MAAE5E,QAAQ,EAAE,iBAAiB;MAAE2E,eAAe,EAAExO,uBAAuB,CAAC0O,MAAM;MAAEpE,IAAI,EAAE;QACvJ,sCAAsC,EAAE,oBAAoB;QAC5D,mCAAmC,EAAE,SAAS;QAC9C,oCAAoC,EAAE,UAAU;QAChD,gDAAgD,EAAE,qBAAqB;QACvE,+CAA+C,EAAE,2BAA2B;QAC5E,OAAO,EAAE,mBAAmB;QAC5B,mBAAmB,EAAE,MAAM;QAC3B,wBAAwB,EAAE,MAAM;QAChC,WAAW,EAAE,IAAI;QACjB,aAAa,EAAE,MAAM;QACrB,SAAS,EAAE,SAAS;QACpB,MAAM,EAAE;MACZ,CAAC;MAAER,UAAU,EAAE,IAAI;MAAE6E,OAAO,EAAE,CAACrO,SAAS,EAAEC,iBAAiB,CAAC;MAAE8M,QAAQ,EAAE,y4DAAy4D;MAAEiB,MAAM,EAAE,CAAC,iwQAAiwQ;IAAE,CAAC;EAC5uU,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE/F,IAAI,EAAEvG,oBAAoB;IAAEuI,UAAU,EAAE,CAAC;MAC1DhC,IAAI,EAAE9I;IACV,CAAC,EAAE;MACC8I,IAAI,EAAE7I,MAAM;MACZyK,IAAI,EAAE,CAACvI,uBAAuB;IAClC,CAAC;EAAE,CAAC,EAAE;IAAE2G,IAAI,EAAEpJ,EAAE,CAACgJ;EAAkB,CAAC,EAAE;IAAEI,IAAI,EAAEpJ,EAAE,CAACgN;EAAW,CAAC,EAAE;IAAE5D,IAAI,EAAE5J,EAAE,CAACyN;EAAa,CAAC,EAAE;IAAE7D,IAAI,EAAExF,SAAS;IAAEwH,UAAU,EAAE,CAAC;MACtHhC,IAAI,EAAEtI,SAAS;MACfkK,IAAI,EAAE,CAAC,UAAU;IACrB,CAAC;EAAE,CAAC,EAAE;IAAE5B,IAAI,EAAExF,SAAS;IAAEwH,UAAU,EAAE,CAAC;MAClChC,IAAI,EAAE9I;IACV,CAAC,EAAE;MACC8I,IAAI,EAAE7I,MAAM;MACZyK,IAAI,EAAE,CAAC9I,iCAAiC;IAC5C,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEyL,SAAS,EAAE,CAAC;MACrCvE,IAAI,EAAE3I,KAAK;MACXuK,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAEgB,cAAc,EAAE,CAAC;MACjB5C,IAAI,EAAE3I,KAAK;MACXuK,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAEyB,cAAc,EAAE,CAAC;MACjBrD,IAAI,EAAErI,SAAS;MACfiK,IAAI,EAAE,CAAC,QAAQ;IACnB,CAAC,CAAC;IAAEhF,EAAE,EAAE,CAAC;MACLoD,IAAI,EAAE3I;IACV,CAAC,CAAC;IAAE2C,IAAI,EAAE,CAAC;MACPgG,IAAI,EAAE3I;IACV,CAAC,CAAC;IAAE0C,KAAK,EAAE,CAAC;MACRiG,IAAI,EAAE3I;IACV,CAAC,CAAC;IAAEmH,QAAQ,EAAE,CAAC;MACXwB,IAAI,EAAE3I;IACV,CAAC,CAAC;IAAEmN,aAAa,EAAE,CAAC;MAChBxE,IAAI,EAAE3I,KAAK;MACXuK,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAElL;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEyE,UAAU,EAAE,CAAC;MACbuE,IAAI,EAAE3I;IACV,CAAC,CAAC;IAAE0E,OAAO,EAAE,CAAC;MACViE,IAAI,EAAE3I,KAAK;MACXuK,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAElL;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE4B,QAAQ,EAAE,CAAC;MACXoH,IAAI,EAAE3I,KAAK;MACXuK,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAElL;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEoC,mBAAmB,EAAE,CAAC;MACtB4G,IAAI,EAAE3I,KAAK;MACXuK,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAElL;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEwE,MAAM,EAAE,CAAC;MACTwE,IAAI,EAAE1I;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM+O,qBAAqB,CAAC;EACxB;IAAS,IAAI,CAAC7G,IAAI,YAAA8G,8BAAA5G,iBAAA;MAAA,YAAAA,iBAAA,IAA+F2G,qBAAqB;IAAA,CAAkD;EAAE;EAC1L;IAAS,IAAI,CAACE,IAAI,kBA7RqF3P,EAAE,CAAA4P,gBAAA;MAAAxG,IAAA,EA6RSqG;IAAqB,EAA0J;EAAE;EACnS;IAAS,IAAI,CAACI,IAAI,kBA9RqF7P,EAAE,CAAA8P,gBAAA;MAAAN,OAAA,GA8R0CnO,eAAe,EAAEC,eAAe,EAAEoI,eAAe,EAAErI,eAAe;IAAA,EAAI;EAAE;AAC/N;AACA;EAAA,QAAA8G,SAAA,oBAAAA,SAAA,KAhS2GnI,EAAE,CAAA+K,iBAAA,CAgSX0E,qBAAqB,EAAc,CAAC;IAC1HrG,IAAI,EAAEpI,QAAQ;IACdgK,IAAI,EAAE,CAAC;MACCwE,OAAO,EAAE,CAACnO,eAAe,EAAEC,eAAe,EAAEuB,oBAAoB,EAAE6G,eAAe,CAAC;MAClFqG,OAAO,EAAE,CAAC1O,eAAe,EAAEwB,oBAAoB,EAAE6G,eAAe;IACpE,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASxH,iCAAiC,EAAEO,uBAAuB,EAAEJ,+CAA+C,EAAEK,sCAAsC,EAAEgH,eAAe,EAAE1G,qBAAqB,EAAEH,oBAAoB,EAAE4M,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}