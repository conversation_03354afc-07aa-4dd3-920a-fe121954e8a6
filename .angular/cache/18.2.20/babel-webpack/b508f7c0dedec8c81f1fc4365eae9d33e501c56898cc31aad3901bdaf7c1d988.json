{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FlexLayoutModule } from '@angular/flex-layout';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { ControlMessagesModule } from '../../../../../common/components/control-messages/control-messages.module';\nimport { ValidationService } from '../../../../../common/services/validation.service';\nimport { CustomerRespGamingComponent } from './customer-resp-gaming.component';\nimport { NoticeModule } from './notice/notice.module';\nimport { RgDepositLimitCasinoComponent } from './rg-deposit-limit/rg-deposit-limit-casino/rg-deposit-limit-casino.component';\nimport { RgDepositLimitDialogComponent } from './rg-deposit-limit/rg-deposit-limit-dialog/rg-deposit-limit-dialog.component';\nimport { RgDepositLimitComponent } from './rg-deposit-limit/rg-deposit-limit.component';\nimport { RgLossLimitCasinoComponent } from './rg-loss-limit/rg-loss-limit-casino/rg-loss-limit-casino.component';\nimport { RgLossLimitDialogComponent } from './rg-loss-limit/rg-loss-limit-dialog/rg-loss-limit-dialog.component';\nimport { RgLossLimitComponent } from './rg-loss-limit/rg-loss-limit.component';\nimport { RgRealityCheckCasinoComponent } from './rg-reality-check/rg-reality-check-casino/rg-reality-check-casino.component';\nimport { RgRealityCheckDialogComponent } from './rg-reality-check/rg-reality-check-dialog/rg-reality-check-dialog.component';\nimport { RgRealityCheckComponent } from './rg-reality-check/rg-reality-check.component';\nimport { RgSelfExclusionCasinoComponent } from './rg-self-exclusion/rg-self-exclusion-casino/rg-self-exclusion-casino.component';\nimport { RgSelfExclusionDialogComponent } from './rg-self-exclusion/rg-self-exclusion-dialog/rg-self-exclusion-dialog.component';\nimport { RgSelfExclusionComponent } from './rg-self-exclusion/rg-self-exclusion.component';\nimport { RgTimeOutCasinoComponent } from './rg-time-out/rg-time-out-casino/rg-time-out-casino.component';\nimport { RgTimeOutDialogComponent } from './rg-time-out/rg-time-out-dialog/rg-time-out-dialog.component';\nimport { RgTimeOutComponent } from './rg-time-out/rg-time-out.component';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatRadioModule } from '@angular/material/radio';\nimport * as i0 from \"@angular/core\";\nexport class CustomerRespGamingModule {\n  static {\n    this.ɵfac = function CustomerRespGamingModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CustomerRespGamingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: CustomerRespGamingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [ValidationService],\n      imports: [CommonModule, ReactiveFormsModule, ControlMessagesModule, NoticeModule, TranslateModule, MatCardModule, MatTabsModule, MatButtonModule, MatSelectModule, FlexLayoutModule, MatRadioModule, MatFormFieldModule, MatInputModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(CustomerRespGamingModule, {\n    declarations: [CustomerRespGamingComponent, RgRealityCheckComponent, RgRealityCheckCasinoComponent, RgLossLimitComponent, RgLossLimitCasinoComponent, RgDepositLimitComponent, RgDepositLimitCasinoComponent, RgTimeOutComponent, RgTimeOutCasinoComponent, RgSelfExclusionComponent, RgSelfExclusionCasinoComponent, RgRealityCheckDialogComponent, RgLossLimitDialogComponent, RgDepositLimitDialogComponent, RgTimeOutDialogComponent, RgSelfExclusionDialogComponent],\n    imports: [CommonModule, ReactiveFormsModule, ControlMessagesModule, NoticeModule, TranslateModule, MatCardModule, MatTabsModule, MatButtonModule, MatSelectModule, FlexLayoutModule, MatRadioModule, MatFormFieldModule, MatInputModule],\n    exports: [CustomerRespGamingComponent]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FlexLayoutModule", "ReactiveFormsModule", "TranslateModule", "ControlMessagesModule", "ValidationService", "CustomerRespGamingComponent", "NoticeModule", "RgDepositLimitCasinoComponent", "RgDepositLimitDialogComponent", "RgDepositLimitComponent", "RgLossLimitCasinoComponent", "RgLossLimitDialogComponent", "RgLossLimitComponent", "RgRealityCheckCasinoComponent", "RgRealityCheckDialogComponent", "RgRealityCheckComponent", "RgSelfExclusionCasinoComponent", "RgSelfExclusionDialogComponent", "RgSelfExclusionComponent", "RgTimeOutCasinoComponent", "RgTimeOutDialogComponent", "RgTimeOutComponent", "MatFormFieldModule", "MatSelectModule", "MatCardModule", "MatTabsModule", "MatInputModule", "MatButtonModule", "MatRadioModule", "CustomerRespGamingModule", "imports", "declarations", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/player/components/customer-page/customer-resp-gaming/customer-resp-gaming.module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { FlexLayoutModule } from '@angular/flex-layout';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { ControlMessagesModule } from '../../../../../common/components/control-messages/control-messages.module';\n\nimport { ValidationService } from '../../../../../common/services/validation.service';\nimport { CustomerRespGamingComponent } from './customer-resp-gaming.component';\nimport { NoticeModule } from './notice/notice.module';\nimport { RgDepositLimitCasinoComponent } from './rg-deposit-limit/rg-deposit-limit-casino/rg-deposit-limit-casino.component';\nimport { RgDepositLimitDialogComponent } from './rg-deposit-limit/rg-deposit-limit-dialog/rg-deposit-limit-dialog.component';\nimport { RgDepositLimitComponent } from './rg-deposit-limit/rg-deposit-limit.component';\nimport { RgLossLimitCasinoComponent } from './rg-loss-limit/rg-loss-limit-casino/rg-loss-limit-casino.component';\nimport { RgLossLimitDialogComponent } from './rg-loss-limit/rg-loss-limit-dialog/rg-loss-limit-dialog.component';\nimport { RgLossLimitComponent } from './rg-loss-limit/rg-loss-limit.component';\nimport { RgRealityCheckCasinoComponent } from './rg-reality-check/rg-reality-check-casino/rg-reality-check-casino.component';\nimport { RgRealityCheckDialogComponent } from './rg-reality-check/rg-reality-check-dialog/rg-reality-check-dialog.component';\nimport { RgRealityCheckComponent } from './rg-reality-check/rg-reality-check.component';\nimport { RgSelfExclusionCasinoComponent } from './rg-self-exclusion/rg-self-exclusion-casino/rg-self-exclusion-casino.component';\nimport { RgSelfExclusionDialogComponent } from './rg-self-exclusion/rg-self-exclusion-dialog/rg-self-exclusion-dialog.component';\nimport { RgSelfExclusionComponent } from './rg-self-exclusion/rg-self-exclusion.component';\nimport { RgTimeOutCasinoComponent } from './rg-time-out/rg-time-out-casino/rg-time-out-casino.component';\nimport { RgTimeOutDialogComponent } from './rg-time-out/rg-time-out-dialog/rg-time-out-dialog.component';\nimport { RgTimeOutComponent } from './rg-time-out/rg-time-out.component';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatRadioModule } from '@angular/material/radio';\n\n@NgModule({\n  declarations: [\n    CustomerRespGamingComponent,\n    RgRealityCheckComponent,\n    RgRealityCheckCasinoComponent,\n    RgLossLimitComponent,\n    RgLossLimitCasinoComponent,\n    RgDepositLimitComponent,\n    RgDepositLimitCasinoComponent,\n    RgTimeOutComponent,\n    RgTimeOutCasinoComponent,\n    RgSelfExclusionComponent,\n    RgSelfExclusionCasinoComponent,\n    RgRealityCheckDialogComponent,\n    RgLossLimitDialogComponent,\n    RgDepositLimitDialogComponent,\n    RgTimeOutDialogComponent,\n    RgSelfExclusionDialogComponent,\n  ],\n  exports: [CustomerRespGamingComponent],\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    ControlMessagesModule,\n    NoticeModule,\n    TranslateModule,\n\n    MatCardModule,\n    MatTabsModule,\n    MatButtonModule,\n    MatSelectModule,\n    FlexLayoutModule,\n    MatRadioModule,\n    MatFormFieldModule,\n    MatInputModule,\n  ],\n  providers: [\n    ValidationService,\n  ]\n})\n\nexport class CustomerRespGamingModule {\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,qBAAqB,QAAQ,2EAA2E;AAEjH,SAASC,iBAAiB,QAAQ,mDAAmD;AACrF,SAASC,2BAA2B,QAAQ,kCAAkC;AAC9E,SAASC,YAAY,QAAQ,wBAAwB;AACrD,SAASC,6BAA6B,QAAQ,8EAA8E;AAC5H,SAASC,6BAA6B,QAAQ,8EAA8E;AAC5H,SAASC,uBAAuB,QAAQ,+CAA+C;AACvF,SAASC,0BAA0B,QAAQ,qEAAqE;AAChH,SAASC,0BAA0B,QAAQ,qEAAqE;AAChH,SAASC,oBAAoB,QAAQ,yCAAyC;AAC9E,SAASC,6BAA6B,QAAQ,8EAA8E;AAC5H,SAASC,6BAA6B,QAAQ,8EAA8E;AAC5H,SAASC,uBAAuB,QAAQ,+CAA+C;AACvF,SAASC,8BAA8B,QAAQ,iFAAiF;AAChI,SAASC,8BAA8B,QAAQ,iFAAiF;AAChI,SAASC,wBAAwB,QAAQ,iDAAiD;AAC1F,SAASC,wBAAwB,QAAQ,+DAA+D;AACxG,SAASC,wBAAwB,QAAQ,+DAA+D;AACxG,SAASC,kBAAkB,QAAQ,qCAAqC;AACxE,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;;AA2CxD,OAAM,MAAOC,wBAAwB;;;uCAAxBA,wBAAwB;IAAA;EAAA;;;YAAxBA;IAAwB;EAAA;;;iBALxB,CACTzB,iBAAiB,CAClB;MAAA0B,OAAA,GAjBC/B,YAAY,EACZE,mBAAmB,EACnBE,qBAAqB,EACrBG,YAAY,EACZJ,eAAe,EAEfsB,aAAa,EACbC,aAAa,EACbE,eAAe,EACfJ,eAAe,EACfvB,gBAAgB,EAChB4B,cAAc,EACdN,kBAAkB,EAClBI,cAAc;IAAA;EAAA;;;2EAOLG,wBAAwB;IAAAE,YAAA,GAvCjC1B,2BAA2B,EAC3BU,uBAAuB,EACvBF,6BAA6B,EAC7BD,oBAAoB,EACpBF,0BAA0B,EAC1BD,uBAAuB,EACvBF,6BAA6B,EAC7Bc,kBAAkB,EAClBF,wBAAwB,EACxBD,wBAAwB,EACxBF,8BAA8B,EAC9BF,6BAA6B,EAC7BH,0BAA0B,EAC1BH,6BAA6B,EAC7BY,wBAAwB,EACxBH,8BAA8B;IAAAa,OAAA,GAI9B/B,YAAY,EACZE,mBAAmB,EACnBE,qBAAqB,EACrBG,YAAY,EACZJ,eAAe,EAEfsB,aAAa,EACbC,aAAa,EACbE,eAAe,EACfJ,eAAe,EACfvB,gBAAgB,EAChB4B,cAAc,EACdN,kBAAkB,EAClBI,cAAc;IAAAM,OAAA,GAfN3B,2BAA2B;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}