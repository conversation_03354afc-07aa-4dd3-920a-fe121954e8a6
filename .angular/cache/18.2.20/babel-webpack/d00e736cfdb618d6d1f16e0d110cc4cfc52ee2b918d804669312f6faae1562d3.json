{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { FlexLayoutModule } from '@angular/flex-layout';\nimport { MatButtonModule } from '@angular/material/button';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { BoConfirmationComponent } from './bo-confirmation.component';\nimport * as i0 from \"@angular/core\";\nexport class BoConfirmationModule {\n  static {\n    this.ɵfac = function BoConfirmationModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || BoConfirmationModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: BoConfirmationModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, TranslateModule, MatButtonModule, FlexLayoutModule, MatDialogModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(BoConfirmationModule, {\n    declarations: [BoConfirmationComponent],\n    imports: [CommonModule, TranslateModule, MatButtonModule, FlexLayoutModule, MatDialogModule],\n    exports: [BoConfirmationComponent]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "MatDialogModule", "FlexLayoutModule", "MatButtonModule", "TranslateModule", "BoConfirmationComponent", "BoConfirmationModule", "declarations", "imports", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/common/components/bo-confirmation/bo-confirmation.module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { MatDialogModule } from '@angular/material/dialog';\n\nimport { FlexLayoutModule } from '@angular/flex-layout';\nimport { MatButtonModule } from '@angular/material/button';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { BoConfirmationComponent } from './bo-confirmation.component';\n\n@NgModule({\n  imports: [\n    CommonModule,\n    TranslateModule,\n    MatButtonModule,\n    FlexLayoutModule,\n    MatDialogModule\n  ],\n  exports: [BoConfirmationComponent],\n  declarations: [BoConfirmationComponent],\n  providers: [],\n\n})\nexport class BoConfirmationModule {\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,eAAe,QAAQ,0BAA0B;AAE1D,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,uBAAuB,QAAQ,6BAA6B;;AAerE,OAAM,MAAOC,oBAAoB;;;uCAApBA,oBAAoB;IAAA;EAAA;;;YAApBA;IAAoB;EAAA;;;gBAX7BN,YAAY,EACZI,eAAe,EACfD,eAAe,EACfD,gBAAgB,EAChBD,eAAe;IAAA;EAAA;;;2EAONK,oBAAoB;IAAAC,YAAA,GAJhBF,uBAAuB;IAAAG,OAAA,GAPpCR,YAAY,EACZI,eAAe,EACfD,eAAe,EACfD,gBAAgB,EAChBD,eAAe;IAAAQ,OAAA,GAEPJ,uBAAuB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}