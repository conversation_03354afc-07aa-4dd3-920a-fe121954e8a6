{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FlexLayoutModule } from '@angular/flex-layout';\nimport { FormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiPagePanelModule } from '@skywind-group/lib-swui';\nimport { PipesModule } from '../../../common/pipes/pipes.module';\nimport { CdnService } from '../../../common/services/cdn.service';\nimport { GameStoreGameDetailsDialogComponent } from './game-store-game-details-dialog/game-store-game-details-dialog.component';\nimport { GameStoreGameDetailsComponent } from './game-store-game-details.component';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatListModule } from '@angular/material/list';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatChipsModule } from '@angular/material/chips';\nimport * as i0 from \"@angular/core\";\nexport class GameStoreGameDetailsModule {\n  static {\n    this.ɵfac = function GameStoreGameDetailsModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || GameStoreGameDetailsModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: GameStoreGameDetailsModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [CdnService],\n      imports: [CommonModule, RouterModule, FormsModule, PipesModule, TranslateModule, SwuiPagePanelModule, MatCardModule, MatTabsModule, MatButtonModule, MatChipsModule, MatTooltipModule, MatExpansionModule, MatIconModule, MatDialogModule, MatListModule, FlexLayoutModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(GameStoreGameDetailsModule, {\n    declarations: [GameStoreGameDetailsComponent, GameStoreGameDetailsDialogComponent],\n    imports: [CommonModule, RouterModule, FormsModule, PipesModule, TranslateModule, SwuiPagePanelModule, MatCardModule, MatTabsModule, MatButtonModule, MatChipsModule, MatTooltipModule, MatExpansionModule, MatIconModule, MatDialogModule, MatListModule, FlexLayoutModule],\n    exports: [GameStoreGameDetailsComponent, GameStoreGameDetailsDialogComponent]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FlexLayoutModule", "FormsModule", "RouterModule", "TranslateModule", "SwuiPagePanelModule", "PipesModule", "CdnService", "GameStoreGameDetailsDialogComponent", "GameStoreGameDetailsComponent", "MatIconModule", "MatDialogModule", "MatCardModule", "MatListModule", "MatTabsModule", "MatTooltipModule", "MatExpansionModule", "MatButtonModule", "MatChipsModule", "GameStoreGameDetailsModule", "imports", "declarations", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/game-store/game-store-game-details/game-store-game-details.module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { FlexLayoutModule } from '@angular/flex-layout';\nimport { FormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiPagePanelModule } from '@skywind-group/lib-swui';\nimport { PipesModule } from '../../../common/pipes/pipes.module';\nimport { CdnService } from '../../../common/services/cdn.service';\n\nimport { GameStoreGameDetailsDialogComponent } from './game-store-game-details-dialog/game-store-game-details-dialog.component';\nimport { GameStoreGameDetailsComponent } from './game-store-game-details.component';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatListModule } from '@angular/material/list';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatChipsModule } from '@angular/material/chips';\n\n\n@NgModule({\n  imports: [\n    CommonModule,\n    RouterModule,\n    FormsModule,\n    PipesModule,\n    TranslateModule,\n    SwuiPagePanelModule,\n    MatCardModule,\n    MatTabsModule,\n    MatButtonModule,\n    MatChipsModule,\n    MatTooltipModule,\n    MatExpansionModule,\n    MatIconModule,\n    MatDialogModule,\n    MatListModule,\n    FlexLayoutModule,\n  ],\n  exports: [\n    GameStoreGameDetailsComponent,\n    GameStoreGameDetailsDialogComponent,\n  ],\n  declarations: [\n    GameStoreGameDetailsComponent,\n    GameStoreGameDetailsDialogComponent,\n  ],\n  providers: [\n    CdnService,\n  ],\n})\nexport class GameStoreGameDetailsModule {\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,mBAAmB,QAAQ,yBAAyB;AAC7D,SAASC,WAAW,QAAQ,oCAAoC;AAChE,SAASC,UAAU,QAAQ,sCAAsC;AAEjE,SAASC,mCAAmC,QAAQ,2EAA2E;AAC/H,SAASC,6BAA6B,QAAQ,qCAAqC;AACnF,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;;AAkCxD,OAAM,MAAOC,0BAA0B;;;uCAA1BA,0BAA0B;IAAA;EAAA;;;YAA1BA;IAA0B;EAAA;;;iBAJ1B,CACTZ,UAAU,CACX;MAAAa,OAAA,GA3BCpB,YAAY,EACZG,YAAY,EACZD,WAAW,EACXI,WAAW,EACXF,eAAe,EACfC,mBAAmB,EACnBO,aAAa,EACbE,aAAa,EACbG,eAAe,EACfC,cAAc,EACdH,gBAAgB,EAChBC,kBAAkB,EAClBN,aAAa,EACbC,eAAe,EACfE,aAAa,EACbZ,gBAAgB;IAAA;EAAA;;;2EAcPkB,0BAA0B;IAAAE,YAAA,GAPnCZ,6BAA6B,EAC7BD,mCAAmC;IAAAY,OAAA,GAvBnCpB,YAAY,EACZG,YAAY,EACZD,WAAW,EACXI,WAAW,EACXF,eAAe,EACfC,mBAAmB,EACnBO,aAAa,EACbE,aAAa,EACbG,eAAe,EACfC,cAAc,EACdH,gBAAgB,EAChBC,kBAAkB,EAClBN,aAAa,EACbC,eAAe,EACfE,aAAa,EACbZ,gBAAgB;IAAAqB,OAAA,GAGhBb,6BAA6B,EAC7BD,mCAAmC;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}