{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { FlexLayoutModule } from '@angular/flex-layout';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatRadioModule } from '@angular/material/radio';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiChipsAutocompleteModule, SwuiControlMessagesModule, SwuiSelectModule } from '@skywind-group/lib-swui';\nimport { TrimInputValueModule } from '../../directives/trim-input-value/trim-input-value.module';\nimport { UserEditorService } from './user-editor.service';\nimport { UserFormComponent } from './user-form.component';\nimport { UserEditorDialogComponent } from './user-editor-dialog.component';\nimport { ControlMessagesModule } from '../control-messages/control-messages.module';\nimport { BoConfirmationModule } from '../bo-confirmation/bo-confirmation.module';\nimport * as i0 from \"@angular/core\";\nexport class MatUserEditorModule {\n  static {\n    this.ɵfac = function MatUserEditorModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatUserEditorModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: MatUserEditorModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [UserEditorService],\n      imports: [CommonModule, TranslateModule, ControlMessagesModule, ReactiveFormsModule, MatDialogModule, MatButtonModule, MatFormFieldModule, MatInputModule, FlexLayoutModule, MatProgressSpinnerModule, MatProgressBarModule, MatSelectModule, MatRadioModule, SwuiChipsAutocompleteModule, SwuiSelectModule, BoConfirmationModule, SwuiControlMessagesModule, MatCardModule, TrimInputValueModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(MatUserEditorModule, {\n    declarations: [UserFormComponent, UserEditorDialogComponent],\n    imports: [CommonModule, TranslateModule, ControlMessagesModule, ReactiveFormsModule, MatDialogModule, MatButtonModule, MatFormFieldModule, MatInputModule, FlexLayoutModule, MatProgressSpinnerModule, MatProgressBarModule, MatSelectModule, MatRadioModule, SwuiChipsAutocompleteModule, SwuiSelectModule, BoConfirmationModule, SwuiControlMessagesModule, MatCardModule, TrimInputValueModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "ReactiveFormsModule", "FlexLayoutModule", "MatFormFieldModule", "MatProgressBarModule", "MatSelectModule", "MatDialogModule", "MatCardModule", "MatInputModule", "MatProgressSpinnerModule", "MatButtonModule", "MatRadioModule", "TranslateModule", "SwuiChipsAutocompleteModule", "SwuiControlMessagesModule", "SwuiSelectModule", "TrimInputValueModule", "UserEditorService", "UserFormComponent", "UserEditorDialogComponent", "ControlMessagesModule", "BoConfirmationModule", "MatUserEditorModule", "imports", "declarations"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/common/components/mat-user-editor/user-editor.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { FlexLayoutModule } from '@angular/flex-layout';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatRadioModule } from '@angular/material/radio';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiChipsAutocompleteModule, SwuiControlMessagesModule, SwuiSelectModule } from '@skywind-group/lib-swui';\nimport { TrimInputValueModule } from '../../directives/trim-input-value/trim-input-value.module';\n\nimport { UserEditorService } from './user-editor.service';\nimport { UserFormComponent } from './user-form.component';\nimport { UserEditorDialogComponent } from './user-editor-dialog.component';\nimport { ControlMessagesModule } from '../control-messages/control-messages.module';\nimport { BoConfirmationModule } from '../bo-confirmation/bo-confirmation.module';\n\n\n@NgModule({\n    imports: [\n        CommonModule,\n        TranslateModule,\n        ControlMessagesModule,\n        ReactiveFormsModule,\n        MatDialogModule,\n        MatButtonModule,\n        MatFormFieldModule,\n        MatInputModule,\n        FlexLayoutModule,\n        MatProgressSpinnerModule,\n        MatProgressBarModule,\n        MatSelectModule,\n        MatRadioModule,\n        SwuiChipsAutocompleteModule,\n        SwuiSelectModule,\n        BoConfirmationModule,\n        SwuiControlMessagesModule,\n        MatCardModule,\n        TrimInputValueModule,\n    ],\n  declarations: [\n    UserFormComponent,\n    UserEditorDialogComponent,\n  ],\n  providers: [\n    UserEditorService,\n  ],\n})\nexport class MatUserEditorModule {\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,2BAA2B,EAAEC,yBAAyB,EAAEC,gBAAgB,QAAQ,yBAAyB;AAClH,SAASC,oBAAoB,QAAQ,2DAA2D;AAEhG,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD,SAASC,yBAAyB,QAAQ,gCAAgC;AAC1E,SAASC,qBAAqB,QAAQ,6CAA6C;AACnF,SAASC,oBAAoB,QAAQ,2CAA2C;;AAiChF,OAAM,MAAOC,mBAAmB;;;uCAAnBA,mBAAmB;IAAA;EAAA;;;YAAnBA;IAAmB;EAAA;;;iBAJnB,CACTL,iBAAiB,CAClB;MAAAM,OAAA,GA1BKvB,YAAY,EACZY,eAAe,EACfQ,qBAAqB,EACrBnB,mBAAmB,EACnBK,eAAe,EACfI,eAAe,EACfP,kBAAkB,EAClBK,cAAc,EACdN,gBAAgB,EAChBO,wBAAwB,EACxBL,oBAAoB,EACpBC,eAAe,EACfM,cAAc,EACdE,2BAA2B,EAC3BE,gBAAgB,EAChBM,oBAAoB,EACpBP,yBAAyB,EACzBP,aAAa,EACbS,oBAAoB;IAAA;EAAA;;;2EAUfM,mBAAmB;IAAAE,YAAA,GAP5BN,iBAAiB,EACjBC,yBAAyB;IAAAI,OAAA,GAtBrBvB,YAAY,EACZY,eAAe,EACfQ,qBAAqB,EACrBnB,mBAAmB,EACnBK,eAAe,EACfI,eAAe,EACfP,kBAAkB,EAClBK,cAAc,EACdN,gBAAgB,EAChBO,wBAAwB,EACxBL,oBAAoB,EACpBC,eAAe,EACfM,cAAc,EACdE,2BAA2B,EAC3BE,gBAAgB,EAChBM,oBAAoB,EACpBP,yBAAyB,EACzBP,aAAa,EACbS,oBAAoB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}