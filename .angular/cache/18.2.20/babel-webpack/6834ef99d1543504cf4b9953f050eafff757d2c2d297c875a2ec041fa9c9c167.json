{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class DisableIfNotAllowedDirective {\n  constructor(el, renderer) {\n    this.el = el;\n    this.renderer = renderer;\n  }\n  ngOnChanges() {\n    if (!this.isAllowed) {\n      this.renderer.setAttribute(this.el.nativeElement, 'disabled', 'disabled');\n    } else {\n      this.renderer.removeAttribute(this.el.nativeElement, 'disabled');\n    }\n  }\n  static {\n    this.ɵfac = function DisableIfNotAllowedDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DisableIfNotAllowedDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2));\n    };\n  }\n  static {\n    this.ɵdir = /*@__PURE__*/i0.ɵɵdefineDirective({\n      type: DisableIfNotAllowedDirective,\n      selectors: [[\"\", \"ifNotAllowedDisable\", \"\"]],\n      inputs: {\n        isAllowed: [0, \"ifNotAllowedDisable\", \"isAllowed\"]\n      },\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}", "map": {"version": 3, "names": ["DisableIfNotAllowedDirective", "constructor", "el", "renderer", "ngOnChanges", "isAllowed", "setAttribute", "nativeElement", "removeAttribute", "i0", "ɵɵdirectiveInject", "ElementRef", "Renderer2", "selectors", "inputs", "features", "ɵɵNgOnChangesFeature"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/common/directives/disable-if-not-allowed/disable-if-not-allowed.directive.ts"], "sourcesContent": ["import { Directive, ElementRef, Input, Renderer2 } from '@angular/core';\n\n\n@Directive({\n  selector: '[ifNotAllowedDisable]'\n})\nexport class DisableIfNotAllowedDirective {\n\n  @Input('ifNotAllowedDisable')\n  private isAllowed: boolean;\n\n  constructor( private el: ElementRef, private renderer: Renderer2 ) {}\n\n  ngOnChanges() {\n    if (!this.isAllowed) {\n      this.renderer.setAttribute(this.el.nativeElement, 'disabled', 'disabled');\n    } else {\n      this.renderer.removeAttribute(this.el.nativeElement, 'disabled');\n    }\n  }\n}\n"], "mappings": ";AAMA,OAAM,MAAOA,4BAA4B;EAKvCC,YAAqBC,EAAc,EAAUC,QAAmB;IAA3C,KAAAD,EAAE,GAAFA,EAAE;IAAsB,KAAAC,QAAQ,GAARA,QAAQ;EAAe;EAEpEC,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACC,SAAS,EAAE;MACnB,IAAI,CAACF,QAAQ,CAACG,YAAY,CAAC,IAAI,CAACJ,EAAE,CAACK,aAAa,EAAE,UAAU,EAAE,UAAU,CAAC;IAC3E,CAAC,MAAM;MACL,IAAI,CAACJ,QAAQ,CAACK,eAAe,CAAC,IAAI,CAACN,EAAE,CAACK,aAAa,EAAE,UAAU,CAAC;IAClE;EACF;;;uCAbWP,4BAA4B,EAAAS,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAE,UAAA,GAAAF,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAG,SAAA;IAAA;EAAA;;;YAA5BZ,4BAA4B;MAAAa,SAAA;MAAAC,MAAA;QAAAT,SAAA;MAAA;MAAAU,QAAA,GAAAN,EAAA,CAAAO,oBAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}