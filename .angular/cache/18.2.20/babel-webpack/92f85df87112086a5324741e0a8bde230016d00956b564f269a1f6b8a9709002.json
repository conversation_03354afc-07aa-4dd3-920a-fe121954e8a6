{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, '__esModule', {\n  value: true\n});\nfunction _interopDefault(ex) {\n  return ex && typeof ex === 'object' && 'default' in ex ? ex['default'] : ex;\n}\nvar arrayFrom = _interopDefault(require('array-from'));\nvar isArray = _interopDefault(require('is-array'));\nvar isElement = _interopDefault(require('iselement'));\nvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) {\n  return typeof obj;\n} : function (obj) {\n  return obj && typeof Symbol === \"function\" && obj.constructor === Symbol ? \"symbol\" : typeof obj;\n};\n\n/**\n * Returns `true` if provided input is Element.\n * @name isElement\n * @param {*} [input]\n * @returns {boolean}\n */\nvar isElement$1 = function (input) {\n  return input != null && (typeof input === 'undefined' ? 'undefined' : _typeof(input)) === 'object' && input.nodeType === 1 && _typeof(input.style) === 'object' && _typeof(input.ownerDocument) === 'object';\n};\nfunction select(selector) {\n  if (typeof selector === 'string') {\n    try {\n      return document.querySelector(selector);\n    } catch (e) {\n      throw e;\n    }\n  } else if (isElement(selector)) {\n    return selector;\n  }\n}\nfunction selectAll(selector) {\n  if (typeof selector === 'string') {\n    return Array.prototype.slice.apply(document.querySelectorAll(selector));\n  } else if (isArray(selector)) {\n    return selector.map(select);\n  } else if ('length' in selector) {\n    return arrayFrom(selector).map(select);\n  }\n}\nfunction indexOfElement(elements, element) {\n  element = resolveElement(element, true);\n  if (!isElement$1(element)) {\n    return -1;\n  }\n  for (var i = 0; i < elements.length; i++) {\n    if (elements[i] === element) {\n      return i;\n    }\n  }\n  return -1;\n}\nfunction hasElement(elements, element) {\n  return -1 !== indexOfElement(elements, element);\n}\nfunction domListOf(arr) {\n  if (!arr) {\n    return [];\n  }\n  try {\n    if (typeof arr === 'string') {\n      return arrayFrom(document.querySelectorAll(arr));\n    } else if (isArray(arr)) {\n      return arr.map(resolveElement);\n    } else {\n      if (typeof arr.length === 'undefined') {\n        return [resolveElement(arr)];\n      }\n      return arrayFrom(arr, resolveElement);\n    }\n  } catch (e) {\n    throw new Error(e);\n  }\n}\nfunction concatElementLists() {\n  var lists = [],\n    len = arguments.length;\n  while (len--) lists[len] = arguments[len];\n  return lists.reduce(function (last, list) {\n    return list.length ? last : last.concat(domListOf(list));\n  }, []);\n}\nfunction pushElements(elements, toAdd) {\n  for (var i = 0; i < toAdd.length; i++) {\n    if (!hasElement(elements, toAdd[i])) {\n      elements.push(toAdd[i]);\n    }\n  }\n  return toAdd;\n}\nfunction addElements(elements) {\n  var toAdd = [],\n    len = arguments.length - 1;\n  while (len-- > 0) toAdd[len] = arguments[len + 1];\n  toAdd = toAdd.map(resolveElement);\n  return pushElements(elements, toAdd);\n}\nfunction removeElements(elements) {\n  var toRemove = [],\n    len = arguments.length - 1;\n  while (len-- > 0) toRemove[len] = arguments[len + 1];\n  return toRemove.map(resolveElement).reduce(function (last, e) {\n    var index = indexOfElement(elements, e);\n    if (index !== -1) {\n      return last.concat(elements.splice(index, 1));\n    }\n    return last;\n  }, []);\n}\nfunction resolveElement(element, noThrow) {\n  if (typeof element === 'string') {\n    try {\n      return document.querySelector(element);\n    } catch (e) {\n      throw e;\n    }\n  }\n  if (!isElement$1(element) && !noThrow) {\n    throw new TypeError(element + \" is not a DOM element.\");\n  }\n  return element;\n}\nexports.indexOfElement = indexOfElement;\nexports.hasElement = hasElement;\nexports.domListOf = domListOf;\nexports.concatElementLists = concatElementLists;\nexports.addElements = addElements;\nexports.removeElements = removeElements;\nexports.resolveElement = resolveElement;\nexports.select = select;\nexports.selectAll = selectAll;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "_interopDefault", "ex", "arrayFrom", "require", "isArray", "isElement", "_typeof", "Symbol", "iterator", "obj", "constructor", "isElement$1", "input", "nodeType", "style", "ownerDocument", "select", "selector", "document", "querySelector", "e", "selectAll", "Array", "prototype", "slice", "apply", "querySelectorAll", "map", "indexOfElement", "elements", "element", "resolveElement", "i", "length", "hasElement", "domListOf", "arr", "Error", "concatElementLists", "lists", "len", "arguments", "reduce", "last", "list", "concat", "pushElements", "toAdd", "push", "addElements", "removeElements", "toRemove", "index", "splice", "noThrow", "TypeError"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/node_modules/dom-set/dist/bundle.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, '__esModule', { value: true });\n\nfunction _interopDefault (ex) { return (ex && (typeof ex === 'object') && 'default' in ex) ? ex['default'] : ex; }\n\nvar arrayFrom = _interopDefault(require('array-from'));\nvar isArray = _interopDefault(require('is-array'));\nvar isElement = _interopDefault(require('iselement'));\n\nvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol ? \"symbol\" : typeof obj; };\n\n/**\n * Returns `true` if provided input is Element.\n * @name isElement\n * @param {*} [input]\n * @returns {boolean}\n */\nvar isElement$1 = function (input) {\n  return input != null && (typeof input === 'undefined' ? 'undefined' : _typeof(input)) === 'object' && input.nodeType === 1 && _typeof(input.style) === 'object' && _typeof(input.ownerDocument) === 'object';\n};\n\nfunction select(selector){\n    if(typeof selector === 'string'){\n        try{\n            return document.querySelector(selector);\n        }catch(e){\n            throw e;\n        }\n    }else if(isElement(selector)){\n        return selector;\n    }\n}\n\nfunction selectAll(selector){\n    if(typeof selector === 'string'){\n        return Array.prototype.slice.apply(\n            document.querySelectorAll(selector)\n        );\n    }else if(isArray(selector)){\n        return selector.map(select);\n    }else if('length' in selector){\n        return arrayFrom(selector).map(select);\n    }\n}\n\nfunction indexOfElement(elements, element){\n    element = resolveElement(element, true);\n    if(!isElement$1(element)) { return -1; }\n    for(var i=0; i<elements.length; i++){\n        if(elements[i] === element){\n            return i;\n        }\n    }\n    return -1;\n}\n\nfunction hasElement(elements, element){\n    return -1 !== indexOfElement(elements, element);\n}\n\nfunction domListOf(arr){\n\n    if(!arr) { return []; }\n\n    try{\n        if(typeof arr === 'string'){\n            return arrayFrom(document.querySelectorAll(arr));\n        }else if(isArray(arr)){\n            return arr.map(resolveElement);\n        }else{\n            if(typeof arr.length === 'undefined'){\n                return [resolveElement(arr)];\n            }\n\n            return arrayFrom(arr, resolveElement);\n\n        }\n    }catch(e){\n        throw new Error(e);\n    }\n\n}\n\nfunction concatElementLists(){\n    var lists = [], len = arguments.length;\n    while ( len-- ) lists[ len ] = arguments[ len ];\n\n    return lists.reduce(function (last, list){\n        return list.length ? last : last.concat(domListOf(list));\n    }, []);\n}\n\nfunction pushElements(elements, toAdd){\n\n    for(var i=0; i<toAdd.length; i++){\n        if(!hasElement(elements, toAdd[i]))\n            { elements.push(toAdd[i]); }\n    }\n\n    return toAdd;\n}\n\nfunction addElements(elements){\n    var toAdd = [], len = arguments.length - 1;\n    while ( len-- > 0 ) toAdd[ len ] = arguments[ len + 1 ];\n\n    toAdd = toAdd.map(resolveElement);\n    return pushElements(elements, toAdd);\n}\n\nfunction removeElements(elements){\n    var toRemove = [], len = arguments.length - 1;\n    while ( len-- > 0 ) toRemove[ len ] = arguments[ len + 1 ];\n\n    return toRemove.map(resolveElement).reduce(function (last, e){\n\n        var index = indexOfElement(elements, e);\n\n        if(index !== -1)\n            { return last.concat(elements.splice(index, 1)); }\n        return last;\n    }, []);\n}\n\nfunction resolveElement(element, noThrow){\n    if(typeof element === 'string'){\n        try{\n            return document.querySelector(element);\n        }catch(e){\n            throw e;\n        }\n\n    }\n\n    if(!isElement$1(element) && !noThrow){\n        throw new TypeError((element + \" is not a DOM element.\"));\n    }\n    return element;\n}\n\nexports.indexOfElement = indexOfElement;\nexports.hasElement = hasElement;\nexports.domListOf = domListOf;\nexports.concatElementLists = concatElementLists;\nexports.addElements = addElements;\nexports.removeElements = removeElements;\nexports.resolveElement = resolveElement;\nexports.select = select;\nexports.selectAll = selectAll;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAE7D,SAASC,eAAeA,CAAEC,EAAE,EAAE;EAAE,OAAQA,EAAE,IAAK,OAAOA,EAAE,KAAK,QAAS,IAAI,SAAS,IAAIA,EAAE,GAAIA,EAAE,CAAC,SAAS,CAAC,GAAGA,EAAE;AAAE;AAEjH,IAAIC,SAAS,GAAGF,eAAe,CAACG,OAAO,CAAC,YAAY,CAAC,CAAC;AACtD,IAAIC,OAAO,GAAGJ,eAAe,CAACG,OAAO,CAAC,UAAU,CAAC,CAAC;AAClD,IAAIE,SAAS,GAAGL,eAAe,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC;AAErD,IAAIG,OAAO,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAI,OAAOA,MAAM,CAACC,QAAQ,KAAK,QAAQ,GAAG,UAAUC,GAAG,EAAE;EAAE,OAAO,OAAOA,GAAG;AAAE,CAAC,GAAG,UAAUA,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAI,OAAOF,MAAM,KAAK,UAAU,IAAIE,GAAG,CAACC,WAAW,KAAKH,MAAM,GAAG,QAAQ,GAAG,OAAOE,GAAG;AAAE,CAAC;;AAEhP;AACA;AACA;AACA;AACA;AACA;AACA,IAAIE,WAAW,GAAG,SAAAA,CAAUC,KAAK,EAAE;EACjC,OAAOA,KAAK,IAAI,IAAI,IAAI,CAAC,OAAOA,KAAK,KAAK,WAAW,GAAG,WAAW,GAAGN,OAAO,CAACM,KAAK,CAAC,MAAM,QAAQ,IAAIA,KAAK,CAACC,QAAQ,KAAK,CAAC,IAAIP,OAAO,CAACM,KAAK,CAACE,KAAK,CAAC,KAAK,QAAQ,IAAIR,OAAO,CAACM,KAAK,CAACG,aAAa,CAAC,KAAK,QAAQ;AAC9M,CAAC;AAED,SAASC,MAAMA,CAACC,QAAQ,EAAC;EACrB,IAAG,OAAOA,QAAQ,KAAK,QAAQ,EAAC;IAC5B,IAAG;MACC,OAAOC,QAAQ,CAACC,aAAa,CAACF,QAAQ,CAAC;IAC3C,CAAC,QAAMG,CAAC,EAAC;MACL,MAAMA,CAAC;IACX;EACJ,CAAC,MAAK,IAAGf,SAAS,CAACY,QAAQ,CAAC,EAAC;IACzB,OAAOA,QAAQ;EACnB;AACJ;AAEA,SAASI,SAASA,CAACJ,QAAQ,EAAC;EACxB,IAAG,OAAOA,QAAQ,KAAK,QAAQ,EAAC;IAC5B,OAAOK,KAAK,CAACC,SAAS,CAACC,KAAK,CAACC,KAAK,CAC9BP,QAAQ,CAACQ,gBAAgB,CAACT,QAAQ,CACtC,CAAC;EACL,CAAC,MAAK,IAAGb,OAAO,CAACa,QAAQ,CAAC,EAAC;IACvB,OAAOA,QAAQ,CAACU,GAAG,CAACX,MAAM,CAAC;EAC/B,CAAC,MAAK,IAAG,QAAQ,IAAIC,QAAQ,EAAC;IAC1B,OAAOf,SAAS,CAACe,QAAQ,CAAC,CAACU,GAAG,CAACX,MAAM,CAAC;EAC1C;AACJ;AAEA,SAASY,cAAcA,CAACC,QAAQ,EAAEC,OAAO,EAAC;EACtCA,OAAO,GAAGC,cAAc,CAACD,OAAO,EAAE,IAAI,CAAC;EACvC,IAAG,CAACnB,WAAW,CAACmB,OAAO,CAAC,EAAE;IAAE,OAAO,CAAC,CAAC;EAAE;EACvC,KAAI,IAAIE,CAAC,GAAC,CAAC,EAAEA,CAAC,GAACH,QAAQ,CAACI,MAAM,EAAED,CAAC,EAAE,EAAC;IAChC,IAAGH,QAAQ,CAACG,CAAC,CAAC,KAAKF,OAAO,EAAC;MACvB,OAAOE,CAAC;IACZ;EACJ;EACA,OAAO,CAAC,CAAC;AACb;AAEA,SAASE,UAAUA,CAACL,QAAQ,EAAEC,OAAO,EAAC;EAClC,OAAO,CAAC,CAAC,KAAKF,cAAc,CAACC,QAAQ,EAAEC,OAAO,CAAC;AACnD;AAEA,SAASK,SAASA,CAACC,GAAG,EAAC;EAEnB,IAAG,CAACA,GAAG,EAAE;IAAE,OAAO,EAAE;EAAE;EAEtB,IAAG;IACC,IAAG,OAAOA,GAAG,KAAK,QAAQ,EAAC;MACvB,OAAOlC,SAAS,CAACgB,QAAQ,CAACQ,gBAAgB,CAACU,GAAG,CAAC,CAAC;IACpD,CAAC,MAAK,IAAGhC,OAAO,CAACgC,GAAG,CAAC,EAAC;MAClB,OAAOA,GAAG,CAACT,GAAG,CAACI,cAAc,CAAC;IAClC,CAAC,MAAI;MACD,IAAG,OAAOK,GAAG,CAACH,MAAM,KAAK,WAAW,EAAC;QACjC,OAAO,CAACF,cAAc,CAACK,GAAG,CAAC,CAAC;MAChC;MAEA,OAAOlC,SAAS,CAACkC,GAAG,EAAEL,cAAc,CAAC;IAEzC;EACJ,CAAC,QAAMX,CAAC,EAAC;IACL,MAAM,IAAIiB,KAAK,CAACjB,CAAC,CAAC;EACtB;AAEJ;AAEA,SAASkB,kBAAkBA,CAAA,EAAE;EACzB,IAAIC,KAAK,GAAG,EAAE;IAAEC,GAAG,GAAGC,SAAS,CAACR,MAAM;EACtC,OAAQO,GAAG,EAAE,EAAGD,KAAK,CAAEC,GAAG,CAAE,GAAGC,SAAS,CAAED,GAAG,CAAE;EAE/C,OAAOD,KAAK,CAACG,MAAM,CAAC,UAAUC,IAAI,EAAEC,IAAI,EAAC;IACrC,OAAOA,IAAI,CAACX,MAAM,GAAGU,IAAI,GAAGA,IAAI,CAACE,MAAM,CAACV,SAAS,CAACS,IAAI,CAAC,CAAC;EAC5D,CAAC,EAAE,EAAE,CAAC;AACV;AAEA,SAASE,YAAYA,CAACjB,QAAQ,EAAEkB,KAAK,EAAC;EAElC,KAAI,IAAIf,CAAC,GAAC,CAAC,EAAEA,CAAC,GAACe,KAAK,CAACd,MAAM,EAAED,CAAC,EAAE,EAAC;IAC7B,IAAG,CAACE,UAAU,CAACL,QAAQ,EAAEkB,KAAK,CAACf,CAAC,CAAC,CAAC,EAC9B;MAAEH,QAAQ,CAACmB,IAAI,CAACD,KAAK,CAACf,CAAC,CAAC,CAAC;IAAE;EACnC;EAEA,OAAOe,KAAK;AAChB;AAEA,SAASE,WAAWA,CAACpB,QAAQ,EAAC;EAC1B,IAAIkB,KAAK,GAAG,EAAE;IAAEP,GAAG,GAAGC,SAAS,CAACR,MAAM,GAAG,CAAC;EAC1C,OAAQO,GAAG,EAAE,GAAG,CAAC,EAAGO,KAAK,CAAEP,GAAG,CAAE,GAAGC,SAAS,CAAED,GAAG,GAAG,CAAC,CAAE;EAEvDO,KAAK,GAAGA,KAAK,CAACpB,GAAG,CAACI,cAAc,CAAC;EACjC,OAAOe,YAAY,CAACjB,QAAQ,EAAEkB,KAAK,CAAC;AACxC;AAEA,SAASG,cAAcA,CAACrB,QAAQ,EAAC;EAC7B,IAAIsB,QAAQ,GAAG,EAAE;IAAEX,GAAG,GAAGC,SAAS,CAACR,MAAM,GAAG,CAAC;EAC7C,OAAQO,GAAG,EAAE,GAAG,CAAC,EAAGW,QAAQ,CAAEX,GAAG,CAAE,GAAGC,SAAS,CAAED,GAAG,GAAG,CAAC,CAAE;EAE1D,OAAOW,QAAQ,CAACxB,GAAG,CAACI,cAAc,CAAC,CAACW,MAAM,CAAC,UAAUC,IAAI,EAAEvB,CAAC,EAAC;IAEzD,IAAIgC,KAAK,GAAGxB,cAAc,CAACC,QAAQ,EAAET,CAAC,CAAC;IAEvC,IAAGgC,KAAK,KAAK,CAAC,CAAC,EACX;MAAE,OAAOT,IAAI,CAACE,MAAM,CAAChB,QAAQ,CAACwB,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC,CAAC;IAAE;IACrD,OAAOT,IAAI;EACf,CAAC,EAAE,EAAE,CAAC;AACV;AAEA,SAASZ,cAAcA,CAACD,OAAO,EAAEwB,OAAO,EAAC;EACrC,IAAG,OAAOxB,OAAO,KAAK,QAAQ,EAAC;IAC3B,IAAG;MACC,OAAOZ,QAAQ,CAACC,aAAa,CAACW,OAAO,CAAC;IAC1C,CAAC,QAAMV,CAAC,EAAC;MACL,MAAMA,CAAC;IACX;EAEJ;EAEA,IAAG,CAACT,WAAW,CAACmB,OAAO,CAAC,IAAI,CAACwB,OAAO,EAAC;IACjC,MAAM,IAAIC,SAAS,CAAEzB,OAAO,GAAG,wBAAyB,CAAC;EAC7D;EACA,OAAOA,OAAO;AAClB;AAEAhC,OAAO,CAAC8B,cAAc,GAAGA,cAAc;AACvC9B,OAAO,CAACoC,UAAU,GAAGA,UAAU;AAC/BpC,OAAO,CAACqC,SAAS,GAAGA,SAAS;AAC7BrC,OAAO,CAACwC,kBAAkB,GAAGA,kBAAkB;AAC/CxC,OAAO,CAACmD,WAAW,GAAGA,WAAW;AACjCnD,OAAO,CAACoD,cAAc,GAAGA,cAAc;AACvCpD,OAAO,CAACiC,cAAc,GAAGA,cAAc;AACvCjC,OAAO,CAACkB,MAAM,GAAGA,MAAM;AACvBlB,OAAO,CAACuB,SAAS,GAAGA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}