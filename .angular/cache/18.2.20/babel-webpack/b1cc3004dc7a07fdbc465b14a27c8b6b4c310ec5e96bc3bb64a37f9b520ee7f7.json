{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@ngx-translate/core\";\nconst _c0 = a0 => ({\n  width: a0\n});\nexport class SetupGameProgressComponent {\n  constructor() {\n    this.progressFinished = new EventEmitter();\n    this.finished = false;\n    this.percent = 0;\n    this.total = 0;\n    this.completed = 0;\n  }\n  set setupGameItems(value) {\n    if (!value) return;\n    this._setupGameItems = value;\n    this.total = value.length;\n    this.updateProgress();\n  }\n  get setupGameItems() {\n    return this._setupGameItems;\n  }\n  updateProgress() {\n    this.calculateCompletedItems();\n    this.calculateTotalPercent();\n    this.checkFinished();\n  }\n  calculateCompletedItems() {\n    this.completed = this._setupGameItems ? this._setupGameItems.filter(item => item.complete).length : 0;\n    this.finished = this.completed === this.total;\n  }\n  calculateTotalPercent() {\n    let percent = 0;\n    if (!this.finished) {\n      if (this.total > 0) {\n        percent = this.completed / this.total * 100;\n      }\n    } else if (this.finished) {\n      percent = 100;\n    }\n    this.percent = percent;\n  }\n  checkFinished() {\n    if (this.finished) {\n      this.progressFinished.emit(this.finished);\n    }\n  }\n  static {\n    this.ɵfac = function SetupGameProgressComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SetupGameProgressComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SetupGameProgressComponent,\n      selectors: [[\"setup-game-progress\"]],\n      inputs: {\n        setupGameItems: \"setupGameItems\"\n      },\n      outputs: {\n        progressFinished: \"progressFinished\"\n      },\n      decls: 9,\n      vars: 8,\n      consts: [[1, \"content-group\"], [1, \"row\"], [1, \"col-md-4\", \"col-sm-6\"], [1, \"progress\", \"progress-micro\", \"mb-5\"], [1, \"progress-bar\", \"progress-bar-success\", 3, \"ngStyle\"], [1, \"pull-right\"]],\n      template: function SetupGameProgressComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelement(4, \"div\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"span\", 5);\n          i0.ɵɵtext(6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(7);\n          i0.ɵɵpipe(8, \"translate\");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(6, _c0, ctx.percent + \"%\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate2(\"\", ctx.completed, \"/\", ctx.total, \"\");\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(8, 4, \"ENTITY_SETUP.GAMES.MODALS.ready\"), \" \");\n        }\n      },\n      dependencies: [i1.NgStyle, i2.TranslatePipe],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "SetupGameProgressComponent", "constructor", "progressFinished", "finished", "percent", "total", "completed", "setupGameItems", "value", "_setupGameItems", "length", "updateProgress", "calculateCompletedItems", "calculateTotalPercent", "checkFinished", "filter", "item", "complete", "emit", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "SetupGameProgressComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction1", "_c0", "ɵɵtextInterpolate2", "ɵɵtextInterpolate1", "ɵɵpipeBind1"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-games/manage-games/setup-game-progress.component.ts", "/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-games/manage-games/setup-game-progress.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, Output } from '@angular/core';\nimport { SetupGameItem } from './setup-game.model';\n\n@Component({\n  selector: 'setup-game-progress',\n  templateUrl: './setup-game-progress.component.html',\n})\nexport class SetupGameProgressComponent {\n\n  @Output() public progressFinished: EventEmitter<boolean> = new EventEmitter();\n\n  public finished: boolean = false;\n  public percent: number = 0;\n  public total: number = 0;\n  public completed: number = 0;\n\n  private _setupGameItems: SetupGameItem[];\n  @Input()\n  set setupGameItems( value: SetupGameItem[] ) {\n    if (!value) return;\n\n    this._setupGameItems = value;\n\n    this.total = value.length;\n    this.updateProgress();\n  }\n\n  get setupGameItems(): SetupGameItem[] {\n    return this._setupGameItems;\n  }\n\n  updateProgress() {\n    this.calculateCompletedItems();\n    this.calculateTotalPercent();\n    this.checkFinished();\n  }\n\n  calculateCompletedItems() {\n    this.completed = this._setupGameItems ? this._setupGameItems.filter(item => item.complete).length : 0;\n    this.finished = this.completed === this.total;\n  }\n\n  calculateTotalPercent() {\n    let percent = 0;\n\n    if (!this.finished) {\n      if (this.total > 0) {\n        percent = (this.completed / this.total) * 100;\n      }\n    } else if (this.finished) {\n      percent = 100;\n    }\n\n    this.percent = percent;\n  }\n\n  checkFinished() {\n    if (this.finished) {\n      this.progressFinished.emit(this.finished);\n    }\n  }\n}\n", "<div class=\"content-group\">\n  <div class=\"row\">\n    <div class=\"col-md-4 col-sm-6\">\n      <div class=\"progress progress-micro mb-5\">\n        <div class=\"progress-bar progress-bar-success\" [ngStyle]=\"{width: percent+'%'}\"></div>\n      </div>\n      <span class=\"pull-right\">{{ completed }}/{{ total }}</span>\n      {{'ENTITY_SETUP.GAMES.MODALS.ready' | translate}}\n    </div>\n  </div>\n</div>\n"], "mappings": "AAAA,SAAoBA,YAAY,QAAuB,eAAe;;;;;;;AAOtE,OAAM,MAAOC,0BAA0B;EAJvCC,YAAA;IAMmB,KAAAC,gBAAgB,GAA0B,IAAIH,YAAY,EAAE;IAEtE,KAAAI,QAAQ,GAAY,KAAK;IACzB,KAAAC,OAAO,GAAW,CAAC;IACnB,KAAAC,KAAK,GAAW,CAAC;IACjB,KAAAC,SAAS,GAAW,CAAC;;EAG5B,IACIC,cAAcA,CAAEC,KAAsB;IACxC,IAAI,CAACA,KAAK,EAAE;IAEZ,IAAI,CAACC,eAAe,GAAGD,KAAK;IAE5B,IAAI,CAACH,KAAK,GAAGG,KAAK,CAACE,MAAM;IACzB,IAAI,CAACC,cAAc,EAAE;EACvB;EAEA,IAAIJ,cAAcA,CAAA;IAChB,OAAO,IAAI,CAACE,eAAe;EAC7B;EAEAE,cAAcA,CAAA;IACZ,IAAI,CAACC,uBAAuB,EAAE;IAC9B,IAAI,CAACC,qBAAqB,EAAE;IAC5B,IAAI,CAACC,aAAa,EAAE;EACtB;EAEAF,uBAAuBA,CAAA;IACrB,IAAI,CAACN,SAAS,GAAG,IAAI,CAACG,eAAe,GAAG,IAAI,CAACA,eAAe,CAACM,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,QAAQ,CAAC,CAACP,MAAM,GAAG,CAAC;IACrG,IAAI,CAACP,QAAQ,GAAG,IAAI,CAACG,SAAS,KAAK,IAAI,CAACD,KAAK;EAC/C;EAEAQ,qBAAqBA,CAAA;IACnB,IAAIT,OAAO,GAAG,CAAC;IAEf,IAAI,CAAC,IAAI,CAACD,QAAQ,EAAE;MAClB,IAAI,IAAI,CAACE,KAAK,GAAG,CAAC,EAAE;QAClBD,OAAO,GAAI,IAAI,CAACE,SAAS,GAAG,IAAI,CAACD,KAAK,GAAI,GAAG;MAC/C;IACF,CAAC,MAAM,IAAI,IAAI,CAACF,QAAQ,EAAE;MACxBC,OAAO,GAAG,GAAG;IACf;IAEA,IAAI,CAACA,OAAO,GAAGA,OAAO;EACxB;EAEAU,aAAaA,CAAA;IACX,IAAI,IAAI,CAACX,QAAQ,EAAE;MACjB,IAAI,CAACD,gBAAgB,CAACgB,IAAI,CAAC,IAAI,CAACf,QAAQ,CAAC;IAC3C;EACF;;;uCArDWH,0BAA0B;IAAA;EAAA;;;YAA1BA,0BAA0B;MAAAmB,SAAA;MAAAC,MAAA;QAAAb,cAAA;MAAA;MAAAc,OAAA;QAAAnB,gBAAA;MAAA;MAAAoB,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCJjCE,EAHN,CAAAC,cAAA,aAA2B,aACR,aACgB,aACa;UACxCD,EAAA,CAAAE,SAAA,aAAsF;UACxFF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,cAAyB;UAAAD,EAAA,CAAAI,MAAA,GAA2B;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAC3DH,EAAA,CAAAI,MAAA,GACF;;UAEJJ,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;UANiDH,EAAA,CAAAK,SAAA,GAAgC;UAAhCL,EAAA,CAAAM,UAAA,YAAAN,EAAA,CAAAO,eAAA,IAAAC,GAAA,EAAAT,GAAA,CAAAxB,OAAA,QAAgC;UAExDyB,EAAA,CAAAK,SAAA,GAA2B;UAA3BL,EAAA,CAAAS,kBAAA,KAAAV,GAAA,CAAAtB,SAAA,OAAAsB,GAAA,CAAAvB,KAAA,KAA2B;UACpDwB,EAAA,CAAAK,SAAA,EACF;UADEL,EAAA,CAAAU,kBAAA,MAAAV,EAAA,CAAAW,WAAA,+CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}