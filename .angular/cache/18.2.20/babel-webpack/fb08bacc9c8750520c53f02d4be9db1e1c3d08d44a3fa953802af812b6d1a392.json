{"ast": null, "code": "import { CdkStepper } from '@angular/cdk/stepper';\nimport { MatStepper } from '@angular/material/stepper';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/material/stepper\";\nfunction GamesSetupStepperComponent_ng_container_1_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 6);\n  }\n}\nfunction GamesSetupStepperComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"mat-step-header\", 4);\n    i0.ɵɵtemplate(2, GamesSetupStepperComponent_ng_container_1_div_2_Template, 1, 0, \"div\", 5);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const step_r1 = ctx.$implicit;\n    const i_r2 = ctx.index;\n    const isLast_r3 = ctx.last;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"mat-horizontal-stepper-header_small\", ctx_r3.small);\n    i0.ɵɵproperty(\"tabIndex\", ctx_r3._getFocusIndex() === i_r2 ? 0 : -1)(\"id\", ctx_r3._getStepLabelId(i_r2))(\"index\", i_r2)(\"state\", ctx_r3._getIndicatorType(i_r2, step_r1.state))(\"label\", step_r1.stepLabel || step_r1.label)(\"selected\", ctx_r3.selectedIndex === i_r2)(\"active\", step_r1.completed || ctx_r3.selectedIndex === i_r2 || !ctx_r3.linear)(\"optional\", step_r1.optional)(\"errorMessage\", step_r1.errorMessage)(\"iconOverrides\", ctx_r3._iconOverrides)(\"disableRipple\", true);\n    i0.ɵɵattribute(\"aria-posinset\", i_r2 + 1)(\"aria-setsize\", ctx_r3.steps.length)(\"aria-controls\", ctx_r3._getStepContentId(i_r2))(\"aria-selected\", ctx_r3.selectedIndex == i_r2)(\"aria-label\", step_r1.ariaLabel || null)(\"aria-labelledby\", !step_r1.ariaLabel && step_r1.ariaLabelledby ? step_r1.ariaLabelledby : null);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !isLast_r3);\n  }\n}\nfunction GamesSetupStepperComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵelementContainer(1, 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const step_r5 = ctx.$implicit;\n    const i_r6 = ctx.index;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"id\", ctx_r3._getStepContentId(i_r6));\n    i0.ɵɵattribute(\"tabindex\", ctx_r3.selectedIndex === i_r6 ? 0 : null)(\"aria-labelledby\", ctx_r3._getStepLabelId(i_r6))(\"aria-expanded\", ctx_r3.selectedIndex === i_r6);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", step_r5.content);\n  }\n}\nexport class GamesSetupStepperComponent extends MatStepper {\n  constructor() {\n    super(...arguments);\n    this.small = false;\n  }\n  static {\n    this.ɵfac = /*@__PURE__*/(() => {\n      let ɵGamesSetupStepperComponent_BaseFactory;\n      return function GamesSetupStepperComponent_Factory(__ngFactoryType__) {\n        return (ɵGamesSetupStepperComponent_BaseFactory || (ɵGamesSetupStepperComponent_BaseFactory = i0.ɵɵgetInheritedFactory(GamesSetupStepperComponent)))(__ngFactoryType__ || GamesSetupStepperComponent);\n      };\n    })();\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: GamesSetupStepperComponent,\n      selectors: [[\"games-setup-stepper\"]],\n      inputs: {\n        small: \"small\"\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MatStepper,\n        useExisting: GamesSetupStepperComponent\n      }, {\n        provide: CdkStepper,\n        useExisting: GamesSetupStepperComponent\n      }]), i0.ɵɵInheritDefinitionFeature],\n      decls: 4,\n      vars: 2,\n      consts: [[1, \"mat-horizontal-stepper-header-container\"], [4, \"ngFor\", \"ngForOf\"], [1, \"mat-horizontal-content-container\"], [\"class\", \"mat-horizontal-stepper-content\", \"role\", \"tabpanel\", 3, \"id\", 4, \"ngFor\", \"ngForOf\"], [1, \"mat-horizontal-stepper-header\", 3, \"tabIndex\", \"id\", \"index\", \"state\", \"label\", \"selected\", \"active\", \"optional\", \"errorMessage\", \"iconOverrides\", \"disableRipple\"], [\"class\", \"mat-stepper-horizontal-line\", 4, \"ngIf\"], [1, \"mat-stepper-horizontal-line\"], [\"role\", \"tabpanel\", 1, \"mat-horizontal-stepper-content\", 3, \"id\"], [3, \"ngTemplateOutlet\"]],\n      template: function GamesSetupStepperComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, GamesSetupStepperComponent_ng_container_1_Template, 3, 20, \"ng-container\", 1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(2, \"div\", 2);\n          i0.ɵɵtemplate(3, GamesSetupStepperComponent_div_3_Template, 2, 5, \"div\", 3);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.steps);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.steps);\n        }\n      },\n      dependencies: [i1.NgForOf, i1.NgIf, i1.NgTemplateOutlet, i2.MatStepHeader],\n      styles: [\".mat-stepper-vertical,\\n.mat-stepper-horizontal {\\n  display: block;\\n}\\n\\n.mat-horizontal-stepper-header-container {\\n  white-space: nowrap;\\n  display: flex;\\n  align-items: center;\\n}\\n.mat-stepper-label-position-bottom .mat-horizontal-stepper-header-container {\\n  align-items: flex-start;\\n}\\n\\n.mat-stepper-horizontal-line {\\n  border-top-width: 1px;\\n  border-top-style: solid;\\n  flex: auto;\\n  height: 0;\\n  margin: 0 -16px;\\n  min-width: 32px;\\n}\\n.mat-stepper-label-position-bottom .mat-stepper-horizontal-line {\\n  margin: 0;\\n  min-width: 0;\\n  position: relative;\\n  top: 36px;\\n}\\n\\n.mat-stepper-label-position-bottom .mat-horizontal-stepper-header:not(:first-child)::before, [dir=rtl] .mat-stepper-label-position-bottom .mat-horizontal-stepper-header:not(:last-child)::before, .mat-stepper-label-position-bottom .mat-horizontal-stepper-header:not(:last-child)::after, [dir=rtl] .mat-stepper-label-position-bottom .mat-horizontal-stepper-header:not(:first-child)::after {\\n  border-top-width: 1px;\\n  border-top-style: solid;\\n  content: \\\"\\\";\\n  display: inline-block;\\n  height: 0;\\n  position: absolute;\\n  top: 36px;\\n  width: calc(50% - 20px);\\n}\\n\\n.mat-horizontal-stepper-header {\\n  display: flex;\\n  height: 72px;\\n  overflow: hidden;\\n  align-items: center;\\n  padding: 0 24px;\\n}\\n.mat-horizontal-stepper-header .mat-step-icon {\\n  margin-right: 8px;\\n  flex: none;\\n}\\n[dir=rtl] .mat-horizontal-stepper-header .mat-step-icon {\\n  margin-right: 0;\\n  margin-left: 8px;\\n}\\n.mat-horizontal-stepper-header_small {\\n  height: 54px;\\n}\\n.mat-stepper-label-position-bottom .mat-horizontal-stepper-header {\\n  box-sizing: border-box;\\n  flex-direction: column;\\n  height: auto;\\n  padding: 24px;\\n}\\n.mat-stepper-label-position-bottom .mat-horizontal-stepper-header:not(:last-child)::after, [dir=rtl] .mat-stepper-label-position-bottom .mat-horizontal-stepper-header:not(:first-child)::after {\\n  right: 0;\\n}\\n.mat-stepper-label-position-bottom .mat-horizontal-stepper-header:not(:first-child)::before, [dir=rtl] .mat-stepper-label-position-bottom .mat-horizontal-stepper-header:not(:last-child)::before {\\n  left: 0;\\n}\\n[dir=rtl] .mat-stepper-label-position-bottom .mat-horizontal-stepper-header:last-child::before, [dir=rtl] .mat-stepper-label-position-bottom .mat-horizontal-stepper-header:first-child::after {\\n  display: none;\\n}\\n.mat-stepper-label-position-bottom .mat-horizontal-stepper-header .mat-step-icon {\\n  margin-right: 0;\\n  margin-left: 0;\\n}\\n.mat-stepper-label-position-bottom .mat-horizontal-stepper-header .mat-step-label {\\n  padding: 16px 0 0 0;\\n  text-align: center;\\n  width: 100%;\\n}\\n\\n.mat-vertical-stepper-header {\\n  display: flex;\\n  align-items: center;\\n  padding: 24px;\\n  height: 24px;\\n}\\n.mat-vertical-stepper-header .mat-step-icon {\\n  margin-right: 12px;\\n}\\n[dir=rtl] .mat-vertical-stepper-header .mat-step-icon {\\n  margin-right: 0;\\n  margin-left: 12px;\\n}\\n\\n.mat-horizontal-stepper-content {\\n  outline: 0;\\n}\\n.mat-horizontal-stepper-content[aria-expanded=false] {\\n  height: 0;\\n  overflow: hidden;\\n}\\n\\n.mat-horizontal-content-container {\\n  overflow: hidden;\\n  padding: 0;\\n}\\n\\n.mat-vertical-content-container {\\n  margin-left: 36px;\\n  border: 0;\\n  position: relative;\\n}\\n[dir=rtl] .mat-vertical-content-container {\\n  margin-left: 0;\\n  margin-right: 36px;\\n}\\n\\n.mat-stepper-vertical-line::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: -16px;\\n  bottom: -16px;\\n  left: 0;\\n  border-left-width: 1px;\\n  border-left-style: solid;\\n}\\n[dir=rtl] .mat-stepper-vertical-line::before {\\n  left: auto;\\n  right: 0;\\n}\\n\\n.mat-vertical-stepper-content {\\n  overflow: hidden;\\n  outline: 0;\\n}\\n\\n.mat-vertical-content {\\n  padding: 0 24px 24px 24px;\\n}\\n\\n.mat-step:last-child .mat-vertical-content-container {\\n  border: none;\\n}\\n\\nmat-step-header:hover {\\n  background: none !important;\\n  cursor: default !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}", "map": {"version": 3, "names": ["CdkStepper", "Mat<PERSON><PERSON><PERSON>", "i0", "ɵɵelement", "ɵɵelementContainerStart", "ɵɵtemplate", "GamesSetupStepperComponent_ng_container_1_div_2_Template", "ɵɵadvance", "ɵɵclassProp", "ctx_r3", "small", "ɵɵproperty", "_getFocusIndex", "i_r2", "_getStepLabelId", "_getIndicatorType", "step_r1", "state", "<PERSON><PERSON><PERSON><PERSON>", "label", "selectedIndex", "completed", "linear", "optional", "errorMessage", "_iconOverrides", "isLast_r3", "ɵɵelementStart", "ɵɵelementContainer", "ɵɵelementEnd", "_getStepContentId", "i_r6", "step_r5", "content", "GamesSetupStepperComponent", "constructor", "__ngFactoryType__", "selectors", "inputs", "features", "ɵɵProvidersFeature", "provide", "useExisting", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "GamesSetupStepperComponent_Template", "rf", "ctx", "GamesSetupStepperComponent_ng_container_1_Template", "GamesSetupStepperComponent_div_3_Template", "steps"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-games/games-setup-stepper/games-setup-stepper.component.ts", "/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-games/games-setup-stepper/games-setup-stepper.component.html"], "sourcesContent": ["import { CdkStepper } from '@angular/cdk/stepper';\nimport { ChangeDetectionStrategy, Component, Input, ViewEncapsulation } from '@angular/core';\nimport { MatStepper } from '@angular/material/stepper';\n\n@Component({\n  selector: 'games-setup-stepper',\n  templateUrl: 'games-setup-stepper.component.html',\n  styleUrls: [\n    './games-setup-stepper.component.scss',\n  ],\n  providers: [\n    {provide: MatStepper, useExisting: GamesSetupStepperComponent},\n    {provide: CdkStepper, useExisting: GamesSetupStepperComponent}\n  ],\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\n\nexport class GamesSetupStepperComponent extends MatStepper {\n  @Input() small = false;\n\n}\n", "<div class=\"mat-horizontal-stepper-header-container\">\n  <ng-container *ngFor=\"let step of steps; let i = index; let isLast = last\">\n    <mat-step-header class=\"mat-horizontal-stepper-header\"\n                     [class.mat-horizontal-stepper-header_small]=\"small\"\n                     [tabIndex]=\"_getFocusIndex() === i ? 0 : -1\"\n                     [id]=\"_getStepLabelId(i)\"\n                     [attr.aria-posinset]=\"i + 1\"\n                     [attr.aria-setsize]=\"steps.length\"\n                     [attr.aria-controls]=\"_getStepContentId(i)\"\n                     [attr.aria-selected]=\"selectedIndex == i\"\n                     [attr.aria-label]=\"step.ariaLabel || null\"\n                     [attr.aria-labelledby]=\"(!step.ariaLabel && step.ariaLabelledby) ? step.ariaLabelledby : null\"\n                     [index]=\"i\"\n                     [state]=\"_getIndicatorType(i, step.state)\"\n                     [label]=\"step.stepLabel || step.label\"\n                     [selected]=\"selectedIndex === i\"\n                     [active]=\"step.completed || selectedIndex === i || !linear\"\n                     [optional]=\"step.optional\"\n                     [errorMessage]=\"step.errorMessage\"\n                     [iconOverrides]=\"_iconOverrides\"\n                     [disableRipple]=\"true\">\n    </mat-step-header>\n    <div *ngIf=\"!isLast\" class=\"mat-stepper-horizontal-line\"></div>\n  </ng-container>\n</div>\n\n<div class=\"mat-horizontal-content-container\">\n  <div *ngFor=\"let step of steps; let i = index\"\n       [attr.tabindex]=\"selectedIndex === i ? 0 : null\"\n       class=\"mat-horizontal-stepper-content\" role=\"tabpanel\"\n       [id]=\"_getStepContentId(i)\"\n       [attr.aria-labelledby]=\"_getStepLabelId(i)\"\n       [attr.aria-expanded]=\"selectedIndex === i\">\n    <ng-container [ngTemplateOutlet]=\"step.content\"></ng-container>\n  </div>\n</div>\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,sBAAsB;AAEjD,SAASC,UAAU,QAAQ,2BAA2B;;;;;;ICoBlDC,EAAA,CAAAC,SAAA,aAA+D;;;;;IArBjED,EAAA,CAAAE,uBAAA,GAA2E;IACzEF,EAAA,CAAAC,SAAA,yBAmBkB;IAClBD,EAAA,CAAAG,UAAA,IAAAC,wDAAA,iBAAyD;;;;;;;;IAnBxCJ,EAAA,CAAAK,SAAA,EAAmD;IAAnDL,EAAA,CAAAM,WAAA,wCAAAC,MAAA,CAAAC,KAAA,CAAmD;IAiBnDR,EAhBA,CAAAS,UAAA,aAAAF,MAAA,CAAAG,cAAA,OAAAC,IAAA,UAA4C,OAAAJ,MAAA,CAAAK,eAAA,CAAAD,IAAA,EACnB,UAAAA,IAAA,CAOd,UAAAJ,MAAA,CAAAM,iBAAA,CAAAF,IAAA,EAAAG,OAAA,CAAAC,KAAA,EAC+B,UAAAD,OAAA,CAAAE,SAAA,IAAAF,OAAA,CAAAG,KAAA,CACJ,aAAAV,MAAA,CAAAW,aAAA,KAAAP,IAAA,CACN,WAAAG,OAAA,CAAAK,SAAA,IAAAZ,MAAA,CAAAW,aAAA,KAAAP,IAAA,KAAAJ,MAAA,CAAAa,MAAA,CAC2B,aAAAN,OAAA,CAAAO,QAAA,CACjC,iBAAAP,OAAA,CAAAQ,YAAA,CACQ,kBAAAf,MAAA,CAAAgB,cAAA,CACF,uBACV;;IAEjCvB,EAAA,CAAAK,SAAA,EAAa;IAAbL,EAAA,CAAAS,UAAA,UAAAe,SAAA,CAAa;;;;;IAKrBxB,EAAA,CAAAyB,cAAA,aAKgD;IAC9CzB,EAAA,CAAA0B,kBAAA,MAA+D;IACjE1B,EAAA,CAAA2B,YAAA,EAAM;;;;;;IAJD3B,EAAA,CAAAS,UAAA,OAAAF,MAAA,CAAAqB,iBAAA,CAAAC,IAAA,EAA2B;;IAGhB7B,EAAA,CAAAK,SAAA,EAAiC;IAAjCL,EAAA,CAAAS,UAAA,qBAAAqB,OAAA,CAAAC,OAAA,CAAiC;;;ADfnD,OAAM,MAAOC,0BAA2B,SAAQjC,UAAU;EAd1DkC,YAAA;;IAeW,KAAAzB,KAAK,GAAG,KAAK;;;;;;+HADXwB,0BAA0B,IAAAE,iBAAA,IAA1BF,0BAA0B;MAAA;IAAA;EAAA;;;YAA1BA,0BAA0B;MAAAG,SAAA;MAAAC,MAAA;QAAA5B,KAAA;MAAA;MAAA6B,QAAA,GAAArC,EAAA,CAAAsC,kBAAA,CAR1B,CACT;QAACC,OAAO,EAAExC,UAAU;QAAEyC,WAAW,EAAER;MAA0B,CAAC,EAC9D;QAACO,OAAO,EAAEzC,UAAU;QAAE0C,WAAW,EAAER;MAA0B,CAAC,CAC/D,GAAAhC,EAAA,CAAAyC,0BAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCbH/C,EAAA,CAAAyB,cAAA,aAAqD;UACnDzB,EAAA,CAAAG,UAAA,IAAA8C,kDAAA,2BAA2E;UAuB7EjD,EAAA,CAAA2B,YAAA,EAAM;UAEN3B,EAAA,CAAAyB,cAAA,aAA8C;UAC5CzB,EAAA,CAAAG,UAAA,IAAA+C,yCAAA,iBAKgD;UAGlDlD,EAAA,CAAA2B,YAAA,EAAM;;;UAlC2B3B,EAAA,CAAAK,SAAA,EAAU;UAAVL,EAAA,CAAAS,UAAA,YAAAuC,GAAA,CAAAG,KAAA,CAAU;UA0BnBnD,EAAA,CAAAK,SAAA,GAAU;UAAVL,EAAA,CAAAS,UAAA,YAAAuC,GAAA,CAAAG,KAAA,CAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}