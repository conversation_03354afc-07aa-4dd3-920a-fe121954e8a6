{"ast": null, "code": "import { InjectionToken } from '@angular/core';\nconst WINDOW = new InjectionToken('WindowToken', typeof window !== 'undefined' && window.document ? {\n  providedIn: 'root',\n  factory: () => window\n} : {\n  providedIn: 'root',\n  factory: () => undefined\n});\n\n/*\n * Public API Surface of ngx-window-token\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { WINDOW };", "map": {"version": 3, "names": ["InjectionToken", "WINDOW", "window", "document", "providedIn", "factory", "undefined"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/node_modules/ngx-clipboard/node_modules/ngx-window-token/fesm2020/ngx-window-token.mjs"], "sourcesContent": ["import { InjectionToken } from '@angular/core';\n\nconst WINDOW = new InjectionToken('WindowToken', typeof window !== 'undefined' && window.document\n    ? { providedIn: 'root', factory: () => window }\n    : { providedIn: 'root', factory: () => undefined });\n\n/*\n * Public API Surface of ngx-window-token\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { WINDOW };\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,eAAe;AAE9C,MAAMC,MAAM,GAAG,IAAID,cAAc,CAAC,aAAa,EAAE,OAAOE,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,QAAQ,GAC3F;EAAEC,UAAU,EAAE,MAAM;EAAEC,OAAO,EAAEA,CAAA,KAAMH;AAAO,CAAC,GAC7C;EAAEE,UAAU,EAAE,MAAM;EAAEC,OAAO,EAAEA,CAAA,KAAMC;AAAU,CAAC,CAAC;;AAEvD;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASL,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}