{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FlexLayoutModule } from '@angular/flex-layout';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiGridModule, SwuiNotificationsModule, SwuiPagePanelModule, SwuiSchemaTopFilterModule } from '@skywind-group/lib-swui';\nimport { ReportFinancialComponent } from './financial.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@skywind-group/lib-swui\";\nexport class ReportFinancialModule {\n  static {\n    this.ɵfac = function ReportFinancialModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ReportFinancialModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ReportFinancialModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, SwuiPagePanelModule, SwuiGridModule, SwuiNotificationsModule.forRoot(), TranslateModule, FlexLayoutModule, MatTooltipModule, SwuiSchemaTopFilterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ReportFinancialModule, {\n    declarations: [ReportFinancialComponent],\n    imports: [CommonModule, SwuiPagePanelModule, SwuiGridModule, i1.SwuiNotificationsModule, TranslateModule, FlexLayoutModule, MatTooltipModule, SwuiSchemaTopFilterModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FlexLayoutModule", "MatTooltipModule", "TranslateModule", "SwuiGridModule", "SwuiNotificationsModule", "SwuiPagePanelModule", "SwuiSchemaTopFilterModule", "ReportFinancialComponent", "ReportFinancialModule", "forRoot", "declarations", "imports", "i1"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/reports/components/financial/report-financial.module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { FlexLayoutModule } from '@angular/flex-layout';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiGridModule, SwuiNotificationsModule, SwuiPagePanelModule, SwuiSchemaTopFilterModule } from '@skywind-group/lib-swui';\nimport { ReportFinancialComponent } from './financial.component';\n\n\n@NgModule({\n  imports: [\n    CommonModule,\n    SwuiPagePanelModule,\n    SwuiGridModule,\n    SwuiNotificationsModule.forRoot(),\n    TranslateModule,\n    FlexLayoutModule,\n    MatTooltipModule,\n    SwuiSchemaTopFilterModule\n  ],\n  declarations: [\n    ReportFinancialComponent\n  ],\n  providers: []\n})\nexport class ReportFinancialModule {\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,cAAc,EAAEC,uBAAuB,EAAEC,mBAAmB,EAAEC,yBAAyB,QAAQ,yBAAyB;AACjI,SAASC,wBAAwB,QAAQ,uBAAuB;;;AAmBhE,OAAM,MAAOC,qBAAqB;;;uCAArBA,qBAAqB;IAAA;EAAA;;;YAArBA;IAAqB;EAAA;;;gBAd9BT,YAAY,EACZM,mBAAmB,EACnBF,cAAc,EACdC,uBAAuB,CAACK,OAAO,EAAE,EACjCP,eAAe,EACfF,gBAAgB,EAChBC,gBAAgB,EAChBK,yBAAyB;IAAA;EAAA;;;2EAOhBE,qBAAqB;IAAAE,YAAA,GAJ9BH,wBAAwB;IAAAI,OAAA,GAVxBZ,YAAY,EACZM,mBAAmB,EACnBF,cAAc,EAAAS,EAAA,CAAAR,uBAAA,EAEdF,eAAe,EACfF,gBAAgB,EAChBC,gBAAgB,EAChBK,yBAAyB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}