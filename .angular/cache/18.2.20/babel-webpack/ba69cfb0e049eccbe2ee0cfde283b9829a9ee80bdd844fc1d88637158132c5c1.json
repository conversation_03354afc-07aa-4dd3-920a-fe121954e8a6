{"ast": null, "code": "import { FormControl } from '@angular/forms';\nimport { first, switchMap } from 'rxjs/operators';\nimport { DOMAIN_TYPES } from 'src/app/common/models/domain.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@skywind-group/lib-swui\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"src/app/common/services/entity-settings.service\";\nimport * as i5 from \"src/app/pages/domains-management/domains-management.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/icon\";\nimport * as i9 from \"@angular/material/tooltip\";\nimport * as i10 from \"@angular/material/form-field\";\nimport * as i11 from \"@angular/material/chips\";\nimport * as i12 from \"@angular/material/select\";\nimport * as i13 from \"@angular/material/core\";\nfunction EntityAllowedChildDomainsComponent_mat_option_11_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 11);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const domain_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" (\", domain_r1.type, \")\");\n  }\n}\nfunction EntityAllowedChildDomainsComponent_mat_option_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 9);\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, EntityAllowedChildDomainsComponent_mat_option_11_span_2_Template, 2, 1, \"span\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const domain_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", domain_r1.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", domain_r1.domain, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", domain_r1.type);\n  }\n}\nfunction EntityAllowedChildDomainsComponent_div_22_mat_chip_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-chip\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const domainId_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getDomainDisplayName(domainId_r2), \" \");\n  }\n}\nfunction EntityAllowedChildDomainsComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"div\", 13);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 14)(5, \"mat-chip-list\");\n    i0.ɵɵtemplate(6, EntityAllowedChildDomainsComponent_div_22_mat_chip_6_Template, 2, 1, \"mat-chip\", 15);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(3, 2, \"ENTITY_SETUP.DOMAINS.selectedDomains\"), \":\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.selectedDomainIds);\n  }\n}\nexport class EntityAllowedChildDomainsComponent {\n  constructor(fb, notifications, translate, entitySettingsService, domainsManagementService) {\n    this.notifications = notifications;\n    this.translate = translate;\n    this.entitySettingsService = entitySettingsService;\n    this.domainsManagementService = domainsManagementService;\n    this.domainsControl = new FormControl([]);\n    this.availableDomains = [];\n    this.selectedDomainIds = [];\n    this.form = fb.group({\n      domains: this.domainsControl\n    });\n  }\n  ngOnInit() {\n    this.domainsManagementService.getList(DOMAIN_TYPES.static).pipe(first()).subscribe(domains => {\n      this.availableDomains = domains;\n    });\n    this.entitySettingsService.getSettings(this.entity.path).pipe(first()).subscribe(settings => {\n      this.currentSettings = settings;\n      if (settings?.allowedStaticDomainsForChildId) {\n        this.selectedDomainIds = [...settings.allowedStaticDomainsForChildId];\n        this.domainsControl.setValue(this.selectedDomainIds);\n      }\n    });\n  }\n  onDomainSelectionChange(selectedIds) {\n    this.selectedDomainIds = selectedIds;\n    this.domainsControl.setValue(selectedIds);\n  }\n  saveDomains() {\n    const domainIds = this.domainsControl.value || [];\n    const settingsUpdate = {\n      allowedStaticDomainsForChildId: domainIds\n    };\n    this.entitySettingsService.patchSettings(settingsUpdate, this.entity.path).pipe(first(), switchMap(() => this.translate.get('ENTITY_SETUP.DOMAINS.allowedChildDomainsSaved'))).subscribe(msg => {\n      this.notifications.success(msg);\n      if (this.currentSettings) {\n        this.currentSettings.allowedStaticDomainsForChildId = domainIds;\n      }\n    });\n  }\n  resetDomains() {\n    const settingsUpdate = {\n      allowedStaticDomainsForChildId: []\n    };\n    this.entitySettingsService.patchSettings(settingsUpdate, this.entity.path).pipe(first(), switchMap(() => this.translate.get('ENTITY_SETUP.DOMAINS.allowedChildDomainsReset'))).subscribe(msg => {\n      this.domainsControl.setValue([]);\n      this.selectedDomainIds = [];\n      this.notifications.success(msg);\n      if (this.currentSettings) {\n        this.currentSettings.allowedStaticDomainsForChildId = [];\n      }\n    });\n  }\n  getDomainDisplayName(domainId) {\n    const domain = this.availableDomains.find(d => d.id === domainId);\n    return domain ? domain.domain : domainId;\n  }\n  static {\n    this.ɵfac = function EntityAllowedChildDomainsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || EntityAllowedChildDomainsComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.SwuiNotificationsService), i0.ɵɵdirectiveInject(i3.TranslateService), i0.ɵɵdirectiveInject(i4.EntitySettingsService), i0.ɵɵdirectiveInject(i5.DomainsManagementService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: EntityAllowedChildDomainsComponent,\n      selectors: [[\"entity-allowed-child-domains\"]],\n      inputs: {\n        entity: \"entity\"\n      },\n      decls: 23,\n      vars: 15,\n      consts: [[1, \"domain\"], [1, \"domain--info\"], [1, \"domain--controls\"], [\"appearance\", \"outline\", 2, \"min-width\", \"320px\", \"width\", \"100%\", \"max-width\", \"560px\", \"display\", \"block\"], [\"multiple\", \"\", 3, \"selectionChange\", \"formControl\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"align\", \"start\"], [\"mat-icon-button\", \"\", 3, \"click\", \"matTooltip\"], [\"class\", \"selected-domains-preview\", 4, \"ngIf\"], [3, \"value\"], [\"class\", \"domain-type\", 4, \"ngIf\"], [1, \"domain-type\"], [1, \"selected-domains-preview\"], [1, \"selected-domains-title\"], [1, \"selected-domains-list\"], [4, \"ngFor\", \"ngForOf\"]],\n      template: function EntityAllowedChildDomainsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"span\");\n          i0.ɵɵtext(3);\n          i0.ɵɵpipe(4, \"translate\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(5, \"div\", 2)(6, \"mat-form-field\", 3)(7, \"mat-label\");\n          i0.ɵɵtext(8);\n          i0.ɵɵpipe(9, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"mat-select\", 4);\n          i0.ɵɵlistener(\"selectionChange\", function EntityAllowedChildDomainsComponent_Template_mat_select_selectionChange_10_listener($event) {\n            return ctx.onDomainSelectionChange($event.value);\n          });\n          i0.ɵɵtemplate(11, EntityAllowedChildDomainsComponent_mat_option_11_Template, 3, 3, \"mat-option\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"mat-hint\", 6);\n          i0.ɵɵtext(13, \"Select which static domains child entities are allowed to use\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"button\", 7);\n          i0.ɵɵpipe(15, \"translate\");\n          i0.ɵɵlistener(\"click\", function EntityAllowedChildDomainsComponent_Template_button_click_14_listener() {\n            return ctx.saveDomains();\n          });\n          i0.ɵɵelementStart(16, \"mat-icon\");\n          i0.ɵɵtext(17, \"save\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"button\", 7);\n          i0.ɵɵpipe(19, \"translate\");\n          i0.ɵɵlistener(\"click\", function EntityAllowedChildDomainsComponent_Template_button_click_18_listener() {\n            return ctx.resetDomains();\n          });\n          i0.ɵɵelementStart(20, \"mat-icon\");\n          i0.ɵɵtext(21, \"undo\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(22, EntityAllowedChildDomainsComponent_div_22_Template, 7, 4, \"div\", 8);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(4, 7, \"ENTITY_SETUP.DOMAINS.allowedChildDomains\"), \":\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(9, 9, \"ENTITY_SETUP.DOMAINS.selectAllowedDomains\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formControl\", ctx.domainsControl);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.availableDomains);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"matTooltip\", i0.ɵɵpipeBind1(15, 11, \"ENTITY_SETUP.DOMAINS.saveAllowedChildDomains\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"matTooltip\", i0.ɵɵpipeBind1(19, 13, \"ENTITY_SETUP.DOMAINS.resetAllowedChildDomains\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedDomainIds.length > 0);\n        }\n      },\n      dependencies: [i6.NgForOf, i6.NgIf, i1.NgControlStatus, i1.FormControlDirective, i7.MatIconButton, i8.MatIcon, i9.MatTooltip, i10.MatFormField, i10.MatLabel, i10.MatHint, i11.MatChip, i12.MatSelect, i13.MatOption, i3.TranslatePipe],\n      styles: [\".domain[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  margin-bottom: 16px;\\n}\\n.domain--info[_ngcontent-%COMP%] {\\n  min-width: 200px;\\n  padding-top: 16px;\\n  font-weight: 500;\\n}\\n.domain--controls[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 8px;\\n}\\n\\n.selected-domains-preview[_ngcontent-%COMP%] {\\n  margin-top: 16px;\\n  padding: 16px;\\n  background-color: #f5f5f5;\\n  border-radius: 4px;\\n}\\n.selected-domains-preview[_ngcontent-%COMP%]   .selected-domains-title[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  margin-bottom: 8px;\\n}\\n.selected-domains-preview[_ngcontent-%COMP%]   .selected-domains-list[_ngcontent-%COMP%]   mat-chip-list[_ngcontent-%COMP%]   mat-chip[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  margin-bottom: 4px;\\n}\\n\\n.domain-type[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 0.9em;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvYnVzaW5lc3MtbWFuYWdlbWVudC9jb21wb25lbnRzL2VudGl0aWVzL3RhYi1kb21haW5zL21hbmFnZS1kb21haW5zL2FsbG93ZWQtY2hpbGQtZG9tYWlucy9lbnRpdHktYWxsb3dlZC1jaGlsZC1kb21haW5zLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsYUFBQTtFQUNBLHVCQUFBO0VBQ0EsbUJBQUE7QUFDRjtBQUNFO0VBQ0UsZ0JBQUE7RUFDQSxpQkFBQTtFQUNBLGdCQUFBO0FBQ0o7QUFFRTtFQUNFLE9BQUE7RUFDQSxhQUFBO0VBQ0EsdUJBQUE7RUFDQSxRQUFBO0FBQUo7O0FBSUE7RUFDRSxnQkFBQTtFQUNBLGFBQUE7RUFDQSx5QkFBQTtFQUNBLGtCQUFBO0FBREY7QUFHRTtFQUNFLGdCQUFBO0VBQ0Esa0JBQUE7QUFESjtBQU1NO0VBQ0UsaUJBQUE7RUFDQSxrQkFBQTtBQUpSOztBQVVBO0VBQ0UsV0FBQTtFQUNBLGdCQUFBO0FBUEYiLCJzb3VyY2VzQ29udGVudCI6WyIuZG9tYWluIHtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGZsZXgtc3RhcnQ7XG4gIG1hcmdpbi1ib3R0b206IDE2cHg7XG4gIFxuICAmLS1pbmZvIHtcbiAgICBtaW4td2lkdGg6IDIwMHB4O1xuICAgIHBhZGRpbmctdG9wOiAxNnB4O1xuICAgIGZvbnQtd2VpZ2h0OiA1MDA7XG4gIH1cbiAgXG4gICYtLWNvbnRyb2xzIHtcbiAgICBmbGV4OiAxO1xuICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgYWxpZ24taXRlbXM6IGZsZXgtc3RhcnQ7XG4gICAgZ2FwOiA4cHg7XG4gIH1cbn1cblxuLnNlbGVjdGVkLWRvbWFpbnMtcHJldmlldyB7XG4gIG1hcmdpbi10b3A6IDE2cHg7XG4gIHBhZGRpbmc6IDE2cHg7XG4gIGJhY2tncm91bmQtY29sb3I6ICNmNWY1ZjU7XG4gIGJvcmRlci1yYWRpdXM6IDRweDtcbiAgXG4gIC5zZWxlY3RlZC1kb21haW5zLXRpdGxlIHtcbiAgICBmb250LXdlaWdodDogNTAwO1xuICAgIG1hcmdpbi1ib3R0b206IDhweDtcbiAgfVxuICBcbiAgLnNlbGVjdGVkLWRvbWFpbnMtbGlzdCB7XG4gICAgbWF0LWNoaXAtbGlzdCB7XG4gICAgICBtYXQtY2hpcCB7XG4gICAgICAgIG1hcmdpbi1yaWdodDogOHB4O1xuICAgICAgICBtYXJnaW4tYm90dG9tOiA0cHg7XG4gICAgICB9XG4gICAgfVxuICB9XG59XG5cbi5kb21haW4tdHlwZSB7XG4gIGNvbG9yOiAjNjY2O1xuICBmb250LXNpemU6IDAuOWVtO1xufVxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["FormControl", "first", "switchMap", "DOMAIN_TYPES", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "domain_r1", "type", "ɵɵtemplate", "EntityAllowedChildDomainsComponent_mat_option_11_span_2_Template", "ɵɵproperty", "id", "domain", "ctx_r2", "getDomainDisplayName", "domainId_r2", "EntityAllowedChildDomainsComponent_div_22_mat_chip_6_Template", "ɵɵpipeBind1", "selectedDomainIds", "EntityAllowedChildDomainsComponent", "constructor", "fb", "notifications", "translate", "entitySettingsService", "domainsManagementService", "domainsControl", "availableDomains", "form", "group", "domains", "ngOnInit", "getList", "static", "pipe", "subscribe", "getSettings", "entity", "path", "settings", "currentSettings", "allowedStaticDomainsForChildId", "setValue", "onDomainSelectionChange", "selectedIds", "saveDomains", "domainIds", "value", "settingsUpdate", "patchSettings", "get", "msg", "success", "resetDomains", "domainId", "find", "d", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "SwuiNotificationsService", "i3", "TranslateService", "i4", "EntitySettingsService", "i5", "DomainsManagementService", "selectors", "inputs", "decls", "vars", "consts", "template", "EntityAllowedChildDomainsComponent_Template", "rf", "ctx", "ɵɵlistener", "EntityAllowedChildDomainsComponent_Template_mat_select_selectionChange_10_listener", "$event", "EntityAllowedChildDomainsComponent_mat_option_11_Template", "EntityAllowedChildDomainsComponent_Template_button_click_14_listener", "EntityAllowedChildDomainsComponent_Template_button_click_18_listener", "EntityAllowedChildDomainsComponent_div_22_Template", "ɵɵtextInterpolate", "length"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-domains/manage-domains/allowed-child-domains/entity-allowed-child-domains.component.ts", "/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-domains/manage-domains/allowed-child-domains/entity-allowed-child-domains.component.html"], "sourcesContent": ["import { Component, Input, OnInit } from '@angular/core';\nimport { FormBuilder, FormControl, FormGroup } from '@angular/forms';\nimport { first, switchMap } from 'rxjs/operators';\nimport { SwuiNotificationsService } from '@skywind-group/lib-swui';\nimport { TranslateService } from '@ngx-translate/core';\nimport { Entity } from 'src/app/common/typings';\nimport { EntitySettingsService } from 'src/app/common/services/entity-settings.service';\nimport { DomainsManagementService } from 'src/app/pages/domains-management/domains-management.service';\nimport { Domain, DOMAIN_TYPES } from 'src/app/common/models/domain.model';\nimport { EntitySettingsModel } from 'src/app/common/models/entity-settings.model';\n\n@Component({\n  selector: 'entity-allowed-child-domains',\n  templateUrl: './entity-allowed-child-domains.component.html',\n  styleUrls: ['./entity-allowed-child-domains.component.scss']\n})\nexport class EntityAllowedChildDomainsComponent implements OnInit {\n  @Input() entity: Entity;\n\n  readonly form: FormGroup;\n  readonly domainsControl = new FormControl([]);\n  availableDomains: Domain[] = [];\n  selectedDomainIds: string[] = [];\n  currentSettings: EntitySettingsModel;\n\n  constructor(\n    fb: FormBuilder,\n    private readonly notifications: SwuiNotificationsService,\n    private readonly translate: TranslateService,\n    private readonly entitySettingsService: EntitySettingsService<EntitySettingsModel>,\n    private readonly domainsManagementService: DomainsManagementService,\n  ) {\n    this.form = fb.group({\n      domains: this.domainsControl,\n    });\n  }\n\n  ngOnInit() {\n    this.domainsManagementService.getList(DOMAIN_TYPES.static)\n      .pipe(first())\n      .subscribe((domains: Domain[]) => {\n        this.availableDomains = domains;\n      });\n\n    this.entitySettingsService.getSettings(this.entity.path)\n      .pipe(first())\n      .subscribe((settings: EntitySettingsModel) => {\n        this.currentSettings = settings;\n        if (settings?.allowedStaticDomainsForChildId) {\n          this.selectedDomainIds = [...settings.allowedStaticDomainsForChildId];\n          this.domainsControl.setValue(this.selectedDomainIds);\n        }\n      });\n  }\n\n  onDomainSelectionChange(selectedIds: string[]) {\n    this.selectedDomainIds = selectedIds;\n    this.domainsControl.setValue(selectedIds);\n  }\n\n  saveDomains() {\n    const domainIds = this.domainsControl.value || [];\n    const settingsUpdate = { allowedStaticDomainsForChildId: domainIds };\n\n    this.entitySettingsService.patchSettings(settingsUpdate, this.entity.path)\n      .pipe(\n        first(),\n        switchMap(() => this.translate.get('ENTITY_SETUP.DOMAINS.allowedChildDomainsSaved'))\n      )\n      .subscribe(msg => {\n        this.notifications.success(msg);\n        if (this.currentSettings) {\n          this.currentSettings.allowedStaticDomainsForChildId = domainIds;\n        }\n      });\n  }\n\n  resetDomains() {\n    const settingsUpdate = { allowedStaticDomainsForChildId: [] };\n    this.entitySettingsService.patchSettings(settingsUpdate, this.entity.path)\n      .pipe(\n        first(),\n        switchMap(() => this.translate.get('ENTITY_SETUP.DOMAINS.allowedChildDomainsReset'))\n      )\n      .subscribe((msg: string) => {\n        this.domainsControl.setValue([]);\n        this.selectedDomainIds = [];\n        this.notifications.success(msg);\n        if (this.currentSettings) {\n          this.currentSettings.allowedStaticDomainsForChildId = [];\n        }\n      });\n  }\n\n  getDomainDisplayName(domainId: string): string {\n    const domain = this.availableDomains.find(d => d.id === domainId);\n    return domain ? domain.domain : domainId;\n  }\n}\n", "<div class=\"domain\">\n  <div class=\"domain--info\">\n    <span>{{ 'ENTITY_SETUP.DOMAINS.allowedChildDomains' | translate }}:</span>\n  </div>\n  <div class=\"domain--controls\">\n    <mat-form-field appearance=\"outline\" style=\"min-width: 320px; width: 100%; max-width: 560px; display: block;\">\n      <mat-label>{{ 'ENTITY_SETUP.DOMAINS.selectAllowedDomains' | translate }}</mat-label>\n      <mat-select\n        [formControl]=\"domainsControl\"\n        multiple\n        (selectionChange)=\"onDomainSelectionChange($event.value)\">\n        <mat-option *ngFor=\"let domain of availableDomains\" [value]=\"domain.id\">\n          {{ domain.domain }}\n          <span *ngIf=\"domain.type\" class=\"domain-type\"> ({{ domain.type }})</span>\n        </mat-option>\n      </mat-select>\n      <mat-hint align=\"start\">Select which static domains child entities are allowed to use</mat-hint>\n    </mat-form-field>\n\n    <button mat-icon-button (click)=\"saveDomains()\" [matTooltip]=\"'ENTITY_SETUP.DOMAINS.saveAllowedChildDomains' | translate\">\n      <mat-icon>save</mat-icon>\n    </button>\n    <button mat-icon-button (click)=\"resetDomains()\" [matTooltip]=\"'ENTITY_SETUP.DOMAINS.resetAllowedChildDomains' | translate\">\n      <mat-icon>undo</mat-icon>\n    </button>\n  </div>\n</div>\n\n<div *ngIf=\"selectedDomainIds.length > 0\" class=\"selected-domains-preview\">\n  <div class=\"selected-domains-title\">{{ 'ENTITY_SETUP.DOMAINS.selectedDomains' | translate }}:</div>\n  <div class=\"selected-domains-list\">\n    <mat-chip-list>\n      <mat-chip *ngFor=\"let domainId of selectedDomainIds\">\n        {{ getDomainDisplayName(domainId) }}\n      </mat-chip>\n    </mat-chip-list>\n  </div>\n</div>\n"], "mappings": "AACA,SAAsBA,WAAW,QAAmB,gBAAgB;AACpE,SAASC,KAAK,EAAEC,SAAS,QAAQ,gBAAgB;AAMjD,SAAiBC,YAAY,QAAQ,oCAAoC;;;;;;;;;;;;;;;;;ICK/DC,EAAA,CAAAC,cAAA,eAA8C;IAACD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA1BH,EAAA,CAAAI,SAAA,EAAmB;IAAnBJ,EAAA,CAAAK,kBAAA,OAAAC,SAAA,CAAAC,IAAA,MAAmB;;;;;IAFpEP,EAAA,CAAAC,cAAA,oBAAwE;IACtED,EAAA,CAAAE,MAAA,GACA;IAAAF,EAAA,CAAAQ,UAAA,IAAAC,gEAAA,mBAA8C;IAChDT,EAAA,CAAAG,YAAA,EAAa;;;;IAHuCH,EAAA,CAAAU,UAAA,UAAAJ,SAAA,CAAAK,EAAA,CAAmB;IACrEX,EAAA,CAAAI,SAAA,EACA;IADAJ,EAAA,CAAAK,kBAAA,MAAAC,SAAA,CAAAM,MAAA,MACA;IAAOZ,EAAA,CAAAI,SAAA,EAAiB;IAAjBJ,EAAA,CAAAU,UAAA,SAAAJ,SAAA,CAAAC,IAAA,CAAiB;;;;;IAmB5BP,EAAA,CAAAC,cAAA,eAAqD;IACnDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IADTH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAQ,MAAA,CAAAC,oBAAA,CAAAC,WAAA,OACF;;;;;IALJf,EADF,CAAAC,cAAA,cAA2E,cACrC;IAAAD,EAAA,CAAAE,MAAA,GAAyD;;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAEjGH,EADF,CAAAC,cAAA,cAAmC,oBAClB;IACbD,EAAA,CAAAQ,UAAA,IAAAQ,6DAAA,uBAAqD;IAK3DhB,EAFI,CAAAG,YAAA,EAAgB,EACZ,EACF;;;;IARgCH,EAAA,CAAAI,SAAA,GAAyD;IAAzDJ,EAAA,CAAAK,kBAAA,KAAAL,EAAA,CAAAiB,WAAA,oDAAyD;IAG1DjB,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAU,UAAA,YAAAG,MAAA,CAAAK,iBAAA,CAAoB;;;ADhBzD,OAAM,MAAOC,kCAAkC;EAS7CC,YACEC,EAAe,EACEC,aAAuC,EACvCC,SAA2B,EAC3BC,qBAAiE,EACjEC,wBAAkD;IAHlD,KAAAH,aAAa,GAAbA,aAAa;IACb,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,wBAAwB,GAAxBA,wBAAwB;IAVlC,KAAAC,cAAc,GAAG,IAAI9B,WAAW,CAAC,EAAE,CAAC;IAC7C,KAAA+B,gBAAgB,GAAa,EAAE;IAC/B,KAAAT,iBAAiB,GAAa,EAAE;IAU9B,IAAI,CAACU,IAAI,GAAGP,EAAE,CAACQ,KAAK,CAAC;MACnBC,OAAO,EAAE,IAAI,CAACJ;KACf,CAAC;EACJ;EAEAK,QAAQA,CAAA;IACN,IAAI,CAACN,wBAAwB,CAACO,OAAO,CAACjC,YAAY,CAACkC,MAAM,CAAC,CACvDC,IAAI,CAACrC,KAAK,EAAE,CAAC,CACbsC,SAAS,CAAEL,OAAiB,IAAI;MAC/B,IAAI,CAACH,gBAAgB,GAAGG,OAAO;IACjC,CAAC,CAAC;IAEJ,IAAI,CAACN,qBAAqB,CAACY,WAAW,CAAC,IAAI,CAACC,MAAM,CAACC,IAAI,CAAC,CACrDJ,IAAI,CAACrC,KAAK,EAAE,CAAC,CACbsC,SAAS,CAAEI,QAA6B,IAAI;MAC3C,IAAI,CAACC,eAAe,GAAGD,QAAQ;MAC/B,IAAIA,QAAQ,EAAEE,8BAA8B,EAAE;QAC5C,IAAI,CAACvB,iBAAiB,GAAG,CAAC,GAAGqB,QAAQ,CAACE,8BAA8B,CAAC;QACrE,IAAI,CAACf,cAAc,CAACgB,QAAQ,CAAC,IAAI,CAACxB,iBAAiB,CAAC;MACtD;IACF,CAAC,CAAC;EACN;EAEAyB,uBAAuBA,CAACC,WAAqB;IAC3C,IAAI,CAAC1B,iBAAiB,GAAG0B,WAAW;IACpC,IAAI,CAAClB,cAAc,CAACgB,QAAQ,CAACE,WAAW,CAAC;EAC3C;EAEAC,WAAWA,CAAA;IACT,MAAMC,SAAS,GAAG,IAAI,CAACpB,cAAc,CAACqB,KAAK,IAAI,EAAE;IACjD,MAAMC,cAAc,GAAG;MAAEP,8BAA8B,EAAEK;IAAS,CAAE;IAEpE,IAAI,CAACtB,qBAAqB,CAACyB,aAAa,CAACD,cAAc,EAAE,IAAI,CAACX,MAAM,CAACC,IAAI,CAAC,CACvEJ,IAAI,CACHrC,KAAK,EAAE,EACPC,SAAS,CAAC,MAAM,IAAI,CAACyB,SAAS,CAAC2B,GAAG,CAAC,+CAA+C,CAAC,CAAC,CACrF,CACAf,SAAS,CAACgB,GAAG,IAAG;MACf,IAAI,CAAC7B,aAAa,CAAC8B,OAAO,CAACD,GAAG,CAAC;MAC/B,IAAI,IAAI,CAACX,eAAe,EAAE;QACxB,IAAI,CAACA,eAAe,CAACC,8BAA8B,GAAGK,SAAS;MACjE;IACF,CAAC,CAAC;EACN;EAEAO,YAAYA,CAAA;IACV,MAAML,cAAc,GAAG;MAAEP,8BAA8B,EAAE;IAAE,CAAE;IAC7D,IAAI,CAACjB,qBAAqB,CAACyB,aAAa,CAACD,cAAc,EAAE,IAAI,CAACX,MAAM,CAACC,IAAI,CAAC,CACvEJ,IAAI,CACHrC,KAAK,EAAE,EACPC,SAAS,CAAC,MAAM,IAAI,CAACyB,SAAS,CAAC2B,GAAG,CAAC,+CAA+C,CAAC,CAAC,CACrF,CACAf,SAAS,CAAEgB,GAAW,IAAI;MACzB,IAAI,CAACzB,cAAc,CAACgB,QAAQ,CAAC,EAAE,CAAC;MAChC,IAAI,CAACxB,iBAAiB,GAAG,EAAE;MAC3B,IAAI,CAACI,aAAa,CAAC8B,OAAO,CAACD,GAAG,CAAC;MAC/B,IAAI,IAAI,CAACX,eAAe,EAAE;QACxB,IAAI,CAACA,eAAe,CAACC,8BAA8B,GAAG,EAAE;MAC1D;IACF,CAAC,CAAC;EACN;EAEA3B,oBAAoBA,CAACwC,QAAgB;IACnC,MAAM1C,MAAM,GAAG,IAAI,CAACe,gBAAgB,CAAC4B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC7C,EAAE,KAAK2C,QAAQ,CAAC;IACjE,OAAO1C,MAAM,GAAGA,MAAM,CAACA,MAAM,GAAG0C,QAAQ;EAC1C;;;uCAjFWnC,kCAAkC,EAAAnB,EAAA,CAAAyD,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA3D,EAAA,CAAAyD,iBAAA,CAAAG,EAAA,CAAAC,wBAAA,GAAA7D,EAAA,CAAAyD,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAA/D,EAAA,CAAAyD,iBAAA,CAAAO,EAAA,CAAAC,qBAAA,GAAAjE,EAAA,CAAAyD,iBAAA,CAAAS,EAAA,CAAAC,wBAAA;IAAA;EAAA;;;YAAlChD,kCAAkC;MAAAiD,SAAA;MAAAC,MAAA;QAAAhC,MAAA;MAAA;MAAAiC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4CAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCd3C3E,EAFJ,CAAAC,cAAA,aAAoB,aACQ,WAClB;UAAAD,EAAA,CAAAE,MAAA,GAA6D;;UACrEF,EADqE,CAAAG,YAAA,EAAO,EACtE;UAGFH,EAFJ,CAAAC,cAAA,aAA8B,wBACkF,gBACjG;UAAAD,EAAA,CAAAE,MAAA,GAA6D;;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACpFH,EAAA,CAAAC,cAAA,qBAG4D;UAA1DD,EAAA,CAAA6E,UAAA,6BAAAC,mFAAAC,MAAA;YAAA,OAAmBH,GAAA,CAAAjC,uBAAA,CAAAoC,MAAA,CAAAhC,KAAA,CAAqC;UAAA,EAAC;UACzD/C,EAAA,CAAAQ,UAAA,KAAAwE,yDAAA,wBAAwE;UAI1EhF,EAAA,CAAAG,YAAA,EAAa;UACbH,EAAA,CAAAC,cAAA,mBAAwB;UAAAD,EAAA,CAAAE,MAAA,qEAA6D;UACvFF,EADuF,CAAAG,YAAA,EAAW,EACjF;UAEjBH,EAAA,CAAAC,cAAA,iBAA0H;;UAAlGD,EAAA,CAAA6E,UAAA,mBAAAI,qEAAA;YAAA,OAASL,GAAA,CAAA/B,WAAA,EAAa;UAAA,EAAC;UAC7C7C,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAChBF,EADgB,CAAAG,YAAA,EAAW,EAClB;UACTH,EAAA,CAAAC,cAAA,iBAA4H;;UAApGD,EAAA,CAAA6E,UAAA,mBAAAK,qEAAA;YAAA,OAASN,GAAA,CAAAvB,YAAA,EAAc;UAAA,EAAC;UAC9CrD,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAGpBF,EAHoB,CAAAG,YAAA,EAAW,EAClB,EACL,EACF;UAENH,EAAA,CAAAQ,UAAA,KAAA2E,kDAAA,iBAA2E;;;UA1BjEnF,EAAA,CAAAI,SAAA,GAA6D;UAA7DJ,EAAA,CAAAK,kBAAA,KAAAL,EAAA,CAAAiB,WAAA,wDAA6D;UAItDjB,EAAA,CAAAI,SAAA,GAA6D;UAA7DJ,EAAA,CAAAoF,iBAAA,CAAApF,EAAA,CAAAiB,WAAA,oDAA6D;UAEtEjB,EAAA,CAAAI,SAAA,GAA8B;UAA9BJ,EAAA,CAAAU,UAAA,gBAAAkE,GAAA,CAAAlD,cAAA,CAA8B;UAGC1B,EAAA,CAAAI,SAAA,EAAmB;UAAnBJ,EAAA,CAAAU,UAAA,YAAAkE,GAAA,CAAAjD,gBAAA,CAAmB;UAQN3B,EAAA,CAAAI,SAAA,GAAyE;UAAzEJ,EAAA,CAAAU,UAAA,eAAAV,EAAA,CAAAiB,WAAA,yDAAyE;UAGxEjB,EAAA,CAAAI,SAAA,GAA0E;UAA1EJ,EAAA,CAAAU,UAAA,eAAAV,EAAA,CAAAiB,WAAA,0DAA0E;UAMzHjB,EAAA,CAAAI,SAAA,GAAkC;UAAlCJ,EAAA,CAAAU,UAAA,SAAAkE,GAAA,CAAA1D,iBAAA,CAAAmE,MAAA,KAAkC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}