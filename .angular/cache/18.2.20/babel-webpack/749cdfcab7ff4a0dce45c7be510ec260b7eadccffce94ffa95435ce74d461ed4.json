{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FlexLayoutModule } from '@angular/flex-layout';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiGridModule, SwuiNotificationsModule, SwuiPagePanelModule, SwuiSchemaTopFilterModule } from '@skywind-group/lib-swui';\nimport { DownloadCsvModule } from '../../../../common/components/download-csv/download-csv.module';\nimport { HintsModule } from '../../../../common/components/hints/hints.module';\nimport { TransfersService } from '../../../../common/services/transfers.service';\nimport { TransfersComponent } from './transfers.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@skywind-group/lib-swui\";\nexport class TransfersModule {\n  static {\n    this.ɵfac = function TransfersModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TransfersModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: TransfersModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [TransfersService],\n      imports: [CommonModule, SwuiPagePanelModule, SwuiGridModule, SwuiNotificationsModule.forRoot(), TranslateModule, FlexLayoutModule, MatTooltipModule, SwuiSchemaTopFilterModule, DownloadCsvModule, HintsModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(TransfersModule, {\n    declarations: [TransfersComponent],\n    imports: [CommonModule, SwuiPagePanelModule, SwuiGridModule, i1.SwuiNotificationsModule, TranslateModule, FlexLayoutModule, MatTooltipModule, SwuiSchemaTopFilterModule, DownloadCsvModule, HintsModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FlexLayoutModule", "MatTooltipModule", "TranslateModule", "SwuiGridModule", "SwuiNotificationsModule", "SwuiPagePanelModule", "SwuiSchemaTopFilterModule", "DownloadCsvModule", "HintsModule", "TransfersService", "TransfersComponent", "TransfersModule", "imports", "forRoot", "declarations", "i1"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/payments/components/transfers/transfers.module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { FlexLayoutModule } from '@angular/flex-layout';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiGridModule, SwuiNotificationsModule, SwuiPagePanelModule, SwuiSchemaTopFilterModule } from '@skywind-group/lib-swui';\nimport { DownloadCsvModule } from '../../../../common/components/download-csv/download-csv.module';\nimport { HintsModule } from '../../../../common/components/hints/hints.module';\nimport { TransfersService } from '../../../../common/services/transfers.service';\nimport { TransfersComponent } from './transfers.component';\n\n\n@NgModule({\n  imports: [\n    CommonModule,\n    SwuiPagePanelModule,\n    SwuiGridModule,\n    SwuiNotificationsModule.forRoot(),\n    TranslateModule,\n    FlexLayoutModule,\n    MatTooltipModule,\n    SwuiSchemaTopFilterModule,\n    DownloadCsvModule,\n    HintsModule\n  ],\n  exports: [],\n  declarations: [\n    TransfersComponent,\n  ],\n  providers: [\n    TransfersService,\n  ]\n})\nexport class TransfersModule {\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,cAAc,EAAEC,uBAAuB,EAAEC,mBAAmB,EAAEC,yBAAyB,QAAQ,yBAAyB;AACjI,SAASC,iBAAiB,QAAQ,gEAAgE;AAClG,SAASC,WAAW,QAAQ,kDAAkD;AAC9E,SAASC,gBAAgB,QAAQ,+CAA+C;AAChF,SAASC,kBAAkB,QAAQ,uBAAuB;;;AAwB1D,OAAM,MAAOC,eAAe;;;uCAAfA,eAAe;IAAA;EAAA;;;YAAfA;IAAe;EAAA;;;iBAJf,CACTF,gBAAgB,CACjB;MAAAG,OAAA,GAjBCb,YAAY,EACZM,mBAAmB,EACnBF,cAAc,EACdC,uBAAuB,CAACS,OAAO,EAAE,EACjCX,eAAe,EACfF,gBAAgB,EAChBC,gBAAgB,EAChBK,yBAAyB,EACzBC,iBAAiB,EACjBC,WAAW;IAAA;EAAA;;;2EAUFG,eAAe;IAAAG,YAAA,GANxBJ,kBAAkB;IAAAE,OAAA,GAblBb,YAAY,EACZM,mBAAmB,EACnBF,cAAc,EAAAY,EAAA,CAAAX,uBAAA,EAEdF,eAAe,EACfF,gBAAgB,EAChBC,gBAAgB,EAChBK,yBAAyB,EACzBC,iBAAiB,EACjBC,WAAW;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}