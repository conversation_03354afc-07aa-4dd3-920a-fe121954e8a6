{"ast": null, "code": "import { MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport { FormGroup } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/material/button\";\nimport * as i4 from \"@skywind-group/lib-swui\";\nimport * as i5 from \"@ngx-translate/core\";\nfunction WidgetOptionsDialogComponent_lib_dynamic_form_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"lib-dynamic-form\", 6);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"form\", ctx_r0.form)(\"options\", ctx_r0.options);\n  }\n}\nfunction jsonReplacer(k, v) {\n  if (k === '') {\n    return v;\n  }\n  return v === null ? undefined : v;\n}\nfunction jsonStringify(value) {\n  return JSON.stringify(value, jsonReplacer);\n}\nexport class WidgetOptionsDialogComponent {\n  constructor(dialogRef, {\n    widget,\n    options\n  }) {\n    this.dialogRef = dialogRef;\n    this.form = new FormGroup({}, {\n      validators: control => {\n        const value = control.getRawValue();\n        if (value) {\n          try {\n            jsonStringify(value);\n          } catch {\n            return {\n              jsonInvalid: true\n            };\n          }\n        }\n        return null;\n      }\n    });\n    this.options = widget.properties && Object.entries(widget.properties).reduce((result, [key, prop]) => ({\n      ...result,\n      [key]: {\n        ...prop,\n        value: options[key]\n      }\n    }), {});\n  }\n  saveChanges(event) {\n    event.preventDefault();\n    this.dialogRef.close(jsonStringify(this.form.getRawValue()));\n  }\n  static {\n    this.ɵfac = function WidgetOptionsDialogComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || WidgetOptionsDialogComponent)(i0.ɵɵdirectiveInject(i1.MatDialogRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: WidgetOptionsDialogComponent,\n      selectors: [[\"ng-component\"]],\n      decls: 11,\n      vars: 8,\n      consts: [[\"mat-dialog-title\", \"\"], [1, \"mat-typography\"], [3, \"form\", \"options\", 4, \"ngIf\"], [\"align\", \"end\"], [\"mat-button\", \"\", \"color\", \"primary\", \"mat-dialog-close\", \"\", 1, \"mat-button-md\"], [\"mat-flat-button\", \"\", \"color\", \"primary\", 1, \"mat-button-md\", 3, \"click\", \"disabled\"], [3, \"form\", \"options\"]],\n      template: function WidgetOptionsDialogComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"h2\", 0);\n          i0.ɵɵtext(1, \"Settings\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(2, \"mat-dialog-content\", 1);\n          i0.ɵɵtemplate(3, WidgetOptionsDialogComponent_lib_dynamic_form_3_Template, 1, 2, \"lib-dynamic-form\", 2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"mat-dialog-actions\", 3)(5, \"button\", 4);\n          i0.ɵɵtext(6);\n          i0.ɵɵpipe(7, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function WidgetOptionsDialogComponent_Template_button_click_8_listener($event) {\n            return ctx.saveChanges($event);\n          });\n          i0.ɵɵtext(9);\n          i0.ɵɵpipe(10, \"translate\");\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.options);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(7, 4, \"ENTITY_SETUP.USERS.MODALS.btnClose\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.form.invalid);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(10, 6, \"ENTITY_SETUP.USERS.MODALS.btnSave\"), \" \");\n        }\n      },\n      dependencies: [i2.NgIf, i1.MatDialogClose, i1.MatDialogTitle, i1.MatDialogActions, i1.MatDialogContent, i3.MatButton, i4.MatDynamicFormComponent, i5.TranslatePipe],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["MAT_DIALOG_DATA", "FormGroup", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r0", "form", "options", "jsonReplacer", "k", "v", "undefined", "jsonStringify", "value", "JSON", "stringify", "WidgetOptionsDialogComponent", "constructor", "dialogRef", "widget", "validators", "control", "getRawValue", "jsonInvalid", "properties", "Object", "entries", "reduce", "result", "key", "prop", "saveChanges", "event", "preventDefault", "close", "ɵɵdirectiveInject", "i1", "MatDialogRef", "selectors", "decls", "vars", "consts", "template", "WidgetOptionsDialogComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "WidgetOptionsDialogComponent_lib_dynamic_form_3_Template", "ɵɵlistener", "WidgetOptionsDialogComponent_Template_button_click_8_listener", "$event", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "invalid"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/lobby/lobby-form/lobby-menu-items/lobby-menu-items-setup/lobby-menu-items-widgets/widget-options-dialog/widget-options-dialog.component.ts", "/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/lobby/lobby-form/lobby-menu-items/lobby-menu-items-setup/lobby-menu-items-widgets/widget-options-dialog/widget-options-dialog.component.html"], "sourcesContent": ["import { Component, Inject } from '@angular/core';\nimport { LobbyWidget } from '../../../../../../../common/services/lobby-widgets.service';\nimport { LobbyMenuItemWidgetOptions } from '../../../../../lobby.model';\nimport { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';\nimport { FormGroup, ValidationErrors } from '@angular/forms';\nimport { DynamicFormOptionData } from '@skywind-group/lib-swui/swui-dynamic-form/dynamic-form.model';\n\nfunction jsonReplacer( k, v: any ) {\n  if (k === '') {\n    return v;\n  }\n  return v === null ? undefined : v;\n}\n\nfunction jsonStringify( value: any ): string {\n  return JSON.stringify(value, jsonReplacer);\n}\n\nexport interface WidgetOptionsDialogData {\n  widget: LobbyWidget;\n  options?: LobbyMenuItemWidgetOptions;\n}\n\n@Component({\n  templateUrl: 'widget-options-dialog.component.html'\n})\nexport class WidgetOptionsDialogComponent {\n  readonly form = new FormGroup({}, {\n    validators: ( control: FormGroup ): ValidationErrors | null => {\n      const value = control.getRawValue();\n      if (value) {\n        try {\n          jsonStringify(value);\n        } catch {\n          return { jsonInvalid: true };\n        }\n      }\n      return null;\n    }\n  });\n  readonly options?: DynamicFormOptionData;\n\n  constructor( private readonly dialogRef: MatDialogRef<WidgetOptionsDialogComponent>,\n               @Inject(MAT_DIALOG_DATA) { widget, options }: WidgetOptionsDialogData ) {\n    this.options = widget.properties && Object.entries(widget.properties).reduce(( result, [key, prop] ) => ({\n      ...result,\n      [key]: {\n        ...prop,\n        value: options[key]\n      }\n    }), {});\n  }\n\n  saveChanges( event: Event ) {\n    event.preventDefault();\n    this.dialogRef.close(jsonStringify(this.form.getRawValue()));\n  }\n}\n", "<h2 mat-dialog-title>Settings</h2>\n\n<mat-dialog-content class=\"mat-typography\">\n  <lib-dynamic-form *ngIf=\"options\" [form]=\"form\" [options]=\"options\"></lib-dynamic-form>\n</mat-dialog-content>\n\n<mat-dialog-actions align=\"end\">\n  <button mat-button color=\"primary\" class=\"mat-button-md\" mat-dialog-close>\n    {{ 'ENTITY_SETUP.USERS.MODALS.btnClose' | translate }}\n  </button>\n  <button mat-flat-button [disabled]=\"form.invalid\" color=\"primary\" class=\"mat-button-md\" (click)=\"saveChanges($event)\">\n    {{ 'ENTITY_SETUP.USERS.MODALS.btnSave' | translate }}\n  </button>\n</mat-dialog-actions>\n"], "mappings": "AAGA,SAASA,eAAe,QAAsB,0BAA0B;AACxE,SAASC,SAAS,QAA0B,gBAAgB;;;;;;;;;ICD1DC,EAAA,CAAAC,SAAA,0BAAuF;;;;IAAvCD,EAAd,CAAAE,UAAA,SAAAC,MAAA,CAAAC,IAAA,CAAa,YAAAD,MAAA,CAAAE,OAAA,CAAoB;;;ADIrE,SAASC,YAAYA,CAAEC,CAAC,EAAEC,CAAM;EAC9B,IAAID,CAAC,KAAK,EAAE,EAAE;IACZ,OAAOC,CAAC;EACV;EACA,OAAOA,CAAC,KAAK,IAAI,GAAGC,SAAS,GAAGD,CAAC;AACnC;AAEA,SAASE,aAAaA,CAAEC,KAAU;EAChC,OAAOC,IAAI,CAACC,SAAS,CAACF,KAAK,EAAEL,YAAY,CAAC;AAC5C;AAUA,OAAM,MAAOQ,4BAA4B;EAgBvCC,YAA8BC,SAAqD,EAC7C;IAAEC,MAAM;IAAEZ;EAAO,CAA2B;IADpD,KAAAW,SAAS,GAATA,SAAS;IAf9B,KAAAZ,IAAI,GAAG,IAAIL,SAAS,CAAC,EAAE,EAAE;MAChCmB,UAAU,EAAIC,OAAkB,IAA8B;QAC5D,MAAMR,KAAK,GAAGQ,OAAO,CAACC,WAAW,EAAE;QACnC,IAAIT,KAAK,EAAE;UACT,IAAI;YACFD,aAAa,CAACC,KAAK,CAAC;UACtB,CAAC,CAAC,MAAM;YACN,OAAO;cAAEU,WAAW,EAAE;YAAI,CAAE;UAC9B;QACF;QACA,OAAO,IAAI;MACb;KACD,CAAC;IAKA,IAAI,CAAChB,OAAO,GAAGY,MAAM,CAACK,UAAU,IAAIC,MAAM,CAACC,OAAO,CAACP,MAAM,CAACK,UAAU,CAAC,CAACG,MAAM,CAAC,CAAEC,MAAM,EAAE,CAACC,GAAG,EAAEC,IAAI,CAAC,MAAO;MACvG,GAAGF,MAAM;MACT,CAACC,GAAG,GAAG;QACL,GAAGC,IAAI;QACPjB,KAAK,EAAEN,OAAO,CAACsB,GAAG;;KAErB,CAAC,EAAE,EAAE,CAAC;EACT;EAEAE,WAAWA,CAAEC,KAAY;IACvBA,KAAK,CAACC,cAAc,EAAE;IACtB,IAAI,CAACf,SAAS,CAACgB,KAAK,CAACtB,aAAa,CAAC,IAAI,CAACN,IAAI,CAACgB,WAAW,EAAE,CAAC,CAAC;EAC9D;;;uCA9BWN,4BAA4B,EAAAd,EAAA,CAAAiC,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAAnC,EAAA,CAAAiC,iBAAA,CAiBlBnC,eAAe;IAAA;EAAA;;;YAjBzBgB,4BAA4B;MAAAsB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC1BzC1C,EAAA,CAAA4C,cAAA,YAAqB;UAAA5C,EAAA,CAAA6C,MAAA,eAAQ;UAAA7C,EAAA,CAAA8C,YAAA,EAAK;UAElC9C,EAAA,CAAA4C,cAAA,4BAA2C;UACzC5C,EAAA,CAAA+C,UAAA,IAAAC,wDAAA,8BAAoE;UACtEhD,EAAA,CAAA8C,YAAA,EAAqB;UAGnB9C,EADF,CAAA4C,cAAA,4BAAgC,gBAC4C;UACxE5C,EAAA,CAAA6C,MAAA,GACF;;UAAA7C,EAAA,CAAA8C,YAAA,EAAS;UACT9C,EAAA,CAAA4C,cAAA,gBAAsH;UAA9B5C,EAAA,CAAAiD,UAAA,mBAAAC,8DAAAC,MAAA;YAAA,OAASR,GAAA,CAAAd,WAAA,CAAAsB,MAAA,CAAmB;UAAA,EAAC;UACnHnD,EAAA,CAAA6C,MAAA,GACF;;UACF7C,EADE,CAAA8C,YAAA,EAAS,EACU;;;UAVA9C,EAAA,CAAAoD,SAAA,GAAa;UAAbpD,EAAA,CAAAE,UAAA,SAAAyC,GAAA,CAAAtC,OAAA,CAAa;UAK9BL,EAAA,CAAAoD,SAAA,GACF;UADEpD,EAAA,CAAAqD,kBAAA,MAAArD,EAAA,CAAAsD,WAAA,kDACF;UACwBtD,EAAA,CAAAoD,SAAA,GAAyB;UAAzBpD,EAAA,CAAAE,UAAA,aAAAyC,GAAA,CAAAvC,IAAA,CAAAmD,OAAA,CAAyB;UAC/CvD,EAAA,CAAAoD,SAAA,EACF;UADEpD,EAAA,CAAAqD,kBAAA,MAAArD,EAAA,CAAAsD,WAAA,kDACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}