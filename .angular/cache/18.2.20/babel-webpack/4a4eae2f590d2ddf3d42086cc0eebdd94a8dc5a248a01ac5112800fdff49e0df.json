{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { SetupGameProgressComponent } from '../setup-game-progress.component';\nimport { SetupGameItem } from '../setup-game.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"../setup-game-info/setup-game-info.component\";\nimport * as i3 from \"../setup-game-progress.component\";\nimport * as i4 from \"../setup-game-form.component\";\nimport * as i5 from \"@ngx-translate/core\";\nconst _c0 = (a0, a1) => ({\n  \"unviewed\": a0,\n  \"active\": a1\n});\nfunction ManageGamesSetupComponent_tr_11_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 13);\n  }\n}\nfunction ManageGamesSetupComponent_tr_11_i_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 14);\n    i0.ɵɵpipe(1, \"translate\");\n  }\n  if (rf & 2) {\n    i0.ɵɵpropertyInterpolate(\"title\", i0.ɵɵpipeBind1(1, 1, \"ENTITY_SETUP.GAMES.MODALS.gameReady\"));\n  }\n}\nfunction ManageGamesSetupComponent_tr_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 10);\n    i0.ɵɵlistener(\"click\", function ManageGamesSetupComponent_tr_11_Template_tr_click_0_listener() {\n      const item_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.selectItem(item_r2));\n    });\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵtemplate(2, ManageGamesSetupComponent_tr_11_i_2_Template, 1, 0, \"i\", 11)(3, ManageGamesSetupComponent_tr_11_i_3_Template, 2, 3, \"i\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"td\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(4, _c0, item_r2.unviewed, ctx_r2.isItemSelected(item_r2)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", item_r2.required);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r2.complete);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r2.title);\n  }\n}\nexport class ManageGamesSetupComponent {\n  set addedGames(value) {\n    if (!value) return;\n    this._addedGames = value;\n    this.setupGameItems = [];\n    value.forEach(game => {\n      let item = new SetupGameItem(game);\n      [...new Set(this._gamesSubmitted)].find(gameSubmitted => {\n        if (game.code === gameSubmitted) {\n          item.complete = true;\n        }\n      });\n      this.setupGameItems.push(item);\n    });\n    this.setNextItem();\n  }\n  get addedGames() {\n    return this._addedGames;\n  }\n  constructor() {\n    this.applyButtonDisable = new EventEmitter();\n    this.gamesSetupComplete = new EventEmitter();\n    this._gamesSubmitted = [];\n  }\n  ngOnDestroy() {\n    this._gamesSubmitted = [];\n  }\n  isItemSelected(item) {\n    return item === this.selectedItem;\n  }\n  selectItem(item) {\n    this.selectedItem = item;\n    item.unviewed = false;\n  }\n  onGameSubmit(formValues) {\n    this.selectedItem.setChanges(formValues);\n    this._gamesSubmitted.push(this.selectedItem.game.code);\n    this.progress.updateProgress();\n    this.setNextItem();\n  }\n  setNextItem() {\n    let idx = this.setupGameItems.indexOf(this.selectedItem);\n    idx++;\n    if (this.setupGameItems[idx]) {\n      this.selectItem(this.setupGameItems[idx]);\n    }\n  }\n  onProgressFinished() {\n    this.applyButtonDisable.emit(false);\n  }\n  completeGamesSetup(event) {\n    event.preventDefault();\n    let addedGames = this.setupGameItems.map(item => item.getGameWithChanges());\n    this.gamesSetupComplete.emit({\n      addedGames\n    });\n  }\n  setAllGamesAsComplete() {\n    this.setupGameItems.forEach(item => {\n      if (!item.required) {\n        item.complete = true;\n        item.unviewed = false;\n      }\n    });\n    this.progress.updateProgress();\n  }\n  static {\n    this.ɵfac = function ManageGamesSetupComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ManageGamesSetupComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ManageGamesSetupComponent,\n      selectors: [[\"manage-games-setup\"]],\n      viewQuery: function ManageGamesSetupComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(SetupGameProgressComponent, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.progress = _t.first);\n        }\n      },\n      inputs: {\n        entity: \"entity\",\n        entityGames: \"entityGames\",\n        addedGames: \"addedGames\"\n      },\n      outputs: {\n        applyButtonDisable: \"applyButtonDisable\",\n        gamesSetupComplete: \"gamesSetupComplete\"\n      },\n      decls: 17,\n      vars: 8,\n      consts: [[1, \"row\"], [1, \"col-sm-6\", \"col-md-4\"], [1, \"content-group\"], [1, \"table\", \"table-xxs\", \"table-fixed-header\", \"games-setup\"], [3, \"ngClass\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-sm-6\", \"col-md-8\"], [1, \"form-horizontal\"], [3, \"game\"], [3, \"gameSubmit\", \"game\", \"entity\"], [3, \"progressFinished\", \"setupGameItems\"], [3, \"click\", \"ngClass\"], [\"class\", \"icon-warning text-warning\", 4, \"ngIf\"], [\"class\", \"icon-checkmark3 text-success\", 3, \"title\", 4, \"ngIf\"], [1, \"icon-warning\", \"text-warning\"], [1, \"icon-checkmark3\", \"text-success\", 3, \"title\"]],\n      template: function ManageGamesSetupComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"table\", 3)(4, \"thead\")(5, \"tr\");\n          i0.ɵɵelement(6, \"th\");\n          i0.ɵɵelementStart(7, \"th\");\n          i0.ɵɵtext(8);\n          i0.ɵɵpipe(9, \"translate\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(10, \"tbody\");\n          i0.ɵɵtemplate(11, ManageGamesSetupComponent_tr_11_Template, 6, 7, \"tr\", 4);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(12, \"div\", 5)(13, \"div\", 6);\n          i0.ɵɵelement(14, \"setup-game-info\", 7);\n          i0.ɵɵelementStart(15, \"setup-game-form\", 8);\n          i0.ɵɵlistener(\"gameSubmit\", function ManageGamesSetupComponent_Template_setup_game_form_gameSubmit_15_listener($event) {\n            return ctx.onGameSubmit($event);\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(16, \"setup-game-progress\", 9);\n          i0.ɵɵlistener(\"progressFinished\", function ManageGamesSetupComponent_Template_setup_game_progress_progressFinished_16_listener() {\n            return ctx.onProgressFinished();\n          });\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(9, 6, \"ENTITY_SETUP.GAMES.MODALS.gameName\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.setupGameItems);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"game\", ctx.selectedItem == null ? null : ctx.selectedItem.game);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"game\", ctx.selectedItem == null ? null : ctx.selectedItem.getGameWithChanges())(\"entity\", ctx.entity);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"setupGameItems\", ctx.setupGameItems);\n        }\n      },\n      dependencies: [i1.NgClass, i1.NgForOf, i1.NgIf, i2.SetupGameInfoComponent, i3.SetupGameProgressComponent, i4.SetupGameFormComponent, i5.TranslatePipe],\n      styles: [\".games-setup[_ngcontent-%COMP%] {\\n  table-layout: fixed;\\n}\\n.games-setup[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background-color: #fafafa;\\n  border-bottom: 1px solid #c1c1c1 !important;\\n}\\n.games-setup[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:nth-child(2), \\n.games-setup[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:nth-child(2) {\\n  width: calc(100% - 51px);\\n  white-space: nowrap;\\n  text-overflow: ellipsis;\\n  overflow: hidden;\\n}\\n.games-setup[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:first-child, \\n.games-setup[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:first-child {\\n  width: 51px;\\n  text-align: center;\\n}\\n.games-setup[_ngcontent-%COMP%]   .unviewed[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n}\\n.games-setup[_ngcontent-%COMP%]   .active[_ngcontent-%COMP%] {\\n  background-color: #fafafa;\\n}\\n.games-setup[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%] {\\n  height: 330px;\\n}\\n.games-setup[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n  cursor: pointer;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvYnVzaW5lc3MtbWFuYWdlbWVudC9jb21wb25lbnRzL2VudGl0aWVzL3RhYi1nYW1lcy9tYW5hZ2UtZ2FtZXMvbWFuYWdlLWdhbWVzLXNldHVwL21hbmFnZS1nYW1lcy1zZXR1cC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLG1CQUFBO0FBQ0Y7QUFBRTtFQUNFLHlCQUFBO0VBQ0EsMkNBQUE7QUFFSjtBQUVJOztFQUNFLHdCQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLGdCQUFBO0FBQ047QUFDSTs7RUFDRSxXQUFBO0VBQ0Esa0JBQUE7QUFFTjtBQUNFO0VBQ0UsZ0JBQUE7QUFDSjtBQUNFO0VBQ0UseUJBQUE7QUFDSjtBQUNFO0VBQ0UsYUFBQTtBQUNKO0FBRUk7RUFDRSxlQUFBO0FBQU4iLCJzb3VyY2VzQ29udGVudCI6WyIuZ2FtZXMtc2V0dXAge1xuICB0YWJsZS1sYXlvdXQ6IGZpeGVkO1xuICB0aCB7XG4gICAgYmFja2dyb3VuZC1jb2xvcjogI2ZhZmFmYTtcbiAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2MxYzFjMSAhaW1wb3J0YW50O1xuICB9XG4gIHRkLFxuICB0aCB7XG4gICAgJjpudGgtY2hpbGQoMikge1xuICAgICAgd2lkdGg6IGNhbGMoMTAwJSAtIDUxcHgpO1xuICAgICAgd2hpdGUtc3BhY2U6IG5vd3JhcDtcbiAgICAgIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzO1xuICAgICAgb3ZlcmZsb3c6IGhpZGRlbjtcbiAgICB9XG4gICAgJjpmaXJzdC1jaGlsZCB7XG4gICAgICB3aWR0aDogNTFweDtcbiAgICAgIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgICB9XG4gIH1cbiAgLnVudmlld2VkIHtcbiAgICBmb250LXdlaWdodDogNTAwO1xuICB9XG4gIC5hY3RpdmUge1xuICAgIGJhY2tncm91bmQtY29sb3I6ICNmYWZhZmE7XG4gIH1cbiAgdGJvZHkge1xuICAgIGhlaWdodDogMzMwcHg7XG4gIH1cbiAgdHIge1xuICAgICY6aG92ZXIge1xuICAgICAgY3Vyc29yOiBwb2ludGVyXG4gICAgfVxuICB9XG5cblxufVxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "SetupGameProgressComponent", "SetupGameItem", "i0", "ɵɵelement", "ɵɵpropertyInterpolate", "ɵɵpipeBind1", "ɵɵelementStart", "ɵɵlistener", "ManageGamesSetupComponent_tr_11_Template_tr_click_0_listener", "item_r2", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "selectItem", "ɵɵtemplate", "ManageGamesSetupComponent_tr_11_i_2_Template", "ManageGamesSetupComponent_tr_11_i_3_Template", "ɵɵelementEnd", "ɵɵtext", "ɵɵproperty", "ɵɵpureFunction2", "_c0", "unviewed", "isItemSelected", "ɵɵadvance", "required", "complete", "ɵɵtextInterpolate", "title", "ManageGamesSetupComponent", "addedGames", "value", "_addedGames", "setupGameItems", "for<PERSON>ach", "game", "item", "Set", "_gamesSubmitted", "find", "gameSubmitted", "code", "push", "setNextItem", "constructor", "applyButtonDisable", "gamesSetupComplete", "ngOnDestroy", "selectedItem", "onGameSubmit", "formValues", "set<PERSON><PERSON><PERSON>", "progress", "updateProgress", "idx", "indexOf", "onProgressFinished", "emit", "completeGamesSetup", "event", "preventDefault", "map", "getGameWithChanges", "setAllGamesAsComplete", "selectors", "viewQuery", "ManageGamesSetupComponent_Query", "rf", "ctx", "ManageGamesSetupComponent_tr_11_Template", "ManageGamesSetupComponent_Template_setup_game_form_gameSubmit_15_listener", "$event", "ManageGamesSetupComponent_Template_setup_game_progress_progressFinished_16_listener", "entity"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-games/manage-games/manage-games-setup/manage-games-setup.component.ts", "/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-games/manage-games/manage-games-setup/manage-games-setup.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, Output, ViewChild } from '@angular/core';\nimport { Entity } from '../../../../../../../common/models/entity.model';\nimport { Game } from '../../../../../../../common/typings';\nimport { SetupGameProgressComponent } from '../setup-game-progress.component';\nimport { SetupGameItem } from '../setup-game.model';\n\n@Component({\n  selector: 'manage-games-setup',\n  templateUrl: './manage-games-setup.component.html',\n  styleUrls: ['./manage-games-setup.component.scss']\n})\nexport class ManageGamesSetupComponent {\n\n  @ViewChild(SetupGameProgressComponent, { static: true }) public progress: SetupGameProgressComponent;\n\n  @Input() public entity: Entity;\n  @Input() public entityGames: Game[];\n\n  @Output() public applyButtonDisable: EventEmitter<boolean> = new EventEmitter();\n  @Output() public gamesSetupComplete: EventEmitter<any> = new EventEmitter();\n\n  public setupGameItems: SetupGameItem[];\n  public selectedItem: SetupGameItem;\n\n  private _addedGames: Game[];\n  private _gamesSubmitted: string[] = [];\n\n  @Input()\n  set addedGames( value: Game[] ) {\n    if (!value) return;\n    this._addedGames = value;\n\n    this.setupGameItems = [];\n    value.forEach(( game: Game ) => {\n      let item = new SetupGameItem(game);\n      [...new Set(this._gamesSubmitted)].find(gameSubmitted => {\n        if (game.code === gameSubmitted) {\n          item.complete = true;\n        }\n      });\n      this.setupGameItems.push(item);\n    });\n\n    this.setNextItem();\n  }\n\n  get addedGames(): Game[] {\n    return this._addedGames;\n  }\n\n  constructor() {\n  }\n\n  ngOnDestroy() {\n    this._gamesSubmitted = [];\n  }\n\n  public isItemSelected( item: SetupGameItem ) {\n    return item === this.selectedItem;\n  }\n\n  public selectItem( item ) {\n    this.selectedItem = item;\n    item.unviewed = false;\n  }\n\n  public onGameSubmit( formValues ) {\n    this.selectedItem.setChanges(formValues);\n    this._gamesSubmitted.push(this.selectedItem.game.code);\n    this.progress.updateProgress();\n    this.setNextItem();\n  }\n\n  public setNextItem() {\n    let idx = this.setupGameItems.indexOf(this.selectedItem);\n    idx++;\n\n    if (this.setupGameItems[idx]) {\n      this.selectItem(this.setupGameItems[idx]);\n    }\n  }\n\n  public onProgressFinished() {\n    this.applyButtonDisable.emit(false);\n  }\n\n  public completeGamesSetup( event ) {\n    event.preventDefault();\n\n    let addedGames = this.setupGameItems\n      .map(( item: SetupGameItem ) => item.getGameWithChanges());\n\n    this.gamesSetupComplete.emit({ addedGames });\n  }\n\n  public setAllGamesAsComplete() {\n    this.setupGameItems.forEach(item => {\n      if (!item.required) {\n        item.complete = true;\n        item.unviewed = false;\n      }\n    });\n    this.progress.updateProgress();\n  }\n}\n", "<div class=\"row\">\n  <div class=\"col-sm-6 col-md-4\">\n    <div class=\"content-group\">\n      <table class=\"table table-xxs table-fixed-header games-setup\">\n        <thead>\n        <tr>\n          <th></th>\n          <th>{{'ENTITY_SETUP.GAMES.MODALS.gameName' | translate}}</th>\n        </tr>\n        </thead>\n        <tbody>\n        <tr *ngFor=\"let item of setupGameItems\" [ngClass]=\"{'unviewed':item.unviewed,'active':isItemSelected(item)}\"\n            (click)=\"selectItem(item)\">\n          <td>\n            <i class=\"icon-warning text-warning\" *ngIf=\"item.required\"></i>\n            <i class=\"icon-checkmark3 text-success\"\n               *ngIf=\"item.complete\" title=\"{{'ENTITY_SETUP.GAMES.MODALS.gameReady' | translate}}\"></i>\n          </td>\n          <td>{{ item.title }}</td>\n        </tr>\n        </tbody>\n      </table>\n    </div>\n  </div>\n  <div class=\"col-sm-6 col-md-8\">\n    <div class=\"form-horizontal\">\n      <setup-game-info [game]=\"selectedItem?.game\"></setup-game-info>\n      <setup-game-form [game]=\"selectedItem?.getGameWithChanges()\"\n                       [entity]=\"entity\"\n                       (gameSubmit)=\"onGameSubmit($event)\">\n      </setup-game-form>\n    </div>\n  </div>\n</div>\n<setup-game-progress [setupGameItems]=\"setupGameItems\" (progressFinished)=\"onProgressFinished()\"></setup-game-progress>\n"], "mappings": "AAAA,SAAoBA,YAAY,QAAkC,eAAe;AAGjF,SAASC,0BAA0B,QAAQ,kCAAkC;AAC7E,SAASC,aAAa,QAAQ,qBAAqB;;;;;;;;;;;;;ICUvCC,EAAA,CAAAC,SAAA,YAA+D;;;;;IAC/DD,EAAA,CAAAC,SAAA,YAC2F;;;;IAAlED,EAAA,CAAAE,qBAAA,UAAAF,EAAA,CAAAG,WAAA,8CAA6D;;;;;;IAL1FH,EAAA,CAAAI,cAAA,aAC+B;IAA3BJ,EAAA,CAAAK,UAAA,mBAAAC,6DAAA;MAAA,MAAAC,OAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAG,UAAA,CAAAP,OAAA,CAAgB;IAAA,EAAC;IAC5BP,EAAA,CAAAI,cAAA,SAAI;IAEFJ,EADA,CAAAe,UAAA,IAAAC,4CAAA,gBAA2D,IAAAC,4CAAA,gBAE4B;IACzFjB,EAAA,CAAAkB,YAAA,EAAK;IACLlB,EAAA,CAAAI,cAAA,SAAI;IAAAJ,EAAA,CAAAmB,MAAA,GAAgB;IACtBnB,EADsB,CAAAkB,YAAA,EAAK,EACtB;;;;;IARmClB,EAAA,CAAAoB,UAAA,YAAApB,EAAA,CAAAqB,eAAA,IAAAC,GAAA,EAAAf,OAAA,CAAAgB,QAAA,EAAAZ,MAAA,CAAAa,cAAA,CAAAjB,OAAA,GAAoE;IAGlEP,EAAA,CAAAyB,SAAA,GAAmB;IAAnBzB,EAAA,CAAAoB,UAAA,SAAAb,OAAA,CAAAmB,QAAA,CAAmB;IAErD1B,EAAA,CAAAyB,SAAA,EAAmB;IAAnBzB,EAAA,CAAAoB,UAAA,SAAAb,OAAA,CAAAoB,QAAA,CAAmB;IAErB3B,EAAA,CAAAyB,SAAA,GAAgB;IAAhBzB,EAAA,CAAA4B,iBAAA,CAAArB,OAAA,CAAAsB,KAAA,CAAgB;;;ADP9B,OAAM,MAAOC,yBAAyB;EAgBpC,IACIC,UAAUA,CAAEC,KAAa;IAC3B,IAAI,CAACA,KAAK,EAAE;IACZ,IAAI,CAACC,WAAW,GAAGD,KAAK;IAExB,IAAI,CAACE,cAAc,GAAG,EAAE;IACxBF,KAAK,CAACG,OAAO,CAAGC,IAAU,IAAK;MAC7B,IAAIC,IAAI,GAAG,IAAItC,aAAa,CAACqC,IAAI,CAAC;MAClC,CAAC,GAAG,IAAIE,GAAG,CAAC,IAAI,CAACC,eAAe,CAAC,CAAC,CAACC,IAAI,CAACC,aAAa,IAAG;QACtD,IAAIL,IAAI,CAACM,IAAI,KAAKD,aAAa,EAAE;UAC/BJ,IAAI,CAACV,QAAQ,GAAG,IAAI;QACtB;MACF,CAAC,CAAC;MACF,IAAI,CAACO,cAAc,CAACS,IAAI,CAACN,IAAI,CAAC;IAChC,CAAC,CAAC;IAEF,IAAI,CAACO,WAAW,EAAE;EACpB;EAEA,IAAIb,UAAUA,CAAA;IACZ,OAAO,IAAI,CAACE,WAAW;EACzB;EAEAY,YAAA;IAhCiB,KAAAC,kBAAkB,GAA0B,IAAIjD,YAAY,EAAE;IAC9D,KAAAkD,kBAAkB,GAAsB,IAAIlD,YAAY,EAAE;IAMnE,KAAA0C,eAAe,GAAa,EAAE;EA0BtC;EAEAS,WAAWA,CAAA;IACT,IAAI,CAACT,eAAe,GAAG,EAAE;EAC3B;EAEOf,cAAcA,CAAEa,IAAmB;IACxC,OAAOA,IAAI,KAAK,IAAI,CAACY,YAAY;EACnC;EAEOnC,UAAUA,CAAEuB,IAAI;IACrB,IAAI,CAACY,YAAY,GAAGZ,IAAI;IACxBA,IAAI,CAACd,QAAQ,GAAG,KAAK;EACvB;EAEO2B,YAAYA,CAAEC,UAAU;IAC7B,IAAI,CAACF,YAAY,CAACG,UAAU,CAACD,UAAU,CAAC;IACxC,IAAI,CAACZ,eAAe,CAACI,IAAI,CAAC,IAAI,CAACM,YAAY,CAACb,IAAI,CAACM,IAAI,CAAC;IACtD,IAAI,CAACW,QAAQ,CAACC,cAAc,EAAE;IAC9B,IAAI,CAACV,WAAW,EAAE;EACpB;EAEOA,WAAWA,CAAA;IAChB,IAAIW,GAAG,GAAG,IAAI,CAACrB,cAAc,CAACsB,OAAO,CAAC,IAAI,CAACP,YAAY,CAAC;IACxDM,GAAG,EAAE;IAEL,IAAI,IAAI,CAACrB,cAAc,CAACqB,GAAG,CAAC,EAAE;MAC5B,IAAI,CAACzC,UAAU,CAAC,IAAI,CAACoB,cAAc,CAACqB,GAAG,CAAC,CAAC;IAC3C;EACF;EAEOE,kBAAkBA,CAAA;IACvB,IAAI,CAACX,kBAAkB,CAACY,IAAI,CAAC,KAAK,CAAC;EACrC;EAEOC,kBAAkBA,CAAEC,KAAK;IAC9BA,KAAK,CAACC,cAAc,EAAE;IAEtB,IAAI9B,UAAU,GAAG,IAAI,CAACG,cAAc,CACjC4B,GAAG,CAAGzB,IAAmB,IAAMA,IAAI,CAAC0B,kBAAkB,EAAE,CAAC;IAE5D,IAAI,CAAChB,kBAAkB,CAACW,IAAI,CAAC;MAAE3B;IAAU,CAAE,CAAC;EAC9C;EAEOiC,qBAAqBA,CAAA;IAC1B,IAAI,CAAC9B,cAAc,CAACC,OAAO,CAACE,IAAI,IAAG;MACjC,IAAI,CAACA,IAAI,CAACX,QAAQ,EAAE;QAClBW,IAAI,CAACV,QAAQ,GAAG,IAAI;QACpBU,IAAI,CAACd,QAAQ,GAAG,KAAK;MACvB;IACF,CAAC,CAAC;IACF,IAAI,CAAC8B,QAAQ,CAACC,cAAc,EAAE;EAChC;;;uCA5FWxB,yBAAyB;IAAA;EAAA;;;YAAzBA,yBAAyB;MAAAmC,SAAA;MAAAC,SAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;yBAEzBtE,0BAA0B;;;;;;;;;;;;;;;;;;;;;UCR/BE,EALR,CAAAI,cAAA,aAAiB,aACgB,aACF,eACqC,YACrD,SACH;UACFJ,EAAA,CAAAC,SAAA,SAAS;UACTD,EAAA,CAAAI,cAAA,SAAI;UAAAJ,EAAA,CAAAmB,MAAA,GAAoD;;UAE1DnB,EAF0D,CAAAkB,YAAA,EAAK,EAC1D,EACG;UACRlB,EAAA,CAAAI,cAAA,aAAO;UACPJ,EAAA,CAAAe,UAAA,KAAAuD,wCAAA,gBAC+B;UAWrCtE,EAHM,CAAAkB,YAAA,EAAQ,EACF,EACJ,EACF;UAEJlB,EADF,CAAAI,cAAA,cAA+B,cACA;UAC3BJ,EAAA,CAAAC,SAAA,0BAA+D;UAC/DD,EAAA,CAAAI,cAAA,0BAEqD;UAApCJ,EAAA,CAAAK,UAAA,wBAAAkE,0EAAAC,MAAA;YAAA,OAAcH,GAAA,CAAAnB,YAAA,CAAAsB,MAAA,CAAoB;UAAA,EAAC;UAI1DxE,EAHM,CAAAkB,YAAA,EAAkB,EACd,EACF,EACF;UACNlB,EAAA,CAAAI,cAAA,8BAAiG;UAA1CJ,EAAA,CAAAK,UAAA,8BAAAoE,oFAAA;YAAA,OAAoBJ,GAAA,CAAAZ,kBAAA,EAAoB;UAAA,EAAC;UAACzD,EAAA,CAAAkB,YAAA,EAAsB;;;UA3BzGlB,EAAA,CAAAyB,SAAA,GAAoD;UAApDzB,EAAA,CAAA4B,iBAAA,CAAA5B,EAAA,CAAAG,WAAA,6CAAoD;UAIrCH,EAAA,CAAAyB,SAAA,GAAiB;UAAjBzB,EAAA,CAAAoB,UAAA,YAAAiD,GAAA,CAAAnC,cAAA,CAAiB;UAevBlC,EAAA,CAAAyB,SAAA,GAA2B;UAA3BzB,EAAA,CAAAoB,UAAA,SAAAiD,GAAA,CAAApB,YAAA,kBAAAoB,GAAA,CAAApB,YAAA,CAAAb,IAAA,CAA2B;UAC3BpC,EAAA,CAAAyB,SAAA,EAA2C;UAC3CzB,EADA,CAAAoB,UAAA,SAAAiD,GAAA,CAAApB,YAAA,kBAAAoB,GAAA,CAAApB,YAAA,CAAAc,kBAAA,GAA2C,WAAAM,GAAA,CAAAK,MAAA,CAC1B;UAMnB1E,EAAA,CAAAyB,SAAA,EAAiC;UAAjCzB,EAAA,CAAAoB,UAAA,mBAAAiD,GAAA,CAAAnC,cAAA,CAAiC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}