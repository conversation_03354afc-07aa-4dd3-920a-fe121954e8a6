{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FlexLayoutModule } from '@angular/flex-layout';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { RouterModule } from '@angular/router';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiGridModule, SwuiPagePanelModule } from '@skywind-group/lib-swui';\nimport { TrimInputValueModule } from '../../common/directives/trim-input-value/trim-input-value.module';\nimport { ProxyConfiramtionDialogComponent } from './proxy-confiramtion-dialog/proxy-confiramtion-dialog.component';\nimport { ProxyItemDialogComponent } from './proxy-item-dialog/proxy-item-dialog.component';\nimport { ProxyManagementComponent } from './proxy-management.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport const PROXY_MANAGEMENT_MODULES = [MatDialogModule, MatInputModule, MatFormFieldModule, MatButtonModule, FlexLayoutModule, ReactiveFormsModule, SwuiPagePanelModule, TranslateModule];\nexport class ProxyManagementModule {\n  static {\n    this.ɵfac = function ProxyManagementModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ProxyManagementModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ProxyManagementModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, PROXY_MANAGEMENT_MODULES, SwuiGridModule, RouterModule.forChild([{\n        path: '',\n        pathMatch: 'full',\n        component: ProxyManagementComponent,\n        data: {\n          title: 'Proxy'\n        }\n      }]), TrimInputValueModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ProxyManagementModule, {\n    declarations: [ProxyManagementComponent, ProxyItemDialogComponent, ProxyConfiramtionDialogComponent],\n    imports: [CommonModule, MatDialogModule, MatInputModule, MatFormFieldModule, MatButtonModule, FlexLayoutModule, ReactiveFormsModule, SwuiPagePanelModule, TranslateModule, SwuiGridModule, i1.RouterModule, TrimInputValueModule],\n    exports: [ProxyManagementComponent]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FlexLayoutModule", "ReactiveFormsModule", "MatButtonModule", "MatDialogModule", "MatFormFieldModule", "MatInputModule", "RouterModule", "TranslateModule", "SwuiGridModule", "SwuiPagePanelModule", "TrimInputValueModule", "ProxyConfiramtionDialogComponent", "ProxyItemDialogComponent", "ProxyManagementComponent", "PROXY_MANAGEMENT_MODULES", "ProxyManagementModule", "<PERSON><PERSON><PERSON><PERSON>", "path", "pathMatch", "component", "data", "title", "declarations", "imports", "i1", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/proxy-management/proxy-management.module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { FlexLayoutModule } from '@angular/flex-layout';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { RouterModule } from '@angular/router';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiGridModule, SwuiPagePanelModule } from '@skywind-group/lib-swui';\nimport { TrimInputValueModule } from '../../common/directives/trim-input-value/trim-input-value.module';\nimport { ProxyConfiramtionDialogComponent } from './proxy-confiramtion-dialog/proxy-confiramtion-dialog.component';\nimport { ProxyItemDialogComponent } from './proxy-item-dialog/proxy-item-dialog.component';\n\nimport { ProxyManagementComponent } from './proxy-management.component';\n\n\nexport const PROXY_MANAGEMENT_MODULES = [\n  MatDialogModule,\n  MatInputModule,\n  MatFormFieldModule,\n  MatButtonModule,\n  FlexLayoutModule,\n  ReactiveFormsModule,\n  SwuiPagePanelModule,\n  TranslateModule,\n];\n\n@NgModule({\n  declarations: [\n    ProxyManagementComponent,\n    ProxyItemDialogComponent,\n    ProxyConfiramtionDialogComponent\n  ],\n  exports: [ProxyManagementComponent],\n    imports: [\n        CommonModule,\n        ...PROXY_MANAGEMENT_MODULES,\n        SwuiGridModule,\n        RouterModule.forChild([\n            {\n                path: '',\n                pathMatch: 'full',\n                component: ProxyManagementComponent,\n                data: {\n                    title: 'Proxy'\n                }\n            }\n        ]),\n        TrimInputValueModule\n    ],\n  providers: []\n})\nexport class ProxyManagementModule {\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,cAAc,EAAEC,mBAAmB,QAAQ,yBAAyB;AAC7E,SAASC,oBAAoB,QAAQ,kEAAkE;AACvG,SAASC,gCAAgC,QAAQ,iEAAiE;AAClH,SAASC,wBAAwB,QAAQ,iDAAiD;AAE1F,SAASC,wBAAwB,QAAQ,8BAA8B;;;AAGvE,OAAO,MAAMC,wBAAwB,GAAG,CACtCX,eAAe,EACfE,cAAc,EACdD,kBAAkB,EAClBF,eAAe,EACfF,gBAAgB,EAChBC,mBAAmB,EACnBQ,mBAAmB,EACnBF,eAAe,CAChB;AA2BD,OAAM,MAAOQ,qBAAqB;;;uCAArBA,qBAAqB;IAAA;EAAA;;;YAArBA;IAAqB;EAAA;;;gBAjB1BhB,YAAY,EACTe,wBAAwB,EAC3BN,cAAc,EACdF,YAAY,CAACU,QAAQ,CAAC,CAClB;QACIC,IAAI,EAAE,EAAE;QACRC,SAAS,EAAE,MAAM;QACjBC,SAAS,EAAEN,wBAAwB;QACnCO,IAAI,EAAE;UACFC,KAAK,EAAE;;OAEd,CACJ,CAAC,EACFX,oBAAoB;IAAA;EAAA;;;2EAIfK,qBAAqB;IAAAO,YAAA,GAvB9BT,wBAAwB,EACxBD,wBAAwB,EACxBD,gCAAgC;IAAAY,OAAA,GAI5BxB,YAAY,EAlBlBI,eAAe,EACfE,cAAc,EACdD,kBAAkB,EAClBF,eAAe,EACfF,gBAAgB,EAChBC,mBAAmB,EACnBQ,mBAAmB,EACnBF,eAAe,EAaTC,cAAc,EAAAgB,EAAA,CAAAlB,YAAA,EAWdI,oBAAoB;IAAAe,OAAA,GAfhBZ,wBAAwB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}