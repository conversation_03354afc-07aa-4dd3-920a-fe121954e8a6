{"ast": null, "code": "// Production steps of ECMA-262, Edition 6, 22.1.2.1\n// Reference: http://www.ecma-international.org/ecma-262/6.0/#sec-array.from\nmodule.exports = function () {\n  var isCallable = function (fn) {\n    return typeof fn === 'function';\n  };\n  var toInteger = function (value) {\n    var number = Number(value);\n    if (isNaN(number)) {\n      return 0;\n    }\n    if (number === 0 || !isFinite(number)) {\n      return number;\n    }\n    return (number > 0 ? 1 : -1) * Math.floor(Math.abs(number));\n  };\n  var maxSafeInteger = Math.pow(2, 53) - 1;\n  var toLength = function (value) {\n    var len = toInteger(value);\n    return Math.min(Math.max(len, 0), maxSafeInteger);\n  };\n  var iteratorProp = function (value) {\n    if (value != null) {\n      if (['string', 'number', 'boolean', 'symbol'].indexOf(typeof value) > -1) {\n        return Symbol.iterator;\n      } else if (typeof Symbol !== 'undefined' && 'iterator' in Symbol && Symbol.iterator in value) {\n        return Symbol.iterator;\n      }\n      // Support \"@@iterator\" placeholder, Gecko 27 to Gecko 35\n      else if ('@@iterator' in value) {\n        return '@@iterator';\n      }\n    }\n  };\n  var getMethod = function (O, P) {\n    // Assert: IsPropertyKey(P) is true.\n    if (O != null && P != null) {\n      // Let func be GetV(O, P).\n      var func = O[P];\n      // ReturnIfAbrupt(func).\n      // If func is either undefined or null, return undefined.\n      if (func == null) {\n        return void 0;\n      }\n      // If IsCallable(func) is false, throw a TypeError exception.\n      if (!isCallable(func)) {\n        throw new TypeError(func + ' is not a function');\n      }\n      return func;\n    }\n  };\n  var iteratorStep = function (iterator) {\n    // Let result be IteratorNext(iterator).\n    // ReturnIfAbrupt(result).\n    var result = iterator.next();\n    // Let done be IteratorComplete(result).\n    // ReturnIfAbrupt(done).\n    var done = Boolean(result.done);\n    // If done is true, return false.\n    if (done) {\n      return false;\n    }\n    // Return result.\n    return result;\n  };\n\n  // The length property of the from method is 1.\n  return function from(items /*, mapFn, thisArg */) {\n    'use strict';\n\n    // 1. Let C be the this value.\n    var C = this;\n\n    // 2. If mapfn is undefined, let mapping be false.\n    var mapFn = arguments.length > 1 ? arguments[1] : void 0;\n    var T;\n    if (typeof mapFn !== 'undefined') {\n      // 3. else\n      //   a. If IsCallable(mapfn) is false, throw a TypeError exception.\n      if (!isCallable(mapFn)) {\n        throw new TypeError('Array.from: when provided, the second argument must be a function');\n      }\n\n      //   b. If thisArg was supplied, let T be thisArg; else let T\n      //      be undefined.\n      if (arguments.length > 2) {\n        T = arguments[2];\n      }\n      //   c. Let mapping be true (implied by mapFn)\n    }\n    var A, k;\n\n    // 4. Let usingIterator be GetMethod(items, @@iterator).\n    // 5. ReturnIfAbrupt(usingIterator).\n    var usingIterator = getMethod(items, iteratorProp(items));\n\n    // 6. If usingIterator is not undefined, then\n    if (usingIterator !== void 0) {\n      // a. If IsConstructor(C) is true, then\n      //   i. Let A be the result of calling the [[Construct]]\n      //      internal method of C with an empty argument list.\n      // b. Else,\n      //   i. Let A be the result of the abstract operation ArrayCreate\n      //      with argument 0.\n      // c. ReturnIfAbrupt(A).\n      A = isCallable(C) ? Object(new C()) : [];\n\n      // d. Let iterator be GetIterator(items, usingIterator).\n      var iterator = usingIterator.call(items);\n\n      // e. ReturnIfAbrupt(iterator).\n      if (iterator == null) {\n        throw new TypeError('Array.from requires an array-like or iterable object');\n      }\n\n      // f. Let k be 0.\n      k = 0;\n\n      // g. Repeat\n      var next, nextValue;\n      while (true) {\n        // i. Let Pk be ToString(k).\n        // ii. Let next be IteratorStep(iterator).\n        // iii. ReturnIfAbrupt(next).\n        next = iteratorStep(iterator);\n\n        // iv. If next is false, then\n        if (!next) {\n          // 1. Let setStatus be Set(A, \"length\", k, true).\n          // 2. ReturnIfAbrupt(setStatus).\n          A.length = k;\n\n          // 3. Return A.\n          return A;\n        }\n        // v. Let nextValue be IteratorValue(next).\n        // vi. ReturnIfAbrupt(nextValue)\n        nextValue = next.value;\n\n        // vii. If mapping is true, then\n        //   1. Let mappedValue be Call(mapfn, T, «nextValue, k»).\n        //   2. If mappedValue is an abrupt completion, return\n        //      IteratorClose(iterator, mappedValue).\n        //   3. Let mappedValue be mappedValue.[[value]].\n        // viii. Else, let mappedValue be nextValue.\n        // ix.  Let defineStatus be the result of\n        //      CreateDataPropertyOrThrow(A, Pk, mappedValue).\n        // x. [TODO] If defineStatus is an abrupt completion, return\n        //    IteratorClose(iterator, defineStatus).\n        if (mapFn) {\n          A[k] = mapFn.call(T, nextValue, k);\n        } else {\n          A[k] = nextValue;\n        }\n        // xi. Increase k by 1.\n        k++;\n      }\n      // 7. Assert: items is not an Iterable so assume it is\n      //    an array-like object.\n    } else {\n      // 8. Let arrayLike be ToObject(items).\n      var arrayLike = Object(items);\n\n      // 9. ReturnIfAbrupt(items).\n      if (items == null) {\n        throw new TypeError('Array.from requires an array-like object - not null or undefined');\n      }\n\n      // 10. Let len be ToLength(Get(arrayLike, \"length\")).\n      // 11. ReturnIfAbrupt(len).\n      var len = toLength(arrayLike.length);\n\n      // 12. If IsConstructor(C) is true, then\n      //     a. Let A be Construct(C, «len»).\n      // 13. Else\n      //     a. Let A be ArrayCreate(len).\n      // 14. ReturnIfAbrupt(A).\n      A = isCallable(C) ? Object(new C(len)) : new Array(len);\n\n      // 15. Let k be 0.\n      k = 0;\n      // 16. Repeat, while k < len… (also steps a - h)\n      var kValue;\n      while (k < len) {\n        kValue = arrayLike[k];\n        if (mapFn) {\n          A[k] = mapFn.call(T, kValue, k);\n        } else {\n          A[k] = kValue;\n        }\n        k++;\n      }\n      // 17. Let setStatus be Set(A, \"length\", len, true).\n      // 18. ReturnIfAbrupt(setStatus).\n      A.length = len;\n      // 19. Return A.\n    }\n    return A;\n  };\n}();", "map": {"version": 3, "names": ["module", "exports", "isCallable", "fn", "toInteger", "value", "number", "Number", "isNaN", "isFinite", "Math", "floor", "abs", "maxSafeInteger", "pow", "to<PERSON><PERSON><PERSON>", "len", "min", "max", "iteratorProp", "indexOf", "Symbol", "iterator", "getMethod", "O", "P", "func", "TypeError", "iteratorStep", "result", "next", "done", "Boolean", "from", "items", "C", "mapFn", "arguments", "length", "T", "A", "k", "usingIterator", "Object", "call", "nextValue", "arrayLike", "Array", "kValue"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/node_modules/array-from/polyfill.js"], "sourcesContent": ["// Production steps of ECMA-262, Edition 6, 22.1.2.1\n// Reference: http://www.ecma-international.org/ecma-262/6.0/#sec-array.from\nmodule.exports = (function() {\n  var isCallable = function(fn) {\n    return typeof fn === 'function';\n  };\n  var toInteger = function (value) {\n    var number = Number(value);\n    if (isNaN(number)) { return 0; }\n    if (number === 0 || !isFinite(number)) { return number; }\n    return (number > 0 ? 1 : -1) * Math.floor(Math.abs(number));\n  };\n  var maxSafeInteger = Math.pow(2, 53) - 1;\n  var toLength = function (value) {\n    var len = toInteger(value);\n    return Math.min(Math.max(len, 0), maxSafeInteger);\n  };\n  var iteratorProp = function(value) {\n    if(value != null) {\n      if(['string','number','boolean','symbol'].indexOf(typeof value) > -1){\n        return Symbol.iterator;\n      } else if (\n        (typeof Symbol !== 'undefined') &&\n        ('iterator' in Symbol) &&\n        (Symbol.iterator in value)\n      ) {\n        return Symbol.iterator;\n      }\n      // Support \"@@iterator\" placeholder, Gecko 27 to Gecko 35\n      else if ('@@iterator' in value) {\n        return '@@iterator';\n      }\n    }\n  };\n  var getMethod = function(O, P) {\n    // Assert: IsPropertyKey(P) is true.\n    if (O != null && P != null) {\n      // Let func be GetV(O, P).\n      var func = O[P];\n      // ReturnIfAbrupt(func).\n      // If func is either undefined or null, return undefined.\n      if(func == null) {\n        return void 0;\n      }\n      // If IsCallable(func) is false, throw a TypeError exception.\n      if (!isCallable(func)) {\n        throw new TypeError(func + ' is not a function');\n      }\n      return func;\n    }\n  };\n  var iteratorStep = function(iterator) {\n    // Let result be IteratorNext(iterator).\n    // ReturnIfAbrupt(result).\n    var result = iterator.next();\n    // Let done be IteratorComplete(result).\n    // ReturnIfAbrupt(done).\n    var done = Boolean(result.done);\n    // If done is true, return false.\n    if(done) {\n      return false;\n    }\n    // Return result.\n    return result;\n  };\n\n  // The length property of the from method is 1.\n  return function from(items /*, mapFn, thisArg */ ) {\n    'use strict';\n\n    // 1. Let C be the this value.\n    var C = this;\n\n    // 2. If mapfn is undefined, let mapping be false.\n    var mapFn = arguments.length > 1 ? arguments[1] : void 0;\n\n    var T;\n    if (typeof mapFn !== 'undefined') {\n      // 3. else\n      //   a. If IsCallable(mapfn) is false, throw a TypeError exception.\n      if (!isCallable(mapFn)) {\n        throw new TypeError(\n          'Array.from: when provided, the second argument must be a function'\n        );\n      }\n\n      //   b. If thisArg was supplied, let T be thisArg; else let T\n      //      be undefined.\n      if (arguments.length > 2) {\n        T = arguments[2];\n      }\n      //   c. Let mapping be true (implied by mapFn)\n    }\n\n    var A, k;\n\n    // 4. Let usingIterator be GetMethod(items, @@iterator).\n    // 5. ReturnIfAbrupt(usingIterator).\n    var usingIterator = getMethod(items, iteratorProp(items));\n\n    // 6. If usingIterator is not undefined, then\n    if (usingIterator !== void 0) {\n      // a. If IsConstructor(C) is true, then\n      //   i. Let A be the result of calling the [[Construct]]\n      //      internal method of C with an empty argument list.\n      // b. Else,\n      //   i. Let A be the result of the abstract operation ArrayCreate\n      //      with argument 0.\n      // c. ReturnIfAbrupt(A).\n      A = isCallable(C) ? Object(new C()) : [];\n\n      // d. Let iterator be GetIterator(items, usingIterator).\n      var iterator = usingIterator.call(items);\n\n      // e. ReturnIfAbrupt(iterator).\n      if (iterator == null) {\n        throw new TypeError(\n          'Array.from requires an array-like or iterable object'\n        );\n      }\n\n      // f. Let k be 0.\n      k = 0;\n\n      // g. Repeat\n      var next, nextValue;\n      while (true) {\n        // i. Let Pk be ToString(k).\n        // ii. Let next be IteratorStep(iterator).\n        // iii. ReturnIfAbrupt(next).\n        next = iteratorStep(iterator);\n\n        // iv. If next is false, then\n        if (!next) {\n\n          // 1. Let setStatus be Set(A, \"length\", k, true).\n          // 2. ReturnIfAbrupt(setStatus).\n          A.length = k;\n\n          // 3. Return A.\n          return A;\n        }\n        // v. Let nextValue be IteratorValue(next).\n        // vi. ReturnIfAbrupt(nextValue)\n        nextValue = next.value;\n\n        // vii. If mapping is true, then\n        //   1. Let mappedValue be Call(mapfn, T, «nextValue, k»).\n        //   2. If mappedValue is an abrupt completion, return\n        //      IteratorClose(iterator, mappedValue).\n        //   3. Let mappedValue be mappedValue.[[value]].\n        // viii. Else, let mappedValue be nextValue.\n        // ix.  Let defineStatus be the result of\n        //      CreateDataPropertyOrThrow(A, Pk, mappedValue).\n        // x. [TODO] If defineStatus is an abrupt completion, return\n        //    IteratorClose(iterator, defineStatus).\n        if (mapFn) {\n          A[k] = mapFn.call(T, nextValue, k);\n        }\n        else {\n          A[k] = nextValue;\n        }\n        // xi. Increase k by 1.\n        k++;\n      }\n      // 7. Assert: items is not an Iterable so assume it is\n      //    an array-like object.\n    } else {\n\n      // 8. Let arrayLike be ToObject(items).\n      var arrayLike = Object(items);\n\n      // 9. ReturnIfAbrupt(items).\n      if (items == null) {\n        throw new TypeError(\n          'Array.from requires an array-like object - not null or undefined'\n        );\n      }\n\n      // 10. Let len be ToLength(Get(arrayLike, \"length\")).\n      // 11. ReturnIfAbrupt(len).\n      var len = toLength(arrayLike.length);\n\n      // 12. If IsConstructor(C) is true, then\n      //     a. Let A be Construct(C, «len»).\n      // 13. Else\n      //     a. Let A be ArrayCreate(len).\n      // 14. ReturnIfAbrupt(A).\n      A = isCallable(C) ? Object(new C(len)) : new Array(len);\n\n      // 15. Let k be 0.\n      k = 0;\n      // 16. Repeat, while k < len… (also steps a - h)\n      var kValue;\n      while (k < len) {\n        kValue = arrayLike[k];\n        if (mapFn) {\n          A[k] = mapFn.call(T, kValue, k);\n        }\n        else {\n          A[k] = kValue;\n        }\n        k++;\n      }\n      // 17. Let setStatus be Set(A, \"length\", len, true).\n      // 18. ReturnIfAbrupt(setStatus).\n      A.length = len;\n      // 19. Return A.\n    }\n    return A;\n  };\n})();\n"], "mappings": "AAAA;AACA;AACAA,MAAM,CAACC,OAAO,GAAI,YAAW;EAC3B,IAAIC,UAAU,GAAG,SAAAA,CAASC,EAAE,EAAE;IAC5B,OAAO,OAAOA,EAAE,KAAK,UAAU;EACjC,CAAC;EACD,IAAIC,SAAS,GAAG,SAAAA,CAAUC,KAAK,EAAE;IAC/B,IAAIC,MAAM,GAAGC,MAAM,CAACF,KAAK,CAAC;IAC1B,IAAIG,KAAK,CAACF,MAAM,CAAC,EAAE;MAAE,OAAO,CAAC;IAAE;IAC/B,IAAIA,MAAM,KAAK,CAAC,IAAI,CAACG,QAAQ,CAACH,MAAM,CAAC,EAAE;MAAE,OAAOA,MAAM;IAAE;IACxD,OAAO,CAACA,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAII,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACN,MAAM,CAAC,CAAC;EAC7D,CAAC;EACD,IAAIO,cAAc,GAAGH,IAAI,CAACI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC;EACxC,IAAIC,QAAQ,GAAG,SAAAA,CAAUV,KAAK,EAAE;IAC9B,IAAIW,GAAG,GAAGZ,SAAS,CAACC,KAAK,CAAC;IAC1B,OAAOK,IAAI,CAACO,GAAG,CAACP,IAAI,CAACQ,GAAG,CAACF,GAAG,EAAE,CAAC,CAAC,EAAEH,cAAc,CAAC;EACnD,CAAC;EACD,IAAIM,YAAY,GAAG,SAAAA,CAASd,KAAK,EAAE;IACjC,IAAGA,KAAK,IAAI,IAAI,EAAE;MAChB,IAAG,CAAC,QAAQ,EAAC,QAAQ,EAAC,SAAS,EAAC,QAAQ,CAAC,CAACe,OAAO,CAAC,OAAOf,KAAK,CAAC,GAAG,CAAC,CAAC,EAAC;QACnE,OAAOgB,MAAM,CAACC,QAAQ;MACxB,CAAC,MAAM,IACJ,OAAOD,MAAM,KAAK,WAAW,IAC7B,UAAU,IAAIA,MAAO,IACrBA,MAAM,CAACC,QAAQ,IAAIjB,KAAM,EAC1B;QACA,OAAOgB,MAAM,CAACC,QAAQ;MACxB;MACA;MAAA,KACK,IAAI,YAAY,IAAIjB,KAAK,EAAE;QAC9B,OAAO,YAAY;MACrB;IACF;EACF,CAAC;EACD,IAAIkB,SAAS,GAAG,SAAAA,CAASC,CAAC,EAAEC,CAAC,EAAE;IAC7B;IACA,IAAID,CAAC,IAAI,IAAI,IAAIC,CAAC,IAAI,IAAI,EAAE;MAC1B;MACA,IAAIC,IAAI,GAAGF,CAAC,CAACC,CAAC,CAAC;MACf;MACA;MACA,IAAGC,IAAI,IAAI,IAAI,EAAE;QACf,OAAO,KAAK,CAAC;MACf;MACA;MACA,IAAI,CAACxB,UAAU,CAACwB,IAAI,CAAC,EAAE;QACrB,MAAM,IAAIC,SAAS,CAACD,IAAI,GAAG,oBAAoB,CAAC;MAClD;MACA,OAAOA,IAAI;IACb;EACF,CAAC;EACD,IAAIE,YAAY,GAAG,SAAAA,CAASN,QAAQ,EAAE;IACpC;IACA;IACA,IAAIO,MAAM,GAAGP,QAAQ,CAACQ,IAAI,CAAC,CAAC;IAC5B;IACA;IACA,IAAIC,IAAI,GAAGC,OAAO,CAACH,MAAM,CAACE,IAAI,CAAC;IAC/B;IACA,IAAGA,IAAI,EAAE;MACP,OAAO,KAAK;IACd;IACA;IACA,OAAOF,MAAM;EACf,CAAC;;EAED;EACA,OAAO,SAASI,IAAIA,CAACC,KAAK,CAAC,uBAAwB;IACjD,YAAY;;IAEZ;IACA,IAAIC,CAAC,GAAG,IAAI;;IAEZ;IACA,IAAIC,KAAK,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;IAExD,IAAIE,CAAC;IACL,IAAI,OAAOH,KAAK,KAAK,WAAW,EAAE;MAChC;MACA;MACA,IAAI,CAAClC,UAAU,CAACkC,KAAK,CAAC,EAAE;QACtB,MAAM,IAAIT,SAAS,CACjB,mEACF,CAAC;MACH;;MAEA;MACA;MACA,IAAIU,SAAS,CAACC,MAAM,GAAG,CAAC,EAAE;QACxBC,CAAC,GAAGF,SAAS,CAAC,CAAC,CAAC;MAClB;MACA;IACF;IAEA,IAAIG,CAAC,EAAEC,CAAC;;IAER;IACA;IACA,IAAIC,aAAa,GAAGnB,SAAS,CAACW,KAAK,EAAEf,YAAY,CAACe,KAAK,CAAC,CAAC;;IAEzD;IACA,IAAIQ,aAAa,KAAK,KAAK,CAAC,EAAE;MAC5B;MACA;MACA;MACA;MACA;MACA;MACA;MACAF,CAAC,GAAGtC,UAAU,CAACiC,CAAC,CAAC,GAAGQ,MAAM,CAAC,IAAIR,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE;;MAExC;MACA,IAAIb,QAAQ,GAAGoB,aAAa,CAACE,IAAI,CAACV,KAAK,CAAC;;MAExC;MACA,IAAIZ,QAAQ,IAAI,IAAI,EAAE;QACpB,MAAM,IAAIK,SAAS,CACjB,sDACF,CAAC;MACH;;MAEA;MACAc,CAAC,GAAG,CAAC;;MAEL;MACA,IAAIX,IAAI,EAAEe,SAAS;MACnB,OAAO,IAAI,EAAE;QACX;QACA;QACA;QACAf,IAAI,GAAGF,YAAY,CAACN,QAAQ,CAAC;;QAE7B;QACA,IAAI,CAACQ,IAAI,EAAE;UAET;UACA;UACAU,CAAC,CAACF,MAAM,GAAGG,CAAC;;UAEZ;UACA,OAAOD,CAAC;QACV;QACA;QACA;QACAK,SAAS,GAAGf,IAAI,CAACzB,KAAK;;QAEtB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAI+B,KAAK,EAAE;UACTI,CAAC,CAACC,CAAC,CAAC,GAAGL,KAAK,CAACQ,IAAI,CAACL,CAAC,EAAEM,SAAS,EAAEJ,CAAC,CAAC;QACpC,CAAC,MACI;UACHD,CAAC,CAACC,CAAC,CAAC,GAAGI,SAAS;QAClB;QACA;QACAJ,CAAC,EAAE;MACL;MACA;MACA;IACF,CAAC,MAAM;MAEL;MACA,IAAIK,SAAS,GAAGH,MAAM,CAACT,KAAK,CAAC;;MAE7B;MACA,IAAIA,KAAK,IAAI,IAAI,EAAE;QACjB,MAAM,IAAIP,SAAS,CACjB,kEACF,CAAC;MACH;;MAEA;MACA;MACA,IAAIX,GAAG,GAAGD,QAAQ,CAAC+B,SAAS,CAACR,MAAM,CAAC;;MAEpC;MACA;MACA;MACA;MACA;MACAE,CAAC,GAAGtC,UAAU,CAACiC,CAAC,CAAC,GAAGQ,MAAM,CAAC,IAAIR,CAAC,CAACnB,GAAG,CAAC,CAAC,GAAG,IAAI+B,KAAK,CAAC/B,GAAG,CAAC;;MAEvD;MACAyB,CAAC,GAAG,CAAC;MACL;MACA,IAAIO,MAAM;MACV,OAAOP,CAAC,GAAGzB,GAAG,EAAE;QACdgC,MAAM,GAAGF,SAAS,CAACL,CAAC,CAAC;QACrB,IAAIL,KAAK,EAAE;UACTI,CAAC,CAACC,CAAC,CAAC,GAAGL,KAAK,CAACQ,IAAI,CAACL,CAAC,EAAES,MAAM,EAAEP,CAAC,CAAC;QACjC,CAAC,MACI;UACHD,CAAC,CAACC,CAAC,CAAC,GAAGO,MAAM;QACf;QACAP,CAAC,EAAE;MACL;MACA;MACA;MACAD,CAAC,CAACF,MAAM,GAAGtB,GAAG;MACd;IACF;IACA,OAAOwB,CAAC;EACV,CAAC;AACH,CAAC,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}