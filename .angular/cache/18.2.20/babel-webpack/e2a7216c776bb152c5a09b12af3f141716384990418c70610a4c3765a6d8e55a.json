{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { MatPaginator } from '@angular/material/paginator';\nimport { MatSort } from '@angular/material/sort';\nimport { MatTableDataSource } from '@angular/material/table';\nimport moment from 'moment';\nimport { Subject } from 'rxjs';\nimport { map, takeUntil } from 'rxjs/operators';\nimport { FormattedMoneyPipe } from '../../../../../common/pipes/formatted-money/formatted-money.pipe';\nimport { checkIfBalanceHidden } from '../base-history/base-history.component';\nimport { transformSpin } from '../../../../../common/core/currecy-transform';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@skywind-group/lib-swui\";\nimport * as i2 from \"../../../../../common/services/reports/gamehistory.spin.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/material/table\";\nimport * as i5 from \"@angular/material/sort\";\nimport * as i6 from \"@angular/material/form-field\";\nimport * as i7 from \"@angular/material/input\";\nimport * as i8 from \"@angular/material/button\";\nimport * as i9 from \"@angular/material/dialog\";\nimport * as i10 from \"@angular/flex-layout/flex\";\nimport * as i11 from \"@angular/material/paginator\";\nimport * as i12 from \"../../../../../common/directives/trim-input-value/trim-input-value.component\";\nimport * as i13 from \"./spin-list-actions/spin-list-actions.component\";\nimport * as i14 from \"@ngx-translate/core\";\nimport * as i15 from \"../../../../../common/pipes/formatted-money/formatted-money.pipe\";\nconst _c0 = () => [5, 10, 25, 100];\nfunction SpinListComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"mat-form-field\", 23)(2, \"mat-label\");\n    i0.ɵɵtext(3, \"Filter\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"input\", 24);\n    i0.ɵɵlistener(\"keyup\", function SpinListComponent_div_0_Template_input_keyup_4_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.applyFilter($event.target[\"value\"]));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 25);\n    i0.ɵɵelement(6, \"spin-list-actions\", 26);\n    i0.ɵɵelementStart(7, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function SpinListComponent_div_0_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.downloadCsv());\n    });\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"translate\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"path\", ctx_r1.path)(\"roundInfo\", ctx_r1.roundInfo);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(9, 3, \"COMPONENTS.GRID.RESULT_DOANLOAD_CSV\"), \" \");\n  }\n}\nfunction SpinListComponent_th_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, \"GAMEHISTORY.GAME.spinId\"));\n  }\n}\nfunction SpinListComponent_td_5_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const row_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵtextInterpolate(row_r3.roundId);\n  }\n}\nfunction SpinListComponent_td_5_a_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 31);\n    i0.ɵɵlistener(\"click\", function SpinListComponent_td_5_a_3_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const row_r3 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.handleSpinClick(row_r3.roundId, $event));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(row_r3.roundId);\n  }\n}\nfunction SpinListComponent_td_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 29);\n    i0.ɵɵtemplate(1, SpinListComponent_td_5_ng_template_1_Template, 1, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(3, SpinListComponent_td_5_a_3_Template, 2, 1, \"a\", 30);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r3 = ctx.$implicit;\n    const closedGameSpin_r5 = i0.ɵɵreference(2);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isSpinTypeAllow(row_r3.type))(\"ngIfElse\", closedGameSpin_r5);\n  }\n}\nfunction SpinListComponent_th_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, \"GAMEHISTORY.GAME.type\"));\n  }\n}\nfunction SpinListComponent_td_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r6 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(row_r6.type || \"-\");\n  }\n}\nfunction SpinListComponent_th_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, \"GAMEHISTORY.GAME.bet\"));\n  }\n}\nfunction SpinListComponent_td_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"formattedMoney\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r7 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind4(2, 1, row_r7.bet, 2, \" \", row_r7.currency));\n  }\n}\nfunction SpinListComponent_th_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 32);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, \"GAMEHISTORY.GAME.win\"));\n  }\n}\nfunction SpinListComponent_td_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"formattedMoney\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r8 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind4(2, 1, row_r8.credit || row_r8.win, 2, \" \", row_r8.currency));\n  }\n}\nfunction SpinListComponent_th_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, \"PROMO.EDIT.QUALIFYING_GAMES.freebetCoinValue\"));\n  }\n}\nfunction SpinListComponent_td_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r9 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(row_r9.freeBetCoin || \"-\");\n  }\n}\nfunction SpinListComponent_th_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, \"GAMEHISTORY.GAME.date\"));\n  }\n}\nfunction SpinListComponent_td_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r10 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(row_r10.ts);\n  }\n}\nfunction SpinListComponent_th_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, \"GAMEHISTORY.GAME.balanceBefore\"));\n  }\n}\nfunction SpinListComponent_td_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r11 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.formattingBalanceBefore(row_r11.balanceBefore, row_r11.currency));\n  }\n}\nfunction SpinListComponent_th_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, \"GAMEHISTORY.GAME.balanceAfter\"));\n  }\n}\nfunction SpinListComponent_td_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r12 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.formattingBalanceBefore(row_r12.balanceAfter, row_r12.currency));\n  }\n}\nfunction SpinListComponent_th_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, \"GAMEHISTORY.GRID.totalJpWin\"));\n  }\n}\nfunction SpinListComponent_td_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"formattedMoney\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r13 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind4(2, 1, row_r13.totalJpWin, 2, \" \", row_r13.currency));\n  }\n}\nfunction SpinListComponent_th_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, \"GAMEHISTORY.GRID.aamsSessionCode\"));\n  }\n}\nfunction SpinListComponent_td_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r14 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(row_r14 == null ? null : row_r14.extraData == null ? null : row_r14.extraData.regulatoryData == null ? null : row_r14.extraData.regulatoryData.aamsSessionCode);\n  }\n}\nfunction SpinListComponent_th_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, \"GAMEHISTORY.GRID.ropCode\"));\n  }\n}\nfunction SpinListComponent_td_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r15 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(row_r15 == null ? null : row_r15.extraData == null ? null : row_r15.extraData.regulatoryData == null ? null : row_r15.extraData.regulatoryData.ropCode);\n  }\n}\nfunction SpinListComponent_th_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, \"GAMEHISTORY.GRID.participationStartDate\"));\n  }\n}\nfunction SpinListComponent_td_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r16 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(row_r16 == null ? null : row_r16.extraData == null ? null : row_r16.extraData.regulatoryData == null ? null : row_r16.extraData.regulatoryData.participationStartDate);\n  }\n}\nfunction SpinListComponent_tr_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 33);\n  }\n}\nfunction SpinListComponent_tr_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 34);\n  }\n}\nexport class SpinListComponent {\n  constructor(authService, spinService) {\n    this.spinService = spinService;\n    this.path = '';\n    this.onShowSpin = new EventEmitter();\n    this.showSmResult = new EventEmitter();\n    this.spins = [];\n    this.pureSpins = [];\n    this.hasFreeBet = false;\n    this.destroyed$ = new Subject();\n    this.formattedMoneyPipe = new FormattedMoneyPipe();\n    this.balanceHidden = checkIfBalanceHidden(authService);\n    this.displayedColumns = this.buildDisplayedColumns();\n    this.spinService.items.pipe(map(data => {\n      if (Array.isArray(data) && data.length === 2) {\n        if (['noMoreBets', 'rushBet'].indexOf(data[0].type) !== -1) {\n          data[1].bet = data[0].bet;\n          data[1].balanceBefore = data[0].balanceBefore;\n          data.shift();\n        }\n      }\n      return data.map(cur => ({\n        ...cur,\n        roundId: this.roundInfo.roundId + '#' + cur.spinNumber,\n        ts: moment(cur.ts).format('YYYY-MM-DD HH:mm:ss')\n      }));\n    }), takeUntil(this.destroyed$)).subscribe(data => {\n      this.pureSpins = data;\n      const spins = JSON.parse(JSON.stringify(data));\n      spins.forEach(spin => {\n        transformSpin(spin, spin.currency);\n      });\n      this.spins = spins;\n      this.displayedColumns = this.buildDisplayedColumns();\n      if (data.length === 1) {\n        this.handleSpinClick(data[0].roundId);\n      }\n      this.dataSource = new MatTableDataSource(this.spins);\n      this.dataSource.paginator = this.paginator;\n      if (this.spins.length > 1) {\n        this.showSmResult.emit(false);\n      }\n    });\n  }\n  ngAfterViewInit() {\n    this.sort?.sortChange.pipe(takeUntil(this.destroyed$)).subscribe(data => {\n      this.paginator.pageIndex = 0;\n      const {\n        active,\n        direction\n      } = data;\n      this.spins = this.sortTable(active, direction);\n      this.dataSource.data = this.spins;\n      this.dataSource.paginator = this.paginator;\n    });\n  }\n  ngOnInit() {\n    this.spinService.getList(this.roundInfo.roundId, {\n      values: {\n        path: this.path\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.destroyed$.next();\n    this.destroyed$.complete();\n  }\n  downloadCsv() {\n    this.spinService.downloadCSV(this.roundInfo, this.path, this.hasFreeBet).pipe(takeUntil(this.destroyed$)).subscribe();\n  }\n  formattingBalanceBefore(valueBalanceBefore, currency) {\n    return valueBalanceBefore || valueBalanceBefore === 0 ? this.formattedMoneyPipe.transform(valueBalanceBefore, 2, '', currency) : '-';\n  }\n  isSpinTypeAllow(type) {\n    return ['revert-game', 'force-finish', 'notification', 'noMoreBets', 'finalize', 'rushBet'].indexOf(type) === -1;\n  }\n  handleSpinClick(roundId, event) {\n    if (event) {\n      event.preventDefault();\n    }\n    const item = this.pureSpins.find(round => round.roundId === roundId);\n    const params = {\n      roundId: this.roundInfo.roundId,\n      spinNumber: item.spinNumber,\n      spin: item,\n      path: this.path\n    };\n    this.spinService.getItem(params);\n  }\n  applyFilter(filterValue) {\n    this.dataSource.filter = filterValue.trim().toLowerCase();\n    if (this.dataSource.paginator) {\n      this.dataSource.paginator.firstPage();\n    }\n  }\n  buildDisplayedColumns() {\n    const balanceVisible = !this.balanceHidden && !(this.roundInfo?._meta?.hideBalanceBeforeAndAfter ?? false);\n    const spins = this.spins;\n    const regulatoryData = spins.map(item => item.extraData?.regulatoryData);\n    const isBalanceBefore = balanceVisible && spins.some(item => item?.balanceBefore);\n    const isBalanceAfter = balanceVisible && spins.some(item => item?.balanceAfter);\n    this.hasFreeBet = spins.some(spin => spin?.type === 'freebet');\n    const hasAamsSessionCode = regulatoryData.some(item => item?.aamsSessionCode);\n    const hasRopCode = regulatoryData.some(item => item?.ropCode);\n    const hasParticipationStartDate = regulatoryData.some(item => item?.participationStartDate);\n    return ['spinId', 'type', 'bet', 'win', ...(this.hasFreeBet ? ['freeBetCoin'] : []), 'ts', ...(isBalanceBefore ? ['balanceBefore'] : []), ...(isBalanceAfter ? ['balanceAfter'] : []), 'totalJpWin', ...(hasAamsSessionCode ? ['aamsSessionCode'] : []), ...(hasRopCode ? ['ropCode'] : []), ...(hasParticipationStartDate ? ['participationStartDate'] : [])];\n  }\n  sortTable(active, direction) {\n    active = active === 'spinId' ? 'spinNumber' : active;\n    const isDate = active === 'ts';\n    let isNumber = false;\n    if (!isDate) {\n      const value = this.spins.map(spin => spin[active]).filter(item => item !== undefined).shift();\n      isNumber = typeof value === 'number';\n    }\n    return this.spins.sort((first, second) => {\n      if (isDate) {\n        if (direction === 'asc') {\n          return moment(first.ts).unix() - moment(second.ts).unix();\n        }\n        if (direction === 'desc') {\n          return moment(second.ts).unix() - moment(first.ts).unix();\n        }\n      }\n      if (isNumber) {\n        if (direction === 'asc') {\n          return first[active] - second[active];\n        }\n        if (direction === 'desc') {\n          return second[active] - first[active];\n        }\n      }\n      if (!!direction) {\n        return direction === 'asc' ? first[active]?.localeCompare(second[active]) : second[active]?.localeCompare(first[active]);\n      }\n    });\n  }\n  static {\n    this.ɵfac = function SpinListComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SpinListComponent)(i0.ɵɵdirectiveInject(i1.SwHubAuthService), i0.ɵɵdirectiveInject(i2.GameHistorySpinService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SpinListComponent,\n      selectors: [[\"spin-list\"]],\n      viewQuery: function SpinListComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(MatPaginator, 5);\n          i0.ɵɵviewQuery(MatSort, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.paginator = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.sort = _t.first);\n        }\n      },\n      inputs: {\n        roundInfo: \"roundInfo\",\n        path: \"path\"\n      },\n      outputs: {\n        onShowSpin: \"onShowSpin\",\n        showSmResult: \"showSmResult\"\n      },\n      decls: 42,\n      vars: 6,\n      consts: [[\"closedGameSpin\", \"\"], [\"class\", \"margin-bottom12\", \"mat-dialog-actions\", \"\", \"fxLayout\", \"row\", 4, \"ngIf\"], [1, \"mat-elevation-z0\", \"width100\"], [\"mat-table\", \"\", \"matSort\", \"\", 3, \"dataSource\"], [\"matColumnDef\", \"spinId\"], [\"mat-header-cell\", \"\", \"mat-sort-header\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"type\"], [\"matColumnDef\", \"bet\"], [\"matColumnDef\", \"win\"], [\"mat-header-cell\", \"\", \"mat-sort-header\", \"\", \"disableClear\", \"\", 4, \"matHeaderCellDef\"], [\"matColumnDef\", \"freeBetCoin\"], [\"matColumnDef\", \"ts\"], [\"matColumnDef\", \"balanceBefore\"], [\"matColumnDef\", \"balanceAfter\"], [\"matColumnDef\", \"totalJpWin\"], [\"matColumnDef\", \"aamsSessionCode\"], [\"matColumnDef\", \"ropCode\"], [\"matColumnDef\", \"participationStartDate\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\"], [\"mat-row\", \"\", 4, \"matRowDef\", \"matRowDefColumns\"], [3, \"pageSizeOptions\"], [\"mat-dialog-actions\", \"\", \"fxLayout\", \"row\", 1, \"margin-bottom12\"], [\"appearance\", \"outline\", 1, \"width40\"], [\"matInput\", \"\", \"trimValue\", \"\", 3, \"keyup\"], [\"fxLayout\", \"row\", \"fxFlex\", \"\", \"fxLayoutAlign\", \"end center\", 1, \"margin-bottom12\"], [3, \"path\", \"roundInfo\"], [\"color\", \"primary\", \"mat-flat-button\", \"\", 1, \"ml-10\", 3, \"click\"], [\"mat-header-cell\", \"\", \"mat-sort-header\", \"\"], [\"mat-cell\", \"\"], [3, \"click\", 4, \"ngIf\", \"ngIfElse\"], [3, \"click\"], [\"mat-header-cell\", \"\", \"mat-sort-header\", \"\", \"disableClear\", \"\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\"]],\n      template: function SpinListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, SpinListComponent_div_0_Template, 10, 5, \"div\", 1);\n          i0.ɵɵelementStart(1, \"div\", 2)(2, \"table\", 3);\n          i0.ɵɵelementContainerStart(3, 4);\n          i0.ɵɵtemplate(4, SpinListComponent_th_4_Template, 3, 3, \"th\", 5)(5, SpinListComponent_td_5_Template, 4, 2, \"td\", 6);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(6, 7);\n          i0.ɵɵtemplate(7, SpinListComponent_th_7_Template, 3, 3, \"th\", 5)(8, SpinListComponent_td_8_Template, 2, 1, \"td\", 6);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(9, 8);\n          i0.ɵɵtemplate(10, SpinListComponent_th_10_Template, 3, 3, \"th\", 5)(11, SpinListComponent_td_11_Template, 3, 6, \"td\", 6);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(12, 9);\n          i0.ɵɵtemplate(13, SpinListComponent_th_13_Template, 3, 3, \"th\", 10)(14, SpinListComponent_td_14_Template, 3, 6, \"td\", 6);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(15, 11);\n          i0.ɵɵtemplate(16, SpinListComponent_th_16_Template, 3, 3, \"th\", 5)(17, SpinListComponent_td_17_Template, 2, 1, \"td\", 6);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(18, 12);\n          i0.ɵɵtemplate(19, SpinListComponent_th_19_Template, 3, 3, \"th\", 5)(20, SpinListComponent_td_20_Template, 2, 1, \"td\", 6);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(21, 13);\n          i0.ɵɵtemplate(22, SpinListComponent_th_22_Template, 3, 3, \"th\", 5)(23, SpinListComponent_td_23_Template, 2, 1, \"td\", 6);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(24, 14);\n          i0.ɵɵtemplate(25, SpinListComponent_th_25_Template, 3, 3, \"th\", 5)(26, SpinListComponent_td_26_Template, 2, 1, \"td\", 6);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(27, 15);\n          i0.ɵɵtemplate(28, SpinListComponent_th_28_Template, 3, 3, \"th\", 5)(29, SpinListComponent_td_29_Template, 3, 6, \"td\", 6);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(30, 16);\n          i0.ɵɵtemplate(31, SpinListComponent_th_31_Template, 3, 3, \"th\", 5)(32, SpinListComponent_td_32_Template, 2, 1, \"td\", 6);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(33, 17);\n          i0.ɵɵtemplate(34, SpinListComponent_th_34_Template, 3, 3, \"th\", 5)(35, SpinListComponent_td_35_Template, 2, 1, \"td\", 6);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(36, 18);\n          i0.ɵɵtemplate(37, SpinListComponent_th_37_Template, 3, 3, \"th\", 5)(38, SpinListComponent_td_38_Template, 2, 1, \"td\", 6);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵtemplate(39, SpinListComponent_tr_39_Template, 1, 0, \"tr\", 19)(40, SpinListComponent_tr_40_Template, 1, 0, \"tr\", 20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(41, \"mat-paginator\", 21);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", (ctx.spins == null ? null : ctx.spins.length) > 1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"dataSource\", ctx.dataSource);\n          i0.ɵɵadvance(37);\n          i0.ɵɵproperty(\"matHeaderRowDef\", ctx.displayedColumns);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"matRowDefColumns\", ctx.displayedColumns);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"pageSizeOptions\", i0.ɵɵpureFunction0(5, _c0));\n        }\n      },\n      dependencies: [i3.NgIf, i4.MatTable, i4.MatHeaderCellDef, i4.MatHeaderRowDef, i4.MatColumnDef, i4.MatCellDef, i4.MatRowDef, i4.MatHeaderCell, i4.MatCell, i4.MatHeaderRow, i4.MatRow, i5.MatSort, i5.MatSortHeader, i6.MatFormField, i6.MatLabel, i7.MatInput, i8.MatButton, i9.MatDialogActions, i10.DefaultLayoutDirective, i10.DefaultLayoutAlignDirective, i10.DefaultFlexDirective, i11.MatPaginator, i12.TrimInputValueComponent, i13.SpinListActionsComponent, i14.TranslatePipe, i15.FormattedMoneyPipe],\n      styles: [\"table.mat-table[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\nmat-paginator[_ngcontent-%COMP%] {\\n  justify-content: flex-start;\\n  display: grid;\\n}\\n\\n.mat-row[_ngcontent-%COMP%]:nth-child(2n+1) {\\n  background-color: #f9f9fa;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvZ2FtZWhpc3RvcnkvY29tcG9uZW50cy9pbnRlcm5hbC1nYW1lLWhpc3Rvcnkvc3Bpbi1saXN0L3NwaW4tbGlzdC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLFdBQUE7QUFDRjs7QUFFQTtFQUNFLDJCQUFBO0VBQ0EsYUFBQTtBQUNGOztBQUVBO0VBQ0UseUJBQUE7QUFDRiIsInNvdXJjZXNDb250ZW50IjpbInRhYmxlLm1hdC10YWJsZSB7XG4gIHdpZHRoOiAxMDAlO1xufVxuXG5tYXQtcGFnaW5hdG9yIHtcbiAganVzdGlmeS1jb250ZW50OiBmbGV4LXN0YXJ0O1xuICBkaXNwbGF5OiBncmlkO1xufVxuXG4ubWF0LXJvdzpudGgtY2hpbGQoMm4rMSl7XG4gIGJhY2tncm91bmQtY29sb3I6ICNmOWY5ZmE7XG59XG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "MatPaginator", "MatSort", "MatTableDataSource", "moment", "Subject", "map", "takeUntil", "FormattedMoneyPipe", "checkIfBalanceHidden", "transformSpin", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "SpinListComponent_div_0_Template_input_keyup_4_listener", "$event", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "applyFilter", "target", "ɵɵelement", "SpinListComponent_div_0_Template_button_click_7_listener", "downloadCsv", "ɵɵadvance", "ɵɵproperty", "path", "roundInfo", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "ɵɵtextInterpolate", "row_r3", "roundId", "SpinListComponent_td_5_a_3_Template_a_click_0_listener", "_r4", "$implicit", "handleSpinClick", "ɵɵtemplate", "SpinListComponent_td_5_ng_template_1_Template", "ɵɵtemplateRefExtractor", "SpinListComponent_td_5_a_3_Template", "isSpinTypeAllow", "type", "closedGameSpin_r5", "row_r6", "ɵɵpipeBind4", "row_r7", "bet", "currency", "row_r8", "credit", "win", "row_r9", "freeBetCoin", "row_r10", "ts", "formattingBalanceBefore", "row_r11", "balanceBefore", "row_r12", "balanceAfter", "row_r13", "totalJpWin", "row_r14", "extraData", "regulatoryData", "aamsSessionCode", "row_r15", "ropCode", "row_r16", "participationStartDate", "SpinListComponent", "constructor", "authService", "spinService", "onShowSpin", "showSmResult", "spins", "pureSpins", "hasFreeBet", "destroyed$", "formattedMoneyPipe", "balanceHidden", "displayedColumns", "buildDisplayedColumns", "items", "pipe", "data", "Array", "isArray", "length", "indexOf", "shift", "cur", "spinNumber", "format", "subscribe", "JSON", "parse", "stringify", "for<PERSON>ach", "spin", "dataSource", "paginator", "emit", "ngAfterViewInit", "sort", "sortChange", "pageIndex", "active", "direction", "sortTable", "ngOnInit", "getList", "values", "ngOnDestroy", "next", "complete", "downloadCSV", "valueBalanceBefore", "transform", "event", "preventDefault", "item", "find", "round", "params", "getItem", "filterValue", "filter", "trim", "toLowerCase", "firstPage", "balanceVisible", "_meta", "hideBalanceBeforeAndAfter", "isBalanceBefore", "some", "isBalanceAfter", "hasAamsSessionCode", "hasRopCode", "hasParticipationStartDate", "isDate", "isNumber", "value", "undefined", "first", "second", "unix", "localeCompare", "ɵɵdirectiveInject", "i1", "SwHubAuthService", "i2", "GameHistorySpinService", "selectors", "viewQuery", "SpinListComponent_Query", "rf", "ctx", "SpinListComponent_div_0_Template", "ɵɵelementContainerStart", "SpinListComponent_th_4_Template", "SpinListComponent_td_5_Template", "SpinListComponent_th_7_Template", "SpinListComponent_td_8_Template", "SpinListComponent_th_10_Template", "SpinListComponent_td_11_Template", "SpinListComponent_th_13_Template", "SpinListComponent_td_14_Template", "SpinListComponent_th_16_Template", "SpinListComponent_td_17_Template", "SpinListComponent_th_19_Template", "SpinListComponent_td_20_Template", "SpinListComponent_th_22_Template", "SpinListComponent_td_23_Template", "SpinListComponent_th_25_Template", "SpinListComponent_td_26_Template", "SpinListComponent_th_28_Template", "SpinListComponent_td_29_Template", "SpinListComponent_th_31_Template", "SpinListComponent_td_32_Template", "SpinListComponent_th_34_Template", "SpinListComponent_td_35_Template", "SpinListComponent_th_37_Template", "SpinListComponent_td_38_Template", "SpinListComponent_tr_39_Template", "SpinListComponent_tr_40_Template", "ɵɵpureFunction0", "_c0"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/gamehistory/components/internal-game-history/spin-list/spin-list.component.ts", "/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/gamehistory/components/internal-game-history/spin-list/spin-list.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, Output, ViewChild } from '@angular/core';\nimport { MatPaginator } from '@angular/material/paginator';\nimport { MatSort, Sort } from '@angular/material/sort';\nimport { SortDirection } from '@angular/material/sort/sort-direction';\nimport { MatTableDataSource } from '@angular/material/table';\nimport { SwHubAuthService } from '@skywind-group/lib-swui';\nimport moment from 'moment';\nimport { Subject } from 'rxjs';\nimport { map, takeUntil } from 'rxjs/operators';\nimport { FormattedMoneyPipe } from '../../../../../common/pipes/formatted-money/formatted-money.pipe';\nimport { GameHistorySpinService } from '../../../../../common/services/reports/gamehistory.spin.service';\nimport { GameHistory, GameHistorySpin } from '../../../../../common/typings';\nimport { checkIfBalanceHidden } from '../base-history/base-history.component';\nimport { transformSpin } from '../../../../../common/core/currecy-transform';\n\n\n@Component({\n  selector: 'spin-list',\n  templateUrl: './spin-list.component.html',\n  styleUrls: ['./spin-list.component.scss'],\n})\nexport class SpinListComponent {\n  @Input() roundInfo: GameHistory;\n  @Input() path: string = '';\n  @Output() onShowSpin = new EventEmitter<any>();\n  @Output() showSmResult = new EventEmitter<boolean>();\n\n  @ViewChild(MatPaginator) paginator: MatPaginator;\n  @ViewChild(MatSort) sort: MatSort;\n\n  displayedColumns: string[];\n  dataSource?: MatTableDataSource<GameHistorySpin>;\n  spins: GameHistorySpin[] = [];\n  pureSpins: GameHistorySpin[] = [];\n  hasFreeBet = false;\n\n  readonly balanceHidden: boolean;\n\n  private readonly destroyed$ = new Subject<void>();\n  private readonly formattedMoneyPipe = new FormattedMoneyPipe();\n\n  constructor( authService: SwHubAuthService, private readonly spinService: GameHistorySpinService<GameHistorySpin> ) {\n    this.balanceHidden = checkIfBalanceHidden(authService);\n    this.displayedColumns = this.buildDisplayedColumns();\n\n    this.spinService.items.pipe(\n      map<GameHistorySpin[], GameHistorySpin[]>(data => {\n        if (Array.isArray(data) && data.length === 2) {\n          if (['noMoreBets', 'rushBet'].indexOf(data[0].type) !== -1) {\n            data[1].bet = data[0].bet;\n            data[1].balanceBefore = data[0].balanceBefore;\n            data.shift();\n          }\n        }\n        return data.map(cur => ({\n          ...cur,\n          roundId: this.roundInfo.roundId + '#' + cur.spinNumber,\n          ts: moment(cur.ts).format('YYYY-MM-DD HH:mm:ss')\n        }));\n      }),\n      takeUntil(this.destroyed$)\n    ).subscribe(data => {\n        this.pureSpins = data;\n        const spins = JSON.parse(JSON.stringify(data));\n        spins.forEach(spin => {\n          transformSpin(spin, spin.currency);\n        });\n        this.spins = spins;\n        this.displayedColumns = this.buildDisplayedColumns();\n        if (data.length === 1) {\n          this.handleSpinClick(data[0].roundId);\n        }\n        this.dataSource = new MatTableDataSource(this.spins);\n        this.dataSource.paginator = this.paginator;\n        if (this.spins.length > 1) {\n          this.showSmResult.emit(false);\n        }\n      }\n    );\n  }\n\n  ngAfterViewInit() {\n    this.sort?.sortChange.pipe(\n      takeUntil(this.destroyed$),\n    ).subscribe(( data: Sort ) => {\n      this.paginator.pageIndex = 0;\n      const { active, direction } = data;\n      this.spins = this.sortTable(active, direction);\n      this.dataSource.data = this.spins;\n      this.dataSource.paginator = this.paginator;\n    });\n  }\n\n  ngOnInit() {\n    this.spinService.getList(this.roundInfo.roundId, {\n      values: {\n        path: this.path\n      }\n    });\n  }\n\n  ngOnDestroy() {\n    this.destroyed$.next();\n    this.destroyed$.complete();\n  }\n\n  downloadCsv() {\n    this.spinService.downloadCSV(this.roundInfo, this.path, this.hasFreeBet).pipe(\n      takeUntil(this.destroyed$)\n    ).subscribe();\n  }\n\n  formattingBalanceBefore( valueBalanceBefore: string | number, currency: string ): string {\n    return valueBalanceBefore || valueBalanceBefore === 0 ?\n      this.formattedMoneyPipe.transform(valueBalanceBefore, 2, '', currency) : '-';\n  }\n\n  isSpinTypeAllow( type: string ): boolean {\n    return ['revert-game', 'force-finish', 'notification', 'noMoreBets', 'finalize', 'rushBet'].indexOf(type) === -1;\n  }\n\n  handleSpinClick( roundId, event? ) {\n    if (event) {\n      event.preventDefault();\n    }\n\n    const item = this.pureSpins.find(round => round.roundId === roundId);\n\n    const params = {\n      roundId: this.roundInfo.roundId,\n      spinNumber: item.spinNumber,\n      spin: item,\n      path: this.path,\n    };\n    this.spinService.getItem(params);\n  }\n\n  applyFilter( filterValue: string ) {\n    this.dataSource.filter = filterValue.trim().toLowerCase();\n    if (this.dataSource.paginator) {\n      this.dataSource.paginator.firstPage();\n    }\n  }\n\n  private buildDisplayedColumns(): string[] {\n    const balanceVisible = !this.balanceHidden && !(this.roundInfo?._meta?.hideBalanceBeforeAndAfter ?? false);\n    const spins = this.spins;\n    const regulatoryData = spins.map(item => item.extraData?.regulatoryData);\n\n    const isBalanceBefore = balanceVisible && spins.some(item => item?.balanceBefore);\n    const isBalanceAfter = balanceVisible && spins.some(item => item?.balanceAfter);\n    this.hasFreeBet = spins.some(spin => spin?.type === 'freebet');\n    const hasAamsSessionCode = regulatoryData.some(item => item?.aamsSessionCode);\n    const hasRopCode = regulatoryData.some(item => item?.ropCode);\n    const hasParticipationStartDate = regulatoryData.some(item => item?.participationStartDate);\n\n    return [\n      'spinId',\n      'type',\n      'bet',\n      'win',\n      ...(this.hasFreeBet ? ['freeBetCoin'] : []),\n      'ts',\n      ...(isBalanceBefore ? ['balanceBefore'] : []),\n      ...(isBalanceAfter ? ['balanceAfter'] : []),\n      'totalJpWin',\n      ...(hasAamsSessionCode ? ['aamsSessionCode'] : []),\n      ...(hasRopCode ? ['ropCode'] : []),\n      ...(hasParticipationStartDate ? ['participationStartDate'] : [])\n    ];\n  }\n\n  private sortTable( active: string, direction: SortDirection ) {\n    active = active === 'spinId' ? 'spinNumber' : active;\n    const isDate = active === 'ts';\n    let isNumber = false;\n    if (!isDate) {\n      const value = this.spins.map(spin => spin[active]).filter(item => item !== undefined).shift();\n      isNumber = typeof value === 'number';\n    }\n    return this.spins.sort(( first: GameHistorySpin, second: GameHistorySpin ) => {\n      if (isDate) {\n        if (direction === 'asc') {\n          return moment(first.ts).unix() - moment(second.ts).unix();\n        }\n        if (direction === 'desc') {\n          return moment(second.ts).unix() - moment(first.ts).unix();\n        }\n      }\n      if (isNumber) {\n        if (direction === 'asc') {\n          return first[active] - second[active];\n        }\n        if (direction === 'desc') {\n          return second[active] - first[active];\n        }\n      }\n      if (!!direction) {\n        return direction === 'asc' ? first[active]?.localeCompare(second[active]) : second[active]?.localeCompare(first[active]);\n      }\n    });\n  }\n}\n", "<div\n  *ngIf=\"spins?.length > 1\"\n  class=\"margin-bottom12\"\n  mat-dialog-actions\n  fxLayout=\"row\">\n  <mat-form-field appearance=\"outline\" class=\"width40\">\n    <mat-label>Filter</mat-label>\n    <input matInput trimValue (keyup)=\"applyFilter($event.target['value'])\">\n  </mat-form-field>\n\n  <div fxLayout=\"row\" fxFlex fxLayoutAlign=\"end center\" class=\"margin-bottom12\">\n    <spin-list-actions [path]=\"path\" [roundInfo]=\"roundInfo\"></spin-list-actions>\n    <button\n      class=\"ml-10\"\n      color=\"primary\"\n      mat-flat-button\n      (click)=\"downloadCsv()\">\n      {{ 'COMPONENTS.GRID.RESULT_DOANLOAD_CSV' | translate }}\n    </button>\n  </div>\n</div>\n\n<div class=\"mat-elevation-z0 width100\">\n  <table mat-table matSort [dataSource]=\"dataSource\">\n\n    <ng-container matColumnDef=\"spinId\">\n      <th mat-header-cell mat-sort-header *matHeaderCellDef>{{ 'GAMEHISTORY.GAME.spinId' | translate }}</th>\n      <td mat-cell *matCellDef=\"let row\">\n        <ng-template #closedGameSpin>{{row.roundId}}</ng-template>\n        <a *ngIf=\"isSpinTypeAllow(row.type); else closedGameSpin\"\n           (click)=\"handleSpinClick(row.roundId, $event)\">{{row.roundId}}</a>\n      </td>\n    </ng-container>\n\n    <ng-container matColumnDef=\"type\">\n      <th mat-header-cell mat-sort-header *matHeaderCellDef>{{ 'GAMEHISTORY.GAME.type' | translate }}</th>\n      <td mat-cell *matCellDef=\"let row\">{{row.type || '-'}}</td>\n    </ng-container>\n\n    <ng-container matColumnDef=\"bet\">\n      <th mat-header-cell mat-sort-header *matHeaderCellDef>{{ 'GAMEHISTORY.GAME.bet' | translate }}</th>\n      <td mat-cell *matCellDef=\"let row\">{{row.bet | formattedMoney: 2: ' ': row.currency}}</td>\n    </ng-container>\n\n    <ng-container matColumnDef=\"win\">\n      <th mat-header-cell mat-sort-header disableClear *matHeaderCellDef>{{ 'GAMEHISTORY.GAME.win' | translate }}</th>\n      <td mat-cell *matCellDef=\"let row\">{{(row.credit || row.win) | formattedMoney: 2: ' ': row.currency}}</td>\n    </ng-container>\n\n    <ng-container matColumnDef=\"freeBetCoin\">\n      <th mat-header-cell mat-sort-header *matHeaderCellDef>{{ 'PROMO.EDIT.QUALIFYING_GAMES.freebetCoinValue' | translate }}</th>\n      <td mat-cell *matCellDef=\"let row\">{{row.freeBetCoin || '-'}}</td>\n    </ng-container>\n\n    <ng-container matColumnDef=\"ts\">\n      <th mat-header-cell mat-sort-header *matHeaderCellDef>{{ 'GAMEHISTORY.GAME.date' | translate }}</th>\n      <td mat-cell *matCellDef=\"let row\">{{row.ts}}</td>\n    </ng-container>\n\n    <ng-container matColumnDef=\"balanceBefore\">\n      <th mat-header-cell mat-sort-header *matHeaderCellDef>{{ 'GAMEHISTORY.GAME.balanceBefore' | translate }}</th>\n      <td mat-cell *matCellDef=\"let row\">{{ formattingBalanceBefore(row.balanceBefore, row.currency) }}</td>\n    </ng-container>\n\n    <ng-container matColumnDef=\"balanceAfter\">\n      <th mat-header-cell mat-sort-header *matHeaderCellDef>{{ 'GAMEHISTORY.GAME.balanceAfter' | translate }}</th>\n      <td mat-cell *matCellDef=\"let row\">{{ formattingBalanceBefore(row.balanceAfter, row.currency) }}</td>\n    </ng-container>\n\n    <ng-container matColumnDef=\"totalJpWin\">\n      <th mat-header-cell mat-sort-header *matHeaderCellDef>{{ 'GAMEHISTORY.GRID.totalJpWin' | translate }}</th>\n      <td mat-cell *matCellDef=\"let row\">{{row.totalJpWin | formattedMoney: 2: ' ': row.currency}}</td>\n    </ng-container>\n\n    <ng-container matColumnDef=\"aamsSessionCode\">\n      <th mat-header-cell mat-sort-header *matHeaderCellDef>{{ 'GAMEHISTORY.GRID.aamsSessionCode' | translate }}</th>\n      <td mat-cell *matCellDef=\"let row\">{{ row?.extraData?.regulatoryData?.aamsSessionCode }}</td>\n    </ng-container>\n\n    <ng-container matColumnDef=\"ropCode\">\n      <th mat-header-cell mat-sort-header *matHeaderCellDef>{{ 'GAMEHISTORY.GRID.ropCode' | translate }}</th>\n      <td mat-cell *matCellDef=\"let row\">{{ row?.extraData?.regulatoryData?.ropCode }}</td>\n    </ng-container>\n\n    <ng-container matColumnDef=\"participationStartDate\">\n      <th mat-header-cell mat-sort-header *matHeaderCellDef>{{ 'GAMEHISTORY.GRID.participationStartDate' | translate }}</th>\n      <td mat-cell *matCellDef=\"let row\">{{ row?.extraData?.regulatoryData?.participationStartDate }}</td>\n    </ng-container>\n\n    <tr mat-header-row *matHeaderRowDef=\"displayedColumns\"></tr>\n    <tr mat-row *matRowDef=\"let row; columns: displayedColumns;\"></tr>\n</table>\n  <mat-paginator [pageSizeOptions]=\"[5, 10, 25, 100]\"></mat-paginator>\n</div>\n"], "mappings": "AAAA,SAAoBA,YAAY,QAAkC,eAAe;AACjF,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,OAAO,QAAc,wBAAwB;AAEtD,SAASC,kBAAkB,QAAQ,yBAAyB;AAE5D,OAAOC,MAAM,MAAM,QAAQ;AAC3B,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,GAAG,EAAEC,SAAS,QAAQ,gBAAgB;AAC/C,SAASC,kBAAkB,QAAQ,kEAAkE;AAGrG,SAASC,oBAAoB,QAAQ,wCAAwC;AAC7E,SAASC,aAAa,QAAQ,8CAA8C;;;;;;;;;;;;;;;;;;;;;ICPxEC,EANJ,CAAAC,cAAA,cAIiB,yBACsC,gBACxC;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC7BH,EAAA,CAAAC,cAAA,gBAAwE;IAA9CD,EAAA,CAAAI,UAAA,mBAAAC,wDAAAC,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,WAAA,CAAAN,MAAA,CAAAO,MAAA,CAA0B,OAAO,EAAE;IAAA,EAAC;IACzEb,EADE,CAAAG,YAAA,EAAwE,EACzD;IAEjBH,EAAA,CAAAC,cAAA,cAA8E;IAC5ED,EAAA,CAAAc,SAAA,4BAA6E;IAC7Ed,EAAA,CAAAC,cAAA,iBAI0B;IAAxBD,EAAA,CAAAI,UAAA,mBAAAW,yDAAA;MAAAf,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAO,WAAA,EAAa;IAAA,EAAC;IACvBhB,EAAA,CAAAE,MAAA,GACF;;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;;IATiBH,EAAA,CAAAiB,SAAA,GAAa;IAACjB,EAAd,CAAAkB,UAAA,SAAAT,MAAA,CAAAU,IAAA,CAAa,cAAAV,MAAA,CAAAW,SAAA,CAAwB;IAMtDpB,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAAqB,kBAAA,MAAArB,EAAA,CAAAsB,WAAA,mDACF;;;;;IAQEtB,EAAA,CAAAC,cAAA,aAAsD;IAAAD,EAAA,CAAAE,MAAA,GAA2C;;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;IAAhDH,EAAA,CAAAiB,SAAA,EAA2C;IAA3CjB,EAAA,CAAAuB,iBAAA,CAAAvB,EAAA,CAAAsB,WAAA,kCAA2C;;;;;IAElEtB,EAAA,CAAAE,MAAA,GAAe;;;;IAAfF,EAAA,CAAAuB,iBAAA,CAAAC,MAAA,CAAAC,OAAA,CAAe;;;;;;IAC5CzB,EAAA,CAAAC,cAAA,YACkD;IAA/CD,EAAA,CAAAI,UAAA,mBAAAsB,uDAAApB,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAoB,GAAA;MAAA,MAAAH,MAAA,GAAAxB,EAAA,CAAAU,aAAA,GAAAkB,SAAA;MAAA,MAAAnB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAoB,eAAA,CAAAL,MAAA,CAAAC,OAAA,EAAAnB,MAAA,CAAoC;IAAA,EAAC;IAACN,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAAnBH,EAAA,CAAAiB,SAAA,EAAe;IAAfjB,EAAA,CAAAuB,iBAAA,CAAAC,MAAA,CAAAC,OAAA,CAAe;;;;;IAHnEzB,EAAA,CAAAC,cAAA,aAAmC;IAEjCD,EADA,CAAA8B,UAAA,IAAAC,6CAAA,gCAAA/B,EAAA,CAAAgC,sBAAA,CAA6B,IAAAC,mCAAA,gBAEqB;IACpDjC,EAAA,CAAAG,YAAA,EAAK;;;;;;IAFCH,EAAA,CAAAiB,SAAA,GAAiC;IAAAjB,EAAjC,CAAAkB,UAAA,SAAAT,MAAA,CAAAyB,eAAA,CAAAV,MAAA,CAAAW,IAAA,EAAiC,aAAAC,iBAAA,CAAmB;;;;;IAM1DpC,EAAA,CAAAC,cAAA,aAAsD;IAAAD,EAAA,CAAAE,MAAA,GAAyC;;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;IAA9CH,EAAA,CAAAiB,SAAA,EAAyC;IAAzCjB,EAAA,CAAAuB,iBAAA,CAAAvB,EAAA,CAAAsB,WAAA,gCAAyC;;;;;IAC/FtB,EAAA,CAAAC,cAAA,aAAmC;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAAxBH,EAAA,CAAAiB,SAAA,EAAmB;IAAnBjB,EAAA,CAAAuB,iBAAA,CAAAc,MAAA,CAAAF,IAAA,QAAmB;;;;;IAItDnC,EAAA,CAAAC,cAAA,aAAsD;IAAAD,EAAA,CAAAE,MAAA,GAAwC;;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;IAA7CH,EAAA,CAAAiB,SAAA,EAAwC;IAAxCjB,EAAA,CAAAuB,iBAAA,CAAAvB,EAAA,CAAAsB,WAAA,+BAAwC;;;;;IAC9FtB,EAAA,CAAAC,cAAA,aAAmC;IAAAD,EAAA,CAAAE,MAAA,GAAkD;;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAAvDH,EAAA,CAAAiB,SAAA,EAAkD;IAAlDjB,EAAA,CAAAuB,iBAAA,CAAAvB,EAAA,CAAAsC,WAAA,OAAAC,MAAA,CAAAC,GAAA,UAAAD,MAAA,CAAAE,QAAA,EAAkD;;;;;IAIrFzC,EAAA,CAAAC,cAAA,aAAmE;IAAAD,EAAA,CAAAE,MAAA,GAAwC;;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;IAA7CH,EAAA,CAAAiB,SAAA,EAAwC;IAAxCjB,EAAA,CAAAuB,iBAAA,CAAAvB,EAAA,CAAAsB,WAAA,+BAAwC;;;;;IAC3GtB,EAAA,CAAAC,cAAA,aAAmC;IAAAD,EAAA,CAAAE,MAAA,GAAkE;;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAAvEH,EAAA,CAAAiB,SAAA,EAAkE;IAAlEjB,EAAA,CAAAuB,iBAAA,CAAAvB,EAAA,CAAAsC,WAAA,OAAAI,MAAA,CAAAC,MAAA,IAAAD,MAAA,CAAAE,GAAA,UAAAF,MAAA,CAAAD,QAAA,EAAkE;;;;;IAIrGzC,EAAA,CAAAC,cAAA,aAAsD;IAAAD,EAAA,CAAAE,MAAA,GAAgE;;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;IAArEH,EAAA,CAAAiB,SAAA,EAAgE;IAAhEjB,EAAA,CAAAuB,iBAAA,CAAAvB,EAAA,CAAAsB,WAAA,uDAAgE;;;;;IACtHtB,EAAA,CAAAC,cAAA,aAAmC;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAA/BH,EAAA,CAAAiB,SAAA,EAA0B;IAA1BjB,EAAA,CAAAuB,iBAAA,CAAAsB,MAAA,CAAAC,WAAA,QAA0B;;;;;IAI7D9C,EAAA,CAAAC,cAAA,aAAsD;IAAAD,EAAA,CAAAE,MAAA,GAAyC;;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;IAA9CH,EAAA,CAAAiB,SAAA,EAAyC;IAAzCjB,EAAA,CAAAuB,iBAAA,CAAAvB,EAAA,CAAAsB,WAAA,gCAAyC;;;;;IAC/FtB,EAAA,CAAAC,cAAA,aAAmC;IAAAD,EAAA,CAAAE,MAAA,GAAU;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAAfH,EAAA,CAAAiB,SAAA,EAAU;IAAVjB,EAAA,CAAAuB,iBAAA,CAAAwB,OAAA,CAAAC,EAAA,CAAU;;;;;IAI7ChD,EAAA,CAAAC,cAAA,aAAsD;IAAAD,EAAA,CAAAE,MAAA,GAAkD;;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;IAAvDH,EAAA,CAAAiB,SAAA,EAAkD;IAAlDjB,EAAA,CAAAuB,iBAAA,CAAAvB,EAAA,CAAAsB,WAAA,yCAAkD;;;;;IACxGtB,EAAA,CAAAC,cAAA,aAAmC;IAAAD,EAAA,CAAAE,MAAA,GAA8D;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAAnEH,EAAA,CAAAiB,SAAA,EAA8D;IAA9DjB,EAAA,CAAAuB,iBAAA,CAAAd,MAAA,CAAAwC,uBAAA,CAAAC,OAAA,CAAAC,aAAA,EAAAD,OAAA,CAAAT,QAAA,EAA8D;;;;;IAIjGzC,EAAA,CAAAC,cAAA,aAAsD;IAAAD,EAAA,CAAAE,MAAA,GAAiD;;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;IAAtDH,EAAA,CAAAiB,SAAA,EAAiD;IAAjDjB,EAAA,CAAAuB,iBAAA,CAAAvB,EAAA,CAAAsB,WAAA,wCAAiD;;;;;IACvGtB,EAAA,CAAAC,cAAA,aAAmC;IAAAD,EAAA,CAAAE,MAAA,GAA6D;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAAlEH,EAAA,CAAAiB,SAAA,EAA6D;IAA7DjB,EAAA,CAAAuB,iBAAA,CAAAd,MAAA,CAAAwC,uBAAA,CAAAG,OAAA,CAAAC,YAAA,EAAAD,OAAA,CAAAX,QAAA,EAA6D;;;;;IAIhGzC,EAAA,CAAAC,cAAA,aAAsD;IAAAD,EAAA,CAAAE,MAAA,GAA+C;;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;IAApDH,EAAA,CAAAiB,SAAA,EAA+C;IAA/CjB,EAAA,CAAAuB,iBAAA,CAAAvB,EAAA,CAAAsB,WAAA,sCAA+C;;;;;IACrGtB,EAAA,CAAAC,cAAA,aAAmC;IAAAD,EAAA,CAAAE,MAAA,GAAyD;;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAA9DH,EAAA,CAAAiB,SAAA,EAAyD;IAAzDjB,EAAA,CAAAuB,iBAAA,CAAAvB,EAAA,CAAAsC,WAAA,OAAAgB,OAAA,CAAAC,UAAA,UAAAD,OAAA,CAAAb,QAAA,EAAyD;;;;;IAI5FzC,EAAA,CAAAC,cAAA,aAAsD;IAAAD,EAAA,CAAAE,MAAA,GAAoD;;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;IAAzDH,EAAA,CAAAiB,SAAA,EAAoD;IAApDjB,EAAA,CAAAuB,iBAAA,CAAAvB,EAAA,CAAAsB,WAAA,2CAAoD;;;;;IAC1GtB,EAAA,CAAAC,cAAA,aAAmC;IAAAD,EAAA,CAAAE,MAAA,GAAqD;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAA1DH,EAAA,CAAAiB,SAAA,EAAqD;IAArDjB,EAAA,CAAAuB,iBAAA,CAAAiC,OAAA,kBAAAA,OAAA,CAAAC,SAAA,kBAAAD,OAAA,CAAAC,SAAA,CAAAC,cAAA,kBAAAF,OAAA,CAAAC,SAAA,CAAAC,cAAA,CAAAC,eAAA,CAAqD;;;;;IAIxF3D,EAAA,CAAAC,cAAA,aAAsD;IAAAD,EAAA,CAAAE,MAAA,GAA4C;;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;IAAjDH,EAAA,CAAAiB,SAAA,EAA4C;IAA5CjB,EAAA,CAAAuB,iBAAA,CAAAvB,EAAA,CAAAsB,WAAA,mCAA4C;;;;;IAClGtB,EAAA,CAAAC,cAAA,aAAmC;IAAAD,EAAA,CAAAE,MAAA,GAA6C;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAAlDH,EAAA,CAAAiB,SAAA,EAA6C;IAA7CjB,EAAA,CAAAuB,iBAAA,CAAAqC,OAAA,kBAAAA,OAAA,CAAAH,SAAA,kBAAAG,OAAA,CAAAH,SAAA,CAAAC,cAAA,kBAAAE,OAAA,CAAAH,SAAA,CAAAC,cAAA,CAAAG,OAAA,CAA6C;;;;;IAIhF7D,EAAA,CAAAC,cAAA,aAAsD;IAAAD,EAAA,CAAAE,MAAA,GAA2D;;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;IAAhEH,EAAA,CAAAiB,SAAA,EAA2D;IAA3DjB,EAAA,CAAAuB,iBAAA,CAAAvB,EAAA,CAAAsB,WAAA,kDAA2D;;;;;IACjHtB,EAAA,CAAAC,cAAA,aAAmC;IAAAD,EAAA,CAAAE,MAAA,GAA4D;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAAjEH,EAAA,CAAAiB,SAAA,EAA4D;IAA5DjB,EAAA,CAAAuB,iBAAA,CAAAuC,OAAA,kBAAAA,OAAA,CAAAL,SAAA,kBAAAK,OAAA,CAAAL,SAAA,CAAAC,cAAA,kBAAAI,OAAA,CAAAL,SAAA,CAAAC,cAAA,CAAAK,sBAAA,CAA4D;;;;;IAGjG/D,EAAA,CAAAc,SAAA,aAA4D;;;;;IAC5Dd,EAAA,CAAAc,SAAA,aAAkE;;;ADrEtE,OAAM,MAAOkD,iBAAiB;EAoB5BC,YAAaC,WAA6B,EAAmBC,WAAoD;IAApD,KAAAA,WAAW,GAAXA,WAAW;IAlB/D,KAAAhD,IAAI,GAAW,EAAE;IAChB,KAAAiD,UAAU,GAAG,IAAI/E,YAAY,EAAO;IACpC,KAAAgF,YAAY,GAAG,IAAIhF,YAAY,EAAW;IAOpD,KAAAiF,KAAK,GAAsB,EAAE;IAC7B,KAAAC,SAAS,GAAsB,EAAE;IACjC,KAAAC,UAAU,GAAG,KAAK;IAID,KAAAC,UAAU,GAAG,IAAI/E,OAAO,EAAQ;IAChC,KAAAgF,kBAAkB,GAAG,IAAI7E,kBAAkB,EAAE;IAG5D,IAAI,CAAC8E,aAAa,GAAG7E,oBAAoB,CAACoE,WAAW,CAAC;IACtD,IAAI,CAACU,gBAAgB,GAAG,IAAI,CAACC,qBAAqB,EAAE;IAEpD,IAAI,CAACV,WAAW,CAACW,KAAK,CAACC,IAAI,CACzBpF,GAAG,CAAuCqF,IAAI,IAAG;MAC/C,IAAIC,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,IAAIA,IAAI,CAACG,MAAM,KAAK,CAAC,EAAE;QAC5C,IAAI,CAAC,YAAY,EAAE,SAAS,CAAC,CAACC,OAAO,CAACJ,IAAI,CAAC,CAAC,CAAC,CAAC7C,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;UAC1D6C,IAAI,CAAC,CAAC,CAAC,CAACxC,GAAG,GAAGwC,IAAI,CAAC,CAAC,CAAC,CAACxC,GAAG;UACzBwC,IAAI,CAAC,CAAC,CAAC,CAAC7B,aAAa,GAAG6B,IAAI,CAAC,CAAC,CAAC,CAAC7B,aAAa;UAC7C6B,IAAI,CAACK,KAAK,EAAE;QACd;MACF;MACA,OAAOL,IAAI,CAACrF,GAAG,CAAC2F,GAAG,KAAK;QACtB,GAAGA,GAAG;QACN7D,OAAO,EAAE,IAAI,CAACL,SAAS,CAACK,OAAO,GAAG,GAAG,GAAG6D,GAAG,CAACC,UAAU;QACtDvC,EAAE,EAAEvD,MAAM,CAAC6F,GAAG,CAACtC,EAAE,CAAC,CAACwC,MAAM,CAAC,qBAAqB;OAChD,CAAC,CAAC;IACL,CAAC,CAAC,EACF5F,SAAS,CAAC,IAAI,CAAC6E,UAAU,CAAC,CAC3B,CAACgB,SAAS,CAACT,IAAI,IAAG;MACf,IAAI,CAACT,SAAS,GAAGS,IAAI;MACrB,MAAMV,KAAK,GAAGoB,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACZ,IAAI,CAAC,CAAC;MAC9CV,KAAK,CAACuB,OAAO,CAACC,IAAI,IAAG;QACnB/F,aAAa,CAAC+F,IAAI,EAAEA,IAAI,CAACrD,QAAQ,CAAC;MACpC,CAAC,CAAC;MACF,IAAI,CAAC6B,KAAK,GAAGA,KAAK;MAClB,IAAI,CAACM,gBAAgB,GAAG,IAAI,CAACC,qBAAqB,EAAE;MACpD,IAAIG,IAAI,CAACG,MAAM,KAAK,CAAC,EAAE;QACrB,IAAI,CAACtD,eAAe,CAACmD,IAAI,CAAC,CAAC,CAAC,CAACvD,OAAO,CAAC;MACvC;MACA,IAAI,CAACsE,UAAU,GAAG,IAAIvG,kBAAkB,CAAC,IAAI,CAAC8E,KAAK,CAAC;MACpD,IAAI,CAACyB,UAAU,CAACC,SAAS,GAAG,IAAI,CAACA,SAAS;MAC1C,IAAI,IAAI,CAAC1B,KAAK,CAACa,MAAM,GAAG,CAAC,EAAE;QACzB,IAAI,CAACd,YAAY,CAAC4B,IAAI,CAAC,KAAK,CAAC;MAC/B;IACF,CAAC,CACF;EACH;EAEAC,eAAeA,CAAA;IACb,IAAI,CAACC,IAAI,EAAEC,UAAU,CAACrB,IAAI,CACxBnF,SAAS,CAAC,IAAI,CAAC6E,UAAU,CAAC,CAC3B,CAACgB,SAAS,CAAGT,IAAU,IAAK;MAC3B,IAAI,CAACgB,SAAS,CAACK,SAAS,GAAG,CAAC;MAC5B,MAAM;QAAEC,MAAM;QAAEC;MAAS,CAAE,GAAGvB,IAAI;MAClC,IAAI,CAACV,KAAK,GAAG,IAAI,CAACkC,SAAS,CAACF,MAAM,EAAEC,SAAS,CAAC;MAC9C,IAAI,CAACR,UAAU,CAACf,IAAI,GAAG,IAAI,CAACV,KAAK;MACjC,IAAI,CAACyB,UAAU,CAACC,SAAS,GAAG,IAAI,CAACA,SAAS;IAC5C,CAAC,CAAC;EACJ;EAEAS,QAAQA,CAAA;IACN,IAAI,CAACtC,WAAW,CAACuC,OAAO,CAAC,IAAI,CAACtF,SAAS,CAACK,OAAO,EAAE;MAC/CkF,MAAM,EAAE;QACNxF,IAAI,EAAE,IAAI,CAACA;;KAEd,CAAC;EACJ;EAEAyF,WAAWA,CAAA;IACT,IAAI,CAACnC,UAAU,CAACoC,IAAI,EAAE;IACtB,IAAI,CAACpC,UAAU,CAACqC,QAAQ,EAAE;EAC5B;EAEA9F,WAAWA,CAAA;IACT,IAAI,CAACmD,WAAW,CAAC4C,WAAW,CAAC,IAAI,CAAC3F,SAAS,EAAE,IAAI,CAACD,IAAI,EAAE,IAAI,CAACqD,UAAU,CAAC,CAACO,IAAI,CAC3EnF,SAAS,CAAC,IAAI,CAAC6E,UAAU,CAAC,CAC3B,CAACgB,SAAS,EAAE;EACf;EAEAxC,uBAAuBA,CAAE+D,kBAAmC,EAAEvE,QAAgB;IAC5E,OAAOuE,kBAAkB,IAAIA,kBAAkB,KAAK,CAAC,GACnD,IAAI,CAACtC,kBAAkB,CAACuC,SAAS,CAACD,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAEvE,QAAQ,CAAC,GAAG,GAAG;EAChF;EAEAP,eAAeA,CAAEC,IAAY;IAC3B,OAAO,CAAC,aAAa,EAAE,cAAc,EAAE,cAAc,EAAE,YAAY,EAAE,UAAU,EAAE,SAAS,CAAC,CAACiD,OAAO,CAACjD,IAAI,CAAC,KAAK,CAAC,CAAC;EAClH;EAEAN,eAAeA,CAAEJ,OAAO,EAAEyF,KAAM;IAC9B,IAAIA,KAAK,EAAE;MACTA,KAAK,CAACC,cAAc,EAAE;IACxB;IAEA,MAAMC,IAAI,GAAG,IAAI,CAAC7C,SAAS,CAAC8C,IAAI,CAACC,KAAK,IAAIA,KAAK,CAAC7F,OAAO,KAAKA,OAAO,CAAC;IAEpE,MAAM8F,MAAM,GAAG;MACb9F,OAAO,EAAE,IAAI,CAACL,SAAS,CAACK,OAAO;MAC/B8D,UAAU,EAAE6B,IAAI,CAAC7B,UAAU;MAC3BO,IAAI,EAAEsB,IAAI;MACVjG,IAAI,EAAE,IAAI,CAACA;KACZ;IACD,IAAI,CAACgD,WAAW,CAACqD,OAAO,CAACD,MAAM,CAAC;EAClC;EAEA3G,WAAWA,CAAE6G,WAAmB;IAC9B,IAAI,CAAC1B,UAAU,CAAC2B,MAAM,GAAGD,WAAW,CAACE,IAAI,EAAE,CAACC,WAAW,EAAE;IACzD,IAAI,IAAI,CAAC7B,UAAU,CAACC,SAAS,EAAE;MAC7B,IAAI,CAACD,UAAU,CAACC,SAAS,CAAC6B,SAAS,EAAE;IACvC;EACF;EAEQhD,qBAAqBA,CAAA;IAC3B,MAAMiD,cAAc,GAAG,CAAC,IAAI,CAACnD,aAAa,IAAI,EAAE,IAAI,CAACvD,SAAS,EAAE2G,KAAK,EAAEC,yBAAyB,IAAI,KAAK,CAAC;IAC1G,MAAM1D,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,MAAMZ,cAAc,GAAGY,KAAK,CAAC3E,GAAG,CAACyH,IAAI,IAAIA,IAAI,CAAC3D,SAAS,EAAEC,cAAc,CAAC;IAExE,MAAMuE,eAAe,GAAGH,cAAc,IAAIxD,KAAK,CAAC4D,IAAI,CAACd,IAAI,IAAIA,IAAI,EAAEjE,aAAa,CAAC;IACjF,MAAMgF,cAAc,GAAGL,cAAc,IAAIxD,KAAK,CAAC4D,IAAI,CAACd,IAAI,IAAIA,IAAI,EAAE/D,YAAY,CAAC;IAC/E,IAAI,CAACmB,UAAU,GAAGF,KAAK,CAAC4D,IAAI,CAACpC,IAAI,IAAIA,IAAI,EAAE3D,IAAI,KAAK,SAAS,CAAC;IAC9D,MAAMiG,kBAAkB,GAAG1E,cAAc,CAACwE,IAAI,CAACd,IAAI,IAAIA,IAAI,EAAEzD,eAAe,CAAC;IAC7E,MAAM0E,UAAU,GAAG3E,cAAc,CAACwE,IAAI,CAACd,IAAI,IAAIA,IAAI,EAAEvD,OAAO,CAAC;IAC7D,MAAMyE,yBAAyB,GAAG5E,cAAc,CAACwE,IAAI,CAACd,IAAI,IAAIA,IAAI,EAAErD,sBAAsB,CAAC;IAE3F,OAAO,CACL,QAAQ,EACR,MAAM,EACN,KAAK,EACL,KAAK,EACL,IAAI,IAAI,CAACS,UAAU,GAAG,CAAC,aAAa,CAAC,GAAG,EAAE,CAAC,EAC3C,IAAI,EACJ,IAAIyD,eAAe,GAAG,CAAC,eAAe,CAAC,GAAG,EAAE,CAAC,EAC7C,IAAIE,cAAc,GAAG,CAAC,cAAc,CAAC,GAAG,EAAE,CAAC,EAC3C,YAAY,EACZ,IAAIC,kBAAkB,GAAG,CAAC,iBAAiB,CAAC,GAAG,EAAE,CAAC,EAClD,IAAIC,UAAU,GAAG,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,EAClC,IAAIC,yBAAyB,GAAG,CAAC,wBAAwB,CAAC,GAAG,EAAE,CAAC,CACjE;EACH;EAEQ9B,SAASA,CAAEF,MAAc,EAAEC,SAAwB;IACzDD,MAAM,GAAGA,MAAM,KAAK,QAAQ,GAAG,YAAY,GAAGA,MAAM;IACpD,MAAMiC,MAAM,GAAGjC,MAAM,KAAK,IAAI;IAC9B,IAAIkC,QAAQ,GAAG,KAAK;IACpB,IAAI,CAACD,MAAM,EAAE;MACX,MAAME,KAAK,GAAG,IAAI,CAACnE,KAAK,CAAC3E,GAAG,CAACmG,IAAI,IAAIA,IAAI,CAACQ,MAAM,CAAC,CAAC,CAACoB,MAAM,CAACN,IAAI,IAAIA,IAAI,KAAKsB,SAAS,CAAC,CAACrD,KAAK,EAAE;MAC7FmD,QAAQ,GAAG,OAAOC,KAAK,KAAK,QAAQ;IACtC;IACA,OAAO,IAAI,CAACnE,KAAK,CAAC6B,IAAI,CAAC,CAAEwC,KAAsB,EAAEC,MAAuB,KAAK;MAC3E,IAAIL,MAAM,EAAE;QACV,IAAIhC,SAAS,KAAK,KAAK,EAAE;UACvB,OAAO9G,MAAM,CAACkJ,KAAK,CAAC3F,EAAE,CAAC,CAAC6F,IAAI,EAAE,GAAGpJ,MAAM,CAACmJ,MAAM,CAAC5F,EAAE,CAAC,CAAC6F,IAAI,EAAE;QAC3D;QACA,IAAItC,SAAS,KAAK,MAAM,EAAE;UACxB,OAAO9G,MAAM,CAACmJ,MAAM,CAAC5F,EAAE,CAAC,CAAC6F,IAAI,EAAE,GAAGpJ,MAAM,CAACkJ,KAAK,CAAC3F,EAAE,CAAC,CAAC6F,IAAI,EAAE;QAC3D;MACF;MACA,IAAIL,QAAQ,EAAE;QACZ,IAAIjC,SAAS,KAAK,KAAK,EAAE;UACvB,OAAOoC,KAAK,CAACrC,MAAM,CAAC,GAAGsC,MAAM,CAACtC,MAAM,CAAC;QACvC;QACA,IAAIC,SAAS,KAAK,MAAM,EAAE;UACxB,OAAOqC,MAAM,CAACtC,MAAM,CAAC,GAAGqC,KAAK,CAACrC,MAAM,CAAC;QACvC;MACF;MACA,IAAI,CAAC,CAACC,SAAS,EAAE;QACf,OAAOA,SAAS,KAAK,KAAK,GAAGoC,KAAK,CAACrC,MAAM,CAAC,EAAEwC,aAAa,CAACF,MAAM,CAACtC,MAAM,CAAC,CAAC,GAAGsC,MAAM,CAACtC,MAAM,CAAC,EAAEwC,aAAa,CAACH,KAAK,CAACrC,MAAM,CAAC,CAAC;MAC1H;IACF,CAAC,CAAC;EACJ;;;uCApLWtC,iBAAiB,EAAAhE,EAAA,CAAA+I,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAjJ,EAAA,CAAA+I,iBAAA,CAAAG,EAAA,CAAAC,sBAAA;IAAA;EAAA;;;YAAjBnF,iBAAiB;MAAAoF,SAAA;MAAAC,SAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;yBAMjBjK,YAAY;yBACZC,OAAO;;;;;;;;;;;;;;;;;;;;;UC5BpBS,EAAA,CAAA8B,UAAA,IAAA2H,gCAAA,kBAIiB;UAmBfzJ,EADF,CAAAC,cAAA,aAAuC,eACc;UAEjDD,EAAA,CAAA0J,uBAAA,MAAoC;UAElC1J,EADA,CAAA8B,UAAA,IAAA6H,+BAAA,gBAAsD,IAAAC,+BAAA,gBACnB;;UAOrC5J,EAAA,CAAA0J,uBAAA,MAAkC;UAEhC1J,EADA,CAAA8B,UAAA,IAAA+H,+BAAA,gBAAsD,IAAAC,+BAAA,gBACnB;;UAGrC9J,EAAA,CAAA0J,uBAAA,MAAiC;UAE/B1J,EADA,CAAA8B,UAAA,KAAAiI,gCAAA,gBAAsD,KAAAC,gCAAA,gBACnB;;UAGrChK,EAAA,CAAA0J,uBAAA,OAAiC;UAE/B1J,EADA,CAAA8B,UAAA,KAAAmI,gCAAA,iBAAmE,KAAAC,gCAAA,gBAChC;;UAGrClK,EAAA,CAAA0J,uBAAA,QAAyC;UAEvC1J,EADA,CAAA8B,UAAA,KAAAqI,gCAAA,gBAAsD,KAAAC,gCAAA,gBACnB;;UAGrCpK,EAAA,CAAA0J,uBAAA,QAAgC;UAE9B1J,EADA,CAAA8B,UAAA,KAAAuI,gCAAA,gBAAsD,KAAAC,gCAAA,gBACnB;;UAGrCtK,EAAA,CAAA0J,uBAAA,QAA2C;UAEzC1J,EADA,CAAA8B,UAAA,KAAAyI,gCAAA,gBAAsD,KAAAC,gCAAA,gBACnB;;UAGrCxK,EAAA,CAAA0J,uBAAA,QAA0C;UAExC1J,EADA,CAAA8B,UAAA,KAAA2I,gCAAA,gBAAsD,KAAAC,gCAAA,gBACnB;;UAGrC1K,EAAA,CAAA0J,uBAAA,QAAwC;UAEtC1J,EADA,CAAA8B,UAAA,KAAA6I,gCAAA,gBAAsD,KAAAC,gCAAA,gBACnB;;UAGrC5K,EAAA,CAAA0J,uBAAA,QAA6C;UAE3C1J,EADA,CAAA8B,UAAA,KAAA+I,gCAAA,gBAAsD,KAAAC,gCAAA,gBACnB;;UAGrC9K,EAAA,CAAA0J,uBAAA,QAAqC;UAEnC1J,EADA,CAAA8B,UAAA,KAAAiJ,gCAAA,gBAAsD,KAAAC,gCAAA,gBACnB;;UAGrChL,EAAA,CAAA0J,uBAAA,QAAoD;UAElD1J,EADA,CAAA8B,UAAA,KAAAmJ,gCAAA,gBAAsD,KAAAC,gCAAA,gBACnB;;UAIrClL,EADA,CAAA8B,UAAA,KAAAqJ,gCAAA,iBAAuD,KAAAC,gCAAA,iBACM;UACjEpL,EAAA,CAAAG,YAAA,EAAQ;UACNH,EAAA,CAAAc,SAAA,yBAAoE;UACtEd,EAAA,CAAAG,YAAA,EAAM;;;UA5FHH,EAAA,CAAAkB,UAAA,UAAAsI,GAAA,CAAAlF,KAAA,kBAAAkF,GAAA,CAAAlF,KAAA,CAAAa,MAAA,MAAuB;UAsBCnF,EAAA,CAAAiB,SAAA,GAAyB;UAAzBjB,EAAA,CAAAkB,UAAA,eAAAsI,GAAA,CAAAzD,UAAA,CAAyB;UAkE5B/F,EAAA,CAAAiB,SAAA,IAAiC;UAAjCjB,EAAA,CAAAkB,UAAA,oBAAAsI,GAAA,CAAA5E,gBAAA,CAAiC;UACpB5E,EAAA,CAAAiB,SAAA,EAA0B;UAA1BjB,EAAA,CAAAkB,UAAA,qBAAAsI,GAAA,CAAA5E,gBAAA,CAA0B;UAE9C5E,EAAA,CAAAiB,SAAA,EAAoC;UAApCjB,EAAA,CAAAkB,UAAA,oBAAAlB,EAAA,CAAAqL,eAAA,IAAAC,GAAA,EAAoC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}