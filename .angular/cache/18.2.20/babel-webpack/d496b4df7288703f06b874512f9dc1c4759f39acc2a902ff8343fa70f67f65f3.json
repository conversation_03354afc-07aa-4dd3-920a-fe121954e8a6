{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FlexModule } from '@angular/flex-layout';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiControlMessagesModule, SwuiDatePickerModule, SwuiGridModule, SwuiPagePanelModule, SwuiSchemaTopFilterModule, SwuiSelectModule } from '@skywind-group/lib-swui';\nimport { BoConfirmationModule } from 'src/app/common/components/bo-confirmation/bo-confirmation.module';\nimport { CalendarModule } from '../../../../common/components/calendar/calendar.module';\nimport { ControlMessagesModule } from '../../../../common/components/control-messages/control-messages.module';\nimport { TrimInputValueModule } from '../../../../common/directives/trim-input-value/trim-input-value.module';\nimport { GrcService } from '../../../../common/services/grc.service';\nimport { GrcEmailFormComponent } from './forms/grc-email-form/grc-email-form.component';\nimport { GrcEmailGetFormComponent } from './forms/grc-email-get-form/grc-email-get-form.component';\nimport { GrcEmailEndComponent } from './grc-email-end/grc-email-end.component';\nimport { GrcEmailStartComponent } from './grc-email-start/grc-email-start.component';\nimport { GrcListComponent } from './grc-list.component';\nimport { AddGrcModalComponent } from './modals/add-grc-modal.component';\nimport { EditDefinitionModalComponent } from './modals/edit-definition-modal.component';\nimport { GrcEmailModalComponent } from './modals/grc-email-modal.component';\nimport * as i0 from \"@angular/core\";\nexport class GrcListModule {\n  static {\n    this.ɵfac = function GrcListModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || GrcListModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: GrcListModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [GrcService],\n      imports: [BoConfirmationModule, CalendarModule, CommonModule, ControlMessagesModule, ReactiveFormsModule, TranslateModule, SwuiPagePanelModule, MatCardModule, SwuiGridModule, MatIconModule, MatTooltipModule, MatButtonModule, SwuiSchemaTopFilterModule, MatDialogModule, FlexModule, MatFormFieldModule, MatInputModule, SwuiControlMessagesModule, SwuiDatePickerModule, SwuiSelectModule, MatTabsModule, TrimInputValueModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(GrcListModule, {\n    declarations: [AddGrcModalComponent, EditDefinitionModalComponent, GrcEmailFormComponent, GrcEmailEndComponent, GrcEmailGetFormComponent, GrcEmailModalComponent, GrcEmailStartComponent, GrcListComponent],\n    imports: [BoConfirmationModule, CalendarModule, CommonModule, ControlMessagesModule, ReactiveFormsModule, TranslateModule, SwuiPagePanelModule, MatCardModule, SwuiGridModule, MatIconModule, MatTooltipModule, MatButtonModule, SwuiSchemaTopFilterModule, MatDialogModule, FlexModule, MatFormFieldModule, MatInputModule, SwuiControlMessagesModule, SwuiDatePickerModule, SwuiSelectModule, MatTabsModule, TrimInputValueModule],\n    exports: [GrcListComponent]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FlexModule", "ReactiveFormsModule", "MatButtonModule", "MatCardModule", "MatDialogModule", "MatFormFieldModule", "MatIconModule", "MatInputModule", "MatTabsModule", "MatTooltipModule", "TranslateModule", "SwuiControlMessagesModule", "SwuiDatePickerModule", "SwuiGridModule", "SwuiPagePanelModule", "SwuiSchemaTopFilterModule", "SwuiSelectModule", "BoConfirmationModule", "CalendarModule", "ControlMessagesModule", "TrimInputValueModule", "GrcService", "GrcEmailFormComponent", "GrcEmailGetFormComponent", "GrcEmailEndComponent", "GrcEmailStartComponent", "GrcListComponent", "AddGrcModalComponent", "EditDefinitionModalComponent", "GrcEmailModalComponent", "GrcListModule", "imports", "declarations", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/grc/components/grc-list/grc-list.module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { FlexModule } from '@angular/flex-layout';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { TranslateModule } from '@ngx-translate/core';\nimport {\n  SwuiControlMessagesModule, SwuiDatePickerModule, SwuiGridModule, SwuiPagePanelModule, SwuiSchemaTopFilterModule, SwuiSelectModule\n} from '@skywind-group/lib-swui';\nimport { BoConfirmationModule } from 'src/app/common/components/bo-confirmation/bo-confirmation.module';\nimport { CalendarModule } from '../../../../common/components/calendar/calendar.module';\nimport { ControlMessagesModule } from '../../../../common/components/control-messages/control-messages.module';\nimport { TrimInputValueModule } from '../../../../common/directives/trim-input-value/trim-input-value.module';\nimport { GrcService } from '../../../../common/services/grc.service';\nimport { GrcEmailFormComponent } from './forms/grc-email-form/grc-email-form.component';\nimport { GrcEmailGetFormComponent } from './forms/grc-email-get-form/grc-email-get-form.component';\nimport { GrcEmailEndComponent } from './grc-email-end/grc-email-end.component';\nimport { GrcEmailStartComponent } from './grc-email-start/grc-email-start.component';\nimport { GrcListComponent } from './grc-list.component';\n\nimport { AddGrcModalComponent } from './modals/add-grc-modal.component';\nimport { EditDefinitionModalComponent } from './modals/edit-definition-modal.component';\nimport { GrcEmailModalComponent } from './modals/grc-email-modal.component';\n\n@NgModule({\n    imports: [\n        BoConfirmationModule,\n        CalendarModule,\n        CommonModule,\n        ControlMessagesModule,\n        ReactiveFormsModule,\n        TranslateModule,\n        SwuiPagePanelModule,\n        MatCardModule,\n        SwuiGridModule,\n        MatIconModule,\n        MatTooltipModule,\n        MatButtonModule,\n        SwuiSchemaTopFilterModule,\n        MatDialogModule,\n        FlexModule,\n        MatFormFieldModule,\n        MatInputModule,\n        SwuiControlMessagesModule,\n        SwuiDatePickerModule,\n        SwuiSelectModule,\n        MatTabsModule,\n        TrimInputValueModule\n    ],\n  exports: [\n    GrcListComponent,\n  ],\n  declarations: [\n    AddGrcModalComponent,\n    EditDefinitionModalComponent,\n    GrcEmailFormComponent,\n    GrcEmailEndComponent,\n    GrcEmailGetFormComponent,\n    GrcEmailModalComponent,\n    GrcEmailStartComponent,\n    GrcListComponent,\n  ],\n  providers: [\n    GrcService,\n  ],\n})\n\nexport class GrcListModule {\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,UAAU,QAAQ,sBAAsB;AACjD,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SACEC,yBAAyB,EAAEC,oBAAoB,EAAEC,cAAc,EAAEC,mBAAmB,EAAEC,yBAAyB,EAAEC,gBAAgB,QAC5H,yBAAyB;AAChC,SAASC,oBAAoB,QAAQ,kEAAkE;AACvG,SAASC,cAAc,QAAQ,wDAAwD;AACvF,SAASC,qBAAqB,QAAQ,wEAAwE;AAC9G,SAASC,oBAAoB,QAAQ,wEAAwE;AAC7G,SAASC,UAAU,QAAQ,yCAAyC;AACpE,SAASC,qBAAqB,QAAQ,iDAAiD;AACvF,SAASC,wBAAwB,QAAQ,yDAAyD;AAClG,SAASC,oBAAoB,QAAQ,yCAAyC;AAC9E,SAASC,sBAAsB,QAAQ,6CAA6C;AACpF,SAASC,gBAAgB,QAAQ,sBAAsB;AAEvD,SAASC,oBAAoB,QAAQ,kCAAkC;AACvE,SAASC,4BAA4B,QAAQ,0CAA0C;AACvF,SAASC,sBAAsB,QAAQ,oCAAoC;;AA6C3E,OAAM,MAAOC,aAAa;;;uCAAbA,aAAa;IAAA;EAAA;;;YAAbA;IAAa;EAAA;;;iBALb,CACTT,UAAU,CACX;MAAAU,OAAA,GAtCKd,oBAAoB,EACpBC,cAAc,EACdnB,YAAY,EACZoB,qBAAqB,EACrBlB,mBAAmB,EACnBS,eAAe,EACfI,mBAAmB,EACnBX,aAAa,EACbU,cAAc,EACdP,aAAa,EACbG,gBAAgB,EAChBP,eAAe,EACfa,yBAAyB,EACzBX,eAAe,EACfJ,UAAU,EACVK,kBAAkB,EAClBE,cAAc,EACdI,yBAAyB,EACzBC,oBAAoB,EACpBI,gBAAgB,EAChBR,aAAa,EACbY,oBAAoB;IAAA;EAAA;;;2EAoBfU,aAAa;IAAAE,YAAA,GAdtBL,oBAAoB,EACpBC,4BAA4B,EAC5BN,qBAAqB,EACrBE,oBAAoB,EACpBD,wBAAwB,EACxBM,sBAAsB,EACtBJ,sBAAsB,EACtBC,gBAAgB;IAAAK,OAAA,GAlCZd,oBAAoB,EACpBC,cAAc,EACdnB,YAAY,EACZoB,qBAAqB,EACrBlB,mBAAmB,EACnBS,eAAe,EACfI,mBAAmB,EACnBX,aAAa,EACbU,cAAc,EACdP,aAAa,EACbG,gBAAgB,EAChBP,eAAe,EACfa,yBAAyB,EACzBX,eAAe,EACfJ,UAAU,EACVK,kBAAkB,EAClBE,cAAc,EACdI,yBAAyB,EACzBC,oBAAoB,EACpBI,gBAAgB,EAChBR,aAAa,EACbY,oBAAoB;IAAAa,OAAA,GAGxBP,gBAAgB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}