{"ast": null, "code": "/**\n * RGB space.\n *\n * @module  color-space/rgb\n */\n'use strict';\n\nmodule.exports = {\n  name: 'rgb',\n  min: [0, 0, 0],\n  max: [255, 255, 255],\n  channel: ['red', 'green', 'blue'],\n  alias: ['RGB']\n};", "map": {"version": 3, "names": ["module", "exports", "name", "min", "max", "channel", "alias"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/node_modules/color-space/rgb.js"], "sourcesContent": ["/**\n * RGB space.\n *\n * @module  color-space/rgb\n */\n'use strict'\n\nmodule.exports = {\n\tname: 'rgb',\n\tmin: [0,0,0],\n\tmax: [255,255,255],\n\tchannel: ['red', 'green', 'blue'],\n\talias: ['RGB']\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAG;EAChBC,IAAI,EAAE,KAAK;EACXC,GAAG,EAAE,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC;EACZC,GAAG,EAAE,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC;EAClBC,OAAO,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC;EACjCC,KAAK,EAAE,CAAC,KAAK;AACd,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}