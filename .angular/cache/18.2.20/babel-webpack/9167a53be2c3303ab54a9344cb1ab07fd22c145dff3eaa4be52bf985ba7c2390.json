{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatListModule } from '@angular/material/list';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiGridModule } from '@skywind-group/lib-swui';\nimport { ControlMessagesModule } from '../../../../../../common/components/control-messages/control-messages.module';\nimport { TrimInputValueModule } from '../../../../../../common/directives/trim-input-value/trim-input-value.module';\nimport { EditSiteDialogComponent } from './dialogs/edit-site-dialog.component';\nimport { RemoveConfirmDialogComponent } from './dialogs/remove-confirm-dialog.component';\nimport { EntitySitesAvailableComponent } from './entity-sites-available.component';\nimport { WhitelistLevelsComponent } from './whitelist-levels.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nexport class EntitySitesAvailableModule {\n  static {\n    this.ɵfac = function EntitySitesAvailableModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || EntitySitesAvailableModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: EntitySitesAvailableModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, ReactiveFormsModule, ControlMessagesModule, TranslateModule.forChild(), SwuiGridModule, MatButtonModule, MatMenuModule, MatIconModule, MatProgressSpinnerModule, MatTooltipModule, MatDialogModule, MatFormFieldModule, MatInputModule, MatSelectModule, MatListModule, MatCheckboxModule, TrimInputValueModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(EntitySitesAvailableModule, {\n    declarations: [EntitySitesAvailableComponent, WhitelistLevelsComponent, EditSiteDialogComponent, RemoveConfirmDialogComponent],\n    imports: [CommonModule, ReactiveFormsModule, ControlMessagesModule, i1.TranslateModule, SwuiGridModule, MatButtonModule, MatMenuModule, MatIconModule, MatProgressSpinnerModule, MatTooltipModule, MatDialogModule, MatFormFieldModule, MatInputModule, MatSelectModule, MatListModule, MatCheckboxModule, TrimInputValueModule],\n    exports: [EntitySitesAvailableComponent, WhitelistLevelsComponent]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "ReactiveFormsModule", "MatButtonModule", "MatCheckboxModule", "MatDialogModule", "MatFormFieldModule", "MatIconModule", "MatInputModule", "MatListModule", "MatMenuModule", "MatProgressSpinnerModule", "MatSelectModule", "MatTooltipModule", "TranslateModule", "SwuiGridModule", "ControlMessagesModule", "TrimInputValueModule", "EditSiteDialogComponent", "RemoveConfirmDialogComponent", "EntitySitesAvailableComponent", "WhitelistLevelsComponent", "EntitySitesAvailableModule", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "imports", "i1", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-2fa/entity-sites-available/entity-sites-available.module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatListModule } from '@angular/material/list';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiGridModule } from '@skywind-group/lib-swui';\nimport { ControlMessagesModule } from '../../../../../../common/components/control-messages/control-messages.module';\nimport { TrimInputValueModule } from '../../../../../../common/directives/trim-input-value/trim-input-value.module';\nimport { EditSiteDialogComponent } from './dialogs/edit-site-dialog.component';\nimport { RemoveConfirmDialogComponent } from './dialogs/remove-confirm-dialog.component';\n\nimport { EntitySitesAvailableComponent } from './entity-sites-available.component';\nimport { WhitelistLevelsComponent } from './whitelist-levels.component';\n\n@NgModule({\n    imports: [\n        CommonModule,\n        ReactiveFormsModule,\n        ControlMessagesModule,\n        TranslateModule.forChild(),\n        SwuiGridModule,\n        MatButtonModule,\n        MatMenuModule,\n        MatIconModule,\n        MatProgressSpinnerModule,\n        MatTooltipModule,\n        MatDialogModule,\n        MatFormFieldModule,\n        MatInputModule,\n        MatSelectModule,\n        MatListModule,\n        MatCheckboxModule,\n        TrimInputValueModule,\n    ],\n  declarations: [\n    EntitySitesAvailableComponent,\n    WhitelistLevelsComponent,\n    EditSiteDialogComponent,\n    RemoveConfirmDialogComponent,\n  ],\n  exports: [\n    EntitySitesAvailableComponent,\n    WhitelistLevelsComponent,\n  ],\n  providers: [],\n})\nexport class EntitySitesAvailableModule {\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,qBAAqB,QAAQ,8EAA8E;AACpH,SAASC,oBAAoB,QAAQ,8EAA8E;AACnH,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,4BAA4B,QAAQ,2CAA2C;AAExF,SAASC,6BAA6B,QAAQ,oCAAoC;AAClF,SAASC,wBAAwB,QAAQ,8BAA8B;;;AAkCvE,OAAM,MAAOC,0BAA0B;;;uCAA1BA,0BAA0B;IAAA;EAAA;;;YAA1BA;IAA0B;EAAA;;;gBA9B/BrB,YAAY,EACZC,mBAAmB,EACnBc,qBAAqB,EACrBF,eAAe,CAACS,QAAQ,EAAE,EAC1BR,cAAc,EACdZ,eAAe,EACfO,aAAa,EACbH,aAAa,EACbI,wBAAwB,EACxBE,gBAAgB,EAChBR,eAAe,EACfC,kBAAkB,EAClBE,cAAc,EACdI,eAAe,EACfH,aAAa,EACbL,iBAAiB,EACjBa,oBAAoB;IAAA;EAAA;;;2EAcfK,0BAA0B;IAAAE,YAAA,GAXnCJ,6BAA6B,EAC7BC,wBAAwB,EACxBH,uBAAuB,EACvBC,4BAA4B;IAAAM,OAAA,GAtBxBxB,YAAY,EACZC,mBAAmB,EACnBc,qBAAqB,EAAAU,EAAA,CAAAZ,eAAA,EAErBC,cAAc,EACdZ,eAAe,EACfO,aAAa,EACbH,aAAa,EACbI,wBAAwB,EACxBE,gBAAgB,EAChBR,eAAe,EACfC,kBAAkB,EAClBE,cAAc,EACdI,eAAe,EACfH,aAAa,EACbL,iBAAiB,EACjBa,oBAAoB;IAAAU,OAAA,GASxBP,6BAA6B,EAC7BC,wBAAwB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}