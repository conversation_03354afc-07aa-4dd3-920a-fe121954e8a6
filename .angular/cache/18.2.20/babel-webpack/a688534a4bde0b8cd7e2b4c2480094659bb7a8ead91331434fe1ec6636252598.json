{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FlexModule } from '@angular/flex-layout';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatPaginatorModule } from '@angular/material/paginator';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiControlMessagesModule, SwuiGridModule, SwuiSchemaTopFilterModule, SwuiSelectModule } from '@skywind-group/lib-swui';\nimport { BoConfirmationModule } from '../../../../../common/components/bo-confirmation/bo-confirmation.module';\nimport { TrimInputValueModule } from '../../../../../common/directives/trim-input-value/trim-input-value.module';\nimport { GameGroupService } from '../../../../../common/services/game-group.service';\nimport { GameLimitsService } from '../../../../../common/services/game-limits.service';\nimport { CurrenciesResolver } from '../../../../../common/services/resolvers/currencies.resolver';\nimport { StakeRangesService } from '../../../../../common/services/stake-ranges.service';\nimport { CloneLimitsComponent } from './forms/clone-limits.component';\nimport { CreateLimitsComponent } from './forms/create-limits.component';\nimport { GameLimitsComponent } from './game-limits.component';\nimport { TabGameLimitsComponent } from './tab-game-limits.component';\nimport { TabGameLimitsRoutingModule } from './tab-game-limits.routing';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nexport class TabGameLimitsModule {\n  static {\n    this.ɵfac = function TabGameLimitsModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TabGameLimitsModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: TabGameLimitsModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [CurrenciesResolver, GameGroupService, StakeRangesService, GameLimitsService],\n      imports: [BoConfirmationModule, CommonModule, MatPaginatorModule, ReactiveFormsModule, TabGameLimitsRoutingModule, TranslateModule.forChild(), MatCardModule, SwuiGridModule, MatButtonModule, SwuiSelectModule, MatFormFieldModule, MatDialogModule, FlexModule, MatIconModule, MatExpansionModule, MatInputModule, SwuiSchemaTopFilterModule, SwuiControlMessagesModule, TrimInputValueModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(TabGameLimitsModule, {\n    declarations: [CloneLimitsComponent, CreateLimitsComponent, GameLimitsComponent, TabGameLimitsComponent],\n    imports: [BoConfirmationModule, CommonModule, MatPaginatorModule, ReactiveFormsModule, TabGameLimitsRoutingModule, i1.TranslateModule, MatCardModule, SwuiGridModule, MatButtonModule, SwuiSelectModule, MatFormFieldModule, MatDialogModule, FlexModule, MatIconModule, MatExpansionModule, MatInputModule, SwuiSchemaTopFilterModule, SwuiControlMessagesModule, TrimInputValueModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FlexModule", "ReactiveFormsModule", "MatButtonModule", "MatCardModule", "MatDialogModule", "MatExpansionModule", "MatFormFieldModule", "MatIconModule", "MatInputModule", "MatPaginatorModule", "TranslateModule", "SwuiControlMessagesModule", "SwuiGridModule", "SwuiSchemaTopFilterModule", "SwuiSelectModule", "BoConfirmationModule", "TrimInputValueModule", "GameGroupService", "GameLimitsService", "CurrenciesResolver", "StakeRangesService", "CloneLimitsComponent", "CreateLimitsComponent", "GameLimitsComponent", "TabGameLimitsComponent", "TabGameLimitsRoutingModule", "TabGameLimitsModule", "imports", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "i1"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-game-limits/tab-game-limits.module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { FlexModule } from '@angular/flex-layout';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatPaginatorModule } from '@angular/material/paginator';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiControlMessagesModule, SwuiGridModule, SwuiSchemaTopFilterModule, SwuiSelectModule } from '@skywind-group/lib-swui';\nimport { BoConfirmationModule } from '../../../../../common/components/bo-confirmation/bo-confirmation.module';\nimport { TrimInputValueModule } from '../../../../../common/directives/trim-input-value/trim-input-value.module';\nimport { GameGroupService } from '../../../../../common/services/game-group.service';\nimport { GameLimitsService } from '../../../../../common/services/game-limits.service';\nimport { CurrenciesResolver } from '../../../../../common/services/resolvers/currencies.resolver';\nimport { StakeRangesService } from '../../../../../common/services/stake-ranges.service';\nimport { CloneLimitsComponent } from './forms/clone-limits.component';\nimport { CreateLimitsComponent } from './forms/create-limits.component';\nimport { GameLimitsComponent } from './game-limits.component';\nimport { TabGameLimitsComponent } from './tab-game-limits.component';\nimport { TabGameLimitsRoutingModule } from './tab-game-limits.routing';\n\n\n@NgModule({\n    imports: [\n        BoConfirmationModule,\n        CommonModule,\n        MatPaginatorModule,\n        ReactiveFormsModule,\n        TabGameLimitsRoutingModule,\n        TranslateModule.forChild(),\n        MatCardModule,\n        SwuiGridModule,\n        MatButtonModule,\n        SwuiSelectModule,\n        MatFormFieldModule,\n        MatDialogModule,\n        FlexModule,\n        MatIconModule,\n        MatExpansionModule,\n        MatInputModule,\n        SwuiSchemaTopFilterModule,\n        SwuiControlMessagesModule,\n        TrimInputValueModule,\n    ],\n  declarations: [\n    CloneLimitsComponent,\n    CreateLimitsComponent,\n    GameLimitsComponent,\n    TabGameLimitsComponent,\n  ],\n  providers: [\n    CurrenciesResolver,\n    GameGroupService,\n    StakeRangesService,\n    GameLimitsService,\n  ],\n})\nexport class TabGameLimitsModule {\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,UAAU,QAAQ,sBAAsB;AACjD,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,yBAAyB,EAAEC,cAAc,EAAEC,yBAAyB,EAAEC,gBAAgB,QAAQ,yBAAyB;AAChI,SAASC,oBAAoB,QAAQ,yEAAyE;AAC9G,SAASC,oBAAoB,QAAQ,2EAA2E;AAChH,SAASC,gBAAgB,QAAQ,mDAAmD;AACpF,SAASC,iBAAiB,QAAQ,oDAAoD;AACtF,SAASC,kBAAkB,QAAQ,8DAA8D;AACjG,SAASC,kBAAkB,QAAQ,qDAAqD;AACxF,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,qBAAqB,QAAQ,iCAAiC;AACvE,SAASC,mBAAmB,QAAQ,yBAAyB;AAC7D,SAASC,sBAAsB,QAAQ,6BAA6B;AACpE,SAASC,0BAA0B,QAAQ,2BAA2B;;;AAsCtE,OAAM,MAAOC,mBAAmB;;;uCAAnBA,mBAAmB;IAAA;EAAA;;;YAAnBA;IAAmB;EAAA;;;iBAPnB,CACTP,kBAAkB,EAClBF,gBAAgB,EAChBG,kBAAkB,EAClBF,iBAAiB,CAClB;MAAAS,OAAA,GA/BKZ,oBAAoB,EACpBhB,YAAY,EACZU,kBAAkB,EAClBR,mBAAmB,EACnBwB,0BAA0B,EAC1Bf,eAAe,CAACkB,QAAQ,EAAE,EAC1BzB,aAAa,EACbS,cAAc,EACdV,eAAe,EACfY,gBAAgB,EAChBR,kBAAkB,EAClBF,eAAe,EACfJ,UAAU,EACVO,aAAa,EACbF,kBAAkB,EAClBG,cAAc,EACdK,yBAAyB,EACzBF,yBAAyB,EACzBK,oBAAoB;IAAA;EAAA;;;2EAefU,mBAAmB;IAAAG,YAAA,GAZ5BR,oBAAoB,EACpBC,qBAAqB,EACrBC,mBAAmB,EACnBC,sBAAsB;IAAAG,OAAA,GAxBlBZ,oBAAoB,EACpBhB,YAAY,EACZU,kBAAkB,EAClBR,mBAAmB,EACnBwB,0BAA0B,EAAAK,EAAA,CAAApB,eAAA,EAE1BP,aAAa,EACbS,cAAc,EACdV,eAAe,EACfY,gBAAgB,EAChBR,kBAAkB,EAClBF,eAAe,EACfJ,UAAU,EACVO,aAAa,EACbF,kBAAkB,EAClBG,cAAc,EACdK,yBAAyB,EACzBF,yBAAyB,EACzBK,oBAAoB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}