{"ast": null, "code": "import { switchMap } from 'rxjs/operators';\nimport { PERMISSIONS_LIST } from '../../../../../../app.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../../../../common/services/entity-settings.service\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"@skywind-group/lib-swui\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/form-field\";\nimport * as i7 from \"@angular/material/input\";\nimport * as i8 from \"@angular/material/button\";\nfunction EntityPaymentRetryComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"button\", 7);\n    i0.ɵɵlistener(\"click\", function EntityPaymentRetryComponent_div_12_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSubmit());\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"ALL.save\"), \" \");\n  }\n}\nexport class EntityPaymentRetryComponent {\n  set entity(value) {\n    if (!value) return;\n    this._entity = value;\n  }\n  get entity() {\n    return this._entity;\n  }\n  set settings(value) {\n    if (!value) return;\n    this._settings = value;\n    this.patchForm(value);\n  }\n  get settings() {\n    return this._settings;\n  }\n  constructor(fb, service, translate, notifications, authService) {\n    this.fb = fb;\n    this.service = service;\n    this.translate = translate;\n    this.notifications = notifications;\n    this.allowedEdit = authService.allowedTo(PERMISSIONS_LIST.ENTITY_SETTINGS);\n    this.form = this.fb.group({\n      maxPaymentRetryAttempts: [{\n        value: '',\n        disabled: !this.allowedEdit\n      }],\n      minPaymentRetryTimeout: [{\n        value: '',\n        disabled: !this.allowedEdit\n      }]\n    });\n  }\n  onSubmit() {\n    this.submitted = true;\n    if (this.form.valid) {\n      this.service.patchSettings(this.processForm(this.form.value), this.entity.path).pipe(switchMap(() => this.translate.get('ENTITY_SETUP.ADDITIONAL.notificationPaymentRetrySettings'))).subscribe(message => {\n        this.notifications.success(message, '');\n      });\n    }\n  }\n  patchForm(value) {\n    const paymentRetry = {\n      maxPaymentRetryAttempts: value && value.maxPaymentRetryAttempts ? value.maxPaymentRetryAttempts : 0,\n      minPaymentRetryTimeout: value && value.minPaymentRetryTimeout ? value.minPaymentRetryTimeout : 0\n    };\n    this.form.patchValue(paymentRetry);\n  }\n  processForm(formValue) {\n    Object.keys(formValue).forEach(key => {\n      if (formValue[key] === '' || formValue[key] === 0 || formValue[key] === undefined) {\n        formValue[key] = null;\n      }\n    });\n    return formValue;\n  }\n  static {\n    this.ɵfac = function EntityPaymentRetryComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || EntityPaymentRetryComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.EntitySettingsService), i0.ɵɵdirectiveInject(i3.TranslateService), i0.ɵɵdirectiveInject(i4.SwuiNotificationsService), i0.ɵɵdirectiveInject(i4.SwHubAuthService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: EntityPaymentRetryComponent,\n      selectors: [[\"entity-payment-retry\"]],\n      inputs: {\n        entity: \"entity\",\n        settings: \"settings\"\n      },\n      decls: 13,\n      vars: 8,\n      consts: [[1, \"entity-game-retry\", 3, \"formGroup\"], [1, \"entity-game-retry__row\"], [\"appearance\", \"outline\"], [\"matInput\", \"\", \"type\", \"number\", \"step\", \"1\", \"min\", \"0\", \"formControlName\", \"maxPaymentRetryAttempts\"], [\"matInput\", \"\", \"type\", \"number\", \"step\", \"1\", \"min\", \"0\", \"formControlName\", \"minPaymentRetryTimeout\"], [\"class\", \"entity-game-retry__footer\", 4, \"ngIf\"], [1, \"entity-game-retry__footer\"], [\"mat-stroked-button\", \"\", \"color\", \"primary\", 3, \"click\"]],\n      template: function EntityPaymentRetryComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"form\", 0)(1, \"div\", 1)(2, \"mat-form-field\", 2)(3, \"mat-label\");\n          i0.ɵɵtext(4);\n          i0.ɵɵpipe(5, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(6, \"input\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"mat-form-field\", 2)(8, \"mat-label\");\n          i0.ɵɵtext(9);\n          i0.ɵɵpipe(10, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(11, \"input\", 4);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(12, EntityPaymentRetryComponent_div_12_Template, 4, 3, \"div\", 5);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"formGroup\", ctx.form);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 4, \"ENTITY_SETUP.ADDITIONAL.maxPaymentRetryAttempts\"));\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(10, 6, \"ENTITY_SETUP.ADDITIONAL.minPaymentRetryTimeout\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.allowedEdit);\n        }\n      },\n      dependencies: [i5.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MinValidator, i1.FormGroupDirective, i1.FormControlName, i6.MatFormField, i6.MatLabel, i7.MatInput, i8.MatButton, i3.TranslatePipe],\n      styles: [\".entity-game-retry__row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 32px;\\n}\\n.entity-game-retry__row[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.entity-game-retry__footer[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  padding-bottom: 16px;\\n}\\n\\n  .mat-form-field-label {\\n  text-overflow: unset !important;\\n  overflow: visible !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvYnVzaW5lc3MtbWFuYWdlbWVudC9jb21wb25lbnRzL2VudGl0aWVzL3RhYi1hZGRpdGlvbmFsL2VudGl0eS1wYXltZW50LXJldHJ5L2VudGl0eS1wYXltZW50LXJldHJ5LmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUNJO0VBQ0UsYUFBQTtFQUNBLFNBQUE7QUFBTjtBQUNNO0VBQ0UsT0FBQTtBQUNSO0FBRUU7RUFDRSxhQUFBO0VBQ0EseUJBQUE7RUFDQSxvQkFBQTtBQUFKOztBQUlBO0VBQ0UsK0JBQUE7RUFDQSw0QkFBQTtBQURGIiwic291cmNlc0NvbnRlbnQiOlsiLmVudGl0eS1nYW1lLXJldHJ5IHtcbiAgICAmX19yb3cge1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGdhcDogMzJweDtcbiAgICAgIG1hdC1mb3JtLWZpZWxkIHtcbiAgICAgICAgZmxleDogMTtcbiAgICAgIH1cbiAgICB9XG4gICZfX2Zvb3RlciB7XG4gICAgZGlzcGxheTogZmxleDtcbiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtZW5kO1xuICAgIHBhZGRpbmctYm90dG9tOiAxNnB4O1xuICB9XG59XG5cbjo6bmctZGVlcCAubWF0LWZvcm0tZmllbGQtbGFiZWwge1xuICB0ZXh0LW92ZXJmbG93OiB1bnNldCAhaW1wb3J0YW50O1xuICBvdmVyZmxvdzogdmlzaWJsZSAhaW1wb3J0YW50O1xufVxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["switchMap", "PERMISSIONS_LIST", "i0", "ɵɵelementStart", "ɵɵlistener", "EntityPaymentRetryComponent_div_12_Template_button_click_1_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onSubmit", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "EntityPaymentRetryComponent", "entity", "value", "_entity", "settings", "_settings", "patchForm", "constructor", "fb", "service", "translate", "notifications", "authService", "allowedEdit", "allowedTo", "ENTITY_SETTINGS", "form", "group", "maxPaymentRetryAttempts", "disabled", "minPaymentRetryTimeout", "submitted", "valid", "patchSettings", "processForm", "path", "pipe", "get", "subscribe", "message", "success", "paymentRetry", "patchValue", "formValue", "Object", "keys", "for<PERSON>ach", "key", "undefined", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "EntitySettingsService", "i3", "TranslateService", "i4", "SwuiNotificationsService", "SwHubAuthService", "selectors", "inputs", "decls", "vars", "consts", "template", "EntityPaymentRetryComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵtemplate", "EntityPaymentRetryComponent_div_12_Template", "ɵɵproperty", "ɵɵtextInterpolate"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-additional/entity-payment-retry/entity-payment-retry.component.ts", "/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-additional/entity-payment-retry/entity-payment-retry.component.html"], "sourcesContent": ["import { Component, Input } from '@angular/core';\nimport { FormBuilder, FormGroup } from '@angular/forms';\nimport { TranslateService } from '@ngx-translate/core';\nimport { SwHubAuthService, SwuiNotificationsService } from '@skywind-group/lib-swui';\nimport { switchMap } from 'rxjs/operators';\nimport { EntitySettingsModel } from '../../../../../../common/models/entity-settings.model';\n\nimport { Entity } from '../../../../../../common/models/entity.model';\nimport { EntitySettingsService } from '../../../../../../common/services/entity-settings.service';\nimport { PERMISSIONS_LIST } from '../../../../../../app.constants';\n\nexport interface EntityPaymentRetry {\n  maxPaymentRetryAttempts: number;\n  minPaymentRetryTimeout: number;\n}\n\n@Component({\n  selector: 'entity-payment-retry',\n  templateUrl: './entity-payment-retry.component.html',\n  styleUrls: ['./entity-payment-retry.component.scss']\n})\n\nexport class EntityPaymentRetryComponent {\n\n  @Input()\n  public set entity( value: Entity ) {\n    if (!value) return;\n    this._entity = value;\n  }\n\n  public get entity(): Entity {\n    return this._entity;\n  }\n\n  @Input()\n  public set settings( value: EntitySettingsModel ) {\n    if (!value) return;\n    this._settings = value;\n    this.patchForm(value);\n  }\n\n  public get settings(): EntitySettingsModel {\n    return this._settings;\n  }\n\n  public form: FormGroup;\n  public submitted: boolean;\n\n  public readonly allowedEdit: boolean;\n\n  private _entity: Entity;\n  private _settings: EntitySettingsModel;\n\n  constructor( private fb: FormBuilder,\n               private service: EntitySettingsService<EntitySettingsModel>,\n               private translate: TranslateService,\n               private notifications: SwuiNotificationsService,\n               authService: SwHubAuthService\n  ) {\n    this.allowedEdit = authService.allowedTo(PERMISSIONS_LIST.ENTITY_SETTINGS);\n    this.form = this.fb.group({\n      maxPaymentRetryAttempts: [{ value: '', disabled: !this.allowedEdit }],\n      minPaymentRetryTimeout: [{ value: '', disabled: !this.allowedEdit }]\n    });\n  }\n\n  onSubmit() {\n    this.submitted = true;\n    if (this.form.valid) {\n      this.service.patchSettings(this.processForm(this.form.value), this.entity.path).pipe(\n        switchMap(() => this.translate.get('ENTITY_SETUP.ADDITIONAL.notificationPaymentRetrySettings'))\n      )\n      .subscribe(message => {\n        this.notifications.success(message, '');\n      });\n    }\n  }\n  private patchForm(value: EntitySettingsModel): void {\n    const paymentRetry = {\n      maxPaymentRetryAttempts: value && value.maxPaymentRetryAttempts ? value.maxPaymentRetryAttempts : 0,\n      minPaymentRetryTimeout: value && value.minPaymentRetryTimeout ? value.minPaymentRetryTimeout : 0,\n    };\n    this.form.patchValue(paymentRetry);\n  }\n\n  private processForm(formValue: EntityPaymentRetry): EntityPaymentRetry {\n    Object.keys(formValue).forEach(key => {\n      if (formValue[key] === '' || formValue[key] === 0 || formValue[key] === undefined) {\n        formValue[key] = null;\n      }\n    });\n    return formValue;\n  }\n}\n", "<form [formGroup]=\"form\" class=\"entity-game-retry\">\n  <div class=\"entity-game-retry__row\">\n    <mat-form-field appearance=\"outline\">\n      <mat-label>{{'ENTITY_SETUP.ADDITIONAL.maxPaymentRetryAttempts' | translate}}</mat-label>\n      <input matInput type=\"number\" step=\"1\" min=\"0\" formControlName=\"maxPaymentRetryAttempts\">\n    </mat-form-field>\n    <mat-form-field appearance=\"outline\">\n      <mat-label>{{'ENTITY_SETUP.ADDITIONAL.minPaymentRetryTimeout' | translate}}</mat-label>\n      <input matInput type=\"number\" step=\"1\" min=\"0\" formControlName=\"minPaymentRetryTimeout\">\n    </mat-form-field>\n  </div>\n  <div class=\"entity-game-retry__footer\" *ngIf=\"allowedEdit\">\n    <button mat-stroked-button color=\"primary\" (click)=\"onSubmit()\">\n      {{'ALL.save' | translate}}\n    </button>\n  </div>\n</form>\n"], "mappings": "AAIA,SAASA,SAAS,QAAQ,gBAAgB;AAK1C,SAASC,gBAAgB,QAAQ,iCAAiC;;;;;;;;;;;;;ICG9DC,EADF,CAAAC,cAAA,aAA2D,gBACO;IAArBD,EAAA,CAAAE,UAAA,mBAAAC,oEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,EAAU;IAAA,EAAC;IAC7DT,EAAA,CAAAU,MAAA,GACF;;IACFV,EADE,CAAAW,YAAA,EAAS,EACL;;;IAFFX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAb,EAAA,CAAAc,WAAA,wBACF;;;ADQJ,OAAM,MAAOC,2BAA2B;EAEtC,IACWC,MAAMA,CAAEC,KAAa;IAC9B,IAAI,CAACA,KAAK,EAAE;IACZ,IAAI,CAACC,OAAO,GAAGD,KAAK;EACtB;EAEA,IAAWD,MAAMA,CAAA;IACf,OAAO,IAAI,CAACE,OAAO;EACrB;EAEA,IACWC,QAAQA,CAAEF,KAA0B;IAC7C,IAAI,CAACA,KAAK,EAAE;IACZ,IAAI,CAACG,SAAS,GAAGH,KAAK;IACtB,IAAI,CAACI,SAAS,CAACJ,KAAK,CAAC;EACvB;EAEA,IAAWE,QAAQA,CAAA;IACjB,OAAO,IAAI,CAACC,SAAS;EACvB;EAUAE,YAAqBC,EAAe,EACfC,OAAmD,EACnDC,SAA2B,EAC3BC,aAAuC,EAC/CC,WAA6B;IAJrB,KAAAJ,EAAE,GAAFA,EAAE;IACF,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,aAAa,GAAbA,aAAa;IAGhC,IAAI,CAACE,WAAW,GAAGD,WAAW,CAACE,SAAS,CAAC9B,gBAAgB,CAAC+B,eAAe,CAAC;IAC1E,IAAI,CAACC,IAAI,GAAG,IAAI,CAACR,EAAE,CAACS,KAAK,CAAC;MACxBC,uBAAuB,EAAE,CAAC;QAAEhB,KAAK,EAAE,EAAE;QAAEiB,QAAQ,EAAE,CAAC,IAAI,CAACN;MAAW,CAAE,CAAC;MACrEO,sBAAsB,EAAE,CAAC;QAAElB,KAAK,EAAE,EAAE;QAAEiB,QAAQ,EAAE,CAAC,IAAI,CAACN;MAAW,CAAE;KACpE,CAAC;EACJ;EAEAnB,QAAQA,CAAA;IACN,IAAI,CAAC2B,SAAS,GAAG,IAAI;IACrB,IAAI,IAAI,CAACL,IAAI,CAACM,KAAK,EAAE;MACnB,IAAI,CAACb,OAAO,CAACc,aAAa,CAAC,IAAI,CAACC,WAAW,CAAC,IAAI,CAACR,IAAI,CAACd,KAAK,CAAC,EAAE,IAAI,CAACD,MAAM,CAACwB,IAAI,CAAC,CAACC,IAAI,CAClF3C,SAAS,CAAC,MAAM,IAAI,CAAC2B,SAAS,CAACiB,GAAG,CAAC,0DAA0D,CAAC,CAAC,CAChG,CACAC,SAAS,CAACC,OAAO,IAAG;QACnB,IAAI,CAAClB,aAAa,CAACmB,OAAO,CAACD,OAAO,EAAE,EAAE,CAAC;MACzC,CAAC,CAAC;IACJ;EACF;EACQvB,SAASA,CAACJ,KAA0B;IAC1C,MAAM6B,YAAY,GAAG;MACnBb,uBAAuB,EAAEhB,KAAK,IAAIA,KAAK,CAACgB,uBAAuB,GAAGhB,KAAK,CAACgB,uBAAuB,GAAG,CAAC;MACnGE,sBAAsB,EAAElB,KAAK,IAAIA,KAAK,CAACkB,sBAAsB,GAAGlB,KAAK,CAACkB,sBAAsB,GAAG;KAChG;IACD,IAAI,CAACJ,IAAI,CAACgB,UAAU,CAACD,YAAY,CAAC;EACpC;EAEQP,WAAWA,CAACS,SAA6B;IAC/CC,MAAM,CAACC,IAAI,CAACF,SAAS,CAAC,CAACG,OAAO,CAACC,GAAG,IAAG;MACnC,IAAIJ,SAAS,CAACI,GAAG,CAAC,KAAK,EAAE,IAAIJ,SAAS,CAACI,GAAG,CAAC,KAAK,CAAC,IAAIJ,SAAS,CAACI,GAAG,CAAC,KAAKC,SAAS,EAAE;QACjFL,SAAS,CAACI,GAAG,CAAC,GAAG,IAAI;MACvB;IACF,CAAC,CAAC;IACF,OAAOJ,SAAS;EAClB;;;uCAtEWjC,2BAA2B,EAAAf,EAAA,CAAAsD,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAxD,EAAA,CAAAsD,iBAAA,CAAAG,EAAA,CAAAC,qBAAA,GAAA1D,EAAA,CAAAsD,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAA5D,EAAA,CAAAsD,iBAAA,CAAAO,EAAA,CAAAC,wBAAA,GAAA9D,EAAA,CAAAsD,iBAAA,CAAAO,EAAA,CAAAE,gBAAA;IAAA;EAAA;;;YAA3BhD,2BAA2B;MAAAiD,SAAA;MAAAC,MAAA;QAAAjD,MAAA;QAAAG,QAAA;MAAA;MAAA+C,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCnBlCvE,EAHN,CAAAC,cAAA,cAAmD,aACb,wBACG,gBACxB;UAAAD,EAAA,CAAAU,MAAA,GAAiE;;UAAAV,EAAA,CAAAW,YAAA,EAAY;UACxFX,EAAA,CAAAyE,SAAA,eAAyF;UAC3FzE,EAAA,CAAAW,YAAA,EAAiB;UAEfX,EADF,CAAAC,cAAA,wBAAqC,gBACxB;UAAAD,EAAA,CAAAU,MAAA,GAAgE;;UAAAV,EAAA,CAAAW,YAAA,EAAY;UACvFX,EAAA,CAAAyE,SAAA,gBAAwF;UAE5FzE,EADE,CAAAW,YAAA,EAAiB,EACb;UACNX,EAAA,CAAA0E,UAAA,KAAAC,2CAAA,iBAA2D;UAK7D3E,EAAA,CAAAW,YAAA,EAAO;;;UAhBDX,EAAA,CAAA4E,UAAA,cAAAJ,GAAA,CAAAzC,IAAA,CAAkB;UAGP/B,EAAA,CAAAY,SAAA,GAAiE;UAAjEZ,EAAA,CAAA6E,iBAAA,CAAA7E,EAAA,CAAAc,WAAA,0DAAiE;UAIjEd,EAAA,CAAAY,SAAA,GAAgE;UAAhEZ,EAAA,CAAA6E,iBAAA,CAAA7E,EAAA,CAAAc,WAAA,0DAAgE;UAIvCd,EAAA,CAAAY,SAAA,GAAiB;UAAjBZ,EAAA,CAAA4E,UAAA,SAAAJ,GAAA,CAAA5C,WAAA,CAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}