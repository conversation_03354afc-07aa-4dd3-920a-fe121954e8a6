{"ast": null, "code": "import { RowAction, SwuiGridComponent, SwuiGridDataService, SwuiTopFilterDataService } from '@skywind-group/lib-swui';\nimport { Subject, throwError } from 'rxjs';\nimport { catchError, filter, switchMap, take, takeUntil, tap } from 'rxjs/operators';\nimport { PERMISSIONS_NAMES } from '../../../../app.constants';\nimport { UserEditorDialogComponent } from '../../../../common/components/mat-user-editor/user-editor-dialog.component';\nimport { Entity } from '../../../../common/models/entity.model';\nimport { UserService } from '../../../../common/services/user.service';\nimport { DeleteUserDialogComponent } from '../../../business-management/components/entities/tab-users/dialogs/delete-user-dialog.component';\nimport { TwofaResetDialogComponent } from '../../../business-management/components/entities/tab-users/dialogs/twofa-reset-dialog.component';\nimport { UnblockUserDialogComponent } from '../../../business-management/components/entities/tab-users/dialogs/unblock-user-dialog.component';\nimport { SCHEMA_FILTER, SCHEMA_LIST } from '../../schema';\nimport { User } from '../../user.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../common/services/user.service\";\nimport * as i2 from \"@skywind-group/lib-swui\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"../../../../common/services/user-actions.service\";\nimport * as i6 from \"@angular/material/dialog\";\nimport * as i7 from \"../../../../common/services/entity-data-source.service\";\nimport * as i8 from \"@angular/material/icon\";\nimport * as i9 from \"@angular/material/button\";\nimport * as i10 from \"@angular/material/tooltip\";\nimport * as i11 from \"../../../../common/components/download-csv/download-csv.component\";\nconst COMPONENT_NAME = 'user-list';\nexport class UsersListComponent {\n  constructor(service, notifications, translate, {\n    snapshot: {\n      data: {\n        brief\n      }\n    }\n  }, authService, userActionsService, dialog, entityDataSourceService, hubEntityService) {\n    this.service = service;\n    this.notifications = notifications;\n    this.translate = translate;\n    this.authService = authService;\n    this.userActionsService = userActionsService;\n    this.dialog = dialog;\n    this.entityDataSourceService = entityDataSourceService;\n    this.hubEntityService = hubEntityService;\n    this.schema = SCHEMA_LIST;\n    this.filterSchema = SCHEMA_FILTER;\n    this.items = [];\n    this.actions = [];\n    this.panelActions = [];\n    this.componentName = COMPONENT_NAME;\n    this.loading = false;\n    this.destroyed$ = new Subject();\n    this.entity = brief ? new Entity(brief) : undefined;\n    this.selectedEntity = this.entity;\n    this.setActions();\n  }\n  ngOnInit() {\n    this.entityDataSourceService.show();\n    this.hubEntityService.entitySelected$.pipe(filter(Boolean), takeUntil(this.destroyed$)).subscribe(entity => {\n      this.selectedEntity = new Entity(entity);\n    });\n  }\n  ngOnDestroy() {\n    this.destroyed$.next();\n    this.destroyed$.complete();\n    this.entityDataSourceService.hide();\n  }\n  downloadCsv() {\n    this.loading = true;\n    this.service.downloadCsv(this.entity?.type, this.selectedEntity?.path).pipe(catchError(err => {\n      this.loading = false;\n      return throwError(err);\n    }), takeUntil(this.destroyed$)).subscribe(() => {\n      this.loading = false;\n    });\n  }\n  printPage() {\n    window.print();\n  }\n  exportPage() {\n    const {\n      dataSource: {\n        data\n      },\n      displayedColumns,\n      paginator: {\n        pageIndex\n      }\n    } = this.grid;\n    this.service.exportPage(this.entity?.type, this.selectedEntity?.path, data, displayedColumns, pageIndex + 1);\n  }\n  showModifyUserDialog(user = new User()) {\n    this.dialog.open(UserEditorDialogComponent, {\n      width: '700px',\n      data: {\n        user,\n        entity: this.selectedEntity,\n        brief: this.entity\n      },\n      disableClose: true\n    }).afterClosed().pipe(take(1)).subscribe(() => {\n      this.grid.dataSource.loadData();\n    });\n  }\n  showUnblockUserConfirmDialog(user) {\n    let dialogUser = {\n      ...user\n    };\n    this.service.getUserProfile(dialogUser).pipe(switchMap(userProfile => this.dialog.open(UnblockUserDialogComponent, {\n      width: '700px',\n      data: {\n        user: {\n          ...dialogUser,\n          ...userProfile\n        }\n      },\n      disableClose: true\n    }).afterClosed()), filter(data => !!data), switchMap(data => this.userActionsService.onUnblockUserAction(data, this.entity)), tap(() => this.notifications.success(this.translate.instant('ENTITY_SETUP.USERS.userUnblocked', user))), takeUntil(this.destroyed$)).subscribe(() => {\n      this.grid.dataSource.loadData();\n    });\n  }\n  showResetTwoAuthDialog(user) {\n    const dialogUser = {\n      ...user\n    };\n    this.dialog.open(TwofaResetDialogComponent, {\n      width: '600px',\n      data: {\n        entity: this.entity,\n        user: dialogUser\n      },\n      disableClose: true\n    }).afterClosed().pipe(filter(data => !!data), switchMap(data => this.userActionsService.onTwofaResetAction(data)), tap(() => this.notifications.success(this.translate.instant('ENTITY_SETUP.USERS.twofaResetSuccessfull', user))), takeUntil(this.destroyed$)).subscribe(() => this.grid.dataSource.loadData());\n  }\n  showDeleteConfirmDialog(user) {\n    const dialogUser = {\n      ...user\n    };\n    this.dialog.open(DeleteUserDialogComponent, {\n      width: '600px',\n      data: {\n        user: dialogUser\n      },\n      disableClose: true\n    }).afterClosed().pipe(filter(data => !!data), switchMap(() => this.userActionsService.onDeleteUserAction(user, this.entity)), tap(() => this.notifications.success(this.translate.instant('ENTITY_SETUP.USERS.userRemoved', user))), catchError(error => {\n      this.notifications.error(error.statusText, `Status: ${error.status}`);\n      return throwError(error);\n    }), takeUntil(this.destroyed$)).subscribe(() => this.grid.dataSource.loadData());\n  }\n  setActions() {\n    if (this.userActionsService.editUserEnabled()) {\n      this.actions.push(new RowAction({\n        title: 'ENTITY_SETUP.USERS.editUserAction',\n        icon: 'edit',\n        fn: this.showModifyUserDialog.bind(this)\n      }));\n    }\n    if (this.userActionsService.unblockUserGranted(this.entity)) {\n      this.actions.push(new RowAction({\n        title: 'ENTITY_SETUP.USERS.MODALS.unblockUserModalTitle',\n        icon: 'lock_open',\n        fn: this.showUnblockUserConfirmDialog.bind(this),\n        canActivateFn: row => {\n          return row.status === 'locked_by_auth';\n        }\n      }));\n    }\n    if (this.userActionsService.twofaResetEnabled()) {\n      this.actions.push(new RowAction({\n        title: 'ENTITY_SETUP.USERS.reset2faAction',\n        icon: 'security',\n        fn: this.showResetTwoAuthDialog.bind(this),\n        canActivateFn: row => !!row.twoFAInfo && ('defaultAuthType' in row.twoFAInfo || 'authTypes' in row.twoFAInfo && row.twoFAInfo.authTypes.length)\n      }));\n    }\n    if (this.userActionsService.deleteUserGranted(this.entity)) {\n      this.actions.push(new RowAction({\n        title: 'ENTITY_SETUP.USERS.deleteUserAction',\n        icon: 'delete',\n        fn: this.showDeleteConfirmDialog.bind(this),\n        canActivateFn: row => row.username !== this.authService.username\n      }));\n    }\n    this.panelActions.push({\n      title: 'USERS.createUser',\n      color: 'primary',\n      icon: 'person_add',\n      actionFn: () => this.showModifyUserDialog(),\n      availableFn: () => this.authService.allowedTo([PERMISSIONS_NAMES.USER, PERMISSIONS_NAMES.USER_CREATE, PERMISSIONS_NAMES.KEYENTITY_USER, PERMISSIONS_NAMES.KEYENTITY_USER_CREATE])\n    });\n  }\n  static {\n    this.ɵfac = function UsersListComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || UsersListComponent)(i0.ɵɵdirectiveInject(i1.UserService), i0.ɵɵdirectiveInject(i2.SwuiNotificationsService), i0.ɵɵdirectiveInject(i3.TranslateService), i0.ɵɵdirectiveInject(i4.ActivatedRoute), i0.ɵɵdirectiveInject(i2.SwHubAuthService), i0.ɵɵdirectiveInject(i5.UserActionsService), i0.ɵɵdirectiveInject(i6.MatDialog), i0.ɵɵdirectiveInject(i7.EntityDataSourceService), i0.ɵɵdirectiveInject(i2.SwHubEntityService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: UsersListComponent,\n      selectors: [[\"user-list\"]],\n      viewQuery: function UsersListComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(SwuiGridComponent, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.grid = _t.first);\n        }\n      },\n      features: [i0.ɵɵProvidersFeature([UserService, SwuiTopFilterDataService, {\n        provide: SwuiGridDataService,\n        useExisting: UserService\n      }])],\n      decls: 12,\n      vars: 11,\n      consts: [[3, \"title\", \"actions\"], [1, \"p-32\", \"sw-grid-layout\"], [1, \"sw-grid-layout__table\"], [3, \"schema\"], [\"sortActive\", \"createdAt\", \"sortDirection\", \"desc\", 3, \"schema\", \"columnsManagement\", \"gridId\", \"rowActionsColumnTitle\", \"rowActions\", \"savedFilteredPageName\", \"useHubEntity\"], [3, \"downloadCsv\", \"loading\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Export current page\", 3, \"click\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Print current page\", 3, \"click\"]],\n      template: function UsersListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"lib-swui-page-panel\", 0);\n          i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵelement(3, \"lib-swui-schema-top-filter\", 3);\n          i0.ɵɵelementStart(4, \"lib-swui-grid\", 4)(5, \"download-csv\", 5);\n          i0.ɵɵlistener(\"downloadCsv\", function UsersListComponent_Template_download_csv_downloadCsv_5_listener() {\n            return ctx.downloadCsv();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function UsersListComponent_Template_button_click_6_listener() {\n            return ctx.exportPage();\n          });\n          i0.ɵɵelementStart(7, \"mat-icon\");\n          i0.ɵɵtext(8, \"archive\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"button\", 7);\n          i0.ɵɵlistener(\"click\", function UsersListComponent_Template_button_click_9_listener() {\n            return ctx.printPage();\n          });\n          i0.ɵɵelementStart(10, \"mat-icon\");\n          i0.ɵɵtext(11, \"print\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"title\", \"MENU_SECTIONS.usersList\")(\"actions\", ctx.panelActions);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"schema\", ctx.filterSchema);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"schema\", ctx.schema)(\"columnsManagement\", true)(\"gridId\", ctx.componentName)(\"rowActionsColumnTitle\", \"\")(\"rowActions\", ctx.actions)(\"savedFilteredPageName\", \"users\")(\"useHubEntity\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"loading\", ctx.loading);\n        }\n      },\n      dependencies: [i2.SwuiPagePanelComponent, i2.SwuiGridComponent, i8.MatIcon, i9.MatIconButton, i10.MatTooltip, i2.SwuiSchemaTopFilterComponent, i11.DownloadCsvComponent],\n      styles: [\".role-cell {\\n  max-width: 150px;\\n}\\n.role-cell .widget {\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvdXNlcnMvY29tcG9uZW50cy9saXN0L3VzZXJzLWxpc3QuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxnQkFBQTtBQUNGO0FBQ0U7RUFDRSxnQkFBQTtFQUNBLHVCQUFBO0FBQ0oiLCJzb3VyY2VzQ29udGVudCI6WyIucm9sZS1jZWxsIHtcbiAgbWF4LXdpZHRoOiAxNTBweDtcblxuICAud2lkZ2V0IHtcbiAgICBvdmVyZmxvdzogaGlkZGVuO1xuICAgIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzO1xuICB9XG59XG4iXSwic291cmNlUm9vdCI6IiJ9 */\"],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["RowAction", "SwuiGridComponent", "SwuiGridDataService", "SwuiTopFilterDataService", "Subject", "throwError", "catchError", "filter", "switchMap", "take", "takeUntil", "tap", "PERMISSIONS_NAMES", "UserEditorDialogComponent", "Entity", "UserService", "DeleteUserDialogComponent", "TwofaResetDialogComponent", "UnblockUserDialogComponent", "SCHEMA_FILTER", "SCHEMA_LIST", "User", "COMPONENT_NAME", "UsersListComponent", "constructor", "service", "notifications", "translate", "snapshot", "data", "brief", "authService", "userActionsService", "dialog", "entityDataSourceService", "hubEntityService", "schema", "filterSchema", "items", "actions", "panelActions", "componentName", "loading", "destroyed$", "entity", "undefined", "selected<PERSON><PERSON><PERSON>", "setActions", "ngOnInit", "show", "entitySelected$", "pipe", "Boolean", "subscribe", "ngOnDestroy", "next", "complete", "hide", "downloadCsv", "type", "path", "err", "printPage", "window", "print", "exportPage", "dataSource", "displayedColumns", "paginator", "pageIndex", "grid", "showModifyUserDialog", "user", "open", "width", "disableClose", "afterClosed", "loadData", "showUnblockUserConfirmDialog", "dialogUser", "getUserProfile", "userProfile", "onUnblockUserAction", "success", "instant", "showResetTwoAuthDialog", "onTwofaResetAction", "showDeleteConfirmDialog", "onDeleteUserAction", "error", "statusText", "status", "editUserEnabled", "push", "title", "icon", "fn", "bind", "unblockUserGranted", "canActivateFn", "row", "twofaResetEnabled", "twoFAInfo", "authTypes", "length", "deleteUserGranted", "username", "color", "actionFn", "availableFn", "allowedTo", "USER", "USER_CREATE", "KEYENTITY_USER", "KEYENTITY_USER_CREATE", "i0", "ɵɵdirectiveInject", "i1", "i2", "SwuiNotificationsService", "i3", "TranslateService", "i4", "ActivatedRoute", "SwHubAuthService", "i5", "UserActionsService", "i6", "MatDialog", "i7", "EntityDataSourceService", "SwHubEntityService", "selectors", "viewQuery", "UsersListComponent_Query", "rf", "ctx", "provide", "useExisting", "decls", "vars", "consts", "template", "UsersListComponent_Template", "ɵɵelement", "ɵɵelementStart", "ɵɵlistener", "UsersListComponent_Template_download_csv_downloadCsv_5_listener", "ɵɵelementEnd", "UsersListComponent_Template_button_click_6_listener", "ɵɵtext", "UsersListComponent_Template_button_click_9_listener", "ɵɵproperty", "ɵɵadvance"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/users/components/list/users-list.component.ts", "/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/users/components/list/users-list.component.html"], "sourcesContent": ["import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';\nimport { MatDialog } from '@angular/material/dialog';\nimport { ActivatedRoute } from '@angular/router';\nimport { TranslateService } from '@ngx-translate/core';\nimport {\n  PanelAction,\n  RowAction,\n  SwHubAuthService,\n  SwHubEntityService,\n  SwuiGridComponent,\n  SwuiGridDataService,\n  SwuiNotificationsService,\n  SwuiTopFilterDataService\n} from '@skywind-group/lib-swui';\nimport { Subject, throwError } from 'rxjs';\nimport { catchError, filter, switchMap, take, takeUntil, tap } from 'rxjs/operators';\nimport { PERMISSIONS_NAMES } from '../../../../app.constants';\nimport {\n  UserEditorDialogComponent,\n  UserEditorDialogData\n} from '../../../../common/components/mat-user-editor/user-editor-dialog.component';\nimport { <PERSON>tity } from '../../../../common/models/entity.model';\nimport { EntityDataSourceService } from '../../../../common/services/entity-data-source.service';\nimport { UserActionsService } from '../../../../common/services/user-actions.service';\nimport { UserService } from '../../../../common/services/user.service';\nimport { DeleteUserDialogComponent } from '../../../business-management/components/entities/tab-users/dialogs/delete-user-dialog.component';\nimport {\n  TwofaResetDialogComponent,\n  TwofaResetDialogResult\n} from '../../../business-management/components/entities/tab-users/dialogs/twofa-reset-dialog.component';\nimport {\n  UnblockUserDialogComponent\n} from '../../../business-management/components/entities/tab-users/dialogs/unblock-user-dialog.component';\nimport { SCHEMA_FILTER, SCHEMA_LIST } from '../../schema';\nimport { User } from '../../user.model';\n\nconst COMPONENT_NAME: string = 'user-list';\n\n@Component({\n  selector: COMPONENT_NAME,\n  encapsulation: ViewEncapsulation.None,\n  templateUrl: './users-list.component.html',\n  styleUrls: ['users-list.component.scss'],\n  providers: [\n    UserService,\n    SwuiTopFilterDataService,\n    { provide: SwuiGridDataService, useExisting: UserService },\n  ]\n})\nexport class UsersListComponent implements OnInit, OnDestroy {\n  @ViewChild(SwuiGridComponent, { static: true }) grid: SwuiGridComponent<User>;\n\n  schema = SCHEMA_LIST;\n  filterSchema = SCHEMA_FILTER;\n\n  items: User[] = [];\n  readonly actions: RowAction[] = [];\n  readonly panelActions: PanelAction[] = [];\n  readonly entity?: Entity;\n  componentName = COMPONENT_NAME;\n  loading: boolean = false;\n\n  private selectedEntity?: Entity;\n  private readonly destroyed$ = new Subject<void>();\n\n  constructor(private service: UserService<User>,\n    private notifications: SwuiNotificationsService,\n    private translate: TranslateService,\n    { snapshot: { data: { brief } } }: ActivatedRoute,\n    private authService: SwHubAuthService,\n    private userActionsService: UserActionsService,\n    private dialog: MatDialog,\n    private readonly entityDataSourceService: EntityDataSourceService,\n    private readonly hubEntityService: SwHubEntityService,\n  ) {\n    this.entity = brief ? new Entity(brief) : undefined;\n    this.selectedEntity = this.entity;\n    this.setActions();\n  }\n\n  ngOnInit() {\n    this.entityDataSourceService.show();\n\n    this.hubEntityService.entitySelected$.pipe(\n      filter(Boolean),\n      takeUntil(this.destroyed$),\n    ).subscribe(entity => {\n      this.selectedEntity = new Entity(entity);\n    });\n  }\n\n  ngOnDestroy(): void {\n    this.destroyed$.next();\n    this.destroyed$.complete();\n    this.entityDataSourceService.hide();\n  }\n\n  downloadCsv() {\n    this.loading = true;\n    this.service.downloadCsv(this.entity?.type, this.selectedEntity?.path)\n      .pipe(\n        catchError((err) => {\n          this.loading = false;\n          return throwError(err);\n        }),\n        takeUntil(this.destroyed$)\n      ).subscribe(() => {\n        this.loading = false;\n      });\n  }\n\n  printPage() {\n    window.print();\n  }\n\n  exportPage() {\n    const { dataSource: { data }, displayedColumns, paginator: { pageIndex } } = this.grid;\n    this.service.exportPage(this.entity?.type, this.selectedEntity?.path, data, displayedColumns, pageIndex + 1);\n  }\n\n  private showModifyUserDialog(user = new User()) {\n    this.dialog.open<any, UserEditorDialogData>(UserEditorDialogComponent, {\n      width: '700px',\n      data: {\n        user,\n        entity: this.selectedEntity,\n        brief: this.entity\n      },\n      disableClose: true\n    }).afterClosed()\n      .pipe(take(1))\n      .subscribe(() => {\n        this.grid.dataSource.loadData();\n      });\n  }\n\n  private showUnblockUserConfirmDialog(user: User) {\n    let dialogUser = { ...user };\n    this.service.getUserProfile(dialogUser)\n      .pipe(\n        switchMap((userProfile) =>\n          this.dialog.open(UnblockUserDialogComponent, {\n            width: '700px',\n            data: { user: { ...dialogUser, ...userProfile } },\n            disableClose: true\n          }).afterClosed()),\n        filter((data) => !!data),\n        switchMap((data) => this.userActionsService.onUnblockUserAction(data, this.entity)),\n        tap(() => this.notifications.success(\n          this.translate.instant('ENTITY_SETUP.USERS.userUnblocked', user))),\n        takeUntil(this.destroyed$)\n      ).subscribe(() => {\n        this.grid.dataSource.loadData();\n      });\n  }\n\n  private showResetTwoAuthDialog(user: User) {\n    const dialogUser = { ...user };\n    this.dialog.open(TwofaResetDialogComponent, {\n      width: '600px',\n      data: {\n        entity: this.entity,\n        user: dialogUser\n      },\n      disableClose: true\n    }).afterClosed().pipe(\n      filter((data) => !!data),\n      switchMap((data: TwofaResetDialogResult) => this.userActionsService.onTwofaResetAction(data)),\n      tap(() => this.notifications.success(this.translate.instant('ENTITY_SETUP.USERS.twofaResetSuccessfull', user))),\n      takeUntil(this.destroyed$)\n    ).subscribe(() => this.grid.dataSource.loadData());\n  }\n\n  private showDeleteConfirmDialog(user: User) {\n    const dialogUser = { ...user };\n    this.dialog.open(DeleteUserDialogComponent, {\n      width: '600px',\n      data: { user: dialogUser },\n      disableClose: true\n    }).afterClosed()\n      .pipe(\n        filter((data) => !!data),\n        switchMap(() => this.userActionsService.onDeleteUserAction(user, this.entity)),\n        tap(() => this.notifications.success(this.translate.instant('ENTITY_SETUP.USERS.userRemoved', user))),\n        catchError((error) => {\n          this.notifications.error(error.statusText, `Status: ${error.status}`);\n          return throwError(error);\n        }),\n        takeUntil(this.destroyed$)\n      )\n      .subscribe(() => this.grid.dataSource.loadData());\n  }\n\n  private setActions() {\n    if (this.userActionsService.editUserEnabled()) {\n      this.actions.push(new RowAction({\n        title: 'ENTITY_SETUP.USERS.editUserAction',\n        icon: 'edit',\n        fn: this.showModifyUserDialog.bind(this)\n      }));\n    }\n\n    if (this.userActionsService.unblockUserGranted(this.entity)) {\n      this.actions.push(new RowAction({\n        title: 'ENTITY_SETUP.USERS.MODALS.unblockUserModalTitle',\n        icon: 'lock_open',\n        fn: this.showUnblockUserConfirmDialog.bind(this),\n        canActivateFn: (row: User) => {\n          return row.status === 'locked_by_auth';\n        }\n      }));\n    }\n\n    if (this.userActionsService.twofaResetEnabled()) {\n      this.actions.push(new RowAction({\n        title: 'ENTITY_SETUP.USERS.reset2faAction',\n        icon: 'security',\n        fn: this.showResetTwoAuthDialog.bind(this),\n        canActivateFn: (row: User) => !!row.twoFAInfo\n          && (('defaultAuthType' in row.twoFAInfo) || ('authTypes' in row.twoFAInfo && row.twoFAInfo.authTypes.length))\n      }));\n    }\n\n    if (this.userActionsService.deleteUserGranted(this.entity)) {\n      this.actions.push(new RowAction({\n        title: 'ENTITY_SETUP.USERS.deleteUserAction',\n        icon: 'delete',\n        fn: this.showDeleteConfirmDialog.bind(this),\n        canActivateFn: (row: User) => row.username !== this.authService.username\n      }));\n    }\n\n    this.panelActions.push(\n      {\n        title: 'USERS.createUser',\n        color: 'primary',\n        icon: 'person_add',\n        actionFn: () => this.showModifyUserDialog(),\n        availableFn: () => this.authService.allowedTo([\n          PERMISSIONS_NAMES.USER,\n          PERMISSIONS_NAMES.USER_CREATE,\n          PERMISSIONS_NAMES.KEYENTITY_USER,\n          PERMISSIONS_NAMES.KEYENTITY_USER_CREATE\n        ]),\n      }\n    );\n  }\n}\n", "<lib-swui-page-panel [title]=\"'MENU_SECTIONS.usersList'\" [actions]=\"panelActions\"></lib-swui-page-panel>\n\n<div class=\"p-32 sw-grid-layout\">\n  <div class=\"sw-grid-layout__table\">\n    <lib-swui-schema-top-filter [schema]=\"filterSchema\"></lib-swui-schema-top-filter>\n    <lib-swui-grid [schema]=\"schema\"\n                   [columnsManagement]=\"true\"\n                   [gridId]=\"componentName\"\n                   [rowActionsColumnTitle]=\"''\"\n                   [rowActions]=\"actions\"\n                   [savedFilteredPageName]=\"'users'\"\n                   [useHubEntity]=\"true\"\n                   sortActive=\"createdAt\"\n                   sortDirection=\"desc\">\n      <download-csv [loading]=\"loading\" (downloadCsv)=\"downloadCsv()\"></download-csv>\n      <button mat-icon-button matTooltip=\"Export current page\" (click)=\"exportPage()\">\n        <mat-icon>archive</mat-icon>\n      </button>\n      <button mat-icon-button matTooltip=\"Print current page\" (click)=\"printPage()\">\n        <mat-icon>print</mat-icon>\n      </button>\n    </lib-swui-grid>\n  </div>\n</div>\n"], "mappings": "AAIA,SAEEA,SAAS,EAGTC,iBAAiB,EACjBC,mBAAmB,EAEnBC,wBAAwB,QACnB,yBAAyB;AAChC,SAASC,OAAO,EAAEC,UAAU,QAAQ,MAAM;AAC1C,SAASC,UAAU,EAAEC,MAAM,EAAEC,SAAS,EAAEC,IAAI,EAAEC,SAAS,EAAEC,GAAG,QAAQ,gBAAgB;AACpF,SAASC,iBAAiB,QAAQ,2BAA2B;AAC7D,SACEC,yBAAyB,QAEpB,4EAA4E;AACnF,SAASC,MAAM,QAAQ,wCAAwC;AAG/D,SAASC,WAAW,QAAQ,0CAA0C;AACtE,SAASC,yBAAyB,QAAQ,iGAAiG;AAC3I,SACEC,yBAAyB,QAEpB,iGAAiG;AACxG,SACEC,0BAA0B,QACrB,kGAAkG;AACzG,SAASC,aAAa,EAAEC,WAAW,QAAQ,cAAc;AACzD,SAASC,IAAI,QAAQ,kBAAkB;;;;;;;;;;;;;AAEvC,MAAMC,cAAc,GAAW,WAAW;AAa1C,OAAM,MAAOC,kBAAkB;EAgB7BC,YAAoBC,OAA0B,EACpCC,aAAuC,EACvCC,SAA2B,EACnC;IAAEC,QAAQ,EAAE;MAAEC,IAAI,EAAE;QAAEC;MAAK;IAAE;EAAE,CAAkB,EACzCC,WAA6B,EAC7BC,kBAAsC,EACtCC,MAAiB,EACRC,uBAAgD,EAChDC,gBAAoC;IARnC,KAAAV,OAAO,GAAPA,OAAO;IACjB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,SAAS,GAATA,SAAS;IAET,KAAAI,WAAW,GAAXA,WAAW;IACX,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,MAAM,GAANA,MAAM;IACG,KAAAC,uBAAuB,GAAvBA,uBAAuB;IACvB,KAAAC,gBAAgB,GAAhBA,gBAAgB;IArBnC,KAAAC,MAAM,GAAGhB,WAAW;IACpB,KAAAiB,YAAY,GAAGlB,aAAa;IAE5B,KAAAmB,KAAK,GAAW,EAAE;IACT,KAAAC,OAAO,GAAgB,EAAE;IACzB,KAAAC,YAAY,GAAkB,EAAE;IAEzC,KAAAC,aAAa,GAAGnB,cAAc;IAC9B,KAAAoB,OAAO,GAAY,KAAK;IAGP,KAAAC,UAAU,GAAG,IAAIvC,OAAO,EAAQ;IAY/C,IAAI,CAACwC,MAAM,GAAGd,KAAK,GAAG,IAAIhB,MAAM,CAACgB,KAAK,CAAC,GAAGe,SAAS;IACnD,IAAI,CAACC,cAAc,GAAG,IAAI,CAACF,MAAM;IACjC,IAAI,CAACG,UAAU,EAAE;EACnB;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACd,uBAAuB,CAACe,IAAI,EAAE;IAEnC,IAAI,CAACd,gBAAgB,CAACe,eAAe,CAACC,IAAI,CACxC5C,MAAM,CAAC6C,OAAO,CAAC,EACf1C,SAAS,CAAC,IAAI,CAACiC,UAAU,CAAC,CAC3B,CAACU,SAAS,CAACT,MAAM,IAAG;MACnB,IAAI,CAACE,cAAc,GAAG,IAAIhC,MAAM,CAAC8B,MAAM,CAAC;IAC1C,CAAC,CAAC;EACJ;EAEAU,WAAWA,CAAA;IACT,IAAI,CAACX,UAAU,CAACY,IAAI,EAAE;IACtB,IAAI,CAACZ,UAAU,CAACa,QAAQ,EAAE;IAC1B,IAAI,CAACtB,uBAAuB,CAACuB,IAAI,EAAE;EACrC;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAChB,OAAO,GAAG,IAAI;IACnB,IAAI,CAACjB,OAAO,CAACiC,WAAW,CAAC,IAAI,CAACd,MAAM,EAAEe,IAAI,EAAE,IAAI,CAACb,cAAc,EAAEc,IAAI,CAAC,CACnET,IAAI,CACH7C,UAAU,CAAEuD,GAAG,IAAI;MACjB,IAAI,CAACnB,OAAO,GAAG,KAAK;MACpB,OAAOrC,UAAU,CAACwD,GAAG,CAAC;IACxB,CAAC,CAAC,EACFnD,SAAS,CAAC,IAAI,CAACiC,UAAU,CAAC,CAC3B,CAACU,SAAS,CAAC,MAAK;MACf,IAAI,CAACX,OAAO,GAAG,KAAK;IACtB,CAAC,CAAC;EACN;EAEAoB,SAASA,CAAA;IACPC,MAAM,CAACC,KAAK,EAAE;EAChB;EAEAC,UAAUA,CAAA;IACR,MAAM;MAAEC,UAAU,EAAE;QAAErC;MAAI,CAAE;MAAEsC,gBAAgB;MAAEC,SAAS,EAAE;QAAEC;MAAS;IAAE,CAAE,GAAG,IAAI,CAACC,IAAI;IACtF,IAAI,CAAC7C,OAAO,CAACwC,UAAU,CAAC,IAAI,CAACrB,MAAM,EAAEe,IAAI,EAAE,IAAI,CAACb,cAAc,EAAEc,IAAI,EAAE/B,IAAI,EAAEsC,gBAAgB,EAAEE,SAAS,GAAG,CAAC,CAAC;EAC9G;EAEQE,oBAAoBA,CAACC,IAAI,GAAG,IAAInD,IAAI,EAAE;IAC5C,IAAI,CAACY,MAAM,CAACwC,IAAI,CAA4B5D,yBAAyB,EAAE;MACrE6D,KAAK,EAAE,OAAO;MACd7C,IAAI,EAAE;QACJ2C,IAAI;QACJ5B,MAAM,EAAE,IAAI,CAACE,cAAc;QAC3BhB,KAAK,EAAE,IAAI,CAACc;OACb;MACD+B,YAAY,EAAE;KACf,CAAC,CAACC,WAAW,EAAE,CACbzB,IAAI,CAAC1C,IAAI,CAAC,CAAC,CAAC,CAAC,CACb4C,SAAS,CAAC,MAAK;MACd,IAAI,CAACiB,IAAI,CAACJ,UAAU,CAACW,QAAQ,EAAE;IACjC,CAAC,CAAC;EACN;EAEQC,4BAA4BA,CAACN,IAAU;IAC7C,IAAIO,UAAU,GAAG;MAAE,GAAGP;IAAI,CAAE;IAC5B,IAAI,CAAC/C,OAAO,CAACuD,cAAc,CAACD,UAAU,CAAC,CACpC5B,IAAI,CACH3C,SAAS,CAAEyE,WAAW,IACpB,IAAI,CAAChD,MAAM,CAACwC,IAAI,CAACvD,0BAA0B,EAAE;MAC3CwD,KAAK,EAAE,OAAO;MACd7C,IAAI,EAAE;QAAE2C,IAAI,EAAE;UAAE,GAAGO,UAAU;UAAE,GAAGE;QAAW;MAAE,CAAE;MACjDN,YAAY,EAAE;KACf,CAAC,CAACC,WAAW,EAAE,CAAC,EACnBrE,MAAM,CAAEsB,IAAI,IAAK,CAAC,CAACA,IAAI,CAAC,EACxBrB,SAAS,CAAEqB,IAAI,IAAK,IAAI,CAACG,kBAAkB,CAACkD,mBAAmB,CAACrD,IAAI,EAAE,IAAI,CAACe,MAAM,CAAC,CAAC,EACnFjC,GAAG,CAAC,MAAM,IAAI,CAACe,aAAa,CAACyD,OAAO,CAClC,IAAI,CAACxD,SAAS,CAACyD,OAAO,CAAC,kCAAkC,EAAEZ,IAAI,CAAC,CAAC,CAAC,EACpE9D,SAAS,CAAC,IAAI,CAACiC,UAAU,CAAC,CAC3B,CAACU,SAAS,CAAC,MAAK;MACf,IAAI,CAACiB,IAAI,CAACJ,UAAU,CAACW,QAAQ,EAAE;IACjC,CAAC,CAAC;EACN;EAEQQ,sBAAsBA,CAACb,IAAU;IACvC,MAAMO,UAAU,GAAG;MAAE,GAAGP;IAAI,CAAE;IAC9B,IAAI,CAACvC,MAAM,CAACwC,IAAI,CAACxD,yBAAyB,EAAE;MAC1CyD,KAAK,EAAE,OAAO;MACd7C,IAAI,EAAE;QACJe,MAAM,EAAE,IAAI,CAACA,MAAM;QACnB4B,IAAI,EAAEO;OACP;MACDJ,YAAY,EAAE;KACf,CAAC,CAACC,WAAW,EAAE,CAACzB,IAAI,CACnB5C,MAAM,CAAEsB,IAAI,IAAK,CAAC,CAACA,IAAI,CAAC,EACxBrB,SAAS,CAAEqB,IAA4B,IAAK,IAAI,CAACG,kBAAkB,CAACsD,kBAAkB,CAACzD,IAAI,CAAC,CAAC,EAC7FlB,GAAG,CAAC,MAAM,IAAI,CAACe,aAAa,CAACyD,OAAO,CAAC,IAAI,CAACxD,SAAS,CAACyD,OAAO,CAAC,0CAA0C,EAAEZ,IAAI,CAAC,CAAC,CAAC,EAC/G9D,SAAS,CAAC,IAAI,CAACiC,UAAU,CAAC,CAC3B,CAACU,SAAS,CAAC,MAAM,IAAI,CAACiB,IAAI,CAACJ,UAAU,CAACW,QAAQ,EAAE,CAAC;EACpD;EAEQU,uBAAuBA,CAACf,IAAU;IACxC,MAAMO,UAAU,GAAG;MAAE,GAAGP;IAAI,CAAE;IAC9B,IAAI,CAACvC,MAAM,CAACwC,IAAI,CAACzD,yBAAyB,EAAE;MAC1C0D,KAAK,EAAE,OAAO;MACd7C,IAAI,EAAE;QAAE2C,IAAI,EAAEO;MAAU,CAAE;MAC1BJ,YAAY,EAAE;KACf,CAAC,CAACC,WAAW,EAAE,CACbzB,IAAI,CACH5C,MAAM,CAAEsB,IAAI,IAAK,CAAC,CAACA,IAAI,CAAC,EACxBrB,SAAS,CAAC,MAAM,IAAI,CAACwB,kBAAkB,CAACwD,kBAAkB,CAAChB,IAAI,EAAE,IAAI,CAAC5B,MAAM,CAAC,CAAC,EAC9EjC,GAAG,CAAC,MAAM,IAAI,CAACe,aAAa,CAACyD,OAAO,CAAC,IAAI,CAACxD,SAAS,CAACyD,OAAO,CAAC,gCAAgC,EAAEZ,IAAI,CAAC,CAAC,CAAC,EACrGlE,UAAU,CAAEmF,KAAK,IAAI;MACnB,IAAI,CAAC/D,aAAa,CAAC+D,KAAK,CAACA,KAAK,CAACC,UAAU,EAAE,WAAWD,KAAK,CAACE,MAAM,EAAE,CAAC;MACrE,OAAOtF,UAAU,CAACoF,KAAK,CAAC;IAC1B,CAAC,CAAC,EACF/E,SAAS,CAAC,IAAI,CAACiC,UAAU,CAAC,CAC3B,CACAU,SAAS,CAAC,MAAM,IAAI,CAACiB,IAAI,CAACJ,UAAU,CAACW,QAAQ,EAAE,CAAC;EACrD;EAEQ9B,UAAUA,CAAA;IAChB,IAAI,IAAI,CAACf,kBAAkB,CAAC4D,eAAe,EAAE,EAAE;MAC7C,IAAI,CAACrD,OAAO,CAACsD,IAAI,CAAC,IAAI7F,SAAS,CAAC;QAC9B8F,KAAK,EAAE,mCAAmC;QAC1CC,IAAI,EAAE,MAAM;QACZC,EAAE,EAAE,IAAI,CAACzB,oBAAoB,CAAC0B,IAAI,CAAC,IAAI;OACxC,CAAC,CAAC;IACL;IAEA,IAAI,IAAI,CAACjE,kBAAkB,CAACkE,kBAAkB,CAAC,IAAI,CAACtD,MAAM,CAAC,EAAE;MAC3D,IAAI,CAACL,OAAO,CAACsD,IAAI,CAAC,IAAI7F,SAAS,CAAC;QAC9B8F,KAAK,EAAE,iDAAiD;QACxDC,IAAI,EAAE,WAAW;QACjBC,EAAE,EAAE,IAAI,CAAClB,4BAA4B,CAACmB,IAAI,CAAC,IAAI,CAAC;QAChDE,aAAa,EAAGC,GAAS,IAAI;UAC3B,OAAOA,GAAG,CAACT,MAAM,KAAK,gBAAgB;QACxC;OACD,CAAC,CAAC;IACL;IAEA,IAAI,IAAI,CAAC3D,kBAAkB,CAACqE,iBAAiB,EAAE,EAAE;MAC/C,IAAI,CAAC9D,OAAO,CAACsD,IAAI,CAAC,IAAI7F,SAAS,CAAC;QAC9B8F,KAAK,EAAE,mCAAmC;QAC1CC,IAAI,EAAE,UAAU;QAChBC,EAAE,EAAE,IAAI,CAACX,sBAAsB,CAACY,IAAI,CAAC,IAAI,CAAC;QAC1CE,aAAa,EAAGC,GAAS,IAAK,CAAC,CAACA,GAAG,CAACE,SAAS,KACtC,iBAAiB,IAAIF,GAAG,CAACE,SAAS,IAAM,WAAW,IAAIF,GAAG,CAACE,SAAS,IAAIF,GAAG,CAACE,SAAS,CAACC,SAAS,CAACC,MAAO;OAC/G,CAAC,CAAC;IACL;IAEA,IAAI,IAAI,CAACxE,kBAAkB,CAACyE,iBAAiB,CAAC,IAAI,CAAC7D,MAAM,CAAC,EAAE;MAC1D,IAAI,CAACL,OAAO,CAACsD,IAAI,CAAC,IAAI7F,SAAS,CAAC;QAC9B8F,KAAK,EAAE,qCAAqC;QAC5CC,IAAI,EAAE,QAAQ;QACdC,EAAE,EAAE,IAAI,CAACT,uBAAuB,CAACU,IAAI,CAAC,IAAI,CAAC;QAC3CE,aAAa,EAAGC,GAAS,IAAKA,GAAG,CAACM,QAAQ,KAAK,IAAI,CAAC3E,WAAW,CAAC2E;OACjE,CAAC,CAAC;IACL;IAEA,IAAI,CAAClE,YAAY,CAACqD,IAAI,CACpB;MACEC,KAAK,EAAE,kBAAkB;MACzBa,KAAK,EAAE,SAAS;MAChBZ,IAAI,EAAE,YAAY;MAClBa,QAAQ,EAAEA,CAAA,KAAM,IAAI,CAACrC,oBAAoB,EAAE;MAC3CsC,WAAW,EAAEA,CAAA,KAAM,IAAI,CAAC9E,WAAW,CAAC+E,SAAS,CAAC,CAC5ClG,iBAAiB,CAACmG,IAAI,EACtBnG,iBAAiB,CAACoG,WAAW,EAC7BpG,iBAAiB,CAACqG,cAAc,EAChCrG,iBAAiB,CAACsG,qBAAqB,CACxC;KACF,CACF;EACH;;;uCArMW3F,kBAAkB,EAAA4F,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAtG,WAAA,GAAAoG,EAAA,CAAAC,iBAAA,CAAAE,EAAA,CAAAC,wBAAA,GAAAJ,EAAA,CAAAC,iBAAA,CAAAI,EAAA,CAAAC,gBAAA,GAAAN,EAAA,CAAAC,iBAAA,CAAAM,EAAA,CAAAC,cAAA,GAAAR,EAAA,CAAAC,iBAAA,CAAAE,EAAA,CAAAM,gBAAA,GAAAT,EAAA,CAAAC,iBAAA,CAAAS,EAAA,CAAAC,kBAAA,GAAAX,EAAA,CAAAC,iBAAA,CAAAW,EAAA,CAAAC,SAAA,GAAAb,EAAA,CAAAC,iBAAA,CAAAa,EAAA,CAAAC,uBAAA,GAAAf,EAAA,CAAAC,iBAAA,CAAAE,EAAA,CAAAa,kBAAA;IAAA;EAAA;;;YAAlB5G,kBAAkB;MAAA6G,SAAA;MAAAC,SAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;yBAClBtI,iBAAiB;;;;;;;uCAPjB,CACTc,WAAW,EACXZ,wBAAwB,EACxB;QAAEsI,OAAO,EAAEvI,mBAAmB;QAAEwI,WAAW,EAAE3H;MAAW,CAAE,CAC3D;MAAA4H,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAR,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC/CHpB,EAAA,CAAA6B,SAAA,6BAAwG;UAGtG7B,EADF,CAAA8B,cAAA,aAAiC,aACI;UACjC9B,EAAA,CAAA6B,SAAA,oCAAiF;UAU/E7B,EATF,CAAA8B,cAAA,uBAQoC,sBAC8B;UAA9B9B,EAAA,CAAA+B,UAAA,yBAAAC,gEAAA;YAAA,OAAeX,GAAA,CAAA9E,WAAA,EAAa;UAAA,EAAC;UAACyD,EAAA,CAAAiC,YAAA,EAAe;UAC/EjC,EAAA,CAAA8B,cAAA,gBAAgF;UAAvB9B,EAAA,CAAA+B,UAAA,mBAAAG,oDAAA;YAAA,OAASb,GAAA,CAAAvE,UAAA,EAAY;UAAA,EAAC;UAC7EkD,EAAA,CAAA8B,cAAA,eAAU;UAAA9B,EAAA,CAAAmC,MAAA,cAAO;UACnBnC,EADmB,CAAAiC,YAAA,EAAW,EACrB;UACTjC,EAAA,CAAA8B,cAAA,gBAA8E;UAAtB9B,EAAA,CAAA+B,UAAA,mBAAAK,oDAAA;YAAA,OAASf,GAAA,CAAA1E,SAAA,EAAW;UAAA,EAAC;UAC3EqD,EAAA,CAAA8B,cAAA,gBAAU;UAAA9B,EAAA,CAAAmC,MAAA,aAAK;UAIvBnC,EAJuB,CAAAiC,YAAA,EAAW,EACnB,EACK,EACZ,EACF;;;UAvBmDjC,EAApC,CAAAqC,UAAA,oCAAmC,YAAAhB,GAAA,CAAAhG,YAAA,CAAyB;UAIjD2E,EAAA,CAAAsC,SAAA,GAAuB;UAAvBtC,EAAA,CAAAqC,UAAA,WAAAhB,GAAA,CAAAnG,YAAA,CAAuB;UACpC8E,EAAA,CAAAsC,SAAA,EAAiB;UAMjBtC,EANA,CAAAqC,UAAA,WAAAhB,GAAA,CAAApG,MAAA,CAAiB,2BACS,WAAAoG,GAAA,CAAA/F,aAAA,CACF,6BACI,eAAA+F,GAAA,CAAAjG,OAAA,CACN,kCACW,sBACZ;UAGpB4E,EAAA,CAAAsC,SAAA,EAAmB;UAAnBtC,EAAA,CAAAqC,UAAA,YAAAhB,GAAA,CAAA9F,OAAA,CAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}