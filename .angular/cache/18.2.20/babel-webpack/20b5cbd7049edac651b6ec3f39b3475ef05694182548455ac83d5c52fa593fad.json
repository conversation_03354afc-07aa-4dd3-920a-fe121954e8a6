{"ast": null, "code": "'use strict';\n\nvar ticky = require('ticky');\nmodule.exports = function debounce(fn, args, ctx) {\n  if (!fn) {\n    return;\n  }\n  ticky(function run() {\n    fn.apply(ctx || null, args || []);\n  });\n};", "map": {"version": 3, "names": ["ticky", "require", "module", "exports", "debounce", "fn", "args", "ctx", "run", "apply"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/node_modules/contra/debounce.js"], "sourcesContent": ["'use strict';\n\nvar ticky = require('ticky');\n\nmodule.exports = function debounce (fn, args, ctx) {\n  if (!fn) { return; }\n  ticky(function run () {\n    fn.apply(ctx || null, args || []);\n  });\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC;AAE5BC,MAAM,CAACC,OAAO,GAAG,SAASC,QAAQA,CAAEC,EAAE,EAAEC,IAAI,EAAEC,GAAG,EAAE;EACjD,IAAI,CAACF,EAAE,EAAE;IAAE;EAAQ;EACnBL,KAAK,CAAC,SAASQ,GAAGA,CAAA,EAAI;IACpBH,EAAE,CAACI,KAAK,CAACF,GAAG,IAAI,IAAI,EAAED,IAAI,IAAI,EAAE,CAAC;EACnC,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}