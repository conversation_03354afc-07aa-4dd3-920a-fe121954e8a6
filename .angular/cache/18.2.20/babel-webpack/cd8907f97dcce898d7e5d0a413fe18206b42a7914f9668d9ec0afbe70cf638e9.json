{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class BoLabelsGroupPipe {\n  transform(items, searchText) {\n    if (!items) return [];\n    if (!searchText) return items;\n    searchText = searchText.toLowerCase();\n    let filteredArr = items.filter(it => {\n      return it.title.toLowerCase().includes(searchText);\n    });\n    if (filteredArr.length === 0) {\n      return [-1];\n    }\n    return filteredArr;\n  }\n  static {\n    this.ɵfac = function BoLabelsGroupPipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || BoLabelsGroupPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n      name: \"boLabelsGroupFilter\",\n      type: BoLabelsGroupPipe,\n      pure: true\n    });\n  }\n}", "map": {"version": 3, "names": ["BoLabelsGroupPipe", "transform", "items", "searchText", "toLowerCase", "filteredArr", "filter", "it", "title", "includes", "length", "pure"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/common/components/bo-labels-group/bo-labels-group.pipe.ts"], "sourcesContent": ["import { Pipe, PipeTransform } from '@angular/core';\n@Pipe({\n  name: 'boLabelsGroupFilter'\n})\nexport class BoLabelsGroupPipe implements PipeTransform {\n  transform( items: any[], searchText: string ): any[] {\n    if (!items ) return [];\n    if (!searchText ) return items;\n    searchText = searchText.toLowerCase();\n    let filteredArr = items.filter( it => {\n      return it.title.toLowerCase().includes(searchText);\n    });\n    if (filteredArr.length === 0){\n      return [-1];\n    }\n    return filteredArr;\n  }\n}\n"], "mappings": ";AAIA,OAAM,MAAOA,iBAAiB;EAC5BC,SAASA,CAAEC,KAAY,EAAEC,UAAkB;IACzC,IAAI,CAACD,KAAK,EAAG,OAAO,EAAE;IACtB,IAAI,CAACC,UAAU,EAAG,OAAOD,KAAK;IAC9BC,UAAU,GAAGA,UAAU,CAACC,WAAW,EAAE;IACrC,IAAIC,WAAW,GAAGH,KAAK,CAACI,MAAM,CAAEC,EAAE,IAAG;MACnC,OAAOA,EAAE,CAACC,KAAK,CAACJ,WAAW,EAAE,CAACK,QAAQ,CAACN,UAAU,CAAC;IACpD,CAAC,CAAC;IACF,IAAIE,WAAW,CAACK,MAAM,KAAK,CAAC,EAAC;MAC3B,OAAO,CAAC,CAAC,CAAC,CAAC;IACb;IACA,OAAOL,WAAW;EACpB;;;uCAZWL,iBAAiB;IAAA;EAAA;;;;YAAjBA,iBAAiB;MAAAW,IAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}