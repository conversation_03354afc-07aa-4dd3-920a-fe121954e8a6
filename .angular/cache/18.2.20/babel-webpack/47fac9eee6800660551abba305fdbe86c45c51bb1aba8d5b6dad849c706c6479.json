{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiPagePanelModule } from '@skywind-group/lib-swui';\nimport { BsDropdownModule } from 'ngx-bootstrap/dropdown';\nimport { TabsModule } from 'ngx-bootstrap/tabs';\nimport { PlayerService } from '../../common/services/player.service';\nimport { GameHistoryService } from '../../common/services/reports/gamehistory.service';\nimport { DepositsModule } from './components/deposits/deposits.module';\nimport { PaymentGroupsComponent } from './components/payment-groups';\nimport { TransfersModule } from './components/transfers/transfers.module';\nimport { WithdrawalsModule } from './components/withdrawals/withdrawals.module';\nimport { PaymentsComponent } from './payments.component';\nimport { PaymentsRoutingModule } from './payments.routing';\nlet PaymentsModule = class PaymentsModule {};\nPaymentsModule = __decorate([NgModule({\n  imports: [BsDropdownModule.forRoot(), CommonModule, DepositsModule, PaymentsRoutingModule, SwuiPagePanelModule, TabsModule.forRoot(), TransfersModule, TranslateModule, WithdrawalsModule],\n  declarations: [PaymentGroupsComponent, PaymentsComponent],\n  providers: [GameHistoryService, PlayerService]\n})], PaymentsModule);\nexport { PaymentsModule };", "map": {"version": 3, "names": ["CommonModule", "NgModule", "TranslateModule", "SwuiPagePanelModule", "BsDropdownModule", "TabsModule", "PlayerService", "GameHistoryService", "DepositsModule", "PaymentGroupsComponent", "TransfersModule", "WithdrawalsModule", "PaymentsComponent", "PaymentsRoutingModule", "PaymentsModule", "__decorate", "imports", "forRoot", "declarations", "providers"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/payments/payments.module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiPagePanelModule } from '@skywind-group/lib-swui';\nimport { BsDropdownModule } from 'ngx-bootstrap/dropdown';\nimport { TabsModule } from 'ngx-bootstrap/tabs';\nimport { PlayerService } from '../../common/services/player.service';\nimport { GameHistoryService } from '../../common/services/reports/gamehistory.service';\nimport { DepositsModule } from './components/deposits/deposits.module';\nimport { PaymentGroupsComponent } from './components/payment-groups';\nimport { TransfersModule } from './components/transfers/transfers.module';\nimport { WithdrawalsModule } from './components/withdrawals/withdrawals.module';\nimport { PaymentsComponent } from './payments.component';\nimport { PaymentsRoutingModule } from './payments.routing';\n\n@NgModule({\n  imports: [\n    BsDropdownModule.forRoot(),\n    CommonModule,\n    DepositsModule,\n    PaymentsRoutingModule,\n    SwuiPagePanelModule,\n    TabsModule.forRoot(),\n    TransfersModule,\n    TranslateModule,\n    WithdrawalsModule,\n  ],\n  declarations: [\n    PaymentGroupsComponent,\n    PaymentsComponent,\n  ],\n  providers: [\n    GameHistoryService,\n    PlayerService,\n  ],\n})\nexport class PaymentsModule {\n}\n"], "mappings": ";AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,mBAAmB,QAAQ,yBAAyB;AAC7D,SAASC,gBAAgB,QAAQ,wBAAwB;AACzD,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,SAASC,aAAa,QAAQ,sCAAsC;AACpE,SAASC,kBAAkB,QAAQ,mDAAmD;AACtF,SAASC,cAAc,QAAQ,uCAAuC;AACtE,SAASC,sBAAsB,QAAQ,6BAA6B;AACpE,SAASC,eAAe,QAAQ,yCAAyC;AACzE,SAASC,iBAAiB,QAAQ,6CAA6C;AAC/E,SAASC,iBAAiB,QAAQ,sBAAsB;AACxD,SAASC,qBAAqB,QAAQ,oBAAoB;AAuBnD,IAAMC,cAAc,GAApB,MAAMA,cAAc,GAC1B;AADYA,cAAc,GAAAC,UAAA,EArB1Bd,QAAQ,CAAC;EACRe,OAAO,EAAE,CACPZ,gBAAgB,CAACa,OAAO,EAAE,EAC1BjB,YAAY,EACZQ,cAAc,EACdK,qBAAqB,EACrBV,mBAAmB,EACnBE,UAAU,CAACY,OAAO,EAAE,EACpBP,eAAe,EACfR,eAAe,EACfS,iBAAiB,CAClB;EACDO,YAAY,EAAE,CACZT,sBAAsB,EACtBG,iBAAiB,CAClB;EACDO,SAAS,EAAE,CACTZ,kBAAkB,EAClBD,aAAa;CAEhB,CAAC,C,EACWQ,cAAc,CAC1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}