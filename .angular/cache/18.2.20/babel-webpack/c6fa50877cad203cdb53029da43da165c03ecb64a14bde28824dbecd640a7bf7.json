{"ast": null, "code": "import { EventEmitter, forwardRef } from '@angular/core';\nimport { NG_VALIDATORS, NG_VALUE_ACCESSOR } from '@angular/forms';\nimport * as moment from 'moment';\nimport 'moment-timezone';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@skywind-group/lib-swui\";\nimport * as i2 from \"../../services/calendar.service\";\nimport * as i3 from \"ng-click-outside\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"ngx-bootstrap/dropdown\";\nimport * as i6 from \"@angular/forms\";\nimport * as i7 from \"angular2-text-mask\";\nimport * as i8 from \"../../directives/trim-input-value/trim-input-value.component\";\nimport * as i9 from \"../../pipes/leading-zero/leading-zero.pipe\";\nconst _c0 = [\"calendar\"];\nconst _c1 = a0 => ({\n  \"disabled\": a0\n});\nconst _c2 = (a0, a1, a2) => ({\n  selected: a0,\n  today: a1,\n  disabled: a2\n});\nconst _c3 = () => ({\n  standalone: true\n});\nfunction CalendarComponent_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 9);\n    i0.ɵɵlistener(\"click\", function CalendarComponent_span_7_Template_span_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.clear($event));\n    });\n    i0.ɵɵelement(1, \"i\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c1, ctx_r3.disabled));\n  }\n}\nfunction CalendarComponent_ng_container_8_div_1_thead_3_tr_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 28);\n    i0.ɵɵlistener(\"click\", function CalendarComponent_ng_container_8_div_1_thead_3_tr_8_Template_th_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r3 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r3.selectDay($event, ctx_r3.selectedDate.clone().add(-1, \"day\")));\n    });\n    i0.ɵɵtext(2, \"-1D \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\", 29);\n    i0.ɵɵlistener(\"click\", function CalendarComponent_ng_container_8_div_1_thead_3_tr_8_Template_th_click_3_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r3 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r3.selectDay($event, ctx_r3.today.clone().add(-1, \"day\")));\n    });\n    i0.ɵɵtext(4, \"YTD \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"th\", 28);\n    i0.ɵɵlistener(\"click\", function CalendarComponent_ng_container_8_div_1_thead_3_tr_8_Template_th_click_5_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r3 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r3.selectDay($event, ctx_r3.today));\n    });\n    i0.ɵɵtext(6, \"TD \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\", 29);\n    i0.ɵɵlistener(\"click\", function CalendarComponent_ng_container_8_div_1_thead_3_tr_8_Template_th_click_7_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r3 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r3.selectDay($event, ctx_r3.today.clone().add(1, \"day\")));\n    });\n    i0.ɵɵtext(8, \"TMW \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\", 28);\n    i0.ɵɵlistener(\"click\", function CalendarComponent_ng_container_8_div_1_thead_3_tr_8_Template_th_click_9_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r3 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r3.selectDay($event, ctx_r3.selectedDate.clone().add(1, \"day\")));\n    });\n    i0.ɵɵtext(10, \"+1D \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CalendarComponent_ng_container_8_div_1_thead_3_th_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 30);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dayName_r8 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(dayName_r8);\n  }\n}\nfunction CalendarComponent_ng_container_8_div_1_thead_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"thead\")(1, \"tr\")(2, \"th\", 21);\n    i0.ɵɵlistener(\"click\", function CalendarComponent_ng_container_8_div_1_thead_3_Template_th_click_2_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r3 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r3.setMonth(ctx_r3.currentDate.clone().add(-1, \"month\")));\n    });\n    i0.ɵɵelement(3, \"i\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"th\", 23);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\", 24);\n    i0.ɵɵlistener(\"click\", function CalendarComponent_ng_container_8_div_1_thead_3_Template_th_click_6_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r3 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r3.setMonth(ctx_r3.currentDate.clone().add(1, \"month\")));\n    });\n    i0.ɵɵelement(7, \"i\", 25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, CalendarComponent_ng_container_8_div_1_thead_3_tr_8_Template, 11, 0, \"tr\", 8);\n    i0.ɵɵelementStart(9, \"tr\", 26);\n    i0.ɵɵtemplate(10, CalendarComponent_ng_container_8_div_1_thead_3_th_10_Template, 2, 1, \"th\", 27);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r3.monthNames[ctx_r3.currentDate.month()], \" \", ctx_r3.currentDate.year(), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.showSpecialButtons);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.dayNames);\n  }\n}\nfunction CalendarComponent_ng_container_8_div_1_tr_5_td_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\", 32);\n    i0.ɵɵlistener(\"click\", function CalendarComponent_ng_container_8_div_1_tr_5_td_1_Template_td_click_0_listener($event) {\n      const day_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r3.selectDay($event, day_r10));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const day_r10 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(2, _c2, day_r10 == null ? null : day_r10.isSame(ctx_r3.selectedDate, \"date\"), day_r10 == null ? null : day_r10.isSame(ctx_r3.today, \"date\"), ctx_r3.minDate && (day_r10 == null ? null : day_r10.isBefore(ctx_r3.minDate, \"date\")) || ctx_r3.maxDate && (day_r10 == null ? null : day_r10.isAfter(ctx_r3.maxDate, \"date\"))));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", day_r10 == null ? null : day_r10.format(\"D\"), \" \");\n  }\n}\nfunction CalendarComponent_ng_container_8_div_1_tr_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 26);\n    i0.ɵɵtemplate(1, CalendarComponent_ng_container_8_div_1_tr_5_td_1_Template, 2, 6, \"td\", 31);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const week_r11 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", week_r11);\n  }\n}\nfunction CalendarComponent_ng_container_8_div_1_div_6_option_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"leadingZero\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const timeOption_r13 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", timeOption_r13);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 2, timeOption_r13));\n  }\n}\nfunction CalendarComponent_ng_container_8_div_1_div_6_option_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"leadingZero\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const timeOption_r14 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", timeOption_r14);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 2, timeOption_r14));\n  }\n}\nfunction CalendarComponent_ng_container_8_div_1_div_6_ng_container_6_option_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"leadingZero\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const timeOption_r16 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", timeOption_r16);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 2, timeOption_r16));\n  }\n}\nfunction CalendarComponent_ng_container_8_div_1_div_6_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1, \" : \");\n    i0.ɵɵelementStart(2, \"select\", 34);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CalendarComponent_ng_container_8_div_1_div_6_ng_container_6_Template_select_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r3 = i0.ɵɵnextContext(4);\n      i0.ɵɵtwoWayBindingSet(ctx_r3.seconds, $event) || (ctx_r3.seconds = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(3, CalendarComponent_ng_container_8_div_1_div_6_ng_container_6_option_3_Template, 3, 4, \"option\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.seconds);\n    i0.ɵɵproperty(\"ngModelOptions\", i0.ɵɵpureFunction0(4, _c3))(\"disabled\", !ctx_r3.timeAvailability.seconds);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.minSecOptions);\n  }\n}\nfunction CalendarComponent_ng_container_8_div_1_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"select\", 34);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CalendarComponent_ng_container_8_div_1_div_6_Template_select_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r3 = i0.ɵɵnextContext(3);\n      i0.ɵɵtwoWayBindingSet(ctx_r3.hours, $event) || (ctx_r3.hours = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(2, CalendarComponent_ng_container_8_div_1_div_6_option_2_Template, 3, 4, \"option\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" : \");\n    i0.ɵɵelementStart(4, \"select\", 34);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CalendarComponent_ng_container_8_div_1_div_6_Template_select_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r3 = i0.ɵɵnextContext(3);\n      i0.ɵɵtwoWayBindingSet(ctx_r3.minutes, $event) || (ctx_r3.minutes = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(5, CalendarComponent_ng_container_8_div_1_div_6_option_5_Template, 3, 4, \"option\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, CalendarComponent_ng_container_8_div_1_div_6_ng_container_6_Template, 4, 5, \"ng-container\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.hours);\n    i0.ɵɵproperty(\"ngModelOptions\", i0.ɵɵpureFunction0(9, _c3))(\"disabled\", !ctx_r3.timeAvailability.hours);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.hoursOptions);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.minutes);\n    i0.ɵɵproperty(\"ngModelOptions\", i0.ɵɵpureFunction0(10, _c3))(\"disabled\", !ctx_r3.timeAvailability.minutes);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.minSecOptions);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !(ctx_r3.hideTimeDisabled && !ctx_r3.timeAvailability.seconds));\n  }\n}\nfunction CalendarComponent_ng_container_8_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"div\", 13)(2, \"table\", 14);\n    i0.ɵɵtemplate(3, CalendarComponent_ng_container_8_div_1_thead_3_Template, 11, 4, \"thead\", 8);\n    i0.ɵɵelementStart(4, \"tbody\");\n    i0.ɵɵtemplate(5, CalendarComponent_ng_container_8_div_1_tr_5_Template, 2, 1, \"tr\", 15);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(6, CalendarComponent_ng_container_8_div_1_div_6_Template, 7, 11, \"div\", 16);\n    i0.ɵɵelementStart(7, \"div\", 17)(8, \"div\", 18)(9, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function CalendarComponent_ng_container_8_div_1_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.setCalendarVisibility(false));\n    });\n    i0.ɵɵtext(10, \"Cancel \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 18)(12, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function CalendarComponent_ng_container_8_div_1_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.applyChanges());\n    });\n    i0.ɵɵtext(13, \"Apply\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.currentDate);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.currentMonth);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.timePickerEnabled);\n  }\n}\nfunction CalendarComponent_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, CalendarComponent_ng_container_8_div_1_Template, 14, 3, \"div\", 11);\n    i0.ɵɵelementContainerEnd();\n  }\n}\nconst HOURS_IN_DAY = 24;\nconst MIN_SEC = 60;\nconst EMPTY_MASK_OPTIONS = {\n  mask: false\n};\nexport class CalendarComponent {\n  set disableCalendar(value) {\n    this.hideCalendar = value;\n  }\n  set timeDisableLevel(value) {\n    this._timeDisableLevel = value;\n    this.timeAvailability = {\n      hours: value !== 'hours',\n      minutes: value !== 'hours' && value !== 'minutes',\n      seconds: value !== 'hours' && value !== 'minutes' && value !== 'seconds'\n    };\n    // Innovation code) Update date through setter\n    this.appliedDate = this.appliedDate;\n  }\n  /**\n   * Getter\n   * @returns {any} - moment js object\n   */\n  get appliedDate() {\n    return this._appliedDate;\n  }\n  /**\n   * Setter\n   * @param value - moment js object\n   */\n  set appliedDate(value) {\n    this.setDateValue(value);\n    this.dateChange.emit(this._appliedDate);\n  }\n  get timeDisableLevel() {\n    return this._timeDisableLevel;\n  }\n  get formattedValue() {\n    if (this.appliedDate) {\n      const timeFormat = this.filterTimeFormat(this.appSettings.timeFormat);\n      return this.appliedDate.format(`${this.appSettings.dateFormat} ${timeFormat}`);\n    }\n    return '';\n  }\n  constructor(ref, settingsService, calendarService) {\n    this.ref = ref;\n    this.settingsService = settingsService;\n    this.calendarService = calendarService;\n    /**\n     * Show when there is not applied date\n     * @type {string}\n     */\n    this.placeholder = 'Select date';\n    this.showSpecialButtons = false;\n    this.enableMask = true;\n    this.timePickerEnabled = true;\n    this.clearOnDateEmpty = true;\n    this.dateChange = new EventEmitter();\n    this.onClear = new EventEmitter();\n    this.disabled = false;\n    this.hideCalendar = false;\n    this.hideTimeDisabled = false;\n    /**\n     * Calendar visibility state\n     * @type {boolean}\n     */\n    this.isCalendarOpen = false;\n    this.maskOptions = EMPTY_MASK_OPTIONS;\n    /**\n     * Current hours, minutes, seconds\n     */\n    this.hours = 0;\n    this.minutes = 0;\n    this.seconds = 0;\n    /**\n     * ['Mon', 'Tue', 'Wed' ... ] Day names in header\n     */\n    this.dayNames = moment.weekdaysShort();\n    /**\n     * Months names map in header\n     */\n    this.monthNames = moment.months();\n    this.hoursOptions = new Array(HOURS_IN_DAY).fill(0).map((_, i) => i);\n    this.minSecOptions = new Array(MIN_SEC).fill(0).map((_, i) => i);\n    /**\n     * Availability to change hours/minutes/seconds\n     */\n    this.timeAvailability = {\n      hours: true,\n      minutes: true,\n      seconds: true\n    };\n    this.subs = [];\n    this._symbol = Symbol('calendar');\n    this.today = moment();\n  }\n  ngOnInit() {\n    this.subs.push(this.settingsService.appSettings$.subscribe(data => {\n      this.setComponentData(data);\n    }));\n    this.initCalendarIdentifier();\n  }\n  ngOnChanges() {\n    this.setComponentData(this.settingsService.appSettings);\n  }\n  ngOnDestroy() {\n    this.subs.forEach(sub => sub.unsubscribe());\n  }\n  validate(control) {\n    let value = control.value;\n    return !Date.parse(value);\n  }\n  writeValue(value) {\n    this.date = value;\n    this.appliedDate = value;\n  }\n  registerOnChange(fn) {\n    this.propagateChange = fn;\n  }\n  registerOnTouched() {}\n  setDisabledState(disabled) {\n    this.disabled = disabled;\n  }\n  /**\n   * Set selected date\n   * @param event - mouse event\n   * @param day\n   */\n  selectDay(event, day) {\n    event.preventDefault();\n    this.selectedDate = day.clone();\n    // const zoneFix = Math.abs(this.selectedDate.utcOffset() * 2);\n    // this.selectedDate.utcOffset(zoneFix);\n    if (!this.selectedDate.isSame(this.currentDate, 'month')) {\n      this.setMonth(this.selectedDate.clone());\n    }\n  }\n  /**\n   * Set applied date and trigger it to parent\n   */\n  applyChanges() {\n    const {\n      years,\n      months,\n      date\n    } = this.selectedDate.toObject();\n    this.appliedDate = this.selectedDate.clone().year(years).month(months).date(date).hours(this.hours).minutes(this.minutes).seconds(this.seconds);\n    this.setCalendarVisibility(false);\n  }\n  setCalendarVisibility(value) {\n    this.isCalendarOpen = value;\n    this.ref.detectChanges();\n    this.calendarTrigger(value);\n  }\n  /**\n   * Clear applied date, set all items to defaults\n   */\n  clear(event) {\n    if (event) {\n      event.stopPropagation();\n    }\n    if (this.disabled) return;\n    this.appliedDate = undefined;\n    this.selectedDate = moment();\n    this.setMonth(this.selectedDate);\n    this.setCalendarVisibility(false);\n    this.onClear.emit();\n  }\n  /**\n   * Set 'main' date of month, set array of month dates\n   * @param date\n   */\n  setMonth(date) {\n    let firstDay = moment(date).startOf('month');\n    let lastDay = moment(date).endOf('month');\n    let result = [];\n    while (firstDay.date() <= lastDay.date()) {\n      if (!result.length || !firstDay.day()) {\n        result.push([]);\n      }\n      result[result.length - 1][firstDay.day()] = moment(firstDay.valueOf());\n      if (firstDay.date() === lastDay.date()) {\n        break;\n      } else {\n        firstDay.add(1, 'days');\n      }\n    }\n    this.currentDate = date.startOf('day');\n    this.currentMonth = result;\n  }\n  handleInputChange(newValue) {\n    if (this.disabled) return;\n    this.appliedDate = newValue;\n  }\n  /**\n   *  Setup selected date and environment\n   */\n  appliedDateToFormat() {\n    if (this.timePickerEnabled) {\n      return this.appSettings.dateFormat + ' ' + this.appSettings.timeFormat;\n    } else {\n      return this.appSettings.dateFormat;\n    }\n  }\n  onClickedOutside() {\n    this.isCalendarOpen = false;\n  }\n  setDateValue(value) {\n    if (value && typeof value === 'string') {\n      // this._appliedDate = moment(value.replace(/[^0-9:./\\\\\\-TZ ]*/gi, '').trim());\n      if (this.timePickerEnabled) {\n        this._appliedDate = moment(value, this.appSettings.dateFormat + ' ' + this.appSettings.timeFormat);\n      } else {\n        this._appliedDate = moment(value, this.appSettings.dateFormat);\n      }\n      if (!this._appliedDate.isValid()) {\n        this._appliedDate = moment();\n      }\n    } else {\n      this._appliedDate = value;\n    }\n    if (this._appliedDate) {\n      this._appliedDate.millisecond(0);\n      this._appliedDate = this.setDateByBounds(this._appliedDate);\n      if (!this.timeAvailability.hours) this._appliedDate.hours(0);\n      if (!this.timeAvailability.minutes) this._appliedDate.minutes(0);\n      if (!this.timeAvailability.seconds) this._appliedDate.seconds(0);\n    }\n    this.setDateByApplied();\n    this.setMonth(this.selectedDate);\n    if (typeof this.propagateChange === 'function') {\n      this.propagateChange(this._appliedDate && this._appliedDate.toString());\n    }\n  }\n  setComponentData(data) {\n    this.appSettings = data;\n    this.setMaskOtpions();\n    if (this.date) {\n      this.setDateValue(this.date);\n    } else if (this.clearOnDateEmpty) {\n      this.clear();\n    }\n  }\n  setMaskOtpions() {\n    if (this.enableMask) {\n      const timeFormat = this.filterTimeFormat(this.appSettings.timeFormat);\n      const dateMask = this.getMaskFromStr(this.appSettings.dateFormat);\n      const timeMask = this.getMaskFromStr(timeFormat);\n      if (this.timePickerEnabled) {\n        this.maskOptions = {\n          mask: dateMask.concat(' ').concat(timeMask)\n        };\n      } else {\n        this.maskOptions = {\n          mask: dateMask\n        };\n      }\n    } else {\n      this.maskOptions = EMPTY_MASK_OPTIONS;\n    }\n  }\n  getMaskFromStr(str) {\n    return str.split('').map(c => /\\w/.test(c) ? new RegExp('[0-9]') : c);\n  }\n  setDateByApplied() {\n    this.selectedDate = this.appliedDate ? this.appliedDate.clone() : moment();\n    this.selectedDate = this.setDateByBounds(this.selectedDate);\n    this.hours = this.timeAvailability.hours ? this.selectedDate.hours() : 0;\n    this.minutes = this.timeAvailability.minutes ? this.selectedDate.minutes() : 0;\n    this.seconds = this.timeAvailability.seconds ? this.selectedDate.seconds() : 0;\n  }\n  setDateByBounds(date) {\n    if (this.minDate && date && this.minDate.isAfter(date, 'date')) return this.minDate.clone();\n    if (this.maxDate && date && this.maxDate.isBefore(date, 'date')) return this.maxDate.clone();\n    return date;\n  }\n  /**\n   * Need for ngx-bootstrap dropdown component\n   * @param value\n   */\n  // @ts-ignore\n  handleDropdownToggle(value) {\n    this.isCalendarOpen = value;\n  }\n  filterTimeFormat(timeFormat) {\n    if (this.hideTimeDisabled) {\n      if (!this.timeAvailability.seconds) {\n        timeFormat = timeFormat.replace(/([\\:|\\.]ss)/, '');\n      }\n      if (!this.timeAvailability.minutes) {\n        timeFormat = timeFormat.replace(/([\\:|\\.]mm)/, '');\n      }\n    }\n    return timeFormat;\n  }\n  initCalendarIdentifier() {\n    const calendarSubscription = this.calendarService.calendars.subscribe(value => {\n      value.forEach(cal => {\n        if (cal === this._symbol) {\n          this.isCalendarOpen = true;\n          this.isCalendarDropUp();\n        } else {\n          this.isCalendarOpen = false;\n        }\n      });\n    });\n    this.subs.push(calendarSubscription);\n  }\n  calendarTrigger(value) {\n    value ? this.calendarService.addCalendar(this._symbol) : this.calendarService.removeCalendar(this._symbol);\n  }\n  isCalendarDropUp() {\n    const inputHeight = this.calendar.nativeElement.getBoundingClientRect().height;\n    const dropdownHeight = 410;\n    const calendarHeight = dropdownHeight + inputHeight;\n    const windowHeight = window.outerHeight;\n    const offsetTop = this.calendar.nativeElement.getBoundingClientRect().top;\n    this.isDropUp = windowHeight - offsetTop < calendarHeight;\n  }\n  static {\n    this.ɵfac = function CalendarComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CalendarComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.SettingsService), i0.ɵɵdirectiveInject(i2.CalendarService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CalendarComponent,\n      selectors: [[\"sw-calendar\"]],\n      viewQuery: function CalendarComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.calendar = _t.first);\n        }\n      },\n      inputs: {\n        placeholder: \"placeholder\",\n        date: \"date\",\n        showSpecialButtons: \"showSpecialButtons\",\n        minDate: \"minDate\",\n        maxDate: \"maxDate\",\n        enableMask: \"enableMask\",\n        timePickerEnabled: \"timePickerEnabled\",\n        clearOnDateEmpty: \"clearOnDateEmpty\",\n        hideTimeDisabled: \"hideTimeDisabled\",\n        disableCalendar: \"disableCalendar\",\n        timeDisableLevel: \"timeDisableLevel\"\n      },\n      outputs: {\n        dateChange: \"dateChange\",\n        onClear: \"onClear\"\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => CalendarComponent),\n        multi: true\n      }, {\n        provide: NG_VALIDATORS,\n        useExisting: forwardRef(() => CalendarComponent),\n        multi: true\n      }]), i0.ɵɵNgOnChangesFeature],\n      decls: 9,\n      vars: 13,\n      consts: [[\"calendar\", \"\"], [\"input\", \"\"], [\"dropdown\", \"\", 1, \"sw-calendar\", 3, \"isOpenChange\", \"clickOutside\", \"isOpen\", \"isDisabled\", \"dropup\", \"autoClose\"], [\"dropdownToggle\", \"\", 1, \"input-group\"], [1, \"input-group-addon\", \"pl-10\", \"pr-10\", 3, \"ngClass\"], [1, \"icon-calendar22\"], [\"type\", \"text\", \"trimValue\", \"\", 1, \"form-control\", 3, \"change\", \"textMask\", \"disabled\", \"value\", \"placeholder\"], [\"class\", \"input-group-addon cursor-pointer pl-10 pr-10\", 3, \"ngClass\", \"click\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"input-group-addon\", \"cursor-pointer\", \"pl-10\", \"pr-10\", 3, \"click\", \"ngClass\"], [1, \"icon-cross\"], [\"class\", \"sw-calendar_wrapper dropdown-menu\", 4, \"dropdownMenu\"], [1, \"sw-calendar_wrapper\", \"dropdown-menu\"], [1, \"mb-10\", \"pl-10\", \"pr-10\"], [1, \"sw-calendar_table\"], [\"class\", \"sw-calendar_tr\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"text-center mb-20\", 4, \"ngIf\"], [1, \"text-center\"], [1, \"col-lg-6\"], [\"type\", \"button\", 1, \"btn\", \"btn-default\", \"full-width\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"bg-success\", \"full-width\", 3, \"click\"], [1, \"sw-calendar_th\", \"prev\", 3, \"click\"], [1, \"icon-arrow-left32\"], [\"colspan\", \"5\", 1, \"sw-calendar_th\", \"month\"], [\"readonly\", \"1\", 1, \"sw-calendar_th\", \"next\", 3, \"click\"], [1, \"icon-arrow-right32\"], [1, \"sw-calendar_tr\"], [\"class\", \"sw-calendar_th\", 4, \"ngFor\", \"ngForOf\"], [1, \"sw-calendar_th\", \"cursor-pointer\", 3, \"click\"], [\"colspan\", \"2\", 1, \"sw-calendar_th\", \"cursor-pointer\", 3, \"click\"], [1, \"sw-calendar_th\"], [\"class\", \"sw-calendar_td\", 3, \"ngClass\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"sw-calendar_td\", 3, \"click\", \"ngClass\"], [1, \"text-center\", \"mb-20\"], [1, \"form-control\", \"input-sm\", \"sw-calendar_input\", \"select\", 3, \"ngModelChange\", \"ngModel\", \"ngModelOptions\", \"disabled\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [3, \"value\"]],\n      template: function CalendarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 2, 0);\n          i0.ɵɵlistener(\"isOpenChange\", function CalendarComponent_Template_div_isOpenChange_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.setCalendarVisibility($event));\n          })(\"clickOutside\", function CalendarComponent_Template_div_clickOutside_0_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onClickedOutside());\n          });\n          i0.ɵɵelementStart(2, \"div\", 3)(3, \"span\", 4);\n          i0.ɵɵelement(4, \"i\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"input\", 6, 1);\n          i0.ɵɵlistener(\"change\", function CalendarComponent_Template_input_change_5_listener() {\n            i0.ɵɵrestoreView(_r1);\n            const input_r2 = i0.ɵɵreference(6);\n            return i0.ɵɵresetView(ctx.handleInputChange(input_r2.value));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(7, CalendarComponent_span_7_Template, 2, 3, \"span\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(8, CalendarComponent_ng_container_8_Template, 2, 0, \"ng-container\", 8);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"isOpen\", ctx.isCalendarOpen)(\"isDisabled\", ctx.disabled)(\"dropup\", ctx.isDropUp)(\"autoClose\", false);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(11, _c1, ctx.disabled));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"textMask\", ctx.maskOptions)(\"disabled\", ctx.disabled)(\"value\", ctx.formattedValue)(\"placeholder\", ctx.placeholder || \"Select date\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.appliedDate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.hideCalendar);\n        }\n      },\n      dependencies: [i3.ClickOutsideDirective, i4.NgClass, i4.NgForOf, i4.NgIf, i5.BsDropdownMenuDirective, i5.BsDropdownToggleDirective, i5.BsDropdownDirective, i6.NgSelectOption, i6.ɵNgSelectMultipleOption, i6.SelectControlValueAccessor, i6.NgControlStatus, i6.NgModel, i7.MaskedInputDirective, i8.TrimInputValueComponent, i9.LeadingZeroPipe],\n      styles: [\".sw-calendar[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.sw-calendar[_ngcontent-%COMP%]   .disabled[_ngcontent-%COMP%] {\\n  background: #fafafa;\\n  cursor: not-allowed;\\n}\\n.sw-calendar[_ngcontent-%COMP%]   .disabled[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #9f9f9f;\\n}\\n.sw-calendar_wrapper[_ngcontent-%COMP%] {\\n  left: inherit;\\n  right: 0;\\n  padding: 14px;\\n  max-width: 350px;\\n  background-color: #fff;\\n  border: 1px solid #ddd;\\n  border-radius: 3px;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n}\\n.sw-calendar_table[_ngcontent-%COMP%] {\\n  width: 100%;\\n  color: black;\\n}\\n.sw-calendar_th[_ngcontent-%COMP%], .sw-calendar_td[_ngcontent-%COMP%] {\\n  padding: 10px;\\n  line-height: 1;\\n  white-space: nowrap;\\n  text-align: center;\\n}\\n.sw-calendar_th.today[_ngcontent-%COMP%], .sw-calendar_td.today[_ngcontent-%COMP%] {\\n  background-color: lightgray;\\n}\\n.sw-calendar_th[_ngcontent-%COMP%]:hover, .sw-calendar_td[_ngcontent-%COMP%]:hover {\\n  background-color: #f5f5f5;\\n}\\n.sw-calendar_th.selected[_ngcontent-%COMP%], .sw-calendar_td.selected[_ngcontent-%COMP%] {\\n  background-color: #26A69A;\\n}\\n.sw-calendar_th.selected[_ngcontent-%COMP%]:not(.today), .sw-calendar_td.selected[_ngcontent-%COMP%]:not(.today) {\\n  color: #fff;\\n}\\n.sw-calendar_th.disabled[_ngcontent-%COMP%], .sw-calendar_td.disabled[_ngcontent-%COMP%] {\\n  pointer-events: none;\\n  opacity: 0.4;\\n}\\n.sw-calendar_td[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  border-radius: 3px;\\n}\\n.sw-calendar_th[_ngcontent-%COMP%] {\\n  color: #999;\\n  font-weight: normal;\\n  font-size: 12px;\\n}\\n.sw-calendar_th.month[_ngcontent-%COMP%] {\\n  font-size: 15px;\\n  line-height: 1;\\n  color: #333;\\n  padding-top: 15px;\\n  padding-bottom: 15px;\\n  font-weight: 400;\\n}\\n.sw-calendar_th.prev[_ngcontent-%COMP%], .sw-calendar_th.next[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n}\\n.sw-calendar_th.prev[_ngcontent-%COMP%]:hover, .sw-calendar_th.next[_ngcontent-%COMP%]:hover {\\n  color: #333;\\n}\\n.sw-calendar_input.form-control[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  width: 70px;\\n}\\n.sw-calendar_input.select[_ngcontent-%COMP%] {\\n  text-align-last: center;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "forwardRef", "NG_VALIDATORS", "NG_VALUE_ACCESSOR", "moment", "i0", "ɵɵelementStart", "ɵɵlistener", "CalendarComponent_span_7_Template_span_click_0_listener", "$event", "ɵɵrestoreView", "_r3", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "clear", "ɵɵelement", "ɵɵelementEnd", "ɵɵproperty", "ɵɵpureFunction1", "_c1", "disabled", "CalendarComponent_ng_container_8_div_1_thead_3_tr_8_Template_th_click_1_listener", "_r7", "selectDay", "selectedDate", "clone", "add", "ɵɵtext", "CalendarComponent_ng_container_8_div_1_thead_3_tr_8_Template_th_click_3_listener", "today", "CalendarComponent_ng_container_8_div_1_thead_3_tr_8_Template_th_click_5_listener", "CalendarComponent_ng_container_8_div_1_thead_3_tr_8_Template_th_click_7_listener", "CalendarComponent_ng_container_8_div_1_thead_3_tr_8_Template_th_click_9_listener", "ɵɵadvance", "ɵɵtextInterpolate", "dayName_r8", "CalendarComponent_ng_container_8_div_1_thead_3_Template_th_click_2_listener", "_r6", "setMonth", "currentDate", "CalendarComponent_ng_container_8_div_1_thead_3_Template_th_click_6_listener", "ɵɵtemplate", "CalendarComponent_ng_container_8_div_1_thead_3_tr_8_Template", "CalendarComponent_ng_container_8_div_1_thead_3_th_10_Template", "ɵɵtextInterpolate2", "monthNames", "month", "year", "showSpecialButtons", "dayNames", "CalendarComponent_ng_container_8_div_1_tr_5_td_1_Template_td_click_0_listener", "day_r10", "_r9", "$implicit", "ɵɵpureFunction3", "_c2", "isSame", "minDate", "isBefore", "maxDate", "isAfter", "ɵɵtextInterpolate1", "format", "CalendarComponent_ng_container_8_div_1_tr_5_td_1_Template", "week_r11", "timeOption_r13", "ɵɵpipeBind1", "timeOption_r14", "timeOption_r16", "ɵɵelementContainerStart", "ɵɵtwoWayListener", "CalendarComponent_ng_container_8_div_1_div_6_ng_container_6_Template_select_ngModelChange_2_listener", "_r15", "ɵɵtwoWayBindingSet", "seconds", "CalendarComponent_ng_container_8_div_1_div_6_ng_container_6_option_3_Template", "ɵɵtwoWayProperty", "ɵɵpureFunction0", "_c3", "timeAvailability", "minSecOptions", "CalendarComponent_ng_container_8_div_1_div_6_Template_select_ngModelChange_1_listener", "_r12", "hours", "CalendarComponent_ng_container_8_div_1_div_6_option_2_Template", "CalendarComponent_ng_container_8_div_1_div_6_Template_select_ngModelChange_4_listener", "minutes", "CalendarComponent_ng_container_8_div_1_div_6_option_5_Template", "CalendarComponent_ng_container_8_div_1_div_6_ng_container_6_Template", "hoursOptions", "hideTimeDisabled", "CalendarComponent_ng_container_8_div_1_thead_3_Template", "CalendarComponent_ng_container_8_div_1_tr_5_Template", "CalendarComponent_ng_container_8_div_1_div_6_Template", "CalendarComponent_ng_container_8_div_1_Template_button_click_9_listener", "_r5", "setCalendarVisibility", "CalendarComponent_ng_container_8_div_1_Template_button_click_12_listener", "applyChanges", "currentMonth", "timePickerEnabled", "CalendarComponent_ng_container_8_div_1_Template", "HOURS_IN_DAY", "MIN_SEC", "EMPTY_MASK_OPTIONS", "mask", "CalendarComponent", "disable<PERSON><PERSON><PERSON><PERSON>", "value", "hideCalendar", "timeDisableLevel", "_timeDisableLevel", "appliedDate", "_appliedDate", "setDateValue", "dateChange", "emit", "formattedValue", "timeFormat", "filterTimeFormat", "appSettings", "dateFormat", "constructor", "ref", "settingsService", "calendarService", "placeholder", "enableMask", "clearOnDateEmpty", "onClear", "isCalendarOpen", "maskOptions", "weekdaysShort", "months", "Array", "fill", "map", "_", "i", "subs", "_symbol", "Symbol", "ngOnInit", "push", "appSettings$", "subscribe", "data", "setComponentData", "initCalendarIdentifier", "ngOnChanges", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "validate", "control", "Date", "parse", "writeValue", "date", "registerOnChange", "fn", "propagateChange", "registerOnTouched", "setDisabledState", "event", "day", "preventDefault", "years", "toObject", "detectChanges", "calendarTrigger", "stopPropagation", "undefined", "firstDay", "startOf", "lastDay", "endOf", "result", "length", "valueOf", "handleInputChange", "newValue", "appliedDateToFormat", "onClickedOutside", "<PERSON><PERSON><PERSON><PERSON>", "millisecond", "setDateByBounds", "setDateByApplied", "toString", "setMaskOtpions", "dateMask", "getMaskFromStr", "timeMask", "concat", "str", "split", "c", "test", "RegExp", "handleDropdownToggle", "replace", "calendarSubscription", "calendars", "cal", "isCalendarDropUp", "addCalendar", "removeCalendar", "inputHeight", "calendar", "nativeElement", "getBoundingClientRect", "height", "dropdownHeight", "calendarHeight", "windowHeight", "window", "outerHeight", "offsetTop", "top", "isDropUp", "ɵɵdirectiveInject", "ChangeDetectorRef", "i1", "SettingsService", "i2", "CalendarService", "selectors", "viewQuery", "CalendarComponent_Query", "rf", "ctx", "provide", "useExisting", "multi", "ɵɵNgOnChangesFeature", "decls", "vars", "consts", "template", "CalendarComponent_Template", "CalendarComponent_Template_div_isOpenChange_0_listener", "_r1", "CalendarComponent_Template_div_clickOutside_0_listener", "CalendarComponent_Template_input_change_5_listener", "input_r2", "ɵɵreference", "CalendarComponent_span_7_Template", "CalendarComponent_ng_container_8_Template"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/common/components/calendar/calendar.component.ts", "/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/common/components/calendar/calendar.component.html"], "sourcesContent": ["import {\n  ChangeDetectorRef,\n  Component,\n  ElementRef,\n  EventEmitter,\n  forwardRef,\n  Input,\n  OnChanges,\n  OnDestroy,\n  OnInit,\n  Output,\n  ViewChild\n} from '@angular/core';\nimport { ControlValueAccessor, FormControl, NG_VALIDATORS, NG_VALUE_ACCESSOR } from '@angular/forms';\nimport * as moment from 'moment';\nimport 'moment-timezone';\nimport { Subscription } from 'rxjs';\n\nimport { SettingsService } from '@skywind-group/lib-swui';\nimport { CalendarService } from '../../services/calendar.service';\nimport { AppSettings } from '../../typings';\n\n\nconst HOURS_IN_DAY = 24;\nconst MIN_SEC = 60;\n\nconst EMPTY_MASK_OPTIONS: CalendarMaskOptions = {\n  mask: false,\n};\n\n\n@Component({\n  selector: 'sw-calendar',\n  templateUrl: './calendar.component.html',\n  styleUrls: ['./calendar.component.scss'],\n  providers: [\n    { provide: NG_VALUE_ACCESSOR, useExisting: forwardRef(() => CalendarComponent), multi: true },\n    { provide: NG_VALIDATORS, useExisting: forwardRef(() => CalendarComponent), multi: true },\n  ],\n})\nexport class CalendarComponent implements ControlValueAccessor, OnInit, OnDestroy, OnChanges {\n  @ViewChild('calendar', { static: true }) calendar: ElementRef;\n  /**\n   * Show when there is not applied date\n   * @type {string}\n   */\n  @Input() placeholder = 'Select date';\n\n  @Input() date: any;\n\n  @Input() showSpecialButtons: boolean = false;\n\n  @Input() minDate: moment.Moment;\n  @Input() maxDate: moment.Moment;\n\n  @Input() enableMask: boolean = true;\n\n  @Input() timePickerEnabled: boolean = true;\n\n  @Input() clearOnDateEmpty: boolean = true;\n\n  @Output() dateChange = new EventEmitter<any>();\n  @Output() onClear = new EventEmitter<void>();\n\n  disabled: boolean = false;\n  hideCalendar: boolean = false;\n\n  @Input() hideTimeDisabled: boolean = false;\n\n  /**\n   * Calendar visibility state\n   * @type {boolean}\n   */\n  isCalendarOpen = false;\n  isDropUp: boolean;\n\n  maskOptions: CalendarMaskOptions = EMPTY_MASK_OPTIONS;\n\n  /**\n   * Array of dates of current month\n   */\n  currentMonth: moment.Moment[][];\n  /**\n   * 'Main' date of month (get current year, month name from it)\n   */\n  currentDate: any;\n\n  /**\n   * Selected, but not applied date\n   */\n  selectedDate: moment.Moment;\n  /**\n   * Current hours, minutes, seconds\n   */\n  hours = 0;\n  minutes = 0;\n  seconds = 0;\n\n  /**\n   * ['Mon', 'Tue', 'Wed' ... ] Day names in header\n   */\n  dayNames = moment.weekdaysShort();\n  /**\n   * Months names map in header\n   */\n  monthNames = moment.months();\n  today;\n\n  hoursOptions = (new Array(HOURS_IN_DAY)).fill(0).map((_, i) => i);\n  minSecOptions = (new Array(MIN_SEC)).fill(0).map((_, i) => i);\n\n  /**\n   * Availability to change hours/minutes/seconds\n   */\n  timeAvailability: CalendarTimeAvailabilityMap = {\n    hours: true,\n    minutes: true,\n    seconds: true,\n  };\n\n  /**\n   * Initial applied date and time. Component is connected with parent by this object\n   */\n  private _appliedDate: moment.Moment;\n\n  /**\n   * timeDisableLevel === 'seconds' disables seconds\n   * timeDisableLevel === 'minutes' disables seconds, minutes\n   * timeDisableLevel === 'hours' disables seconds, minutes\n   * default timeDisableLevel === 'all' disables nothing\n   */\n  private _timeDisableLevel: CalendarTimeDisableLevel;\n\n  private propagateChange: any;\n  private appSettings: AppSettings;\n  private _symbol: Symbol;\n  private subs: Subscription[] = [];\n\n  @Input()\n  set disableCalendar(value: boolean) {\n    this.hideCalendar = value;\n  }\n\n  @Input()\n  set timeDisableLevel(value: CalendarTimeDisableLevel) {\n    this._timeDisableLevel = value;\n\n    this.timeAvailability = {\n      hours: value !== 'hours',\n      minutes: value !== 'hours' && value !== 'minutes',\n      seconds: value !== 'hours' && value !== 'minutes' && value !== 'seconds',\n    };\n\n    // Innovation code) Update date through setter\n    this.appliedDate = this.appliedDate;\n  }\n\n  /**\n   * Getter\n   * @returns {any} - moment js object\n   */\n  get appliedDate() {\n    return this._appliedDate;\n  }\n\n  /**\n   * Setter\n   * @param value - moment js object\n   */\n  set appliedDate(value: any) {\n    this.setDateValue(value);\n    this.dateChange.emit(this._appliedDate);\n  }\n\n  get timeDisableLevel(): CalendarTimeDisableLevel {\n    return this._timeDisableLevel;\n  }\n  get formattedValue() {\n    if (this.appliedDate) {\n      const timeFormat = this.filterTimeFormat(this.appSettings.timeFormat);\n      return this.appliedDate.format(`${this.appSettings.dateFormat} ${timeFormat}`);\n    }\n    return '';\n  }\n\n\n  constructor(protected ref: ChangeDetectorRef,\n    protected settingsService: SettingsService,\n    private calendarService: CalendarService,\n  ) {\n    this._symbol = Symbol('calendar');\n    this.today = moment();\n  }\n\n  ngOnInit(): void {\n    this.subs.push(this.settingsService.appSettings$.subscribe(data => {\n      this.setComponentData(data);\n    }));\n    this.initCalendarIdentifier();\n  }\n\n  ngOnChanges(): void {\n    this.setComponentData(this.settingsService.appSettings);\n  }\n\n  ngOnDestroy(): void {\n    this.subs.forEach(sub => sub.unsubscribe());\n  }\n\n  validate(control: FormControl) {\n    let value = control.value;\n    return !Date.parse(value);\n  }\n\n  writeValue(value) {\n    this.date = value;\n    this.appliedDate = value;\n  }\n\n  registerOnChange(fn) {\n    this.propagateChange = fn;\n  }\n\n  registerOnTouched() {\n  }\n\n  setDisabledState(disabled: boolean) {\n    this.disabled = disabled;\n  }\n\n  /**\n   * Set selected date\n   * @param event - mouse event\n   * @param day\n   */\n  public selectDay(event: any, day: any): void {\n    event.preventDefault();\n\n    this.selectedDate = day.clone();\n\n    // const zoneFix = Math.abs(this.selectedDate.utcOffset() * 2);\n    // this.selectedDate.utcOffset(zoneFix);\n    if (!this.selectedDate.isSame(this.currentDate, 'month')) {\n      this.setMonth(this.selectedDate.clone());\n    }\n  }\n\n  /**\n   * Set applied date and trigger it to parent\n   */\n  public applyChanges(): void {\n    const { years, months, date } = this.selectedDate.toObject();\n    this.appliedDate = this.selectedDate\n      .clone()\n      .year(years)\n      .month(months)\n      .date(date)\n      .hours(this.hours)\n      .minutes(this.minutes)\n      .seconds(this.seconds);\n\n    this.setCalendarVisibility(false);\n  }\n\n  public setCalendarVisibility(value: boolean): void {\n    this.isCalendarOpen = value;\n    this.ref.detectChanges();\n    this.calendarTrigger(value);\n  }\n\n  /**\n   * Clear applied date, set all items to defaults\n   */\n  public clear(event?: Event): void {\n    if (event) {\n      event.stopPropagation();\n    }\n\n    if (this.disabled) return;\n    this.appliedDate = undefined;\n\n    this.selectedDate = moment();\n\n    this.setMonth(this.selectedDate);\n    this.setCalendarVisibility(false);\n\n    this.onClear.emit();\n  }\n\n  /**\n   * Set 'main' date of month, set array of month dates\n   * @param date\n   */\n  public setMonth(date: moment.Moment): void {\n    let firstDay = moment(date).startOf('month');\n    let lastDay = moment(date).endOf('month');\n\n    let result = [];\n\n    while (firstDay.date() <= lastDay.date()) {\n      if (!result.length || !firstDay.day()) {\n        result.push([]);\n      }\n\n      result[result.length - 1][firstDay.day()] = moment(firstDay.valueOf());\n      if (firstDay.date() === lastDay.date()) {\n        break;\n      } else {\n        firstDay.add(1, 'days');\n      }\n    }\n\n    this.currentDate = date.startOf('day');\n    this.currentMonth = result;\n  }\n\n  public handleInputChange(newValue: string): void {\n    if (this.disabled) return;\n    this.appliedDate = newValue;\n  }\n\n  /**\n   *  Setup selected date and environment\n   */\n  public appliedDateToFormat() {\n    if (this.timePickerEnabled) {\n      return this.appSettings.dateFormat + ' ' + this.appSettings.timeFormat;\n    } else {\n      return this.appSettings.dateFormat;\n    }\n  }\n\n  public onClickedOutside() {\n    this.isCalendarOpen = false;\n  }\n\n  private setDateValue(value: any) {\n    if (value && typeof value === 'string') {\n      // this._appliedDate = moment(value.replace(/[^0-9:./\\\\\\-TZ ]*/gi, '').trim());\n      if (this.timePickerEnabled) {\n        this._appliedDate = moment(value, this.appSettings.dateFormat + ' ' + this.appSettings.timeFormat);\n      } else {\n        this._appliedDate = moment(value, this.appSettings.dateFormat);\n      }\n      if (!this._appliedDate.isValid()) {\n        this._appliedDate = moment();\n      }\n    } else {\n      this._appliedDate = value;\n    }\n\n    if (this._appliedDate) {\n      this._appliedDate.millisecond(0);\n      this._appliedDate = this.setDateByBounds(this._appliedDate);\n\n      if (!this.timeAvailability.hours) this._appliedDate.hours(0);\n      if (!this.timeAvailability.minutes) this._appliedDate.minutes(0);\n      if (!this.timeAvailability.seconds) this._appliedDate.seconds(0);\n    }\n\n    this.setDateByApplied();\n    this.setMonth(this.selectedDate);\n\n    if (typeof this.propagateChange === 'function') {\n      this.propagateChange(this._appliedDate && this._appliedDate.toString());\n    }\n  }\n\n  private setComponentData(data: AppSettings): void {\n    this.appSettings = data;\n    this.setMaskOtpions();\n\n    if (this.date) {\n      this.setDateValue(this.date);\n    } else if (this.clearOnDateEmpty) {\n      this.clear();\n    }\n  }\n\n  private setMaskOtpions(): void {\n    if (this.enableMask) {\n      const timeFormat = this.filterTimeFormat(this.appSettings.timeFormat);\n      const dateMask = this.getMaskFromStr(this.appSettings.dateFormat);\n      const timeMask = this.getMaskFromStr(timeFormat);\n\n      if (this.timePickerEnabled) {\n        this.maskOptions = {\n          mask: dateMask.concat(' ').concat(timeMask),\n        };\n      } else {\n        this.maskOptions = {\n          mask: dateMask,\n        };\n      }\n    } else {\n      this.maskOptions = EMPTY_MASK_OPTIONS;\n    }\n  }\n\n  private getMaskFromStr(str: string): any[] {\n    return str\n      .split('')\n      .map(c => /\\w/.test(c) ? new RegExp('[0-9]') : c);\n  }\n\n  private setDateByApplied(): void {\n    this.selectedDate = this.appliedDate ? this.appliedDate.clone() : moment();\n\n    this.selectedDate = this.setDateByBounds(this.selectedDate);\n\n    this.hours = this.timeAvailability.hours ? this.selectedDate.hours() : 0;\n    this.minutes = this.timeAvailability.minutes ? this.selectedDate.minutes() : 0;\n    this.seconds = this.timeAvailability.seconds ? this.selectedDate.seconds() : 0;\n  }\n\n  private setDateByBounds(date: moment.Moment): moment.Moment {\n    if (this.minDate && date && this.minDate.isAfter(date, 'date')) return this.minDate.clone();\n    if (this.maxDate && date && this.maxDate.isBefore(date, 'date')) return this.maxDate.clone();\n\n    return date;\n  }\n\n  /**\n   * Need for ngx-bootstrap dropdown component\n   * @param value\n   */\n  // @ts-ignore\n  private handleDropdownToggle(value: boolean): void {\n    this.isCalendarOpen = value;\n  }\n\n  private filterTimeFormat(timeFormat: string) {\n    if (this.hideTimeDisabled) {\n\n      if (!this.timeAvailability.seconds) {\n        timeFormat = timeFormat.replace(/([\\:|\\.]ss)/, '');\n      }\n\n      if (!this.timeAvailability.minutes) {\n        timeFormat = timeFormat.replace(/([\\:|\\.]mm)/, '');\n      }\n\n    }\n    return timeFormat;\n  }\n\n  private initCalendarIdentifier(): void {\n    const calendarSubscription = this.calendarService.calendars.subscribe((value: Symbol[]) => {\n      value.forEach(cal => {\n        if (cal === this._symbol) {\n          this.isCalendarOpen = true;\n          this.isCalendarDropUp();\n        } else {\n          this.isCalendarOpen = false;\n        }\n      });\n    });\n\n    this.subs.push(calendarSubscription);\n  }\n\n  private calendarTrigger(value: boolean): void {\n    value ? this.calendarService.addCalendar(this._symbol) : this.calendarService.removeCalendar(this._symbol);\n  }\n\n  private isCalendarDropUp() {\n    const inputHeight = this.calendar.nativeElement.getBoundingClientRect().height;\n    const dropdownHeight = 410;\n    const calendarHeight = dropdownHeight + inputHeight;\n    const windowHeight = window.outerHeight;\n    const offsetTop = this.calendar.nativeElement.getBoundingClientRect().top;\n    this.isDropUp = windowHeight - offsetTop < calendarHeight;\n  }\n}\n", "<div class=\"sw-calendar\"\n     #calendar\n     [isOpen]=\"isCalendarOpen\"\n     dropdown\n     [isDisabled]=\"disabled\"\n     [dropup]=\"isDropUp\"\n     [autoClose]=\"false\"\n     (isOpenChange)=\"setCalendarVisibility($event)\"\n     (clickOutside)=\"onClickedOutside()\">\n\n  <!-- Component input with applied date if it is -->\n  <div class=\"input-group\" dropdownToggle>\n    <span class=\"input-group-addon pl-10 pr-10\" [ngClass]=\"{'disabled': disabled}\">\n      <i class=\"icon-calendar22\"></i>\n    </span>\n    <input type=\"text\" class=\"form-control\" trimValue\n           #input\n           [textMask]=\"maskOptions\"\n           [disabled]=\"disabled\"\n           [value]=\"formattedValue\"\n           (change)=\"handleInputChange(input.value)\"\n           [placeholder]=\"placeholder || 'Select date'\">\n\n    <span class=\"input-group-addon cursor-pointer pl-10 pr-10\" *ngIf=\"appliedDate\" (click)=\"clear($event)\"\n          [ngClass]=\"{'disabled': disabled}\">\n      <i class=\"icon-cross\"></i>\n    </span>\n  </div>\n\n  <ng-container *ngIf=\"!hideCalendar\">\n    <div class=\"sw-calendar_wrapper dropdown-menu\" *dropdownMenu>\n      <!-- Date area START -->\n      <div class=\"mb-10 pl-10 pr-10\">\n        <table class=\"sw-calendar_table\">\n          <thead *ngIf=\"currentDate\">\n          <tr>\n            <th class=\"sw-calendar_th prev\" (click)=\"setMonth(currentDate.clone().add(-1, 'month'))\">\n              <i class=\"icon-arrow-left32\"></i>\n            </th>\n            <th colspan=\"5\" class=\"sw-calendar_th month\">\n              {{monthNames[currentDate.month()]}} {{currentDate.year()}}\n            </th>\n            <th class=\"sw-calendar_th next\" (click)=\"setMonth(currentDate.clone().add(1, 'month'))\" readonly=\"1\">\n              <i class=\"icon-arrow-right32\"></i>\n            </th>\n          </tr>\n          <tr *ngIf=\"showSpecialButtons\">\n            <th class=\"sw-calendar_th cursor-pointer\"\n                (click)=\"selectDay($event, selectedDate.clone().add(-1, 'day'))\">-1D\n            </th>\n            <th colspan=\"2\" class=\"sw-calendar_th cursor-pointer\"\n                (click)=\"selectDay($event, today.clone().add(-1, 'day'))\">YTD\n            </th>\n            <th class=\"sw-calendar_th cursor-pointer\"\n                (click)=\"selectDay($event, today)\">TD\n            </th>\n            <th colspan=\"2\" class=\"sw-calendar_th cursor-pointer\"\n                (click)=\"selectDay($event, today.clone().add(1, 'day'))\">TMW\n            </th>\n            <th class=\"sw-calendar_th cursor-pointer\"\n                (click)=\"selectDay($event, selectedDate.clone().add(1, 'day'))\">+1D\n            </th>\n          </tr>\n          <tr class=\"sw-calendar_tr\">\n            <th *ngFor=\"let dayName of dayNames\" class=\"sw-calendar_th\">{{dayName}}</th>\n          </tr>\n          </thead>\n\n          <tbody>\n          <tr *ngFor=\"let week of currentMonth\" class=\"sw-calendar_tr\">\n            <td *ngFor=\"let day of week\" class=\"sw-calendar_td\"\n                [ngClass]=\"{\n                selected: day?.isSame(selectedDate, 'date'),\n                today: day?.isSame(today, 'date'),\n                disabled: (minDate && day?.isBefore(minDate, 'date')) || (maxDate && day?.isAfter(maxDate, 'date'))\n               }\"\n                (click)=\"selectDay($event, day)\">\n              {{day?.format('D')}}\n            </td>\n          </tr>\n          </tbody>\n        </table>\n      </div>\n      <!-- Date area END -->\n\n      <!-- Time area START -->\n      <div class=\"text-center mb-20\" *ngIf=\"timePickerEnabled\">\n        <select class=\"form-control input-sm sw-calendar_input select\" [(ngModel)]=\"hours\"\n                [ngModelOptions]=\"{standalone: true}\" [disabled]=\"!timeAvailability.hours\">\n          <option *ngFor=\"let timeOption of hoursOptions\" [value]=\"timeOption\">{{timeOption | leadingZero}}</option>\n        </select>\n        :\n        <select class=\"form-control input-sm sw-calendar_input select\" [(ngModel)]=\"minutes\"\n                [ngModelOptions]=\"{standalone: true}\" [disabled]=\"!timeAvailability.minutes\">\n          <option *ngFor=\"let timeOption of minSecOptions\" [value]=\"timeOption\">{{timeOption | leadingZero}}</option>\n        </select>\n\n        <ng-container *ngIf=\"!(hideTimeDisabled && !timeAvailability.seconds)\">\n          :\n          <select class=\"form-control input-sm sw-calendar_input select\" [(ngModel)]=\"seconds\"\n                  [ngModelOptions]=\"{standalone: true}\" [disabled]=\"!timeAvailability.seconds\">\n            <option *ngFor=\"let timeOption of minSecOptions\" [value]=\"timeOption\">{{timeOption | leadingZero}}</option>\n          </select>\n        </ng-container>\n      </div>\n      <!-- Time area END -->\n\n      <!-- Buttons area START -->\n      <div class=\"text-center\">\n        <div class=\"col-lg-6\">\n          <button type=\"button\" class=\"btn btn-default full-width\" (click)=\"setCalendarVisibility(false)\">Cancel\n          </button>\n        </div>\n        <div class=\"col-lg-6\">\n          <button type=\"button\" class=\"btn bg-success full-width\" (click)=\"applyChanges()\">Apply</button>\n        </div>\n      </div>\n      <!-- Buttons area END -->\n    </div>\n  </ng-container>\n</div>\n"], "mappings": "AAAA,SAIEA,YAAY,EACZC,UAAU,QAOL,eAAe;AACtB,SAA4CC,aAAa,EAAEC,iBAAiB,QAAQ,gBAAgB;AACpG,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,OAAO,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;ICQpBC,EAAA,CAAAC,cAAA,cACyC;IADsCD,EAAA,CAAAE,UAAA,mBAAAC,wDAAAC,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAASF,MAAA,CAAAG,KAAA,CAAAN,MAAA,CAAa;IAAA,EAAC;IAEpGJ,EAAA,CAAAW,SAAA,YAA0B;IAC5BX,EAAA,CAAAY,YAAA,EAAO;;;;IAFDZ,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAc,eAAA,IAAAC,GAAA,EAAAR,MAAA,CAAAS,QAAA,EAAkC;;;;;;IAuBhChB,EADF,CAAAC,cAAA,SAA+B,aAEwC;IAAjED,EAAA,CAAAE,UAAA,mBAAAe,iFAAAb,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAASF,MAAA,CAAAY,SAAA,CAAAf,MAAA,EAAkBG,MAAA,CAAAa,YAAA,CAAAC,KAAA,EAAoB,CAAAC,GAAA,EAAM,CAAC,EAAE,KAAK,CAAC,CAAC;IAAA,EAAC;IAACtB,EAAA,CAAAuB,MAAA,WACrE;IAAAvB,EAAA,CAAAY,YAAA,EAAK;IACLZ,EAAA,CAAAC,cAAA,aAC8D;IAA1DD,EAAA,CAAAE,UAAA,mBAAAsB,iFAAApB,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAASF,MAAA,CAAAY,SAAA,CAAAf,MAAA,EAAkBG,MAAA,CAAAkB,KAAA,CAAAJ,KAAA,EAAa,CAAAC,GAAA,EAAM,CAAC,EAAE,KAAK,CAAC,CAAC;IAAA,EAAC;IAACtB,EAAA,CAAAuB,MAAA,WAC9D;IAAAvB,EAAA,CAAAY,YAAA,EAAK;IACLZ,EAAA,CAAAC,cAAA,aACuC;IAAnCD,EAAA,CAAAE,UAAA,mBAAAwB,iFAAAtB,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAASF,MAAA,CAAAY,SAAA,CAAAf,MAAA,EAAAG,MAAA,CAAAkB,KAAA,CAAwB;IAAA,EAAC;IAACzB,EAAA,CAAAuB,MAAA,UACvC;IAAAvB,EAAA,CAAAY,YAAA,EAAK;IACLZ,EAAA,CAAAC,cAAA,aAC6D;IAAzDD,EAAA,CAAAE,UAAA,mBAAAyB,iFAAAvB,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAASF,MAAA,CAAAY,SAAA,CAAAf,MAAA,EAAkBG,MAAA,CAAAkB,KAAA,CAAAJ,KAAA,EAAa,CAAAC,GAAA,CAAK,CAAC,EAAE,KAAK,CAAC,CAAC;IAAA,EAAC;IAACtB,EAAA,CAAAuB,MAAA,WAC7D;IAAAvB,EAAA,CAAAY,YAAA,EAAK;IACLZ,EAAA,CAAAC,cAAA,aACoE;IAAhED,EAAA,CAAAE,UAAA,mBAAA0B,iFAAAxB,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAASF,MAAA,CAAAY,SAAA,CAAAf,MAAA,EAAkBG,MAAA,CAAAa,YAAA,CAAAC,KAAA,EAAoB,CAAAC,GAAA,CAAK,CAAC,EAAE,KAAK,CAAC,CAAC;IAAA,EAAC;IAACtB,EAAA,CAAAuB,MAAA,YACpE;IACFvB,EADE,CAAAY,YAAA,EAAK,EACF;;;;;IAEHZ,EAAA,CAAAC,cAAA,aAA4D;IAAAD,EAAA,CAAAuB,MAAA,GAAW;IAAAvB,EAAA,CAAAY,YAAA,EAAK;;;;IAAhBZ,EAAA,CAAA6B,SAAA,EAAW;IAAX7B,EAAA,CAAA8B,iBAAA,CAAAC,UAAA,CAAW;;;;;;IA5BvE/B,EAFF,CAAAC,cAAA,YAA2B,SACvB,aACuF;IAAzDD,EAAA,CAAAE,UAAA,mBAAA8B,4EAAA;MAAAhC,EAAA,CAAAK,aAAA,CAAA4B,GAAA;MAAA,MAAA1B,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAASF,MAAA,CAAA2B,QAAA,CAAS3B,MAAA,CAAA4B,WAAA,CAAAd,KAAA,EAAmB,CAAAC,GAAA,EAAM,CAAC,EAAE,OAAO,CAAC,CAAC;IAAA,EAAC;IACtFtB,EAAA,CAAAW,SAAA,YAAiC;IACnCX,EAAA,CAAAY,YAAA,EAAK;IACLZ,EAAA,CAAAC,cAAA,aAA6C;IAC3CD,EAAA,CAAAuB,MAAA,GACF;IAAAvB,EAAA,CAAAY,YAAA,EAAK;IACLZ,EAAA,CAAAC,cAAA,aAAqG;IAArED,EAAA,CAAAE,UAAA,mBAAAkC,4EAAA;MAAApC,EAAA,CAAAK,aAAA,CAAA4B,GAAA;MAAA,MAAA1B,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAASF,MAAA,CAAA2B,QAAA,CAAS3B,MAAA,CAAA4B,WAAA,CAAAd,KAAA,EAAmB,CAAAC,GAAA,CAAK,CAAC,EAAE,OAAO,CAAC,CAAC;IAAA,EAAC;IACrFtB,EAAA,CAAAW,SAAA,YAAkC;IAEtCX,EADE,CAAAY,YAAA,EAAK,EACF;IACLZ,EAAA,CAAAqC,UAAA,IAAAC,4DAAA,iBAA+B;IAiB/BtC,EAAA,CAAAC,cAAA,aAA2B;IACzBD,EAAA,CAAAqC,UAAA,KAAAE,6DAAA,iBAA4D;IAE9DvC,EADA,CAAAY,YAAA,EAAK,EACG;;;;IA1BJZ,EAAA,CAAA6B,SAAA,GACF;IADE7B,EAAA,CAAAwC,kBAAA,MAAAjC,MAAA,CAAAkC,UAAA,CAAAlC,MAAA,CAAA4B,WAAA,CAAAO,KAAA,UAAAnC,MAAA,CAAA4B,WAAA,CAAAQ,IAAA,QACF;IAKG3C,EAAA,CAAA6B,SAAA,GAAwB;IAAxB7B,EAAA,CAAAa,UAAA,SAAAN,MAAA,CAAAqC,kBAAA,CAAwB;IAkBH5C,EAAA,CAAA6B,SAAA,GAAW;IAAX7B,EAAA,CAAAa,UAAA,YAAAN,MAAA,CAAAsC,QAAA,CAAW;;;;;;IAMnC7C,EAAA,CAAAC,cAAA,aAMqC;IAAjCD,EAAA,CAAAE,UAAA,mBAAA4C,8EAAA1C,MAAA;MAAA,MAAA2C,OAAA,GAAA/C,EAAA,CAAAK,aAAA,CAAA2C,GAAA,EAAAC,SAAA;MAAA,MAAA1C,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAASF,MAAA,CAAAY,SAAA,CAAAf,MAAA,EAAA2C,OAAA,CAAsB;IAAA,EAAC;IAClC/C,EAAA,CAAAuB,MAAA,GACF;IAAAvB,EAAA,CAAAY,YAAA,EAAK;;;;;IAPDZ,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAkD,eAAA,IAAAC,GAAA,EAAAJ,OAAA,kBAAAA,OAAA,CAAAK,MAAA,CAAA7C,MAAA,CAAAa,YAAA,WAAA2B,OAAA,kBAAAA,OAAA,CAAAK,MAAA,CAAA7C,MAAA,CAAAkB,KAAA,WAAAlB,MAAA,CAAA8C,OAAA,KAAAN,OAAA,kBAAAA,OAAA,CAAAO,QAAA,CAAA/C,MAAA,CAAA8C,OAAA,cAAA9C,MAAA,CAAAgD,OAAA,KAAAR,OAAA,kBAAAA,OAAA,CAAAS,OAAA,CAAAjD,MAAA,CAAAgD,OAAA,YAIC;IAEHvD,EAAA,CAAA6B,SAAA,EACF;IADE7B,EAAA,CAAAyD,kBAAA,MAAAV,OAAA,kBAAAA,OAAA,CAAAW,MAAA,WACF;;;;;IATF1D,EAAA,CAAAC,cAAA,aAA6D;IAC3DD,EAAA,CAAAqC,UAAA,IAAAsB,yDAAA,iBAMqC;IAGvC3D,EAAA,CAAAY,YAAA,EAAK;;;;IATiBZ,EAAA,CAAA6B,SAAA,EAAO;IAAP7B,EAAA,CAAAa,UAAA,YAAA+C,QAAA,CAAO;;;;;IAmB7B5D,EAAA,CAAAC,cAAA,iBAAqE;IAAAD,EAAA,CAAAuB,MAAA,GAA4B;;IAAAvB,EAAA,CAAAY,YAAA,EAAS;;;;IAA1DZ,EAAA,CAAAa,UAAA,UAAAgD,cAAA,CAAoB;IAAC7D,EAAA,CAAA6B,SAAA,EAA4B;IAA5B7B,EAAA,CAAA8B,iBAAA,CAAA9B,EAAA,CAAA8D,WAAA,OAAAD,cAAA,EAA4B;;;;;IAKjG7D,EAAA,CAAAC,cAAA,iBAAsE;IAAAD,EAAA,CAAAuB,MAAA,GAA4B;;IAAAvB,EAAA,CAAAY,YAAA,EAAS;;;;IAA1DZ,EAAA,CAAAa,UAAA,UAAAkD,cAAA,CAAoB;IAAC/D,EAAA,CAAA6B,SAAA,EAA4B;IAA5B7B,EAAA,CAAA8B,iBAAA,CAAA9B,EAAA,CAAA8D,WAAA,OAAAC,cAAA,EAA4B;;;;;IAOhG/D,EAAA,CAAAC,cAAA,iBAAsE;IAAAD,EAAA,CAAAuB,MAAA,GAA4B;;IAAAvB,EAAA,CAAAY,YAAA,EAAS;;;;IAA1DZ,EAAA,CAAAa,UAAA,UAAAmD,cAAA,CAAoB;IAAChE,EAAA,CAAA6B,SAAA,EAA4B;IAA5B7B,EAAA,CAAA8B,iBAAA,CAAA9B,EAAA,CAAA8D,WAAA,OAAAE,cAAA,EAA4B;;;;;;IAJtGhE,EAAA,CAAAiE,uBAAA,GAAuE;IACrEjE,EAAA,CAAAuB,MAAA,UACA;IAAAvB,EAAA,CAAAC,cAAA,iBACqF;IADtBD,EAAA,CAAAkE,gBAAA,2BAAAC,qGAAA/D,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAA+D,IAAA;MAAA,MAAA7D,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAAqE,kBAAA,CAAA9D,MAAA,CAAA+D,OAAA,EAAAlE,MAAA,MAAAG,MAAA,CAAA+D,OAAA,GAAAlE,MAAA;MAAA,OAAAJ,EAAA,CAAAS,WAAA,CAAAL,MAAA;IAAA,EAAqB;IAElFJ,EAAA,CAAAqC,UAAA,IAAAkC,6EAAA,qBAAsE;IACxEvE,EAAA,CAAAY,YAAA,EAAS;;;;;IAHsDZ,EAAA,CAAA6B,SAAA,GAAqB;IAArB7B,EAAA,CAAAwE,gBAAA,YAAAjE,MAAA,CAAA+D,OAAA,CAAqB;IACtCtE,EAAtC,CAAAa,UAAA,mBAAAb,EAAA,CAAAyE,eAAA,IAAAC,GAAA,EAAqC,cAAAnE,MAAA,CAAAoE,gBAAA,CAAAL,OAAA,CAAuC;IACnDtE,EAAA,CAAA6B,SAAA,EAAgB;IAAhB7B,EAAA,CAAAa,UAAA,YAAAN,MAAA,CAAAqE,aAAA,CAAgB;;;;;;IAdnD5E,EADF,CAAAC,cAAA,cAAyD,iBAE4B;IADpBD,EAAA,CAAAkE,gBAAA,2BAAAW,sFAAAzE,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAyE,IAAA;MAAA,MAAAvE,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAAqE,kBAAA,CAAA9D,MAAA,CAAAwE,KAAA,EAAA3E,MAAA,MAAAG,MAAA,CAAAwE,KAAA,GAAA3E,MAAA;MAAA,OAAAJ,EAAA,CAAAS,WAAA,CAAAL,MAAA;IAAA,EAAmB;IAEhFJ,EAAA,CAAAqC,UAAA,IAAA2C,8DAAA,qBAAqE;IACvEhF,EAAA,CAAAY,YAAA,EAAS;IACTZ,EAAA,CAAAuB,MAAA,UACA;IAAAvB,EAAA,CAAAC,cAAA,iBACqF;IADtBD,EAAA,CAAAkE,gBAAA,2BAAAe,sFAAA7E,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAyE,IAAA;MAAA,MAAAvE,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAAqE,kBAAA,CAAA9D,MAAA,CAAA2E,OAAA,EAAA9E,MAAA,MAAAG,MAAA,CAAA2E,OAAA,GAAA9E,MAAA;MAAA,OAAAJ,EAAA,CAAAS,WAAA,CAAAL,MAAA;IAAA,EAAqB;IAElFJ,EAAA,CAAAqC,UAAA,IAAA8C,8DAAA,qBAAsE;IACxEnF,EAAA,CAAAY,YAAA,EAAS;IAETZ,EAAA,CAAAqC,UAAA,IAAA+C,oEAAA,0BAAuE;IAOzEpF,EAAA,CAAAY,YAAA,EAAM;;;;IAjB2DZ,EAAA,CAAA6B,SAAA,EAAmB;IAAnB7B,EAAA,CAAAwE,gBAAA,YAAAjE,MAAA,CAAAwE,KAAA,CAAmB;IACpC/E,EAAtC,CAAAa,UAAA,mBAAAb,EAAA,CAAAyE,eAAA,IAAAC,GAAA,EAAqC,cAAAnE,MAAA,CAAAoE,gBAAA,CAAAI,KAAA,CAAqC;IACjD/E,EAAA,CAAA6B,SAAA,EAAe;IAAf7B,EAAA,CAAAa,UAAA,YAAAN,MAAA,CAAA8E,YAAA,CAAe;IAGerF,EAAA,CAAA6B,SAAA,GAAqB;IAArB7B,EAAA,CAAAwE,gBAAA,YAAAjE,MAAA,CAAA2E,OAAA,CAAqB;IACtClF,EAAtC,CAAAa,UAAA,mBAAAb,EAAA,CAAAyE,eAAA,KAAAC,GAAA,EAAqC,cAAAnE,MAAA,CAAAoE,gBAAA,CAAAO,OAAA,CAAuC;IACnDlF,EAAA,CAAA6B,SAAA,EAAgB;IAAhB7B,EAAA,CAAAa,UAAA,YAAAN,MAAA,CAAAqE,aAAA,CAAgB;IAGlC5E,EAAA,CAAA6B,SAAA,EAAsD;IAAtD7B,EAAA,CAAAa,UAAA,WAAAN,MAAA,CAAA+E,gBAAA,KAAA/E,MAAA,CAAAoE,gBAAA,CAAAL,OAAA,EAAsD;;;;;;IAhErEtE,EAHJ,CAAAC,cAAA,cAA6D,cAE5B,gBACI;IAC/BD,EAAA,CAAAqC,UAAA,IAAAkD,uDAAA,oBAA2B;IAkC3BvF,EAAA,CAAAC,cAAA,YAAO;IACPD,EAAA,CAAAqC,UAAA,IAAAmD,oDAAA,iBAA6D;IAajExF,EAFI,CAAAY,YAAA,EAAQ,EACF,EACJ;IAINZ,EAAA,CAAAqC,UAAA,IAAAoD,qDAAA,mBAAyD;IAwBrDzF,EAFJ,CAAAC,cAAA,cAAyB,cACD,iBAC4E;IAAvCD,EAAA,CAAAE,UAAA,mBAAAwF,wEAAA;MAAA1F,EAAA,CAAAK,aAAA,CAAAsF,GAAA;MAAA,MAAApF,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAASF,MAAA,CAAAqF,qBAAA,CAAsB,KAAK,CAAC;IAAA,EAAC;IAAC5F,EAAA,CAAAuB,MAAA,eAChG;IACFvB,EADE,CAAAY,YAAA,EAAS,EACL;IAEJZ,EADF,CAAAC,cAAA,eAAsB,kBAC6D;IAAzBD,EAAA,CAAAE,UAAA,mBAAA2F,yEAAA;MAAA7F,EAAA,CAAAK,aAAA,CAAAsF,GAAA;MAAA,MAAApF,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAASF,MAAA,CAAAuF,YAAA,EAAc;IAAA,EAAC;IAAC9F,EAAA,CAAAuB,MAAA,aAAK;IAI5FvB,EAJ4F,CAAAY,YAAA,EAAS,EAC3F,EACF,EAEF;;;;IApFQZ,EAAA,CAAA6B,SAAA,GAAiB;IAAjB7B,EAAA,CAAAa,UAAA,SAAAN,MAAA,CAAA4B,WAAA,CAAiB;IAmCJnC,EAAA,CAAA6B,SAAA,GAAe;IAAf7B,EAAA,CAAAa,UAAA,YAAAN,MAAA,CAAAwF,YAAA,CAAe;IAiBR/F,EAAA,CAAA6B,SAAA,EAAuB;IAAvB7B,EAAA,CAAAa,UAAA,SAAAN,MAAA,CAAAyF,iBAAA,CAAuB;;;;;IAzD3DhG,EAAA,CAAAiE,uBAAA,GAAoC;IAClCjE,EAAA,CAAAqC,UAAA,IAAA4D,+CAAA,mBAA6D;;;;ADPjE,MAAMC,YAAY,GAAG,EAAE;AACvB,MAAMC,OAAO,GAAG,EAAE;AAElB,MAAMC,kBAAkB,GAAwB;EAC9CC,IAAI,EAAE;CACP;AAYD,OAAM,MAAOC,iBAAiB;EAkG5B,IACIC,eAAeA,CAACC,KAAc;IAChC,IAAI,CAACC,YAAY,GAAGD,KAAK;EAC3B;EAEA,IACIE,gBAAgBA,CAACF,KAA+B;IAClD,IAAI,CAACG,iBAAiB,GAAGH,KAAK;IAE9B,IAAI,CAAC7B,gBAAgB,GAAG;MACtBI,KAAK,EAAEyB,KAAK,KAAK,OAAO;MACxBtB,OAAO,EAAEsB,KAAK,KAAK,OAAO,IAAIA,KAAK,KAAK,SAAS;MACjDlC,OAAO,EAAEkC,KAAK,KAAK,OAAO,IAAIA,KAAK,KAAK,SAAS,IAAIA,KAAK,KAAK;KAChE;IAED;IACA,IAAI,CAACI,WAAW,GAAG,IAAI,CAACA,WAAW;EACrC;EAEA;;;;EAIA,IAAIA,WAAWA,CAAA;IACb,OAAO,IAAI,CAACC,YAAY;EAC1B;EAEA;;;;EAIA,IAAID,WAAWA,CAACJ,KAAU;IACxB,IAAI,CAACM,YAAY,CAACN,KAAK,CAAC;IACxB,IAAI,CAACO,UAAU,CAACC,IAAI,CAAC,IAAI,CAACH,YAAY,CAAC;EACzC;EAEA,IAAIH,gBAAgBA,CAAA;IAClB,OAAO,IAAI,CAACC,iBAAiB;EAC/B;EACA,IAAIM,cAAcA,CAAA;IAChB,IAAI,IAAI,CAACL,WAAW,EAAE;MACpB,MAAMM,UAAU,GAAG,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAACC,WAAW,CAACF,UAAU,CAAC;MACrE,OAAO,IAAI,CAACN,WAAW,CAAClD,MAAM,CAAC,GAAG,IAAI,CAAC0D,WAAW,CAACC,UAAU,IAAIH,UAAU,EAAE,CAAC;IAChF;IACA,OAAO,EAAE;EACX;EAGAI,YAAsBC,GAAsB,EAChCC,eAAgC,EAClCC,eAAgC;IAFpB,KAAAF,GAAG,GAAHA,GAAG;IACb,KAAAC,eAAe,GAAfA,eAAe;IACjB,KAAAC,eAAe,GAAfA,eAAe;IAlJzB;;;;IAIS,KAAAC,WAAW,GAAG,aAAa;IAI3B,KAAA9E,kBAAkB,GAAY,KAAK;IAKnC,KAAA+E,UAAU,GAAY,IAAI;IAE1B,KAAA3B,iBAAiB,GAAY,IAAI;IAEjC,KAAA4B,gBAAgB,GAAY,IAAI;IAE/B,KAAAb,UAAU,GAAG,IAAIpH,YAAY,EAAO;IACpC,KAAAkI,OAAO,GAAG,IAAIlI,YAAY,EAAQ;IAE5C,KAAAqB,QAAQ,GAAY,KAAK;IACzB,KAAAyF,YAAY,GAAY,KAAK;IAEpB,KAAAnB,gBAAgB,GAAY,KAAK;IAE1C;;;;IAIA,KAAAwC,cAAc,GAAG,KAAK;IAGtB,KAAAC,WAAW,GAAwB3B,kBAAkB;IAerD;;;IAGA,KAAArB,KAAK,GAAG,CAAC;IACT,KAAAG,OAAO,GAAG,CAAC;IACX,KAAAZ,OAAO,GAAG,CAAC;IAEX;;;IAGA,KAAAzB,QAAQ,GAAG9C,MAAM,CAACiI,aAAa,EAAE;IACjC;;;IAGA,KAAAvF,UAAU,GAAG1C,MAAM,CAACkI,MAAM,EAAE;IAG5B,KAAA5C,YAAY,GAAI,IAAI6C,KAAK,CAAChC,YAAY,CAAC,CAAEiC,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAC;IACjE,KAAA1D,aAAa,GAAI,IAAIsD,KAAK,CAAC/B,OAAO,CAAC,CAAEgC,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAC;IAE7D;;;IAGA,KAAA3D,gBAAgB,GAAgC;MAC9CI,KAAK,EAAE,IAAI;MACXG,OAAO,EAAE,IAAI;MACbZ,OAAO,EAAE;KACV;IAkBO,KAAAiE,IAAI,GAAmB,EAAE;IAsD/B,IAAI,CAACC,OAAO,GAAGC,MAAM,CAAC,UAAU,CAAC;IACjC,IAAI,CAAChH,KAAK,GAAG1B,MAAM,EAAE;EACvB;EAEA2I,QAAQA,CAAA;IACN,IAAI,CAACH,IAAI,CAACI,IAAI,CAAC,IAAI,CAACnB,eAAe,CAACoB,YAAY,CAACC,SAAS,CAACC,IAAI,IAAG;MAChE,IAAI,CAACC,gBAAgB,CAACD,IAAI,CAAC;IAC7B,CAAC,CAAC,CAAC;IACH,IAAI,CAACE,sBAAsB,EAAE;EAC/B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACF,gBAAgB,CAAC,IAAI,CAACvB,eAAe,CAACJ,WAAW,CAAC;EACzD;EAEA8B,WAAWA,CAAA;IACT,IAAI,CAACX,IAAI,CAACY,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;EAC7C;EAEAC,QAAQA,CAACC,OAAoB;IAC3B,IAAI/C,KAAK,GAAG+C,OAAO,CAAC/C,KAAK;IACzB,OAAO,CAACgD,IAAI,CAACC,KAAK,CAACjD,KAAK,CAAC;EAC3B;EAEAkD,UAAUA,CAAClD,KAAK;IACd,IAAI,CAACmD,IAAI,GAAGnD,KAAK;IACjB,IAAI,CAACI,WAAW,GAAGJ,KAAK;EAC1B;EAEAoD,gBAAgBA,CAACC,EAAE;IACjB,IAAI,CAACC,eAAe,GAAGD,EAAE;EAC3B;EAEAE,iBAAiBA,CAAA,GACjB;EAEAC,gBAAgBA,CAAChJ,QAAiB;IAChC,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC1B;EAEA;;;;;EAKOG,SAASA,CAAC8I,KAAU,EAAEC,GAAQ;IACnCD,KAAK,CAACE,cAAc,EAAE;IAEtB,IAAI,CAAC/I,YAAY,GAAG8I,GAAG,CAAC7I,KAAK,EAAE;IAE/B;IACA;IACA,IAAI,CAAC,IAAI,CAACD,YAAY,CAACgC,MAAM,CAAC,IAAI,CAACjB,WAAW,EAAE,OAAO,CAAC,EAAE;MACxD,IAAI,CAACD,QAAQ,CAAC,IAAI,CAACd,YAAY,CAACC,KAAK,EAAE,CAAC;IAC1C;EACF;EAEA;;;EAGOyE,YAAYA,CAAA;IACjB,MAAM;MAAEsE,KAAK;MAAEnC,MAAM;MAAE0B;IAAI,CAAE,GAAG,IAAI,CAACvI,YAAY,CAACiJ,QAAQ,EAAE;IAC5D,IAAI,CAACzD,WAAW,GAAG,IAAI,CAACxF,YAAY,CACjCC,KAAK,EAAE,CACPsB,IAAI,CAACyH,KAAK,CAAC,CACX1H,KAAK,CAACuF,MAAM,CAAC,CACb0B,IAAI,CAACA,IAAI,CAAC,CACV5E,KAAK,CAAC,IAAI,CAACA,KAAK,CAAC,CACjBG,OAAO,CAAC,IAAI,CAACA,OAAO,CAAC,CACrBZ,OAAO,CAAC,IAAI,CAACA,OAAO,CAAC;IAExB,IAAI,CAACsB,qBAAqB,CAAC,KAAK,CAAC;EACnC;EAEOA,qBAAqBA,CAACY,KAAc;IACzC,IAAI,CAACsB,cAAc,GAAGtB,KAAK;IAC3B,IAAI,CAACe,GAAG,CAAC+C,aAAa,EAAE;IACxB,IAAI,CAACC,eAAe,CAAC/D,KAAK,CAAC;EAC7B;EAEA;;;EAGO9F,KAAKA,CAACuJ,KAAa;IACxB,IAAIA,KAAK,EAAE;MACTA,KAAK,CAACO,eAAe,EAAE;IACzB;IAEA,IAAI,IAAI,CAACxJ,QAAQ,EAAE;IACnB,IAAI,CAAC4F,WAAW,GAAG6D,SAAS;IAE5B,IAAI,CAACrJ,YAAY,GAAGrB,MAAM,EAAE;IAE5B,IAAI,CAACmC,QAAQ,CAAC,IAAI,CAACd,YAAY,CAAC;IAChC,IAAI,CAACwE,qBAAqB,CAAC,KAAK,CAAC;IAEjC,IAAI,CAACiC,OAAO,CAACb,IAAI,EAAE;EACrB;EAEA;;;;EAIO9E,QAAQA,CAACyH,IAAmB;IACjC,IAAIe,QAAQ,GAAG3K,MAAM,CAAC4J,IAAI,CAAC,CAACgB,OAAO,CAAC,OAAO,CAAC;IAC5C,IAAIC,OAAO,GAAG7K,MAAM,CAAC4J,IAAI,CAAC,CAACkB,KAAK,CAAC,OAAO,CAAC;IAEzC,IAAIC,MAAM,GAAG,EAAE;IAEf,OAAOJ,QAAQ,CAACf,IAAI,EAAE,IAAIiB,OAAO,CAACjB,IAAI,EAAE,EAAE;MACxC,IAAI,CAACmB,MAAM,CAACC,MAAM,IAAI,CAACL,QAAQ,CAACR,GAAG,EAAE,EAAE;QACrCY,MAAM,CAACnC,IAAI,CAAC,EAAE,CAAC;MACjB;MAEAmC,MAAM,CAACA,MAAM,CAACC,MAAM,GAAG,CAAC,CAAC,CAACL,QAAQ,CAACR,GAAG,EAAE,CAAC,GAAGnK,MAAM,CAAC2K,QAAQ,CAACM,OAAO,EAAE,CAAC;MACtE,IAAIN,QAAQ,CAACf,IAAI,EAAE,KAAKiB,OAAO,CAACjB,IAAI,EAAE,EAAE;QACtC;MACF,CAAC,MAAM;QACLe,QAAQ,CAACpJ,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC;MACzB;IACF;IAEA,IAAI,CAACa,WAAW,GAAGwH,IAAI,CAACgB,OAAO,CAAC,KAAK,CAAC;IACtC,IAAI,CAAC5E,YAAY,GAAG+E,MAAM;EAC5B;EAEOG,iBAAiBA,CAACC,QAAgB;IACvC,IAAI,IAAI,CAAClK,QAAQ,EAAE;IACnB,IAAI,CAAC4F,WAAW,GAAGsE,QAAQ;EAC7B;EAEA;;;EAGOC,mBAAmBA,CAAA;IACxB,IAAI,IAAI,CAACnF,iBAAiB,EAAE;MAC1B,OAAO,IAAI,CAACoB,WAAW,CAACC,UAAU,GAAG,GAAG,GAAG,IAAI,CAACD,WAAW,CAACF,UAAU;IACxE,CAAC,MAAM;MACL,OAAO,IAAI,CAACE,WAAW,CAACC,UAAU;IACpC;EACF;EAEO+D,gBAAgBA,CAAA;IACrB,IAAI,CAACtD,cAAc,GAAG,KAAK;EAC7B;EAEQhB,YAAYA,CAACN,KAAU;IAC7B,IAAIA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MACtC;MACA,IAAI,IAAI,CAACR,iBAAiB,EAAE;QAC1B,IAAI,CAACa,YAAY,GAAG9G,MAAM,CAACyG,KAAK,EAAE,IAAI,CAACY,WAAW,CAACC,UAAU,GAAG,GAAG,GAAG,IAAI,CAACD,WAAW,CAACF,UAAU,CAAC;MACpG,CAAC,MAAM;QACL,IAAI,CAACL,YAAY,GAAG9G,MAAM,CAACyG,KAAK,EAAE,IAAI,CAACY,WAAW,CAACC,UAAU,CAAC;MAChE;MACA,IAAI,CAAC,IAAI,CAACR,YAAY,CAACwE,OAAO,EAAE,EAAE;QAChC,IAAI,CAACxE,YAAY,GAAG9G,MAAM,EAAE;MAC9B;IACF,CAAC,MAAM;MACL,IAAI,CAAC8G,YAAY,GAAGL,KAAK;IAC3B;IAEA,IAAI,IAAI,CAACK,YAAY,EAAE;MACrB,IAAI,CAACA,YAAY,CAACyE,WAAW,CAAC,CAAC,CAAC;MAChC,IAAI,CAACzE,YAAY,GAAG,IAAI,CAAC0E,eAAe,CAAC,IAAI,CAAC1E,YAAY,CAAC;MAE3D,IAAI,CAAC,IAAI,CAAClC,gBAAgB,CAACI,KAAK,EAAE,IAAI,CAAC8B,YAAY,CAAC9B,KAAK,CAAC,CAAC,CAAC;MAC5D,IAAI,CAAC,IAAI,CAACJ,gBAAgB,CAACO,OAAO,EAAE,IAAI,CAAC2B,YAAY,CAAC3B,OAAO,CAAC,CAAC,CAAC;MAChE,IAAI,CAAC,IAAI,CAACP,gBAAgB,CAACL,OAAO,EAAE,IAAI,CAACuC,YAAY,CAACvC,OAAO,CAAC,CAAC,CAAC;IAClE;IAEA,IAAI,CAACkH,gBAAgB,EAAE;IACvB,IAAI,CAACtJ,QAAQ,CAAC,IAAI,CAACd,YAAY,CAAC;IAEhC,IAAI,OAAO,IAAI,CAAC0I,eAAe,KAAK,UAAU,EAAE;MAC9C,IAAI,CAACA,eAAe,CAAC,IAAI,CAACjD,YAAY,IAAI,IAAI,CAACA,YAAY,CAAC4E,QAAQ,EAAE,CAAC;IACzE;EACF;EAEQ1C,gBAAgBA,CAACD,IAAiB;IACxC,IAAI,CAAC1B,WAAW,GAAG0B,IAAI;IACvB,IAAI,CAAC4C,cAAc,EAAE;IAErB,IAAI,IAAI,CAAC/B,IAAI,EAAE;MACb,IAAI,CAAC7C,YAAY,CAAC,IAAI,CAAC6C,IAAI,CAAC;IAC9B,CAAC,MAAM,IAAI,IAAI,CAAC/B,gBAAgB,EAAE;MAChC,IAAI,CAAClH,KAAK,EAAE;IACd;EACF;EAEQgL,cAAcA,CAAA;IACpB,IAAI,IAAI,CAAC/D,UAAU,EAAE;MACnB,MAAMT,UAAU,GAAG,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAACC,WAAW,CAACF,UAAU,CAAC;MACrE,MAAMyE,QAAQ,GAAG,IAAI,CAACC,cAAc,CAAC,IAAI,CAACxE,WAAW,CAACC,UAAU,CAAC;MACjE,MAAMwE,QAAQ,GAAG,IAAI,CAACD,cAAc,CAAC1E,UAAU,CAAC;MAEhD,IAAI,IAAI,CAAClB,iBAAiB,EAAE;QAC1B,IAAI,CAAC+B,WAAW,GAAG;UACjB1B,IAAI,EAAEsF,QAAQ,CAACG,MAAM,CAAC,GAAG,CAAC,CAACA,MAAM,CAACD,QAAQ;SAC3C;MACH,CAAC,MAAM;QACL,IAAI,CAAC9D,WAAW,GAAG;UACjB1B,IAAI,EAAEsF;SACP;MACH;IACF,CAAC,MAAM;MACL,IAAI,CAAC5D,WAAW,GAAG3B,kBAAkB;IACvC;EACF;EAEQwF,cAAcA,CAACG,GAAW;IAChC,OAAOA,GAAG,CACPC,KAAK,CAAC,EAAE,CAAC,CACT5D,GAAG,CAAC6D,CAAC,IAAI,IAAI,CAACC,IAAI,CAACD,CAAC,CAAC,GAAG,IAAIE,MAAM,CAAC,OAAO,CAAC,GAAGF,CAAC,CAAC;EACrD;EAEQT,gBAAgBA,CAAA;IACtB,IAAI,CAACpK,YAAY,GAAG,IAAI,CAACwF,WAAW,GAAG,IAAI,CAACA,WAAW,CAACvF,KAAK,EAAE,GAAGtB,MAAM,EAAE;IAE1E,IAAI,CAACqB,YAAY,GAAG,IAAI,CAACmK,eAAe,CAAC,IAAI,CAACnK,YAAY,CAAC;IAE3D,IAAI,CAAC2D,KAAK,GAAG,IAAI,CAACJ,gBAAgB,CAACI,KAAK,GAAG,IAAI,CAAC3D,YAAY,CAAC2D,KAAK,EAAE,GAAG,CAAC;IACxE,IAAI,CAACG,OAAO,GAAG,IAAI,CAACP,gBAAgB,CAACO,OAAO,GAAG,IAAI,CAAC9D,YAAY,CAAC8D,OAAO,EAAE,GAAG,CAAC;IAC9E,IAAI,CAACZ,OAAO,GAAG,IAAI,CAACK,gBAAgB,CAACL,OAAO,GAAG,IAAI,CAAClD,YAAY,CAACkD,OAAO,EAAE,GAAG,CAAC;EAChF;EAEQiH,eAAeA,CAAC5B,IAAmB;IACzC,IAAI,IAAI,CAACtG,OAAO,IAAIsG,IAAI,IAAI,IAAI,CAACtG,OAAO,CAACG,OAAO,CAACmG,IAAI,EAAE,MAAM,CAAC,EAAE,OAAO,IAAI,CAACtG,OAAO,CAAChC,KAAK,EAAE;IAC3F,IAAI,IAAI,CAACkC,OAAO,IAAIoG,IAAI,IAAI,IAAI,CAACpG,OAAO,CAACD,QAAQ,CAACqG,IAAI,EAAE,MAAM,CAAC,EAAE,OAAO,IAAI,CAACpG,OAAO,CAAClC,KAAK,EAAE;IAE5F,OAAOsI,IAAI;EACb;EAEA;;;;EAIA;EACQyC,oBAAoBA,CAAC5F,KAAc;IACzC,IAAI,CAACsB,cAAc,GAAGtB,KAAK;EAC7B;EAEQW,gBAAgBA,CAACD,UAAkB;IACzC,IAAI,IAAI,CAAC5B,gBAAgB,EAAE;MAEzB,IAAI,CAAC,IAAI,CAACX,gBAAgB,CAACL,OAAO,EAAE;QAClC4C,UAAU,GAAGA,UAAU,CAACmF,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC;MACpD;MAEA,IAAI,CAAC,IAAI,CAAC1H,gBAAgB,CAACO,OAAO,EAAE;QAClCgC,UAAU,GAAGA,UAAU,CAACmF,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC;MACpD;IAEF;IACA,OAAOnF,UAAU;EACnB;EAEQ8B,sBAAsBA,CAAA;IAC5B,MAAMsD,oBAAoB,GAAG,IAAI,CAAC7E,eAAe,CAAC8E,SAAS,CAAC1D,SAAS,CAAErC,KAAe,IAAI;MACxFA,KAAK,CAAC2C,OAAO,CAACqD,GAAG,IAAG;QAClB,IAAIA,GAAG,KAAK,IAAI,CAAChE,OAAO,EAAE;UACxB,IAAI,CAACV,cAAc,GAAG,IAAI;UAC1B,IAAI,CAAC2E,gBAAgB,EAAE;QACzB,CAAC,MAAM;UACL,IAAI,CAAC3E,cAAc,GAAG,KAAK;QAC7B;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,IAAI,CAACS,IAAI,CAACI,IAAI,CAAC2D,oBAAoB,CAAC;EACtC;EAEQ/B,eAAeA,CAAC/D,KAAc;IACpCA,KAAK,GAAG,IAAI,CAACiB,eAAe,CAACiF,WAAW,CAAC,IAAI,CAAClE,OAAO,CAAC,GAAG,IAAI,CAACf,eAAe,CAACkF,cAAc,CAAC,IAAI,CAACnE,OAAO,CAAC;EAC5G;EAEQiE,gBAAgBA,CAAA;IACtB,MAAMG,WAAW,GAAG,IAAI,CAACC,QAAQ,CAACC,aAAa,CAACC,qBAAqB,EAAE,CAACC,MAAM;IAC9E,MAAMC,cAAc,GAAG,GAAG;IAC1B,MAAMC,cAAc,GAAGD,cAAc,GAAGL,WAAW;IACnD,MAAMO,YAAY,GAAGC,MAAM,CAACC,WAAW;IACvC,MAAMC,SAAS,GAAG,IAAI,CAACT,QAAQ,CAACC,aAAa,CAACC,qBAAqB,EAAE,CAACQ,GAAG;IACzE,IAAI,CAACC,QAAQ,GAAGL,YAAY,GAAGG,SAAS,GAAGJ,cAAc;EAC3D;;;uCAhbW5G,iBAAiB,EAAAtG,EAAA,CAAAyN,iBAAA,CAAAzN,EAAA,CAAA0N,iBAAA,GAAA1N,EAAA,CAAAyN,iBAAA,CAAAE,EAAA,CAAAC,eAAA,GAAA5N,EAAA,CAAAyN,iBAAA,CAAAI,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAAjBxH,iBAAiB;MAAAyH,SAAA;MAAAC,SAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;uCALjB,CACT;QAAEE,OAAO,EAAEtO,iBAAiB;QAAEuO,WAAW,EAAEzO,UAAU,CAAC,MAAM0G,iBAAiB,CAAC;QAAEgI,KAAK,EAAE;MAAI,CAAE,EAC7F;QAAEF,OAAO,EAAEvO,aAAa;QAAEwO,WAAW,EAAEzO,UAAU,CAAC,MAAM0G,iBAAiB,CAAC;QAAEgI,KAAK,EAAE;MAAI,CAAE,CAC1F,GAAAtO,EAAA,CAAAuO,oBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAV,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCtCHlO,EAAA,CAAAC,cAAA,gBAQyC;UAApCD,EADA,CAAAE,UAAA,0BAAA2O,uDAAAzO,MAAA;YAAAJ,EAAA,CAAAK,aAAA,CAAAyO,GAAA;YAAA,OAAA9O,EAAA,CAAAS,WAAA,CAAgB0N,GAAA,CAAAvI,qBAAA,CAAAxF,MAAA,CAA6B;UAAA,EAAC,0BAAA2O,uDAAA;YAAA/O,EAAA,CAAAK,aAAA,CAAAyO,GAAA;YAAA,OAAA9O,EAAA,CAAAS,WAAA,CAC9B0N,GAAA,CAAA/C,gBAAA,EAAkB;UAAA,EAAC;UAIpCpL,EADF,CAAAC,cAAA,aAAwC,cACyC;UAC7ED,EAAA,CAAAW,SAAA,WAA+B;UACjCX,EAAA,CAAAY,YAAA,EAAO;UACPZ,EAAA,CAAAC,cAAA,kBAMoD;UAD7CD,EAAA,CAAAE,UAAA,oBAAA8O,mDAAA;YAAAhP,EAAA,CAAAK,aAAA,CAAAyO,GAAA;YAAA,MAAAG,QAAA,GAAAjP,EAAA,CAAAkP,WAAA;YAAA,OAAAlP,EAAA,CAAAS,WAAA,CAAU0N,GAAA,CAAAlD,iBAAA,CAAAgE,QAAA,CAAAzI,KAAA,CAA8B;UAAA,EAAC;UALhDxG,EAAA,CAAAY,YAAA,EAMoD;UAEpDZ,EAAA,CAAAqC,UAAA,IAAA8M,iCAAA,kBACyC;UAG3CnP,EAAA,CAAAY,YAAA,EAAM;UAENZ,EAAA,CAAAqC,UAAA,IAAA+M,yCAAA,0BAAoC;UA2FtCpP,EAAA,CAAAY,YAAA,EAAM;;;UAlHDZ,EAJA,CAAAa,UAAA,WAAAsN,GAAA,CAAArG,cAAA,CAAyB,eAAAqG,GAAA,CAAAnN,QAAA,CAEF,WAAAmN,GAAA,CAAAX,QAAA,CACJ,oBACA;UAMwBxN,EAAA,CAAA6B,SAAA,GAAkC;UAAlC7B,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAc,eAAA,KAAAC,GAAA,EAAAoN,GAAA,CAAAnN,QAAA,EAAkC;UAKvEhB,EAAA,CAAA6B,SAAA,GAAwB;UAIxB7B,EAJA,CAAAa,UAAA,aAAAsN,GAAA,CAAApG,WAAA,CAAwB,aAAAoG,GAAA,CAAAnN,QAAA,CACH,UAAAmN,GAAA,CAAAlH,cAAA,CACG,gBAAAkH,GAAA,CAAAzG,WAAA,kBAEoB;UAES1H,EAAA,CAAA6B,SAAA,GAAiB;UAAjB7B,EAAA,CAAAa,UAAA,SAAAsN,GAAA,CAAAvH,WAAA,CAAiB;UAMhE5G,EAAA,CAAA6B,SAAA,EAAmB;UAAnB7B,EAAA,CAAAa,UAAA,UAAAsN,GAAA,CAAA1H,YAAA,CAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}