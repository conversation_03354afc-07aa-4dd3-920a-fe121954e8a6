{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { FormControl } from '@angular/forms';\nimport { CdkVirtualScrollViewport } from '@angular/cdk/scrolling';\nimport { isObservable, of, Subject } from 'rxjs';\nimport { debounceTime, distinctUntilChanged, filter, map, startWith, switchMap, takeUntil, tap } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"@angular/material/checkbox\";\nimport * as i4 from \"@angular/material/form-field\";\nimport * as i5 from \"@angular/material/icon\";\nimport * as i6 from \"@angular/material/chips\";\nimport * as i7 from \"@angular/material/input\";\nimport * as i8 from \"@angular/cdk/scrolling\";\nimport * as i9 from \"@angular/material/button\";\nimport * as i10 from \"@angular/flex-layout/flex\";\nimport * as i11 from \"../../../../../../../common/directives/trim-input-value/trim-input-value.component\";\nimport * as i12 from \"@ngx-translate/core\";\nconst _c0 = a0 => ({\n  height: a0\n});\nconst _c1 = a0 => ({\n  \"selected\": a0\n});\nfunction TaggedItemsComponent_button_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function TaggedItemsComponent_button_10_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.clearSearch());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"close\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TaggedItemsComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 14);\n    i0.ɵɵelement(2, \"i\", 15);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TaggedItemsComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"COMPONENTS.GAMES_SELECT_MANAGER.noGamesToShow\"), \" \");\n  }\n}\nfunction TaggedItemsComponent_div_16_mat_chip_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-chip\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"titlecase\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const label_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getLabelClass(label_r5.group));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, label_r5.title), \" \");\n  }\n}\nfunction TaggedItemsComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"div\", 17)(2, \"mat-checkbox\", 18);\n    i0.ɵɵlistener(\"change\", function TaggedItemsComponent_div_16_Template_mat_checkbox_change_2_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.emitOnChanged());\n    });\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TaggedItemsComponent_div_16_Template_mat_checkbox_ngModelChange_2_listener($event) {\n      const game_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      i0.ɵɵtwoWayBindingSet(game_r4.checked, $event) || (game_r4.checked = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(3, \"div\", 19);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(5, \"div\", 3);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 20)(8, \"mat-chip-list\");\n    i0.ɵɵtemplate(9, TaggedItemsComponent_div_16_mat_chip_9_Template, 3, 4, \"mat-chip\", 21);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const game_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(7, _c1, game_r4.checked));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", game_r4.checked);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.disabled);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"title\", game_r4.title);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(game_r4.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", game_r4.id, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", game_r4.labels);\n  }\n}\nexport class TaggedItemsComponent {\n  constructor(cd) {\n    this.cd = cd;\n    this.items = [];\n    this.selectedItems = [];\n    this.disabled = false;\n    this.height = '500px';\n    this.checkedOnly = false;\n    this.changed = new EventEmitter();\n    this.searchInput = new FormControl('');\n    this.loading = false;\n    this.availableItems = [];\n    this.cachedItems = [];\n    this.inputChanged$ = new Subject();\n    this.destroyed$ = new Subject();\n    this.trackByFn = (_, {\n      id\n    }) => id;\n    this.inputChanged$.pipe(switchMap(() => isObservable(this.items) ? this.items : of(this.items)), filter(items => !!items), tap(() => {\n      this.loading = true;\n    }), map(items => items.map(item => ({\n      ...item,\n      checked: this.selectedItems.indexOf(item.id) !== -1\n    }))), startWith([]), tap(() => {\n      this.loading = false;\n    }), takeUntil(this.destroyed$)).subscribe(items => {\n      this.cd.markForCheck();\n      this.cachedItems = items;\n      this.setSearchTerm(this.searchInput.value);\n    });\n  }\n  ngOnInit() {\n    if (this.searchInput) {\n      this.searchInput.valueChanges.pipe(debounceTime(100), distinctUntilChanged(), takeUntil(this.destroyed$)).subscribe(search => {\n        this.setSearchTerm(search);\n      });\n    }\n  }\n  ngOnChanges({\n    selectedItems,\n    items\n  }) {\n    if (items || selectedItems) {\n      this.inputChanged$.next();\n    } else {\n      this.setSearchTerm(this.searchInput.value);\n    }\n  }\n  ngAfterViewInit() {\n    if (!this.virtualScroll) {\n      return;\n    }\n    const observer = new IntersectionObserver(entries => {\n      entries.forEach(({\n        isIntersecting\n      }) => {\n        if (isIntersecting && this.virtualScroll) {\n          this.virtualScroll.checkViewportSize();\n        }\n      });\n    });\n    observer.observe(this.virtualScroll.elementRef.nativeElement);\n  }\n  ngOnDestroy() {\n    this.destroyed$.next();\n    this.destroyed$.complete();\n  }\n  get isAllChecked() {\n    return this.availableItems.every(({\n      checked\n    }) => checked);\n  }\n  toggleAllChecked() {\n    const allChecked = !this.isAllChecked;\n    this.availableItems.forEach(game => {\n      game.checked = allChecked;\n    });\n    this.cd.markForCheck();\n    this.emitOnChanged();\n  }\n  emitOnChanged() {\n    this.changed.emit(this.cachedItems.filter(({\n      checked\n    }) => checked).map(id => id));\n    this.setSearchTerm(this.searchInput.value);\n  }\n  getLabelClass(group) {\n    switch (group) {\n      case 'platform':\n        return 'sw-bg-purple';\n      case 'class':\n        return 'sw-bg-deep-orange';\n      case 'feature':\n        return 'sw-bg-red';\n      default:\n        return 'sw-bg-light-blue';\n    }\n  }\n  clearSearch() {\n    this.searchInput.setValue('');\n  }\n  setSearchTerm(search) {\n    const needle = search.toLowerCase();\n    this.availableItems = this.cachedItems.filter(({\n      id,\n      title,\n      labels,\n      checked\n    }) => {\n      return (this.checkedOnly ? checked : true) && (title.toLowerCase().indexOf(needle) > -1 || id.toLowerCase().indexOf(needle) > -1 || labels.map(label => label.title.toLowerCase()).filter(text => text.indexOf(needle) > -1).length > 0);\n    });\n    this.cd.markForCheck();\n  }\n  static {\n    this.ɵfac = function TaggedItemsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TaggedItemsComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TaggedItemsComponent,\n      selectors: [[\"sw-tagged-items\"]],\n      viewQuery: function TaggedItemsComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(CdkVirtualScrollViewport, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.virtualScroll = _t.first);\n        }\n      },\n      inputs: {\n        items: \"items\",\n        selectedItems: \"selectedItems\",\n        disabled: \"disabled\",\n        height: \"height\",\n        checkedOnly: \"checkedOnly\"\n      },\n      outputs: {\n        changed: \"changed\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature],\n      decls: 17,\n      vars: 20,\n      consts: [[1, \"table-sticky\", \"bordered\", 3, \"ngStyle\"], [\"fxLayout\", \"row\", \"fxLayoutAlign\", \"end center\", 1, \"table-sticky__header\"], [1, \"table-sticky__select-all\", 3, \"change\", \"checked\", \"disabled\"], [1, \"table-sticky__info\"], [\"appearance\", \"outline\", 1, \"table-sticky__search\", \"no-field-padding\"], [\"matPrefix\", \"\", 1, \"search-icon\"], [\"matInput\", \"\", \"trimValue\", \"\", \"type\", \"text\", 3, \"formControl\", \"placeholder\", \"disabled\"], [\"mat-button\", \"\", \"matSuffix\", \"\", \"mat-icon-button\", \"\", \"aria-label\", \"Clear\", 3, \"click\", 4, \"ngIf\"], [1, \"table-sticky__body\"], [1, \"table-sticky__scroll\", 3, \"minBufferPx\", \"maxBufferPx\", \"itemSize\"], [1, \"table-sticky__table\"], [4, \"ngIf\"], [\"class\", \"table-sticky__row\", 3, \"ngClass\", 4, \"cdkVirtualFor\", \"cdkVirtualForOf\", \"cdkVirtualForTemplateCacheSize\", \"cdkVirtualForTrackBy\"], [\"mat-button\", \"\", \"matSuffix\", \"\", \"mat-icon-button\", \"\", \"aria-label\", \"Clear\", 3, \"click\"], [1, \"loading-overlay\"], [1, \"icon-spinner4\", \"spinner\"], [1, \"table-sticky__row\", 3, \"ngClass\"], [1, \"table-sticky__checkbox\"], [1, \"games-checkbox\", 3, \"change\", \"ngModelChange\", \"ngModel\", \"disabled\"], [1, \"table-sticky__checkbox_label\", 3, \"title\"], [1, \"table-sticky__chips\"], [\"class\", \"chip\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"chip\", 3, \"ngClass\"]],\n      template: function TaggedItemsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"mat-checkbox\", 2);\n          i0.ɵɵlistener(\"change\", function TaggedItemsComponent_Template_mat_checkbox_change_2_listener() {\n            return ctx.toggleAllChecked();\n          });\n          i0.ɵɵtext(3);\n          i0.ɵɵpipe(4, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 3)(6, \"mat-form-field\", 4)(7, \"mat-icon\", 5);\n          i0.ɵɵtext(8, \"search\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(9, \"input\", 6);\n          i0.ɵɵtemplate(10, TaggedItemsComponent_button_10_Template, 3, 0, \"button\", 7);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(11, \"div\", 8)(12, \"cdk-virtual-scroll-viewport\", 9)(13, \"div\", 10);\n          i0.ɵɵtemplate(14, TaggedItemsComponent_div_14_Template, 3, 0, \"div\", 11)(15, TaggedItemsComponent_div_15_Template, 3, 3, \"div\", 11)(16, TaggedItemsComponent_div_16_Template, 10, 9, \"div\", 12);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(18, _c0, ctx.height));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"checked\", ctx.isAllChecked)(\"disabled\", ctx.disabled || !ctx.availableItems.length);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 16, \"COMPONENTS.GAMES_SELECT_MANAGER.selectAll\"), \" \");\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"formControl\", ctx.searchInput)(\"placeholder\", \"Search by Name or Label\")(\"disabled\", ctx.disabled);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.searchInput.value);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"minBufferPx\", 960)(\"maxBufferPx\", 1008)(\"itemSize\", 48);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", (!ctx.availableItems || ctx.availableItems.length === 0) && !ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"cdkVirtualForOf\", ctx.availableItems)(\"cdkVirtualForTemplateCacheSize\", 0)(\"cdkVirtualForTrackBy\", ctx.trackByFn);\n        }\n      },\n      dependencies: [i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgStyle, i2.DefaultValueAccessor, i2.NgControlStatus, i2.FormControlDirective, i2.NgModel, i3.MatCheckbox, i4.MatFormField, i4.MatPrefix, i4.MatSuffix, i5.MatIcon, i6.MatChip, i7.MatInput, i8.CdkFixedSizeVirtualScroll, i8.CdkVirtualForOf, i8.CdkVirtualScrollViewport, i9.MatButton, i9.MatIconButton, i10.DefaultLayoutDirective, i10.DefaultLayoutAlignDirective, i11.TrimInputValueComponent, i1.TitleCasePipe, i12.TranslatePipe],\n      styles: [\".table-games[_ngcontent-%COMP%] {\\n  overflow: auto;\\n}\\n\\n.loading-overlay[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n\\n.table-sticky__scroll[_ngcontent-%COMP%] {\\n  height: 420px;\\n}\\n.table-sticky__row[_ngcontent-%COMP%] {\\n  min-height: 48px;\\n  border-bottom: 1px solid rgba(0, 0, 0, 0.12);\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n.table-sticky__row.selected[_ngcontent-%COMP%] {\\n  background: #FFF7E8;\\n}\\n.table-sticky__checkbox[_ngcontent-%COMP%] {\\n  padding: 4px 0 4px 24px;\\n  overflow: hidden;\\n  flex: 1;\\n}\\n.table-sticky__checkbox_label[_ngcontent-%COMP%] {\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n.table-sticky__checkbox[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%] {\\n  display: flex;\\n  overflow: hidden;\\n}\\n.table-sticky__chips[_ngcontent-%COMP%] {\\n  min-width: 250px;\\n  max-width: 250px;\\n  width: 250px;\\n  padding: 4px 24px;\\n  flex: 1;\\n}\\n.table-sticky__info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding-left: 24px;\\n  flex: 1;\\n}\\n.table-sticky__search[_ngcontent-%COMP%] {\\n  width: 240px;\\n}\\n.table-sticky__table[_ngcontent-%COMP%] {\\n  border-collapse: collapse;\\n}\\n.table-sticky__body[_ngcontent-%COMP%] {\\n  margin-top: 10px;\\n  height: calc(70vh - 200px);\\n}\\n\\n.selected[_ngcontent-%COMP%] {\\n  background: #FFF7E8;\\n}\\n\\n.search-icon[_ngcontent-%COMP%] {\\n  color: rgba(0, 0, 0, 0.42);\\n  margin-right: 4px;\\n}\\n\\n.chip[_ngcontent-%COMP%] {\\n  color: #fff;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      changeDetection: 0\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "FormControl", "CdkVirtualScrollViewport", "isObservable", "of", "Subject", "debounceTime", "distinctUntilChanged", "filter", "map", "startWith", "switchMap", "takeUntil", "tap", "i0", "ɵɵelementStart", "ɵɵlistener", "TaggedItemsComponent_button_10_Template_button_click_0_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "clearSearch", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "ɵɵproperty", "getLabelClass", "label_r5", "group", "title", "TaggedItemsComponent_div_16_Template_mat_checkbox_change_2_listener", "_r3", "emitOn<PERSON><PERSON>ed", "ɵɵtwoWayListener", "TaggedItemsComponent_div_16_Template_mat_checkbox_ngModelChange_2_listener", "$event", "game_r4", "$implicit", "ɵɵtwoWayBindingSet", "checked", "ɵɵtemplate", "TaggedItemsComponent_div_16_mat_chip_9_Template", "ɵɵpureFunction1", "_c1", "ɵɵtwoWayProperty", "disabled", "ɵɵtextInterpolate", "id", "labels", "TaggedItemsComponent", "constructor", "cd", "items", "selectedItems", "height", "checkedOnly", "changed", "searchInput", "loading", "availableItems", "cachedItems", "inputChanged$", "destroyed$", "trackByFn", "_", "pipe", "item", "indexOf", "subscribe", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setSearchTerm", "value", "ngOnInit", "valueChanges", "search", "ngOnChanges", "next", "ngAfterViewInit", "virtualScroll", "observer", "IntersectionObserver", "entries", "for<PERSON>ach", "isIntersecting", "checkViewportSize", "observe", "elementRef", "nativeElement", "ngOnDestroy", "complete", "isAllChecked", "every", "toggleAllChecked", "allChecked", "game", "emit", "setValue", "needle", "toLowerCase", "label", "text", "length", "ɵɵdirectiveInject", "ChangeDetectorRef", "selectors", "viewQuery", "TaggedItemsComponent_Query", "rf", "ctx", "TaggedItemsComponent_Template_mat_checkbox_change_2_listener", "TaggedItemsComponent_button_10_Template", "TaggedItemsComponent_div_14_Template", "TaggedItemsComponent_div_15_Template", "TaggedItemsComponent_div_16_Template", "_c0"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-rtp-reducer/rtp-reduce-modal/tagged-items/tagged-items.component.ts", "/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-rtp-reducer/rtp-reduce-modal/tagged-items/tagged-items.component.html"], "sourcesContent": ["import {\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  EventEmitter,\n  Input,\n  OnChanges,\n  OnDestroy,\n  OnInit,\n  Output,\n  SimpleChanges, ViewChild\n} from '@angular/core';\nimport { FormControl } from '@angular/forms';\nimport { CdkVirtualScrollViewport } from '@angular/cdk/scrolling';\nimport { isObservable, Observable, of, Subject } from 'rxjs';\nimport { debounceTime, distinctUntilChanged, filter, map, startWith, switchMap, takeUntil, tap } from 'rxjs/operators';\n\nexport interface TaggedItem {\n  checked: boolean;\n  currentRtp?: number;\n  currentRtpMax?: number;\n  currentRtpMin?: number;\n  readonly id: string;\n  readonly title: string;\n  readonly labels: {\n    readonly group: string;\n    readonly title: string;\n  }[];\n}\n\n@Component({\n  selector: 'sw-tagged-items',\n  templateUrl: 'tagged-items.component.html',\n  styleUrls: ['./tagged-items.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class TaggedItemsComponent implements OnInit, OnChanges, OnD<PERSON>roy {\n  @ViewChild(CdkVirtualScrollViewport, { static: false }) virtualScroll?: CdkVirtualScrollViewport;\n  @Input() items: TaggedItem[] | Observable<TaggedItem[]> = [];\n  @Input() selectedItems: string[] = [];\n  @Input() disabled = false;\n  @Input() height = '500px';\n  @Input() checkedOnly = false;\n\n  @Output() changed = new EventEmitter<TaggedItem[]>();\n\n  searchInput = new FormControl('');\n  loading = false;\n  availableItems: TaggedItem[] = [];\n\n  private cachedItems: TaggedItem[] = [];\n  private readonly inputChanged$ = new Subject();\n  private readonly destroyed$ = new Subject();\n\n  constructor( private readonly cd: ChangeDetectorRef ) {\n    this.inputChanged$.pipe(\n      switchMap(() => isObservable(this.items) ? this.items : of(this.items)),\n      filter(items => !!items),\n      tap(() => {\n        this.loading = true;\n      }),\n      map(items => items.map(item => ({\n        ...item,\n        checked: this.selectedItems.indexOf(item.id) !== -1\n      }))),\n      startWith([]),\n      tap(() => {\n        this.loading = false;\n      }),\n      takeUntil(this.destroyed$)\n    ).subscribe(items => {\n      this.cd.markForCheck();\n      this.cachedItems = items;\n      this.setSearchTerm(this.searchInput.value);\n    });\n  }\n\n  ngOnInit() {\n    if (this.searchInput) {\n      this.searchInput.valueChanges.pipe(\n        debounceTime(100),\n        distinctUntilChanged(),\n        takeUntil(this.destroyed$)\n      ).subscribe(search => {\n        this.setSearchTerm(search);\n      });\n    }\n  }\n\n  ngOnChanges( { selectedItems, items }: SimpleChanges ): void {\n    if (items || selectedItems) {\n      this.inputChanged$.next();\n    } else {\n      this.setSearchTerm(this.searchInput.value);\n    }\n  }\n\n  ngAfterViewInit() {\n    if (!this.virtualScroll) {\n      return;\n    }\n\n    const observer = new IntersectionObserver(( entries ) => {\n      entries.forEach(( { isIntersecting } ) => {\n        if (isIntersecting && this.virtualScroll) {\n          this.virtualScroll.checkViewportSize();\n        }\n      });\n    });\n\n    observer.observe(this.virtualScroll.elementRef.nativeElement);\n  }\n\n  ngOnDestroy() {\n    this.destroyed$.next();\n    this.destroyed$.complete();\n  }\n\n  trackByFn = ( _: number, { id }: TaggedItem ) => id;\n\n  get isAllChecked(): boolean {\n    return this.availableItems.every(( { checked } ) => checked);\n  }\n\n  toggleAllChecked() {\n    const allChecked = !this.isAllChecked;\n    this.availableItems.forEach(game => {\n      game.checked = allChecked;\n    });\n    this.cd.markForCheck();\n    this.emitOnChanged();\n  }\n\n  emitOnChanged() {\n    this.changed.emit(this.cachedItems.filter(( { checked } ) => checked).map(( id: TaggedItem ) => id));\n    this.setSearchTerm(this.searchInput.value);\n  }\n\n  getLabelClass( group: string ): string {\n    switch (group) {\n      case 'platform':\n        return 'sw-bg-purple';\n      case 'class':\n        return 'sw-bg-deep-orange';\n      case 'feature':\n        return 'sw-bg-red';\n      default:\n        return 'sw-bg-light-blue';\n    }\n  }\n\n  clearSearch() {\n    this.searchInput.setValue('');\n  }\n\n  private setSearchTerm( search: string ) {\n    const needle = search.toLowerCase();\n    this.availableItems = this.cachedItems.filter(( { id, title, labels, checked } ) => {\n      return (this.checkedOnly ? checked : true)\n        && (title.toLowerCase().indexOf(needle) > -1\n          || id.toLowerCase().indexOf(needle) > -1\n          || labels.map(label => label.title.toLowerCase()).filter(text => text.indexOf(needle) > -1).length > 0);\n    });\n    this.cd.markForCheck();\n  }\n}\n", "<div class=\"table-sticky bordered\" [ngStyle]=\"{height:height}\">\n  <div class=\"table-sticky__header\" fxLayout=\"row\" fxLayoutAlign=\"end center\">\n    <mat-checkbox\n      class=\"table-sticky__select-all\"\n      [checked]=\"isAllChecked\"\n      (change)=\"toggleAllChecked()\"\n      [disabled]=\"disabled || !availableItems.length\">\n      {{ 'COMPONENTS.GAMES_SELECT_MANAGER.selectAll' | translate }}\n    </mat-checkbox>\n    <div class=\"table-sticky__info\">\n      <mat-form-field class=\"table-sticky__search no-field-padding\" appearance=\"outline\">\n        <mat-icon matPrefix class=\"search-icon\">search</mat-icon>\n        <input\n          matInput trimValue\n          type=\"text\"\n          [formControl]=\"searchInput\"\n          [placeholder]=\"'Search by Name or Label'\"\n          [disabled]=\"disabled\">\n        <button mat-button *ngIf=\"searchInput.value\" matSuffix mat-icon-button aria-label=\"Clear\"\n                (click)=\"clearSearch()\">\n          <mat-icon>close</mat-icon>\n        </button>\n      </mat-form-field>\n    </div>\n  </div>\n\n  <div class=\"table-sticky__body\">\n    <cdk-virtual-scroll-viewport\n      class=\"table-sticky__scroll\"\n      [minBufferPx]=\"960\"\n      [maxBufferPx]=\"1008\"\n      [itemSize]=\"48\">\n\n      <div class=\"table-sticky__table\">\n        <div *ngIf=\"loading\">\n          <div class=\"loading-overlay\"><i class=\"icon-spinner4 spinner\"></i></div>\n        </div>\n\n        <div *ngIf=\"(!availableItems || availableItems.length === 0) && !loading\">\n          {{ 'COMPONENTS.GAMES_SELECT_MANAGER.noGamesToShow' | translate }}\n        </div>\n\n        <div class=\"table-sticky__row\"\n             *cdkVirtualFor=\"let game of availableItems; templateCacheSize: 0; trackBy: trackByFn\"\n             [ngClass]=\"{'selected': game.checked}\">\n          <div class=\"table-sticky__checkbox\">\n            <mat-checkbox\n              class=\"games-checkbox\"\n              (change)=\"emitOnChanged()\"\n              [(ngModel)]=\"game.checked\"\n              [disabled]=\"disabled\">\n              <div class=\"table-sticky__checkbox_label\"\n                   [title]=\"game.title\">{{ game.title }}</div>\n            </mat-checkbox>\n          </div>\n          <div class=\"table-sticky__info\">\n            {{ game.id }}\n          </div>\n          <div class=\"table-sticky__chips\">\n            <mat-chip-list>\n              <mat-chip class=\"chip\" *ngFor=\"let label of game.labels\" [ngClass]=\"getLabelClass(label.group)\">\n                {{ label.title | titlecase }}\n              </mat-chip>\n            </mat-chip-list>\n          </div>\n        </div>\n      </div>\n    </cdk-virtual-scroll-viewport>\n  </div>\n</div>\n"], "mappings": "AAAA,SAIEA,YAAY,QAOP,eAAe;AACtB,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,wBAAwB,QAAQ,wBAAwB;AACjE,SAASC,YAAY,EAAcC,EAAE,EAAEC,OAAO,QAAQ,MAAM;AAC5D,SAASC,YAAY,EAAEC,oBAAoB,EAAEC,MAAM,EAAEC,GAAG,EAAEC,SAAS,EAAEC,SAAS,EAAEC,SAAS,EAAEC,GAAG,QAAQ,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;ICG9GC,EAAA,CAAAC,cAAA,iBACgC;IAAxBD,EAAA,CAAAE,UAAA,mBAAAC,gEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAC7BT,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAU,MAAA,YAAK;IACjBV,EADiB,CAAAW,YAAA,EAAW,EACnB;;;;;IAcPX,EADF,CAAAC,cAAA,UAAqB,cACU;IAAAD,EAAA,CAAAY,SAAA,YAAqC;IACpEZ,EADoE,CAAAW,YAAA,EAAM,EACpE;;;;;IAENX,EAAA,CAAAC,cAAA,UAA0E;IACxED,EAAA,CAAAU,MAAA,GACF;;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;IADJX,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAc,kBAAA,MAAAd,EAAA,CAAAe,WAAA,6DACF;;;;;IAoBMf,EAAA,CAAAC,cAAA,mBAAgG;IAC9FD,EAAA,CAAAU,MAAA,GACF;;IAAAV,EAAA,CAAAW,YAAA,EAAW;;;;;IAF8CX,EAAA,CAAAgB,UAAA,YAAAV,MAAA,CAAAW,aAAA,CAAAC,QAAA,CAAAC,KAAA,EAAsC;IAC7FnB,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAc,kBAAA,MAAAd,EAAA,CAAAe,WAAA,OAAAG,QAAA,CAAAE,KAAA,OACF;;;;;;IAhBFpB,EAJJ,CAAAC,cAAA,cAE4C,cACN,uBAKV;IAFtBD,EAAA,CAAAE,UAAA,oBAAAmB,oEAAA;MAAArB,EAAA,CAAAI,aAAA,CAAAkB,GAAA;MAAA,MAAAhB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAUF,MAAA,CAAAiB,aAAA,EAAe;IAAA,EAAC;IAC1BvB,EAAA,CAAAwB,gBAAA,2BAAAC,2EAAAC,MAAA;MAAA,MAAAC,OAAA,GAAA3B,EAAA,CAAAI,aAAA,CAAAkB,GAAA,EAAAM,SAAA;MAAA5B,EAAA,CAAA6B,kBAAA,CAAAF,OAAA,CAAAG,OAAA,EAAAJ,MAAA,MAAAC,OAAA,CAAAG,OAAA,GAAAJ,MAAA;MAAA,OAAA1B,EAAA,CAAAQ,WAAA,CAAAkB,MAAA;IAAA,EAA0B;IAE1B1B,EAAA,CAAAC,cAAA,cAC0B;IAAAD,EAAA,CAAAU,MAAA,GAAgB;IAE9CV,EAF8C,CAAAW,YAAA,EAAM,EACnC,EACX;IACNX,EAAA,CAAAC,cAAA,aAAgC;IAC9BD,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAM;IAEJX,EADF,CAAAC,cAAA,cAAiC,oBAChB;IACbD,EAAA,CAAA+B,UAAA,IAAAC,+CAAA,uBAAgG;IAKtGhC,EAFI,CAAAW,YAAA,EAAgB,EACZ,EACF;;;;;IArBDX,EAAA,CAAAgB,UAAA,YAAAhB,EAAA,CAAAiC,eAAA,IAAAC,GAAA,EAAAP,OAAA,CAAAG,OAAA,EAAsC;IAKrC9B,EAAA,CAAAa,SAAA,GAA0B;IAA1Bb,EAAA,CAAAmC,gBAAA,YAAAR,OAAA,CAAAG,OAAA,CAA0B;IAC1B9B,EAAA,CAAAgB,UAAA,aAAAV,MAAA,CAAA8B,QAAA,CAAqB;IAEhBpC,EAAA,CAAAa,SAAA,EAAoB;IAApBb,EAAA,CAAAgB,UAAA,UAAAW,OAAA,CAAAP,KAAA,CAAoB;IAACpB,EAAA,CAAAa,SAAA,EAAgB;IAAhBb,EAAA,CAAAqC,iBAAA,CAAAV,OAAA,CAAAP,KAAA,CAAgB;IAI5CpB,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAAa,OAAA,CAAAW,EAAA,MACF;IAG6CtC,EAAA,CAAAa,SAAA,GAAc;IAAdb,EAAA,CAAAgB,UAAA,YAAAW,OAAA,CAAAY,MAAA,CAAc;;;ADxBrE,OAAM,MAAOC,oBAAoB;EAkB/BC,YAA8BC,EAAqB;IAArB,KAAAA,EAAE,GAAFA,EAAE;IAhBvB,KAAAC,KAAK,GAA4C,EAAE;IACnD,KAAAC,aAAa,GAAa,EAAE;IAC5B,KAAAR,QAAQ,GAAG,KAAK;IAChB,KAAAS,MAAM,GAAG,OAAO;IAChB,KAAAC,WAAW,GAAG,KAAK;IAElB,KAAAC,OAAO,GAAG,IAAI7D,YAAY,EAAgB;IAEpD,KAAA8D,WAAW,GAAG,IAAI7D,WAAW,CAAC,EAAE,CAAC;IACjC,KAAA8D,OAAO,GAAG,KAAK;IACf,KAAAC,cAAc,GAAiB,EAAE;IAEzB,KAAAC,WAAW,GAAiB,EAAE;IACrB,KAAAC,aAAa,GAAG,IAAI7D,OAAO,EAAE;IAC7B,KAAA8D,UAAU,GAAG,IAAI9D,OAAO,EAAE;IAkE3C,KAAA+D,SAAS,GAAG,CAAEC,CAAS,EAAE;MAAEjB;IAAE,CAAc,KAAMA,EAAE;IA/DjD,IAAI,CAACc,aAAa,CAACI,IAAI,CACrB3D,SAAS,CAAC,MAAMR,YAAY,CAAC,IAAI,CAACsD,KAAK,CAAC,GAAG,IAAI,CAACA,KAAK,GAAGrD,EAAE,CAAC,IAAI,CAACqD,KAAK,CAAC,CAAC,EACvEjD,MAAM,CAACiD,KAAK,IAAI,CAAC,CAACA,KAAK,CAAC,EACxB5C,GAAG,CAAC,MAAK;MACP,IAAI,CAACkD,OAAO,GAAG,IAAI;IACrB,CAAC,CAAC,EACFtD,GAAG,CAACgD,KAAK,IAAIA,KAAK,CAAChD,GAAG,CAAC8D,IAAI,KAAK;MAC9B,GAAGA,IAAI;MACP3B,OAAO,EAAE,IAAI,CAACc,aAAa,CAACc,OAAO,CAACD,IAAI,CAACnB,EAAE,CAAC,KAAK,CAAC;KACnD,CAAC,CAAC,CAAC,EACJ1C,SAAS,CAAC,EAAE,CAAC,EACbG,GAAG,CAAC,MAAK;MACP,IAAI,CAACkD,OAAO,GAAG,KAAK;IACtB,CAAC,CAAC,EACFnD,SAAS,CAAC,IAAI,CAACuD,UAAU,CAAC,CAC3B,CAACM,SAAS,CAAChB,KAAK,IAAG;MAClB,IAAI,CAACD,EAAE,CAACkB,YAAY,EAAE;MACtB,IAAI,CAACT,WAAW,GAAGR,KAAK;MACxB,IAAI,CAACkB,aAAa,CAAC,IAAI,CAACb,WAAW,CAACc,KAAK,CAAC;IAC5C,CAAC,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACf,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAACgB,YAAY,CAACR,IAAI,CAChChE,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBK,SAAS,CAAC,IAAI,CAACuD,UAAU,CAAC,CAC3B,CAACM,SAAS,CAACM,MAAM,IAAG;QACnB,IAAI,CAACJ,aAAa,CAACI,MAAM,CAAC;MAC5B,CAAC,CAAC;IACJ;EACF;EAEAC,WAAWA,CAAE;IAAEtB,aAAa;IAAED;EAAK,CAAiB;IAClD,IAAIA,KAAK,IAAIC,aAAa,EAAE;MAC1B,IAAI,CAACQ,aAAa,CAACe,IAAI,EAAE;IAC3B,CAAC,MAAM;MACL,IAAI,CAACN,aAAa,CAAC,IAAI,CAACb,WAAW,CAACc,KAAK,CAAC;IAC5C;EACF;EAEAM,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAACC,aAAa,EAAE;MACvB;IACF;IAEA,MAAMC,QAAQ,GAAG,IAAIC,oBAAoB,CAAGC,OAAO,IAAK;MACtDA,OAAO,CAACC,OAAO,CAAC,CAAE;QAAEC;MAAc,CAAE,KAAK;QACvC,IAAIA,cAAc,IAAI,IAAI,CAACL,aAAa,EAAE;UACxC,IAAI,CAACA,aAAa,CAACM,iBAAiB,EAAE;QACxC;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFL,QAAQ,CAACM,OAAO,CAAC,IAAI,CAACP,aAAa,CAACQ,UAAU,CAACC,aAAa,CAAC;EAC/D;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAC1B,UAAU,CAACc,IAAI,EAAE;IACtB,IAAI,CAACd,UAAU,CAAC2B,QAAQ,EAAE;EAC5B;EAIA,IAAIC,YAAYA,CAAA;IACd,OAAO,IAAI,CAAC/B,cAAc,CAACgC,KAAK,CAAC,CAAE;MAAEpD;IAAO,CAAE,KAAMA,OAAO,CAAC;EAC9D;EAEAqD,gBAAgBA,CAAA;IACd,MAAMC,UAAU,GAAG,CAAC,IAAI,CAACH,YAAY;IACrC,IAAI,CAAC/B,cAAc,CAACuB,OAAO,CAACY,IAAI,IAAG;MACjCA,IAAI,CAACvD,OAAO,GAAGsD,UAAU;IAC3B,CAAC,CAAC;IACF,IAAI,CAAC1C,EAAE,CAACkB,YAAY,EAAE;IACtB,IAAI,CAACrC,aAAa,EAAE;EACtB;EAEAA,aAAaA,CAAA;IACX,IAAI,CAACwB,OAAO,CAACuC,IAAI,CAAC,IAAI,CAACnC,WAAW,CAACzD,MAAM,CAAC,CAAE;MAAEoC;IAAO,CAAE,KAAMA,OAAO,CAAC,CAACnC,GAAG,CAAG2C,EAAc,IAAMA,EAAE,CAAC,CAAC;IACpG,IAAI,CAACuB,aAAa,CAAC,IAAI,CAACb,WAAW,CAACc,KAAK,CAAC;EAC5C;EAEA7C,aAAaA,CAAEE,KAAa;IAC1B,QAAQA,KAAK;MACX,KAAK,UAAU;QACb,OAAO,cAAc;MACvB,KAAK,OAAO;QACV,OAAO,mBAAmB;MAC5B,KAAK,SAAS;QACZ,OAAO,WAAW;MACpB;QACE,OAAO,kBAAkB;IAC7B;EACF;EAEAV,WAAWA,CAAA;IACT,IAAI,CAACuC,WAAW,CAACuC,QAAQ,CAAC,EAAE,CAAC;EAC/B;EAEQ1B,aAAaA,CAAEI,MAAc;IACnC,MAAMuB,MAAM,GAAGvB,MAAM,CAACwB,WAAW,EAAE;IACnC,IAAI,CAACvC,cAAc,GAAG,IAAI,CAACC,WAAW,CAACzD,MAAM,CAAC,CAAE;MAAE4C,EAAE;MAAElB,KAAK;MAAEmB,MAAM;MAAET;IAAO,CAAE,KAAK;MACjF,OAAO,CAAC,IAAI,CAACgB,WAAW,GAAGhB,OAAO,GAAG,IAAI,MACnCV,KAAK,CAACqE,WAAW,EAAE,CAAC/B,OAAO,CAAC8B,MAAM,CAAC,GAAG,CAAC,CAAC,IACvClD,EAAE,CAACmD,WAAW,EAAE,CAAC/B,OAAO,CAAC8B,MAAM,CAAC,GAAG,CAAC,CAAC,IACrCjD,MAAM,CAAC5C,GAAG,CAAC+F,KAAK,IAAIA,KAAK,CAACtE,KAAK,CAACqE,WAAW,EAAE,CAAC,CAAC/F,MAAM,CAACiG,IAAI,IAAIA,IAAI,CAACjC,OAAO,CAAC8B,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAACI,MAAM,GAAG,CAAC,CAAC;IAC7G,CAAC,CAAC;IACF,IAAI,CAAClD,EAAE,CAACkB,YAAY,EAAE;EACxB;;;uCAhIWpB,oBAAoB,EAAAxC,EAAA,CAAA6F,iBAAA,CAAA7F,EAAA,CAAA8F,iBAAA;IAAA;EAAA;;;YAApBtD,oBAAoB;MAAAuD,SAAA;MAAAC,SAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;yBACpB9G,wBAAwB;;;;;;;;;;;;;;;;;;;;;;;UCnCjCY,EAFJ,CAAAC,cAAA,aAA+D,aACe,sBAKxB;UADhDD,EAAA,CAAAE,UAAA,oBAAAkG,6DAAA;YAAA,OAAUD,GAAA,CAAAhB,gBAAA,EAAkB;UAAA,EAAC;UAE7BnF,EAAA,CAAAU,MAAA,GACF;;UAAAV,EAAA,CAAAW,YAAA,EAAe;UAGXX,EAFJ,CAAAC,cAAA,aAAgC,wBACqD,kBACzC;UAAAD,EAAA,CAAAU,MAAA,aAAM;UAAAV,EAAA,CAAAW,YAAA,EAAW;UACzDX,EAAA,CAAAY,SAAA,eAKwB;UACxBZ,EAAA,CAAA+B,UAAA,KAAAsE,uCAAA,oBACgC;UAKtCrG,EAFI,CAAAW,YAAA,EAAiB,EACb,EACF;UASFX,EAPJ,CAAAC,cAAA,cAAgC,sCAKZ,eAEiB;UAS/BD,EARA,CAAA+B,UAAA,KAAAuE,oCAAA,kBAAqB,KAAAC,oCAAA,kBAIqD,KAAAC,oCAAA,mBAM9B;UAyBpDxG,EAHM,CAAAW,YAAA,EAAM,EACsB,EAC1B,EACF;;;UArE6BX,EAAA,CAAAgB,UAAA,YAAAhB,EAAA,CAAAiC,eAAA,KAAAwE,GAAA,EAAAN,GAAA,CAAAtD,MAAA,EAA2B;UAIxD7C,EAAA,CAAAa,SAAA,GAAwB;UAExBb,EAFA,CAAAgB,UAAA,YAAAmF,GAAA,CAAAlB,YAAA,CAAwB,aAAAkB,GAAA,CAAA/D,QAAA,KAAA+D,GAAA,CAAAjD,cAAA,CAAA0C,MAAA,CAEuB;UAC/C5F,EAAA,CAAAa,SAAA,EACF;UADEb,EAAA,CAAAc,kBAAA,MAAAd,EAAA,CAAAe,WAAA,0DACF;UAOMf,EAAA,CAAAa,SAAA,GAA2B;UAE3Bb,EAFA,CAAAgB,UAAA,gBAAAmF,GAAA,CAAAnD,WAAA,CAA2B,0CACc,aAAAmD,GAAA,CAAA/D,QAAA,CACpB;UACHpC,EAAA,CAAAa,SAAA,EAAuB;UAAvBb,EAAA,CAAAgB,UAAA,SAAAmF,GAAA,CAAAnD,WAAA,CAAAc,KAAA,CAAuB;UAW7C9D,EAAA,CAAAa,SAAA,GAAmB;UAEnBb,EAFA,CAAAgB,UAAA,oBAAmB,qBACC,gBACL;UAGPhB,EAAA,CAAAa,SAAA,GAAa;UAAbb,EAAA,CAAAgB,UAAA,SAAAmF,GAAA,CAAAlD,OAAA,CAAa;UAIbjD,EAAA,CAAAa,SAAA,EAAkE;UAAlEb,EAAA,CAAAgB,UAAA,WAAAmF,GAAA,CAAAjD,cAAA,IAAAiD,GAAA,CAAAjD,cAAA,CAAA0C,MAAA,YAAAO,GAAA,CAAAlD,OAAA,CAAkE;UAK1CjD,EAAA,CAAAa,SAAA,EAAmB;UAAsBb,EAAzC,CAAAgB,UAAA,oBAAAmF,GAAA,CAAAjD,cAAA,CAAmB,qCAAsB,yBAAAiD,GAAA,CAAA7C,SAAA,CAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}