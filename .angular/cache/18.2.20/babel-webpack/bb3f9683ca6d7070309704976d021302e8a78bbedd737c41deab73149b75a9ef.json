{"ast": null, "code": "import { FormControl, FormGroup, Validators } from '@angular/forms';\nimport { ValidationService } from '../../../../../../../../common/services/validation.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"@angular/material/input\";\nimport * as i4 from \"@angular/material/form-field\";\nimport * as i5 from \"@angular/material/select\";\nimport * as i6 from \"@angular/material/core\";\nimport * as i7 from \"@skywind-group/lib-swui\";\nfunction PlacementPositionComponent_mat_form_field_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-form-field\", 5)(1, \"mat-label\");\n    i0.ɵɵtext(2, \"Position\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"mat-select\", 6);\n    i0.ɵɵlistener(\"selectionChange\", function PlacementPositionComponent_mat_form_field_1_Template_mat_select_selectionChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.fixedChanged($event));\n    });\n    i0.ɵɵelementStart(4, \"mat-option\", 7);\n    i0.ɵɵtext(5, \"left\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-option\", 8);\n    i0.ɵɵtext(7, \"right\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"mat-option\", 9);\n    i0.ɵɵtext(9, \"center\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"mat-option\", 10);\n    i0.ɵɵtext(11, \"fixed\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"formControl\", ctx_r1.fixedControl);\n  }\n}\nfunction PlacementPositionComponent_div_3_mat_form_field_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-form-field\", 5)(1, \"mat-label\");\n    i0.ɵɵtext(2, \"X\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 11);\n    i0.ɵɵelementStart(4, \"mat-error\");\n    i0.ɵɵelement(5, \"lib-swui-control-messages\", 12);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"formControl\", ctx_r1.xControl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"messages\", ctx_r1.messageErrors)(\"control\", ctx_r1.xControl);\n  }\n}\nfunction PlacementPositionComponent_div_3_mat_form_field_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-form-field\", 5)(1, \"mat-label\");\n    i0.ɵɵtext(2, \"Y\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 14);\n    i0.ɵɵelementStart(4, \"mat-error\");\n    i0.ɵɵelement(5, \"lib-swui-control-messages\", 12);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"formControl\", ctx_r1.yControl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"messages\", ctx_r1.messageErrors)(\"control\", ctx_r1.yControl);\n  }\n}\nfunction PlacementPositionComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"mat-form-field\", 5)(2, \"mat-label\");\n    i0.ɵɵtext(3, \"Width\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 11);\n    i0.ɵɵelementStart(5, \"mat-error\");\n    i0.ɵɵelement(6, \"lib-swui-control-messages\", 12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"mat-form-field\", 5)(8, \"mat-label\");\n    i0.ɵɵtext(9, \"Height\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(10, \"input\", 13);\n    i0.ɵɵelementStart(11, \"mat-error\");\n    i0.ɵɵelement(12, \"lib-swui-control-messages\", 12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(13, PlacementPositionComponent_div_3_mat_form_field_13_Template, 6, 3, \"mat-form-field\", 1)(14, PlacementPositionComponent_div_3_mat_form_field_14_Template, 6, 3, \"mat-form-field\", 1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"formControl\", ctx_r1.widthControl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"messages\", ctx_r1.messageErrors)(\"control\", ctx_r1.widthControl);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"formControl\", ctx_r1.heightControl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"messages\", ctx_r1.messageErrors)(\"control\", ctx_r1.heightControl);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.fixedControl == null ? null : ctx_r1.fixedControl.value) == \"fixed\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.fixedControl == null ? null : ctx_r1.fixedControl.value);\n  }\n}\nfunction PlacementPositionComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"mat-form-field\", 5)(2, \"mat-label\");\n    i0.ɵɵtext(3, \"Width\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 11);\n    i0.ɵɵelementStart(5, \"mat-error\");\n    i0.ɵɵelement(6, \"lib-swui-control-messages\", 12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"mat-form-field\", 5)(8, \"mat-label\");\n    i0.ɵɵtext(9, \"Height\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(10, \"input\", 13);\n    i0.ɵɵelementStart(11, \"mat-error\");\n    i0.ɵɵelement(12, \"lib-swui-control-messages\", 12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"mat-form-field\", 5)(14, \"mat-label\");\n    i0.ɵɵtext(15, \"Y\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(16, \"input\", 14);\n    i0.ɵɵelementStart(17, \"mat-error\");\n    i0.ɵɵelement(18, \"lib-swui-control-messages\", 12);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"formControl\", ctx_r1.widthControl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"messages\", ctx_r1.messageErrors)(\"control\", ctx_r1.widthControl);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"formControl\", ctx_r1.heightControl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"messages\", ctx_r1.messageErrors)(\"control\", ctx_r1.heightControl);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"formControl\", ctx_r1.yControl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"messages\", ctx_r1.messageErrors)(\"control\", ctx_r1.yControl);\n  }\n}\nfunction PlacementPositionComponent_div_5_mat_form_field_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-form-field\", 5)(1, \"mat-label\");\n    i0.ɵɵtext(2, \"Width\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 11);\n    i0.ɵɵelementStart(4, \"mat-error\");\n    i0.ɵɵelement(5, \"lib-swui-control-messages\", 12);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"formControl\", ctx_r1.widthControl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"messages\", ctx_r1.messageErrors)(\"control\", ctx_r1.widthControl);\n  }\n}\nfunction PlacementPositionComponent_div_5_mat_form_field_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-form-field\", 5)(1, \"mat-label\");\n    i0.ɵɵtext(2, \"Height\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 13);\n    i0.ɵɵelementStart(4, \"mat-error\");\n    i0.ɵɵelement(5, \"lib-swui-control-messages\", 12);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"formControl\", ctx_r1.heightControl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"messages\", ctx_r1.messageErrors)(\"control\", ctx_r1.heightControl);\n  }\n}\nfunction PlacementPositionComponent_div_5_mat_form_field_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-form-field\", 5)(1, \"mat-label\");\n    i0.ɵɵtext(2, \"X\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 11);\n    i0.ɵɵelementStart(4, \"mat-error\");\n    i0.ɵɵelement(5, \"lib-swui-control-messages\", 12);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"formControl\", ctx_r1.xControl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"messages\", ctx_r1.messageErrors)(\"control\", ctx_r1.xControl);\n  }\n}\nfunction PlacementPositionComponent_div_5_mat_form_field_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-form-field\", 5)(1, \"mat-label\");\n    i0.ɵɵtext(2, \"Y\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 14);\n    i0.ɵɵelementStart(4, \"mat-error\");\n    i0.ɵɵelement(5, \"lib-swui-control-messages\", 12);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"formControl\", ctx_r1.yControl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"messages\", ctx_r1.messageErrors)(\"control\", ctx_r1.yControl);\n  }\n}\nfunction PlacementPositionComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, PlacementPositionComponent_div_5_mat_form_field_1_Template, 6, 3, \"mat-form-field\", 1)(2, PlacementPositionComponent_div_5_mat_form_field_2_Template, 6, 3, \"mat-form-field\", 1)(3, PlacementPositionComponent_div_5_mat_form_field_3_Template, 6, 3, \"mat-form-field\", 1)(4, PlacementPositionComponent_div_5_mat_form_field_4_Template, 6, 3, \"mat-form-field\", 1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.fixedControl == null ? null : ctx_r1.fixedControl.value);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.fixedControl == null ? null : ctx_r1.fixedControl.value);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.fixedControl == null ? null : ctx_r1.fixedControl.value) == \"fixed\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.fixedControl == null ? null : ctx_r1.fixedControl.value);\n  }\n}\nexport function buildPositionForm(gridType, options) {\n  const group = new FormGroup({\n    fixed: new FormControl(options?.fixed),\n    x: new FormControl(options?.x, {\n      updateOn: 'blur'\n    }),\n    y: new FormControl(options?.y, {\n      validators: Validators.compose([Validators.required, Validators.min(1), Validators.max(500), ValidationService.digitsOnlyValidator]),\n      updateOn: 'blur'\n    }),\n    width: new FormControl(gridType === 'mobile' || gridType === 'portrait' ? 1 : options?.width, {\n      validators: Validators.compose([Validators.required, Validators.min(1), Validators.max(7), ValidationService.digitsOnlyValidator]),\n      updateOn: 'blur'\n    }),\n    height: new FormControl(gridType === 'mobile' || gridType === 'portrait' ? 1 : options?.height, {\n      validators: Validators.compose([Validators.required, Validators.min(1), ValidationService.digitsOnlyValidator]),\n      updateOn: 'blur'\n    })\n  });\n  const xControl = group.get('x');\n  const fixedControl = group.get('fixed');\n  if (fixedControl.value === 'fixed') {\n    xControl.setValidators(Validators.compose([Validators.required, Validators.min(1), Validators.max(7), ValidationService.digitsOnlyValidator]));\n  } else {\n    xControl.clearValidators();\n    xControl.setValue(null);\n  }\n  return group;\n}\nexport class PlacementPositionComponent {\n  constructor() {\n    this.messageErrors = {\n      max: `VALIDATION.max`,\n      min: `VALIDATION.min`,\n      required: 'VALIDATION.required',\n      invalidDigitsOnly: 'VALIDATION.invalidDigitsOnly'\n    };\n  }\n  ngOnChanges() {\n    if (this.gridType === undefined) {\n      this.fixedControl?.disable({\n        emitEvent: false\n      });\n      this.xControl?.disable({\n        emitEvent: false\n      });\n      this.yControl?.disable({\n        emitEvent: false\n      });\n      this.widthControl?.disable({\n        emitEvent: false\n      });\n      this.heightControl?.disable({\n        emitEvent: false\n      });\n    } else if (this.gridType === 'portrait') {\n      this.fixedControl?.disable({\n        emitEvent: false\n      });\n      this.xControl?.disable({\n        emitEvent: false\n      });\n      this.widthControl?.disable({\n        emitEvent: false\n      });\n      this.heightControl?.disable({\n        emitEvent: false\n      });\n    } else if (this.gridType === 'mobile') {\n      this.widthControl?.disable({\n        emitEvent: false\n      });\n      this.heightControl?.disable({\n        emitEvent: false\n      });\n    }\n  }\n  fixedChanged({\n    value\n  }) {\n    if (value === 'fixed') {\n      this.xControl.setValidators(Validators.compose([Validators.required, Validators.min(1), Validators.max(7), ValidationService.digitsOnlyValidator]));\n    } else {\n      this.xControl.clearValidators();\n      this.xControl.setValue(null, {\n        emitEvent: null\n      });\n    }\n    this.xControl.updateValueAndValidity();\n  }\n  get form() {\n    return this.control;\n  }\n  get fixedControl() {\n    return this.form?.get('fixed');\n  }\n  get xControl() {\n    return this.form?.get('x');\n  }\n  get yControl() {\n    return this.form?.get('y');\n  }\n  get widthControl() {\n    return this.form?.get('width');\n  }\n  get heightControl() {\n    return this.form?.get('height');\n  }\n  static {\n    this.ɵfac = function PlacementPositionComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PlacementPositionComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PlacementPositionComponent,\n      selectors: [[\"placement-position\"]],\n      inputs: {\n        gridType: \"gridType\",\n        control: \"control\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature],\n      decls: 6,\n      vars: 5,\n      consts: [[1, \"widget-positions\", 3, \"formGroup\"], [\"appearance\", \"outline\", 4, \"ngIf\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [4, \"ngSwitchDefault\"], [\"appearance\", \"outline\"], [\"required\", \"\", 3, \"selectionChange\", \"formControl\"], [\"value\", \"left\"], [\"value\", \"right\"], [\"value\", \"center\"], [\"value\", \"fixed\"], [\"matInput\", \"\", \"type\", \"number\", \"min\", \"1\", \"max\", \"7\", 3, \"formControl\"], [3, \"messages\", \"control\"], [\"matInput\", \"\", \"type\", \"number\", \"min\", \"1\", 3, \"formControl\"], [\"matInput\", \"\", \"type\", \"number\", \"min\", \"1\", \"max\", \"500\", 3, \"formControl\"]],\n      template: function PlacementPositionComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, PlacementPositionComponent_mat_form_field_1_Template, 12, 1, \"mat-form-field\", 1);\n          i0.ɵɵelementContainerStart(2, 2);\n          i0.ɵɵtemplate(3, PlacementPositionComponent_div_3_Template, 15, 8, \"div\", 3)(4, PlacementPositionComponent_div_4_Template, 19, 9, \"div\", 3)(5, PlacementPositionComponent_div_5_Template, 5, 4, \"div\", 4);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"formGroup\", ctx.form);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.gridType != \"portrait\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngSwitch\", ctx.gridType);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngSwitchCase\", \"mobile\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngSwitchCase\", \"portrait\");\n        }\n      },\n      dependencies: [i1.NgIf, i1.NgSwitch, i1.NgSwitchCase, i1.NgSwitchDefault, i2.DefaultValueAccessor, i2.NumberValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.RequiredValidator, i2.MinValidator, i2.MaxValidator, i2.FormControlDirective, i2.FormGroupDirective, i3.MatInput, i4.MatFormField, i4.MatLabel, i4.MatError, i5.MatSelect, i6.MatOption, i7.SwuiControlMessagesComponent],\n      styles: [\".widget-positions[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%] {\\n  display: block;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvbG9iYnkvbG9iYnktZm9ybS9sb2JieS1tZW51LWl0ZW1zL2xvYmJ5LW1lbnUtaXRlbXMtc2V0dXAvbG9iYnktbWVudS1pdGVtcy13aWRnZXRzL3BsYWNlbWVudC9wbGFjZW1lbnQtcG9zaXRpb24vcGxhY2VtZW50LXBvc2l0aW9uLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUNFO0VBQ0UsY0FBQTtBQUFKIiwic291cmNlc0NvbnRlbnQiOlsiLndpZGdldC1wb3NpdGlvbnMge1xuICAubWF0LWZvcm0tZmllbGQge1xuICAgIGRpc3BsYXk6IGJsb2NrO1xuICB9XG59XG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["FormControl", "FormGroup", "Validators", "ValidationService", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "PlacementPositionComponent_mat_form_field_1_Template_mat_select_selectionChange_3_listener", "$event", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "fixedChanged", "ɵɵadvance", "ɵɵproperty", "fixedControl", "ɵɵelement", "xControl", "messageErrors", "yControl", "ɵɵtemplate", "PlacementPositionComponent_div_3_mat_form_field_13_Template", "PlacementPositionComponent_div_3_mat_form_field_14_Template", "widthControl", "heightControl", "value", "PlacementPositionComponent_div_5_mat_form_field_1_Template", "PlacementPositionComponent_div_5_mat_form_field_2_Template", "PlacementPositionComponent_div_5_mat_form_field_3_Template", "PlacementPositionComponent_div_5_mat_form_field_4_Template", "buildPositionForm", "gridType", "options", "group", "fixed", "x", "updateOn", "y", "validators", "compose", "required", "min", "max", "digitsOnlyValidator", "width", "height", "get", "setValidators", "clearValidators", "setValue", "PlacementPositionComponent", "constructor", "invalidDigitsOnly", "ngOnChanges", "undefined", "disable", "emitEvent", "updateValueAndValidity", "form", "control", "selectors", "inputs", "features", "ɵɵNgOnChangesFeature", "decls", "vars", "consts", "template", "PlacementPositionComponent_Template", "rf", "ctx", "PlacementPositionComponent_mat_form_field_1_Template", "ɵɵelementContainerStart", "PlacementPositionComponent_div_3_Template", "PlacementPositionComponent_div_4_Template", "PlacementPositionComponent_div_5_Template"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/lobby/lobby-form/lobby-menu-items/lobby-menu-items-setup/lobby-menu-items-widgets/placement/placement-position/placement-position.component.ts", "/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/lobby/lobby-form/lobby-menu-items/lobby-menu-items-setup/lobby-menu-items-widgets/placement/placement-position/placement-position.component.html"], "sourcesContent": ["import { Component, Input, OnChanges } from '@angular/core';\nimport { AbstractControl, FormControl, FormGroup, Validators } from '@angular/forms';\nimport { ErrorMessage } from '../../../../../../../../common/components/mat-user-editor/user-form.component';\nimport { ValidationService } from '../../../../../../../../common/services/validation.service';\nimport { LobbyMenuItemWidgetGridOptionsPositions } from '../../../../../../lobby.model';\nimport { MatSelectChange } from '@angular/material/select';\n\n\ntype GridType = 'desktop' | 'mobile' | 'portrait';\n\nexport function buildPositionForm(gridType: GridType, options?: LobbyMenuItemWidgetGridOptionsPositions ): FormGroup {\n  const group = new FormGroup({\n    fixed: new FormControl(options?.fixed),\n    x: new FormControl(options?.x, { updateOn: 'blur' }),\n    y: new FormControl(options?.y, { validators: Validators.compose([\n        Validators.required,\n        Validators.min(1),\n        Validators.max(500),\n        ValidationService.digitsOnlyValidator\n      ]), updateOn: 'blur'}),\n    width: new FormControl(gridType === 'mobile' || gridType === 'portrait' ? 1 : options?.width, { validators: Validators.compose([\n      Validators.required,\n      Validators.min(1),\n      Validators.max(7),\n      ValidationService.digitsOnlyValidator\n    ]), updateOn: 'blur' }),\n    height: new FormControl(gridType === 'mobile' || gridType === 'portrait' ? 1 : options?.height, { validators: Validators.compose([\n      Validators.required,\n      Validators.min(1),\n      ValidationService.digitsOnlyValidator\n    ]), updateOn: 'blur'}),\n  });\n\n  const xControl = group.get('x') as FormControl;\n  const fixedControl = group.get('fixed') as FormControl;\n\n  if (fixedControl.value === 'fixed') {\n    xControl.setValidators(Validators.compose([\n      Validators.required,\n      Validators.min(1),\n      Validators.max(7),\n      ValidationService.digitsOnlyValidator\n    ]));\n  } else {\n    xControl.clearValidators();\n    xControl.setValue(null);\n  }\n\n  return group;\n}\n\n@Component({\n  selector: 'placement-position',\n  templateUrl: './placement-position.component.html',\n  styleUrls: ['./placement-position.component.scss'],\n})\nexport class PlacementPositionComponent implements OnChanges {\n  @Input() gridType?: GridType;\n  @Input() control?: AbstractControl | null;\n\n  messageErrors: ErrorMessage = {\n    max: `VALIDATION.max`,\n    min: `VALIDATION.min`,\n    required: 'VALIDATION.required',\n    invalidDigitsOnly: 'VALIDATION.invalidDigitsOnly'\n  };\n\n  ngOnChanges(): void {\n    if (this.gridType === undefined) {\n      this.fixedControl?.disable({ emitEvent: false });\n      this.xControl?.disable({ emitEvent: false });\n      this.yControl?.disable({ emitEvent: false });\n      this.widthControl?.disable({ emitEvent: false });\n      this.heightControl?.disable({ emitEvent: false });\n    } else if (this.gridType === 'portrait') {\n      this.fixedControl?.disable({ emitEvent: false });\n      this.xControl?.disable({ emitEvent: false });\n      this.widthControl?.disable({ emitEvent: false });\n      this.heightControl?.disable({ emitEvent: false });\n    } else if (this.gridType === 'mobile') {\n      this.widthControl?.disable({ emitEvent: false });\n      this.heightControl?.disable({ emitEvent: false });\n    }\n  }\n\n  fixedChanged( { value }: MatSelectChange ) {\n    if (value === 'fixed') {\n      this.xControl.setValidators(Validators.compose([\n        Validators.required,\n        Validators.min(1),\n        Validators.max(7),\n        ValidationService.digitsOnlyValidator\n      ]));\n    } else {\n      this.xControl.clearValidators();\n      this.xControl.setValue(null, { emitEvent: null });\n    }\n    this.xControl.updateValueAndValidity();\n  }\n\n  get form(): FormGroup | null | undefined {\n    return this.control as FormGroup;\n  }\n\n  get fixedControl(): FormControl | undefined {\n    return this.form?.get('fixed') as FormControl;\n  }\n\n  get xControl(): FormControl | undefined {\n    return this.form?.get('x') as FormControl;\n  }\n\n  get yControl(): FormControl | undefined {\n    return this.form?.get('y') as FormControl;\n  }\n\n  get widthControl(): FormControl | undefined {\n    return this.form?.get('width') as FormControl;\n  }\n\n  get heightControl(): FormControl | undefined {\n    return this.form?.get('height') as FormControl;\n  }\n}\n", "<div [formGroup]=\"form\" class=\"widget-positions\">\n\n  <mat-form-field appearance=\"outline\" *ngIf=\"gridType!='portrait'\">\n    <mat-label>Position</mat-label>\n    <mat-select [formControl]=\"fixedControl\" required (selectionChange)=\"fixedChanged($event)\">\n      <mat-option value=\"left\">left</mat-option>\n      <mat-option value=\"right\">right</mat-option>\n      <mat-option value=\"center\">center</mat-option>\n      <mat-option value=\"fixed\">fixed</mat-option>\n    </mat-select>\n  </mat-form-field>\n\n  <ng-container [ngSwitch]=\"gridType\">\n    <div *ngSwitchCase=\"'mobile'\">\n      <mat-form-field appearance=\"outline\">\n        <mat-label>Width</mat-label>\n        <input matInput type=\"number\" [formControl]=\"widthControl\" min=\"1\" max=\"7\">\n        <mat-error>\n          <lib-swui-control-messages [messages]=\"messageErrors\" [control]=\"widthControl\"></lib-swui-control-messages>\n        </mat-error>\n      </mat-form-field>\n      <mat-form-field appearance=\"outline\">\n        <mat-label>Height</mat-label>\n        <input matInput type=\"number\" [formControl]=\"heightControl\" min=\"1\">\n        <mat-error>\n          <lib-swui-control-messages [messages]=\"messageErrors\" [control]=\"heightControl\"></lib-swui-control-messages>\n        </mat-error>\n      </mat-form-field>\n      <mat-form-field appearance=\"outline\" *ngIf=\"fixedControl?.value == 'fixed'\">\n        <mat-label>X</mat-label>\n        <input matInput type=\"number\" [formControl]=\"xControl\" min=\"1\" max=\"7\">\n        <mat-error>\n          <lib-swui-control-messages [messages]=\"messageErrors\" [control]=\"xControl\"></lib-swui-control-messages>\n        </mat-error>\n      </mat-form-field>\n\n      <mat-form-field appearance=\"outline\" *ngIf=\"fixedControl?.value\">\n        <mat-label>Y</mat-label>\n        <input matInput type=\"number\" [formControl]=\"yControl\" min=\"1\" max=\"500\">\n        <mat-error>\n          <lib-swui-control-messages [messages]=\"messageErrors\" [control]=\"yControl\"></lib-swui-control-messages>\n        </mat-error>\n      </mat-form-field>\n    </div>\n\n    <div *ngSwitchCase=\"'portrait'\">\n      <mat-form-field appearance=\"outline\">\n        <mat-label>Width</mat-label>\n        <input matInput type=\"number\" [formControl]=\"widthControl\" min=\"1\" max=\"7\">\n        <mat-error>\n          <lib-swui-control-messages [messages]=\"messageErrors\" [control]=\"widthControl\"></lib-swui-control-messages>\n        </mat-error>\n      </mat-form-field>\n      <mat-form-field appearance=\"outline\">\n        <mat-label>Height</mat-label>\n        <input matInput type=\"number\" [formControl]=\"heightControl\" min=\"1\">\n        <mat-error>\n          <lib-swui-control-messages [messages]=\"messageErrors\" [control]=\"heightControl\"></lib-swui-control-messages>\n        </mat-error>\n      </mat-form-field>\n      <mat-form-field appearance=\"outline\">\n        <mat-label>Y</mat-label>\n        <input matInput type=\"number\" [formControl]=\"yControl\" min=\"1\" max=\"500\">\n        <mat-error>\n          <lib-swui-control-messages [messages]=\"messageErrors\" [control]=\"yControl\"></lib-swui-control-messages>\n        </mat-error>\n      </mat-form-field>\n    </div>\n\n    <div *ngSwitchDefault>\n      <mat-form-field appearance=\"outline\" *ngIf=\"fixedControl?.value\">\n        <mat-label>Width</mat-label>\n        <input matInput type=\"number\" [formControl]=\"widthControl\" min=\"1\" max=\"7\">\n        <mat-error>\n          <lib-swui-control-messages [messages]=\"messageErrors\" [control]=\"widthControl\"></lib-swui-control-messages>\n        </mat-error>\n      </mat-form-field>\n      <mat-form-field appearance=\"outline\" *ngIf=\"fixedControl?.value\">\n        <mat-label>Height</mat-label>\n        <input matInput type=\"number\" [formControl]=\"heightControl\" min=\"1\">\n        <mat-error>\n          <lib-swui-control-messages [messages]=\"messageErrors\" [control]=\"heightControl\"></lib-swui-control-messages>\n        </mat-error>\n      </mat-form-field>\n      <mat-form-field appearance=\"outline\" *ngIf=\"fixedControl?.value == 'fixed'\">\n        <mat-label>X</mat-label>\n        <input matInput type=\"number\" [formControl]=\"xControl\" min=\"1\" max=\"7\">\n        <mat-error>\n          <lib-swui-control-messages [messages]=\"messageErrors\" [control]=\"xControl\"></lib-swui-control-messages>\n        </mat-error>\n      </mat-form-field>\n      <mat-form-field appearance=\"outline\" *ngIf=\"fixedControl?.value\">\n        <mat-label>Y</mat-label>\n        <input matInput type=\"number\" [formControl]=\"yControl\" min=\"1\" max=\"500\">\n        <mat-error>\n          <lib-swui-control-messages [messages]=\"messageErrors\" [control]=\"yControl\"></lib-swui-control-messages>\n        </mat-error>\n      </mat-form-field>\n    </div>\n  </ng-container>\n\n</div>\n\n"], "mappings": "AACA,SAA0BA,WAAW,EAAEC,SAAS,EAAEC,UAAU,QAAQ,gBAAgB;AAEpF,SAASC,iBAAiB,QAAQ,4DAA4D;;;;;;;;;;;;ICA1FC,EADF,CAAAC,cAAA,wBAAkE,gBACrD;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC/BH,EAAA,CAAAC,cAAA,oBAA2F;IAAzCD,EAAA,CAAAI,UAAA,6BAAAC,2FAAAC,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAmBF,MAAA,CAAAG,YAAA,CAAAN,MAAA,CAAoB;IAAA,EAAC;IACxFN,EAAA,CAAAC,cAAA,oBAAyB;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAa;IAC1CH,EAAA,CAAAC,cAAA,oBAA0B;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAa;IAC5CH,EAAA,CAAAC,cAAA,oBAA2B;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAa;IAC9CH,EAAA,CAAAC,cAAA,sBAA0B;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAEnCF,EAFmC,CAAAG,YAAA,EAAa,EACjC,EACE;;;;IANHH,EAAA,CAAAa,SAAA,GAA4B;IAA5Bb,EAAA,CAAAc,UAAA,gBAAAL,MAAA,CAAAM,YAAA,CAA4B;;;;;IAyBpCf,EADF,CAAAC,cAAA,wBAA4E,gBAC/D;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACxBH,EAAA,CAAAgB,SAAA,gBAAuE;IACvEhB,EAAA,CAAAC,cAAA,gBAAW;IACTD,EAAA,CAAAgB,SAAA,oCAAuG;IAE3GhB,EADE,CAAAG,YAAA,EAAY,EACG;;;;IAJeH,EAAA,CAAAa,SAAA,GAAwB;IAAxBb,EAAA,CAAAc,UAAA,gBAAAL,MAAA,CAAAQ,QAAA,CAAwB;IAEzBjB,EAAA,CAAAa,SAAA,GAA0B;IAACb,EAA3B,CAAAc,UAAA,aAAAL,MAAA,CAAAS,aAAA,CAA0B,YAAAT,MAAA,CAAAQ,QAAA,CAAqB;;;;;IAK5EjB,EADF,CAAAC,cAAA,wBAAiE,gBACpD;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACxBH,EAAA,CAAAgB,SAAA,gBAAyE;IACzEhB,EAAA,CAAAC,cAAA,gBAAW;IACTD,EAAA,CAAAgB,SAAA,oCAAuG;IAE3GhB,EADE,CAAAG,YAAA,EAAY,EACG;;;;IAJeH,EAAA,CAAAa,SAAA,GAAwB;IAAxBb,EAAA,CAAAc,UAAA,gBAAAL,MAAA,CAAAU,QAAA,CAAwB;IAEzBnB,EAAA,CAAAa,SAAA,GAA0B;IAACb,EAA3B,CAAAc,UAAA,aAAAL,MAAA,CAAAS,aAAA,CAA0B,YAAAT,MAAA,CAAAU,QAAA,CAAqB;;;;;IAzB5EnB,EAFJ,CAAAC,cAAA,UAA8B,wBACS,gBACxB;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC5BH,EAAA,CAAAgB,SAAA,gBAA2E;IAC3EhB,EAAA,CAAAC,cAAA,gBAAW;IACTD,EAAA,CAAAgB,SAAA,oCAA2G;IAE/GhB,EADE,CAAAG,YAAA,EAAY,EACG;IAEfH,EADF,CAAAC,cAAA,wBAAqC,gBACxB;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC7BH,EAAA,CAAAgB,SAAA,iBAAoE;IACpEhB,EAAA,CAAAC,cAAA,iBAAW;IACTD,EAAA,CAAAgB,SAAA,qCAA4G;IAEhHhB,EADE,CAAAG,YAAA,EAAY,EACG;IASjBH,EARA,CAAAoB,UAAA,KAAAC,2DAAA,4BAA4E,KAAAC,2DAAA,4BAQX;IAOnEtB,EAAA,CAAAG,YAAA,EAAM;;;;IA3B4BH,EAAA,CAAAa,SAAA,GAA4B;IAA5Bb,EAAA,CAAAc,UAAA,gBAAAL,MAAA,CAAAc,YAAA,CAA4B;IAE7BvB,EAAA,CAAAa,SAAA,GAA0B;IAACb,EAA3B,CAAAc,UAAA,aAAAL,MAAA,CAAAS,aAAA,CAA0B,YAAAT,MAAA,CAAAc,YAAA,CAAyB;IAKlDvB,EAAA,CAAAa,SAAA,GAA6B;IAA7Bb,EAAA,CAAAc,UAAA,gBAAAL,MAAA,CAAAe,aAAA,CAA6B;IAE9BxB,EAAA,CAAAa,SAAA,GAA0B;IAACb,EAA3B,CAAAc,UAAA,aAAAL,MAAA,CAAAS,aAAA,CAA0B,YAAAT,MAAA,CAAAe,aAAA,CAA0B;IAG7CxB,EAAA,CAAAa,SAAA,EAAoC;IAApCb,EAAA,CAAAc,UAAA,UAAAL,MAAA,CAAAM,YAAA,kBAAAN,MAAA,CAAAM,YAAA,CAAAU,KAAA,aAAoC;IAQpCzB,EAAA,CAAAa,SAAA,EAAyB;IAAzBb,EAAA,CAAAc,UAAA,SAAAL,MAAA,CAAAM,YAAA,kBAAAN,MAAA,CAAAM,YAAA,CAAAU,KAAA,CAAyB;;;;;IAW7DzB,EAFJ,CAAAC,cAAA,UAAgC,wBACO,gBACxB;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC5BH,EAAA,CAAAgB,SAAA,gBAA2E;IAC3EhB,EAAA,CAAAC,cAAA,gBAAW;IACTD,EAAA,CAAAgB,SAAA,oCAA2G;IAE/GhB,EADE,CAAAG,YAAA,EAAY,EACG;IAEfH,EADF,CAAAC,cAAA,wBAAqC,gBACxB;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC7BH,EAAA,CAAAgB,SAAA,iBAAoE;IACpEhB,EAAA,CAAAC,cAAA,iBAAW;IACTD,EAAA,CAAAgB,SAAA,qCAA4G;IAEhHhB,EADE,CAAAG,YAAA,EAAY,EACG;IAEfH,EADF,CAAAC,cAAA,yBAAqC,iBACxB;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACxBH,EAAA,CAAAgB,SAAA,iBAAyE;IACzEhB,EAAA,CAAAC,cAAA,iBAAW;IACTD,EAAA,CAAAgB,SAAA,qCAAuG;IAG7GhB,EAFI,CAAAG,YAAA,EAAY,EACG,EACb;;;;IAnB4BH,EAAA,CAAAa,SAAA,GAA4B;IAA5Bb,EAAA,CAAAc,UAAA,gBAAAL,MAAA,CAAAc,YAAA,CAA4B;IAE7BvB,EAAA,CAAAa,SAAA,GAA0B;IAACb,EAA3B,CAAAc,UAAA,aAAAL,MAAA,CAAAS,aAAA,CAA0B,YAAAT,MAAA,CAAAc,YAAA,CAAyB;IAKlDvB,EAAA,CAAAa,SAAA,GAA6B;IAA7Bb,EAAA,CAAAc,UAAA,gBAAAL,MAAA,CAAAe,aAAA,CAA6B;IAE9BxB,EAAA,CAAAa,SAAA,GAA0B;IAACb,EAA3B,CAAAc,UAAA,aAAAL,MAAA,CAAAS,aAAA,CAA0B,YAAAT,MAAA,CAAAe,aAAA,CAA0B;IAKnDxB,EAAA,CAAAa,SAAA,GAAwB;IAAxBb,EAAA,CAAAc,UAAA,gBAAAL,MAAA,CAAAU,QAAA,CAAwB;IAEzBnB,EAAA,CAAAa,SAAA,GAA0B;IAACb,EAA3B,CAAAc,UAAA,aAAAL,MAAA,CAAAS,aAAA,CAA0B,YAAAT,MAAA,CAAAU,QAAA,CAAqB;;;;;IAO5EnB,EADF,CAAAC,cAAA,wBAAiE,gBACpD;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC5BH,EAAA,CAAAgB,SAAA,gBAA2E;IAC3EhB,EAAA,CAAAC,cAAA,gBAAW;IACTD,EAAA,CAAAgB,SAAA,oCAA2G;IAE/GhB,EADE,CAAAG,YAAA,EAAY,EACG;;;;IAJeH,EAAA,CAAAa,SAAA,GAA4B;IAA5Bb,EAAA,CAAAc,UAAA,gBAAAL,MAAA,CAAAc,YAAA,CAA4B;IAE7BvB,EAAA,CAAAa,SAAA,GAA0B;IAACb,EAA3B,CAAAc,UAAA,aAAAL,MAAA,CAAAS,aAAA,CAA0B,YAAAT,MAAA,CAAAc,YAAA,CAAyB;;;;;IAIhFvB,EADF,CAAAC,cAAA,wBAAiE,gBACpD;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC7BH,EAAA,CAAAgB,SAAA,gBAAoE;IACpEhB,EAAA,CAAAC,cAAA,gBAAW;IACTD,EAAA,CAAAgB,SAAA,oCAA4G;IAEhHhB,EADE,CAAAG,YAAA,EAAY,EACG;;;;IAJeH,EAAA,CAAAa,SAAA,GAA6B;IAA7Bb,EAAA,CAAAc,UAAA,gBAAAL,MAAA,CAAAe,aAAA,CAA6B;IAE9BxB,EAAA,CAAAa,SAAA,GAA0B;IAACb,EAA3B,CAAAc,UAAA,aAAAL,MAAA,CAAAS,aAAA,CAA0B,YAAAT,MAAA,CAAAe,aAAA,CAA0B;;;;;IAIjFxB,EADF,CAAAC,cAAA,wBAA4E,gBAC/D;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACxBH,EAAA,CAAAgB,SAAA,gBAAuE;IACvEhB,EAAA,CAAAC,cAAA,gBAAW;IACTD,EAAA,CAAAgB,SAAA,oCAAuG;IAE3GhB,EADE,CAAAG,YAAA,EAAY,EACG;;;;IAJeH,EAAA,CAAAa,SAAA,GAAwB;IAAxBb,EAAA,CAAAc,UAAA,gBAAAL,MAAA,CAAAQ,QAAA,CAAwB;IAEzBjB,EAAA,CAAAa,SAAA,GAA0B;IAACb,EAA3B,CAAAc,UAAA,aAAAL,MAAA,CAAAS,aAAA,CAA0B,YAAAT,MAAA,CAAAQ,QAAA,CAAqB;;;;;IAI5EjB,EADF,CAAAC,cAAA,wBAAiE,gBACpD;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACxBH,EAAA,CAAAgB,SAAA,gBAAyE;IACzEhB,EAAA,CAAAC,cAAA,gBAAW;IACTD,EAAA,CAAAgB,SAAA,oCAAuG;IAE3GhB,EADE,CAAAG,YAAA,EAAY,EACG;;;;IAJeH,EAAA,CAAAa,SAAA,GAAwB;IAAxBb,EAAA,CAAAc,UAAA,gBAAAL,MAAA,CAAAU,QAAA,CAAwB;IAEzBnB,EAAA,CAAAa,SAAA,GAA0B;IAACb,EAA3B,CAAAc,UAAA,aAAAL,MAAA,CAAAS,aAAA,CAA0B,YAAAT,MAAA,CAAAU,QAAA,CAAqB;;;;;IA1BhFnB,EAAA,CAAAC,cAAA,UAAsB;IAsBpBD,EArBA,CAAAoB,UAAA,IAAAM,0DAAA,4BAAiE,IAAAC,0DAAA,4BAOA,IAAAC,0DAAA,4BAOW,IAAAC,0DAAA,4BAOX;IAOnE7B,EAAA,CAAAG,YAAA,EAAM;;;;IA5BkCH,EAAA,CAAAa,SAAA,EAAyB;IAAzBb,EAAA,CAAAc,UAAA,SAAAL,MAAA,CAAAM,YAAA,kBAAAN,MAAA,CAAAM,YAAA,CAAAU,KAAA,CAAyB;IAOzBzB,EAAA,CAAAa,SAAA,EAAyB;IAAzBb,EAAA,CAAAc,UAAA,SAAAL,MAAA,CAAAM,YAAA,kBAAAN,MAAA,CAAAM,YAAA,CAAAU,KAAA,CAAyB;IAOzBzB,EAAA,CAAAa,SAAA,EAAoC;IAApCb,EAAA,CAAAc,UAAA,UAAAL,MAAA,CAAAM,YAAA,kBAAAN,MAAA,CAAAM,YAAA,CAAAU,KAAA,aAAoC;IAOpCzB,EAAA,CAAAa,SAAA,EAAyB;IAAzBb,EAAA,CAAAc,UAAA,SAAAL,MAAA,CAAAM,YAAA,kBAAAN,MAAA,CAAAM,YAAA,CAAAU,KAAA,CAAyB;;;ADjFrE,OAAM,SAAUK,iBAAiBA,CAACC,QAAkB,EAAEC,OAAiD;EACrG,MAAMC,KAAK,GAAG,IAAIpC,SAAS,CAAC;IAC1BqC,KAAK,EAAE,IAAItC,WAAW,CAACoC,OAAO,EAAEE,KAAK,CAAC;IACtCC,CAAC,EAAE,IAAIvC,WAAW,CAACoC,OAAO,EAAEG,CAAC,EAAE;MAAEC,QAAQ,EAAE;IAAM,CAAE,CAAC;IACpDC,CAAC,EAAE,IAAIzC,WAAW,CAACoC,OAAO,EAAEK,CAAC,EAAE;MAAEC,UAAU,EAAExC,UAAU,CAACyC,OAAO,CAAC,CAC5DzC,UAAU,CAAC0C,QAAQ,EACnB1C,UAAU,CAAC2C,GAAG,CAAC,CAAC,CAAC,EACjB3C,UAAU,CAAC4C,GAAG,CAAC,GAAG,CAAC,EACnB3C,iBAAiB,CAAC4C,mBAAmB,CACtC,CAAC;MAAEP,QAAQ,EAAE;IAAM,CAAC,CAAC;IACxBQ,KAAK,EAAE,IAAIhD,WAAW,CAACmC,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,KAAK,UAAU,GAAG,CAAC,GAAGC,OAAO,EAAEY,KAAK,EAAE;MAAEN,UAAU,EAAExC,UAAU,CAACyC,OAAO,CAAC,CAC7HzC,UAAU,CAAC0C,QAAQ,EACnB1C,UAAU,CAAC2C,GAAG,CAAC,CAAC,CAAC,EACjB3C,UAAU,CAAC4C,GAAG,CAAC,CAAC,CAAC,EACjB3C,iBAAiB,CAAC4C,mBAAmB,CACtC,CAAC;MAAEP,QAAQ,EAAE;IAAM,CAAE,CAAC;IACvBS,MAAM,EAAE,IAAIjD,WAAW,CAACmC,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,KAAK,UAAU,GAAG,CAAC,GAAGC,OAAO,EAAEa,MAAM,EAAE;MAAEP,UAAU,EAAExC,UAAU,CAACyC,OAAO,CAAC,CAC/HzC,UAAU,CAAC0C,QAAQ,EACnB1C,UAAU,CAAC2C,GAAG,CAAC,CAAC,CAAC,EACjB1C,iBAAiB,CAAC4C,mBAAmB,CACtC,CAAC;MAAEP,QAAQ,EAAE;IAAM,CAAC;GACtB,CAAC;EAEF,MAAMnB,QAAQ,GAAGgB,KAAK,CAACa,GAAG,CAAC,GAAG,CAAgB;EAC9C,MAAM/B,YAAY,GAAGkB,KAAK,CAACa,GAAG,CAAC,OAAO,CAAgB;EAEtD,IAAI/B,YAAY,CAACU,KAAK,KAAK,OAAO,EAAE;IAClCR,QAAQ,CAAC8B,aAAa,CAACjD,UAAU,CAACyC,OAAO,CAAC,CACxCzC,UAAU,CAAC0C,QAAQ,EACnB1C,UAAU,CAAC2C,GAAG,CAAC,CAAC,CAAC,EACjB3C,UAAU,CAAC4C,GAAG,CAAC,CAAC,CAAC,EACjB3C,iBAAiB,CAAC4C,mBAAmB,CACtC,CAAC,CAAC;EACL,CAAC,MAAM;IACL1B,QAAQ,CAAC+B,eAAe,EAAE;IAC1B/B,QAAQ,CAACgC,QAAQ,CAAC,IAAI,CAAC;EACzB;EAEA,OAAOhB,KAAK;AACd;AAOA,OAAM,MAAOiB,0BAA0B;EALvCC,YAAA;IASE,KAAAjC,aAAa,GAAiB;MAC5BwB,GAAG,EAAE,gBAAgB;MACrBD,GAAG,EAAE,gBAAgB;MACrBD,QAAQ,EAAE,qBAAqB;MAC/BY,iBAAiB,EAAE;KACpB;;EAEDC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACtB,QAAQ,KAAKuB,SAAS,EAAE;MAC/B,IAAI,CAACvC,YAAY,EAAEwC,OAAO,CAAC;QAAEC,SAAS,EAAE;MAAK,CAAE,CAAC;MAChD,IAAI,CAACvC,QAAQ,EAAEsC,OAAO,CAAC;QAAEC,SAAS,EAAE;MAAK,CAAE,CAAC;MAC5C,IAAI,CAACrC,QAAQ,EAAEoC,OAAO,CAAC;QAAEC,SAAS,EAAE;MAAK,CAAE,CAAC;MAC5C,IAAI,CAACjC,YAAY,EAAEgC,OAAO,CAAC;QAAEC,SAAS,EAAE;MAAK,CAAE,CAAC;MAChD,IAAI,CAAChC,aAAa,EAAE+B,OAAO,CAAC;QAAEC,SAAS,EAAE;MAAK,CAAE,CAAC;IACnD,CAAC,MAAM,IAAI,IAAI,CAACzB,QAAQ,KAAK,UAAU,EAAE;MACvC,IAAI,CAAChB,YAAY,EAAEwC,OAAO,CAAC;QAAEC,SAAS,EAAE;MAAK,CAAE,CAAC;MAChD,IAAI,CAACvC,QAAQ,EAAEsC,OAAO,CAAC;QAAEC,SAAS,EAAE;MAAK,CAAE,CAAC;MAC5C,IAAI,CAACjC,YAAY,EAAEgC,OAAO,CAAC;QAAEC,SAAS,EAAE;MAAK,CAAE,CAAC;MAChD,IAAI,CAAChC,aAAa,EAAE+B,OAAO,CAAC;QAAEC,SAAS,EAAE;MAAK,CAAE,CAAC;IACnD,CAAC,MAAM,IAAI,IAAI,CAACzB,QAAQ,KAAK,QAAQ,EAAE;MACrC,IAAI,CAACR,YAAY,EAAEgC,OAAO,CAAC;QAAEC,SAAS,EAAE;MAAK,CAAE,CAAC;MAChD,IAAI,CAAChC,aAAa,EAAE+B,OAAO,CAAC;QAAEC,SAAS,EAAE;MAAK,CAAE,CAAC;IACnD;EACF;EAEA5C,YAAYA,CAAE;IAAEa;EAAK,CAAmB;IACtC,IAAIA,KAAK,KAAK,OAAO,EAAE;MACrB,IAAI,CAACR,QAAQ,CAAC8B,aAAa,CAACjD,UAAU,CAACyC,OAAO,CAAC,CAC7CzC,UAAU,CAAC0C,QAAQ,EACnB1C,UAAU,CAAC2C,GAAG,CAAC,CAAC,CAAC,EACjB3C,UAAU,CAAC4C,GAAG,CAAC,CAAC,CAAC,EACjB3C,iBAAiB,CAAC4C,mBAAmB,CACtC,CAAC,CAAC;IACL,CAAC,MAAM;MACL,IAAI,CAAC1B,QAAQ,CAAC+B,eAAe,EAAE;MAC/B,IAAI,CAAC/B,QAAQ,CAACgC,QAAQ,CAAC,IAAI,EAAE;QAAEO,SAAS,EAAE;MAAI,CAAE,CAAC;IACnD;IACA,IAAI,CAACvC,QAAQ,CAACwC,sBAAsB,EAAE;EACxC;EAEA,IAAIC,IAAIA,CAAA;IACN,OAAO,IAAI,CAACC,OAAoB;EAClC;EAEA,IAAI5C,YAAYA,CAAA;IACd,OAAO,IAAI,CAAC2C,IAAI,EAAEZ,GAAG,CAAC,OAAO,CAAgB;EAC/C;EAEA,IAAI7B,QAAQA,CAAA;IACV,OAAO,IAAI,CAACyC,IAAI,EAAEZ,GAAG,CAAC,GAAG,CAAgB;EAC3C;EAEA,IAAI3B,QAAQA,CAAA;IACV,OAAO,IAAI,CAACuC,IAAI,EAAEZ,GAAG,CAAC,GAAG,CAAgB;EAC3C;EAEA,IAAIvB,YAAYA,CAAA;IACd,OAAO,IAAI,CAACmC,IAAI,EAAEZ,GAAG,CAAC,OAAO,CAAgB;EAC/C;EAEA,IAAItB,aAAaA,CAAA;IACf,OAAO,IAAI,CAACkC,IAAI,EAAEZ,GAAG,CAAC,QAAQ,CAAgB;EAChD;;;uCAlEWI,0BAA0B;IAAA;EAAA;;;YAA1BA,0BAA0B;MAAAU,SAAA;MAAAC,MAAA;QAAA9B,QAAA;QAAA4B,OAAA;MAAA;MAAAG,QAAA,GAAA9D,EAAA,CAAA+D,oBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCxDvCrE,EAAA,CAAAC,cAAA,aAAiD;UAE/CD,EAAA,CAAAoB,UAAA,IAAAmD,oDAAA,6BAAkE;UAUlEvE,EAAA,CAAAwE,uBAAA,MAAoC;UAyDlCxE,EAxDA,CAAAoB,UAAA,IAAAqD,yCAAA,kBAA8B,IAAAC,yCAAA,kBAgCE,IAAAC,yCAAA,iBAwBV;;UAgC1B3E,EAAA,CAAAG,YAAA,EAAM;;;UArGDH,EAAA,CAAAc,UAAA,cAAAwD,GAAA,CAAAZ,IAAA,CAAkB;UAEiB1D,EAAA,CAAAa,SAAA,EAA0B;UAA1Bb,EAAA,CAAAc,UAAA,SAAAwD,GAAA,CAAAvC,QAAA,eAA0B;UAUlD/B,EAAA,CAAAa,SAAA,EAAqB;UAArBb,EAAA,CAAAc,UAAA,aAAAwD,GAAA,CAAAvC,QAAA,CAAqB;UAC3B/B,EAAA,CAAAa,SAAA,EAAsB;UAAtBb,EAAA,CAAAc,UAAA,0BAAsB;UAgCtBd,EAAA,CAAAa,SAAA,EAAwB;UAAxBb,EAAA,CAAAc,UAAA,4BAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}