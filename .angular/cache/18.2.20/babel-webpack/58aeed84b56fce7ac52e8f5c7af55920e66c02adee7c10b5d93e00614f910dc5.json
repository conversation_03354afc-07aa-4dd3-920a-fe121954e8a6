{"ast": null, "code": "import { Injectable, Component, ChangeDetectionStrategy, EventEmitter, Directive, ViewContainerRef, ElementRef, Renderer2, Input, Output, NgModule } from '@angular/core';\nimport { isBs3, warnOnce, parseTriggers, OnChange } from 'ngx-bootstrap/utils';\nimport { __decorate, __metadata } from 'tslib';\nimport { ComponentLoaderFactory } from 'ngx-bootstrap/component-loader';\nimport { PositioningService } from 'ngx-bootstrap/positioning';\nimport { timer } from 'rxjs';\nimport { CommonModule } from '@angular/common';\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n/**\n * Default values provider for tooltip\n */\nclass TooltipConfig {\n  constructor() {\n    /**\n     * sets disable adaptive position\n     */\n    this.adaptivePosition = true;\n    /**\n     * tooltip placement, supported positions: 'top', 'bottom', 'left', 'right'\n     */\n    this.placement = 'top';\n    /**\n     * array of event names which triggers tooltip opening\n     */\n    this.triggers = 'hover focus';\n    /**\n     * delay before showing the tooltip\n     */\n    this.delay = 0;\n  }\n}\nTooltipConfig.decorators = [{\n  type: Injectable\n}];\nif (false) {\n  /**\n   * sets disable adaptive position\n   * @type {?}\n   */\n  TooltipConfig.prototype.adaptivePosition;\n  /**\n   * tooltip placement, supported positions: 'top', 'bottom', 'left', 'right'\n   * @type {?}\n   */\n  TooltipConfig.prototype.placement;\n  /**\n   * array of event names which triggers tooltip opening\n   * @type {?}\n   */\n  TooltipConfig.prototype.triggers;\n  /**\n   * a selector specifying the element the tooltip should be appended to.\n   * @type {?}\n   */\n  TooltipConfig.prototype.container;\n  /**\n   * delay before showing the tooltip\n   * @type {?}\n   */\n  TooltipConfig.prototype.delay;\n}\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\nclass TooltipContainerComponent {\n  /**\n   * @param {?} config\n   */\n  constructor(config) {\n    Object.assign(this, config);\n  }\n  /**\n   * @return {?}\n   */\n  get isBs3() {\n    return isBs3();\n  }\n  /**\n   * @return {?}\n   */\n  ngAfterViewInit() {\n    this.classMap = {\n      in: false,\n      fade: false\n    };\n    this.classMap[this.placement] = true;\n    this.classMap[`tooltip-${this.placement}`] = true;\n    this.classMap.in = true;\n    if (this.animation) {\n      this.classMap.fade = true;\n    }\n    if (this.containerClass) {\n      this.classMap[this.containerClass] = true;\n    }\n  }\n}\nTooltipContainerComponent.decorators = [{\n  type: Component,\n  args: [{\n    selector: 'bs-tooltip-container',\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    // tslint:disable-next-line\n    host: {\n      '[class]': '\"tooltip in tooltip-\" + placement + \" \" + \"bs-tooltip-\" + placement + \" \" + placement + \" \" + containerClass',\n      '[class.show]': '!isBs3',\n      '[class.bs3]': 'isBs3',\n      '[attr.id]': 'this.id',\n      role: 'tooltip'\n    },\n    template: `\n    <div class=\"tooltip-arrow arrow\"></div>\n    <div class=\"tooltip-inner\"><ng-content></ng-content></div>\n    `,\n    styles: [`\n    :host.tooltip {\n      display: block;\n      pointer-events: none;\n    }\n    :host.bs3.tooltip.top>.arrow {\n      margin-left: -2px;\n    }\n    :host.bs3.tooltip.bottom {\n      margin-top: 0px;\n    }\n    :host.bs3.bs-tooltip-left, :host.bs3.bs-tooltip-right{\n      margin: 0px;\n    }\n    :host.bs3.bs-tooltip-right .arrow, :host.bs3.bs-tooltip-left .arrow {\n      margin: .3rem 0;\n    }\n  `]\n  }]\n}];\n/** @nocollapse */\nTooltipContainerComponent.ctorParameters = () => [{\n  type: TooltipConfig\n}];\nif (false) {\n  /** @type {?} */\n  TooltipContainerComponent.prototype.classMap;\n  /** @type {?} */\n  TooltipContainerComponent.prototype.placement;\n  /** @type {?} */\n  TooltipContainerComponent.prototype.containerClass;\n  /** @type {?} */\n  TooltipContainerComponent.prototype.animation;\n  /** @type {?} */\n  TooltipContainerComponent.prototype.id;\n}\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n/** @type {?} */\nlet id = 0;\nclass TooltipDirective {\n  /**\n   * @param {?} _viewContainerRef\n   * @param {?} cis\n   * @param {?} config\n   * @param {?} _elementRef\n   * @param {?} _renderer\n   * @param {?} _positionService\n   */\n  constructor(_viewContainerRef, cis, config, _elementRef, _renderer, _positionService) {\n    this._elementRef = _elementRef;\n    this._renderer = _renderer;\n    this._positionService = _positionService;\n    this.tooltipId = id++;\n    /**\n     * Fired when tooltip content changes\n     */\n    /* tslint:disable-next-line:no-any */\n    this.tooltipChange = new EventEmitter();\n    /**\n     * Css class for tooltip container\n     */\n    this.containerClass = '';\n    /**\n     * @deprecated - removed, will be added to configuration\n     */\n    this.tooltipAnimation = true;\n    /**\n     * @deprecated\n     */\n    this.tooltipFadeDuration = 150;\n    /**\n     * @deprecated\n     */\n    this.tooltipStateChanged = new EventEmitter();\n    this._tooltip = cis.createLoader(this._elementRef, _viewContainerRef, this._renderer).provide({\n      provide: TooltipConfig,\n      useValue: config\n    });\n    Object.assign(this, config);\n    this.onShown = this._tooltip.onShown;\n    this.onHidden = this._tooltip.onHidden;\n  }\n  /**\n   * Returns whether or not the tooltip is currently being shown\n   * @return {?}\n   */\n  get isOpen() {\n    return this._tooltip.isShown;\n  }\n  /**\n   * @param {?} value\n   * @return {?}\n   */\n  set isOpen(value) {\n    if (value) {\n      this.show();\n    } else {\n      this.hide();\n    }\n  }\n  /**\n   * @deprecated - please use `tooltip` instead\n   * @param {?} value\n   * @return {?}\n   */\n  set htmlContent(value) {\n    warnOnce('tooltipHtml was deprecated, please use `tooltip` instead');\n    this.tooltip = value;\n  }\n  /**\n   * @deprecated - please use `placement` instead\n   * @param {?} value\n   * @return {?}\n   */\n  set _placement(value) {\n    warnOnce('tooltipPlacement was deprecated, please use `placement` instead');\n    this.placement = value;\n  }\n  /**\n   * @deprecated - please use `isOpen` instead\n   * @param {?} value\n   * @return {?}\n   */\n  set _isOpen(value) {\n    warnOnce('tooltipIsOpen was deprecated, please use `isOpen` instead');\n    this.isOpen = value;\n  }\n  /**\n   * @return {?}\n   */\n  get _isOpen() {\n    warnOnce('tooltipIsOpen was deprecated, please use `isOpen` instead');\n    return this.isOpen;\n  }\n  /**\n   * @deprecated - please use `isDisabled` instead\n   * @param {?} value\n   * @return {?}\n   */\n  set _enable(value) {\n    warnOnce('tooltipEnable was deprecated, please use `isDisabled` instead');\n    this.isDisabled = !value;\n  }\n  /**\n   * @return {?}\n   */\n  get _enable() {\n    warnOnce('tooltipEnable was deprecated, please use `isDisabled` instead');\n    return this.isDisabled;\n  }\n  /**\n   * @deprecated - please use `container=\"body\"` instead\n   * @param {?} value\n   * @return {?}\n   */\n  set _appendToBody(value) {\n    warnOnce('tooltipAppendToBody was deprecated, please use `container=\"body\"` instead');\n    this.container = value ? 'body' : this.container;\n  }\n  /**\n   * @return {?}\n   */\n  get _appendToBody() {\n    warnOnce('tooltipAppendToBody was deprecated, please use `container=\"body\"` instead');\n    return this.container === 'body';\n  }\n  /**\n   * @deprecated - will replaced with customClass\n   * @param {?} value\n   * @return {?}\n   */\n  set _popupClass(value) {\n    warnOnce('tooltipClass deprecated');\n  }\n  /**\n   * @deprecated - removed\n   * @param {?} value\n   * @return {?}\n   */\n  set _tooltipContext(value) {\n    warnOnce('tooltipContext deprecated');\n  }\n  /**\n   * @deprecated\n   * @param {?} value\n   * @return {?}\n   */\n  set _tooltipPopupDelay(value) {\n    warnOnce('tooltipPopupDelay is deprecated, use `delay` instead');\n    this.delay = value;\n  }\n  /**\n   * @deprecated -  please use `triggers` instead\n   * @return {?}\n   */\n  get _tooltipTrigger() {\n    warnOnce('tooltipTrigger was deprecated, please use `triggers` instead');\n    return this.triggers;\n  }\n  /**\n   * @param {?} value\n   * @return {?}\n   */\n  set _tooltipTrigger(value) {\n    warnOnce('tooltipTrigger was deprecated, please use `triggers` instead');\n    this.triggers = (value || '').toString();\n  }\n  /**\n   * @return {?}\n   */\n  ngOnInit() {\n    this._tooltip.listen({\n      triggers: this.triggers,\n      show: (\n      /**\n      * @return {?}\n      */\n      () => this.show())\n    });\n    /* tslint:disable-next-line:no-any */\n    this.tooltipChange.subscribe(\n    /**\n    * @param {?} value\n    * @return {?}\n    */\n    value => {\n      if (!value) {\n        this._tooltip.hide();\n      }\n    });\n    this.onShown.subscribe(\n    /**\n    * @return {?}\n    */\n    () => {\n      this.setAriaDescribedBy();\n    });\n    this.onHidden.subscribe(\n    /**\n    * @return {?}\n    */\n    () => {\n      this.setAriaDescribedBy();\n    });\n  }\n  /**\n   * @return {?}\n   */\n  setAriaDescribedBy() {\n    this._ariaDescribedby = this.isOpen ? `tooltip-${this.tooltipId}` : null;\n    if (this._ariaDescribedby) {\n      this._renderer.setAttribute(this._elementRef.nativeElement, 'aria-describedby', this._ariaDescribedby);\n    } else {\n      this._renderer.removeAttribute(this._elementRef.nativeElement, 'aria-describedby');\n    }\n  }\n  /**\n   * Toggles an element’s tooltip. This is considered a “manual” triggering of\n   * the tooltip.\n   * @return {?}\n   */\n  toggle() {\n    if (this.isOpen) {\n      return this.hide();\n    }\n    this.show();\n  }\n  /**\n   * Opens an element’s tooltip. This is considered a “manual” triggering of\n   * the tooltip.\n   * @return {?}\n   */\n  show() {\n    this._positionService.setOptions({\n      modifiers: {\n        flip: {\n          enabled: this.adaptivePosition\n        },\n        preventOverflow: {\n          enabled: this.adaptivePosition\n        }\n      }\n    });\n    if (this.isOpen || this.isDisabled || this._delayTimeoutId || !this.tooltip) {\n      return;\n    }\n    /** @type {?} */\n    const showTooltip =\n    /**\n    * @return {?}\n    */\n    () => {\n      if (this._delayTimeoutId) {\n        this._delayTimeoutId = undefined;\n      }\n      this._tooltip.attach(TooltipContainerComponent).to(this.container).position({\n        attachment: this.placement\n      }).show({\n        content: this.tooltip,\n        placement: this.placement,\n        containerClass: this.containerClass,\n        id: `tooltip-${this.tooltipId}`\n      });\n    };\n    /** @type {?} */\n    const cancelDelayedTooltipShowing =\n    /**\n    * @return {?}\n    */\n    () => {\n      if (this._tooltipCancelShowFn) {\n        this._tooltipCancelShowFn();\n      }\n    };\n    if (this.delay) {\n      if (this._delaySubscription) {\n        this._delaySubscription.unsubscribe();\n      }\n      this._delaySubscription = timer(this.delay).subscribe(\n      /**\n      * @return {?}\n      */\n      () => {\n        showTooltip();\n        cancelDelayedTooltipShowing();\n      });\n      if (this.triggers) {\n        parseTriggers(this.triggers).forEach(\n        /**\n        * @param {?} trigger\n        * @return {?}\n        */\n        trigger => {\n          this._tooltipCancelShowFn = this._renderer.listen(this._elementRef.nativeElement, trigger.close,\n          /**\n          * @return {?}\n          */\n          () => {\n            this._delaySubscription.unsubscribe();\n            cancelDelayedTooltipShowing();\n          });\n        });\n      }\n    } else {\n      showTooltip();\n    }\n  }\n  /**\n   * Closes an element’s tooltip. This is considered a “manual” triggering of\n   * the tooltip.\n   * @return {?}\n   */\n  hide() {\n    if (this._delayTimeoutId) {\n      clearTimeout(this._delayTimeoutId);\n      this._delayTimeoutId = undefined;\n    }\n    if (!this._tooltip.isShown) {\n      return;\n    }\n    this._tooltip.instance.classMap.in = false;\n    setTimeout(\n    /**\n    * @return {?}\n    */\n    () => {\n      this._tooltip.hide();\n    }, this.tooltipFadeDuration);\n  }\n  /**\n   * @return {?}\n   */\n  ngOnDestroy() {\n    this._tooltip.dispose();\n    this.tooltipChange.unsubscribe();\n    if (this._delaySubscription) {\n      this._delaySubscription.unsubscribe();\n    }\n    this.onShown.unsubscribe();\n    this.onHidden.unsubscribe();\n  }\n}\nTooltipDirective.decorators = [{\n  type: Directive,\n  args: [{\n    selector: '[tooltip], [tooltipHtml]',\n    exportAs: 'bs-tooltip'\n  }]\n}];\n/** @nocollapse */\nTooltipDirective.ctorParameters = () => [{\n  type: ViewContainerRef\n}, {\n  type: ComponentLoaderFactory\n}, {\n  type: TooltipConfig\n}, {\n  type: ElementRef\n}, {\n  type: Renderer2\n}, {\n  type: PositioningService\n}];\nTooltipDirective.propDecorators = {\n  adaptivePosition: [{\n    type: Input\n  }],\n  tooltip: [{\n    type: Input\n  }],\n  tooltipChange: [{\n    type: Output\n  }],\n  placement: [{\n    type: Input\n  }],\n  triggers: [{\n    type: Input\n  }],\n  container: [{\n    type: Input\n  }],\n  containerClass: [{\n    type: Input\n  }],\n  isOpen: [{\n    type: Input\n  }],\n  isDisabled: [{\n    type: Input\n  }],\n  delay: [{\n    type: Input\n  }],\n  onShown: [{\n    type: Output\n  }],\n  onHidden: [{\n    type: Output\n  }],\n  htmlContent: [{\n    type: Input,\n    args: ['tooltipHtml']\n  }],\n  _placement: [{\n    type: Input,\n    args: ['tooltipPlacement']\n  }],\n  _isOpen: [{\n    type: Input,\n    args: ['tooltipIsOpen']\n  }],\n  _enable: [{\n    type: Input,\n    args: ['tooltipEnable']\n  }],\n  _appendToBody: [{\n    type: Input,\n    args: ['tooltipAppendToBody']\n  }],\n  tooltipAnimation: [{\n    type: Input\n  }],\n  _popupClass: [{\n    type: Input,\n    args: ['tooltipClass']\n  }],\n  _tooltipContext: [{\n    type: Input,\n    args: ['tooltipContext']\n  }],\n  _tooltipPopupDelay: [{\n    type: Input,\n    args: ['tooltipPopupDelay']\n  }],\n  tooltipFadeDuration: [{\n    type: Input\n  }],\n  _tooltipTrigger: [{\n    type: Input,\n    args: ['tooltipTrigger']\n  }],\n  tooltipStateChanged: [{\n    type: Output\n  }]\n};\n__decorate([OnChange(), __metadata(\"design:type\", Object)], TooltipDirective.prototype, \"tooltip\", void 0);\nif (false) {\n  /** @type {?} */\n  TooltipDirective.prototype.tooltipId;\n  /**\n   * sets disable adaptive position\n   * @type {?}\n   */\n  TooltipDirective.prototype.adaptivePosition;\n  /**\n   * Content to be displayed as tooltip.\n   * @type {?}\n   */\n  TooltipDirective.prototype.tooltip;\n  /**\n   * Fired when tooltip content changes\n   * @type {?}\n   */\n  TooltipDirective.prototype.tooltipChange;\n  /**\n   * Placement of a tooltip. Accepts: \"top\", \"bottom\", \"left\", \"right\"\n   * @type {?}\n   */\n  TooltipDirective.prototype.placement;\n  /**\n   * Specifies events that should trigger. Supports a space separated list of\n   * event names.\n   * @type {?}\n   */\n  TooltipDirective.prototype.triggers;\n  /**\n   * A selector specifying the element the tooltip should be appended to.\n   * @type {?}\n   */\n  TooltipDirective.prototype.container;\n  /**\n   * Css class for tooltip container\n   * @type {?}\n   */\n  TooltipDirective.prototype.containerClass;\n  /**\n   * Allows to disable tooltip\n   * @type {?}\n   */\n  TooltipDirective.prototype.isDisabled;\n  /**\n   * Delay before showing the tooltip\n   * @type {?}\n   */\n  TooltipDirective.prototype.delay;\n  /**\n   * Emits an event when the tooltip is shown\n   * @type {?}\n   */\n  TooltipDirective.prototype.onShown;\n  /**\n   * Emits an event when the tooltip is hidden\n   * @type {?}\n   */\n  TooltipDirective.prototype.onHidden;\n  /**\n   * @deprecated - removed, will be added to configuration\n   * @type {?}\n   */\n  TooltipDirective.prototype.tooltipAnimation;\n  /**\n   * @deprecated\n   * @type {?}\n   */\n  TooltipDirective.prototype.tooltipFadeDuration;\n  /**\n   * @deprecated\n   * @type {?}\n   */\n  TooltipDirective.prototype.tooltipStateChanged;\n  /**\n   * @type {?}\n   * @protected\n   */\n  TooltipDirective.prototype._delayTimeoutId;\n  /**\n   * @type {?}\n   * @protected\n   */\n  TooltipDirective.prototype._tooltipCancelShowFn;\n  /**\n   * @type {?}\n   * @private\n   */\n  TooltipDirective.prototype._tooltip;\n  /**\n   * @type {?}\n   * @private\n   */\n  TooltipDirective.prototype._delaySubscription;\n  /**\n   * @type {?}\n   * @private\n   */\n  TooltipDirective.prototype._ariaDescribedby;\n  /**\n   * @type {?}\n   * @private\n   */\n  TooltipDirective.prototype._elementRef;\n  /**\n   * @type {?}\n   * @private\n   */\n  TooltipDirective.prototype._renderer;\n  /**\n   * @type {?}\n   * @private\n   */\n  TooltipDirective.prototype._positionService;\n}\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\nclass TooltipModule {\n  /**\n   * @return {?}\n   */\n  static forRoot() {\n    return {\n      ngModule: TooltipModule,\n      providers: [TooltipConfig, ComponentLoaderFactory, PositioningService]\n    };\n  }\n}\nTooltipModule.decorators = [{\n  type: NgModule,\n  args: [{\n    imports: [CommonModule],\n    declarations: [TooltipDirective, TooltipContainerComponent],\n    exports: [TooltipDirective],\n    entryComponents: [TooltipContainerComponent]\n  }]\n}];\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\nexport { TooltipConfig, TooltipContainerComponent, TooltipDirective, TooltipModule };", "map": {"version": 3, "names": ["Injectable", "Component", "ChangeDetectionStrategy", "EventEmitter", "Directive", "ViewContainerRef", "ElementRef", "Renderer2", "Input", "Output", "NgModule", "isBs3", "warnOnce", "parseTriggers", "OnChange", "__decorate", "__metadata", "ComponentLoaderFactory", "PositioningService", "timer", "CommonModule", "TooltipConfig", "constructor", "adaptivePosition", "placement", "triggers", "delay", "decorators", "type", "prototype", "container", "TooltipContainerComponent", "config", "Object", "assign", "ngAfterViewInit", "classMap", "in", "fade", "animation", "containerClass", "args", "selector", "changeDetection", "OnPush", "host", "role", "template", "styles", "ctorParameters", "id", "TooltipDirective", "_viewContainerRef", "cis", "_elementRef", "_renderer", "_positionService", "tooltipId", "tooltipChange", "tooltipAnimation", "tooltipFadeDuration", "tooltipStateChanged", "_tooltip", "createLoader", "provide", "useValue", "onShown", "onHidden", "isOpen", "isShown", "value", "show", "hide", "htmlContent", "tooltip", "_placement", "_isOpen", "_enable", "isDisabled", "_appendToBody", "_popupClass", "_tooltipContext", "_tooltipPopupDelay", "_tooltipTrigger", "toString", "ngOnInit", "listen", "subscribe", "setAriaDescribedBy", "_ariaDes<PERSON>by", "setAttribute", "nativeElement", "removeAttribute", "toggle", "setOptions", "modifiers", "flip", "enabled", "preventOverflow", "_delayTimeoutId", "showTooltip", "undefined", "attach", "to", "position", "attachment", "content", "cancelDelayedTooltipShowing", "_tooltipCancelShowFn", "_delaySubscription", "unsubscribe", "for<PERSON>ach", "trigger", "close", "clearTimeout", "instance", "setTimeout", "ngOnDestroy", "dispose", "exportAs", "propDecorators", "TooltipModule", "forRoot", "ngModule", "providers", "imports", "declarations", "exports", "entryComponents"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/node_modules/ngx-bootstrap/tooltip/fesm2015/ngx-bootstrap-tooltip.js"], "sourcesContent": ["import { Injectable, Component, ChangeDetectionStrategy, EventEmitter, Directive, ViewContainerRef, ElementRef, Renderer2, Input, Output, NgModule } from '@angular/core';\nimport { isBs3, warnOnce, parseTriggers, OnChange } from 'ngx-bootstrap/utils';\nimport { __decorate, __metadata } from 'tslib';\nimport { ComponentLoaderFactory } from 'ngx-bootstrap/component-loader';\nimport { PositioningService } from 'ngx-bootstrap/positioning';\nimport { timer } from 'rxjs';\nimport { CommonModule } from '@angular/common';\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n/**\n * Default values provider for tooltip\n */\nclass TooltipConfig {\n    constructor() {\n        /**\n         * sets disable adaptive position\n         */\n        this.adaptivePosition = true;\n        /**\n         * tooltip placement, supported positions: 'top', 'bottom', 'left', 'right'\n         */\n        this.placement = 'top';\n        /**\n         * array of event names which triggers tooltip opening\n         */\n        this.triggers = 'hover focus';\n        /**\n         * delay before showing the tooltip\n         */\n        this.delay = 0;\n    }\n}\nTooltipConfig.decorators = [\n    { type: Injectable }\n];\nif (false) {\n    /**\n     * sets disable adaptive position\n     * @type {?}\n     */\n    TooltipConfig.prototype.adaptivePosition;\n    /**\n     * tooltip placement, supported positions: 'top', 'bottom', 'left', 'right'\n     * @type {?}\n     */\n    TooltipConfig.prototype.placement;\n    /**\n     * array of event names which triggers tooltip opening\n     * @type {?}\n     */\n    TooltipConfig.prototype.triggers;\n    /**\n     * a selector specifying the element the tooltip should be appended to.\n     * @type {?}\n     */\n    TooltipConfig.prototype.container;\n    /**\n     * delay before showing the tooltip\n     * @type {?}\n     */\n    TooltipConfig.prototype.delay;\n}\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\nclass TooltipContainerComponent {\n    /**\n     * @param {?} config\n     */\n    constructor(config) {\n        Object.assign(this, config);\n    }\n    /**\n     * @return {?}\n     */\n    get isBs3() {\n        return isBs3();\n    }\n    /**\n     * @return {?}\n     */\n    ngAfterViewInit() {\n        this.classMap = { in: false, fade: false };\n        this.classMap[this.placement] = true;\n        this.classMap[`tooltip-${this.placement}`] = true;\n        this.classMap.in = true;\n        if (this.animation) {\n            this.classMap.fade = true;\n        }\n        if (this.containerClass) {\n            this.classMap[this.containerClass] = true;\n        }\n    }\n}\nTooltipContainerComponent.decorators = [\n    { type: Component, args: [{\n                selector: 'bs-tooltip-container',\n                changeDetection: ChangeDetectionStrategy.OnPush,\n                // tslint:disable-next-line\n                host: {\n                    '[class]': '\"tooltip in tooltip-\" + placement + \" \" + \"bs-tooltip-\" + placement + \" \" + placement + \" \" + containerClass',\n                    '[class.show]': '!isBs3',\n                    '[class.bs3]': 'isBs3',\n                    '[attr.id]': 'this.id',\n                    role: 'tooltip'\n                },\n                template: `\n    <div class=\"tooltip-arrow arrow\"></div>\n    <div class=\"tooltip-inner\"><ng-content></ng-content></div>\n    `,\n                styles: [`\n    :host.tooltip {\n      display: block;\n      pointer-events: none;\n    }\n    :host.bs3.tooltip.top>.arrow {\n      margin-left: -2px;\n    }\n    :host.bs3.tooltip.bottom {\n      margin-top: 0px;\n    }\n    :host.bs3.bs-tooltip-left, :host.bs3.bs-tooltip-right{\n      margin: 0px;\n    }\n    :host.bs3.bs-tooltip-right .arrow, :host.bs3.bs-tooltip-left .arrow {\n      margin: .3rem 0;\n    }\n  `]\n            }] }\n];\n/** @nocollapse */\nTooltipContainerComponent.ctorParameters = () => [\n    { type: TooltipConfig }\n];\nif (false) {\n    /** @type {?} */\n    TooltipContainerComponent.prototype.classMap;\n    /** @type {?} */\n    TooltipContainerComponent.prototype.placement;\n    /** @type {?} */\n    TooltipContainerComponent.prototype.containerClass;\n    /** @type {?} */\n    TooltipContainerComponent.prototype.animation;\n    /** @type {?} */\n    TooltipContainerComponent.prototype.id;\n}\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n/** @type {?} */\nlet id = 0;\nclass TooltipDirective {\n    /**\n     * @param {?} _viewContainerRef\n     * @param {?} cis\n     * @param {?} config\n     * @param {?} _elementRef\n     * @param {?} _renderer\n     * @param {?} _positionService\n     */\n    constructor(_viewContainerRef, cis, config, _elementRef, _renderer, _positionService) {\n        this._elementRef = _elementRef;\n        this._renderer = _renderer;\n        this._positionService = _positionService;\n        this.tooltipId = id++;\n        /**\n         * Fired when tooltip content changes\n         */\n        /* tslint:disable-next-line:no-any */\n        this.tooltipChange = new EventEmitter();\n        /**\n         * Css class for tooltip container\n         */\n        this.containerClass = '';\n        /**\n         * @deprecated - removed, will be added to configuration\n         */\n        this.tooltipAnimation = true;\n        /**\n         * @deprecated\n         */\n        this.tooltipFadeDuration = 150;\n        /**\n         * @deprecated\n         */\n        this.tooltipStateChanged = new EventEmitter();\n        this._tooltip = cis\n            .createLoader(this._elementRef, _viewContainerRef, this._renderer)\n            .provide({ provide: TooltipConfig, useValue: config });\n        Object.assign(this, config);\n        this.onShown = this._tooltip.onShown;\n        this.onHidden = this._tooltip.onHidden;\n    }\n    /**\n     * Returns whether or not the tooltip is currently being shown\n     * @return {?}\n     */\n    get isOpen() {\n        return this._tooltip.isShown;\n    }\n    /**\n     * @param {?} value\n     * @return {?}\n     */\n    set isOpen(value) {\n        if (value) {\n            this.show();\n        }\n        else {\n            this.hide();\n        }\n    }\n    /**\n     * @deprecated - please use `tooltip` instead\n     * @param {?} value\n     * @return {?}\n     */\n    set htmlContent(value) {\n        warnOnce('tooltipHtml was deprecated, please use `tooltip` instead');\n        this.tooltip = value;\n    }\n    /**\n     * @deprecated - please use `placement` instead\n     * @param {?} value\n     * @return {?}\n     */\n    set _placement(value) {\n        warnOnce('tooltipPlacement was deprecated, please use `placement` instead');\n        this.placement = value;\n    }\n    /**\n     * @deprecated - please use `isOpen` instead\n     * @param {?} value\n     * @return {?}\n     */\n    set _isOpen(value) {\n        warnOnce('tooltipIsOpen was deprecated, please use `isOpen` instead');\n        this.isOpen = value;\n    }\n    /**\n     * @return {?}\n     */\n    get _isOpen() {\n        warnOnce('tooltipIsOpen was deprecated, please use `isOpen` instead');\n        return this.isOpen;\n    }\n    /**\n     * @deprecated - please use `isDisabled` instead\n     * @param {?} value\n     * @return {?}\n     */\n    set _enable(value) {\n        warnOnce('tooltipEnable was deprecated, please use `isDisabled` instead');\n        this.isDisabled = !value;\n    }\n    /**\n     * @return {?}\n     */\n    get _enable() {\n        warnOnce('tooltipEnable was deprecated, please use `isDisabled` instead');\n        return this.isDisabled;\n    }\n    /**\n     * @deprecated - please use `container=\"body\"` instead\n     * @param {?} value\n     * @return {?}\n     */\n    set _appendToBody(value) {\n        warnOnce('tooltipAppendToBody was deprecated, please use `container=\"body\"` instead');\n        this.container = value ? 'body' : this.container;\n    }\n    /**\n     * @return {?}\n     */\n    get _appendToBody() {\n        warnOnce('tooltipAppendToBody was deprecated, please use `container=\"body\"` instead');\n        return this.container === 'body';\n    }\n    /**\n     * @deprecated - will replaced with customClass\n     * @param {?} value\n     * @return {?}\n     */\n    set _popupClass(value) {\n        warnOnce('tooltipClass deprecated');\n    }\n    /**\n     * @deprecated - removed\n     * @param {?} value\n     * @return {?}\n     */\n    set _tooltipContext(value) {\n        warnOnce('tooltipContext deprecated');\n    }\n    /**\n     * @deprecated\n     * @param {?} value\n     * @return {?}\n     */\n    set _tooltipPopupDelay(value) {\n        warnOnce('tooltipPopupDelay is deprecated, use `delay` instead');\n        this.delay = value;\n    }\n    /**\n     * @deprecated -  please use `triggers` instead\n     * @return {?}\n     */\n    get _tooltipTrigger() {\n        warnOnce('tooltipTrigger was deprecated, please use `triggers` instead');\n        return this.triggers;\n    }\n    /**\n     * @param {?} value\n     * @return {?}\n     */\n    set _tooltipTrigger(value) {\n        warnOnce('tooltipTrigger was deprecated, please use `triggers` instead');\n        this.triggers = (value || '').toString();\n    }\n    /**\n     * @return {?}\n     */\n    ngOnInit() {\n        this._tooltip.listen({\n            triggers: this.triggers,\n            show: (/**\n             * @return {?}\n             */\n            () => this.show())\n        });\n        /* tslint:disable-next-line:no-any */\n        this.tooltipChange.subscribe((/**\n         * @param {?} value\n         * @return {?}\n         */\n        (value) => {\n            if (!value) {\n                this._tooltip.hide();\n            }\n        }));\n        this.onShown.subscribe((/**\n         * @return {?}\n         */\n        () => {\n            this.setAriaDescribedBy();\n        }));\n        this.onHidden.subscribe((/**\n         * @return {?}\n         */\n        () => {\n            this.setAriaDescribedBy();\n        }));\n    }\n    /**\n     * @return {?}\n     */\n    setAriaDescribedBy() {\n        this._ariaDescribedby = this.isOpen ? `tooltip-${this.tooltipId}` : null;\n        if (this._ariaDescribedby) {\n            this._renderer.setAttribute(this._elementRef.nativeElement, 'aria-describedby', this._ariaDescribedby);\n        }\n        else {\n            this._renderer.removeAttribute(this._elementRef.nativeElement, 'aria-describedby');\n        }\n    }\n    /**\n     * Toggles an element’s tooltip. This is considered a “manual” triggering of\n     * the tooltip.\n     * @return {?}\n     */\n    toggle() {\n        if (this.isOpen) {\n            return this.hide();\n        }\n        this.show();\n    }\n    /**\n     * Opens an element’s tooltip. This is considered a “manual” triggering of\n     * the tooltip.\n     * @return {?}\n     */\n    show() {\n        this._positionService.setOptions({\n            modifiers: {\n                flip: {\n                    enabled: this.adaptivePosition\n                },\n                preventOverflow: {\n                    enabled: this.adaptivePosition\n                }\n            }\n        });\n        if (this.isOpen ||\n            this.isDisabled ||\n            this._delayTimeoutId ||\n            !this.tooltip) {\n            return;\n        }\n        /** @type {?} */\n        const showTooltip = (/**\n         * @return {?}\n         */\n        () => {\n            if (this._delayTimeoutId) {\n                this._delayTimeoutId = undefined;\n            }\n            this._tooltip\n                .attach(TooltipContainerComponent)\n                .to(this.container)\n                .position({ attachment: this.placement })\n                .show({\n                content: this.tooltip,\n                placement: this.placement,\n                containerClass: this.containerClass,\n                id: `tooltip-${this.tooltipId}`\n            });\n        });\n        /** @type {?} */\n        const cancelDelayedTooltipShowing = (/**\n         * @return {?}\n         */\n        () => {\n            if (this._tooltipCancelShowFn) {\n                this._tooltipCancelShowFn();\n            }\n        });\n        if (this.delay) {\n            if (this._delaySubscription) {\n                this._delaySubscription.unsubscribe();\n            }\n            this._delaySubscription = timer(this.delay).subscribe((/**\n             * @return {?}\n             */\n            () => {\n                showTooltip();\n                cancelDelayedTooltipShowing();\n            }));\n            if (this.triggers) {\n                parseTriggers(this.triggers)\n                    .forEach((/**\n                 * @param {?} trigger\n                 * @return {?}\n                 */\n                (trigger) => {\n                    this._tooltipCancelShowFn = this._renderer.listen(this._elementRef.nativeElement, trigger.close, (/**\n                     * @return {?}\n                     */\n                    () => {\n                        this._delaySubscription.unsubscribe();\n                        cancelDelayedTooltipShowing();\n                    }));\n                }));\n            }\n        }\n        else {\n            showTooltip();\n        }\n    }\n    /**\n     * Closes an element’s tooltip. This is considered a “manual” triggering of\n     * the tooltip.\n     * @return {?}\n     */\n    hide() {\n        if (this._delayTimeoutId) {\n            clearTimeout(this._delayTimeoutId);\n            this._delayTimeoutId = undefined;\n        }\n        if (!this._tooltip.isShown) {\n            return;\n        }\n        this._tooltip.instance.classMap.in = false;\n        setTimeout((/**\n         * @return {?}\n         */\n        () => {\n            this._tooltip.hide();\n        }), this.tooltipFadeDuration);\n    }\n    /**\n     * @return {?}\n     */\n    ngOnDestroy() {\n        this._tooltip.dispose();\n        this.tooltipChange.unsubscribe();\n        if (this._delaySubscription) {\n            this._delaySubscription.unsubscribe();\n        }\n        this.onShown.unsubscribe();\n        this.onHidden.unsubscribe();\n    }\n}\nTooltipDirective.decorators = [\n    { type: Directive, args: [{\n                selector: '[tooltip], [tooltipHtml]',\n                exportAs: 'bs-tooltip'\n            },] }\n];\n/** @nocollapse */\nTooltipDirective.ctorParameters = () => [\n    { type: ViewContainerRef },\n    { type: ComponentLoaderFactory },\n    { type: TooltipConfig },\n    { type: ElementRef },\n    { type: Renderer2 },\n    { type: PositioningService }\n];\nTooltipDirective.propDecorators = {\n    adaptivePosition: [{ type: Input }],\n    tooltip: [{ type: Input }],\n    tooltipChange: [{ type: Output }],\n    placement: [{ type: Input }],\n    triggers: [{ type: Input }],\n    container: [{ type: Input }],\n    containerClass: [{ type: Input }],\n    isOpen: [{ type: Input }],\n    isDisabled: [{ type: Input }],\n    delay: [{ type: Input }],\n    onShown: [{ type: Output }],\n    onHidden: [{ type: Output }],\n    htmlContent: [{ type: Input, args: ['tooltipHtml',] }],\n    _placement: [{ type: Input, args: ['tooltipPlacement',] }],\n    _isOpen: [{ type: Input, args: ['tooltipIsOpen',] }],\n    _enable: [{ type: Input, args: ['tooltipEnable',] }],\n    _appendToBody: [{ type: Input, args: ['tooltipAppendToBody',] }],\n    tooltipAnimation: [{ type: Input }],\n    _popupClass: [{ type: Input, args: ['tooltipClass',] }],\n    _tooltipContext: [{ type: Input, args: ['tooltipContext',] }],\n    _tooltipPopupDelay: [{ type: Input, args: ['tooltipPopupDelay',] }],\n    tooltipFadeDuration: [{ type: Input }],\n    _tooltipTrigger: [{ type: Input, args: ['tooltipTrigger',] }],\n    tooltipStateChanged: [{ type: Output }]\n};\n__decorate([\n    OnChange(),\n    __metadata(\"design:type\", Object)\n], TooltipDirective.prototype, \"tooltip\", void 0);\nif (false) {\n    /** @type {?} */\n    TooltipDirective.prototype.tooltipId;\n    /**\n     * sets disable adaptive position\n     * @type {?}\n     */\n    TooltipDirective.prototype.adaptivePosition;\n    /**\n     * Content to be displayed as tooltip.\n     * @type {?}\n     */\n    TooltipDirective.prototype.tooltip;\n    /**\n     * Fired when tooltip content changes\n     * @type {?}\n     */\n    TooltipDirective.prototype.tooltipChange;\n    /**\n     * Placement of a tooltip. Accepts: \"top\", \"bottom\", \"left\", \"right\"\n     * @type {?}\n     */\n    TooltipDirective.prototype.placement;\n    /**\n     * Specifies events that should trigger. Supports a space separated list of\n     * event names.\n     * @type {?}\n     */\n    TooltipDirective.prototype.triggers;\n    /**\n     * A selector specifying the element the tooltip should be appended to.\n     * @type {?}\n     */\n    TooltipDirective.prototype.container;\n    /**\n     * Css class for tooltip container\n     * @type {?}\n     */\n    TooltipDirective.prototype.containerClass;\n    /**\n     * Allows to disable tooltip\n     * @type {?}\n     */\n    TooltipDirective.prototype.isDisabled;\n    /**\n     * Delay before showing the tooltip\n     * @type {?}\n     */\n    TooltipDirective.prototype.delay;\n    /**\n     * Emits an event when the tooltip is shown\n     * @type {?}\n     */\n    TooltipDirective.prototype.onShown;\n    /**\n     * Emits an event when the tooltip is hidden\n     * @type {?}\n     */\n    TooltipDirective.prototype.onHidden;\n    /**\n     * @deprecated - removed, will be added to configuration\n     * @type {?}\n     */\n    TooltipDirective.prototype.tooltipAnimation;\n    /**\n     * @deprecated\n     * @type {?}\n     */\n    TooltipDirective.prototype.tooltipFadeDuration;\n    /**\n     * @deprecated\n     * @type {?}\n     */\n    TooltipDirective.prototype.tooltipStateChanged;\n    /**\n     * @type {?}\n     * @protected\n     */\n    TooltipDirective.prototype._delayTimeoutId;\n    /**\n     * @type {?}\n     * @protected\n     */\n    TooltipDirective.prototype._tooltipCancelShowFn;\n    /**\n     * @type {?}\n     * @private\n     */\n    TooltipDirective.prototype._tooltip;\n    /**\n     * @type {?}\n     * @private\n     */\n    TooltipDirective.prototype._delaySubscription;\n    /**\n     * @type {?}\n     * @private\n     */\n    TooltipDirective.prototype._ariaDescribedby;\n    /**\n     * @type {?}\n     * @private\n     */\n    TooltipDirective.prototype._elementRef;\n    /**\n     * @type {?}\n     * @private\n     */\n    TooltipDirective.prototype._renderer;\n    /**\n     * @type {?}\n     * @private\n     */\n    TooltipDirective.prototype._positionService;\n}\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\nclass TooltipModule {\n    /**\n     * @return {?}\n     */\n    static forRoot() {\n        return {\n            ngModule: TooltipModule,\n            providers: [TooltipConfig, ComponentLoaderFactory, PositioningService]\n        };\n    }\n}\nTooltipModule.decorators = [\n    { type: NgModule, args: [{\n                imports: [CommonModule],\n                declarations: [TooltipDirective, TooltipContainerComponent],\n                exports: [TooltipDirective],\n                entryComponents: [TooltipContainerComponent]\n            },] }\n];\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\nexport { TooltipConfig, TooltipContainerComponent, TooltipDirective, TooltipModule };\n"], "mappings": "AAAA,SAASA,UAAU,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,YAAY,EAAEC,SAAS,EAAEC,gBAAgB,EAAEC,UAAU,EAAEC,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AACzK,SAASC,KAAK,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,QAAQ,QAAQ,qBAAqB;AAC9E,SAASC,UAAU,EAAEC,UAAU,QAAQ,OAAO;AAC9C,SAASC,sBAAsB,QAAQ,gCAAgC;AACvE,SAASC,kBAAkB,QAAQ,2BAA2B;AAC9D,SAASC,KAAK,QAAQ,MAAM;AAC5B,SAASC,YAAY,QAAQ,iBAAiB;;AAE9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa,CAAC;EAChBC,WAAWA,CAAA,EAAG;IACV;AACR;AACA;IACQ,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B;AACR;AACA;IACQ,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB;AACR;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,aAAa;IAC7B;AACR;AACA;IACQ,IAAI,CAACC,KAAK,GAAG,CAAC;EAClB;AACJ;AACAL,aAAa,CAACM,UAAU,GAAG,CACvB;EAAEC,IAAI,EAAE5B;AAAW,CAAC,CACvB;AACD,IAAI,KAAK,EAAE;EACP;AACJ;AACA;AACA;EACIqB,aAAa,CAACQ,SAAS,CAACN,gBAAgB;EACxC;AACJ;AACA;AACA;EACIF,aAAa,CAACQ,SAAS,CAACL,SAAS;EACjC;AACJ;AACA;AACA;EACIH,aAAa,CAACQ,SAAS,CAACJ,QAAQ;EAChC;AACJ;AACA;AACA;EACIJ,aAAa,CAACQ,SAAS,CAACC,SAAS;EACjC;AACJ;AACA;AACA;EACIT,aAAa,CAACQ,SAAS,CAACH,KAAK;AACjC;;AAEA;AACA;AACA;AACA;AACA,MAAMK,yBAAyB,CAAC;EAC5B;AACJ;AACA;EACIT,WAAWA,CAACU,MAAM,EAAE;IAChBC,MAAM,CAACC,MAAM,CAAC,IAAI,EAAEF,MAAM,CAAC;EAC/B;EACA;AACJ;AACA;EACI,IAAIrB,KAAKA,CAAA,EAAG;IACR,OAAOA,KAAK,CAAC,CAAC;EAClB;EACA;AACJ;AACA;EACIwB,eAAeA,CAAA,EAAG;IACd,IAAI,CAACC,QAAQ,GAAG;MAAEC,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE;IAAM,CAAC;IAC1C,IAAI,CAACF,QAAQ,CAAC,IAAI,CAACZ,SAAS,CAAC,GAAG,IAAI;IACpC,IAAI,CAACY,QAAQ,CAAC,WAAW,IAAI,CAACZ,SAAS,EAAE,CAAC,GAAG,IAAI;IACjD,IAAI,CAACY,QAAQ,CAACC,EAAE,GAAG,IAAI;IACvB,IAAI,IAAI,CAACE,SAAS,EAAE;MAChB,IAAI,CAACH,QAAQ,CAACE,IAAI,GAAG,IAAI;IAC7B;IACA,IAAI,IAAI,CAACE,cAAc,EAAE;MACrB,IAAI,CAACJ,QAAQ,CAAC,IAAI,CAACI,cAAc,CAAC,GAAG,IAAI;IAC7C;EACJ;AACJ;AACAT,yBAAyB,CAACJ,UAAU,GAAG,CACnC;EAAEC,IAAI,EAAE3B,SAAS;EAAEwC,IAAI,EAAE,CAAC;IACdC,QAAQ,EAAE,sBAAsB;IAChCC,eAAe,EAAEzC,uBAAuB,CAAC0C,MAAM;IAC/C;IACAC,IAAI,EAAE;MACF,SAAS,EAAE,8GAA8G;MACzH,cAAc,EAAE,QAAQ;MACxB,aAAa,EAAE,OAAO;MACtB,WAAW,EAAE,SAAS;MACtBC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;AAC1B;AACA;AACA,KAAK;IACWC,MAAM,EAAE,CAAC;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;EACS,CAAC;AAAE,CAAC,CACf;AACD;AACAjB,yBAAyB,CAACkB,cAAc,GAAG,MAAM,CAC7C;EAAErB,IAAI,EAAEP;AAAc,CAAC,CAC1B;AACD,IAAI,KAAK,EAAE;EACP;EACAU,yBAAyB,CAACF,SAAS,CAACO,QAAQ;EAC5C;EACAL,yBAAyB,CAACF,SAAS,CAACL,SAAS;EAC7C;EACAO,yBAAyB,CAACF,SAAS,CAACW,cAAc;EAClD;EACAT,yBAAyB,CAACF,SAAS,CAACU,SAAS;EAC7C;EACAR,yBAAyB,CAACF,SAAS,CAACqB,EAAE;AAC1C;;AAEA;AACA;AACA;AACA;AACA;AACA,IAAIA,EAAE,GAAG,CAAC;AACV,MAAMC,gBAAgB,CAAC;EACnB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI7B,WAAWA,CAAC8B,iBAAiB,EAAEC,GAAG,EAAErB,MAAM,EAAEsB,WAAW,EAAEC,SAAS,EAAEC,gBAAgB,EAAE;IAClF,IAAI,CAACF,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,SAAS,GAAGP,EAAE,EAAE;IACrB;AACR;AACA;IACQ;IACA,IAAI,CAACQ,aAAa,GAAG,IAAIvD,YAAY,CAAC,CAAC;IACvC;AACR;AACA;IACQ,IAAI,CAACqC,cAAc,GAAG,EAAE;IACxB;AACR;AACA;IACQ,IAAI,CAACmB,gBAAgB,GAAG,IAAI;IAC5B;AACR;AACA;IACQ,IAAI,CAACC,mBAAmB,GAAG,GAAG;IAC9B;AACR;AACA;IACQ,IAAI,CAACC,mBAAmB,GAAG,IAAI1D,YAAY,CAAC,CAAC;IAC7C,IAAI,CAAC2D,QAAQ,GAAGT,GAAG,CACdU,YAAY,CAAC,IAAI,CAACT,WAAW,EAAEF,iBAAiB,EAAE,IAAI,CAACG,SAAS,CAAC,CACjES,OAAO,CAAC;MAAEA,OAAO,EAAE3C,aAAa;MAAE4C,QAAQ,EAAEjC;IAAO,CAAC,CAAC;IAC1DC,MAAM,CAACC,MAAM,CAAC,IAAI,EAAEF,MAAM,CAAC;IAC3B,IAAI,CAACkC,OAAO,GAAG,IAAI,CAACJ,QAAQ,CAACI,OAAO;IACpC,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACL,QAAQ,CAACK,QAAQ;EAC1C;EACA;AACJ;AACA;AACA;EACI,IAAIC,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACN,QAAQ,CAACO,OAAO;EAChC;EACA;AACJ;AACA;AACA;EACI,IAAID,MAAMA,CAACE,KAAK,EAAE;IACd,IAAIA,KAAK,EAAE;MACP,IAAI,CAACC,IAAI,CAAC,CAAC;IACf,CAAC,MACI;MACD,IAAI,CAACC,IAAI,CAAC,CAAC;IACf;EACJ;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIC,WAAWA,CAACH,KAAK,EAAE;IACnB1D,QAAQ,CAAC,0DAA0D,CAAC;IACpE,IAAI,CAAC8D,OAAO,GAAGJ,KAAK;EACxB;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIK,UAAUA,CAACL,KAAK,EAAE;IAClB1D,QAAQ,CAAC,iEAAiE,CAAC;IAC3E,IAAI,CAACY,SAAS,GAAG8C,KAAK;EAC1B;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIM,OAAOA,CAACN,KAAK,EAAE;IACf1D,QAAQ,CAAC,2DAA2D,CAAC;IACrE,IAAI,CAACwD,MAAM,GAAGE,KAAK;EACvB;EACA;AACJ;AACA;EACI,IAAIM,OAAOA,CAAA,EAAG;IACVhE,QAAQ,CAAC,2DAA2D,CAAC;IACrE,OAAO,IAAI,CAACwD,MAAM;EACtB;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIS,OAAOA,CAACP,KAAK,EAAE;IACf1D,QAAQ,CAAC,+DAA+D,CAAC;IACzE,IAAI,CAACkE,UAAU,GAAG,CAACR,KAAK;EAC5B;EACA;AACJ;AACA;EACI,IAAIO,OAAOA,CAAA,EAAG;IACVjE,QAAQ,CAAC,+DAA+D,CAAC;IACzE,OAAO,IAAI,CAACkE,UAAU;EAC1B;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIC,aAAaA,CAACT,KAAK,EAAE;IACrB1D,QAAQ,CAAC,2EAA2E,CAAC;IACrF,IAAI,CAACkB,SAAS,GAAGwC,KAAK,GAAG,MAAM,GAAG,IAAI,CAACxC,SAAS;EACpD;EACA;AACJ;AACA;EACI,IAAIiD,aAAaA,CAAA,EAAG;IAChBnE,QAAQ,CAAC,2EAA2E,CAAC;IACrF,OAAO,IAAI,CAACkB,SAAS,KAAK,MAAM;EACpC;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIkD,WAAWA,CAACV,KAAK,EAAE;IACnB1D,QAAQ,CAAC,yBAAyB,CAAC;EACvC;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIqE,eAAeA,CAACX,KAAK,EAAE;IACvB1D,QAAQ,CAAC,2BAA2B,CAAC;EACzC;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIsE,kBAAkBA,CAACZ,KAAK,EAAE;IAC1B1D,QAAQ,CAAC,sDAAsD,CAAC;IAChE,IAAI,CAACc,KAAK,GAAG4C,KAAK;EACtB;EACA;AACJ;AACA;AACA;EACI,IAAIa,eAAeA,CAAA,EAAG;IAClBvE,QAAQ,CAAC,8DAA8D,CAAC;IACxE,OAAO,IAAI,CAACa,QAAQ;EACxB;EACA;AACJ;AACA;AACA;EACI,IAAI0D,eAAeA,CAACb,KAAK,EAAE;IACvB1D,QAAQ,CAAC,8DAA8D,CAAC;IACxE,IAAI,CAACa,QAAQ,GAAG,CAAC6C,KAAK,IAAI,EAAE,EAAEc,QAAQ,CAAC,CAAC;EAC5C;EACA;AACJ;AACA;EACIC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACvB,QAAQ,CAACwB,MAAM,CAAC;MACjB7D,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvB8C,IAAI;MAAG;AACnB;AACA;MACYA,CAAA,KAAM,IAAI,CAACA,IAAI,CAAC,CAAC;IACrB,CAAC,CAAC;IACF;IACA,IAAI,CAACb,aAAa,CAAC6B,SAAS;IAAE;AACtC;AACA;AACA;IACSjB,KAAK,IAAK;MACP,IAAI,CAACA,KAAK,EAAE;QACR,IAAI,CAACR,QAAQ,CAACU,IAAI,CAAC,CAAC;MACxB;IACJ,CAAE,CAAC;IACH,IAAI,CAACN,OAAO,CAACqB,SAAS;IAAE;AAChC;AACA;IACQ,MAAM;MACF,IAAI,CAACC,kBAAkB,CAAC,CAAC;IAC7B,CAAE,CAAC;IACH,IAAI,CAACrB,QAAQ,CAACoB,SAAS;IAAE;AACjC;AACA;IACQ,MAAM;MACF,IAAI,CAACC,kBAAkB,CAAC,CAAC;IAC7B,CAAE,CAAC;EACP;EACA;AACJ;AACA;EACIA,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACrB,MAAM,GAAG,WAAW,IAAI,CAACX,SAAS,EAAE,GAAG,IAAI;IACxE,IAAI,IAAI,CAACgC,gBAAgB,EAAE;MACvB,IAAI,CAAClC,SAAS,CAACmC,YAAY,CAAC,IAAI,CAACpC,WAAW,CAACqC,aAAa,EAAE,kBAAkB,EAAE,IAAI,CAACF,gBAAgB,CAAC;IAC1G,CAAC,MACI;MACD,IAAI,CAAClC,SAAS,CAACqC,eAAe,CAAC,IAAI,CAACtC,WAAW,CAACqC,aAAa,EAAE,kBAAkB,CAAC;IACtF;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIE,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAACzB,MAAM,EAAE;MACb,OAAO,IAAI,CAACI,IAAI,CAAC,CAAC;IACtB;IACA,IAAI,CAACD,IAAI,CAAC,CAAC;EACf;EACA;AACJ;AACA;AACA;AACA;EACIA,IAAIA,CAAA,EAAG;IACH,IAAI,CAACf,gBAAgB,CAACsC,UAAU,CAAC;MAC7BC,SAAS,EAAE;QACPC,IAAI,EAAE;UACFC,OAAO,EAAE,IAAI,CAAC1E;QAClB,CAAC;QACD2E,eAAe,EAAE;UACbD,OAAO,EAAE,IAAI,CAAC1E;QAClB;MACJ;IACJ,CAAC,CAAC;IACF,IAAI,IAAI,CAAC6C,MAAM,IACX,IAAI,CAACU,UAAU,IACf,IAAI,CAACqB,eAAe,IACpB,CAAC,IAAI,CAACzB,OAAO,EAAE;MACf;IACJ;IACA;IACA,MAAM0B,WAAW;IAAI;AAC7B;AACA;IACQA,CAAA,KAAM;MACF,IAAI,IAAI,CAACD,eAAe,EAAE;QACtB,IAAI,CAACA,eAAe,GAAGE,SAAS;MACpC;MACA,IAAI,CAACvC,QAAQ,CACRwC,MAAM,CAACvE,yBAAyB,CAAC,CACjCwE,EAAE,CAAC,IAAI,CAACzE,SAAS,CAAC,CAClB0E,QAAQ,CAAC;QAAEC,UAAU,EAAE,IAAI,CAACjF;MAAU,CAAC,CAAC,CACxC+C,IAAI,CAAC;QACNmC,OAAO,EAAE,IAAI,CAAChC,OAAO;QACrBlD,SAAS,EAAE,IAAI,CAACA,SAAS;QACzBgB,cAAc,EAAE,IAAI,CAACA,cAAc;QACnCU,EAAE,EAAE,WAAW,IAAI,CAACO,SAAS;MACjC,CAAC,CAAC;IACN,CAAE;IACF;IACA,MAAMkD,2BAA2B;IAAI;AAC7C;AACA;IACQA,CAAA,KAAM;MACF,IAAI,IAAI,CAACC,oBAAoB,EAAE;QAC3B,IAAI,CAACA,oBAAoB,CAAC,CAAC;MAC/B;IACJ,CAAE;IACF,IAAI,IAAI,CAAClF,KAAK,EAAE;MACZ,IAAI,IAAI,CAACmF,kBAAkB,EAAE;QACzB,IAAI,CAACA,kBAAkB,CAACC,WAAW,CAAC,CAAC;MACzC;MACA,IAAI,CAACD,kBAAkB,GAAG1F,KAAK,CAAC,IAAI,CAACO,KAAK,CAAC,CAAC6D,SAAS;MAAE;AACnE;AACA;MACY,MAAM;QACFa,WAAW,CAAC,CAAC;QACbO,2BAA2B,CAAC,CAAC;MACjC,CAAE,CAAC;MACH,IAAI,IAAI,CAAClF,QAAQ,EAAE;QACfZ,aAAa,CAAC,IAAI,CAACY,QAAQ,CAAC,CACvBsF,OAAO;QAAE;AAC9B;AACA;AACA;QACiBC,OAAO,IAAK;UACT,IAAI,CAACJ,oBAAoB,GAAG,IAAI,CAACrD,SAAS,CAAC+B,MAAM,CAAC,IAAI,CAAChC,WAAW,CAACqC,aAAa,EAAEqB,OAAO,CAACC,KAAK;UAAG;AACtH;AACA;UACoB,MAAM;YACF,IAAI,CAACJ,kBAAkB,CAACC,WAAW,CAAC,CAAC;YACrCH,2BAA2B,CAAC,CAAC;UACjC,CAAE,CAAC;QACP,CAAE,CAAC;MACP;IACJ,CAAC,MACI;MACDP,WAAW,CAAC,CAAC;IACjB;EACJ;EACA;AACJ;AACA;AACA;AACA;EACI5B,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAAC2B,eAAe,EAAE;MACtBe,YAAY,CAAC,IAAI,CAACf,eAAe,CAAC;MAClC,IAAI,CAACA,eAAe,GAAGE,SAAS;IACpC;IACA,IAAI,CAAC,IAAI,CAACvC,QAAQ,CAACO,OAAO,EAAE;MACxB;IACJ;IACA,IAAI,CAACP,QAAQ,CAACqD,QAAQ,CAAC/E,QAAQ,CAACC,EAAE,GAAG,KAAK;IAC1C+E,UAAU;IAAE;AACpB;AACA;IACQ,MAAM;MACF,IAAI,CAACtD,QAAQ,CAACU,IAAI,CAAC,CAAC;IACxB,CAAC,EAAG,IAAI,CAACZ,mBAAmB,CAAC;EACjC;EACA;AACJ;AACA;EACIyD,WAAWA,CAAA,EAAG;IACV,IAAI,CAACvD,QAAQ,CAACwD,OAAO,CAAC,CAAC;IACvB,IAAI,CAAC5D,aAAa,CAACoD,WAAW,CAAC,CAAC;IAChC,IAAI,IAAI,CAACD,kBAAkB,EAAE;MACzB,IAAI,CAACA,kBAAkB,CAACC,WAAW,CAAC,CAAC;IACzC;IACA,IAAI,CAAC5C,OAAO,CAAC4C,WAAW,CAAC,CAAC;IAC1B,IAAI,CAAC3C,QAAQ,CAAC2C,WAAW,CAAC,CAAC;EAC/B;AACJ;AACA3D,gBAAgB,CAACxB,UAAU,GAAG,CAC1B;EAAEC,IAAI,EAAExB,SAAS;EAAEqC,IAAI,EAAE,CAAC;IACdC,QAAQ,EAAE,0BAA0B;IACpC6E,QAAQ,EAAE;EACd,CAAC;AAAG,CAAC,CAChB;AACD;AACApE,gBAAgB,CAACF,cAAc,GAAG,MAAM,CACpC;EAAErB,IAAI,EAAEvB;AAAiB,CAAC,EAC1B;EAAEuB,IAAI,EAAEX;AAAuB,CAAC,EAChC;EAAEW,IAAI,EAAEP;AAAc,CAAC,EACvB;EAAEO,IAAI,EAAEtB;AAAW,CAAC,EACpB;EAAEsB,IAAI,EAAErB;AAAU,CAAC,EACnB;EAAEqB,IAAI,EAAEV;AAAmB,CAAC,CAC/B;AACDiC,gBAAgB,CAACqE,cAAc,GAAG;EAC9BjG,gBAAgB,EAAE,CAAC;IAAEK,IAAI,EAAEpB;EAAM,CAAC,CAAC;EACnCkE,OAAO,EAAE,CAAC;IAAE9C,IAAI,EAAEpB;EAAM,CAAC,CAAC;EAC1BkD,aAAa,EAAE,CAAC;IAAE9B,IAAI,EAAEnB;EAAO,CAAC,CAAC;EACjCe,SAAS,EAAE,CAAC;IAAEI,IAAI,EAAEpB;EAAM,CAAC,CAAC;EAC5BiB,QAAQ,EAAE,CAAC;IAAEG,IAAI,EAAEpB;EAAM,CAAC,CAAC;EAC3BsB,SAAS,EAAE,CAAC;IAAEF,IAAI,EAAEpB;EAAM,CAAC,CAAC;EAC5BgC,cAAc,EAAE,CAAC;IAAEZ,IAAI,EAAEpB;EAAM,CAAC,CAAC;EACjC4D,MAAM,EAAE,CAAC;IAAExC,IAAI,EAAEpB;EAAM,CAAC,CAAC;EACzBsE,UAAU,EAAE,CAAC;IAAElD,IAAI,EAAEpB;EAAM,CAAC,CAAC;EAC7BkB,KAAK,EAAE,CAAC;IAAEE,IAAI,EAAEpB;EAAM,CAAC,CAAC;EACxB0D,OAAO,EAAE,CAAC;IAAEtC,IAAI,EAAEnB;EAAO,CAAC,CAAC;EAC3B0D,QAAQ,EAAE,CAAC;IAAEvC,IAAI,EAAEnB;EAAO,CAAC,CAAC;EAC5BgE,WAAW,EAAE,CAAC;IAAE7C,IAAI,EAAEpB,KAAK;IAAEiC,IAAI,EAAE,CAAC,aAAa;EAAG,CAAC,CAAC;EACtDkC,UAAU,EAAE,CAAC;IAAE/C,IAAI,EAAEpB,KAAK;IAAEiC,IAAI,EAAE,CAAC,kBAAkB;EAAG,CAAC,CAAC;EAC1DmC,OAAO,EAAE,CAAC;IAAEhD,IAAI,EAAEpB,KAAK;IAAEiC,IAAI,EAAE,CAAC,eAAe;EAAG,CAAC,CAAC;EACpDoC,OAAO,EAAE,CAAC;IAAEjD,IAAI,EAAEpB,KAAK;IAAEiC,IAAI,EAAE,CAAC,eAAe;EAAG,CAAC,CAAC;EACpDsC,aAAa,EAAE,CAAC;IAAEnD,IAAI,EAAEpB,KAAK;IAAEiC,IAAI,EAAE,CAAC,qBAAqB;EAAG,CAAC,CAAC;EAChEkB,gBAAgB,EAAE,CAAC;IAAE/B,IAAI,EAAEpB;EAAM,CAAC,CAAC;EACnCwE,WAAW,EAAE,CAAC;IAAEpD,IAAI,EAAEpB,KAAK;IAAEiC,IAAI,EAAE,CAAC,cAAc;EAAG,CAAC,CAAC;EACvDwC,eAAe,EAAE,CAAC;IAAErD,IAAI,EAAEpB,KAAK;IAAEiC,IAAI,EAAE,CAAC,gBAAgB;EAAG,CAAC,CAAC;EAC7DyC,kBAAkB,EAAE,CAAC;IAAEtD,IAAI,EAAEpB,KAAK;IAAEiC,IAAI,EAAE,CAAC,mBAAmB;EAAG,CAAC,CAAC;EACnEmB,mBAAmB,EAAE,CAAC;IAAEhC,IAAI,EAAEpB;EAAM,CAAC,CAAC;EACtC2E,eAAe,EAAE,CAAC;IAAEvD,IAAI,EAAEpB,KAAK;IAAEiC,IAAI,EAAE,CAAC,gBAAgB;EAAG,CAAC,CAAC;EAC7DoB,mBAAmB,EAAE,CAAC;IAAEjC,IAAI,EAAEnB;EAAO,CAAC;AAC1C,CAAC;AACDM,UAAU,CAAC,CACPD,QAAQ,CAAC,CAAC,EACVE,UAAU,CAAC,aAAa,EAAEiB,MAAM,CAAC,CACpC,EAAEkB,gBAAgB,CAACtB,SAAS,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;AACjD,IAAI,KAAK,EAAE;EACP;EACAsB,gBAAgB,CAACtB,SAAS,CAAC4B,SAAS;EACpC;AACJ;AACA;AACA;EACIN,gBAAgB,CAACtB,SAAS,CAACN,gBAAgB;EAC3C;AACJ;AACA;AACA;EACI4B,gBAAgB,CAACtB,SAAS,CAAC6C,OAAO;EAClC;AACJ;AACA;AACA;EACIvB,gBAAgB,CAACtB,SAAS,CAAC6B,aAAa;EACxC;AACJ;AACA;AACA;EACIP,gBAAgB,CAACtB,SAAS,CAACL,SAAS;EACpC;AACJ;AACA;AACA;AACA;EACI2B,gBAAgB,CAACtB,SAAS,CAACJ,QAAQ;EACnC;AACJ;AACA;AACA;EACI0B,gBAAgB,CAACtB,SAAS,CAACC,SAAS;EACpC;AACJ;AACA;AACA;EACIqB,gBAAgB,CAACtB,SAAS,CAACW,cAAc;EACzC;AACJ;AACA;AACA;EACIW,gBAAgB,CAACtB,SAAS,CAACiD,UAAU;EACrC;AACJ;AACA;AACA;EACI3B,gBAAgB,CAACtB,SAAS,CAACH,KAAK;EAChC;AACJ;AACA;AACA;EACIyB,gBAAgB,CAACtB,SAAS,CAACqC,OAAO;EAClC;AACJ;AACA;AACA;EACIf,gBAAgB,CAACtB,SAAS,CAACsC,QAAQ;EACnC;AACJ;AACA;AACA;EACIhB,gBAAgB,CAACtB,SAAS,CAAC8B,gBAAgB;EAC3C;AACJ;AACA;AACA;EACIR,gBAAgB,CAACtB,SAAS,CAAC+B,mBAAmB;EAC9C;AACJ;AACA;AACA;EACIT,gBAAgB,CAACtB,SAAS,CAACgC,mBAAmB;EAC9C;AACJ;AACA;AACA;EACIV,gBAAgB,CAACtB,SAAS,CAACsE,eAAe;EAC1C;AACJ;AACA;AACA;EACIhD,gBAAgB,CAACtB,SAAS,CAAC+E,oBAAoB;EAC/C;AACJ;AACA;AACA;EACIzD,gBAAgB,CAACtB,SAAS,CAACiC,QAAQ;EACnC;AACJ;AACA;AACA;EACIX,gBAAgB,CAACtB,SAAS,CAACgF,kBAAkB;EAC7C;AACJ;AACA;AACA;EACI1D,gBAAgB,CAACtB,SAAS,CAAC4D,gBAAgB;EAC3C;AACJ;AACA;AACA;EACItC,gBAAgB,CAACtB,SAAS,CAACyB,WAAW;EACtC;AACJ;AACA;AACA;EACIH,gBAAgB,CAACtB,SAAS,CAAC0B,SAAS;EACpC;AACJ;AACA;AACA;EACIJ,gBAAgB,CAACtB,SAAS,CAAC2B,gBAAgB;AAC/C;;AAEA;AACA;AACA;AACA;AACA,MAAMiE,aAAa,CAAC;EAChB;AACJ;AACA;EACI,OAAOC,OAAOA,CAAA,EAAG;IACb,OAAO;MACHC,QAAQ,EAAEF,aAAa;MACvBG,SAAS,EAAE,CAACvG,aAAa,EAAEJ,sBAAsB,EAAEC,kBAAkB;IACzE,CAAC;EACL;AACJ;AACAuG,aAAa,CAAC9F,UAAU,GAAG,CACvB;EAAEC,IAAI,EAAElB,QAAQ;EAAE+B,IAAI,EAAE,CAAC;IACboF,OAAO,EAAE,CAACzG,YAAY,CAAC;IACvB0G,YAAY,EAAE,CAAC3E,gBAAgB,EAAEpB,yBAAyB,CAAC;IAC3DgG,OAAO,EAAE,CAAC5E,gBAAgB,CAAC;IAC3B6E,eAAe,EAAE,CAACjG,yBAAyB;EAC/C,CAAC;AAAG,CAAC,CAChB;;AAED;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA,SAASV,aAAa,EAAEU,yBAAyB,EAAEoB,gBAAgB,EAAEsE,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}