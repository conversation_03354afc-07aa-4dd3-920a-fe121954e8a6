{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { SpinListActionsComponent } from './spin-list-actions.component';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatIconModule } from '@angular/material/icon';\nimport { ClipboardModule } from '../../../../../../common/components/clipboard/clipboard.module';\nimport { TranslateModule } from '@ngx-translate/core';\nimport * as i0 from \"@angular/core\";\nexport class SpinListActionsModule {\n  static {\n    this.ɵfac = function SpinListActionsModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SpinListActionsModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: SpinListActionsModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, MatButtonModule, MatMenuModule, MatIconModule, ClipboardModule, TranslateModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(SpinListActionsModule, {\n    declarations: [SpinListActionsComponent],\n    imports: [CommonModule, MatButtonModule, MatMenuModule, MatIconModule, ClipboardModule, TranslateModule],\n    exports: [SpinListActionsComponent]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "SpinListActionsComponent", "MatButtonModule", "MatMenuModule", "MatIconModule", "ClipboardModule", "TranslateModule", "SpinListActionsModule", "declarations", "imports", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/gamehistory/components/internal-game-history/spin-list/spin-list-actions/spin-list-actions.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { SpinListActionsComponent } from './spin-list-actions.component';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatIconModule } from '@angular/material/icon';\nimport { ClipboardModule } from '../../../../../../common/components/clipboard/clipboard.module';\nimport { TranslateModule } from '@ngx-translate/core';\n\n\n@NgModule({\n  declarations: [\n    SpinListActionsComponent\n  ],\n  exports: [\n    SpinListActionsComponent\n  ],\n  imports: [\n    CommonModule,\n    MatButtonModule,\n    MatMenuModule,\n    MatIconModule,\n    ClipboardModule,\n    TranslateModule,\n  ]\n})\nexport class SpinListActionsModule {\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,wBAAwB,QAAQ,+BAA+B;AACxE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,gEAAgE;AAChG,SAASC,eAAe,QAAQ,qBAAqB;;AAmBrD,OAAM,MAAOC,qBAAqB;;;uCAArBA,qBAAqB;IAAA;EAAA;;;YAArBA;IAAqB;EAAA;;;gBAR9BP,YAAY,EACZE,eAAe,EACfC,aAAa,EACbC,aAAa,EACbC,eAAe,EACfC,eAAe;IAAA;EAAA;;;2EAGNC,qBAAqB;IAAAC,YAAA,GAd9BP,wBAAwB;IAAAQ,OAAA,GAMxBT,YAAY,EACZE,eAAe,EACfC,aAAa,EACbC,aAAa,EACbC,eAAe,EACfC,eAAe;IAAAI,OAAA,GARfT,wBAAwB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}