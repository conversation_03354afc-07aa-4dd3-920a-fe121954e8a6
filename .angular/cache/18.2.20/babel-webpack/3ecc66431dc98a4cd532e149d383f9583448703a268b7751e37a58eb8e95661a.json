{"ast": null, "code": "import { FormControl, FormGroup } from '@angular/forms';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { ValidationService } from '../../../../../../../common/services/validation.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../lobby-menu-item-preview.service\";\nimport * as i3 from \"../../../../../../../common/services/cdn.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"../../../lobby-menu-items-ribbons/lobby-menu-items-ribbons.component\";\nimport * as i6 from \"@angular/material/form-field\";\nimport * as i7 from \"@angular/material/input\";\nimport * as i8 from \"@skywind-group/lib-swui\";\nimport * as i9 from \"@ngx-translate/core\";\nfunction LobbyMenuItemsPreviewFormComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"Category: \", ctx_r0.gameCategory, \"\");\n  }\n}\nfunction LobbyMenuItemsPreviewFormComponent_div_3_img_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 18);\n  }\n  if (rf & 2) {\n    const game_r2 = i0.ɵɵnextContext().$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"src\", ctx_r0.getGameImageUrl(game_r2.code), i0.ɵɵsanitizeUrl)(\"alt\", game_r2.title);\n  }\n}\nfunction LobbyMenuItemsPreviewFormComponent_div_3_img_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 19);\n  }\n  if (rf & 2) {\n    const game_r2 = i0.ɵɵnextContext().$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"src\", ctx_r0.getOverlayUrl(game_r2), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction LobbyMenuItemsPreviewFormComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"div\", 7)(2, \"div\", 8)(3, \"div\", 9);\n    i0.ɵɵtemplate(4, LobbyMenuItemsPreviewFormComponent_div_3_img_4_Template, 1, 2, \"img\", 10)(5, LobbyMenuItemsPreviewFormComponent_div_3_img_5_Template, 1, 1, \"img\", 11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 12)(7, \"div\", 13);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(9, \"lobby-menu-items-ribbons\", 14);\n    i0.ɵɵelementStart(10, \"mat-form-field\", 15)(11, \"mat-label\");\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(14, \"input\", 16);\n    i0.ɵɵpipe(15, \"translate\");\n    i0.ɵɵelementStart(16, \"mat-error\");\n    i0.ɵɵelement(17, \"lib-swui-control-messages\", 17);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const game_r2 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.getGameImageUrl(game_r2.code));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.getOverlayUrl(game_r2));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", game_r2.title, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formControl\", ctx_r0.getRibbonControl(game_r2.code))(\"ribbons\", ctx_r0.defaultRibbons)(\"maxLength\", 18);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(13, 11, \"LOBBY.MENU_ITEMS.overlayUrl\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"formControl\", ctx_r0.getOverlayUrlControl(game_r2.code))(\"placeholder\", i0.ɵɵpipeBind1(15, 13, \"LOBBY.MENU_ITEMS.overlayUrl\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"messages\", ctx_r0.errorMessages)(\"control\", ctx_r0.getOverlayUrlControl(game_r2.code));\n  }\n}\nfunction LobbyMenuItemsPreviewFormComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20)(1, \"div\", 21);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"LOBBY.MENU_ITEMS.empty\"), \" \");\n  }\n}\nexport class LobbyMenuItemsPreviewFormComponent {\n  constructor(fb, lobbyMenuItemPreviewService, cdnService, cdr) {\n    this.fb = fb;\n    this.lobbyMenuItemPreviewService = lobbyMenuItemPreviewService;\n    this.cdnService = cdnService;\n    this.cdr = cdr;\n    this.images = {};\n    this.defaultRibbons = [{\n      text: 'Hot',\n      bg: 'linear-gradient(270deg, #EC203300 0%, #EC2033 50%, #EC2033 100%)',\n      color: '#FFFFFF'\n    }, {\n      text: 'New',\n      bg: 'linear-gradient(270deg, #FECB0100 0%, #FECB01 51%, #FECB01 100%)',\n      color: '#000000'\n    }, {\n      text: 'Exclusive',\n      bg: 'linear-gradient(270deg, #1B76EE00 0%, #1B76EE 51%, #1B76EE 100%)',\n      color: '#FFFFFF'\n    }];\n    this.games = [];\n    this.errorMessages = {\n      urlIsNotCorrect: 'VALIDATION.urlIsNotCorrect'\n    };\n    this.destroyed$ = new Subject();\n    this.lobbyMenuItemPreviewService.getGames().pipe(takeUntil(this.destroyed$)).subscribe(val => {\n      this.games = val;\n      this.cdr.markForCheck();\n    });\n    this.form = this.initForm();\n  }\n  ngOnInit() {\n    this.form.valueChanges.pipe(takeUntil(this.destroyed$)).subscribe(() => {\n      this.cdr.detectChanges();\n    });\n    this.cdnService.gameImages.pipe(takeUntil(this.destroyed$)).subscribe(images => {\n      this.images = images;\n      this.cdr.detectChanges();\n    });\n  }\n  ngOnDestroy() {\n    this.destroyed$.next();\n    this.destroyed$.complete();\n  }\n  initForm(val) {\n    const group = this.fb.group({\n      id: ['']\n    });\n    this.games.forEach(({\n      code\n    }) => {\n      group.setControl(code, new FormGroup({\n        ribbon: new FormControl(),\n        overlayUrl: new FormControl(null, ValidationService.urlValidation)\n      }));\n    });\n    if (val) {\n      group.patchValue(val);\n    }\n    return group;\n  }\n  setValue(val) {\n    if (val) {\n      this.form.patchValue(val);\n    }\n  }\n  getOverlayUrl(game) {\n    return this.getOverlayUrlControl(game.code).value || game?.defaultInfo?.images?.overlay;\n  }\n  get valueChanges() {\n    return this.form.valueChanges;\n  }\n  get statusChanges() {\n    return this.form.statusChanges;\n  }\n  get invalid() {\n    return this.form.invalid;\n  }\n  getRibbonControl(name) {\n    return this.form.get(name).get('ribbon');\n  }\n  getOverlayUrlControl(name) {\n    return this.form.get(name).get('overlayUrl');\n  }\n  patchValue(values, options) {\n    Object.entries(values).forEach(([code, value]) => {\n      if (code in this.form.controls) {\n        this.form.get(code).patchValue(value, options);\n      } else {\n        this.form.setControl(code, new FormControl(value));\n      }\n    });\n    this.form.markAsPristine();\n    this.form.markAsUntouched();\n  }\n  getGameImageUrl(gameId) {\n    return this.images[gameId];\n  }\n  static {\n    this.ɵfac = function LobbyMenuItemsPreviewFormComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LobbyMenuItemsPreviewFormComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.LobbyMenuItemPreviewService), i0.ɵɵdirectiveInject(i3.CdnService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LobbyMenuItemsPreviewFormComponent,\n      selectors: [[\"lobby-menu-items-preview-form\"]],\n      inputs: {\n        gameCategory: \"gameCategory\"\n      },\n      decls: 5,\n      vars: 4,\n      consts: [[1, \"lobby-games\", 3, \"formGroup\"], [\"class\", \"lobby-games__header\", 4, \"ngIf\"], [1, \"lobby-games__body\"], [\"class\", \"lobby-games__item\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"lobby-games__message\", 4, \"ngIf\"], [1, \"lobby-games__header\"], [1, \"lobby-games__item\"], [1, \"lobby-thumb\"], [1, \"lobby-thumb__wrapper\"], [1, \"lobby-thumb__image\"], [3, \"src\", \"alt\", 4, \"ngIf\"], [\"class\", \"lobby-thumb__overlay-img\", 3, \"src\", 4, \"ngIf\"], [1, \"lobby-thumb__caption\"], [1, \"lobby-thumb__title\"], [3, \"formControl\", \"ribbons\", \"maxLength\"], [\"appearance\", \"outline\", 1, \"width100\"], [\"matInput\", \"\", \"trimValue\", \"\", \"autocomplete\", \"off\", 3, \"formControl\", \"placeholder\"], [3, \"messages\", \"control\"], [3, \"src\", \"alt\"], [1, \"lobby-thumb__overlay-img\", 3, \"src\"], [1, \"lobby-games__message\"], [1, \"alert\", \"alert-danger\"]],\n      template: function LobbyMenuItemsPreviewFormComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, LobbyMenuItemsPreviewFormComponent_div_1_Template, 2, 1, \"div\", 1);\n          i0.ɵɵelementStart(2, \"div\", 2);\n          i0.ɵɵtemplate(3, LobbyMenuItemsPreviewFormComponent_div_3_Template, 18, 15, \"div\", 3)(4, LobbyMenuItemsPreviewFormComponent_div_4_Template, 4, 3, \"div\", 4);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"formGroup\", ctx.form);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.gameCategory);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.games);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", (ctx.games == null ? null : ctx.games.length) === 0);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormControlDirective, i1.FormGroupDirective, i5.LobbyMenuItemsRibbonsComponent, i6.MatFormField, i6.MatLabel, i6.MatError, i7.MatInput, i8.SwuiControlMessagesComponent, i9.TranslatePipe],\n      styles: [\".lobby-games__header[_ngcontent-%COMP%] {\\n  height: 50px;\\n}\\n.lobby-games__body[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  flex-wrap: wrap;\\n  height: calc(100% - 50px);\\n  margin: 0 -10px;\\n  overflow: auto;\\n}\\n.lobby-games__item[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin: 10px;\\n}\\n@media (min-width: 1100px) {\\n  .lobby-games__item[_ngcontent-%COMP%] {\\n    width: calc(50% - 20px);\\n  }\\n}\\n@media (min-width: 1500px) {\\n  .lobby-games__item[_ngcontent-%COMP%] {\\n    width: calc(33.3333333333% - 20px);\\n  }\\n}\\n@media (min-width: 1981px) {\\n  .lobby-games__item[_ngcontent-%COMP%] {\\n    width: calc(25% - 20px);\\n  }\\n}\\n.lobby-games__loading[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 100%;\\n  height: 100%;\\n  min-height: 550px;\\n}\\n.lobby-games__message[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 20px 10px;\\n}\\n\\n.lobby-thumb__wrapper[_ngcontent-%COMP%] {\\n  position: relative;\\n  padding-top: 62.75%;\\n  overflow: hidden;\\n  border-top-right-radius: 3px;\\n  border-top-left-radius: 3px;\\n}\\n.lobby-thumb__image[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 100%;\\n  height: 100%;\\n  background-color: #393939;\\n  background-image: url(\\\"data:image/png;base64,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\\\");\\n  background-repeat: no-repeat;\\n  background-size: contain;\\n  background-position: center;\\n}\\n.lobby-thumb__image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  display: block;\\n  width: 100%;\\n}\\n.lobby-thumb__caption[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  height: 30px;\\n  width: 100%;\\n  margin-bottom: 10px;\\n  padding: 0 10px;\\n  border: 1px solid #ddd;\\n  border-top: none;\\n  border-bottom-left-radius: 3px;\\n  border-bottom-right-radius: 3px;\\n}\\n.lobby-thumb__title[_ngcontent-%COMP%] {\\n  width: 100%;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n.lobby-thumb__overlay-img[_ngcontent-%COMP%] {\\n  position: absolute;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      changeDetection: 0\n    });\n  }\n}", "map": {"version": 3, "names": ["FormControl", "FormGroup", "Subject", "takeUntil", "ValidationService", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "gameCategory", "ɵɵelement", "ɵɵproperty", "getGameImageUrl", "game_r2", "code", "ɵɵsanitizeUrl", "title", "getOverlayUrl", "ɵɵtemplate", "LobbyMenuItemsPreviewFormComponent_div_3_img_4_Template", "LobbyMenuItemsPreviewFormComponent_div_3_img_5_Template", "getRibbonControl", "defaultRibbons", "ɵɵtextInterpolate", "ɵɵpipeBind1", "getOverlayUrlControl", "errorMessages", "LobbyMenuItemsPreviewFormComponent", "constructor", "fb", "lobbyMenuItemPreviewService", "cdnService", "cdr", "images", "text", "bg", "color", "games", "urlIsNotCorrect", "destroyed$", "getGames", "pipe", "subscribe", "val", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "form", "initForm", "ngOnInit", "valueChanges", "detectChanges", "gameImages", "ngOnDestroy", "next", "complete", "group", "id", "for<PERSON>ach", "setControl", "ribbon", "overlayUrl", "urlValidation", "patchValue", "setValue", "game", "value", "defaultInfo", "overlay", "statusChanges", "invalid", "name", "get", "values", "options", "Object", "entries", "controls", "mark<PERSON><PERSON>ristine", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameId", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "LobbyMenuItemPreviewService", "i3", "CdnService", "ChangeDetectorRef", "selectors", "inputs", "decls", "vars", "consts", "template", "LobbyMenuItemsPreviewFormComponent_Template", "rf", "ctx", "LobbyMenuItemsPreviewFormComponent_div_1_Template", "LobbyMenuItemsPreviewFormComponent_div_3_Template", "LobbyMenuItemsPreviewFormComponent_div_4_Template", "length"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/lobby/lobby-form/lobby-menu-items/lobby-menu-items-setup/lobby-menu-items-preview/lobby-menu-items-preview-form/lobby-menu-items-preview-form.component.ts", "/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/lobby/lobby-form/lobby-menu-items/lobby-menu-items-setup/lobby-menu-items-preview/lobby-menu-items-preview-form/lobby-menu-items-preview-from.component.html"], "sourcesContent": ["import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';\nimport { FormBuilder, FormControl, FormGroup } from '@angular/forms';\nimport { Observable, Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\n\nimport { CdnService } from '../../../../../../../common/services/cdn.service';\nimport { ValidationService } from '../../../../../../../common/services/validation.service';\nimport { GameInfo } from '../../../../../../../common/typings';\nimport { LobbyMenuItemRibbon } from '../../../../../lobby.model';\nimport { LobbyMenuItemPreviewService } from '../lobby-menu-item-preview.service';\n\n\n@Component({\n  selector: 'lobby-menu-items-preview-form',\n  templateUrl: './lobby-menu-items-preview-from.component.html',\n  styleUrls: ['./lobby-menu-items-preview-form.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class LobbyMenuItemsPreviewFormComponent implements OnInit, OnDestroy {\n  @Input() gameCategory?: string;\n  images: { [key: string]: string; } = {};\n  defaultRibbons: LobbyMenuItemRibbon[] = [\n    {\n      text: 'Hot',\n      bg: 'linear-gradient(270deg, #EC203300 0%, #EC2033 50%, #EC2033 100%)',\n      color: '#FFFFFF'\n    },\n    {\n      text: 'New',\n      bg: 'linear-gradient(270deg, #FECB0100 0%, #FECB01 51%, #FECB01 100%)',\n      color: '#000000'\n    },\n    {\n      text: 'Exclusive',\n      bg: 'linear-gradient(270deg, #1B76EE00 0%, #1B76EE 51%, #1B76EE 100%)',\n      color: '#FFFFFF'\n    }\n  ];\n  games: GameInfo[] = [];\n  readonly form: FormGroup;\n  readonly errorMessages = {\n    urlIsNotCorrect: 'VALIDATION.urlIsNotCorrect',\n  };\n\n  private readonly destroyed$ = new Subject();\n\n  constructor(private fb: FormBuilder,\n    private lobbyMenuItemPreviewService: LobbyMenuItemPreviewService,\n    private readonly cdnService: CdnService,\n    private readonly cdr: ChangeDetectorRef\n  ) {\n    this.lobbyMenuItemPreviewService.getGames()\n      .pipe(\n        takeUntil(this.destroyed$)\n      )\n      .subscribe((val: GameInfo[]) => {\n        this.games = val;\n        this.cdr.markForCheck();\n      });\n\n    this.form = this.initForm();\n  }\n\n  ngOnInit() {\n    this.form.valueChanges\n      .pipe(\n        takeUntil(this.destroyed$)\n      )\n      .subscribe(() => {\n        this.cdr.detectChanges();\n      });\n\n    this.cdnService.gameImages.pipe(\n      takeUntil(this.destroyed$)\n    ).subscribe(images => {\n      this.images = images;\n      this.cdr.detectChanges();\n    });\n  }\n\n  ngOnDestroy(): void {\n    this.destroyed$.next();\n    this.destroyed$.complete();\n  }\n\n  initForm(val?: any): FormGroup {\n    const group = this.fb.group({\n      id: [''],\n    }) as FormGroup<any>;\n\n    this.games.forEach(({ code }) => {\n      group.setControl(code, new FormGroup({\n        ribbon: new FormControl(),\n        overlayUrl: new FormControl(null, ValidationService.urlValidation)\n      }));\n    });\n\n    if (val) {\n      group.patchValue(val);\n    }\n\n    return group;\n  }\n\n  setValue(val: any) {\n    if (val) {\n      this.form.patchValue(val);\n    }\n  }\n\n  getOverlayUrl(game: GameInfo): string {\n    return this.getOverlayUrlControl(game.code).value || game?.defaultInfo?.images?.overlay;\n  }\n\n  get valueChanges(): Observable<any> {\n    return this.form.valueChanges;\n  }\n\n  get statusChanges(): Observable<any> {\n    return this.form.statusChanges;\n  }\n\n  get invalid(): boolean {\n    return this.form.invalid;\n  }\n\n  getRibbonControl(name: string): FormControl {\n    return this.form.get(name).get('ribbon') as FormControl;\n  }\n\n  getOverlayUrlControl(name: string): FormControl {\n    return this.form.get(name).get('overlayUrl') as FormControl;\n  }\n\n  patchValue(values: { [name: string]: any; }, options?: { emitEvent: boolean; }): void {\n    Object.entries(values).forEach(([code, value]) => {\n      if (code in this.form.controls) {\n        this.form.get(code).patchValue(value, options);\n      } else {\n        this.form.setControl(code, new FormControl(value));\n      }\n    });\n    this.form.markAsPristine();\n    this.form.markAsUntouched();\n  }\n\n  getGameImageUrl(gameId: string): string {\n    return this.images[gameId];\n  }\n}\n", "<div class=\"lobby-games\" [formGroup]=\"form\">\n  <div class=\"lobby-games__header\" *ngIf=\"gameCategory\">Category: {{ gameCategory }}</div>\n  <div class=\"lobby-games__body\">\n    <div class=\"lobby-games__item\" *ngFor=\"let game of games\">\n      <div class=\"lobby-thumb\">\n        <div class=\"lobby-thumb__wrapper\">\n          <div class=\"lobby-thumb__image\">\n            <img *ngIf=\"getGameImageUrl(game.code)\" [src]=\"getGameImageUrl(game.code)\" [alt]=\"game.title\">\n            <img class=\"lobby-thumb__overlay-img\" *ngIf=\"getOverlayUrl(game)\" [src]=\"getOverlayUrl(game)\">\n          </div>\n        </div>\n        <div class=\"lobby-thumb__caption\">\n          <div class=\"lobby-thumb__title\">\n            {{game.title}}\n          </div>\n        </div>\n        <lobby-menu-items-ribbons\n          [formControl]=\"getRibbonControl(game.code)\"\n          [ribbons]=\"defaultRibbons\"\n          [maxLength]=\"18\">\n        </lobby-menu-items-ribbons>\n        <mat-form-field appearance=\"outline\" class=\"width100\">\n          <mat-label>{{'LOBBY.MENU_ITEMS.overlayUrl' | translate}}</mat-label>\n          <input matInput trimValue autocomplete=\"off\"\n                 [formControl]=\"getOverlayUrlControl(game.code)\"\n                 [placeholder]=\"'LOBBY.MENU_ITEMS.overlayUrl' | translate\"/>\n          <mat-error>\n            <lib-swui-control-messages\n              [messages]=\"errorMessages\"\n              [control]=\"getOverlayUrlControl(game.code)\">\n            </lib-swui-control-messages>\n          </mat-error>\n        </mat-form-field>\n      </div>\n    </div>\n    <div class=\"lobby-games__message\" *ngIf=\"games?.length === 0\">\n      <div class=\"alert alert-danger\">\n        {{'LOBBY.MENU_ITEMS.empty' | translate}}\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAAsBA,WAAW,EAAEC,SAAS,QAAQ,gBAAgB;AACpE,SAAqBC,OAAO,QAAQ,MAAM;AAC1C,SAASC,SAAS,QAAQ,gBAAgB;AAG1C,SAASC,iBAAiB,QAAQ,yDAAyD;;;;;;;;;;;;;ICLzFC,EAAA,CAAAC,cAAA,aAAsD;IAAAD,EAAA,CAAAE,MAAA,GAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAAlCH,EAAA,CAAAI,SAAA,EAA4B;IAA5BJ,EAAA,CAAAK,kBAAA,eAAAC,MAAA,CAAAC,YAAA,KAA4B;;;;;IAMxEP,EAAA,CAAAQ,SAAA,cAA8F;;;;;IAAnBR,EAAnC,CAAAS,UAAA,QAAAH,MAAA,CAAAI,eAAA,CAAAC,OAAA,CAAAC,IAAA,GAAAZ,EAAA,CAAAa,aAAA,CAAkC,QAAAF,OAAA,CAAAG,KAAA,CAAmB;;;;;IAC7Fd,EAAA,CAAAQ,SAAA,cAA8F;;;;;IAA5BR,EAAA,CAAAS,UAAA,QAAAH,MAAA,CAAAS,aAAA,CAAAJ,OAAA,GAAAX,EAAA,CAAAa,aAAA,CAA2B;;;;;IAF/Fb,EAHN,CAAAC,cAAA,aAA0D,aAC/B,aACW,aACA;IAE9BD,EADA,CAAAgB,UAAA,IAAAC,uDAAA,kBAA8F,IAAAC,uDAAA,kBACA;IAElGlB,EADE,CAAAG,YAAA,EAAM,EACF;IAEJH,EADF,CAAAC,cAAA,cAAkC,cACA;IAC9BD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;IACNH,EAAA,CAAAQ,SAAA,mCAI2B;IAEzBR,EADF,CAAAC,cAAA,0BAAsD,iBACzC;IAAAD,EAAA,CAAAE,MAAA,IAA6C;;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACpEH,EAAA,CAAAQ,SAAA,iBAEkE;;IAClER,EAAA,CAAAC,cAAA,iBAAW;IACTD,EAAA,CAAAQ,SAAA,qCAG4B;IAIpCR,EAHM,CAAAG,YAAA,EAAY,EACG,EACb,EACF;;;;;IA3BQH,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAS,UAAA,SAAAH,MAAA,CAAAI,eAAA,CAAAC,OAAA,CAAAC,IAAA,EAAgC;IACCZ,EAAA,CAAAI,SAAA,EAAyB;IAAzBJ,EAAA,CAAAS,UAAA,SAAAH,MAAA,CAAAS,aAAA,CAAAJ,OAAA,EAAyB;IAKhEX,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAM,OAAA,CAAAG,KAAA,MACF;IAGAd,EAAA,CAAAI,SAAA,EAA2C;IAE3CJ,EAFA,CAAAS,UAAA,gBAAAH,MAAA,CAAAa,gBAAA,CAAAR,OAAA,CAAAC,IAAA,EAA2C,YAAAN,MAAA,CAAAc,cAAA,CACjB,iBACV;IAGLpB,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAqB,iBAAA,CAAArB,EAAA,CAAAsB,WAAA,wCAA6C;IAEjDtB,EAAA,CAAAI,SAAA,GAA+C;IAC/CJ,EADA,CAAAS,UAAA,gBAAAH,MAAA,CAAAiB,oBAAA,CAAAZ,OAAA,CAAAC,IAAA,EAA+C,gBAAAZ,EAAA,CAAAsB,WAAA,wCACU;IAG5DtB,EAAA,CAAAI,SAAA,GAA0B;IAC1BJ,EADA,CAAAS,UAAA,aAAAH,MAAA,CAAAkB,aAAA,CAA0B,YAAAlB,MAAA,CAAAiB,oBAAA,CAAAZ,OAAA,CAAAC,IAAA,EACiB;;;;;IAOnDZ,EADF,CAAAC,cAAA,cAA8D,cAC5B;IAC9BD,EAAA,CAAAE,MAAA,GACF;;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;;;IAFFH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAL,EAAA,CAAAsB,WAAA,sCACF;;;ADpBN,OAAM,MAAOG,kCAAkC;EA4B7CC,YAAoBC,EAAe,EACzBC,2BAAwD,EAC/CC,UAAsB,EACtBC,GAAsB;IAHrB,KAAAH,EAAE,GAAFA,EAAE;IACZ,KAAAC,2BAA2B,GAA3BA,2BAA2B;IAClB,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,GAAG,GAAHA,GAAG;IA7BtB,KAAAC,MAAM,GAA+B,EAAE;IACvC,KAAAX,cAAc,GAA0B,CACtC;MACEY,IAAI,EAAE,KAAK;MACXC,EAAE,EAAE,kEAAkE;MACtEC,KAAK,EAAE;KACR,EACD;MACEF,IAAI,EAAE,KAAK;MACXC,EAAE,EAAE,kEAAkE;MACtEC,KAAK,EAAE;KACR,EACD;MACEF,IAAI,EAAE,WAAW;MACjBC,EAAE,EAAE,kEAAkE;MACtEC,KAAK,EAAE;KACR,CACF;IACD,KAAAC,KAAK,GAAe,EAAE;IAEb,KAAAX,aAAa,GAAG;MACvBY,eAAe,EAAE;KAClB;IAEgB,KAAAC,UAAU,GAAG,IAAIxC,OAAO,EAAE;IAOzC,IAAI,CAAC+B,2BAA2B,CAACU,QAAQ,EAAE,CACxCC,IAAI,CACHzC,SAAS,CAAC,IAAI,CAACuC,UAAU,CAAC,CAC3B,CACAG,SAAS,CAAEC,GAAe,IAAI;MAC7B,IAAI,CAACN,KAAK,GAAGM,GAAG;MAChB,IAAI,CAACX,GAAG,CAACY,YAAY,EAAE;IACzB,CAAC,CAAC;IAEJ,IAAI,CAACC,IAAI,GAAG,IAAI,CAACC,QAAQ,EAAE;EAC7B;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACF,IAAI,CAACG,YAAY,CACnBP,IAAI,CACHzC,SAAS,CAAC,IAAI,CAACuC,UAAU,CAAC,CAC3B,CACAG,SAAS,CAAC,MAAK;MACd,IAAI,CAACV,GAAG,CAACiB,aAAa,EAAE;IAC1B,CAAC,CAAC;IAEJ,IAAI,CAAClB,UAAU,CAACmB,UAAU,CAACT,IAAI,CAC7BzC,SAAS,CAAC,IAAI,CAACuC,UAAU,CAAC,CAC3B,CAACG,SAAS,CAACT,MAAM,IAAG;MACnB,IAAI,CAACA,MAAM,GAAGA,MAAM;MACpB,IAAI,CAACD,GAAG,CAACiB,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEAE,WAAWA,CAAA;IACT,IAAI,CAACZ,UAAU,CAACa,IAAI,EAAE;IACtB,IAAI,CAACb,UAAU,CAACc,QAAQ,EAAE;EAC5B;EAEAP,QAAQA,CAACH,GAAS;IAChB,MAAMW,KAAK,GAAG,IAAI,CAACzB,EAAE,CAACyB,KAAK,CAAC;MAC1BC,EAAE,EAAE,CAAC,EAAE;KACR,CAAmB;IAEpB,IAAI,CAAClB,KAAK,CAACmB,OAAO,CAAC,CAAC;MAAE1C;IAAI,CAAE,KAAI;MAC9BwC,KAAK,CAACG,UAAU,CAAC3C,IAAI,EAAE,IAAIhB,SAAS,CAAC;QACnC4D,MAAM,EAAE,IAAI7D,WAAW,EAAE;QACzB8D,UAAU,EAAE,IAAI9D,WAAW,CAAC,IAAI,EAAEI,iBAAiB,CAAC2D,aAAa;OAClE,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,IAAIjB,GAAG,EAAE;MACPW,KAAK,CAACO,UAAU,CAAClB,GAAG,CAAC;IACvB;IAEA,OAAOW,KAAK;EACd;EAEAQ,QAAQA,CAACnB,GAAQ;IACf,IAAIA,GAAG,EAAE;MACP,IAAI,CAACE,IAAI,CAACgB,UAAU,CAAClB,GAAG,CAAC;IAC3B;EACF;EAEA1B,aAAaA,CAAC8C,IAAc;IAC1B,OAAO,IAAI,CAACtC,oBAAoB,CAACsC,IAAI,CAACjD,IAAI,CAAC,CAACkD,KAAK,IAAID,IAAI,EAAEE,WAAW,EAAEhC,MAAM,EAAEiC,OAAO;EACzF;EAEA,IAAIlB,YAAYA,CAAA;IACd,OAAO,IAAI,CAACH,IAAI,CAACG,YAAY;EAC/B;EAEA,IAAImB,aAAaA,CAAA;IACf,OAAO,IAAI,CAACtB,IAAI,CAACsB,aAAa;EAChC;EAEA,IAAIC,OAAOA,CAAA;IACT,OAAO,IAAI,CAACvB,IAAI,CAACuB,OAAO;EAC1B;EAEA/C,gBAAgBA,CAACgD,IAAY;IAC3B,OAAO,IAAI,CAACxB,IAAI,CAACyB,GAAG,CAACD,IAAI,CAAC,CAACC,GAAG,CAAC,QAAQ,CAAgB;EACzD;EAEA7C,oBAAoBA,CAAC4C,IAAY;IAC/B,OAAO,IAAI,CAACxB,IAAI,CAACyB,GAAG,CAACD,IAAI,CAAC,CAACC,GAAG,CAAC,YAAY,CAAgB;EAC7D;EAEAT,UAAUA,CAACU,MAAgC,EAAEC,OAAiC;IAC5EC,MAAM,CAACC,OAAO,CAACH,MAAM,CAAC,CAACf,OAAO,CAAC,CAAC,CAAC1C,IAAI,EAAEkD,KAAK,CAAC,KAAI;MAC/C,IAAIlD,IAAI,IAAI,IAAI,CAAC+B,IAAI,CAAC8B,QAAQ,EAAE;QAC9B,IAAI,CAAC9B,IAAI,CAACyB,GAAG,CAACxD,IAAI,CAAC,CAAC+C,UAAU,CAACG,KAAK,EAAEQ,OAAO,CAAC;MAChD,CAAC,MAAM;QACL,IAAI,CAAC3B,IAAI,CAACY,UAAU,CAAC3C,IAAI,EAAE,IAAIjB,WAAW,CAACmE,KAAK,CAAC,CAAC;MACpD;IACF,CAAC,CAAC;IACF,IAAI,CAACnB,IAAI,CAAC+B,cAAc,EAAE;IAC1B,IAAI,CAAC/B,IAAI,CAACgC,eAAe,EAAE;EAC7B;EAEAjE,eAAeA,CAACkE,MAAc;IAC5B,OAAO,IAAI,CAAC7C,MAAM,CAAC6C,MAAM,CAAC;EAC5B;;;uCAlIWnD,kCAAkC,EAAAzB,EAAA,CAAA6E,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA/E,EAAA,CAAA6E,iBAAA,CAAAG,EAAA,CAAAC,2BAAA,GAAAjF,EAAA,CAAA6E,iBAAA,CAAAK,EAAA,CAAAC,UAAA,GAAAnF,EAAA,CAAA6E,iBAAA,CAAA7E,EAAA,CAAAoF,iBAAA;IAAA;EAAA;;;YAAlC3D,kCAAkC;MAAA4D,SAAA;MAAAC,MAAA;QAAA/E,YAAA;MAAA;MAAAgF,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4CAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UClB/C5F,EAAA,CAAAC,cAAA,aAA4C;UAC1CD,EAAA,CAAAgB,UAAA,IAAA8E,iDAAA,iBAAsD;UACtD9F,EAAA,CAAAC,cAAA,aAA+B;UAiC7BD,EAhCA,CAAAgB,UAAA,IAAA+E,iDAAA,mBAA0D,IAAAC,iDAAA,iBAgCI;UAMlEhG,EADE,CAAAG,YAAA,EAAM,EACF;;;UAzCmBH,EAAA,CAAAS,UAAA,cAAAoF,GAAA,CAAAlD,IAAA,CAAkB;UACP3C,EAAA,CAAAI,SAAA,EAAkB;UAAlBJ,EAAA,CAAAS,UAAA,SAAAoF,GAAA,CAAAtF,YAAA,CAAkB;UAEFP,EAAA,CAAAI,SAAA,GAAQ;UAARJ,EAAA,CAAAS,UAAA,YAAAoF,GAAA,CAAA1D,KAAA,CAAQ;UAgCrBnC,EAAA,CAAAI,SAAA,EAAyB;UAAzBJ,EAAA,CAAAS,UAAA,UAAAoF,GAAA,CAAA1D,KAAA,kBAAA0D,GAAA,CAAA1D,KAAA,CAAA8D,MAAA,QAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}