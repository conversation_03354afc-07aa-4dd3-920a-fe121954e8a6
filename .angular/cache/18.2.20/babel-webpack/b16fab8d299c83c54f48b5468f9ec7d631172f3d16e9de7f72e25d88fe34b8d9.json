{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiSelectModule } from '@skywind-group/lib-swui';\nimport { MerchantParamsComponent } from './merchant-params.component';\nimport { SelectProxyDialogComponent } from './select-proxy-dialog.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nexport class ManageMerchantParamsModule {\n  static {\n    this.ɵfac = function ManageMerchantParamsModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ManageMerchantParamsModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ManageMerchantParamsModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, ReactiveFormsModule, TranslateModule.forChild(), MatDialogModule, MatButtonModule, MatIconModule, MatTooltipModule, MatFormFieldModule, SwuiSelectModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ManageMerchantParamsModule, {\n    declarations: [MerchantParamsComponent, SelectProxyDialogComponent],\n    imports: [CommonModule, ReactiveFormsModule, i1.TranslateModule, MatDialogModule, MatButtonModule, MatIconModule, MatTooltipModule, MatFormFieldModule, SwuiSelectModule],\n    exports: [MerchantParamsComponent]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "ReactiveFormsModule", "MatFormFieldModule", "MatButtonModule", "MatDialogModule", "MatIconModule", "MatTooltipModule", "TranslateModule", "SwuiSelectModule", "MerchantParamsComponent", "SelectProxyDialogComponent", "ManageMerchantParamsModule", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "imports", "i1", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-domains/manage-merchant-params/manage-merchant-params.module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiSelectModule } from '@skywind-group/lib-swui';\n\nimport { MerchantParamsComponent } from './merchant-params.component';\nimport { SelectProxyDialogComponent } from './select-proxy-dialog.component';\n\n@NgModule({\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    TranslateModule.forChild(),\n    MatDialogModule,\n    MatButtonModule,\n    MatIconModule,\n    MatTooltipModule,\n    MatFormFieldModule,\n    SwuiSelectModule,\n  ],\n  exports: [\n    MerchantParamsComponent,\n  ],\n  declarations: [\n    MerchantParamsComponent,\n    SelectProxyDialogComponent,\n  ],\n  providers: [],\n})\nexport class ManageMerchantParamsModule {\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,gBAAgB,QAAQ,yBAAyB;AAE1D,SAASC,uBAAuB,QAAQ,6BAA6B;AACrE,SAASC,0BAA0B,QAAQ,iCAAiC;;;AAuB5E,OAAM,MAAOC,0BAA0B;;;uCAA1BA,0BAA0B;IAAA;EAAA;;;YAA1BA;IAA0B;EAAA;;;gBAnBnCX,YAAY,EACZC,mBAAmB,EACnBM,eAAe,CAACK,QAAQ,EAAE,EAC1BR,eAAe,EACfD,eAAe,EACfE,aAAa,EACbC,gBAAgB,EAChBJ,kBAAkB,EAClBM,gBAAgB;IAAA;EAAA;;;2EAWPG,0BAA0B;IAAAE,YAAA,GALnCJ,uBAAuB,EACvBC,0BAA0B;IAAAI,OAAA,GAf1Bd,YAAY,EACZC,mBAAmB,EAAAc,EAAA,CAAAR,eAAA,EAEnBH,eAAe,EACfD,eAAe,EACfE,aAAa,EACbC,gBAAgB,EAChBJ,kBAAkB,EAClBM,gBAAgB;IAAAQ,OAAA,GAGhBP,uBAAuB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}