{"ast": null, "code": "import { filter, finalize, switchMap, take } from 'rxjs/operators';\nimport { DOMAIN_TYPES, STATIC_DOMAIN_TYPES } from '../../../../../../../common/models/domain.model';\nimport { SelectDomainDialogComponent } from '../select-domain-dialog.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/pages/domains-management/entity-domain.service\";\nimport * as i2 from \"@angular/material/dialog\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/material/button\";\nimport * as i5 from \"@angular/material/icon\";\nimport * as i6 from \"@angular/material/tooltip\";\nimport * as i7 from \"@ngx-translate/core\";\nfunction DomainItemComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"strong\", 8);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, \"ENTITY_SETUP.DOMAINS.notSet\"));\n  }\n}\nfunction DomainItemComponent_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n    i0.ɵɵpipe(1, \"translate\");\n  }\n  if (rf & 2) {\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(1, 1, \"COMPONENTS.GRID.LOADING\"), \"\\n\");\n  }\n}\nfunction DomainItemComponent_ng_container_10_ng_container_1_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 11);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\"(\", i0.ɵɵpipeBind1(2, 2, \"ENTITY_SETUP.DOMAINS.environment\"), \": \", ctx_r1.domain.environment, \")\");\n  }\n}\nfunction DomainItemComponent_ng_container_10_ng_container_1_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 12);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"(\", i0.ɵɵpipeBind1(2, 1, \"ENTITY_SETUP.DOMAINS.inherited\"), \")\");\n  }\n}\nfunction DomainItemComponent_ng_container_10_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"strong\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, DomainItemComponent_ng_container_10_ng_container_1_span_3_Template, 3, 4, \"span\", 9)(4, DomainItemComponent_ng_container_10_ng_container_1_span_4_Template, 3, 3, \"span\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.domain.domain);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.domainType === ctx_r1.domainTypes.dynamic);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.domain.inherited);\n  }\n}\nfunction DomainItemComponent_ng_container_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, DomainItemComponent_ng_container_10_ng_container_1_Template, 5, 3, \"ng-container\", 4);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    const emptyDomain_r3 = i0.ɵɵreference(1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.domain == null ? null : ctx_r1.domain.id)(\"ngIfElse\", emptyDomain_r3);\n  }\n}\nexport class DomainItemComponent {\n  constructor(entityDomainService, dialog) {\n    this.entityDomainService = entityDomainService;\n    this.dialog = dialog;\n    this.domainTypes = DOMAIN_TYPES;\n    this.loading = true;\n    this.resetComplete = false;\n  }\n  get domain() {\n    let domainId;\n    if (this.domainType === DOMAIN_TYPES.static) {\n      if (this.staticDomainType === STATIC_DOMAIN_TYPES.static) {\n        domainId = this.entity.staticDomainId;\n      } else if (this.staticDomainType === STATIC_DOMAIN_TYPES.lobby) {\n        domainId = this.entity.lobbyDomainId;\n      } else if (this.staticDomainType === STATIC_DOMAIN_TYPES.liveStreaming) {\n        domainId = this.entity.liveStreamingDomainId;\n      } else if (this.staticDomainType === STATIC_DOMAIN_TYPES.ehub) {\n        domainId = this.entity.ehubDomainId;\n      }\n    } else {\n      domainId = this.entity.dynamicDomainId;\n    }\n    if (this._domain && this._domain.id !== domainId) {\n      return {\n        ...this._domain,\n        inherited: true\n      };\n    }\n    return this._domain;\n  }\n  set domain(domain) {\n    this._domain = domain;\n  }\n  ngOnInit() {\n    this.loadDomain();\n  }\n  resetButtonDisabled() {\n    return !(this.domain && this.domain.hasOwnProperty('id')) || this.resetComplete || this.domain.inherited;\n  }\n  resetToParent(event) {\n    event.preventDefault();\n    if (this.resetButtonDisabled()) {\n      return;\n    }\n    this.entityDomainService.removeEntityDomain(this.domainType, this.entity.path, this.staticDomainType).pipe(take(1)).subscribe(() => {\n      this.setEntity(null);\n      this.loadDomain();\n      this.resetComplete = true;\n    });\n  }\n  setNewDomain() {\n    this.dialog.open(SelectDomainDialogComponent, {\n      width: '600px',\n      data: {\n        domainType: this.domainType,\n        staticDomainType: this.staticDomainType,\n        domainId: this.domain?.id\n      },\n      disableClose: true\n    }).afterClosed().pipe(filter(result => !!result), switchMap(domain => this.entityDomainService.setEntityDomain(this.domainType, domain.id, this.entity.path, this.staticDomainType)), take(1)).subscribe(domain => {\n      this.setEntity(domain);\n      this.loadDomain();\n      this.resetComplete = false;\n    });\n  }\n  setEntity(domain) {\n    if (this.domainType === DOMAIN_TYPES.static) {\n      if (this.staticDomainType === STATIC_DOMAIN_TYPES.static) {\n        this.entity.staticDomainId = domain?.id;\n      } else if (this.staticDomainType === STATIC_DOMAIN_TYPES.lobby) {\n        this.entity.lobbyDomainId = domain?.id;\n      } else if (this.staticDomainType === STATIC_DOMAIN_TYPES.liveStreaming) {\n        this.entity.liveStreamingDomainId = domain?.id;\n      } else if (this.staticDomainType === STATIC_DOMAIN_TYPES.ehub) {\n        this.entity.ehubDomainId = domain?.id;\n      }\n    } else {\n      this.entity.dynamicDomainId = domain?.id;\n    }\n  }\n  loadDomain() {\n    this.loading = true;\n    this.entityDomainService.getEntityDomain(this.domainType, this.entity.path, true, this.staticDomainType).pipe(finalize(() => this.loading = false), take(1)).subscribe(domain => {\n      this.domain = domain;\n    });\n  }\n  static {\n    this.ɵfac = function DomainItemComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DomainItemComponent)(i0.ɵɵdirectiveInject(i1.EntityDomainService), i0.ɵɵdirectiveInject(i2.MatDialog));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DomainItemComponent,\n      selectors: [[\"domain-item\"]],\n      inputs: {\n        domainType: \"domainType\",\n        staticDomainType: \"staticDomainType\",\n        entity: \"entity\"\n      },\n      decls: 20,\n      vars: 12,\n      consts: [[\"emptyDomain\", \"\"], [\"loadingSpinner\", \"\"], [1, \"domain\"], [1, \"domain--info\"], [4, \"ngIf\", \"ngIfElse\"], [1, \"domain--controls\"], [\"mat-icon-button\", \"\", 3, \"click\", \"matTooltip\"], [\"mat-icon-button\", \"\", 3, \"click\", \"disabled\", \"matTooltip\"], [1, \"text-warning\"], [\"class\", \"ml-5\", 4, \"ngIf\"], [\"class\", \"domain-inherited ml-5\", 4, \"ngIf\"], [1, \"ml-5\"], [1, \"domain-inherited\", \"ml-5\"]],\n      template: function DomainItemComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵtemplate(0, DomainItemComponent_ng_template_0_Template, 3, 3, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(2, DomainItemComponent_ng_template_2_Template, 2, 3, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementStart(4, \"div\", 2)(5, \"div\", 3)(6, \"span\");\n          i0.ɵɵtext(7);\n          i0.ɵɵpipe(8, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(9, \"\\u00A0 \");\n          i0.ɵɵtemplate(10, DomainItemComponent_ng_container_10_Template, 2, 2, \"ng-container\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 5)(12, \"button\", 6);\n          i0.ɵɵpipe(13, \"translate\");\n          i0.ɵɵlistener(\"click\", function DomainItemComponent_Template_button_click_12_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.setNewDomain());\n          });\n          i0.ɵɵelementStart(14, \"mat-icon\");\n          i0.ɵɵtext(15, \"edit\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"button\", 7);\n          i0.ɵɵpipe(17, \"translate\");\n          i0.ɵɵlistener(\"click\", function DomainItemComponent_Template_button_click_16_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.resetToParent($event));\n          });\n          i0.ɵɵelementStart(18, \"mat-icon\");\n          i0.ɵɵtext(19, \"replay\");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          const loadingSpinner_r4 = i0.ɵɵreference(3);\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(8, 6, \"ENTITY_SETUP.DOMAINS.\" + ctx.domainType + (ctx.staticDomainType ? \".\" + ctx.staticDomainType : \"\")), \":\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading)(\"ngIfElse\", loadingSpinner_r4);\n          i0.ɵɵadvance(2);\n          i0.ɵɵpropertyInterpolate(\"matTooltip\", i0.ɵɵpipeBind1(13, 8, \"ENTITY_SETUP.DOMAINS.set\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵpropertyInterpolate(\"matTooltip\", i0.ɵɵpipeBind1(17, 10, \"ENTITY_SETUP.DOMAINS.reset\"));\n          i0.ɵɵproperty(\"disabled\", ctx.resetButtonDisabled());\n        }\n      },\n      dependencies: [i3.NgIf, i4.MatIconButton, i5.MatIcon, i6.MatTooltip, i7.TranslatePipe],\n      styles: [\".domain[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: row;\\n  align-items: baseline;\\n}\\n.domain[_ngcontent-%COMP%]   .domain-inherited[_ngcontent-%COMP%] {\\n  color: #43A047;\\n  opacity: 0.8;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvYnVzaW5lc3MtbWFuYWdlbWVudC9jb21wb25lbnRzL2VudGl0aWVzL3RhYi1kb21haW5zL21hbmFnZS1kb21haW5zL2RvbWFpbi1pdGVtL2RvbWFpbi1pdGVtLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EscUJBQUE7QUFDRjtBQUNFO0VBQ0UsY0FBQTtFQUNBLFlBQUE7QUFDSiIsInNvdXJjZXNDb250ZW50IjpbIi5kb21haW4ge1xuICBkaXNwbGF5OiBmbGV4O1xuICBmbGV4LWRpcmVjdGlvbjogcm93O1xuICBhbGlnbi1pdGVtczogYmFzZWxpbmU7XG5cbiAgLmRvbWFpbi1pbmhlcml0ZWQge1xuICAgIGNvbG9yOiAjNDNBMDQ3O1xuICAgIG9wYWNpdHk6IC44O1xuICB9XG59XG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["filter", "finalize", "switchMap", "take", "DOMAIN_TYPES", "STATIC_DOMAIN_TYPES", "SelectDomainDialogComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpipeBind1", "ɵɵtextInterpolate1", "ɵɵtextInterpolate2", "ctx_r1", "domain", "environment", "ɵɵelementContainerStart", "ɵɵtemplate", "DomainItemComponent_ng_container_10_ng_container_1_span_3_Template", "DomainItemComponent_ng_container_10_ng_container_1_span_4_Template", "ɵɵproperty", "domainType", "domainTypes", "dynamic", "inherited", "DomainItemComponent_ng_container_10_ng_container_1_Template", "id", "emptyDomain_r3", "DomainItemComponent", "constructor", "entityDomainService", "dialog", "loading", "resetComplete", "domainId", "static", "staticDomainType", "entity", "staticDomainId", "lobby", "lobbyDomainId", "liveStreaming", "liveStreamingDomainId", "ehub", "ehubDomainId", "dynamicDomainId", "_domain", "ngOnInit", "loadDomain", "resetButtonDisabled", "hasOwnProperty", "resetToParent", "event", "preventDefault", "removeEntityDomain", "path", "pipe", "subscribe", "setEntity", "set<PERSON>ew<PERSON><PERSON><PERSON>", "open", "width", "data", "disableClose", "afterClosed", "result", "setEntityDomain", "getEntityDomain", "ɵɵdirectiveInject", "i1", "EntityDomainService", "i2", "MatDialog", "selectors", "inputs", "decls", "vars", "consts", "template", "DomainItemComponent_Template", "rf", "ctx", "DomainItemComponent_ng_template_0_Template", "ɵɵtemplateRefExtractor", "DomainItemComponent_ng_template_2_Template", "DomainItemComponent_ng_container_10_Template", "ɵɵlistener", "DomainItemComponent_Template_button_click_12_listener", "ɵɵrestoreView", "_r1", "ɵɵresetView", "DomainItemComponent_Template_button_click_16_listener", "$event", "loadingSpinner_r4", "ɵɵpropertyInterpolate"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-domains/manage-domains/domain-item/domain-item.component.ts", "/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-domains/manage-domains/domain-item/domain-item.component.html"], "sourcesContent": ["import { Component, Input } from '@angular/core';\nimport { MatDialog } from '@angular/material/dialog';\nimport { filter, finalize, switchMap, take } from 'rxjs/operators';\nimport { EntityDomainService } from 'src/app/pages/domains-management/entity-domain.service';\nimport { Domain, DOMAIN_TYPES, DomainType, STATIC_DOMAIN_TYPES, StaticDomainType } from '../../../../../../../common/models/domain.model';\nimport { Entity } from '../../../../../../../common/models/entity.model';\nimport { SelectDomainDialogComponent } from '../select-domain-dialog.component';\n\n@Component({\n  selector: 'domain-item',\n  templateUrl: './domain-item.component.html',\n  styleUrls: [\n    './domain-item.component.scss',\n  ],\n})\nexport class DomainItemComponent {\n  readonly domainTypes = DOMAIN_TYPES;\n\n  @Input() domainType: DomainType;\n  @Input() staticDomainType: StaticDomainType;\n  @Input() entity: Entity;\n\n  loading = true;\n\n  private _domain: Domain | null;\n  private resetComplete: boolean = false;\n\n  constructor(\n    private readonly entityDomainService: EntityDomainService,\n    private readonly dialog: MatDialog,\n  ) {\n  }\n\n  get domain(): Domain | null {\n    let domainId: string;\n    if (this.domainType === DOMAIN_TYPES.static) {\n      if (this.staticDomainType === STATIC_DOMAIN_TYPES.static) {\n        domainId = this.entity.staticDomainId;\n      } else if (this.staticDomainType === STATIC_DOMAIN_TYPES.lobby) {\n        domainId = this.entity.lobbyDomainId;\n      } else if (this.staticDomainType === STATIC_DOMAIN_TYPES.liveStreaming) {\n        domainId = this.entity.liveStreamingDomainId;\n      } else if (this.staticDomainType === STATIC_DOMAIN_TYPES.ehub) {\n        domainId = this.entity.ehubDomainId;\n      }\n    } else {\n      domainId = this.entity.dynamicDomainId;\n    }\n    if (this._domain && this._domain.id !== domainId) {\n      return { ...this._domain, inherited: true };\n    }\n    return this._domain;\n  }\n\n  set domain(domain: Domain | null) {\n    this._domain = domain;\n  }\n\n  ngOnInit() {\n    this.loadDomain();\n  }\n\n  resetButtonDisabled(): boolean {\n    return !(this.domain && this.domain.hasOwnProperty('id')) || this.resetComplete || this.domain.inherited;\n  }\n\n  resetToParent(event: Event) {\n    event.preventDefault();\n    if (this.resetButtonDisabled()) {\n      return;\n    }\n    this.entityDomainService.removeEntityDomain(this.domainType, this.entity.path, this.staticDomainType).pipe(\n      take(1),\n    ).subscribe(() => {\n      this.setEntity(null);\n      this.loadDomain();\n      this.resetComplete = true;\n    });\n  }\n\n  setNewDomain() {\n    this.dialog.open(SelectDomainDialogComponent, {\n      width: '600px',\n      data: {\n        domainType: this.domainType,\n        staticDomainType: this.staticDomainType,\n        domainId: this.domain?.id,\n      },\n      disableClose: true\n    }).afterClosed().pipe(\n      filter(result => !!result),\n      switchMap((domain) => this.entityDomainService.setEntityDomain(this.domainType, domain.id, this.entity.path, this.staticDomainType)),\n      take(1),\n    ).subscribe((domain) => {\n      this.setEntity(domain);\n      this.loadDomain();\n      this.resetComplete = false;\n    });\n  }\n\n  private setEntity(domain: Domain | null) {\n    if (this.domainType === DOMAIN_TYPES.static) {\n      if (this.staticDomainType === STATIC_DOMAIN_TYPES.static) {\n        this.entity.staticDomainId = domain?.id;\n      } else if (this.staticDomainType === STATIC_DOMAIN_TYPES.lobby) {\n        this.entity.lobbyDomainId = domain?.id;\n      } else if (this.staticDomainType === STATIC_DOMAIN_TYPES.liveStreaming) {\n        this.entity.liveStreamingDomainId = domain?.id;\n      } else if (this.staticDomainType === STATIC_DOMAIN_TYPES.ehub) {\n        this.entity.ehubDomainId = domain?.id;\n      }\n    } else {\n      this.entity.dynamicDomainId = domain?.id;\n    }\n  }\n\n  private loadDomain() {\n    this.loading = true;\n    this.entityDomainService.getEntityDomain(this.domainType, this.entity.path, true, this.staticDomainType).pipe(\n      finalize(() => this.loading = false),\n      take(1)\n    ).subscribe((domain) => {\n      this.domain = domain;\n    });\n  }\n}\n", "<ng-template #emptyDomain>\n  <strong class=\"text-warning\">{{'ENTITY_SETUP.DOMAINS.notSet' | translate}}</strong>\n</ng-template>\n\n<ng-template #loadingSpinner>\n  {{ 'COMPONENTS.GRID.LOADING' | translate }}\n</ng-template>\n\n<div class=\"domain\">\n  <div class=\"domain--info\">\n    <span>{{ 'ENTITY_SETUP.DOMAINS.' + domainType + (staticDomainType ? '.' + staticDomainType : '') | translate}}:</span>&nbsp;\n    <ng-container *ngIf=\"!loading; else loadingSpinner\">\n      <ng-container *ngIf=\"domain?.id; else emptyDomain\">\n        <strong>{{ domain.domain }}</strong>\n        <span *ngIf=\"domainType === domainTypes.dynamic\" class=\"ml-5\">({{'ENTITY_SETUP.DOMAINS.environment' | translate}}: {{ domain.environment }})</span>\n        <span *ngIf=\"domain.inherited\" class=\"domain-inherited ml-5\">({{'ENTITY_SETUP.DOMAINS.inherited' | translate}})</span>\n      </ng-container>\n    </ng-container>\n  </div>\n  <div class=\"domain--controls\">\n    <button mat-icon-button (click)=\"setNewDomain()\" matTooltip=\"{{'ENTITY_SETUP.DOMAINS.set' | translate}}\">\n      <mat-icon>edit</mat-icon>\n    </button>\n    <button mat-icon-button [disabled]=\"resetButtonDisabled()\" matTooltip=\"{{'ENTITY_SETUP.DOMAINS.reset' | translate}}\"\n            (click)=\"resetToParent($event)\">\n      <mat-icon>replay</mat-icon>\n    </button>\n  </div>\n\n</div>\n"], "mappings": "AAEA,SAASA,MAAM,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,IAAI,QAAQ,gBAAgB;AAElE,SAAiBC,YAAY,EAAcC,mBAAmB,QAA0B,iDAAiD;AAEzI,SAASC,2BAA2B,QAAQ,mCAAmC;;;;;;;;;;;ICL7EC,EAAA,CAAAC,cAAA,gBAA6B;IAAAD,EAAA,CAAAE,MAAA,GAA6C;;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;IAAtDH,EAAA,CAAAI,SAAA,EAA6C;IAA7CJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAM,WAAA,sCAA6C;;;;;IAI1EN,EAAA,CAAAE,MAAA,GACF;;;;IADEF,EAAA,CAAAO,kBAAA,MAAAP,EAAA,CAAAM,WAAA,wCACF;;;;;IAQQN,EAAA,CAAAC,cAAA,eAA8D;IAAAD,EAAA,CAAAE,MAAA,GAA8E;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAArFH,EAAA,CAAAI,SAAA,EAA8E;IAA9EJ,EAAA,CAAAQ,kBAAA,MAAAR,EAAA,CAAAM,WAAA,kDAAAG,MAAA,CAAAC,MAAA,CAAAC,WAAA,MAA8E;;;;;IAC5IX,EAAA,CAAAC,cAAA,eAA6D;IAAAD,EAAA,CAAAE,MAAA,GAAkD;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;IAAzDH,EAAA,CAAAI,SAAA,EAAkD;IAAlDJ,EAAA,CAAAO,kBAAA,MAAAP,EAAA,CAAAM,WAAA,8CAAkD;;;;;IAHjHN,EAAA,CAAAY,uBAAA,GAAmD;IACjDZ,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAEpCH,EADA,CAAAa,UAAA,IAAAC,kEAAA,kBAA8D,IAAAC,kEAAA,mBACD;;;;;IAFrDf,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAAI,MAAA,CAAAC,MAAA,CAAAA,MAAA,CAAmB;IACpBV,EAAA,CAAAI,SAAA,EAAwC;IAAxCJ,EAAA,CAAAgB,UAAA,SAAAP,MAAA,CAAAQ,UAAA,KAAAR,MAAA,CAAAS,WAAA,CAAAC,OAAA,CAAwC;IACxCnB,EAAA,CAAAI,SAAA,EAAsB;IAAtBJ,EAAA,CAAAgB,UAAA,SAAAP,MAAA,CAAAC,MAAA,CAAAU,SAAA,CAAsB;;;;;IAJjCpB,EAAA,CAAAY,uBAAA,GAAoD;IAClDZ,EAAA,CAAAa,UAAA,IAAAQ,2DAAA,0BAAmD;;;;;;IAApCrB,EAAA,CAAAI,SAAA,EAAkB;IAAAJ,EAAlB,CAAAgB,UAAA,SAAAP,MAAA,CAAAC,MAAA,kBAAAD,MAAA,CAAAC,MAAA,CAAAY,EAAA,CAAkB,aAAAC,cAAA,CAAgB;;;ADGvD,OAAM,MAAOC,mBAAmB;EAY9BC,YACmBC,mBAAwC,EACxCC,MAAiB;IADjB,KAAAD,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,MAAM,GAANA,MAAM;IAbhB,KAAAT,WAAW,GAAGrB,YAAY;IAMnC,KAAA+B,OAAO,GAAG,IAAI;IAGN,KAAAC,aAAa,GAAY,KAAK;EAMtC;EAEA,IAAInB,MAAMA,CAAA;IACR,IAAIoB,QAAgB;IACpB,IAAI,IAAI,CAACb,UAAU,KAAKpB,YAAY,CAACkC,MAAM,EAAE;MAC3C,IAAI,IAAI,CAACC,gBAAgB,KAAKlC,mBAAmB,CAACiC,MAAM,EAAE;QACxDD,QAAQ,GAAG,IAAI,CAACG,MAAM,CAACC,cAAc;MACvC,CAAC,MAAM,IAAI,IAAI,CAACF,gBAAgB,KAAKlC,mBAAmB,CAACqC,KAAK,EAAE;QAC9DL,QAAQ,GAAG,IAAI,CAACG,MAAM,CAACG,aAAa;MACtC,CAAC,MAAM,IAAI,IAAI,CAACJ,gBAAgB,KAAKlC,mBAAmB,CAACuC,aAAa,EAAE;QACtEP,QAAQ,GAAG,IAAI,CAACG,MAAM,CAACK,qBAAqB;MAC9C,CAAC,MAAM,IAAI,IAAI,CAACN,gBAAgB,KAAKlC,mBAAmB,CAACyC,IAAI,EAAE;QAC7DT,QAAQ,GAAG,IAAI,CAACG,MAAM,CAACO,YAAY;MACrC;IACF,CAAC,MAAM;MACLV,QAAQ,GAAG,IAAI,CAACG,MAAM,CAACQ,eAAe;IACxC;IACA,IAAI,IAAI,CAACC,OAAO,IAAI,IAAI,CAACA,OAAO,CAACpB,EAAE,KAAKQ,QAAQ,EAAE;MAChD,OAAO;QAAE,GAAG,IAAI,CAACY,OAAO;QAAEtB,SAAS,EAAE;MAAI,CAAE;IAC7C;IACA,OAAO,IAAI,CAACsB,OAAO;EACrB;EAEA,IAAIhC,MAAMA,CAACA,MAAqB;IAC9B,IAAI,CAACgC,OAAO,GAAGhC,MAAM;EACvB;EAEAiC,QAAQA,CAAA;IACN,IAAI,CAACC,UAAU,EAAE;EACnB;EAEAC,mBAAmBA,CAAA;IACjB,OAAO,EAAE,IAAI,CAACnC,MAAM,IAAI,IAAI,CAACA,MAAM,CAACoC,cAAc,CAAC,IAAI,CAAC,CAAC,IAAI,IAAI,CAACjB,aAAa,IAAI,IAAI,CAACnB,MAAM,CAACU,SAAS;EAC1G;EAEA2B,aAAaA,CAACC,KAAY;IACxBA,KAAK,CAACC,cAAc,EAAE;IACtB,IAAI,IAAI,CAACJ,mBAAmB,EAAE,EAAE;MAC9B;IACF;IACA,IAAI,CAACnB,mBAAmB,CAACwB,kBAAkB,CAAC,IAAI,CAACjC,UAAU,EAAE,IAAI,CAACgB,MAAM,CAACkB,IAAI,EAAE,IAAI,CAACnB,gBAAgB,CAAC,CAACoB,IAAI,CACxGxD,IAAI,CAAC,CAAC,CAAC,CACR,CAACyD,SAAS,CAAC,MAAK;MACf,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC;MACpB,IAAI,CAACV,UAAU,EAAE;MACjB,IAAI,CAACf,aAAa,GAAG,IAAI;IAC3B,CAAC,CAAC;EACJ;EAEA0B,YAAYA,CAAA;IACV,IAAI,CAAC5B,MAAM,CAAC6B,IAAI,CAACzD,2BAA2B,EAAE;MAC5C0D,KAAK,EAAE,OAAO;MACdC,IAAI,EAAE;QACJzC,UAAU,EAAE,IAAI,CAACA,UAAU;QAC3Be,gBAAgB,EAAE,IAAI,CAACA,gBAAgB;QACvCF,QAAQ,EAAE,IAAI,CAACpB,MAAM,EAAEY;OACxB;MACDqC,YAAY,EAAE;KACf,CAAC,CAACC,WAAW,EAAE,CAACR,IAAI,CACnB3D,MAAM,CAACoE,MAAM,IAAI,CAAC,CAACA,MAAM,CAAC,EAC1BlE,SAAS,CAAEe,MAAM,IAAK,IAAI,CAACgB,mBAAmB,CAACoC,eAAe,CAAC,IAAI,CAAC7C,UAAU,EAAEP,MAAM,CAACY,EAAE,EAAE,IAAI,CAACW,MAAM,CAACkB,IAAI,EAAE,IAAI,CAACnB,gBAAgB,CAAC,CAAC,EACpIpC,IAAI,CAAC,CAAC,CAAC,CACR,CAACyD,SAAS,CAAE3C,MAAM,IAAI;MACrB,IAAI,CAAC4C,SAAS,CAAC5C,MAAM,CAAC;MACtB,IAAI,CAACkC,UAAU,EAAE;MACjB,IAAI,CAACf,aAAa,GAAG,KAAK;IAC5B,CAAC,CAAC;EACJ;EAEQyB,SAASA,CAAC5C,MAAqB;IACrC,IAAI,IAAI,CAACO,UAAU,KAAKpB,YAAY,CAACkC,MAAM,EAAE;MAC3C,IAAI,IAAI,CAACC,gBAAgB,KAAKlC,mBAAmB,CAACiC,MAAM,EAAE;QACxD,IAAI,CAACE,MAAM,CAACC,cAAc,GAAGxB,MAAM,EAAEY,EAAE;MACzC,CAAC,MAAM,IAAI,IAAI,CAACU,gBAAgB,KAAKlC,mBAAmB,CAACqC,KAAK,EAAE;QAC9D,IAAI,CAACF,MAAM,CAACG,aAAa,GAAG1B,MAAM,EAAEY,EAAE;MACxC,CAAC,MAAM,IAAI,IAAI,CAACU,gBAAgB,KAAKlC,mBAAmB,CAACuC,aAAa,EAAE;QACtE,IAAI,CAACJ,MAAM,CAACK,qBAAqB,GAAG5B,MAAM,EAAEY,EAAE;MAChD,CAAC,MAAM,IAAI,IAAI,CAACU,gBAAgB,KAAKlC,mBAAmB,CAACyC,IAAI,EAAE;QAC7D,IAAI,CAACN,MAAM,CAACO,YAAY,GAAG9B,MAAM,EAAEY,EAAE;MACvC;IACF,CAAC,MAAM;MACL,IAAI,CAACW,MAAM,CAACQ,eAAe,GAAG/B,MAAM,EAAEY,EAAE;IAC1C;EACF;EAEQsB,UAAUA,CAAA;IAChB,IAAI,CAAChB,OAAO,GAAG,IAAI;IACnB,IAAI,CAACF,mBAAmB,CAACqC,eAAe,CAAC,IAAI,CAAC9C,UAAU,EAAE,IAAI,CAACgB,MAAM,CAACkB,IAAI,EAAE,IAAI,EAAE,IAAI,CAACnB,gBAAgB,CAAC,CAACoB,IAAI,CAC3G1D,QAAQ,CAAC,MAAM,IAAI,CAACkC,OAAO,GAAG,KAAK,CAAC,EACpChC,IAAI,CAAC,CAAC,CAAC,CACR,CAACyD,SAAS,CAAE3C,MAAM,IAAI;MACrB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACtB,CAAC,CAAC;EACJ;;;uCA7GWc,mBAAmB,EAAAxB,EAAA,CAAAgE,iBAAA,CAAAC,EAAA,CAAAC,mBAAA,GAAAlE,EAAA,CAAAgE,iBAAA,CAAAG,EAAA,CAAAC,SAAA;IAAA;EAAA;;;YAAnB5C,mBAAmB;MAAA6C,SAAA;MAAAC,MAAA;QAAArD,UAAA;QAAAe,gBAAA;QAAAC,MAAA;MAAA;MAAAsC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCXhC5E,EAJA,CAAAa,UAAA,IAAAiE,0CAAA,gCAAA9E,EAAA,CAAA+E,sBAAA,CAA0B,IAAAC,0CAAA,gCAAAhF,EAAA,CAAA+E,sBAAA,CAIG;UAMzB/E,EAFJ,CAAAC,cAAA,aAAoB,aACQ,WAClB;UAAAD,EAAA,CAAAE,MAAA,GAAyG;;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAE,MAAA,cACtH;UAAAF,EAAA,CAAAa,UAAA,KAAAoE,4CAAA,0BAAoD;UAOtDjF,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,cAA8B,iBAC6E;;UAAjFD,EAAA,CAAAkF,UAAA,mBAAAC,sDAAA;YAAAnF,EAAA,CAAAoF,aAAA,CAAAC,GAAA;YAAA,OAAArF,EAAA,CAAAsF,WAAA,CAAST,GAAA,CAAAtB,YAAA,EAAc;UAAA,EAAC;UAC9CvD,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAChBF,EADgB,CAAAG,YAAA,EAAW,EAClB;UACTH,EAAA,CAAAC,cAAA,iBACwC;;UAAhCD,EAAA,CAAAkF,UAAA,mBAAAK,sDAAAC,MAAA;YAAAxF,EAAA,CAAAoF,aAAA,CAAAC,GAAA;YAAA,OAAArF,EAAA,CAAAsF,WAAA,CAAST,GAAA,CAAA9B,aAAA,CAAAyC,MAAA,CAAqB;UAAA,EAAC;UACrCxF,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAItBF,EAJsB,CAAAG,YAAA,EAAW,EACpB,EACL,EAEF;;;;UAnBIH,EAAA,CAAAI,SAAA,GAAyG;UAAzGJ,EAAA,CAAAO,kBAAA,KAAAP,EAAA,CAAAM,WAAA,iCAAAuE,GAAA,CAAA5D,UAAA,IAAA4D,GAAA,CAAA7C,gBAAA,SAAA6C,GAAA,CAAA7C,gBAAA,aAAyG;UAChGhC,EAAA,CAAAI,SAAA,GAAgB;UAAAJ,EAAhB,CAAAgB,UAAA,UAAA6D,GAAA,CAAAjD,OAAA,CAAgB,aAAA6D,iBAAA,CAAmB;UASDzF,EAAA,CAAAI,SAAA,GAAuD;UAAvDJ,EAAA,CAAA0F,qBAAA,eAAA1F,EAAA,CAAAM,WAAA,oCAAuD;UAG7CN,EAAA,CAAAI,SAAA,GAAyD;UAAzDJ,EAAA,CAAA0F,qBAAA,eAAA1F,EAAA,CAAAM,WAAA,uCAAyD;UAA5FN,EAAA,CAAAgB,UAAA,aAAA6D,GAAA,CAAAhC,mBAAA,GAAkC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}