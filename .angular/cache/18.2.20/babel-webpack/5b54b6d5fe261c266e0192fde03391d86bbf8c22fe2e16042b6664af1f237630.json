{"ast": null, "code": "import { FormControl } from '@angular/forms';\nimport { MatTableDataSource } from '@angular/material/table';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { codeArrayToObjectReducer } from '../../../../../common/services/entity.service';\nimport { MatCountryDialogComponent } from '../../mat-business-structure/dialogs/mat-country-dialog/mat-country-dialog.component';\nimport { RemoveConfirmDialogComponent } from '../dialogs/remove-confirm-dialog.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nconst _c0 = [\"manageCountryBtn\"];\nexport class EntityCountriesComponent {\n  set disabledList(val) {\n    this._disabledList = val;\n    this.refreshData();\n  }\n  get disabledList() {\n    return this._disabledList;\n  }\n  set countries(countries) {\n    this._countries = countries;\n    this.countriesHash = this._countries.reduce(codeArrayToObjectReducer, {});\n  }\n  get countries() {\n    return this._countries;\n  }\n  constructor(dialog, cdr) {\n    this.dialog = dialog;\n    this.cdr = cdr;\n    this.searchControl = new FormControl();\n    this.destroyed$ = new Subject();\n    this.setRowActions();\n  }\n  ngOnInit() {\n    this.refreshData();\n    this.searchControl.valueChanges.pipe(takeUntil(this.destroyed$)).subscribe(filterValue => {\n      this.dataSource.filter = filterValue?.trim().toLowerCase();\n    });\n  }\n  ngOnDestroy() {\n    this.destroyed$.next();\n    this.destroyed$.complete();\n  }\n  get displayedColumns() {\n    return ['displayName', 'status', ...(this.disabledList ? [] : ['code'])];\n  }\n  showCountryModal($event) {\n    $event.preventDefault();\n    this.searchControl.reset();\n  }\n  applyFilter(filterValue) {\n    this.dataSource.filter = filterValue.trim().toLowerCase();\n  }\n  getRowActions(country) {\n    if (this.disabledList || country.isInherited) {\n      return [];\n    }\n    return this.rowActions;\n  }\n  buildCountriesModal(countriesType) {\n    const data = {\n      countriesType,\n      entity: this.entity,\n      items: this.buildAvailableCountries()\n    };\n    return this.dialog.open(MatCountryDialogComponent, {\n      disableClose: true,\n      width: '500px',\n      data\n    }).afterClosed();\n  }\n  buildRemoveCountryModal(countryCode) {\n    return this.dialog.open(RemoveConfirmDialogComponent, {\n      width: '500px',\n      data: {\n        removeCode: countryCode\n      },\n      disableClose: true\n    }).afterClosed();\n  }\n  refreshData() {\n    this.unFocusButton();\n    this.dataSource = new MatTableDataSource(this.loadData());\n    this.cdr.detectChanges();\n  }\n  unFocusButton() {\n    if (this.manageCountryBtn) {\n      this.manageCountryBtn['_elementRef'].nativeElement.classList.remove('cdk-program-focused');\n    }\n  }\n  static {\n    this.ɵfac = function EntityCountriesComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || EntityCountriesComponent)(i0.ɵɵdirectiveInject(i1.MatDialog), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵdir = /*@__PURE__*/i0.ɵɵdefineDirective({\n      type: EntityCountriesComponent,\n      viewQuery: function EntityCountriesComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.manageCountryBtn = _t.first);\n        }\n      },\n      inputs: {\n        disabledList: \"disabledList\",\n        entity: \"entity\",\n        countries: \"countries\"\n      }\n    });\n  }\n}", "map": {"version": 3, "names": ["FormControl", "MatTableDataSource", "Subject", "takeUntil", "codeArrayToObjectReducer", "MatCountryDialogComponent", "RemoveConfirmDialogComponent", "EntityCountriesComponent", "disabledList", "val", "_disabledList", "refreshData", "countries", "_countries", "countriesHash", "reduce", "constructor", "dialog", "cdr", "searchControl", "destroyed$", "setRowActions", "ngOnInit", "valueChanges", "pipe", "subscribe", "filterValue", "dataSource", "filter", "trim", "toLowerCase", "ngOnDestroy", "next", "complete", "displayedColumns", "showCountryModal", "$event", "preventDefault", "reset", "applyFilter", "getRowActions", "country", "isInherited", "rowActions", "buildCountriesModal", "countriesType", "data", "entity", "items", "buildAvailableCountries", "open", "disableClose", "width", "afterClosed", "buildRemoveCountryModal", "countryCode", "removeCode", "unFocusButton", "loadData", "detectChanges", "manageCountryBtn", "nativeElement", "classList", "remove", "i0", "ɵɵdirectiveInject", "i1", "MatDialog", "ChangeDetectorRef", "viewQuery", "EntityCountriesComponent_Query", "rf", "ctx"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-regional/entity-countries.component.ts"], "sourcesContent": ["import { ChangeDetectorRef, Directive, ElementRef, Input, OnInit, ViewChild } from '@angular/core';\nimport { FormControl } from '@angular/forms';\nimport { MatDialog } from '@angular/material/dialog';\nimport { MatTableDataSource } from '@angular/material/table';\nimport { RowAction } from '@skywind-group/lib-swui';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { EntitySettingsModel } from '../../../../../common/models/entity-settings.model';\nimport { codeArrayToObjectReducer } from '../../../../../common/services/entity.service';\nimport { Country } from '../../../../../common/typings';\nimport { CountryItem, MatCountryDialogComponent, MatCountryDialogData } from '../../mat-business-structure/dialogs/mat-country-dialog/mat-country-dialog.component';\nimport { RemoveConfirmDialogComponent } from '../dialogs/remove-confirm-dialog.component';\nimport { StructureEntityModel } from '../../mat-business-structure/structure-entity.model';\n\nexport interface EntityCountry {\n  code: string;\n  displayName: string;\n  isDefault: boolean;\n  isInherited: boolean;\n}\n\n@Directive()\nexport abstract class EntityCountriesComponent implements OnInit {\n  searchControl: FormControl = new FormControl();\n  rowActions: RowAction[];\n  dataSource: MatTableDataSource<EntityCountry>;\n\n  @Input() set disabledList(val: boolean) {\n    this._disabledList = val;\n    this.refreshData();\n  }\n\n  get disabledList(): boolean {\n    return this._disabledList;\n  }\n\n  @Input() entity: StructureEntityModel;\n\n  abstract title: string;\n\n  @ViewChild('manageCountryBtn') private manageCountryBtn: ElementRef;\n\n\n  @Input()\n  set countries(countries: Country[]) {\n    this._countries = countries;\n    this.countriesHash = this._countries.reduce(codeArrayToObjectReducer, {});\n  }\n\n  get countries(): Country[] {\n    return this._countries;\n  }\n\n  protected countriesHash: Object;\n  protected _entitySettings: EntitySettingsModel;\n  protected readonly destroyed$ = new Subject<void>();\n\n  private _countries: Country[];\n  private _disabledList: boolean;\n\n  constructor(private dialog: MatDialog, private cdr: ChangeDetectorRef) {\n    this.setRowActions();\n  }\n\n  ngOnInit() {\n    this.refreshData();\n\n    this.searchControl.valueChanges.pipe(\n      takeUntil(this.destroyed$)\n    ).subscribe((filterValue: string) => {\n      this.dataSource.filter = filterValue?.trim().toLowerCase();\n    });\n  }\n\n  ngOnDestroy(): void {\n    this.destroyed$.next();\n    this.destroyed$.complete();\n  }\n\n  get displayedColumns(): string[] {\n    return ['displayName', 'status', ...(this.disabledList ? [] : ['code'])];\n  }\n\n  showCountryModal($event: Event): void {\n    $event.preventDefault();\n    this.searchControl.reset();\n  }\n\n  abstract removeCountryModal(countryCode: string): void;\n\n  applyFilter(filterValue: string) {\n    this.dataSource.filter = filterValue.trim().toLowerCase();\n  }\n\n  getRowActions(country: EntityCountry): RowAction[] {\n    if (this.disabledList || country.isInherited) {\n      return [];\n    }\n    return this.rowActions;\n  }\n\n  protected buildCountriesModal(countriesType: 'allowed' | 'restricted' | 'blocked') {\n    const data: MatCountryDialogData = {\n      countriesType,\n      entity: this.entity,\n      items: this.buildAvailableCountries()\n    };\n    return this.dialog.open(MatCountryDialogComponent, {\n      disableClose: true,\n      width: '500px',\n      data\n    }).afterClosed();\n  }\n\n  protected abstract buildAvailableCountries(): CountryItem[];\n\n  protected buildRemoveCountryModal(countryCode: string) {\n    return this.dialog.open(RemoveConfirmDialogComponent, {\n      width: '500px',\n      data: { removeCode: countryCode },\n      disableClose: true\n    }).afterClosed();\n  }\n\n  protected refreshData() {\n    this.unFocusButton();\n    this.dataSource = new MatTableDataSource(this.loadData());\n    this.cdr.detectChanges();\n  }\n\n  protected abstract loadData(): EntityCountry[];\n\n  protected abstract setRowActions(): void;\n\n  private unFocusButton() {\n    if (this.manageCountryBtn) {\n      this.manageCountryBtn['_elementRef'].nativeElement.classList.remove('cdk-program-focused');\n    }\n  }\n}\n"], "mappings": "AACA,SAASA,WAAW,QAAQ,gBAAgB;AAE5C,SAASC,kBAAkB,QAAQ,yBAAyB;AAE5D,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,SAAS,QAAQ,gBAAgB;AAE1C,SAASC,wBAAwB,QAAQ,+CAA+C;AAExF,SAAsBC,yBAAyB,QAA8B,sFAAsF;AACnK,SAASC,4BAA4B,QAAQ,4CAA4C;;;;AAWzF,OAAM,MAAgBC,wBAAwB;EAK5C,IAAaC,YAAYA,CAACC,GAAY;IACpC,IAAI,CAACC,aAAa,GAAGD,GAAG;IACxB,IAAI,CAACE,WAAW,EAAE;EACpB;EAEA,IAAIH,YAAYA,CAAA;IACd,OAAO,IAAI,CAACE,aAAa;EAC3B;EASA,IACIE,SAASA,CAACA,SAAoB;IAChC,IAAI,CAACC,UAAU,GAAGD,SAAS;IAC3B,IAAI,CAACE,aAAa,GAAG,IAAI,CAACD,UAAU,CAACE,MAAM,CAACX,wBAAwB,EAAE,EAAE,CAAC;EAC3E;EAEA,IAAIQ,SAASA,CAAA;IACX,OAAO,IAAI,CAACC,UAAU;EACxB;EASAG,YAAoBC,MAAiB,EAAUC,GAAsB;IAAjD,KAAAD,MAAM,GAANA,MAAM;IAAqB,KAAAC,GAAG,GAAHA,GAAG;IArClD,KAAAC,aAAa,GAAgB,IAAInB,WAAW,EAAE;IAgC3B,KAAAoB,UAAU,GAAG,IAAIlB,OAAO,EAAQ;IAMjD,IAAI,CAACmB,aAAa,EAAE;EACtB;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACX,WAAW,EAAE;IAElB,IAAI,CAACQ,aAAa,CAACI,YAAY,CAACC,IAAI,CAClCrB,SAAS,CAAC,IAAI,CAACiB,UAAU,CAAC,CAC3B,CAACK,SAAS,CAAEC,WAAmB,IAAI;MAClC,IAAI,CAACC,UAAU,CAACC,MAAM,GAAGF,WAAW,EAAEG,IAAI,EAAE,CAACC,WAAW,EAAE;IAC5D,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACX,UAAU,CAACY,IAAI,EAAE;IACtB,IAAI,CAACZ,UAAU,CAACa,QAAQ,EAAE;EAC5B;EAEA,IAAIC,gBAAgBA,CAAA;IAClB,OAAO,CAAC,aAAa,EAAE,QAAQ,EAAE,IAAI,IAAI,CAAC1B,YAAY,GAAG,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;EAC1E;EAEA2B,gBAAgBA,CAACC,MAAa;IAC5BA,MAAM,CAACC,cAAc,EAAE;IACvB,IAAI,CAAClB,aAAa,CAACmB,KAAK,EAAE;EAC5B;EAIAC,WAAWA,CAACb,WAAmB;IAC7B,IAAI,CAACC,UAAU,CAACC,MAAM,GAAGF,WAAW,CAACG,IAAI,EAAE,CAACC,WAAW,EAAE;EAC3D;EAEAU,aAAaA,CAACC,OAAsB;IAClC,IAAI,IAAI,CAACjC,YAAY,IAAIiC,OAAO,CAACC,WAAW,EAAE;MAC5C,OAAO,EAAE;IACX;IACA,OAAO,IAAI,CAACC,UAAU;EACxB;EAEUC,mBAAmBA,CAACC,aAAmD;IAC/E,MAAMC,IAAI,GAAyB;MACjCD,aAAa;MACbE,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBC,KAAK,EAAE,IAAI,CAACC,uBAAuB;KACpC;IACD,OAAO,IAAI,CAAChC,MAAM,CAACiC,IAAI,CAAC7C,yBAAyB,EAAE;MACjD8C,YAAY,EAAE,IAAI;MAClBC,KAAK,EAAE,OAAO;MACdN;KACD,CAAC,CAACO,WAAW,EAAE;EAClB;EAIUC,uBAAuBA,CAACC,WAAmB;IACnD,OAAO,IAAI,CAACtC,MAAM,CAACiC,IAAI,CAAC5C,4BAA4B,EAAE;MACpD8C,KAAK,EAAE,OAAO;MACdN,IAAI,EAAE;QAAEU,UAAU,EAAED;MAAW,CAAE;MACjCJ,YAAY,EAAE;KACf,CAAC,CAACE,WAAW,EAAE;EAClB;EAEU1C,WAAWA,CAAA;IACnB,IAAI,CAAC8C,aAAa,EAAE;IACpB,IAAI,CAAC9B,UAAU,GAAG,IAAI1B,kBAAkB,CAAC,IAAI,CAACyD,QAAQ,EAAE,CAAC;IACzD,IAAI,CAACxC,GAAG,CAACyC,aAAa,EAAE;EAC1B;EAMQF,aAAaA,CAAA;IACnB,IAAI,IAAI,CAACG,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAAC,aAAa,CAAC,CAACC,aAAa,CAACC,SAAS,CAACC,MAAM,CAAC,qBAAqB,CAAC;IAC5F;EACF;;;uCApHoBxD,wBAAwB,EAAAyD,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,SAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAI,iBAAA;IAAA;EAAA;;;YAAxB7D,wBAAwB;MAAA8D,SAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}