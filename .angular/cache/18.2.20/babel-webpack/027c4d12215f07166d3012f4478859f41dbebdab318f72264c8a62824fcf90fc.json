{"ast": null, "code": "import { FormControl } from '@angular/forms';\nimport { MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"@ngx-translate/core\";\nimport * as i3 from \"@skywind-group/lib-swui\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"../../../../../../../common/components/clipboard/clipboard.directive\";\nimport * as i6 from \"@angular/material/button\";\nconst _c0 = [\"textArea\"];\nexport class ViewDetailsComponent {\n  constructor({\n    data\n  }, dialogRef, translate, notificationsService) {\n    this.dialogRef = dialogRef;\n    this.translate = translate;\n    this.notificationsService = notificationsService;\n    this.area = new FormControl('');\n    this.data = data;\n    this.area.setValue(JSON.stringify(data, null, 4));\n  }\n  ngAfterViewInit() {\n    this.textArea.nativeElement.setSelectionRange(0, 0);\n  }\n  getCopyContentFn() {\n    return () => this.area.value;\n  }\n  copySuccess() {\n    this.translate.get('INTEGRATIONS.notificationCopy').subscribe(message => this.notificationsService.success(message, ''));\n  }\n  static {\n    this.ɵfac = function ViewDetailsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ViewDetailsComponent)(i0.ɵɵdirectiveInject(MAT_DIALOG_DATA), i0.ɵɵdirectiveInject(i1.MatDialogRef), i0.ɵɵdirectiveInject(i2.TranslateService), i0.ɵɵdirectiveInject(i3.SwuiNotificationsService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ViewDetailsComponent,\n      selectors: [[\"view-details\"]],\n      viewQuery: function ViewDetailsComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.textArea = _t.first);\n        }\n      },\n      decls: 14,\n      vars: 9,\n      consts: [[\"textArea\", \"\"], [\"mat-dialog-title\", \"\"], [1, \"mat-typography\"], [1, \"mb-20\"], [\"clipboard\", \"\", 2, \"float\", \"right\", 3, \"success\", \"textFn\"], [\"cols\", \"15\", \"rows\", \"20\", 1, \"form-control\", 3, \"formControl\"], [\"align\", \"end\"], [\"mat-button\", \"\", \"tabindex\", \"-1\", \"color\", \"primary\", \"type\", \"button\", 3, \"click\"]],\n      template: function ViewDetailsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"h2\", 1);\n          i0.ɵɵtext(1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(2, \"mat-dialog-content\", 2)(3, \"div\", 3)(4, \"a\", 4);\n          i0.ɵɵlistener(\"success\", function ViewDetailsComponent_Template_a_success_4_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.copySuccess());\n          });\n          i0.ɵɵtext(5);\n          i0.ɵɵpipe(6, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"textarea\", 5, 0);\n          i0.ɵɵtext(9, \"      \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(10, \"mat-dialog-actions\", 6)(11, \"button\", 7);\n          i0.ɵɵlistener(\"click\", function ViewDetailsComponent_Template_button_click_11_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.dialogRef.close());\n          });\n          i0.ɵɵtext(12);\n          i0.ɵɵpipe(13, \"translate\");\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate(ctx.data.id);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"textFn\", ctx.getCopyContentFn());\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(6, 5, \"ALL.copyToClipboard\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formControl\", ctx.area);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(13, 7, \"DIALOG.close\"), \" \");\n        }\n      },\n      dependencies: [i4.DefaultValueAccessor, i4.NgControlStatus, i4.FormControlDirective, i5.ClipboardDirective, i6.MatButton, i1.MatDialogTitle, i1.MatDialogActions, i1.MatDialogContent, i2.TranslatePipe],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["FormControl", "MAT_DIALOG_DATA", "ViewDetailsComponent", "constructor", "data", "dialogRef", "translate", "notificationsService", "area", "setValue", "JSON", "stringify", "ngAfterViewInit", "textArea", "nativeElement", "setSelectionRange", "getCopyContentFn", "value", "copySuccess", "get", "subscribe", "message", "success", "i0", "ɵɵdirectiveInject", "i1", "MatDialogRef", "i2", "TranslateService", "i3", "SwuiNotificationsService", "selectors", "viewQuery", "ViewDetailsComponent_Query", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "ViewDetailsComponent_Template_a_success_4_listener", "ɵɵrestoreView", "_r1", "ɵɵresetView", "ViewDetailsComponent_Template_button_click_11_listener", "close", "ɵɵadvance", "ɵɵtextInterpolate", "id", "ɵɵproperty", "ɵɵtextInterpolate1", "ɵɵpipeBind1"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-games/jp-games-info/modals/view-details.component.ts", "/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-games/jp-games-info/modals/view-details.component.html"], "sourcesContent": ["import { AfterViewInit, Component, ElementRef, Inject, ViewChild } from '@angular/core';\nimport { FormControl } from '@angular/forms';\nimport { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';\nimport { TranslateService } from '@ngx-translate/core';\nimport { SwuiNotificationsService } from '@skywind-group/lib-swui';\n\n@Component({\n  selector: 'view-details',\n  templateUrl: './view-details.component.html'\n})\nexport class ViewDetailsComponent implements AfterViewInit {\n  area: FormControl = new FormControl('');\n  data: any;\n  @ViewChild('textArea') textArea: ElementRef;\n\n  constructor( @Inject(MAT_DIALOG_DATA) { data }: any,\n               public dialogRef: MatDialogRef<ViewDetailsComponent>,\n               private translate: TranslateService,\n               private notificationsService: SwuiNotificationsService,\n  ) {\n    this.data = data;\n    this.area.setValue(JSON.stringify(data, null, 4));\n  }\n\n  ngAfterViewInit() {\n    this.textArea.nativeElement.setSelectionRange(0, 0);\n  }\n\n  getCopyContentFn() {\n    return () => this.area.value;\n  }\n\n  copySuccess() {\n    this.translate.get('INTEGRATIONS.notificationCopy')\n      .subscribe(message => this.notificationsService.success(message, ''));\n  }\n}\n", "<h2 mat-dialog-title>{{ data.id }}</h2>\n<mat-dialog-content class=\"mat-typography\">\n  <div class=\"mb-20\">\n    <a clipboard\n       style=\"float: right;\"\n       [textFn]=\"getCopyContentFn()\"\n       (success)=\"copySuccess()\">\n      {{ 'ALL.copyToClipboard' | translate }}\n    </a>\n    <textarea\n      #textArea\n      class=\"form-control\"\n      [formControl]=\"area\"\n      cols=\"15\"\n      rows=\"20\">\n      </textarea>\n  </div>\n</mat-dialog-content>\n<mat-dialog-actions align=\"end\">\n  <button mat-button\n          tabindex=\"-1\"\n          color=\"primary\"\n          type=\"button\" (click)=\"dialogRef.close()\">\n    {{ 'DIALOG.close' | translate }}\n  </button>\n</mat-dialog-actions>\n"], "mappings": "AACA,SAASA,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,eAAe,QAAsB,0BAA0B;;;;;;;;;AAQxE,OAAM,MAAOC,oBAAoB;EAK/BC,YAAsC;IAAEC;EAAI,CAAO,EAC/BC,SAA6C,EAC5CC,SAA2B,EAC3BC,oBAA8C;IAF/C,KAAAF,SAAS,GAATA,SAAS;IACR,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,oBAAoB,GAApBA,oBAAoB;IAPzC,KAAAC,IAAI,GAAgB,IAAIR,WAAW,CAAC,EAAE,CAAC;IASrC,IAAI,CAACI,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACI,IAAI,CAACC,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACP,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;EACnD;EAEAQ,eAAeA,CAAA;IACb,IAAI,CAACC,QAAQ,CAACC,aAAa,CAACC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC;EACrD;EAEAC,gBAAgBA,CAAA;IACd,OAAO,MAAM,IAAI,CAACR,IAAI,CAACS,KAAK;EAC9B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACZ,SAAS,CAACa,GAAG,CAAC,+BAA+B,CAAC,CAChDC,SAAS,CAACC,OAAO,IAAI,IAAI,CAACd,oBAAoB,CAACe,OAAO,CAACD,OAAO,EAAE,EAAE,CAAC,CAAC;EACzE;;;uCAzBWnB,oBAAoB,EAAAqB,EAAA,CAAAC,iBAAA,CAKVvB,eAAe,GAAAsB,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,wBAAA;IAAA;EAAA;;;YALzB5B,oBAAoB;MAAA6B,SAAA;MAAAC,SAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;UCVjCX,EAAA,CAAAa,cAAA,YAAqB;UAAAb,EAAA,CAAAc,MAAA,GAAa;UAAAd,EAAA,CAAAe,YAAA,EAAK;UAGnCf,EAFJ,CAAAa,cAAA,4BAA2C,aACtB,WAIY;UAA1Bb,EAAA,CAAAgB,UAAA,qBAAAC,mDAAA;YAAAjB,EAAA,CAAAkB,aAAA,CAAAC,GAAA;YAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAWR,GAAA,CAAAjB,WAAA,EAAa;UAAA,EAAC;UAC1BK,EAAA,CAAAc,MAAA,GACF;;UAAAd,EAAA,CAAAe,YAAA,EAAI;UACJf,EAAA,CAAAa,cAAA,qBAKY;UACVb,EAAA,CAAAc,MAAA;UAENd,EAFM,CAAAe,YAAA,EAAW,EACT,EACa;UAEnBf,EADF,CAAAa,cAAA,6BAAgC,iBAIoB;UAA5Bb,EAAA,CAAAgB,UAAA,mBAAAK,uDAAA;YAAArB,EAAA,CAAAkB,aAAA,CAAAC,GAAA;YAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASR,GAAA,CAAA9B,SAAA,CAAAwC,KAAA,EAAiB;UAAA,EAAC;UAC/CtB,EAAA,CAAAc,MAAA,IACF;;UACFd,EADE,CAAAe,YAAA,EAAS,EACU;;;UAzBAf,EAAA,CAAAuB,SAAA,EAAa;UAAbvB,EAAA,CAAAwB,iBAAA,CAAAZ,GAAA,CAAA/B,IAAA,CAAA4C,EAAA,CAAa;UAK3BzB,EAAA,CAAAuB,SAAA,GAA6B;UAA7BvB,EAAA,CAAA0B,UAAA,WAAAd,GAAA,CAAAnB,gBAAA,GAA6B;UAE9BO,EAAA,CAAAuB,SAAA,EACF;UADEvB,EAAA,CAAA2B,kBAAA,MAAA3B,EAAA,CAAA4B,WAAA,mCACF;UAIE5B,EAAA,CAAAuB,SAAA,GAAoB;UAApBvB,EAAA,CAAA0B,UAAA,gBAAAd,GAAA,CAAA3B,IAAA,CAAoB;UAWtBe,EAAA,CAAAuB,SAAA,GACF;UADEvB,EAAA,CAAA2B,kBAAA,MAAA3B,EAAA,CAAA4B,WAAA,6BACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}