{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Directive, InjectionToken, inject, Injectable, ComponentFactoryResolver, ApplicationRef, SecurityContext, Injector, Inject, signal, Component, ChangeDetectionStrategy, HostBinding, HostListener, makeEnvironmentProviders, NgModule } from '@angular/core';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport { DOCUMENT, NgIf } from '@angular/common';\nimport { Subject } from 'rxjs';\nimport * as i2 from '@angular/platform-browser';\nconst _c0 = [\"toast-component\", \"\"];\nfunction Toast_button_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 5);\n    i0.ɵɵlistener(\"click\", function Toast_button_0_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.remove());\n    });\n    i0.ɵɵelementStart(1, \"span\", 6);\n    i0.ɵɵtext(2, \"\\xD7\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction Toast_div_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"[\", ctx_r1.duplicatesCount + 1, \"]\");\n  }\n}\nfunction Toast_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, Toast_div_1_ng_container_2_Template, 2, 1, \"ng-container\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r1.options.titleClass);\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.title);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.title, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.duplicatesCount);\n  }\n}\nfunction Toast_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 7);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r1.options.messageClass);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r1.message, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction Toast_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r1.options.messageClass);\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.message);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.message, \" \");\n  }\n}\nfunction Toast_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"div\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"width\", ctx_r1.width() + \"%\");\n  }\n}\nfunction ToastNoAnimation_button_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 5);\n    i0.ɵɵlistener(\"click\", function ToastNoAnimation_button_0_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.remove());\n    });\n    i0.ɵɵelementStart(1, \"span\", 6);\n    i0.ɵɵtext(2, \"\\xD7\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ToastNoAnimation_div_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"[\", ctx_r1.duplicatesCount + 1, \"]\");\n  }\n}\nfunction ToastNoAnimation_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, ToastNoAnimation_div_1_ng_container_2_Template, 2, 1, \"ng-container\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r1.options.titleClass);\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.title);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.title, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.duplicatesCount);\n  }\n}\nfunction ToastNoAnimation_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 7);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r1.options.messageClass);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r1.message, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction ToastNoAnimation_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r1.options.messageClass);\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.message);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.message, \" \");\n  }\n}\nfunction ToastNoAnimation_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"div\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"width\", ctx_r1.width() + \"%\");\n  }\n}\nclass ToastContainerDirective {\n  el;\n  constructor(el) {\n    this.el = el;\n  }\n  getContainerElement() {\n    return this.el.nativeElement;\n  }\n  static ɵfac = function ToastContainerDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ToastContainerDirective)(i0.ɵɵdirectiveInject(i0.ElementRef));\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: ToastContainerDirective,\n    selectors: [[\"\", \"toastContainer\", \"\"]],\n    exportAs: [\"toastContainer\"],\n    standalone: true\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ToastContainerDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[toastContainer]',\n      exportAs: 'toastContainer',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }], null);\n})();\n\n/**\n * A `ComponentPortal` is a portal that instantiates some Component upon attachment.\n */\nclass ComponentPortal {\n  _attachedHost;\n  /** The type of the component that will be instantiated for attachment. */\n  component;\n  /**\n   * [Optional] Where the attached component should live in Angular's *logical* component tree.\n   * This is different from where the component *renders*, which is determined by the PortalHost.\n   * The origin necessary when the host is outside of the Angular application context.\n   */\n  viewContainerRef;\n  /** Injector used for the instantiation of the component. */\n  injector;\n  constructor(component, injector) {\n    this.component = component;\n    this.injector = injector;\n  }\n  /** Attach this portal to a host. */\n  attach(host, newestOnTop) {\n    this._attachedHost = host;\n    return host.attach(this, newestOnTop);\n  }\n  /** Detach this portal from its host */\n  detach() {\n    const host = this._attachedHost;\n    if (host) {\n      this._attachedHost = undefined;\n      return host.detach();\n    }\n  }\n  /** Whether this portal is attached to a host. */\n  get isAttached() {\n    return this._attachedHost != null;\n  }\n  /**\n   * Sets the PortalHost reference without performing `attach()`. This is used directly by\n   * the PortalHost when it is performing an `attach()` or `detach()`.\n   */\n  setAttachedHost(host) {\n    this._attachedHost = host;\n  }\n}\n/**\n * Partial implementation of PortalHost that only deals with attaching a\n * ComponentPortal\n */\nclass BasePortalHost {\n  /** The portal currently attached to the host. */\n  _attachedPortal;\n  /** A function that will permanently dispose this host. */\n  _disposeFn;\n  attach(portal, newestOnTop) {\n    this._attachedPortal = portal;\n    return this.attachComponentPortal(portal, newestOnTop);\n  }\n  detach() {\n    if (this._attachedPortal) {\n      this._attachedPortal.setAttachedHost();\n    }\n    this._attachedPortal = undefined;\n    if (this._disposeFn) {\n      this._disposeFn();\n      this._disposeFn = undefined;\n    }\n  }\n  setDisposeFn(fn) {\n    this._disposeFn = fn;\n  }\n}\n\n/**\n * Reference to a toast opened via the Toastr service.\n */\nclass ToastRef {\n  _overlayRef;\n  /** The instance of component opened into the toast. */\n  componentInstance;\n  /** Count of duplicates of this toast */\n  duplicatesCount = 0;\n  /** Subject for notifying the user that the toast has finished closing. */\n  _afterClosed = new Subject();\n  /** triggered when toast is activated */\n  _activate = new Subject();\n  /** notifies the toast that it should close before the timeout */\n  _manualClose = new Subject();\n  /** notifies the toast that it should reset the timeouts */\n  _resetTimeout = new Subject();\n  /** notifies the toast that it should count a duplicate toast */\n  _countDuplicate = new Subject();\n  constructor(_overlayRef) {\n    this._overlayRef = _overlayRef;\n  }\n  manualClose() {\n    this._manualClose.next();\n    this._manualClose.complete();\n  }\n  manualClosed() {\n    return this._manualClose.asObservable();\n  }\n  timeoutReset() {\n    return this._resetTimeout.asObservable();\n  }\n  countDuplicate() {\n    return this._countDuplicate.asObservable();\n  }\n  /**\n   * Close the toast.\n   */\n  close() {\n    this._overlayRef.detach();\n    this._afterClosed.next();\n    this._manualClose.next();\n    this._afterClosed.complete();\n    this._manualClose.complete();\n    this._activate.complete();\n    this._resetTimeout.complete();\n    this._countDuplicate.complete();\n  }\n  /** Gets an observable that is notified when the toast is finished closing. */\n  afterClosed() {\n    return this._afterClosed.asObservable();\n  }\n  isInactive() {\n    return this._activate.isStopped;\n  }\n  activate() {\n    this._activate.next();\n    this._activate.complete();\n  }\n  /** Gets an observable that is notified when the toast has started opening. */\n  afterActivate() {\n    return this._activate.asObservable();\n  }\n  /** Reset the toast timouts and count duplicates */\n  onDuplicate(resetTimeout, countDuplicate) {\n    if (resetTimeout) {\n      this._resetTimeout.next();\n    }\n    if (countDuplicate) {\n      this._countDuplicate.next(++this.duplicatesCount);\n    }\n  }\n}\n\n/**\n * Everything a toast needs to launch\n */\nclass ToastPackage {\n  toastId;\n  config;\n  message;\n  title;\n  toastType;\n  toastRef;\n  _onTap = new Subject();\n  _onAction = new Subject();\n  constructor(toastId, config, message, title, toastType, toastRef) {\n    this.toastId = toastId;\n    this.config = config;\n    this.message = message;\n    this.title = title;\n    this.toastType = toastType;\n    this.toastRef = toastRef;\n    this.toastRef.afterClosed().subscribe(() => {\n      this._onAction.complete();\n      this._onTap.complete();\n    });\n  }\n  /** Fired on click */\n  triggerTap() {\n    this._onTap.next();\n    if (this.config.tapToDismiss) {\n      this._onTap.complete();\n    }\n  }\n  onTap() {\n    return this._onTap.asObservable();\n  }\n  /** available for use in custom toast */\n  triggerAction(action) {\n    this._onAction.next(action);\n  }\n  onAction() {\n    return this._onAction.asObservable();\n  }\n}\nconst DefaultNoComponentGlobalConfig = {\n  maxOpened: 0,\n  autoDismiss: false,\n  newestOnTop: true,\n  preventDuplicates: false,\n  countDuplicates: false,\n  resetTimeoutOnDuplicate: false,\n  includeTitleDuplicates: false,\n  iconClasses: {\n    error: 'toast-error',\n    info: 'toast-info',\n    success: 'toast-success',\n    warning: 'toast-warning'\n  },\n  // Individual\n  closeButton: false,\n  disableTimeOut: false,\n  timeOut: 5000,\n  extendedTimeOut: 1000,\n  enableHtml: false,\n  progressBar: false,\n  toastClass: 'ngx-toastr',\n  positionClass: 'toast-top-right',\n  titleClass: 'toast-title',\n  messageClass: 'toast-message',\n  easing: 'ease-in',\n  easeTime: 300,\n  tapToDismiss: true,\n  onActivateTick: false,\n  progressAnimation: 'decreasing'\n};\nconst TOAST_CONFIG = new InjectionToken('ToastConfig');\n\n/**\n * A PortalHost for attaching portals to an arbitrary DOM element outside of the Angular\n * application context.\n *\n * This is the only part of the portal core that directly touches the DOM.\n */\nclass DomPortalHost extends BasePortalHost {\n  _hostDomElement;\n  _componentFactoryResolver;\n  _appRef;\n  constructor(_hostDomElement, _componentFactoryResolver, _appRef) {\n    super();\n    this._hostDomElement = _hostDomElement;\n    this._componentFactoryResolver = _componentFactoryResolver;\n    this._appRef = _appRef;\n  }\n  /**\n   * Attach the given ComponentPortal to DOM element using the ComponentFactoryResolver.\n   * @param portal Portal to be attached\n   */\n  attachComponentPortal(portal, newestOnTop) {\n    const componentFactory = this._componentFactoryResolver.resolveComponentFactory(portal.component);\n    let componentRef;\n    // If the portal specifies a ViewContainerRef, we will use that as the attachment point\n    // for the component (in terms of Angular's component tree, not rendering).\n    // When the ViewContainerRef is missing, we use the factory to create the component directly\n    // and then manually attach the ChangeDetector for that component to the application (which\n    // happens automatically when using a ViewContainer).\n    componentRef = componentFactory.create(portal.injector);\n    // When creating a component outside of a ViewContainer, we need to manually register\n    // its ChangeDetector with the application. This API is unfortunately not yet published\n    // in Angular core. The change detector must also be deregistered when the component\n    // is destroyed to prevent memory leaks.\n    this._appRef.attachView(componentRef.hostView);\n    this.setDisposeFn(() => {\n      this._appRef.detachView(componentRef.hostView);\n      componentRef.destroy();\n    });\n    // At this point the component has been instantiated, so we move it to the location in the DOM\n    // where we want it to be rendered.\n    if (newestOnTop) {\n      this._hostDomElement.insertBefore(this._getComponentRootNode(componentRef), this._hostDomElement.firstChild);\n    } else {\n      this._hostDomElement.appendChild(this._getComponentRootNode(componentRef));\n    }\n    return componentRef;\n  }\n  /** Gets the root HTMLElement for an instantiated component. */\n  _getComponentRootNode(componentRef) {\n    return componentRef.hostView.rootNodes[0];\n  }\n}\n\n/** Container inside which all toasts will render. */\nclass OverlayContainer {\n  _document = inject(DOCUMENT);\n  _containerElement;\n  ngOnDestroy() {\n    if (this._containerElement && this._containerElement.parentNode) {\n      this._containerElement.parentNode.removeChild(this._containerElement);\n    }\n  }\n  /**\n   * This method returns the overlay container element. It will lazily\n   * create the element the first time  it is called to facilitate using\n   * the container in non-browser environments.\n   * @returns the container element\n   */\n  getContainerElement() {\n    if (!this._containerElement) {\n      this._createContainer();\n    }\n    return this._containerElement;\n  }\n  /**\n   * Create the overlay container element, which is simply a div\n   * with the 'cdk-overlay-container' class on the document body\n   * and 'aria-live=\"polite\"'\n   */\n  _createContainer() {\n    const container = this._document.createElement('div');\n    container.classList.add('overlay-container');\n    container.setAttribute('aria-live', 'polite');\n    this._document.body.appendChild(container);\n    this._containerElement = container;\n  }\n  static ɵfac = function OverlayContainer_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || OverlayContainer)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: OverlayContainer,\n    factory: OverlayContainer.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayContainer, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n\n/**\n * Reference to an overlay that has been created with the Overlay service.\n * Used to manipulate or dispose of said overlay.\n */\nclass OverlayRef {\n  _portalHost;\n  constructor(_portalHost) {\n    this._portalHost = _portalHost;\n  }\n  attach(portal, newestOnTop = true) {\n    return this._portalHost.attach(portal, newestOnTop);\n  }\n  /**\n   * Detaches an overlay from a portal.\n   * @returns Resolves when the overlay has been detached.\n   */\n  detach() {\n    return this._portalHost.detach();\n  }\n}\n\n/**\n * Service to create Overlays. Overlays are dynamically added pieces of floating UI, meant to be\n * used as a low-level building building block for other components. Dialogs, tooltips, menus,\n * selects, etc. can all be built using overlays. The service should primarily be used by authors\n * of re-usable components rather than developers building end-user applications.\n *\n * An overlay *is* a PortalHost, so any kind of Portal can be loaded into one.\n */\nclass Overlay {\n  _overlayContainer = inject(OverlayContainer);\n  _componentFactoryResolver = inject(ComponentFactoryResolver);\n  _appRef = inject(ApplicationRef);\n  _document = inject(DOCUMENT);\n  // Namespace panes by overlay container\n  _paneElements = new Map();\n  /**\n   * Creates an overlay.\n   * @returns A reference to the created overlay.\n   */\n  create(positionClass, overlayContainer) {\n    // get existing pane if possible\n    return this._createOverlayRef(this.getPaneElement(positionClass, overlayContainer));\n  }\n  getPaneElement(positionClass = '', overlayContainer) {\n    if (!this._paneElements.get(overlayContainer)) {\n      this._paneElements.set(overlayContainer, {});\n    }\n    if (!this._paneElements.get(overlayContainer)[positionClass]) {\n      this._paneElements.get(overlayContainer)[positionClass] = this._createPaneElement(positionClass, overlayContainer);\n    }\n    return this._paneElements.get(overlayContainer)[positionClass];\n  }\n  /**\n   * Creates the DOM element for an overlay and appends it to the overlay container.\n   * @returns Newly-created pane element\n   */\n  _createPaneElement(positionClass, overlayContainer) {\n    const pane = this._document.createElement('div');\n    pane.id = 'toast-container';\n    pane.classList.add(positionClass);\n    pane.classList.add('toast-container');\n    if (!overlayContainer) {\n      this._overlayContainer.getContainerElement().appendChild(pane);\n    } else {\n      overlayContainer.getContainerElement().appendChild(pane);\n    }\n    return pane;\n  }\n  /**\n   * Create a DomPortalHost into which the overlay content can be loaded.\n   * @param pane The DOM element to turn into a portal host.\n   * @returns A portal host for the given DOM element.\n   */\n  _createPortalHost(pane) {\n    return new DomPortalHost(pane, this._componentFactoryResolver, this._appRef);\n  }\n  /**\n   * Creates an OverlayRef for an overlay in the given DOM element.\n   * @param pane DOM element for the overlay\n   */\n  _createOverlayRef(pane) {\n    return new OverlayRef(this._createPortalHost(pane));\n  }\n  static ɵfac = function Overlay_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || Overlay)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: Overlay,\n    factory: Overlay.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Overlay, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass ToastrService {\n  overlay;\n  _injector;\n  sanitizer;\n  ngZone;\n  toastrConfig;\n  currentlyActive = 0;\n  toasts = [];\n  overlayContainer;\n  previousToastMessage;\n  index = 0;\n  constructor(token, overlay, _injector, sanitizer, ngZone) {\n    this.overlay = overlay;\n    this._injector = _injector;\n    this.sanitizer = sanitizer;\n    this.ngZone = ngZone;\n    this.toastrConfig = {\n      ...token.default,\n      ...token.config\n    };\n    if (token.config.iconClasses) {\n      this.toastrConfig.iconClasses = {\n        ...token.default.iconClasses,\n        ...token.config.iconClasses\n      };\n    }\n  }\n  /** show toast */\n  show(message, title, override = {}, type = '') {\n    return this._preBuildNotification(type, message, title, this.applyConfig(override));\n  }\n  /** show successful toast */\n  success(message, title, override = {}) {\n    const type = this.toastrConfig.iconClasses.success || '';\n    return this._preBuildNotification(type, message, title, this.applyConfig(override));\n  }\n  /** show error toast */\n  error(message, title, override = {}) {\n    const type = this.toastrConfig.iconClasses.error || '';\n    return this._preBuildNotification(type, message, title, this.applyConfig(override));\n  }\n  /** show info toast */\n  info(message, title, override = {}) {\n    const type = this.toastrConfig.iconClasses.info || '';\n    return this._preBuildNotification(type, message, title, this.applyConfig(override));\n  }\n  /** show warning toast */\n  warning(message, title, override = {}) {\n    const type = this.toastrConfig.iconClasses.warning || '';\n    return this._preBuildNotification(type, message, title, this.applyConfig(override));\n  }\n  /**\n   * Remove all or a single toast by id\n   */\n  clear(toastId) {\n    // Call every toastRef manualClose function\n    for (const toast of this.toasts) {\n      if (toastId !== undefined) {\n        if (toast.toastId === toastId) {\n          toast.toastRef.manualClose();\n          return;\n        }\n      } else {\n        toast.toastRef.manualClose();\n      }\n    }\n  }\n  /**\n   * Remove and destroy a single toast by id\n   */\n  remove(toastId) {\n    const found = this._findToast(toastId);\n    if (!found) {\n      return false;\n    }\n    found.activeToast.toastRef.close();\n    this.toasts.splice(found.index, 1);\n    this.currentlyActive = this.currentlyActive - 1;\n    if (!this.toastrConfig.maxOpened || !this.toasts.length) {\n      return false;\n    }\n    if (this.currentlyActive < this.toastrConfig.maxOpened && this.toasts[this.currentlyActive]) {\n      const p = this.toasts[this.currentlyActive].toastRef;\n      if (!p.isInactive()) {\n        this.currentlyActive = this.currentlyActive + 1;\n        p.activate();\n      }\n    }\n    return true;\n  }\n  /**\n   * Determines if toast message is already shown\n   */\n  findDuplicate(title = '', message = '', resetOnDuplicate, countDuplicates) {\n    const {\n      includeTitleDuplicates\n    } = this.toastrConfig;\n    for (const toast of this.toasts) {\n      const hasDuplicateTitle = includeTitleDuplicates && toast.title === title;\n      if ((!includeTitleDuplicates || hasDuplicateTitle) && toast.message === message) {\n        toast.toastRef.onDuplicate(resetOnDuplicate, countDuplicates);\n        return toast;\n      }\n    }\n    return null;\n  }\n  /** create a clone of global config and apply individual settings */\n  applyConfig(override = {}) {\n    return {\n      ...this.toastrConfig,\n      ...override\n    };\n  }\n  /**\n   * Find toast object by id\n   */\n  _findToast(toastId) {\n    for (let i = 0; i < this.toasts.length; i++) {\n      if (this.toasts[i].toastId === toastId) {\n        return {\n          index: i,\n          activeToast: this.toasts[i]\n        };\n      }\n    }\n    return null;\n  }\n  /**\n   * Determines the need to run inside angular's zone then builds the toast\n   */\n  _preBuildNotification(toastType, message, title, config) {\n    if (config.onActivateTick) {\n      return this.ngZone.run(() => this._buildNotification(toastType, message, title, config));\n    }\n    return this._buildNotification(toastType, message, title, config);\n  }\n  /**\n   * Creates and attaches toast data to component\n   * returns the active toast, or in case preventDuplicates is enabled the original/non-duplicate active toast.\n   */\n  _buildNotification(toastType, message, title, config) {\n    if (!config.toastComponent) {\n      throw new Error('toastComponent required');\n    }\n    // max opened and auto dismiss = true\n    // if timeout = 0 resetting it would result in setting this.hideTime = Date.now(). Hence, we only want to reset timeout if there is\n    // a timeout at all\n    const duplicate = this.findDuplicate(title, message, this.toastrConfig.resetTimeoutOnDuplicate && config.timeOut > 0, this.toastrConfig.countDuplicates);\n    if ((this.toastrConfig.includeTitleDuplicates && title || message) && this.toastrConfig.preventDuplicates && duplicate !== null) {\n      return duplicate;\n    }\n    this.previousToastMessage = message;\n    let keepInactive = false;\n    if (this.toastrConfig.maxOpened && this.currentlyActive >= this.toastrConfig.maxOpened) {\n      keepInactive = true;\n      if (this.toastrConfig.autoDismiss) {\n        this.clear(this.toasts[0].toastId);\n      }\n    }\n    const overlayRef = this.overlay.create(config.positionClass, this.overlayContainer);\n    this.index = this.index + 1;\n    let sanitizedMessage = message;\n    if (message && config.enableHtml) {\n      sanitizedMessage = this.sanitizer.sanitize(SecurityContext.HTML, message);\n    }\n    const toastRef = new ToastRef(overlayRef);\n    const toastPackage = new ToastPackage(this.index, config, sanitizedMessage, title, toastType, toastRef);\n    /** New injector that contains an instance of `ToastPackage`. */\n    const providers = [{\n      provide: ToastPackage,\n      useValue: toastPackage\n    }];\n    const toastInjector = Injector.create({\n      providers,\n      parent: this._injector\n    });\n    const component = new ComponentPortal(config.toastComponent, toastInjector);\n    const portal = overlayRef.attach(component, config.newestOnTop);\n    toastRef.componentInstance = portal.instance;\n    const ins = {\n      toastId: this.index,\n      title: title || '',\n      message: message || '',\n      toastRef,\n      onShown: toastRef.afterActivate(),\n      onHidden: toastRef.afterClosed(),\n      onTap: toastPackage.onTap(),\n      onAction: toastPackage.onAction(),\n      portal\n    };\n    if (!keepInactive) {\n      this.currentlyActive = this.currentlyActive + 1;\n      setTimeout(() => {\n        ins.toastRef.activate();\n      });\n    }\n    this.toasts.push(ins);\n    return ins;\n  }\n  static ɵfac = function ToastrService_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ToastrService)(i0.ɵɵinject(TOAST_CONFIG), i0.ɵɵinject(Overlay), i0.ɵɵinject(i0.Injector), i0.ɵɵinject(i2.DomSanitizer), i0.ɵɵinject(i0.NgZone));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: ToastrService,\n    factory: ToastrService.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ToastrService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [TOAST_CONFIG]\n    }]\n  }, {\n    type: Overlay\n  }, {\n    type: i0.Injector\n  }, {\n    type: i2.DomSanitizer\n  }, {\n    type: i0.NgZone\n  }], null);\n})();\nclass Toast {\n  toastrService;\n  toastPackage;\n  ngZone;\n  message;\n  title;\n  options;\n  duplicatesCount;\n  originalTimeout;\n  /** width of progress bar */\n  width = signal(-1);\n  /** a combination of toast type and options.toastClass */\n  toastClasses = '';\n  state;\n  /** controls animation */\n  get _state() {\n    return this.state();\n  }\n  /** hides component when waiting to be displayed */\n  get displayStyle() {\n    if (this.state().value === 'inactive') {\n      return 'none';\n    }\n    return;\n  }\n  timeout;\n  intervalId;\n  hideTime;\n  sub;\n  sub1;\n  sub2;\n  sub3;\n  constructor(toastrService, toastPackage, ngZone) {\n    this.toastrService = toastrService;\n    this.toastPackage = toastPackage;\n    this.ngZone = ngZone;\n    this.message = toastPackage.message;\n    this.title = toastPackage.title;\n    this.options = toastPackage.config;\n    this.originalTimeout = toastPackage.config.timeOut;\n    this.toastClasses = `${toastPackage.toastType} ${toastPackage.config.toastClass}`;\n    this.sub = toastPackage.toastRef.afterActivate().subscribe(() => {\n      this.activateToast();\n    });\n    this.sub1 = toastPackage.toastRef.manualClosed().subscribe(() => {\n      this.remove();\n    });\n    this.sub2 = toastPackage.toastRef.timeoutReset().subscribe(() => {\n      this.resetTimeout();\n    });\n    this.sub3 = toastPackage.toastRef.countDuplicate().subscribe(count => {\n      this.duplicatesCount = count;\n    });\n    this.state = signal({\n      value: 'inactive',\n      params: {\n        easeTime: this.toastPackage.config.easeTime,\n        easing: 'ease-in'\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.sub.unsubscribe();\n    this.sub1.unsubscribe();\n    this.sub2.unsubscribe();\n    this.sub3.unsubscribe();\n    clearInterval(this.intervalId);\n    clearTimeout(this.timeout);\n  }\n  /**\n   * activates toast and sets timeout\n   */\n  activateToast() {\n    this.state.update(state => ({\n      ...state,\n      value: 'active'\n    }));\n    if (!(this.options.disableTimeOut === true || this.options.disableTimeOut === 'timeOut') && this.options.timeOut) {\n      this.outsideTimeout(() => this.remove(), this.options.timeOut);\n      this.hideTime = new Date().getTime() + this.options.timeOut;\n      if (this.options.progressBar) {\n        this.outsideInterval(() => this.updateProgress(), 10);\n      }\n    }\n  }\n  /**\n   * updates progress bar width\n   */\n  updateProgress() {\n    if (this.width() === 0 || this.width() === 100 || !this.options.timeOut) {\n      return;\n    }\n    const now = new Date().getTime();\n    const remaining = this.hideTime - now;\n    this.width.set(remaining / this.options.timeOut * 100);\n    if (this.options.progressAnimation === 'increasing') {\n      this.width.update(width => 100 - width);\n    }\n    if (this.width() <= 0) {\n      this.width.set(0);\n    }\n    if (this.width() >= 100) {\n      this.width.set(100);\n    }\n  }\n  resetTimeout() {\n    clearTimeout(this.timeout);\n    clearInterval(this.intervalId);\n    this.state.update(state => ({\n      ...state,\n      value: 'active'\n    }));\n    this.outsideTimeout(() => this.remove(), this.originalTimeout);\n    this.options.timeOut = this.originalTimeout;\n    this.hideTime = new Date().getTime() + (this.options.timeOut || 0);\n    this.width.set(-1);\n    if (this.options.progressBar) {\n      this.outsideInterval(() => this.updateProgress(), 10);\n    }\n  }\n  /**\n   * tells toastrService to remove this toast after animation time\n   */\n  remove() {\n    if (this.state().value === 'removed') {\n      return;\n    }\n    clearTimeout(this.timeout);\n    this.state.update(state => ({\n      ...state,\n      value: 'removed'\n    }));\n    this.outsideTimeout(() => this.toastrService.remove(this.toastPackage.toastId), +this.toastPackage.config.easeTime);\n  }\n  tapToast() {\n    if (this.state().value === 'removed') {\n      return;\n    }\n    this.toastPackage.triggerTap();\n    if (this.options.tapToDismiss) {\n      this.remove();\n    }\n  }\n  stickAround() {\n    if (this.state().value === 'removed') {\n      return;\n    }\n    if (this.options.disableTimeOut !== 'extendedTimeOut') {\n      clearTimeout(this.timeout);\n      this.options.timeOut = 0;\n      this.hideTime = 0;\n      // disable progressBar\n      clearInterval(this.intervalId);\n      this.width.set(0);\n    }\n  }\n  delayedHideToast() {\n    if (this.options.disableTimeOut === true || this.options.disableTimeOut === 'extendedTimeOut' || this.options.extendedTimeOut === 0 || this.state().value === 'removed') {\n      return;\n    }\n    this.outsideTimeout(() => this.remove(), this.options.extendedTimeOut);\n    this.options.timeOut = this.options.extendedTimeOut;\n    this.hideTime = new Date().getTime() + (this.options.timeOut || 0);\n    this.width.set(-1);\n    if (this.options.progressBar) {\n      this.outsideInterval(() => this.updateProgress(), 10);\n    }\n  }\n  outsideTimeout(func, timeout) {\n    if (this.ngZone) {\n      this.ngZone.runOutsideAngular(() => this.timeout = setTimeout(() => this.runInsideAngular(func), timeout));\n    } else {\n      this.timeout = setTimeout(() => func(), timeout);\n    }\n  }\n  outsideInterval(func, timeout) {\n    if (this.ngZone) {\n      this.ngZone.runOutsideAngular(() => this.intervalId = setInterval(() => this.runInsideAngular(func), timeout));\n    } else {\n      this.intervalId = setInterval(() => func(), timeout);\n    }\n  }\n  runInsideAngular(func) {\n    if (this.ngZone) {\n      this.ngZone.run(() => func());\n    } else {\n      func();\n    }\n  }\n  static ɵfac = function Toast_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || Toast)(i0.ɵɵdirectiveInject(ToastrService), i0.ɵɵdirectiveInject(ToastPackage), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: Toast,\n    selectors: [[\"\", \"toast-component\", \"\"]],\n    hostVars: 5,\n    hostBindings: function Toast_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function Toast_click_HostBindingHandler() {\n          return ctx.tapToast();\n        })(\"mouseenter\", function Toast_mouseenter_HostBindingHandler() {\n          return ctx.stickAround();\n        })(\"mouseleave\", function Toast_mouseleave_HostBindingHandler() {\n          return ctx.delayedHideToast();\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵsyntheticHostProperty(\"@flyInOut\", ctx._state);\n        i0.ɵɵclassMap(ctx.toastClasses);\n        i0.ɵɵstyleProp(\"display\", ctx.displayStyle);\n      }\n    },\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    attrs: _c0,\n    decls: 5,\n    vars: 5,\n    consts: [[\"type\", \"button\", \"class\", \"toast-close-button\", \"aria-label\", \"Close\", 3, \"click\", 4, \"ngIf\"], [3, \"class\", 4, \"ngIf\"], [\"role\", \"alert\", 3, \"class\", \"innerHTML\", 4, \"ngIf\"], [\"role\", \"alert\", 3, \"class\", 4, \"ngIf\"], [4, \"ngIf\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"toast-close-button\", 3, \"click\"], [\"aria-hidden\", \"true\"], [\"role\", \"alert\", 3, \"innerHTML\"], [\"role\", \"alert\"], [1, \"toast-progress\"]],\n    template: function Toast_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, Toast_button_0_Template, 3, 0, \"button\", 0)(1, Toast_div_1_Template, 3, 5, \"div\", 1)(2, Toast_div_2_Template, 1, 3, \"div\", 2)(3, Toast_div_3_Template, 2, 4, \"div\", 3)(4, Toast_div_4_Template, 2, 2, \"div\", 4);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.options.closeButton);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.title);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.message && ctx.options.enableHtml);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.message && !ctx.options.enableHtml);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.options.progressBar);\n      }\n    },\n    dependencies: [NgIf],\n    encapsulation: 2,\n    data: {\n      animation: [trigger('flyInOut', [state('inactive', style({\n        opacity: 0\n      })), state('active', style({\n        opacity: 1\n      })), state('removed', style({\n        opacity: 0\n      })), transition('inactive => active', animate('{{ easeTime }}ms {{ easing }}')), transition('active => removed', animate('{{ easeTime }}ms {{ easing }}'))])]\n    },\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Toast, [{\n    type: Component,\n    args: [{\n      selector: '[toast-component]',\n      template: `\n  <button *ngIf=\"options.closeButton\" (click)=\"remove()\" type=\"button\" class=\"toast-close-button\" aria-label=\"Close\">\n    <span aria-hidden=\"true\">&times;</span>\n  </button>\n  <div *ngIf=\"title\" [class]=\"options.titleClass\" [attr.aria-label]=\"title\">\n    {{ title }} <ng-container *ngIf=\"duplicatesCount\">[{{ duplicatesCount + 1 }}]</ng-container>\n  </div>\n  <div *ngIf=\"message && options.enableHtml\" role=\"alert\"\n    [class]=\"options.messageClass\" [innerHTML]=\"message\">\n  </div>\n  <div *ngIf=\"message && !options.enableHtml\" role=\"alert\"\n    [class]=\"options.messageClass\" [attr.aria-label]=\"message\">\n    {{ message }}\n  </div>\n  <div *ngIf=\"options.progressBar\">\n    <div class=\"toast-progress\" [style.width]=\"width() + '%'\"></div>\n  </div>\n  `,\n      animations: [trigger('flyInOut', [state('inactive', style({\n        opacity: 0\n      })), state('active', style({\n        opacity: 1\n      })), state('removed', style({\n        opacity: 0\n      })), transition('inactive => active', animate('{{ easeTime }}ms {{ easing }}')), transition('active => removed', animate('{{ easeTime }}ms {{ easing }}'))])],\n      preserveWhitespaces: false,\n      standalone: true,\n      imports: [NgIf],\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], () => [{\n    type: ToastrService\n  }, {\n    type: ToastPackage\n  }, {\n    type: i0.NgZone\n  }], {\n    toastClasses: [{\n      type: HostBinding,\n      args: ['class']\n    }],\n    _state: [{\n      type: HostBinding,\n      args: ['@flyInOut']\n    }],\n    displayStyle: [{\n      type: HostBinding,\n      args: ['style.display']\n    }],\n    tapToast: [{\n      type: HostListener,\n      args: ['click']\n    }],\n    stickAround: [{\n      type: HostListener,\n      args: ['mouseenter']\n    }],\n    delayedHideToast: [{\n      type: HostListener,\n      args: ['mouseleave']\n    }]\n  });\n})();\nconst DefaultGlobalConfig = {\n  ...DefaultNoComponentGlobalConfig,\n  toastComponent: Toast\n};\n/**\n * @description\n * Provides the `TOAST_CONFIG` token with the given config.\n *\n * @param config The config to configure toastr.\n * @returns The environment providers.\n *\n * @example\n * ```ts\n * import { provideToastr } from 'ngx-toastr';\n *\n * bootstrap(AppComponent, {\n *   providers: [\n *     provideToastr({\n *       timeOut: 2000,\n *       positionClass: 'toast-top-right',\n *     }),\n *   ],\n * })\n */\nconst provideToastr = (config = {}) => {\n  const providers = [{\n    provide: TOAST_CONFIG,\n    useValue: {\n      default: DefaultGlobalConfig,\n      config\n    }\n  }];\n  return makeEnvironmentProviders(providers);\n};\nclass ToastrModule {\n  static forRoot(config = {}) {\n    return {\n      ngModule: ToastrModule,\n      providers: [provideToastr(config)]\n    };\n  }\n  static ɵfac = function ToastrModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ToastrModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: ToastrModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ToastrModule, [{\n    type: NgModule,\n    args: [{\n      imports: [Toast],\n      exports: [Toast]\n    }]\n  }], null, null);\n})();\nclass ToastrComponentlessModule {\n  static forRoot(config = {}) {\n    return {\n      ngModule: ToastrModule,\n      providers: [{\n        provide: TOAST_CONFIG,\n        useValue: {\n          default: DefaultNoComponentGlobalConfig,\n          config\n        }\n      }]\n    };\n  }\n  static ɵfac = function ToastrComponentlessModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ToastrComponentlessModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: ToastrComponentlessModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ToastrComponentlessModule, [{\n    type: NgModule,\n    args: [{}]\n  }], null, null);\n})();\nclass ToastNoAnimation {\n  toastrService;\n  toastPackage;\n  appRef;\n  message;\n  title;\n  options;\n  duplicatesCount;\n  originalTimeout;\n  /** width of progress bar */\n  width = signal(-1);\n  /** a combination of toast type and options.toastClass */\n  toastClasses = '';\n  /** hides component when waiting to be displayed */\n  get displayStyle() {\n    if (this.state() === 'inactive') {\n      return 'none';\n    }\n    return null;\n  }\n  /** controls animation */\n  state = signal('inactive');\n  timeout;\n  intervalId;\n  hideTime;\n  sub;\n  sub1;\n  sub2;\n  sub3;\n  constructor(toastrService, toastPackage, appRef) {\n    this.toastrService = toastrService;\n    this.toastPackage = toastPackage;\n    this.appRef = appRef;\n    this.message = toastPackage.message;\n    this.title = toastPackage.title;\n    this.options = toastPackage.config;\n    this.originalTimeout = toastPackage.config.timeOut;\n    this.toastClasses = `${toastPackage.toastType} ${toastPackage.config.toastClass}`;\n    this.sub = toastPackage.toastRef.afterActivate().subscribe(() => {\n      this.activateToast();\n    });\n    this.sub1 = toastPackage.toastRef.manualClosed().subscribe(() => {\n      this.remove();\n    });\n    this.sub2 = toastPackage.toastRef.timeoutReset().subscribe(() => {\n      this.resetTimeout();\n    });\n    this.sub3 = toastPackage.toastRef.countDuplicate().subscribe(count => {\n      this.duplicatesCount = count;\n    });\n  }\n  ngOnDestroy() {\n    this.sub.unsubscribe();\n    this.sub1.unsubscribe();\n    this.sub2.unsubscribe();\n    this.sub3.unsubscribe();\n    clearInterval(this.intervalId);\n    clearTimeout(this.timeout);\n  }\n  /**\n   * activates toast and sets timeout\n   */\n  activateToast() {\n    this.state.set('active');\n    if (!(this.options.disableTimeOut === true || this.options.disableTimeOut === 'timeOut') && this.options.timeOut) {\n      this.timeout = setTimeout(() => {\n        this.remove();\n      }, this.options.timeOut);\n      this.hideTime = new Date().getTime() + this.options.timeOut;\n      if (this.options.progressBar) {\n        this.intervalId = setInterval(() => this.updateProgress(), 10);\n      }\n    }\n    if (this.options.onActivateTick) {\n      this.appRef.tick();\n    }\n  }\n  /**\n   * updates progress bar width\n   */\n  updateProgress() {\n    if (this.width() === 0 || this.width() === 100 || !this.options.timeOut) {\n      return;\n    }\n    const now = new Date().getTime();\n    const remaining = this.hideTime - now;\n    this.width.set(remaining / this.options.timeOut * 100);\n    if (this.options.progressAnimation === 'increasing') {\n      this.width.update(width => 100 - width);\n    }\n    if (this.width() <= 0) {\n      this.width.set(0);\n    }\n    if (this.width() >= 100) {\n      this.width.set(100);\n    }\n  }\n  resetTimeout() {\n    clearTimeout(this.timeout);\n    clearInterval(this.intervalId);\n    this.state.set('active');\n    this.options.timeOut = this.originalTimeout;\n    this.timeout = setTimeout(() => this.remove(), this.originalTimeout);\n    this.hideTime = new Date().getTime() + (this.originalTimeout || 0);\n    this.width.set(-1);\n    if (this.options.progressBar) {\n      this.intervalId = setInterval(() => this.updateProgress(), 10);\n    }\n  }\n  /**\n   * tells toastrService to remove this toast after animation time\n   */\n  remove() {\n    if (this.state() === 'removed') {\n      return;\n    }\n    clearTimeout(this.timeout);\n    this.state.set('removed');\n    this.timeout = setTimeout(() => this.toastrService.remove(this.toastPackage.toastId));\n  }\n  tapToast() {\n    if (this.state() === 'removed') {\n      return;\n    }\n    this.toastPackage.triggerTap();\n    if (this.options.tapToDismiss) {\n      this.remove();\n    }\n  }\n  stickAround() {\n    if (this.state() === 'removed') {\n      return;\n    }\n    clearTimeout(this.timeout);\n    this.options.timeOut = 0;\n    this.hideTime = 0;\n    // disable progressBar\n    clearInterval(this.intervalId);\n    this.width.set(0);\n  }\n  delayedHideToast() {\n    if (this.options.disableTimeOut === true || this.options.disableTimeOut === 'extendedTimeOut' || this.options.extendedTimeOut === 0 || this.state() === 'removed') {\n      return;\n    }\n    this.timeout = setTimeout(() => this.remove(), this.options.extendedTimeOut);\n    this.options.timeOut = this.options.extendedTimeOut;\n    this.hideTime = new Date().getTime() + (this.options.timeOut || 0);\n    this.width.set(-1);\n    if (this.options.progressBar) {\n      this.intervalId = setInterval(() => this.updateProgress(), 10);\n    }\n  }\n  static ɵfac = function ToastNoAnimation_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ToastNoAnimation)(i0.ɵɵdirectiveInject(ToastrService), i0.ɵɵdirectiveInject(ToastPackage), i0.ɵɵdirectiveInject(i0.ApplicationRef));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: ToastNoAnimation,\n    selectors: [[\"\", \"toast-component\", \"\"]],\n    hostVars: 4,\n    hostBindings: function ToastNoAnimation_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function ToastNoAnimation_click_HostBindingHandler() {\n          return ctx.tapToast();\n        })(\"mouseenter\", function ToastNoAnimation_mouseenter_HostBindingHandler() {\n          return ctx.stickAround();\n        })(\"mouseleave\", function ToastNoAnimation_mouseleave_HostBindingHandler() {\n          return ctx.delayedHideToast();\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵclassMap(ctx.toastClasses);\n        i0.ɵɵstyleProp(\"display\", ctx.displayStyle);\n      }\n    },\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    attrs: _c0,\n    decls: 5,\n    vars: 5,\n    consts: [[\"type\", \"button\", \"class\", \"toast-close-button\", \"aria-label\", \"Close\", 3, \"click\", 4, \"ngIf\"], [3, \"class\", 4, \"ngIf\"], [\"role\", \"alert\", 3, \"class\", \"innerHTML\", 4, \"ngIf\"], [\"role\", \"alert\", 3, \"class\", 4, \"ngIf\"], [4, \"ngIf\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"toast-close-button\", 3, \"click\"], [\"aria-hidden\", \"true\"], [\"role\", \"alert\", 3, \"innerHTML\"], [\"role\", \"alert\"], [1, \"toast-progress\"]],\n    template: function ToastNoAnimation_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, ToastNoAnimation_button_0_Template, 3, 0, \"button\", 0)(1, ToastNoAnimation_div_1_Template, 3, 5, \"div\", 1)(2, ToastNoAnimation_div_2_Template, 1, 3, \"div\", 2)(3, ToastNoAnimation_div_3_Template, 2, 4, \"div\", 3)(4, ToastNoAnimation_div_4_Template, 2, 2, \"div\", 4);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.options.closeButton);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.title);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.message && ctx.options.enableHtml);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.message && !ctx.options.enableHtml);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.options.progressBar);\n      }\n    },\n    dependencies: [NgIf],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ToastNoAnimation, [{\n    type: Component,\n    args: [{\n      selector: '[toast-component]',\n      template: `\n  <button *ngIf=\"options.closeButton\" (click)=\"remove()\" type=\"button\" class=\"toast-close-button\" aria-label=\"Close\">\n    <span aria-hidden=\"true\">&times;</span>\n  </button>\n  <div *ngIf=\"title\" [class]=\"options.titleClass\" [attr.aria-label]=\"title\">\n    {{ title }} <ng-container *ngIf=\"duplicatesCount\">[{{ duplicatesCount + 1 }}]</ng-container>\n  </div>\n  <div *ngIf=\"message && options.enableHtml\" role=\"alert\"\n    [class]=\"options.messageClass\" [innerHTML]=\"message\">\n  </div>\n  <div *ngIf=\"message && !options.enableHtml\" role=\"alert\"\n    [class]=\"options.messageClass\" [attr.aria-label]=\"message\">\n    {{ message }}\n  </div>\n  <div *ngIf=\"options.progressBar\">\n    <div class=\"toast-progress\" [style.width]=\"width() + '%'\"></div>\n  </div>\n  `,\n      standalone: true,\n      imports: [NgIf],\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], () => [{\n    type: ToastrService\n  }, {\n    type: ToastPackage\n  }, {\n    type: i0.ApplicationRef\n  }], {\n    toastClasses: [{\n      type: HostBinding,\n      args: ['class']\n    }],\n    displayStyle: [{\n      type: HostBinding,\n      args: ['style.display']\n    }],\n    tapToast: [{\n      type: HostListener,\n      args: ['click']\n    }],\n    stickAround: [{\n      type: HostListener,\n      args: ['mouseenter']\n    }],\n    delayedHideToast: [{\n      type: HostListener,\n      args: ['mouseleave']\n    }]\n  });\n})();\nconst DefaultNoAnimationsGlobalConfig = {\n  ...DefaultNoComponentGlobalConfig,\n  toastComponent: ToastNoAnimation\n};\nclass ToastNoAnimationModule {\n  static forRoot(config = {}) {\n    return {\n      ngModule: ToastNoAnimationModule,\n      providers: [{\n        provide: TOAST_CONFIG,\n        useValue: {\n          default: DefaultNoAnimationsGlobalConfig,\n          config\n        }\n      }]\n    };\n  }\n  static ɵfac = function ToastNoAnimationModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ToastNoAnimationModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: ToastNoAnimationModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ToastNoAnimationModule, [{\n    type: NgModule,\n    args: [{\n      imports: [ToastNoAnimation],\n      exports: [ToastNoAnimation]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BasePortalHost, ComponentPortal, DefaultGlobalConfig, DefaultNoAnimationsGlobalConfig, DefaultNoComponentGlobalConfig, Overlay, OverlayContainer, OverlayRef, TOAST_CONFIG, Toast, ToastContainerDirective, ToastNoAnimation, ToastNoAnimationModule, ToastPackage, ToastRef, ToastrComponentlessModule, ToastrModule, ToastrService, provideToastr };", "map": {"version": 3, "names": ["i0", "Directive", "InjectionToken", "inject", "Injectable", "ComponentFactoryResolver", "ApplicationRef", "SecurityContext", "Injector", "Inject", "signal", "Component", "ChangeDetectionStrategy", "HostBinding", "HostListener", "makeEnvironmentProviders", "NgModule", "trigger", "state", "style", "transition", "animate", "DOCUMENT", "NgIf", "Subject", "i2", "_c0", "Toast_button_0_Template", "rf", "ctx", "_r1", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "Toast_button_0_Template_button_click_0_listener", "ɵɵrestoreView", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "remove", "ɵɵtext", "ɵɵelementEnd", "Toast_div_1_ng_container_2_Template", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "duplicatesCount", "Toast_div_1_Template", "ɵɵtemplate", "ɵɵclassMap", "options", "titleClass", "ɵɵattribute", "title", "ɵɵproperty", "Toast_div_2_Template", "ɵɵelement", "messageClass", "message", "ɵɵsanitizeHtml", "Toast_div_3_Template", "Toast_div_4_Template", "ɵɵstyleProp", "width", "ToastNoAnimation_button_0_Template", "ToastNoAnimation_button_0_Template_button_click_0_listener", "ToastNoAnimation_div_1_ng_container_2_Template", "ToastNoAnimation_div_1_Template", "ToastNoAnimation_div_2_Template", "ToastNoAnimation_div_3_Template", "ToastNoAnimation_div_4_Template", "ToastContainerDirective", "el", "constructor", "getContainerElement", "nativeElement", "ɵfac", "ToastContainerDirective_Factory", "__ngFactoryType__", "ɵɵdirectiveInject", "ElementRef", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "exportAs", "standalone", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "ComponentPortal", "_attachedHost", "component", "viewContainerRef", "injector", "attach", "host", "newestOnTop", "detach", "undefined", "isAttached", "setAttachedHost", "BasePortalHost", "_attachedPortal", "_disposeFn", "portal", "attachComponentPortal", "setDisposeFn", "fn", "ToastRef", "_overlayRef", "componentInstance", "_afterClosed", "_activate", "_manualClose", "_resetTimeout", "_countDuplicate", "manualClose", "next", "complete", "manualClosed", "asObservable", "timeoutReset", "countDuplicate", "close", "afterClosed", "isInactive", "isStopped", "activate", "afterActivate", "onDuplicate", "resetTimeout", "ToastPackage", "toastId", "config", "toastType", "toastRef", "_onTap", "_onAction", "subscribe", "triggerTap", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onTap", "triggerAction", "action", "onAction", "DefaultNoComponentGlobalConfig", "maxOpened", "autoDismiss", "preventDuplicates", "countDuplicates", "resetTimeoutOnDuplicate", "includeTitleDuplicates", "iconClasses", "error", "info", "success", "warning", "closeButton", "disableTimeOut", "timeOut", "extendedTimeOut", "enableHtml", "progressBar", "toastClass", "positionClass", "easing", "easeTime", "onActivateTick", "progressAnimation", "TOAST_CONFIG", "DomPortalHost", "_hostDomElement", "_componentFactoryResolver", "_appRef", "componentFactory", "resolveComponentFactory", "componentRef", "create", "attachView", "<PERSON><PERSON><PERSON><PERSON>", "detach<PERSON>iew", "destroy", "insertBefore", "_getComponentRootNode", "<PERSON><PERSON><PERSON><PERSON>", "append<PERSON><PERSON><PERSON>", "rootNodes", "OverlayContainer", "_document", "_containerElement", "ngOnDestroy", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "_createContainer", "container", "createElement", "classList", "add", "setAttribute", "body", "OverlayContainer_Factory", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "OverlayRef", "_portalHost", "Overlay", "_overlayContainer", "_paneElements", "Map", "overlayContainer", "_createOverlayRef", "getPaneElement", "get", "set", "_createPaneElement", "pane", "id", "_createPortalHost", "Overlay_Factory", "ToastrService", "overlay", "_injector", "sanitizer", "ngZone", "toastrConfig", "currentlyActive", "toasts", "previousToastMessage", "index", "default", "show", "override", "_preBuildNotification", "applyConfig", "clear", "toast", "found", "_findToast", "activeToast", "splice", "length", "p", "findDuplicate", "resetOnDuplicate", "hasDuplicateTitle", "i", "run", "_buildNotification", "toastComponent", "Error", "duplicate", "keepInactive", "overlayRef", "sanitizedMessage", "sanitize", "HTML", "toastPackage", "providers", "provide", "useValue", "toastInjector", "parent", "instance", "ins", "onShown", "onHidden", "setTimeout", "push", "ToastrService_Factory", "ɵɵinject", "Dom<PERSON><PERSON><PERSON>zer", "NgZone", "decorators", "Toast", "toastrService", "originalTimeout", "toastClasses", "_state", "displayStyle", "value", "timeout", "intervalId", "hideTime", "sub", "sub1", "sub2", "sub3", "activateToast", "count", "params", "unsubscribe", "clearInterval", "clearTimeout", "update", "outsideTimeout", "Date", "getTime", "outsideInterval", "updateProgress", "now", "remaining", "tapToast", "stickAround", "delayedHideToast", "func", "runOutsideAngular", "runInsideAngular", "setInterval", "Toast_Factory", "ɵcmp", "ɵɵdefineComponent", "hostVars", "hostBindings", "Toast_HostBindings", "Toast_click_HostBindingHandler", "Toast_mouseenter_HostBindingHandler", "Toast_mouseleave_HostBindingHandler", "ɵɵsyntheticHostProperty", "features", "ɵɵStandaloneFeature", "attrs", "decls", "vars", "consts", "template", "Toast_Template", "dependencies", "encapsulation", "data", "animation", "opacity", "changeDetection", "animations", "preserveWhitespaces", "imports", "OnPush", "DefaultGlobalConfig", "provideToastr", "ToastrModule", "forRoot", "ngModule", "ToastrModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "exports", "ToastrComponentlessModule", "ToastrComponentlessModule_Factory", "ToastNoAnimation", "appRef", "tick", "ToastNoAnimation_Factory", "ToastNoAnimation_HostBindings", "ToastNoAnimation_click_HostBindingHandler", "ToastNoAnimation_mouseenter_HostBindingHandler", "ToastNoAnimation_mouseleave_HostBindingHandler", "ToastNoAnimation_Template", "DefaultNoAnimationsGlobalConfig", "ToastNoAnimationModule", "ToastNoAnimationModule_Factory"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/node_modules/ngx-toastr/fesm2022/ngx-toastr.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Directive, InjectionToken, inject, Injectable, ComponentFactoryResolver, ApplicationRef, SecurityContext, Injector, Inject, signal, Component, ChangeDetectionStrategy, HostBinding, HostListener, makeEnvironmentProviders, NgModule } from '@angular/core';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport { DOCUMENT, NgIf } from '@angular/common';\nimport { Subject } from 'rxjs';\nimport * as i2 from '@angular/platform-browser';\n\nclass ToastContainerDirective {\n    el;\n    constructor(el) {\n        this.el = el;\n    }\n    getContainerElement() {\n        return this.el.nativeElement;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: ToastContainerDirective, deps: [{ token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"18.0.0\", type: ToastContainerDirective, isStandalone: true, selector: \"[toastContainer]\", exportAs: [\"toastContainer\"], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: ToastContainerDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[toastContainer]',\n                    exportAs: 'toastContainer',\n                    standalone: true\n                }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }] });\n\n/**\n * A `ComponentPortal` is a portal that instantiates some Component upon attachment.\n */\nclass ComponentPortal {\n    _attachedHost;\n    /** The type of the component that will be instantiated for attachment. */\n    component;\n    /**\n     * [Optional] Where the attached component should live in Angular's *logical* component tree.\n     * This is different from where the component *renders*, which is determined by the PortalHost.\n     * The origin necessary when the host is outside of the Angular application context.\n     */\n    viewContainerRef;\n    /** Injector used for the instantiation of the component. */\n    injector;\n    constructor(component, injector) {\n        this.component = component;\n        this.injector = injector;\n    }\n    /** Attach this portal to a host. */\n    attach(host, newestOnTop) {\n        this._attachedHost = host;\n        return host.attach(this, newestOnTop);\n    }\n    /** Detach this portal from its host */\n    detach() {\n        const host = this._attachedHost;\n        if (host) {\n            this._attachedHost = undefined;\n            return host.detach();\n        }\n    }\n    /** Whether this portal is attached to a host. */\n    get isAttached() {\n        return this._attachedHost != null;\n    }\n    /**\n     * Sets the PortalHost reference without performing `attach()`. This is used directly by\n     * the PortalHost when it is performing an `attach()` or `detach()`.\n     */\n    setAttachedHost(host) {\n        this._attachedHost = host;\n    }\n}\n/**\n * Partial implementation of PortalHost that only deals with attaching a\n * ComponentPortal\n */\nclass BasePortalHost {\n    /** The portal currently attached to the host. */\n    _attachedPortal;\n    /** A function that will permanently dispose this host. */\n    _disposeFn;\n    attach(portal, newestOnTop) {\n        this._attachedPortal = portal;\n        return this.attachComponentPortal(portal, newestOnTop);\n    }\n    detach() {\n        if (this._attachedPortal) {\n            this._attachedPortal.setAttachedHost();\n        }\n        this._attachedPortal = undefined;\n        if (this._disposeFn) {\n            this._disposeFn();\n            this._disposeFn = undefined;\n        }\n    }\n    setDisposeFn(fn) {\n        this._disposeFn = fn;\n    }\n}\n\n/**\n * Reference to a toast opened via the Toastr service.\n */\nclass ToastRef {\n    _overlayRef;\n    /** The instance of component opened into the toast. */\n    componentInstance;\n    /** Count of duplicates of this toast */\n    duplicatesCount = 0;\n    /** Subject for notifying the user that the toast has finished closing. */\n    _afterClosed = new Subject();\n    /** triggered when toast is activated */\n    _activate = new Subject();\n    /** notifies the toast that it should close before the timeout */\n    _manualClose = new Subject();\n    /** notifies the toast that it should reset the timeouts */\n    _resetTimeout = new Subject();\n    /** notifies the toast that it should count a duplicate toast */\n    _countDuplicate = new Subject();\n    constructor(_overlayRef) {\n        this._overlayRef = _overlayRef;\n    }\n    manualClose() {\n        this._manualClose.next();\n        this._manualClose.complete();\n    }\n    manualClosed() {\n        return this._manualClose.asObservable();\n    }\n    timeoutReset() {\n        return this._resetTimeout.asObservable();\n    }\n    countDuplicate() {\n        return this._countDuplicate.asObservable();\n    }\n    /**\n     * Close the toast.\n     */\n    close() {\n        this._overlayRef.detach();\n        this._afterClosed.next();\n        this._manualClose.next();\n        this._afterClosed.complete();\n        this._manualClose.complete();\n        this._activate.complete();\n        this._resetTimeout.complete();\n        this._countDuplicate.complete();\n    }\n    /** Gets an observable that is notified when the toast is finished closing. */\n    afterClosed() {\n        return this._afterClosed.asObservable();\n    }\n    isInactive() {\n        return this._activate.isStopped;\n    }\n    activate() {\n        this._activate.next();\n        this._activate.complete();\n    }\n    /** Gets an observable that is notified when the toast has started opening. */\n    afterActivate() {\n        return this._activate.asObservable();\n    }\n    /** Reset the toast timouts and count duplicates */\n    onDuplicate(resetTimeout, countDuplicate) {\n        if (resetTimeout) {\n            this._resetTimeout.next();\n        }\n        if (countDuplicate) {\n            this._countDuplicate.next(++this.duplicatesCount);\n        }\n    }\n}\n\n/**\n * Everything a toast needs to launch\n */\nclass ToastPackage {\n    toastId;\n    config;\n    message;\n    title;\n    toastType;\n    toastRef;\n    _onTap = new Subject();\n    _onAction = new Subject();\n    constructor(toastId, config, message, title, toastType, toastRef) {\n        this.toastId = toastId;\n        this.config = config;\n        this.message = message;\n        this.title = title;\n        this.toastType = toastType;\n        this.toastRef = toastRef;\n        this.toastRef.afterClosed().subscribe(() => {\n            this._onAction.complete();\n            this._onTap.complete();\n        });\n    }\n    /** Fired on click */\n    triggerTap() {\n        this._onTap.next();\n        if (this.config.tapToDismiss) {\n            this._onTap.complete();\n        }\n    }\n    onTap() {\n        return this._onTap.asObservable();\n    }\n    /** available for use in custom toast */\n    triggerAction(action) {\n        this._onAction.next(action);\n    }\n    onAction() {\n        return this._onAction.asObservable();\n    }\n}\nconst DefaultNoComponentGlobalConfig = {\n    maxOpened: 0,\n    autoDismiss: false,\n    newestOnTop: true,\n    preventDuplicates: false,\n    countDuplicates: false,\n    resetTimeoutOnDuplicate: false,\n    includeTitleDuplicates: false,\n    iconClasses: {\n        error: 'toast-error',\n        info: 'toast-info',\n        success: 'toast-success',\n        warning: 'toast-warning',\n    },\n    // Individual\n    closeButton: false,\n    disableTimeOut: false,\n    timeOut: 5000,\n    extendedTimeOut: 1000,\n    enableHtml: false,\n    progressBar: false,\n    toastClass: 'ngx-toastr',\n    positionClass: 'toast-top-right',\n    titleClass: 'toast-title',\n    messageClass: 'toast-message',\n    easing: 'ease-in',\n    easeTime: 300,\n    tapToDismiss: true,\n    onActivateTick: false,\n    progressAnimation: 'decreasing',\n};\nconst TOAST_CONFIG = new InjectionToken('ToastConfig');\n\n/**\n * A PortalHost for attaching portals to an arbitrary DOM element outside of the Angular\n * application context.\n *\n * This is the only part of the portal core that directly touches the DOM.\n */\nclass DomPortalHost extends BasePortalHost {\n    _hostDomElement;\n    _componentFactoryResolver;\n    _appRef;\n    constructor(_hostDomElement, _componentFactoryResolver, _appRef) {\n        super();\n        this._hostDomElement = _hostDomElement;\n        this._componentFactoryResolver = _componentFactoryResolver;\n        this._appRef = _appRef;\n    }\n    /**\n     * Attach the given ComponentPortal to DOM element using the ComponentFactoryResolver.\n     * @param portal Portal to be attached\n     */\n    attachComponentPortal(portal, newestOnTop) {\n        const componentFactory = this._componentFactoryResolver.resolveComponentFactory(portal.component);\n        let componentRef;\n        // If the portal specifies a ViewContainerRef, we will use that as the attachment point\n        // for the component (in terms of Angular's component tree, not rendering).\n        // When the ViewContainerRef is missing, we use the factory to create the component directly\n        // and then manually attach the ChangeDetector for that component to the application (which\n        // happens automatically when using a ViewContainer).\n        componentRef = componentFactory.create(portal.injector);\n        // When creating a component outside of a ViewContainer, we need to manually register\n        // its ChangeDetector with the application. This API is unfortunately not yet published\n        // in Angular core. The change detector must also be deregistered when the component\n        // is destroyed to prevent memory leaks.\n        this._appRef.attachView(componentRef.hostView);\n        this.setDisposeFn(() => {\n            this._appRef.detachView(componentRef.hostView);\n            componentRef.destroy();\n        });\n        // At this point the component has been instantiated, so we move it to the location in the DOM\n        // where we want it to be rendered.\n        if (newestOnTop) {\n            this._hostDomElement.insertBefore(this._getComponentRootNode(componentRef), this._hostDomElement.firstChild);\n        }\n        else {\n            this._hostDomElement.appendChild(this._getComponentRootNode(componentRef));\n        }\n        return componentRef;\n    }\n    /** Gets the root HTMLElement for an instantiated component. */\n    _getComponentRootNode(componentRef) {\n        return componentRef.hostView.rootNodes[0];\n    }\n}\n\n/** Container inside which all toasts will render. */\nclass OverlayContainer {\n    _document = inject(DOCUMENT);\n    _containerElement;\n    ngOnDestroy() {\n        if (this._containerElement && this._containerElement.parentNode) {\n            this._containerElement.parentNode.removeChild(this._containerElement);\n        }\n    }\n    /**\n     * This method returns the overlay container element. It will lazily\n     * create the element the first time  it is called to facilitate using\n     * the container in non-browser environments.\n     * @returns the container element\n     */\n    getContainerElement() {\n        if (!this._containerElement) {\n            this._createContainer();\n        }\n        return this._containerElement;\n    }\n    /**\n     * Create the overlay container element, which is simply a div\n     * with the 'cdk-overlay-container' class on the document body\n     * and 'aria-live=\"polite\"'\n     */\n    _createContainer() {\n        const container = this._document.createElement('div');\n        container.classList.add('overlay-container');\n        container.setAttribute('aria-live', 'polite');\n        this._document.body.appendChild(container);\n        this._containerElement = container;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: OverlayContainer, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: OverlayContainer, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: OverlayContainer, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n\n/**\n * Reference to an overlay that has been created with the Overlay service.\n * Used to manipulate or dispose of said overlay.\n */\nclass OverlayRef {\n    _portalHost;\n    constructor(_portalHost) {\n        this._portalHost = _portalHost;\n    }\n    attach(portal, newestOnTop = true) {\n        return this._portalHost.attach(portal, newestOnTop);\n    }\n    /**\n     * Detaches an overlay from a portal.\n     * @returns Resolves when the overlay has been detached.\n     */\n    detach() {\n        return this._portalHost.detach();\n    }\n}\n\n/**\n * Service to create Overlays. Overlays are dynamically added pieces of floating UI, meant to be\n * used as a low-level building building block for other components. Dialogs, tooltips, menus,\n * selects, etc. can all be built using overlays. The service should primarily be used by authors\n * of re-usable components rather than developers building end-user applications.\n *\n * An overlay *is* a PortalHost, so any kind of Portal can be loaded into one.\n */\nclass Overlay {\n    _overlayContainer = inject(OverlayContainer);\n    _componentFactoryResolver = inject(ComponentFactoryResolver);\n    _appRef = inject(ApplicationRef);\n    _document = inject(DOCUMENT);\n    // Namespace panes by overlay container\n    _paneElements = new Map();\n    /**\n     * Creates an overlay.\n     * @returns A reference to the created overlay.\n     */\n    create(positionClass, overlayContainer) {\n        // get existing pane if possible\n        return this._createOverlayRef(this.getPaneElement(positionClass, overlayContainer));\n    }\n    getPaneElement(positionClass = '', overlayContainer) {\n        if (!this._paneElements.get(overlayContainer)) {\n            this._paneElements.set(overlayContainer, {});\n        }\n        if (!this._paneElements.get(overlayContainer)[positionClass]) {\n            this._paneElements.get(overlayContainer)[positionClass] = this._createPaneElement(positionClass, overlayContainer);\n        }\n        return this._paneElements.get(overlayContainer)[positionClass];\n    }\n    /**\n     * Creates the DOM element for an overlay and appends it to the overlay container.\n     * @returns Newly-created pane element\n     */\n    _createPaneElement(positionClass, overlayContainer) {\n        const pane = this._document.createElement('div');\n        pane.id = 'toast-container';\n        pane.classList.add(positionClass);\n        pane.classList.add('toast-container');\n        if (!overlayContainer) {\n            this._overlayContainer.getContainerElement().appendChild(pane);\n        }\n        else {\n            overlayContainer.getContainerElement().appendChild(pane);\n        }\n        return pane;\n    }\n    /**\n     * Create a DomPortalHost into which the overlay content can be loaded.\n     * @param pane The DOM element to turn into a portal host.\n     * @returns A portal host for the given DOM element.\n     */\n    _createPortalHost(pane) {\n        return new DomPortalHost(pane, this._componentFactoryResolver, this._appRef);\n    }\n    /**\n     * Creates an OverlayRef for an overlay in the given DOM element.\n     * @param pane DOM element for the overlay\n     */\n    _createOverlayRef(pane) {\n        return new OverlayRef(this._createPortalHost(pane));\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: Overlay, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: Overlay, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: Overlay, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n\nclass ToastrService {\n    overlay;\n    _injector;\n    sanitizer;\n    ngZone;\n    toastrConfig;\n    currentlyActive = 0;\n    toasts = [];\n    overlayContainer;\n    previousToastMessage;\n    index = 0;\n    constructor(token, overlay, _injector, sanitizer, ngZone) {\n        this.overlay = overlay;\n        this._injector = _injector;\n        this.sanitizer = sanitizer;\n        this.ngZone = ngZone;\n        this.toastrConfig = {\n            ...token.default,\n            ...token.config,\n        };\n        if (token.config.iconClasses) {\n            this.toastrConfig.iconClasses = {\n                ...token.default.iconClasses,\n                ...token.config.iconClasses,\n            };\n        }\n    }\n    /** show toast */\n    show(message, title, override = {}, type = '') {\n        return this._preBuildNotification(type, message, title, this.applyConfig(override));\n    }\n    /** show successful toast */\n    success(message, title, override = {}) {\n        const type = this.toastrConfig.iconClasses.success || '';\n        return this._preBuildNotification(type, message, title, this.applyConfig(override));\n    }\n    /** show error toast */\n    error(message, title, override = {}) {\n        const type = this.toastrConfig.iconClasses.error || '';\n        return this._preBuildNotification(type, message, title, this.applyConfig(override));\n    }\n    /** show info toast */\n    info(message, title, override = {}) {\n        const type = this.toastrConfig.iconClasses.info || '';\n        return this._preBuildNotification(type, message, title, this.applyConfig(override));\n    }\n    /** show warning toast */\n    warning(message, title, override = {}) {\n        const type = this.toastrConfig.iconClasses.warning || '';\n        return this._preBuildNotification(type, message, title, this.applyConfig(override));\n    }\n    /**\n     * Remove all or a single toast by id\n     */\n    clear(toastId) {\n        // Call every toastRef manualClose function\n        for (const toast of this.toasts) {\n            if (toastId !== undefined) {\n                if (toast.toastId === toastId) {\n                    toast.toastRef.manualClose();\n                    return;\n                }\n            }\n            else {\n                toast.toastRef.manualClose();\n            }\n        }\n    }\n    /**\n     * Remove and destroy a single toast by id\n     */\n    remove(toastId) {\n        const found = this._findToast(toastId);\n        if (!found) {\n            return false;\n        }\n        found.activeToast.toastRef.close();\n        this.toasts.splice(found.index, 1);\n        this.currentlyActive = this.currentlyActive - 1;\n        if (!this.toastrConfig.maxOpened || !this.toasts.length) {\n            return false;\n        }\n        if (this.currentlyActive < this.toastrConfig.maxOpened && this.toasts[this.currentlyActive]) {\n            const p = this.toasts[this.currentlyActive].toastRef;\n            if (!p.isInactive()) {\n                this.currentlyActive = this.currentlyActive + 1;\n                p.activate();\n            }\n        }\n        return true;\n    }\n    /**\n     * Determines if toast message is already shown\n     */\n    findDuplicate(title = '', message = '', resetOnDuplicate, countDuplicates) {\n        const { includeTitleDuplicates } = this.toastrConfig;\n        for (const toast of this.toasts) {\n            const hasDuplicateTitle = includeTitleDuplicates && toast.title === title;\n            if ((!includeTitleDuplicates || hasDuplicateTitle) && toast.message === message) {\n                toast.toastRef.onDuplicate(resetOnDuplicate, countDuplicates);\n                return toast;\n            }\n        }\n        return null;\n    }\n    /** create a clone of global config and apply individual settings */\n    applyConfig(override = {}) {\n        return { ...this.toastrConfig, ...override };\n    }\n    /**\n     * Find toast object by id\n     */\n    _findToast(toastId) {\n        for (let i = 0; i < this.toasts.length; i++) {\n            if (this.toasts[i].toastId === toastId) {\n                return { index: i, activeToast: this.toasts[i] };\n            }\n        }\n        return null;\n    }\n    /**\n     * Determines the need to run inside angular's zone then builds the toast\n     */\n    _preBuildNotification(toastType, message, title, config) {\n        if (config.onActivateTick) {\n            return this.ngZone.run(() => this._buildNotification(toastType, message, title, config));\n        }\n        return this._buildNotification(toastType, message, title, config);\n    }\n    /**\n     * Creates and attaches toast data to component\n     * returns the active toast, or in case preventDuplicates is enabled the original/non-duplicate active toast.\n     */\n    _buildNotification(toastType, message, title, config) {\n        if (!config.toastComponent) {\n            throw new Error('toastComponent required');\n        }\n        // max opened and auto dismiss = true\n        // if timeout = 0 resetting it would result in setting this.hideTime = Date.now(). Hence, we only want to reset timeout if there is\n        // a timeout at all\n        const duplicate = this.findDuplicate(title, message, this.toastrConfig.resetTimeoutOnDuplicate && config.timeOut > 0, this.toastrConfig.countDuplicates);\n        if (((this.toastrConfig.includeTitleDuplicates && title) || message) &&\n            this.toastrConfig.preventDuplicates &&\n            duplicate !== null) {\n            return duplicate;\n        }\n        this.previousToastMessage = message;\n        let keepInactive = false;\n        if (this.toastrConfig.maxOpened && this.currentlyActive >= this.toastrConfig.maxOpened) {\n            keepInactive = true;\n            if (this.toastrConfig.autoDismiss) {\n                this.clear(this.toasts[0].toastId);\n            }\n        }\n        const overlayRef = this.overlay.create(config.positionClass, this.overlayContainer);\n        this.index = this.index + 1;\n        let sanitizedMessage = message;\n        if (message && config.enableHtml) {\n            sanitizedMessage = this.sanitizer.sanitize(SecurityContext.HTML, message);\n        }\n        const toastRef = new ToastRef(overlayRef);\n        const toastPackage = new ToastPackage(this.index, config, sanitizedMessage, title, toastType, toastRef);\n        /** New injector that contains an instance of `ToastPackage`. */\n        const providers = [{ provide: ToastPackage, useValue: toastPackage }];\n        const toastInjector = Injector.create({ providers, parent: this._injector });\n        const component = new ComponentPortal(config.toastComponent, toastInjector);\n        const portal = overlayRef.attach(component, config.newestOnTop);\n        toastRef.componentInstance = portal.instance;\n        const ins = {\n            toastId: this.index,\n            title: title || '',\n            message: message || '',\n            toastRef,\n            onShown: toastRef.afterActivate(),\n            onHidden: toastRef.afterClosed(),\n            onTap: toastPackage.onTap(),\n            onAction: toastPackage.onAction(),\n            portal,\n        };\n        if (!keepInactive) {\n            this.currentlyActive = this.currentlyActive + 1;\n            setTimeout(() => {\n                ins.toastRef.activate();\n            });\n        }\n        this.toasts.push(ins);\n        return ins;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: ToastrService, deps: [{ token: TOAST_CONFIG }, { token: Overlay }, { token: i0.Injector }, { token: i2.DomSanitizer }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: ToastrService, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: ToastrService, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [TOAST_CONFIG]\n                }] }, { type: Overlay }, { type: i0.Injector }, { type: i2.DomSanitizer }, { type: i0.NgZone }] });\n\nclass Toast {\n    toastrService;\n    toastPackage;\n    ngZone;\n    message;\n    title;\n    options;\n    duplicatesCount;\n    originalTimeout;\n    /** width of progress bar */\n    width = signal(-1);\n    /** a combination of toast type and options.toastClass */\n    toastClasses = '';\n    state;\n    /** controls animation */\n    get _state() {\n        return this.state();\n    }\n    /** hides component when waiting to be displayed */\n    get displayStyle() {\n        if (this.state().value === 'inactive') {\n            return 'none';\n        }\n        return;\n    }\n    timeout;\n    intervalId;\n    hideTime;\n    sub;\n    sub1;\n    sub2;\n    sub3;\n    constructor(toastrService, toastPackage, ngZone) {\n        this.toastrService = toastrService;\n        this.toastPackage = toastPackage;\n        this.ngZone = ngZone;\n        this.message = toastPackage.message;\n        this.title = toastPackage.title;\n        this.options = toastPackage.config;\n        this.originalTimeout = toastPackage.config.timeOut;\n        this.toastClasses = `${toastPackage.toastType} ${toastPackage.config.toastClass}`;\n        this.sub = toastPackage.toastRef.afterActivate().subscribe(() => {\n            this.activateToast();\n        });\n        this.sub1 = toastPackage.toastRef.manualClosed().subscribe(() => {\n            this.remove();\n        });\n        this.sub2 = toastPackage.toastRef.timeoutReset().subscribe(() => {\n            this.resetTimeout();\n        });\n        this.sub3 = toastPackage.toastRef.countDuplicate().subscribe(count => {\n            this.duplicatesCount = count;\n        });\n        this.state = signal({\n            value: 'inactive',\n            params: {\n                easeTime: this.toastPackage.config.easeTime,\n                easing: 'ease-in',\n            },\n        });\n    }\n    ngOnDestroy() {\n        this.sub.unsubscribe();\n        this.sub1.unsubscribe();\n        this.sub2.unsubscribe();\n        this.sub3.unsubscribe();\n        clearInterval(this.intervalId);\n        clearTimeout(this.timeout);\n    }\n    /**\n     * activates toast and sets timeout\n     */\n    activateToast() {\n        this.state.update(state => ({ ...state, value: 'active' }));\n        if (!(this.options.disableTimeOut === true || this.options.disableTimeOut === 'timeOut') &&\n            this.options.timeOut) {\n            this.outsideTimeout(() => this.remove(), this.options.timeOut);\n            this.hideTime = new Date().getTime() + this.options.timeOut;\n            if (this.options.progressBar) {\n                this.outsideInterval(() => this.updateProgress(), 10);\n            }\n        }\n    }\n    /**\n     * updates progress bar width\n     */\n    updateProgress() {\n        if (this.width() === 0 || this.width() === 100 || !this.options.timeOut) {\n            return;\n        }\n        const now = new Date().getTime();\n        const remaining = this.hideTime - now;\n        this.width.set((remaining / this.options.timeOut) * 100);\n        if (this.options.progressAnimation === 'increasing') {\n            this.width.update(width => 100 - width);\n        }\n        if (this.width() <= 0) {\n            this.width.set(0);\n        }\n        if (this.width() >= 100) {\n            this.width.set(100);\n        }\n    }\n    resetTimeout() {\n        clearTimeout(this.timeout);\n        clearInterval(this.intervalId);\n        this.state.update(state => ({ ...state, value: 'active' }));\n        this.outsideTimeout(() => this.remove(), this.originalTimeout);\n        this.options.timeOut = this.originalTimeout;\n        this.hideTime = new Date().getTime() + (this.options.timeOut || 0);\n        this.width.set(-1);\n        if (this.options.progressBar) {\n            this.outsideInterval(() => this.updateProgress(), 10);\n        }\n    }\n    /**\n     * tells toastrService to remove this toast after animation time\n     */\n    remove() {\n        if (this.state().value === 'removed') {\n            return;\n        }\n        clearTimeout(this.timeout);\n        this.state.update(state => ({ ...state, value: 'removed' }));\n        this.outsideTimeout(() => this.toastrService.remove(this.toastPackage.toastId), +this.toastPackage.config.easeTime);\n    }\n    tapToast() {\n        if (this.state().value === 'removed') {\n            return;\n        }\n        this.toastPackage.triggerTap();\n        if (this.options.tapToDismiss) {\n            this.remove();\n        }\n    }\n    stickAround() {\n        if (this.state().value === 'removed') {\n            return;\n        }\n        if (this.options.disableTimeOut !== 'extendedTimeOut') {\n            clearTimeout(this.timeout);\n            this.options.timeOut = 0;\n            this.hideTime = 0;\n            // disable progressBar\n            clearInterval(this.intervalId);\n            this.width.set(0);\n        }\n    }\n    delayedHideToast() {\n        if ((this.options.disableTimeOut === true || this.options.disableTimeOut === 'extendedTimeOut') ||\n            this.options.extendedTimeOut === 0 ||\n            this.state().value === 'removed') {\n            return;\n        }\n        this.outsideTimeout(() => this.remove(), this.options.extendedTimeOut);\n        this.options.timeOut = this.options.extendedTimeOut;\n        this.hideTime = new Date().getTime() + (this.options.timeOut || 0);\n        this.width.set(-1);\n        if (this.options.progressBar) {\n            this.outsideInterval(() => this.updateProgress(), 10);\n        }\n    }\n    outsideTimeout(func, timeout) {\n        if (this.ngZone) {\n            this.ngZone.runOutsideAngular(() => (this.timeout = setTimeout(() => this.runInsideAngular(func), timeout)));\n        }\n        else {\n            this.timeout = setTimeout(() => func(), timeout);\n        }\n    }\n    outsideInterval(func, timeout) {\n        if (this.ngZone) {\n            this.ngZone.runOutsideAngular(() => (this.intervalId = setInterval(() => this.runInsideAngular(func), timeout)));\n        }\n        else {\n            this.intervalId = setInterval(() => func(), timeout);\n        }\n    }\n    runInsideAngular(func) {\n        if (this.ngZone) {\n            this.ngZone.run(() => func());\n        }\n        else {\n            func();\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: Toast, deps: [{ token: ToastrService }, { token: ToastPackage }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"18.0.0\", type: Toast, isStandalone: true, selector: \"[toast-component]\", host: { listeners: { \"click\": \"tapToast()\", \"mouseenter\": \"stickAround()\", \"mouseleave\": \"delayedHideToast()\" }, properties: { \"class\": \"this.toastClasses\", \"@flyInOut\": \"this._state\", \"style.display\": \"this.displayStyle\" } }, ngImport: i0, template: `\n  <button *ngIf=\"options.closeButton\" (click)=\"remove()\" type=\"button\" class=\"toast-close-button\" aria-label=\"Close\">\n    <span aria-hidden=\"true\">&times;</span>\n  </button>\n  <div *ngIf=\"title\" [class]=\"options.titleClass\" [attr.aria-label]=\"title\">\n    {{ title }} <ng-container *ngIf=\"duplicatesCount\">[{{ duplicatesCount + 1 }}]</ng-container>\n  </div>\n  <div *ngIf=\"message && options.enableHtml\" role=\"alert\"\n    [class]=\"options.messageClass\" [innerHTML]=\"message\">\n  </div>\n  <div *ngIf=\"message && !options.enableHtml\" role=\"alert\"\n    [class]=\"options.messageClass\" [attr.aria-label]=\"message\">\n    {{ message }}\n  </div>\n  <div *ngIf=\"options.progressBar\">\n    <div class=\"toast-progress\" [style.width]=\"width() + '%'\"></div>\n  </div>\n  `, isInline: true, dependencies: [{ kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }], animations: [\n            trigger('flyInOut', [\n                state('inactive', style({ opacity: 0 })),\n                state('active', style({ opacity: 1 })),\n                state('removed', style({ opacity: 0 })),\n                transition('inactive => active', animate('{{ easeTime }}ms {{ easing }}')),\n                transition('active => removed', animate('{{ easeTime }}ms {{ easing }}')),\n            ]),\n        ], changeDetection: i0.ChangeDetectionStrategy.OnPush });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: Toast, decorators: [{\n            type: Component,\n            args: [{\n                    selector: '[toast-component]',\n                    template: `\n  <button *ngIf=\"options.closeButton\" (click)=\"remove()\" type=\"button\" class=\"toast-close-button\" aria-label=\"Close\">\n    <span aria-hidden=\"true\">&times;</span>\n  </button>\n  <div *ngIf=\"title\" [class]=\"options.titleClass\" [attr.aria-label]=\"title\">\n    {{ title }} <ng-container *ngIf=\"duplicatesCount\">[{{ duplicatesCount + 1 }}]</ng-container>\n  </div>\n  <div *ngIf=\"message && options.enableHtml\" role=\"alert\"\n    [class]=\"options.messageClass\" [innerHTML]=\"message\">\n  </div>\n  <div *ngIf=\"message && !options.enableHtml\" role=\"alert\"\n    [class]=\"options.messageClass\" [attr.aria-label]=\"message\">\n    {{ message }}\n  </div>\n  <div *ngIf=\"options.progressBar\">\n    <div class=\"toast-progress\" [style.width]=\"width() + '%'\"></div>\n  </div>\n  `,\n                    animations: [\n                        trigger('flyInOut', [\n                            state('inactive', style({ opacity: 0 })),\n                            state('active', style({ opacity: 1 })),\n                            state('removed', style({ opacity: 0 })),\n                            transition('inactive => active', animate('{{ easeTime }}ms {{ easing }}')),\n                            transition('active => removed', animate('{{ easeTime }}ms {{ easing }}')),\n                        ]),\n                    ],\n                    preserveWhitespaces: false,\n                    standalone: true,\n                    imports: [NgIf],\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                }]\n        }], ctorParameters: () => [{ type: ToastrService }, { type: ToastPackage }, { type: i0.NgZone }], propDecorators: { toastClasses: [{\n                type: HostBinding,\n                args: ['class']\n            }], _state: [{\n                type: HostBinding,\n                args: ['@flyInOut']\n            }], displayStyle: [{\n                type: HostBinding,\n                args: ['style.display']\n            }], tapToast: [{\n                type: HostListener,\n                args: ['click']\n            }], stickAround: [{\n                type: HostListener,\n                args: ['mouseenter']\n            }], delayedHideToast: [{\n                type: HostListener,\n                args: ['mouseleave']\n            }] } });\n\nconst DefaultGlobalConfig = {\n    ...DefaultNoComponentGlobalConfig,\n    toastComponent: Toast,\n};\n/**\n * @description\n * Provides the `TOAST_CONFIG` token with the given config.\n *\n * @param config The config to configure toastr.\n * @returns The environment providers.\n *\n * @example\n * ```ts\n * import { provideToastr } from 'ngx-toastr';\n *\n * bootstrap(AppComponent, {\n *   providers: [\n *     provideToastr({\n *       timeOut: 2000,\n *       positionClass: 'toast-top-right',\n *     }),\n *   ],\n * })\n */\nconst provideToastr = (config = {}) => {\n    const providers = [\n        {\n            provide: TOAST_CONFIG,\n            useValue: {\n                default: DefaultGlobalConfig,\n                config,\n            }\n        }\n    ];\n    return makeEnvironmentProviders(providers);\n};\n\nclass ToastrModule {\n    static forRoot(config = {}) {\n        return {\n            ngModule: ToastrModule,\n            providers: [provideToastr(config)],\n        };\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: ToastrModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"18.0.0\", ngImport: i0, type: ToastrModule, imports: [Toast], exports: [Toast] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: ToastrModule });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: ToastrModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [Toast],\n                    exports: [Toast],\n                }]\n        }] });\nclass ToastrComponentlessModule {\n    static forRoot(config = {}) {\n        return {\n            ngModule: ToastrModule,\n            providers: [\n                {\n                    provide: TOAST_CONFIG,\n                    useValue: {\n                        default: DefaultNoComponentGlobalConfig,\n                        config,\n                    },\n                },\n            ],\n        };\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: ToastrComponentlessModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"18.0.0\", ngImport: i0, type: ToastrComponentlessModule });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: ToastrComponentlessModule });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: ToastrComponentlessModule, decorators: [{\n            type: NgModule,\n            args: [{}]\n        }] });\n\nclass ToastNoAnimation {\n    toastrService;\n    toastPackage;\n    appRef;\n    message;\n    title;\n    options;\n    duplicatesCount;\n    originalTimeout;\n    /** width of progress bar */\n    width = signal(-1);\n    /** a combination of toast type and options.toastClass */\n    toastClasses = '';\n    /** hides component when waiting to be displayed */\n    get displayStyle() {\n        if (this.state() === 'inactive') {\n            return 'none';\n        }\n        return null;\n    }\n    /** controls animation */\n    state = signal('inactive');\n    timeout;\n    intervalId;\n    hideTime;\n    sub;\n    sub1;\n    sub2;\n    sub3;\n    constructor(toastrService, toastPackage, appRef) {\n        this.toastrService = toastrService;\n        this.toastPackage = toastPackage;\n        this.appRef = appRef;\n        this.message = toastPackage.message;\n        this.title = toastPackage.title;\n        this.options = toastPackage.config;\n        this.originalTimeout = toastPackage.config.timeOut;\n        this.toastClasses = `${toastPackage.toastType} ${toastPackage.config.toastClass}`;\n        this.sub = toastPackage.toastRef.afterActivate().subscribe(() => {\n            this.activateToast();\n        });\n        this.sub1 = toastPackage.toastRef.manualClosed().subscribe(() => {\n            this.remove();\n        });\n        this.sub2 = toastPackage.toastRef.timeoutReset().subscribe(() => {\n            this.resetTimeout();\n        });\n        this.sub3 = toastPackage.toastRef.countDuplicate().subscribe(count => {\n            this.duplicatesCount = count;\n        });\n    }\n    ngOnDestroy() {\n        this.sub.unsubscribe();\n        this.sub1.unsubscribe();\n        this.sub2.unsubscribe();\n        this.sub3.unsubscribe();\n        clearInterval(this.intervalId);\n        clearTimeout(this.timeout);\n    }\n    /**\n     * activates toast and sets timeout\n     */\n    activateToast() {\n        this.state.set('active');\n        if (!(this.options.disableTimeOut === true || this.options.disableTimeOut === 'timeOut') && this.options.timeOut) {\n            this.timeout = setTimeout(() => {\n                this.remove();\n            }, this.options.timeOut);\n            this.hideTime = new Date().getTime() + this.options.timeOut;\n            if (this.options.progressBar) {\n                this.intervalId = setInterval(() => this.updateProgress(), 10);\n            }\n        }\n        if (this.options.onActivateTick) {\n            this.appRef.tick();\n        }\n    }\n    /**\n     * updates progress bar width\n     */\n    updateProgress() {\n        if (this.width() === 0 || this.width() === 100 || !this.options.timeOut) {\n            return;\n        }\n        const now = new Date().getTime();\n        const remaining = this.hideTime - now;\n        this.width.set((remaining / this.options.timeOut) * 100);\n        if (this.options.progressAnimation === 'increasing') {\n            this.width.update(width => 100 - width);\n        }\n        if (this.width() <= 0) {\n            this.width.set(0);\n        }\n        if (this.width() >= 100) {\n            this.width.set(100);\n        }\n    }\n    resetTimeout() {\n        clearTimeout(this.timeout);\n        clearInterval(this.intervalId);\n        this.state.set('active');\n        this.options.timeOut = this.originalTimeout;\n        this.timeout = setTimeout(() => this.remove(), this.originalTimeout);\n        this.hideTime = new Date().getTime() + (this.originalTimeout || 0);\n        this.width.set(-1);\n        if (this.options.progressBar) {\n            this.intervalId = setInterval(() => this.updateProgress(), 10);\n        }\n    }\n    /**\n     * tells toastrService to remove this toast after animation time\n     */\n    remove() {\n        if (this.state() === 'removed') {\n            return;\n        }\n        clearTimeout(this.timeout);\n        this.state.set('removed');\n        this.timeout = setTimeout(() => this.toastrService.remove(this.toastPackage.toastId));\n    }\n    tapToast() {\n        if (this.state() === 'removed') {\n            return;\n        }\n        this.toastPackage.triggerTap();\n        if (this.options.tapToDismiss) {\n            this.remove();\n        }\n    }\n    stickAround() {\n        if (this.state() === 'removed') {\n            return;\n        }\n        clearTimeout(this.timeout);\n        this.options.timeOut = 0;\n        this.hideTime = 0;\n        // disable progressBar\n        clearInterval(this.intervalId);\n        this.width.set(0);\n    }\n    delayedHideToast() {\n        if ((this.options.disableTimeOut === true || this.options.disableTimeOut === 'extendedTimeOut') ||\n            this.options.extendedTimeOut === 0 ||\n            this.state() === 'removed') {\n            return;\n        }\n        this.timeout = setTimeout(() => this.remove(), this.options.extendedTimeOut);\n        this.options.timeOut = this.options.extendedTimeOut;\n        this.hideTime = new Date().getTime() + (this.options.timeOut || 0);\n        this.width.set(-1);\n        if (this.options.progressBar) {\n            this.intervalId = setInterval(() => this.updateProgress(), 10);\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: ToastNoAnimation, deps: [{ token: ToastrService }, { token: ToastPackage }, { token: i0.ApplicationRef }], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"18.0.0\", type: ToastNoAnimation, isStandalone: true, selector: \"[toast-component]\", host: { listeners: { \"click\": \"tapToast()\", \"mouseenter\": \"stickAround()\", \"mouseleave\": \"delayedHideToast()\" }, properties: { \"class\": \"this.toastClasses\", \"style.display\": \"this.displayStyle\" } }, ngImport: i0, template: `\n  <button *ngIf=\"options.closeButton\" (click)=\"remove()\" type=\"button\" class=\"toast-close-button\" aria-label=\"Close\">\n    <span aria-hidden=\"true\">&times;</span>\n  </button>\n  <div *ngIf=\"title\" [class]=\"options.titleClass\" [attr.aria-label]=\"title\">\n    {{ title }} <ng-container *ngIf=\"duplicatesCount\">[{{ duplicatesCount + 1 }}]</ng-container>\n  </div>\n  <div *ngIf=\"message && options.enableHtml\" role=\"alert\"\n    [class]=\"options.messageClass\" [innerHTML]=\"message\">\n  </div>\n  <div *ngIf=\"message && !options.enableHtml\" role=\"alert\"\n    [class]=\"options.messageClass\" [attr.aria-label]=\"message\">\n    {{ message }}\n  </div>\n  <div *ngIf=\"options.progressBar\">\n    <div class=\"toast-progress\" [style.width]=\"width() + '%'\"></div>\n  </div>\n  `, isInline: true, dependencies: [{ kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: ToastNoAnimation, decorators: [{\n            type: Component,\n            args: [{\n                    selector: '[toast-component]',\n                    template: `\n  <button *ngIf=\"options.closeButton\" (click)=\"remove()\" type=\"button\" class=\"toast-close-button\" aria-label=\"Close\">\n    <span aria-hidden=\"true\">&times;</span>\n  </button>\n  <div *ngIf=\"title\" [class]=\"options.titleClass\" [attr.aria-label]=\"title\">\n    {{ title }} <ng-container *ngIf=\"duplicatesCount\">[{{ duplicatesCount + 1 }}]</ng-container>\n  </div>\n  <div *ngIf=\"message && options.enableHtml\" role=\"alert\"\n    [class]=\"options.messageClass\" [innerHTML]=\"message\">\n  </div>\n  <div *ngIf=\"message && !options.enableHtml\" role=\"alert\"\n    [class]=\"options.messageClass\" [attr.aria-label]=\"message\">\n    {{ message }}\n  </div>\n  <div *ngIf=\"options.progressBar\">\n    <div class=\"toast-progress\" [style.width]=\"width() + '%'\"></div>\n  </div>\n  `,\n                    standalone: true,\n                    imports: [NgIf],\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                }]\n        }], ctorParameters: () => [{ type: ToastrService }, { type: ToastPackage }, { type: i0.ApplicationRef }], propDecorators: { toastClasses: [{\n                type: HostBinding,\n                args: ['class']\n            }], displayStyle: [{\n                type: HostBinding,\n                args: ['style.display']\n            }], tapToast: [{\n                type: HostListener,\n                args: ['click']\n            }], stickAround: [{\n                type: HostListener,\n                args: ['mouseenter']\n            }], delayedHideToast: [{\n                type: HostListener,\n                args: ['mouseleave']\n            }] } });\nconst DefaultNoAnimationsGlobalConfig = {\n    ...DefaultNoComponentGlobalConfig,\n    toastComponent: ToastNoAnimation,\n};\nclass ToastNoAnimationModule {\n    static forRoot(config = {}) {\n        return {\n            ngModule: ToastNoAnimationModule,\n            providers: [\n                {\n                    provide: TOAST_CONFIG,\n                    useValue: {\n                        default: DefaultNoAnimationsGlobalConfig,\n                        config,\n                    },\n                },\n            ],\n        };\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: ToastNoAnimationModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"18.0.0\", ngImport: i0, type: ToastNoAnimationModule, imports: [ToastNoAnimation], exports: [ToastNoAnimation] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: ToastNoAnimationModule });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: ToastNoAnimationModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [ToastNoAnimation],\n                    exports: [ToastNoAnimation],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BasePortalHost, ComponentPortal, DefaultGlobalConfig, DefaultNoAnimationsGlobalConfig, DefaultNoComponentGlobalConfig, Overlay, OverlayContainer, OverlayRef, TOAST_CONFIG, Toast, ToastContainerDirective, ToastNoAnimation, ToastNoAnimationModule, ToastPackage, ToastRef, ToastrComponentlessModule, ToastrModule, ToastrService, provideToastr };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,cAAc,EAAEC,MAAM,EAAEC,UAAU,EAAEC,wBAAwB,EAAEC,cAAc,EAAEC,eAAe,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,MAAM,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,WAAW,EAAEC,YAAY,EAAEC,wBAAwB,EAAEC,QAAQ,QAAQ,eAAe;AACrQ,SAASC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAEC,OAAO,QAAQ,qBAAqB;AAChF,SAASC,QAAQ,EAAEC,IAAI,QAAQ,iBAAiB;AAChD,SAASC,OAAO,QAAQ,MAAM;AAC9B,OAAO,KAAKC,EAAE,MAAM,2BAA2B;AAAC,MAAAC,GAAA;AAAA,SAAAC,wBAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAE,GAAA,GAU6C9B,EAAE,CAAA+B,gBAAA;IAAF/B,EAAE,CAAAgC,cAAA,eAwyBqB,CAAC;IAxyBxBhC,EAAE,CAAAiC,UAAA,mBAAAC,gDAAA;MAAFlC,EAAE,CAAAmC,aAAA,CAAAL,GAAA;MAAA,MAAAM,MAAA,GAAFpC,EAAE,CAAAqC,aAAA;MAAA,OAAFrC,EAAE,CAAAsC,WAAA,CAwyBhDF,MAAA,CAAAG,MAAA,CAAO,CAAC;IAAA,EAAC;IAxyBqCvC,EAAE,CAAAgC,cAAA,aAyyBnE,CAAC;IAzyBgEhC,EAAE,CAAAwC,MAAA,UAyyB5D,CAAC;IAzyByDxC,EAAE,CAAAyC,YAAA,CAyyBrD,CAAC,CACjC,CAAC;EAAA;AAAA;AAAA,SAAAC,oCAAAd,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA1yBkF5B,EAAE,CAAA2C,uBAAA,EA4yB1C,CAAC;IA5yBuC3C,EAAE,CAAAwC,MAAA,EA4yBf,CAAC;IA5yBYxC,EAAE,CAAA4C,qBAAA;EAAA;EAAA,IAAAhB,EAAA;IAAA,MAAAQ,MAAA,GAAFpC,EAAE,CAAAqC,aAAA;IAAFrC,EAAE,CAAA6C,SAAA,CA4yBf,CAAC;IA5yBY7C,EAAE,CAAA8C,kBAAA,MAAAV,MAAA,CAAAW,eAAA,SA4yBf,CAAC;EAAA;AAAA;AAAA,SAAAC,qBAAApB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA5yBY5B,EAAE,CAAAgC,cAAA,SA2yBpB,CAAC;IA3yBiBhC,EAAE,CAAAwC,MAAA,EA4yBhF,CAAC;IA5yB6ExC,EAAE,CAAAiD,UAAA,IAAAP,mCAAA,yBA4yB1C,CAAC;IA5yBuC1C,EAAE,CAAAyC,YAAA,CA6yBxF,CAAC;EAAA;EAAA,IAAAb,EAAA;IAAA,MAAAQ,MAAA,GA7yBqFpC,EAAE,CAAAqC,aAAA;IAAFrC,EAAE,CAAAkD,UAAA,CAAAd,MAAA,CAAAe,OAAA,CAAAC,UA2yB/C,CAAC;IA3yB4CpD,EAAE,CAAAqD,WAAA,eAAAjB,MAAA,CAAAkB,KAAA;IAAFtD,EAAE,CAAA6C,SAAA,CA4yBhF,CAAC;IA5yB6E7C,EAAE,CAAA8C,kBAAA,MAAAV,MAAA,CAAAkB,KAAA,KA4yBhF,CAAC;IA5yB6EtD,EAAE,CAAA6C,SAAA,CA4yB5C,CAAC;IA5yByC7C,EAAE,CAAAuD,UAAA,SAAAnB,MAAA,CAAAW,eA4yB5C,CAAC;EAAA;AAAA;AAAA,SAAAS,qBAAA5B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA5yByC5B,EAAE,CAAAyD,SAAA,YAgzBxF,CAAC;EAAA;EAAA,IAAA7B,EAAA;IAAA,MAAAQ,MAAA,GAhzBqFpC,EAAE,CAAAqC,aAAA;IAAFrC,EAAE,CAAAkD,UAAA,CAAAd,MAAA,CAAAe,OAAA,CAAAO,YA+yB9D,CAAC;IA/yB2D1D,EAAE,CAAAuD,UAAA,cAAAnB,MAAA,CAAAuB,OAAA,EAAF3D,EAAE,CAAA4D,cA+yBxC,CAAC;EAAA;AAAA;AAAA,SAAAC,qBAAAjC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/yBqC5B,EAAE,CAAAgC,cAAA,YAkzBjC,CAAC;IAlzB8BhC,EAAE,CAAAwC,MAAA,EAozB9F,CAAC;IApzB2FxC,EAAE,CAAAyC,YAAA,CAozBxF,CAAC;EAAA;EAAA,IAAAb,EAAA;IAAA,MAAAQ,MAAA,GApzBqFpC,EAAE,CAAAqC,aAAA;IAAFrC,EAAE,CAAAkD,UAAA,CAAAd,MAAA,CAAAe,OAAA,CAAAO,YAkzB9D,CAAC;IAlzB2D1D,EAAE,CAAAqD,WAAA,eAAAjB,MAAA,CAAAuB,OAAA;IAAF3D,EAAE,CAAA6C,SAAA,CAozB9F,CAAC;IApzB2F7C,EAAE,CAAA8C,kBAAA,MAAAV,MAAA,CAAAuB,OAAA,KAozB9F,CAAC;EAAA;AAAA;AAAA,SAAAG,qBAAAlC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IApzB2F5B,EAAE,CAAAgC,cAAA,SAqzB7D,CAAC;IArzB0DhC,EAAE,CAAAyD,SAAA,YAszB5B,CAAC;IAtzByBzD,EAAE,CAAAyC,YAAA,CAuzBxF,CAAC;EAAA;EAAA,IAAAb,EAAA;IAAA,MAAAQ,MAAA,GAvzBqFpC,EAAE,CAAAqC,aAAA;IAAFrC,EAAE,CAAA6C,SAAA,CAszBnC,CAAC;IAtzBgC7C,EAAE,CAAA+D,WAAA,UAAA3B,MAAA,CAAA4B,KAAA,QAszBnC,CAAC;EAAA;AAAA;AAAA,SAAAC,mCAAArC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAE,GAAA,GAtzBgC9B,EAAE,CAAA+B,gBAAA;IAAF/B,EAAE,CAAAgC,cAAA,eAqmCqB,CAAC;IArmCxBhC,EAAE,CAAAiC,UAAA,mBAAAiC,2DAAA;MAAFlE,EAAE,CAAAmC,aAAA,CAAAL,GAAA;MAAA,MAAAM,MAAA,GAAFpC,EAAE,CAAAqC,aAAA;MAAA,OAAFrC,EAAE,CAAAsC,WAAA,CAqmChDF,MAAA,CAAAG,MAAA,CAAO,CAAC;IAAA,EAAC;IArmCqCvC,EAAE,CAAAgC,cAAA,aAsmCnE,CAAC;IAtmCgEhC,EAAE,CAAAwC,MAAA,UAsmC5D,CAAC;IAtmCyDxC,EAAE,CAAAyC,YAAA,CAsmCrD,CAAC,CACjC,CAAC;EAAA;AAAA;AAAA,SAAA0B,+CAAAvC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAvmCkF5B,EAAE,CAAA2C,uBAAA,EAymC1C,CAAC;IAzmCuC3C,EAAE,CAAAwC,MAAA,EAymCf,CAAC;IAzmCYxC,EAAE,CAAA4C,qBAAA;EAAA;EAAA,IAAAhB,EAAA;IAAA,MAAAQ,MAAA,GAAFpC,EAAE,CAAAqC,aAAA;IAAFrC,EAAE,CAAA6C,SAAA,CAymCf,CAAC;IAzmCY7C,EAAE,CAAA8C,kBAAA,MAAAV,MAAA,CAAAW,eAAA,SAymCf,CAAC;EAAA;AAAA;AAAA,SAAAqB,gCAAAxC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAzmCY5B,EAAE,CAAAgC,cAAA,SAwmCpB,CAAC;IAxmCiBhC,EAAE,CAAAwC,MAAA,EAymChF,CAAC;IAzmC6ExC,EAAE,CAAAiD,UAAA,IAAAkB,8CAAA,yBAymC1C,CAAC;IAzmCuCnE,EAAE,CAAAyC,YAAA,CA0mCxF,CAAC;EAAA;EAAA,IAAAb,EAAA;IAAA,MAAAQ,MAAA,GA1mCqFpC,EAAE,CAAAqC,aAAA;IAAFrC,EAAE,CAAAkD,UAAA,CAAAd,MAAA,CAAAe,OAAA,CAAAC,UAwmC/C,CAAC;IAxmC4CpD,EAAE,CAAAqD,WAAA,eAAAjB,MAAA,CAAAkB,KAAA;IAAFtD,EAAE,CAAA6C,SAAA,CAymChF,CAAC;IAzmC6E7C,EAAE,CAAA8C,kBAAA,MAAAV,MAAA,CAAAkB,KAAA,KAymChF,CAAC;IAzmC6EtD,EAAE,CAAA6C,SAAA,CAymC5C,CAAC;IAzmCyC7C,EAAE,CAAAuD,UAAA,SAAAnB,MAAA,CAAAW,eAymC5C,CAAC;EAAA;AAAA;AAAA,SAAAsB,gCAAAzC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAzmCyC5B,EAAE,CAAAyD,SAAA,YA6mCxF,CAAC;EAAA;EAAA,IAAA7B,EAAA;IAAA,MAAAQ,MAAA,GA7mCqFpC,EAAE,CAAAqC,aAAA;IAAFrC,EAAE,CAAAkD,UAAA,CAAAd,MAAA,CAAAe,OAAA,CAAAO,YA4mC9D,CAAC;IA5mC2D1D,EAAE,CAAAuD,UAAA,cAAAnB,MAAA,CAAAuB,OAAA,EAAF3D,EAAE,CAAA4D,cA4mCxC,CAAC;EAAA;AAAA;AAAA,SAAAU,gCAAA1C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA5mCqC5B,EAAE,CAAAgC,cAAA,YA+mCjC,CAAC;IA/mC8BhC,EAAE,CAAAwC,MAAA,EAinC9F,CAAC;IAjnC2FxC,EAAE,CAAAyC,YAAA,CAinCxF,CAAC;EAAA;EAAA,IAAAb,EAAA;IAAA,MAAAQ,MAAA,GAjnCqFpC,EAAE,CAAAqC,aAAA;IAAFrC,EAAE,CAAAkD,UAAA,CAAAd,MAAA,CAAAe,OAAA,CAAAO,YA+mC9D,CAAC;IA/mC2D1D,EAAE,CAAAqD,WAAA,eAAAjB,MAAA,CAAAuB,OAAA;IAAF3D,EAAE,CAAA6C,SAAA,CAinC9F,CAAC;IAjnC2F7C,EAAE,CAAA8C,kBAAA,MAAAV,MAAA,CAAAuB,OAAA,KAinC9F,CAAC;EAAA;AAAA;AAAA,SAAAY,gCAAA3C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAjnC2F5B,EAAE,CAAAgC,cAAA,SAknC7D,CAAC;IAlnC0DhC,EAAE,CAAAyD,SAAA,YAmnC5B,CAAC;IAnnCyBzD,EAAE,CAAAyC,YAAA,CAonCxF,CAAC;EAAA;EAAA,IAAAb,EAAA;IAAA,MAAAQ,MAAA,GApnCqFpC,EAAE,CAAAqC,aAAA;IAAFrC,EAAE,CAAA6C,SAAA,CAmnCnC,CAAC;IAnnCgC7C,EAAE,CAAA+D,WAAA,UAAA3B,MAAA,CAAA4B,KAAA,QAmnCnC,CAAC;EAAA;AAAA;AA3nC7D,MAAMQ,uBAAuB,CAAC;EAC1BC,EAAE;EACFC,WAAWA,CAACD,EAAE,EAAE;IACZ,IAAI,CAACA,EAAE,GAAGA,EAAE;EAChB;EACAE,mBAAmBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACF,EAAE,CAACG,aAAa;EAChC;EACA,OAAOC,IAAI,YAAAC,gCAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwFP,uBAAuB,EAAjCxE,EAAE,CAAAgF,iBAAA,CAAiDhF,EAAE,CAACiF,UAAU;EAAA;EACzJ,OAAOC,IAAI,kBAD8ElF,EAAE,CAAAmF,iBAAA;IAAAC,IAAA,EACJZ,uBAAuB;IAAAa,SAAA;IAAAC,QAAA;IAAAC,UAAA;EAAA;AAClH;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAH6FxF,EAAE,CAAAyF,iBAAA,CAGJjB,uBAAuB,EAAc,CAAC;IACrHY,IAAI,EAAEnF,SAAS;IACfyF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,kBAAkB;MAC5BL,QAAQ,EAAE,gBAAgB;MAC1BC,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEH,IAAI,EAAEpF,EAAE,CAACiF;EAAW,CAAC,CAAC;AAAA;;AAE3D;AACA;AACA;AACA,MAAMW,eAAe,CAAC;EAClBC,aAAa;EACb;EACAC,SAAS;EACT;AACJ;AACA;AACA;AACA;EACIC,gBAAgB;EAChB;EACAC,QAAQ;EACRtB,WAAWA,CAACoB,SAAS,EAAEE,QAAQ,EAAE;IAC7B,IAAI,CAACF,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACE,QAAQ,GAAGA,QAAQ;EAC5B;EACA;EACAC,MAAMA,CAACC,IAAI,EAAEC,WAAW,EAAE;IACtB,IAAI,CAACN,aAAa,GAAGK,IAAI;IACzB,OAAOA,IAAI,CAACD,MAAM,CAAC,IAAI,EAAEE,WAAW,CAAC;EACzC;EACA;EACAC,MAAMA,CAAA,EAAG;IACL,MAAMF,IAAI,GAAG,IAAI,CAACL,aAAa;IAC/B,IAAIK,IAAI,EAAE;MACN,IAAI,CAACL,aAAa,GAAGQ,SAAS;MAC9B,OAAOH,IAAI,CAACE,MAAM,CAAC,CAAC;IACxB;EACJ;EACA;EACA,IAAIE,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACT,aAAa,IAAI,IAAI;EACrC;EACA;AACJ;AACA;AACA;EACIU,eAAeA,CAACL,IAAI,EAAE;IAClB,IAAI,CAACL,aAAa,GAAGK,IAAI;EAC7B;AACJ;AACA;AACA;AACA;AACA;AACA,MAAMM,cAAc,CAAC;EACjB;EACAC,eAAe;EACf;EACAC,UAAU;EACVT,MAAMA,CAACU,MAAM,EAAER,WAAW,EAAE;IACxB,IAAI,CAACM,eAAe,GAAGE,MAAM;IAC7B,OAAO,IAAI,CAACC,qBAAqB,CAACD,MAAM,EAAER,WAAW,CAAC;EAC1D;EACAC,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAACK,eAAe,EAAE;MACtB,IAAI,CAACA,eAAe,CAACF,eAAe,CAAC,CAAC;IAC1C;IACA,IAAI,CAACE,eAAe,GAAGJ,SAAS;IAChC,IAAI,IAAI,CAACK,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAAC,CAAC;MACjB,IAAI,CAACA,UAAU,GAAGL,SAAS;IAC/B;EACJ;EACAQ,YAAYA,CAACC,EAAE,EAAE;IACb,IAAI,CAACJ,UAAU,GAAGI,EAAE;EACxB;AACJ;;AAEA;AACA;AACA;AACA,MAAMC,QAAQ,CAAC;EACXC,WAAW;EACX;EACAC,iBAAiB;EACjB;EACAlE,eAAe,GAAG,CAAC;EACnB;EACAmE,YAAY,GAAG,IAAI1F,OAAO,CAAC,CAAC;EAC5B;EACA2F,SAAS,GAAG,IAAI3F,OAAO,CAAC,CAAC;EACzB;EACA4F,YAAY,GAAG,IAAI5F,OAAO,CAAC,CAAC;EAC5B;EACA6F,aAAa,GAAG,IAAI7F,OAAO,CAAC,CAAC;EAC7B;EACA8F,eAAe,GAAG,IAAI9F,OAAO,CAAC,CAAC;EAC/BkD,WAAWA,CAACsC,WAAW,EAAE;IACrB,IAAI,CAACA,WAAW,GAAGA,WAAW;EAClC;EACAO,WAAWA,CAAA,EAAG;IACV,IAAI,CAACH,YAAY,CAACI,IAAI,CAAC,CAAC;IACxB,IAAI,CAACJ,YAAY,CAACK,QAAQ,CAAC,CAAC;EAChC;EACAC,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACN,YAAY,CAACO,YAAY,CAAC,CAAC;EAC3C;EACAC,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACP,aAAa,CAACM,YAAY,CAAC,CAAC;EAC5C;EACAE,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACP,eAAe,CAACK,YAAY,CAAC,CAAC;EAC9C;EACA;AACJ;AACA;EACIG,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACd,WAAW,CAACZ,MAAM,CAAC,CAAC;IACzB,IAAI,CAACc,YAAY,CAACM,IAAI,CAAC,CAAC;IACxB,IAAI,CAACJ,YAAY,CAACI,IAAI,CAAC,CAAC;IACxB,IAAI,CAACN,YAAY,CAACO,QAAQ,CAAC,CAAC;IAC5B,IAAI,CAACL,YAAY,CAACK,QAAQ,CAAC,CAAC;IAC5B,IAAI,CAACN,SAAS,CAACM,QAAQ,CAAC,CAAC;IACzB,IAAI,CAACJ,aAAa,CAACI,QAAQ,CAAC,CAAC;IAC7B,IAAI,CAACH,eAAe,CAACG,QAAQ,CAAC,CAAC;EACnC;EACA;EACAM,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACb,YAAY,CAACS,YAAY,CAAC,CAAC;EAC3C;EACAK,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACb,SAAS,CAACc,SAAS;EACnC;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACf,SAAS,CAACK,IAAI,CAAC,CAAC;IACrB,IAAI,CAACL,SAAS,CAACM,QAAQ,CAAC,CAAC;EAC7B;EACA;EACAU,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAChB,SAAS,CAACQ,YAAY,CAAC,CAAC;EACxC;EACA;EACAS,WAAWA,CAACC,YAAY,EAAER,cAAc,EAAE;IACtC,IAAIQ,YAAY,EAAE;MACd,IAAI,CAAChB,aAAa,CAACG,IAAI,CAAC,CAAC;IAC7B;IACA,IAAIK,cAAc,EAAE;MAChB,IAAI,CAACP,eAAe,CAACE,IAAI,CAAC,EAAE,IAAI,CAACzE,eAAe,CAAC;IACrD;EACJ;AACJ;;AAEA;AACA;AACA;AACA,MAAMuF,YAAY,CAAC;EACfC,OAAO;EACPC,MAAM;EACN7E,OAAO;EACPL,KAAK;EACLmF,SAAS;EACTC,QAAQ;EACRC,MAAM,GAAG,IAAInH,OAAO,CAAC,CAAC;EACtBoH,SAAS,GAAG,IAAIpH,OAAO,CAAC,CAAC;EACzBkD,WAAWA,CAAC6D,OAAO,EAAEC,MAAM,EAAE7E,OAAO,EAAEL,KAAK,EAAEmF,SAAS,EAAEC,QAAQ,EAAE;IAC9D,IAAI,CAACH,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC7E,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACL,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACmF,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACA,QAAQ,CAACX,WAAW,CAAC,CAAC,CAACc,SAAS,CAAC,MAAM;MACxC,IAAI,CAACD,SAAS,CAACnB,QAAQ,CAAC,CAAC;MACzB,IAAI,CAACkB,MAAM,CAAClB,QAAQ,CAAC,CAAC;IAC1B,CAAC,CAAC;EACN;EACA;EACAqB,UAAUA,CAAA,EAAG;IACT,IAAI,CAACH,MAAM,CAACnB,IAAI,CAAC,CAAC;IAClB,IAAI,IAAI,CAACgB,MAAM,CAACO,YAAY,EAAE;MAC1B,IAAI,CAACJ,MAAM,CAAClB,QAAQ,CAAC,CAAC;IAC1B;EACJ;EACAuB,KAAKA,CAAA,EAAG;IACJ,OAAO,IAAI,CAACL,MAAM,CAAChB,YAAY,CAAC,CAAC;EACrC;EACA;EACAsB,aAAaA,CAACC,MAAM,EAAE;IAClB,IAAI,CAACN,SAAS,CAACpB,IAAI,CAAC0B,MAAM,CAAC;EAC/B;EACAC,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACP,SAAS,CAACjB,YAAY,CAAC,CAAC;EACxC;AACJ;AACA,MAAMyB,8BAA8B,GAAG;EACnCC,SAAS,EAAE,CAAC;EACZC,WAAW,EAAE,KAAK;EAClBnD,WAAW,EAAE,IAAI;EACjBoD,iBAAiB,EAAE,KAAK;EACxBC,eAAe,EAAE,KAAK;EACtBC,uBAAuB,EAAE,KAAK;EAC9BC,sBAAsB,EAAE,KAAK;EAC7BC,WAAW,EAAE;IACTC,KAAK,EAAE,aAAa;IACpBC,IAAI,EAAE,YAAY;IAClBC,OAAO,EAAE,eAAe;IACxBC,OAAO,EAAE;EACb,CAAC;EACD;EACAC,WAAW,EAAE,KAAK;EAClBC,cAAc,EAAE,KAAK;EACrBC,OAAO,EAAE,IAAI;EACbC,eAAe,EAAE,IAAI;EACrBC,UAAU,EAAE,KAAK;EACjBC,WAAW,EAAE,KAAK;EAClBC,UAAU,EAAE,YAAY;EACxBC,aAAa,EAAE,iBAAiB;EAChCnH,UAAU,EAAE,aAAa;EACzBM,YAAY,EAAE,eAAe;EAC7B8G,MAAM,EAAE,SAAS;EACjBC,QAAQ,EAAE,GAAG;EACb1B,YAAY,EAAE,IAAI;EAClB2B,cAAc,EAAE,KAAK;EACrBC,iBAAiB,EAAE;AACvB,CAAC;AACD,MAAMC,YAAY,GAAG,IAAI1K,cAAc,CAAC,aAAa,CAAC;;AAEtD;AACA;AACA;AACA;AACA;AACA;AACA,MAAM2K,aAAa,SAASrE,cAAc,CAAC;EACvCsE,eAAe;EACfC,yBAAyB;EACzBC,OAAO;EACPtG,WAAWA,CAACoG,eAAe,EAAEC,yBAAyB,EAAEC,OAAO,EAAE;IAC7D,KAAK,CAAC,CAAC;IACP,IAAI,CAACF,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,yBAAyB,GAAGA,yBAAyB;IAC1D,IAAI,CAACC,OAAO,GAAGA,OAAO;EAC1B;EACA;AACJ;AACA;AACA;EACIpE,qBAAqBA,CAACD,MAAM,EAAER,WAAW,EAAE;IACvC,MAAM8E,gBAAgB,GAAG,IAAI,CAACF,yBAAyB,CAACG,uBAAuB,CAACvE,MAAM,CAACb,SAAS,CAAC;IACjG,IAAIqF,YAAY;IAChB;IACA;IACA;IACA;IACA;IACAA,YAAY,GAAGF,gBAAgB,CAACG,MAAM,CAACzE,MAAM,CAACX,QAAQ,CAAC;IACvD;IACA;IACA;IACA;IACA,IAAI,CAACgF,OAAO,CAACK,UAAU,CAACF,YAAY,CAACG,QAAQ,CAAC;IAC9C,IAAI,CAACzE,YAAY,CAAC,MAAM;MACpB,IAAI,CAACmE,OAAO,CAACO,UAAU,CAACJ,YAAY,CAACG,QAAQ,CAAC;MAC9CH,YAAY,CAACK,OAAO,CAAC,CAAC;IAC1B,CAAC,CAAC;IACF;IACA;IACA,IAAIrF,WAAW,EAAE;MACb,IAAI,CAAC2E,eAAe,CAACW,YAAY,CAAC,IAAI,CAACC,qBAAqB,CAACP,YAAY,CAAC,EAAE,IAAI,CAACL,eAAe,CAACa,UAAU,CAAC;IAChH,CAAC,MACI;MACD,IAAI,CAACb,eAAe,CAACc,WAAW,CAAC,IAAI,CAACF,qBAAqB,CAACP,YAAY,CAAC,CAAC;IAC9E;IACA,OAAOA,YAAY;EACvB;EACA;EACAO,qBAAqBA,CAACP,YAAY,EAAE;IAChC,OAAOA,YAAY,CAACG,QAAQ,CAACO,SAAS,CAAC,CAAC,CAAC;EAC7C;AACJ;;AAEA;AACA,MAAMC,gBAAgB,CAAC;EACnBC,SAAS,GAAG5L,MAAM,CAACmB,QAAQ,CAAC;EAC5B0K,iBAAiB;EACjBC,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACD,iBAAiB,IAAI,IAAI,CAACA,iBAAiB,CAACE,UAAU,EAAE;MAC7D,IAAI,CAACF,iBAAiB,CAACE,UAAU,CAACC,WAAW,CAAC,IAAI,CAACH,iBAAiB,CAAC;IACzE;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACIrH,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAAC,IAAI,CAACqH,iBAAiB,EAAE;MACzB,IAAI,CAACI,gBAAgB,CAAC,CAAC;IAC3B;IACA,OAAO,IAAI,CAACJ,iBAAiB;EACjC;EACA;AACJ;AACA;AACA;AACA;EACII,gBAAgBA,CAAA,EAAG;IACf,MAAMC,SAAS,GAAG,IAAI,CAACN,SAAS,CAACO,aAAa,CAAC,KAAK,CAAC;IACrDD,SAAS,CAACE,SAAS,CAACC,GAAG,CAAC,mBAAmB,CAAC;IAC5CH,SAAS,CAACI,YAAY,CAAC,WAAW,EAAE,QAAQ,CAAC;IAC7C,IAAI,CAACV,SAAS,CAACW,IAAI,CAACd,WAAW,CAACS,SAAS,CAAC;IAC1C,IAAI,CAACL,iBAAiB,GAAGK,SAAS;EACtC;EACA,OAAOxH,IAAI,YAAA8H,yBAAA5H,iBAAA;IAAA,YAAAA,iBAAA,IAAwF+G,gBAAgB;EAAA;EACnH,OAAOc,KAAK,kBAjU6E5M,EAAE,CAAA6M,kBAAA;IAAAC,KAAA,EAiUYhB,gBAAgB;IAAAiB,OAAA,EAAhBjB,gBAAgB,CAAAjH,IAAA;IAAAmI,UAAA,EAAc;EAAM;AAC/I;AACA;EAAA,QAAAxH,SAAA,oBAAAA,SAAA,KAnU6FxF,EAAE,CAAAyF,iBAAA,CAmUJqG,gBAAgB,EAAc,CAAC;IAC9G1G,IAAI,EAAEhF,UAAU;IAChBsF,IAAI,EAAE,CAAC;MAAEsH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA,MAAMC,UAAU,CAAC;EACbC,WAAW;EACXxI,WAAWA,CAACwI,WAAW,EAAE;IACrB,IAAI,CAACA,WAAW,GAAGA,WAAW;EAClC;EACAjH,MAAMA,CAACU,MAAM,EAAER,WAAW,GAAG,IAAI,EAAE;IAC/B,OAAO,IAAI,CAAC+G,WAAW,CAACjH,MAAM,CAACU,MAAM,EAAER,WAAW,CAAC;EACvD;EACA;AACJ;AACA;AACA;EACIC,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAAC8G,WAAW,CAAC9G,MAAM,CAAC,CAAC;EACpC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM+G,OAAO,CAAC;EACVC,iBAAiB,GAAGjN,MAAM,CAAC2L,gBAAgB,CAAC;EAC5Cf,yBAAyB,GAAG5K,MAAM,CAACE,wBAAwB,CAAC;EAC5D2K,OAAO,GAAG7K,MAAM,CAACG,cAAc,CAAC;EAChCyL,SAAS,GAAG5L,MAAM,CAACmB,QAAQ,CAAC;EAC5B;EACA+L,aAAa,GAAG,IAAIC,GAAG,CAAC,CAAC;EACzB;AACJ;AACA;AACA;EACIlC,MAAMA,CAACb,aAAa,EAAEgD,gBAAgB,EAAE;IACpC;IACA,OAAO,IAAI,CAACC,iBAAiB,CAAC,IAAI,CAACC,cAAc,CAAClD,aAAa,EAAEgD,gBAAgB,CAAC,CAAC;EACvF;EACAE,cAAcA,CAAClD,aAAa,GAAG,EAAE,EAAEgD,gBAAgB,EAAE;IACjD,IAAI,CAAC,IAAI,CAACF,aAAa,CAACK,GAAG,CAACH,gBAAgB,CAAC,EAAE;MAC3C,IAAI,CAACF,aAAa,CAACM,GAAG,CAACJ,gBAAgB,EAAE,CAAC,CAAC,CAAC;IAChD;IACA,IAAI,CAAC,IAAI,CAACF,aAAa,CAACK,GAAG,CAACH,gBAAgB,CAAC,CAAChD,aAAa,CAAC,EAAE;MAC1D,IAAI,CAAC8C,aAAa,CAACK,GAAG,CAACH,gBAAgB,CAAC,CAAChD,aAAa,CAAC,GAAG,IAAI,CAACqD,kBAAkB,CAACrD,aAAa,EAAEgD,gBAAgB,CAAC;IACtH;IACA,OAAO,IAAI,CAACF,aAAa,CAACK,GAAG,CAACH,gBAAgB,CAAC,CAAChD,aAAa,CAAC;EAClE;EACA;AACJ;AACA;AACA;EACIqD,kBAAkBA,CAACrD,aAAa,EAAEgD,gBAAgB,EAAE;IAChD,MAAMM,IAAI,GAAG,IAAI,CAAC9B,SAAS,CAACO,aAAa,CAAC,KAAK,CAAC;IAChDuB,IAAI,CAACC,EAAE,GAAG,iBAAiB;IAC3BD,IAAI,CAACtB,SAAS,CAACC,GAAG,CAACjC,aAAa,CAAC;IACjCsD,IAAI,CAACtB,SAAS,CAACC,GAAG,CAAC,iBAAiB,CAAC;IACrC,IAAI,CAACe,gBAAgB,EAAE;MACnB,IAAI,CAACH,iBAAiB,CAACzI,mBAAmB,CAAC,CAAC,CAACiH,WAAW,CAACiC,IAAI,CAAC;IAClE,CAAC,MACI;MACDN,gBAAgB,CAAC5I,mBAAmB,CAAC,CAAC,CAACiH,WAAW,CAACiC,IAAI,CAAC;IAC5D;IACA,OAAOA,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACIE,iBAAiBA,CAACF,IAAI,EAAE;IACpB,OAAO,IAAIhD,aAAa,CAACgD,IAAI,EAAE,IAAI,CAAC9C,yBAAyB,EAAE,IAAI,CAACC,OAAO,CAAC;EAChF;EACA;AACJ;AACA;AACA;EACIwC,iBAAiBA,CAACK,IAAI,EAAE;IACpB,OAAO,IAAIZ,UAAU,CAAC,IAAI,CAACc,iBAAiB,CAACF,IAAI,CAAC,CAAC;EACvD;EACA,OAAOhJ,IAAI,YAAAmJ,gBAAAjJ,iBAAA;IAAA,YAAAA,iBAAA,IAAwFoI,OAAO;EAAA;EAC1G,OAAOP,KAAK,kBA9Z6E5M,EAAE,CAAA6M,kBAAA;IAAAC,KAAA,EA8ZYK,OAAO;IAAAJ,OAAA,EAAPI,OAAO,CAAAtI,IAAA;IAAAmI,UAAA,EAAc;EAAM;AACtI;AACA;EAAA,QAAAxH,SAAA,oBAAAA,SAAA,KAha6FxF,EAAE,CAAAyF,iBAAA,CAgaJ0H,OAAO,EAAc,CAAC;IACrG/H,IAAI,EAAEhF,UAAU;IAChBsF,IAAI,EAAE,CAAC;MAAEsH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;AAEV,MAAMiB,aAAa,CAAC;EAChBC,OAAO;EACPC,SAAS;EACTC,SAAS;EACTC,MAAM;EACNC,YAAY;EACZC,eAAe,GAAG,CAAC;EACnBC,MAAM,GAAG,EAAE;EACXjB,gBAAgB;EAChBkB,oBAAoB;EACpBC,KAAK,GAAG,CAAC;EACThK,WAAWA,CAACoI,KAAK,EAAEoB,OAAO,EAAEC,SAAS,EAAEC,SAAS,EAAEC,MAAM,EAAE;IACtD,IAAI,CAACH,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,YAAY,GAAG;MAChB,GAAGxB,KAAK,CAAC6B,OAAO;MAChB,GAAG7B,KAAK,CAACtE;IACb,CAAC;IACD,IAAIsE,KAAK,CAACtE,MAAM,CAACmB,WAAW,EAAE;MAC1B,IAAI,CAAC2E,YAAY,CAAC3E,WAAW,GAAG;QAC5B,GAAGmD,KAAK,CAAC6B,OAAO,CAAChF,WAAW;QAC5B,GAAGmD,KAAK,CAACtE,MAAM,CAACmB;MACpB,CAAC;IACL;EACJ;EACA;EACAiF,IAAIA,CAACjL,OAAO,EAAEL,KAAK,EAAEuL,QAAQ,GAAG,CAAC,CAAC,EAAEzJ,IAAI,GAAG,EAAE,EAAE;IAC3C,OAAO,IAAI,CAAC0J,qBAAqB,CAAC1J,IAAI,EAAEzB,OAAO,EAAEL,KAAK,EAAE,IAAI,CAACyL,WAAW,CAACF,QAAQ,CAAC,CAAC;EACvF;EACA;EACA/E,OAAOA,CAACnG,OAAO,EAAEL,KAAK,EAAEuL,QAAQ,GAAG,CAAC,CAAC,EAAE;IACnC,MAAMzJ,IAAI,GAAG,IAAI,CAACkJ,YAAY,CAAC3E,WAAW,CAACG,OAAO,IAAI,EAAE;IACxD,OAAO,IAAI,CAACgF,qBAAqB,CAAC1J,IAAI,EAAEzB,OAAO,EAAEL,KAAK,EAAE,IAAI,CAACyL,WAAW,CAACF,QAAQ,CAAC,CAAC;EACvF;EACA;EACAjF,KAAKA,CAACjG,OAAO,EAAEL,KAAK,EAAEuL,QAAQ,GAAG,CAAC,CAAC,EAAE;IACjC,MAAMzJ,IAAI,GAAG,IAAI,CAACkJ,YAAY,CAAC3E,WAAW,CAACC,KAAK,IAAI,EAAE;IACtD,OAAO,IAAI,CAACkF,qBAAqB,CAAC1J,IAAI,EAAEzB,OAAO,EAAEL,KAAK,EAAE,IAAI,CAACyL,WAAW,CAACF,QAAQ,CAAC,CAAC;EACvF;EACA;EACAhF,IAAIA,CAAClG,OAAO,EAAEL,KAAK,EAAEuL,QAAQ,GAAG,CAAC,CAAC,EAAE;IAChC,MAAMzJ,IAAI,GAAG,IAAI,CAACkJ,YAAY,CAAC3E,WAAW,CAACE,IAAI,IAAI,EAAE;IACrD,OAAO,IAAI,CAACiF,qBAAqB,CAAC1J,IAAI,EAAEzB,OAAO,EAAEL,KAAK,EAAE,IAAI,CAACyL,WAAW,CAACF,QAAQ,CAAC,CAAC;EACvF;EACA;EACA9E,OAAOA,CAACpG,OAAO,EAAEL,KAAK,EAAEuL,QAAQ,GAAG,CAAC,CAAC,EAAE;IACnC,MAAMzJ,IAAI,GAAG,IAAI,CAACkJ,YAAY,CAAC3E,WAAW,CAACI,OAAO,IAAI,EAAE;IACxD,OAAO,IAAI,CAAC+E,qBAAqB,CAAC1J,IAAI,EAAEzB,OAAO,EAAEL,KAAK,EAAE,IAAI,CAACyL,WAAW,CAACF,QAAQ,CAAC,CAAC;EACvF;EACA;AACJ;AACA;EACIG,KAAKA,CAACzG,OAAO,EAAE;IACX;IACA,KAAK,MAAM0G,KAAK,IAAI,IAAI,CAACT,MAAM,EAAE;MAC7B,IAAIjG,OAAO,KAAKlC,SAAS,EAAE;QACvB,IAAI4I,KAAK,CAAC1G,OAAO,KAAKA,OAAO,EAAE;UAC3B0G,KAAK,CAACvG,QAAQ,CAACnB,WAAW,CAAC,CAAC;UAC5B;QACJ;MACJ,CAAC,MACI;QACD0H,KAAK,CAACvG,QAAQ,CAACnB,WAAW,CAAC,CAAC;MAChC;IACJ;EACJ;EACA;AACJ;AACA;EACIhF,MAAMA,CAACgG,OAAO,EAAE;IACZ,MAAM2G,KAAK,GAAG,IAAI,CAACC,UAAU,CAAC5G,OAAO,CAAC;IACtC,IAAI,CAAC2G,KAAK,EAAE;MACR,OAAO,KAAK;IAChB;IACAA,KAAK,CAACE,WAAW,CAAC1G,QAAQ,CAACZ,KAAK,CAAC,CAAC;IAClC,IAAI,CAAC0G,MAAM,CAACa,MAAM,CAACH,KAAK,CAACR,KAAK,EAAE,CAAC,CAAC;IAClC,IAAI,CAACH,eAAe,GAAG,IAAI,CAACA,eAAe,GAAG,CAAC;IAC/C,IAAI,CAAC,IAAI,CAACD,YAAY,CAACjF,SAAS,IAAI,CAAC,IAAI,CAACmF,MAAM,CAACc,MAAM,EAAE;MACrD,OAAO,KAAK;IAChB;IACA,IAAI,IAAI,CAACf,eAAe,GAAG,IAAI,CAACD,YAAY,CAACjF,SAAS,IAAI,IAAI,CAACmF,MAAM,CAAC,IAAI,CAACD,eAAe,CAAC,EAAE;MACzF,MAAMgB,CAAC,GAAG,IAAI,CAACf,MAAM,CAAC,IAAI,CAACD,eAAe,CAAC,CAAC7F,QAAQ;MACpD,IAAI,CAAC6G,CAAC,CAACvH,UAAU,CAAC,CAAC,EAAE;QACjB,IAAI,CAACuG,eAAe,GAAG,IAAI,CAACA,eAAe,GAAG,CAAC;QAC/CgB,CAAC,CAACrH,QAAQ,CAAC,CAAC;MAChB;IACJ;IACA,OAAO,IAAI;EACf;EACA;AACJ;AACA;EACIsH,aAAaA,CAAClM,KAAK,GAAG,EAAE,EAAEK,OAAO,GAAG,EAAE,EAAE8L,gBAAgB,EAAEjG,eAAe,EAAE;IACvE,MAAM;MAAEE;IAAuB,CAAC,GAAG,IAAI,CAAC4E,YAAY;IACpD,KAAK,MAAMW,KAAK,IAAI,IAAI,CAACT,MAAM,EAAE;MAC7B,MAAMkB,iBAAiB,GAAGhG,sBAAsB,IAAIuF,KAAK,CAAC3L,KAAK,KAAKA,KAAK;MACzE,IAAI,CAAC,CAACoG,sBAAsB,IAAIgG,iBAAiB,KAAKT,KAAK,CAACtL,OAAO,KAAKA,OAAO,EAAE;QAC7EsL,KAAK,CAACvG,QAAQ,CAACN,WAAW,CAACqH,gBAAgB,EAAEjG,eAAe,CAAC;QAC7D,OAAOyF,KAAK;MAChB;IACJ;IACA,OAAO,IAAI;EACf;EACA;EACAF,WAAWA,CAACF,QAAQ,GAAG,CAAC,CAAC,EAAE;IACvB,OAAO;MAAE,GAAG,IAAI,CAACP,YAAY;MAAE,GAAGO;IAAS,CAAC;EAChD;EACA;AACJ;AACA;EACIM,UAAUA,CAAC5G,OAAO,EAAE;IAChB,KAAK,IAAIoH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACnB,MAAM,CAACc,MAAM,EAAEK,CAAC,EAAE,EAAE;MACzC,IAAI,IAAI,CAACnB,MAAM,CAACmB,CAAC,CAAC,CAACpH,OAAO,KAAKA,OAAO,EAAE;QACpC,OAAO;UAAEmG,KAAK,EAAEiB,CAAC;UAAEP,WAAW,EAAE,IAAI,CAACZ,MAAM,CAACmB,CAAC;QAAE,CAAC;MACpD;IACJ;IACA,OAAO,IAAI;EACf;EACA;AACJ;AACA;EACIb,qBAAqBA,CAACrG,SAAS,EAAE9E,OAAO,EAAEL,KAAK,EAAEkF,MAAM,EAAE;IACrD,IAAIA,MAAM,CAACkC,cAAc,EAAE;MACvB,OAAO,IAAI,CAAC2D,MAAM,CAACuB,GAAG,CAAC,MAAM,IAAI,CAACC,kBAAkB,CAACpH,SAAS,EAAE9E,OAAO,EAAEL,KAAK,EAAEkF,MAAM,CAAC,CAAC;IAC5F;IACA,OAAO,IAAI,CAACqH,kBAAkB,CAACpH,SAAS,EAAE9E,OAAO,EAAEL,KAAK,EAAEkF,MAAM,CAAC;EACrE;EACA;AACJ;AACA;AACA;EACIqH,kBAAkBA,CAACpH,SAAS,EAAE9E,OAAO,EAAEL,KAAK,EAAEkF,MAAM,EAAE;IAClD,IAAI,CAACA,MAAM,CAACsH,cAAc,EAAE;MACxB,MAAM,IAAIC,KAAK,CAAC,yBAAyB,CAAC;IAC9C;IACA;IACA;IACA;IACA,MAAMC,SAAS,GAAG,IAAI,CAACR,aAAa,CAAClM,KAAK,EAAEK,OAAO,EAAE,IAAI,CAAC2K,YAAY,CAAC7E,uBAAuB,IAAIjB,MAAM,CAAC0B,OAAO,GAAG,CAAC,EAAE,IAAI,CAACoE,YAAY,CAAC9E,eAAe,CAAC;IACxJ,IAAI,CAAE,IAAI,CAAC8E,YAAY,CAAC5E,sBAAsB,IAAIpG,KAAK,IAAKK,OAAO,KAC/D,IAAI,CAAC2K,YAAY,CAAC/E,iBAAiB,IACnCyG,SAAS,KAAK,IAAI,EAAE;MACpB,OAAOA,SAAS;IACpB;IACA,IAAI,CAACvB,oBAAoB,GAAG9K,OAAO;IACnC,IAAIsM,YAAY,GAAG,KAAK;IACxB,IAAI,IAAI,CAAC3B,YAAY,CAACjF,SAAS,IAAI,IAAI,CAACkF,eAAe,IAAI,IAAI,CAACD,YAAY,CAACjF,SAAS,EAAE;MACpF4G,YAAY,GAAG,IAAI;MACnB,IAAI,IAAI,CAAC3B,YAAY,CAAChF,WAAW,EAAE;QAC/B,IAAI,CAAC0F,KAAK,CAAC,IAAI,CAACR,MAAM,CAAC,CAAC,CAAC,CAACjG,OAAO,CAAC;MACtC;IACJ;IACA,MAAM2H,UAAU,GAAG,IAAI,CAAChC,OAAO,CAAC9C,MAAM,CAAC5C,MAAM,CAAC+B,aAAa,EAAE,IAAI,CAACgD,gBAAgB,CAAC;IACnF,IAAI,CAACmB,KAAK,GAAG,IAAI,CAACA,KAAK,GAAG,CAAC;IAC3B,IAAIyB,gBAAgB,GAAGxM,OAAO;IAC9B,IAAIA,OAAO,IAAI6E,MAAM,CAAC4B,UAAU,EAAE;MAC9B+F,gBAAgB,GAAG,IAAI,CAAC/B,SAAS,CAACgC,QAAQ,CAAC7P,eAAe,CAAC8P,IAAI,EAAE1M,OAAO,CAAC;IAC7E;IACA,MAAM+E,QAAQ,GAAG,IAAI3B,QAAQ,CAACmJ,UAAU,CAAC;IACzC,MAAMI,YAAY,GAAG,IAAIhI,YAAY,CAAC,IAAI,CAACoG,KAAK,EAAElG,MAAM,EAAE2H,gBAAgB,EAAE7M,KAAK,EAAEmF,SAAS,EAAEC,QAAQ,CAAC;IACvG;IACA,MAAM6H,SAAS,GAAG,CAAC;MAAEC,OAAO,EAAElI,YAAY;MAAEmI,QAAQ,EAAEH;IAAa,CAAC,CAAC;IACrE,MAAMI,aAAa,GAAGlQ,QAAQ,CAAC4K,MAAM,CAAC;MAAEmF,SAAS;MAAEI,MAAM,EAAE,IAAI,CAACxC;IAAU,CAAC,CAAC;IAC5E,MAAMrI,SAAS,GAAG,IAAIF,eAAe,CAAC4C,MAAM,CAACsH,cAAc,EAAEY,aAAa,CAAC;IAC3E,MAAM/J,MAAM,GAAGuJ,UAAU,CAACjK,MAAM,CAACH,SAAS,EAAE0C,MAAM,CAACrC,WAAW,CAAC;IAC/DuC,QAAQ,CAACzB,iBAAiB,GAAGN,MAAM,CAACiK,QAAQ;IAC5C,MAAMC,GAAG,GAAG;MACRtI,OAAO,EAAE,IAAI,CAACmG,KAAK;MACnBpL,KAAK,EAAEA,KAAK,IAAI,EAAE;MAClBK,OAAO,EAAEA,OAAO,IAAI,EAAE;MACtB+E,QAAQ;MACRoI,OAAO,EAAEpI,QAAQ,CAACP,aAAa,CAAC,CAAC;MACjC4I,QAAQ,EAAErI,QAAQ,CAACX,WAAW,CAAC,CAAC;MAChCiB,KAAK,EAAEsH,YAAY,CAACtH,KAAK,CAAC,CAAC;MAC3BG,QAAQ,EAAEmH,YAAY,CAACnH,QAAQ,CAAC,CAAC;MACjCxC;IACJ,CAAC;IACD,IAAI,CAACsJ,YAAY,EAAE;MACf,IAAI,CAAC1B,eAAe,GAAG,IAAI,CAACA,eAAe,GAAG,CAAC;MAC/CyC,UAAU,CAAC,MAAM;QACbH,GAAG,CAACnI,QAAQ,CAACR,QAAQ,CAAC,CAAC;MAC3B,CAAC,CAAC;IACN;IACA,IAAI,CAACsG,MAAM,CAACyC,IAAI,CAACJ,GAAG,CAAC;IACrB,OAAOA,GAAG;EACd;EACA,OAAOhM,IAAI,YAAAqM,sBAAAnM,iBAAA;IAAA,YAAAA,iBAAA,IAAwFkJ,aAAa,EAjmBvBjO,EAAE,CAAAmR,QAAA,CAimBuCvG,YAAY,GAjmBrD5K,EAAE,CAAAmR,QAAA,CAimBgEhE,OAAO,GAjmBzEnN,EAAE,CAAAmR,QAAA,CAimBoFnR,EAAE,CAACQ,QAAQ,GAjmBjGR,EAAE,CAAAmR,QAAA,CAimB4G1P,EAAE,CAAC2P,YAAY,GAjmB7HpR,EAAE,CAAAmR,QAAA,CAimBwInR,EAAE,CAACqR,MAAM;EAAA;EAC5O,OAAOzE,KAAK,kBAlmB6E5M,EAAE,CAAA6M,kBAAA;IAAAC,KAAA,EAkmBYmB,aAAa;IAAAlB,OAAA,EAAbkB,aAAa,CAAApJ,IAAA;IAAAmI,UAAA,EAAc;EAAM;AAC5I;AACA;EAAA,QAAAxH,SAAA,oBAAAA,SAAA,KApmB6FxF,EAAE,CAAAyF,iBAAA,CAomBJwI,aAAa,EAAc,CAAC;IAC3G7I,IAAI,EAAEhF,UAAU;IAChBsF,IAAI,EAAE,CAAC;MAAEsH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE5H,IAAI,EAAEiB,SAAS;IAAEiL,UAAU,EAAE,CAAC;MAC/ClM,IAAI,EAAE3E,MAAM;MACZiF,IAAI,EAAE,CAACkF,YAAY;IACvB,CAAC;EAAE,CAAC,EAAE;IAAExF,IAAI,EAAE+H;EAAQ,CAAC,EAAE;IAAE/H,IAAI,EAAEpF,EAAE,CAACQ;EAAS,CAAC,EAAE;IAAE4E,IAAI,EAAE3D,EAAE,CAAC2P;EAAa,CAAC,EAAE;IAAEhM,IAAI,EAAEpF,EAAE,CAACqR;EAAO,CAAC,CAAC;AAAA;AAE/G,MAAME,KAAK,CAAC;EACRC,aAAa;EACblB,YAAY;EACZjC,MAAM;EACN1K,OAAO;EACPL,KAAK;EACLH,OAAO;EACPJ,eAAe;EACf0O,eAAe;EACf;EACAzN,KAAK,GAAGtD,MAAM,CAAC,CAAC,CAAC,CAAC;EAClB;EACAgR,YAAY,GAAG,EAAE;EACjBxQ,KAAK;EACL;EACA,IAAIyQ,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACzQ,KAAK,CAAC,CAAC;EACvB;EACA;EACA,IAAI0Q,YAAYA,CAAA,EAAG;IACf,IAAI,IAAI,CAAC1Q,KAAK,CAAC,CAAC,CAAC2Q,KAAK,KAAK,UAAU,EAAE;MACnC,OAAO,MAAM;IACjB;IACA;EACJ;EACAC,OAAO;EACPC,UAAU;EACVC,QAAQ;EACRC,GAAG;EACHC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJ1N,WAAWA,CAAC8M,aAAa,EAAElB,YAAY,EAAEjC,MAAM,EAAE;IAC7C,IAAI,CAACmD,aAAa,GAAGA,aAAa;IAClC,IAAI,CAAClB,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACjC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC1K,OAAO,GAAG2M,YAAY,CAAC3M,OAAO;IACnC,IAAI,CAACL,KAAK,GAAGgN,YAAY,CAAChN,KAAK;IAC/B,IAAI,CAACH,OAAO,GAAGmN,YAAY,CAAC9H,MAAM;IAClC,IAAI,CAACiJ,eAAe,GAAGnB,YAAY,CAAC9H,MAAM,CAAC0B,OAAO;IAClD,IAAI,CAACwH,YAAY,GAAG,GAAGpB,YAAY,CAAC7H,SAAS,IAAI6H,YAAY,CAAC9H,MAAM,CAAC8B,UAAU,EAAE;IACjF,IAAI,CAAC2H,GAAG,GAAG3B,YAAY,CAAC5H,QAAQ,CAACP,aAAa,CAAC,CAAC,CAACU,SAAS,CAAC,MAAM;MAC7D,IAAI,CAACwJ,aAAa,CAAC,CAAC;IACxB,CAAC,CAAC;IACF,IAAI,CAACH,IAAI,GAAG5B,YAAY,CAAC5H,QAAQ,CAAChB,YAAY,CAAC,CAAC,CAACmB,SAAS,CAAC,MAAM;MAC7D,IAAI,CAACtG,MAAM,CAAC,CAAC;IACjB,CAAC,CAAC;IACF,IAAI,CAAC4P,IAAI,GAAG7B,YAAY,CAAC5H,QAAQ,CAACd,YAAY,CAAC,CAAC,CAACiB,SAAS,CAAC,MAAM;MAC7D,IAAI,CAACR,YAAY,CAAC,CAAC;IACvB,CAAC,CAAC;IACF,IAAI,CAAC+J,IAAI,GAAG9B,YAAY,CAAC5H,QAAQ,CAACb,cAAc,CAAC,CAAC,CAACgB,SAAS,CAACyJ,KAAK,IAAI;MAClE,IAAI,CAACvP,eAAe,GAAGuP,KAAK;IAChC,CAAC,CAAC;IACF,IAAI,CAACpR,KAAK,GAAGR,MAAM,CAAC;MAChBmR,KAAK,EAAE,UAAU;MACjBU,MAAM,EAAE;QACJ9H,QAAQ,EAAE,IAAI,CAAC6F,YAAY,CAAC9H,MAAM,CAACiC,QAAQ;QAC3CD,MAAM,EAAE;MACZ;IACJ,CAAC,CAAC;EACN;EACAyB,WAAWA,CAAA,EAAG;IACV,IAAI,CAACgG,GAAG,CAACO,WAAW,CAAC,CAAC;IACtB,IAAI,CAACN,IAAI,CAACM,WAAW,CAAC,CAAC;IACvB,IAAI,CAACL,IAAI,CAACK,WAAW,CAAC,CAAC;IACvB,IAAI,CAACJ,IAAI,CAACI,WAAW,CAAC,CAAC;IACvBC,aAAa,CAAC,IAAI,CAACV,UAAU,CAAC;IAC9BW,YAAY,CAAC,IAAI,CAACZ,OAAO,CAAC;EAC9B;EACA;AACJ;AACA;EACIO,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACnR,KAAK,CAACyR,MAAM,CAACzR,KAAK,KAAK;MAAE,GAAGA,KAAK;MAAE2Q,KAAK,EAAE;IAAS,CAAC,CAAC,CAAC;IAC3D,IAAI,EAAE,IAAI,CAAC1O,OAAO,CAAC8G,cAAc,KAAK,IAAI,IAAI,IAAI,CAAC9G,OAAO,CAAC8G,cAAc,KAAK,SAAS,CAAC,IACpF,IAAI,CAAC9G,OAAO,CAAC+G,OAAO,EAAE;MACtB,IAAI,CAAC0I,cAAc,CAAC,MAAM,IAAI,CAACrQ,MAAM,CAAC,CAAC,EAAE,IAAI,CAACY,OAAO,CAAC+G,OAAO,CAAC;MAC9D,IAAI,CAAC8H,QAAQ,GAAG,IAAIa,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC3P,OAAO,CAAC+G,OAAO;MAC3D,IAAI,IAAI,CAAC/G,OAAO,CAACkH,WAAW,EAAE;QAC1B,IAAI,CAAC0I,eAAe,CAAC,MAAM,IAAI,CAACC,cAAc,CAAC,CAAC,EAAE,EAAE,CAAC;MACzD;IACJ;EACJ;EACA;AACJ;AACA;EACIA,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAAChP,KAAK,CAAC,CAAC,KAAK,CAAC,IAAI,IAAI,CAACA,KAAK,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAACb,OAAO,CAAC+G,OAAO,EAAE;MACrE;IACJ;IACA,MAAM+I,GAAG,GAAG,IAAIJ,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;IAChC,MAAMI,SAAS,GAAG,IAAI,CAAClB,QAAQ,GAAGiB,GAAG;IACrC,IAAI,CAACjP,KAAK,CAAC2J,GAAG,CAAEuF,SAAS,GAAG,IAAI,CAAC/P,OAAO,CAAC+G,OAAO,GAAI,GAAG,CAAC;IACxD,IAAI,IAAI,CAAC/G,OAAO,CAACwH,iBAAiB,KAAK,YAAY,EAAE;MACjD,IAAI,CAAC3G,KAAK,CAAC2O,MAAM,CAAC3O,KAAK,IAAI,GAAG,GAAGA,KAAK,CAAC;IAC3C;IACA,IAAI,IAAI,CAACA,KAAK,CAAC,CAAC,IAAI,CAAC,EAAE;MACnB,IAAI,CAACA,KAAK,CAAC2J,GAAG,CAAC,CAAC,CAAC;IACrB;IACA,IAAI,IAAI,CAAC3J,KAAK,CAAC,CAAC,IAAI,GAAG,EAAE;MACrB,IAAI,CAACA,KAAK,CAAC2J,GAAG,CAAC,GAAG,CAAC;IACvB;EACJ;EACAtF,YAAYA,CAAA,EAAG;IACXqK,YAAY,CAAC,IAAI,CAACZ,OAAO,CAAC;IAC1BW,aAAa,CAAC,IAAI,CAACV,UAAU,CAAC;IAC9B,IAAI,CAAC7Q,KAAK,CAACyR,MAAM,CAACzR,KAAK,KAAK;MAAE,GAAGA,KAAK;MAAE2Q,KAAK,EAAE;IAAS,CAAC,CAAC,CAAC;IAC3D,IAAI,CAACe,cAAc,CAAC,MAAM,IAAI,CAACrQ,MAAM,CAAC,CAAC,EAAE,IAAI,CAACkP,eAAe,CAAC;IAC9D,IAAI,CAACtO,OAAO,CAAC+G,OAAO,GAAG,IAAI,CAACuH,eAAe;IAC3C,IAAI,CAACO,QAAQ,GAAG,IAAIa,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,IAAI,IAAI,CAAC3P,OAAO,CAAC+G,OAAO,IAAI,CAAC,CAAC;IAClE,IAAI,CAAClG,KAAK,CAAC2J,GAAG,CAAC,CAAC,CAAC,CAAC;IAClB,IAAI,IAAI,CAACxK,OAAO,CAACkH,WAAW,EAAE;MAC1B,IAAI,CAAC0I,eAAe,CAAC,MAAM,IAAI,CAACC,cAAc,CAAC,CAAC,EAAE,EAAE,CAAC;IACzD;EACJ;EACA;AACJ;AACA;EACIzQ,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAACrB,KAAK,CAAC,CAAC,CAAC2Q,KAAK,KAAK,SAAS,EAAE;MAClC;IACJ;IACAa,YAAY,CAAC,IAAI,CAACZ,OAAO,CAAC;IAC1B,IAAI,CAAC5Q,KAAK,CAACyR,MAAM,CAACzR,KAAK,KAAK;MAAE,GAAGA,KAAK;MAAE2Q,KAAK,EAAE;IAAU,CAAC,CAAC,CAAC;IAC5D,IAAI,CAACe,cAAc,CAAC,MAAM,IAAI,CAACpB,aAAa,CAACjP,MAAM,CAAC,IAAI,CAAC+N,YAAY,CAAC/H,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC+H,YAAY,CAAC9H,MAAM,CAACiC,QAAQ,CAAC;EACvH;EACA0I,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACjS,KAAK,CAAC,CAAC,CAAC2Q,KAAK,KAAK,SAAS,EAAE;MAClC;IACJ;IACA,IAAI,CAACvB,YAAY,CAACxH,UAAU,CAAC,CAAC;IAC9B,IAAI,IAAI,CAAC3F,OAAO,CAAC4F,YAAY,EAAE;MAC3B,IAAI,CAACxG,MAAM,CAAC,CAAC;IACjB;EACJ;EACA6Q,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAAClS,KAAK,CAAC,CAAC,CAAC2Q,KAAK,KAAK,SAAS,EAAE;MAClC;IACJ;IACA,IAAI,IAAI,CAAC1O,OAAO,CAAC8G,cAAc,KAAK,iBAAiB,EAAE;MACnDyI,YAAY,CAAC,IAAI,CAACZ,OAAO,CAAC;MAC1B,IAAI,CAAC3O,OAAO,CAAC+G,OAAO,GAAG,CAAC;MACxB,IAAI,CAAC8H,QAAQ,GAAG,CAAC;MACjB;MACAS,aAAa,CAAC,IAAI,CAACV,UAAU,CAAC;MAC9B,IAAI,CAAC/N,KAAK,CAAC2J,GAAG,CAAC,CAAC,CAAC;IACrB;EACJ;EACA0F,gBAAgBA,CAAA,EAAG;IACf,IAAK,IAAI,CAAClQ,OAAO,CAAC8G,cAAc,KAAK,IAAI,IAAI,IAAI,CAAC9G,OAAO,CAAC8G,cAAc,KAAK,iBAAiB,IAC1F,IAAI,CAAC9G,OAAO,CAACgH,eAAe,KAAK,CAAC,IAClC,IAAI,CAACjJ,KAAK,CAAC,CAAC,CAAC2Q,KAAK,KAAK,SAAS,EAAE;MAClC;IACJ;IACA,IAAI,CAACe,cAAc,CAAC,MAAM,IAAI,CAACrQ,MAAM,CAAC,CAAC,EAAE,IAAI,CAACY,OAAO,CAACgH,eAAe,CAAC;IACtE,IAAI,CAAChH,OAAO,CAAC+G,OAAO,GAAG,IAAI,CAAC/G,OAAO,CAACgH,eAAe;IACnD,IAAI,CAAC6H,QAAQ,GAAG,IAAIa,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,IAAI,IAAI,CAAC3P,OAAO,CAAC+G,OAAO,IAAI,CAAC,CAAC;IAClE,IAAI,CAAClG,KAAK,CAAC2J,GAAG,CAAC,CAAC,CAAC,CAAC;IAClB,IAAI,IAAI,CAACxK,OAAO,CAACkH,WAAW,EAAE;MAC1B,IAAI,CAAC0I,eAAe,CAAC,MAAM,IAAI,CAACC,cAAc,CAAC,CAAC,EAAE,EAAE,CAAC;IACzD;EACJ;EACAJ,cAAcA,CAACU,IAAI,EAAExB,OAAO,EAAE;IAC1B,IAAI,IAAI,CAACzD,MAAM,EAAE;MACb,IAAI,CAACA,MAAM,CAACkF,iBAAiB,CAAC,MAAO,IAAI,CAACzB,OAAO,GAAGd,UAAU,CAAC,MAAM,IAAI,CAACwC,gBAAgB,CAACF,IAAI,CAAC,EAAExB,OAAO,CAAE,CAAC;IAChH,CAAC,MACI;MACD,IAAI,CAACA,OAAO,GAAGd,UAAU,CAAC,MAAMsC,IAAI,CAAC,CAAC,EAAExB,OAAO,CAAC;IACpD;EACJ;EACAiB,eAAeA,CAACO,IAAI,EAAExB,OAAO,EAAE;IAC3B,IAAI,IAAI,CAACzD,MAAM,EAAE;MACb,IAAI,CAACA,MAAM,CAACkF,iBAAiB,CAAC,MAAO,IAAI,CAACxB,UAAU,GAAG0B,WAAW,CAAC,MAAM,IAAI,CAACD,gBAAgB,CAACF,IAAI,CAAC,EAAExB,OAAO,CAAE,CAAC;IACpH,CAAC,MACI;MACD,IAAI,CAACC,UAAU,GAAG0B,WAAW,CAAC,MAAMH,IAAI,CAAC,CAAC,EAAExB,OAAO,CAAC;IACxD;EACJ;EACA0B,gBAAgBA,CAACF,IAAI,EAAE;IACnB,IAAI,IAAI,CAACjF,MAAM,EAAE;MACb,IAAI,CAACA,MAAM,CAACuB,GAAG,CAAC,MAAM0D,IAAI,CAAC,CAAC,CAAC;IACjC,CAAC,MACI;MACDA,IAAI,CAAC,CAAC;IACV;EACJ;EACA,OAAOzO,IAAI,YAAA6O,cAAA3O,iBAAA;IAAA,YAAAA,iBAAA,IAAwFwM,KAAK,EAtyBfvR,EAAE,CAAAgF,iBAAA,CAsyB+BiJ,aAAa,GAtyB9CjO,EAAE,CAAAgF,iBAAA,CAsyByDsD,YAAY,GAtyBvEtI,EAAE,CAAAgF,iBAAA,CAsyBkFhF,EAAE,CAACqR,MAAM;EAAA;EACtL,OAAOsC,IAAI,kBAvyB8E3T,EAAE,CAAA4T,iBAAA;IAAAxO,IAAA,EAuyBJmM,KAAK;IAAAlM,SAAA;IAAAwO,QAAA;IAAAC,YAAA,WAAAC,mBAAAnS,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAvyBH5B,EAAE,CAAAiC,UAAA,mBAAA+R,+BAAA;UAAA,OAuyBJnS,GAAA,CAAAsR,QAAA,CAAS,CAAC;QAAA,CAAN,CAAC,wBAAAc,oCAAA;UAAA,OAALpS,GAAA,CAAAuR,WAAA,CAAY,CAAC;QAAA,CAAT,CAAC,wBAAAc,oCAAA;UAAA,OAALrS,GAAA,CAAAwR,gBAAA,CAAiB,CAAC;QAAA,CAAd,CAAC;MAAA;MAAA,IAAAzR,EAAA;QAvyBH5B,EAAE,CAAAmU,uBAAA,cAAAtS,GAAA,CAAA8P,MAuyBA,CAAC;QAvyBH3R,EAAE,CAAAkD,UAAA,CAAArB,GAAA,CAAA6P,YAuyBA,CAAC;QAvyBH1R,EAAE,CAAA+D,WAAA,YAAAlC,GAAA,CAAA+P,YAuyBA,CAAC;MAAA;IAAA;IAAArM,UAAA;IAAA6O,QAAA,GAvyBHpU,EAAE,CAAAqU,mBAAA;IAAAC,KAAA,EAAA5S,GAAA;IAAA6S,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,eAAA/S,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAF5B,EAAE,CAAAiD,UAAA,IAAAtB,uBAAA,mBAwyBqB,CAAC,IAAAqB,oBAAA,gBAG1C,CAAC,IAAAQ,oBAAA,gBAIpB,CAAC,IAAAK,oBAAA,gBAGK,CAAC,IAAAC,oBAAA,gBAG7B,CAAC;MAAA;MAAA,IAAAlC,EAAA;QArzB0D5B,EAAE,CAAAuD,UAAA,SAAA1B,GAAA,CAAAsB,OAAA,CAAA6G,WAwyB5D,CAAC;QAxyByDhK,EAAE,CAAA6C,SAAA,CA2yB7E,CAAC;QA3yB0E7C,EAAE,CAAAuD,UAAA,SAAA1B,GAAA,CAAAyB,KA2yB7E,CAAC;QA3yB0EtD,EAAE,CAAA6C,SAAA,CA8yBrD,CAAC;QA9yBkD7C,EAAE,CAAAuD,UAAA,SAAA1B,GAAA,CAAA8B,OAAA,IAAA9B,GAAA,CAAAsB,OAAA,CAAAiH,UA8yBrD,CAAC;QA9yBkDpK,EAAE,CAAA6C,SAAA,CAizBpD,CAAC;QAjzBiD7C,EAAE,CAAAuD,UAAA,SAAA1B,GAAA,CAAA8B,OAAA,KAAA9B,GAAA,CAAAsB,OAAA,CAAAiH,UAizBpD,CAAC;QAjzBiDpK,EAAE,CAAA6C,SAAA,CAqzB/D,CAAC;QArzB4D7C,EAAE,CAAAuD,UAAA,SAAA1B,GAAA,CAAAsB,OAAA,CAAAkH,WAqzB/D,CAAC;MAAA;IAAA;IAAAuK,YAAA,GAG8BrT,IAAI;IAAAsT,aAAA;IAAAC,IAAA;MAAAC,SAAA,EAA+E,CACtI9T,OAAO,CAAC,UAAU,EAAE,CAChBC,KAAK,CAAC,UAAU,EAAEC,KAAK,CAAC;QAAE6T,OAAO,EAAE;MAAE,CAAC,CAAC,CAAC,EACxC9T,KAAK,CAAC,QAAQ,EAAEC,KAAK,CAAC;QAAE6T,OAAO,EAAE;MAAE,CAAC,CAAC,CAAC,EACtC9T,KAAK,CAAC,SAAS,EAAEC,KAAK,CAAC;QAAE6T,OAAO,EAAE;MAAE,CAAC,CAAC,CAAC,EACvC5T,UAAU,CAAC,oBAAoB,EAAEC,OAAO,CAAC,+BAA+B,CAAC,CAAC,EAC1ED,UAAU,CAAC,mBAAmB,EAAEC,OAAO,CAAC,+BAA+B,CAAC,CAAC,CAC5E,CAAC;IACL;IAAA4T,eAAA;EAAA;AACT;AACA;EAAA,QAAAzP,SAAA,oBAAAA,SAAA,KAl0B6FxF,EAAE,CAAAyF,iBAAA,CAk0BJ8L,KAAK,EAAc,CAAC;IACnGnM,IAAI,EAAEzE,SAAS;IACf+E,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,mBAAmB;MAC7B+O,QAAQ,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MACiBQ,UAAU,EAAE,CACRjU,OAAO,CAAC,UAAU,EAAE,CAChBC,KAAK,CAAC,UAAU,EAAEC,KAAK,CAAC;QAAE6T,OAAO,EAAE;MAAE,CAAC,CAAC,CAAC,EACxC9T,KAAK,CAAC,QAAQ,EAAEC,KAAK,CAAC;QAAE6T,OAAO,EAAE;MAAE,CAAC,CAAC,CAAC,EACtC9T,KAAK,CAAC,SAAS,EAAEC,KAAK,CAAC;QAAE6T,OAAO,EAAE;MAAE,CAAC,CAAC,CAAC,EACvC5T,UAAU,CAAC,oBAAoB,EAAEC,OAAO,CAAC,+BAA+B,CAAC,CAAC,EAC1ED,UAAU,CAAC,mBAAmB,EAAEC,OAAO,CAAC,+BAA+B,CAAC,CAAC,CAC5E,CAAC,CACL;MACD8T,mBAAmB,EAAE,KAAK;MAC1B5P,UAAU,EAAE,IAAI;MAChB6P,OAAO,EAAE,CAAC7T,IAAI,CAAC;MACf0T,eAAe,EAAErU,uBAAuB,CAACyU;IAC7C,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEjQ,IAAI,EAAE6I;EAAc,CAAC,EAAE;IAAE7I,IAAI,EAAEkD;EAAa,CAAC,EAAE;IAAElD,IAAI,EAAEpF,EAAE,CAACqR;EAAO,CAAC,CAAC,EAAkB;IAAEK,YAAY,EAAE,CAAC;MAC3HtM,IAAI,EAAEvE,WAAW;MACjB6E,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAEiM,MAAM,EAAE,CAAC;MACTvM,IAAI,EAAEvE,WAAW;MACjB6E,IAAI,EAAE,CAAC,WAAW;IACtB,CAAC,CAAC;IAAEkM,YAAY,EAAE,CAAC;MACfxM,IAAI,EAAEvE,WAAW;MACjB6E,IAAI,EAAE,CAAC,eAAe;IAC1B,CAAC,CAAC;IAAEyN,QAAQ,EAAE,CAAC;MACX/N,IAAI,EAAEtE,YAAY;MAClB4E,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAE0N,WAAW,EAAE,CAAC;MACdhO,IAAI,EAAEtE,YAAY;MAClB4E,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAE2N,gBAAgB,EAAE,CAAC;MACnBjO,IAAI,EAAEtE,YAAY;MAClB4E,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM4P,mBAAmB,GAAG;EACxB,GAAGlM,8BAA8B;EACjC0G,cAAc,EAAEyB;AACpB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMgE,aAAa,GAAGA,CAAC/M,MAAM,GAAG,CAAC,CAAC,KAAK;EACnC,MAAM+H,SAAS,GAAG,CACd;IACIC,OAAO,EAAE5F,YAAY;IACrB6F,QAAQ,EAAE;MACN9B,OAAO,EAAE2G,mBAAmB;MAC5B9M;IACJ;EACJ,CAAC,CACJ;EACD,OAAOzH,wBAAwB,CAACwP,SAAS,CAAC;AAC9C,CAAC;AAED,MAAMiF,YAAY,CAAC;EACf,OAAOC,OAAOA,CAACjN,MAAM,GAAG,CAAC,CAAC,EAAE;IACxB,OAAO;MACHkN,QAAQ,EAAEF,YAAY;MACtBjF,SAAS,EAAE,CAACgF,aAAa,CAAC/M,MAAM,CAAC;IACrC,CAAC;EACL;EACA,OAAO3D,IAAI,YAAA8Q,qBAAA5Q,iBAAA;IAAA,YAAAA,iBAAA,IAAwFyQ,YAAY;EAAA;EAC/G,OAAOI,IAAI,kBAv6B8E5V,EAAE,CAAA6V,gBAAA;IAAAzQ,IAAA,EAu6BSoQ;EAAY;EAChH,OAAOM,IAAI,kBAx6B8E9V,EAAE,CAAA+V,gBAAA;AAy6B/F;AACA;EAAA,QAAAvQ,SAAA,oBAAAA,SAAA,KA16B6FxF,EAAE,CAAAyF,iBAAA,CA06BJ+P,YAAY,EAAc,CAAC;IAC1GpQ,IAAI,EAAEpE,QAAQ;IACd0E,IAAI,EAAE,CAAC;MACC0P,OAAO,EAAE,CAAC7D,KAAK,CAAC;MAChByE,OAAO,EAAE,CAACzE,KAAK;IACnB,CAAC;EACT,CAAC,CAAC;AAAA;AACV,MAAM0E,yBAAyB,CAAC;EAC5B,OAAOR,OAAOA,CAACjN,MAAM,GAAG,CAAC,CAAC,EAAE;IACxB,OAAO;MACHkN,QAAQ,EAAEF,YAAY;MACtBjF,SAAS,EAAE,CACP;QACIC,OAAO,EAAE5F,YAAY;QACrB6F,QAAQ,EAAE;UACN9B,OAAO,EAAEvF,8BAA8B;UACvCZ;QACJ;MACJ,CAAC;IAET,CAAC;EACL;EACA,OAAO3D,IAAI,YAAAqR,kCAAAnR,iBAAA;IAAA,YAAAA,iBAAA,IAAwFkR,yBAAyB;EAAA;EAC5H,OAAOL,IAAI,kBAj8B8E5V,EAAE,CAAA6V,gBAAA;IAAAzQ,IAAA,EAi8BS6Q;EAAyB;EAC7H,OAAOH,IAAI,kBAl8B8E9V,EAAE,CAAA+V,gBAAA;AAm8B/F;AACA;EAAA,QAAAvQ,SAAA,oBAAAA,SAAA,KAp8B6FxF,EAAE,CAAAyF,iBAAA,CAo8BJwQ,yBAAyB,EAAc,CAAC;IACvH7Q,IAAI,EAAEpE,QAAQ;IACd0E,IAAI,EAAE,CAAC,CAAC,CAAC;EACb,CAAC,CAAC;AAAA;AAEV,MAAMyQ,gBAAgB,CAAC;EACnB3E,aAAa;EACblB,YAAY;EACZ8F,MAAM;EACNzS,OAAO;EACPL,KAAK;EACLH,OAAO;EACPJ,eAAe;EACf0O,eAAe;EACf;EACAzN,KAAK,GAAGtD,MAAM,CAAC,CAAC,CAAC,CAAC;EAClB;EACAgR,YAAY,GAAG,EAAE;EACjB;EACA,IAAIE,YAAYA,CAAA,EAAG;IACf,IAAI,IAAI,CAAC1Q,KAAK,CAAC,CAAC,KAAK,UAAU,EAAE;MAC7B,OAAO,MAAM;IACjB;IACA,OAAO,IAAI;EACf;EACA;EACAA,KAAK,GAAGR,MAAM,CAAC,UAAU,CAAC;EAC1BoR,OAAO;EACPC,UAAU;EACVC,QAAQ;EACRC,GAAG;EACHC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJ1N,WAAWA,CAAC8M,aAAa,EAAElB,YAAY,EAAE8F,MAAM,EAAE;IAC7C,IAAI,CAAC5E,aAAa,GAAGA,aAAa;IAClC,IAAI,CAAClB,YAAY,GAAGA,YAAY;IAChC,IAAI,CAAC8F,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACzS,OAAO,GAAG2M,YAAY,CAAC3M,OAAO;IACnC,IAAI,CAACL,KAAK,GAAGgN,YAAY,CAAChN,KAAK;IAC/B,IAAI,CAACH,OAAO,GAAGmN,YAAY,CAAC9H,MAAM;IAClC,IAAI,CAACiJ,eAAe,GAAGnB,YAAY,CAAC9H,MAAM,CAAC0B,OAAO;IAClD,IAAI,CAACwH,YAAY,GAAG,GAAGpB,YAAY,CAAC7H,SAAS,IAAI6H,YAAY,CAAC9H,MAAM,CAAC8B,UAAU,EAAE;IACjF,IAAI,CAAC2H,GAAG,GAAG3B,YAAY,CAAC5H,QAAQ,CAACP,aAAa,CAAC,CAAC,CAACU,SAAS,CAAC,MAAM;MAC7D,IAAI,CAACwJ,aAAa,CAAC,CAAC;IACxB,CAAC,CAAC;IACF,IAAI,CAACH,IAAI,GAAG5B,YAAY,CAAC5H,QAAQ,CAAChB,YAAY,CAAC,CAAC,CAACmB,SAAS,CAAC,MAAM;MAC7D,IAAI,CAACtG,MAAM,CAAC,CAAC;IACjB,CAAC,CAAC;IACF,IAAI,CAAC4P,IAAI,GAAG7B,YAAY,CAAC5H,QAAQ,CAACd,YAAY,CAAC,CAAC,CAACiB,SAAS,CAAC,MAAM;MAC7D,IAAI,CAACR,YAAY,CAAC,CAAC;IACvB,CAAC,CAAC;IACF,IAAI,CAAC+J,IAAI,GAAG9B,YAAY,CAAC5H,QAAQ,CAACb,cAAc,CAAC,CAAC,CAACgB,SAAS,CAACyJ,KAAK,IAAI;MAClE,IAAI,CAACvP,eAAe,GAAGuP,KAAK;IAChC,CAAC,CAAC;EACN;EACArG,WAAWA,CAAA,EAAG;IACV,IAAI,CAACgG,GAAG,CAACO,WAAW,CAAC,CAAC;IACtB,IAAI,CAACN,IAAI,CAACM,WAAW,CAAC,CAAC;IACvB,IAAI,CAACL,IAAI,CAACK,WAAW,CAAC,CAAC;IACvB,IAAI,CAACJ,IAAI,CAACI,WAAW,CAAC,CAAC;IACvBC,aAAa,CAAC,IAAI,CAACV,UAAU,CAAC;IAC9BW,YAAY,CAAC,IAAI,CAACZ,OAAO,CAAC;EAC9B;EACA;AACJ;AACA;EACIO,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACnR,KAAK,CAACyM,GAAG,CAAC,QAAQ,CAAC;IACxB,IAAI,EAAE,IAAI,CAACxK,OAAO,CAAC8G,cAAc,KAAK,IAAI,IAAI,IAAI,CAAC9G,OAAO,CAAC8G,cAAc,KAAK,SAAS,CAAC,IAAI,IAAI,CAAC9G,OAAO,CAAC+G,OAAO,EAAE;MAC9G,IAAI,CAAC4H,OAAO,GAAGd,UAAU,CAAC,MAAM;QAC5B,IAAI,CAACzO,MAAM,CAAC,CAAC;MACjB,CAAC,EAAE,IAAI,CAACY,OAAO,CAAC+G,OAAO,CAAC;MACxB,IAAI,CAAC8H,QAAQ,GAAG,IAAIa,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC3P,OAAO,CAAC+G,OAAO;MAC3D,IAAI,IAAI,CAAC/G,OAAO,CAACkH,WAAW,EAAE;QAC1B,IAAI,CAAC0H,UAAU,GAAG0B,WAAW,CAAC,MAAM,IAAI,CAACT,cAAc,CAAC,CAAC,EAAE,EAAE,CAAC;MAClE;IACJ;IACA,IAAI,IAAI,CAAC7P,OAAO,CAACuH,cAAc,EAAE;MAC7B,IAAI,CAAC0L,MAAM,CAACC,IAAI,CAAC,CAAC;IACtB;EACJ;EACA;AACJ;AACA;EACIrD,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAAChP,KAAK,CAAC,CAAC,KAAK,CAAC,IAAI,IAAI,CAACA,KAAK,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAACb,OAAO,CAAC+G,OAAO,EAAE;MACrE;IACJ;IACA,MAAM+I,GAAG,GAAG,IAAIJ,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;IAChC,MAAMI,SAAS,GAAG,IAAI,CAAClB,QAAQ,GAAGiB,GAAG;IACrC,IAAI,CAACjP,KAAK,CAAC2J,GAAG,CAAEuF,SAAS,GAAG,IAAI,CAAC/P,OAAO,CAAC+G,OAAO,GAAI,GAAG,CAAC;IACxD,IAAI,IAAI,CAAC/G,OAAO,CAACwH,iBAAiB,KAAK,YAAY,EAAE;MACjD,IAAI,CAAC3G,KAAK,CAAC2O,MAAM,CAAC3O,KAAK,IAAI,GAAG,GAAGA,KAAK,CAAC;IAC3C;IACA,IAAI,IAAI,CAACA,KAAK,CAAC,CAAC,IAAI,CAAC,EAAE;MACnB,IAAI,CAACA,KAAK,CAAC2J,GAAG,CAAC,CAAC,CAAC;IACrB;IACA,IAAI,IAAI,CAAC3J,KAAK,CAAC,CAAC,IAAI,GAAG,EAAE;MACrB,IAAI,CAACA,KAAK,CAAC2J,GAAG,CAAC,GAAG,CAAC;IACvB;EACJ;EACAtF,YAAYA,CAAA,EAAG;IACXqK,YAAY,CAAC,IAAI,CAACZ,OAAO,CAAC;IAC1BW,aAAa,CAAC,IAAI,CAACV,UAAU,CAAC;IAC9B,IAAI,CAAC7Q,KAAK,CAACyM,GAAG,CAAC,QAAQ,CAAC;IACxB,IAAI,CAACxK,OAAO,CAAC+G,OAAO,GAAG,IAAI,CAACuH,eAAe;IAC3C,IAAI,CAACK,OAAO,GAAGd,UAAU,CAAC,MAAM,IAAI,CAACzO,MAAM,CAAC,CAAC,EAAE,IAAI,CAACkP,eAAe,CAAC;IACpE,IAAI,CAACO,QAAQ,GAAG,IAAIa,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,IAAI,IAAI,CAACrB,eAAe,IAAI,CAAC,CAAC;IAClE,IAAI,CAACzN,KAAK,CAAC2J,GAAG,CAAC,CAAC,CAAC,CAAC;IAClB,IAAI,IAAI,CAACxK,OAAO,CAACkH,WAAW,EAAE;MAC1B,IAAI,CAAC0H,UAAU,GAAG0B,WAAW,CAAC,MAAM,IAAI,CAACT,cAAc,CAAC,CAAC,EAAE,EAAE,CAAC;IAClE;EACJ;EACA;AACJ;AACA;EACIzQ,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAACrB,KAAK,CAAC,CAAC,KAAK,SAAS,EAAE;MAC5B;IACJ;IACAwR,YAAY,CAAC,IAAI,CAACZ,OAAO,CAAC;IAC1B,IAAI,CAAC5Q,KAAK,CAACyM,GAAG,CAAC,SAAS,CAAC;IACzB,IAAI,CAACmE,OAAO,GAAGd,UAAU,CAAC,MAAM,IAAI,CAACQ,aAAa,CAACjP,MAAM,CAAC,IAAI,CAAC+N,YAAY,CAAC/H,OAAO,CAAC,CAAC;EACzF;EACA4K,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACjS,KAAK,CAAC,CAAC,KAAK,SAAS,EAAE;MAC5B;IACJ;IACA,IAAI,CAACoP,YAAY,CAACxH,UAAU,CAAC,CAAC;IAC9B,IAAI,IAAI,CAAC3F,OAAO,CAAC4F,YAAY,EAAE;MAC3B,IAAI,CAACxG,MAAM,CAAC,CAAC;IACjB;EACJ;EACA6Q,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAAClS,KAAK,CAAC,CAAC,KAAK,SAAS,EAAE;MAC5B;IACJ;IACAwR,YAAY,CAAC,IAAI,CAACZ,OAAO,CAAC;IAC1B,IAAI,CAAC3O,OAAO,CAAC+G,OAAO,GAAG,CAAC;IACxB,IAAI,CAAC8H,QAAQ,GAAG,CAAC;IACjB;IACAS,aAAa,CAAC,IAAI,CAACV,UAAU,CAAC;IAC9B,IAAI,CAAC/N,KAAK,CAAC2J,GAAG,CAAC,CAAC,CAAC;EACrB;EACA0F,gBAAgBA,CAAA,EAAG;IACf,IAAK,IAAI,CAAClQ,OAAO,CAAC8G,cAAc,KAAK,IAAI,IAAI,IAAI,CAAC9G,OAAO,CAAC8G,cAAc,KAAK,iBAAiB,IAC1F,IAAI,CAAC9G,OAAO,CAACgH,eAAe,KAAK,CAAC,IAClC,IAAI,CAACjJ,KAAK,CAAC,CAAC,KAAK,SAAS,EAAE;MAC5B;IACJ;IACA,IAAI,CAAC4Q,OAAO,GAAGd,UAAU,CAAC,MAAM,IAAI,CAACzO,MAAM,CAAC,CAAC,EAAE,IAAI,CAACY,OAAO,CAACgH,eAAe,CAAC;IAC5E,IAAI,CAAChH,OAAO,CAAC+G,OAAO,GAAG,IAAI,CAAC/G,OAAO,CAACgH,eAAe;IACnD,IAAI,CAAC6H,QAAQ,GAAG,IAAIa,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,IAAI,IAAI,CAAC3P,OAAO,CAAC+G,OAAO,IAAI,CAAC,CAAC;IAClE,IAAI,CAAClG,KAAK,CAAC2J,GAAG,CAAC,CAAC,CAAC,CAAC;IAClB,IAAI,IAAI,CAACxK,OAAO,CAACkH,WAAW,EAAE;MAC1B,IAAI,CAAC0H,UAAU,GAAG0B,WAAW,CAAC,MAAM,IAAI,CAACT,cAAc,CAAC,CAAC,EAAE,EAAE,CAAC;IAClE;EACJ;EACA,OAAOnO,IAAI,YAAAyR,yBAAAvR,iBAAA;IAAA,YAAAA,iBAAA,IAAwFoR,gBAAgB,EAnmC1BnW,EAAE,CAAAgF,iBAAA,CAmmC0CiJ,aAAa,GAnmCzDjO,EAAE,CAAAgF,iBAAA,CAmmCoEsD,YAAY,GAnmClFtI,EAAE,CAAAgF,iBAAA,CAmmC6FhF,EAAE,CAACM,cAAc;EAAA;EACzM,OAAOqT,IAAI,kBApmC8E3T,EAAE,CAAA4T,iBAAA;IAAAxO,IAAA,EAomCJ+Q,gBAAgB;IAAA9Q,SAAA;IAAAwO,QAAA;IAAAC,YAAA,WAAAyC,8BAAA3U,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QApmCd5B,EAAE,CAAAiC,UAAA,mBAAAuU,0CAAA;UAAA,OAomCJ3U,GAAA,CAAAsR,QAAA,CAAS,CAAC;QAAA,CAAK,CAAC,wBAAAsD,+CAAA;UAAA,OAAhB5U,GAAA,CAAAuR,WAAA,CAAY,CAAC;QAAA,CAAE,CAAC,wBAAAsD,+CAAA;UAAA,OAAhB7U,GAAA,CAAAwR,gBAAA,CAAiB,CAAC;QAAA,CAAH,CAAC;MAAA;MAAA,IAAAzR,EAAA;QApmCd5B,EAAE,CAAAkD,UAAA,CAAArB,GAAA,CAAA6P,YAomCW,CAAC;QApmCd1R,EAAE,CAAA+D,WAAA,YAAAlC,GAAA,CAAA+P,YAomCW,CAAC;MAAA;IAAA;IAAArM,UAAA;IAAA6O,QAAA,GApmCdpU,EAAE,CAAAqU,mBAAA;IAAAC,KAAA,EAAA5S,GAAA;IAAA6S,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAiC,0BAAA/U,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAF5B,EAAE,CAAAiD,UAAA,IAAAgB,kCAAA,mBAqmCqB,CAAC,IAAAG,+BAAA,gBAG1C,CAAC,IAAAC,+BAAA,gBAIpB,CAAC,IAAAC,+BAAA,gBAGK,CAAC,IAAAC,+BAAA,gBAG7B,CAAC;MAAA;MAAA,IAAA3C,EAAA;QAlnC0D5B,EAAE,CAAAuD,UAAA,SAAA1B,GAAA,CAAAsB,OAAA,CAAA6G,WAqmC5D,CAAC;QArmCyDhK,EAAE,CAAA6C,SAAA,CAwmC7E,CAAC;QAxmC0E7C,EAAE,CAAAuD,UAAA,SAAA1B,GAAA,CAAAyB,KAwmC7E,CAAC;QAxmC0EtD,EAAE,CAAA6C,SAAA,CA2mCrD,CAAC;QA3mCkD7C,EAAE,CAAAuD,UAAA,SAAA1B,GAAA,CAAA8B,OAAA,IAAA9B,GAAA,CAAAsB,OAAA,CAAAiH,UA2mCrD,CAAC;QA3mCkDpK,EAAE,CAAA6C,SAAA,CA8mCpD,CAAC;QA9mCiD7C,EAAE,CAAAuD,UAAA,SAAA1B,GAAA,CAAA8B,OAAA,KAAA9B,GAAA,CAAAsB,OAAA,CAAAiH,UA8mCpD,CAAC;QA9mCiDpK,EAAE,CAAA6C,SAAA,CAknC/D,CAAC;QAlnC4D7C,EAAE,CAAAuD,UAAA,SAAA1B,GAAA,CAAAsB,OAAA,CAAAkH,WAknC/D,CAAC;MAAA;IAAA;IAAAuK,YAAA,GAG8BrT,IAAI;IAAAsT,aAAA;IAAAI,eAAA;EAAA;AACnE;AACA;EAAA,QAAAzP,SAAA,oBAAAA,SAAA,KAvnC6FxF,EAAE,CAAAyF,iBAAA,CAunCJ0Q,gBAAgB,EAAc,CAAC;IAC9G/Q,IAAI,EAAEzE,SAAS;IACf+E,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,mBAAmB;MAC7B+O,QAAQ,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MACiBnP,UAAU,EAAE,IAAI;MAChB6P,OAAO,EAAE,CAAC7T,IAAI,CAAC;MACf0T,eAAe,EAAErU,uBAAuB,CAACyU;IAC7C,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEjQ,IAAI,EAAE6I;EAAc,CAAC,EAAE;IAAE7I,IAAI,EAAEkD;EAAa,CAAC,EAAE;IAAElD,IAAI,EAAEpF,EAAE,CAACM;EAAe,CAAC,CAAC,EAAkB;IAAEoR,YAAY,EAAE,CAAC;MACnItM,IAAI,EAAEvE,WAAW;MACjB6E,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAEkM,YAAY,EAAE,CAAC;MACfxM,IAAI,EAAEvE,WAAW;MACjB6E,IAAI,EAAE,CAAC,eAAe;IAC1B,CAAC,CAAC;IAAEyN,QAAQ,EAAE,CAAC;MACX/N,IAAI,EAAEtE,YAAY;MAClB4E,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAE0N,WAAW,EAAE,CAAC;MACdhO,IAAI,EAAEtE,YAAY;MAClB4E,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAE2N,gBAAgB,EAAE,CAAC;MACnBjO,IAAI,EAAEtE,YAAY;MAClB4E,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMkR,+BAA+B,GAAG;EACpC,GAAGxN,8BAA8B;EACjC0G,cAAc,EAAEqG;AACpB,CAAC;AACD,MAAMU,sBAAsB,CAAC;EACzB,OAAOpB,OAAOA,CAACjN,MAAM,GAAG,CAAC,CAAC,EAAE;IACxB,OAAO;MACHkN,QAAQ,EAAEmB,sBAAsB;MAChCtG,SAAS,EAAE,CACP;QACIC,OAAO,EAAE5F,YAAY;QACrB6F,QAAQ,EAAE;UACN9B,OAAO,EAAEiI,+BAA+B;UACxCpO;QACJ;MACJ,CAAC;IAET,CAAC;EACL;EACA,OAAO3D,IAAI,YAAAiS,+BAAA/R,iBAAA;IAAA,YAAAA,iBAAA,IAAwF8R,sBAAsB;EAAA;EACzH,OAAOjB,IAAI,kBArrC8E5V,EAAE,CAAA6V,gBAAA;IAAAzQ,IAAA,EAqrCSyR;EAAsB;EAC1H,OAAOf,IAAI,kBAtrC8E9V,EAAE,CAAA+V,gBAAA;AAurC/F;AACA;EAAA,QAAAvQ,SAAA,oBAAAA,SAAA,KAxrC6FxF,EAAE,CAAAyF,iBAAA,CAwrCJoR,sBAAsB,EAAc,CAAC;IACpHzR,IAAI,EAAEpE,QAAQ;IACd0E,IAAI,EAAE,CAAC;MACC0P,OAAO,EAAE,CAACe,gBAAgB,CAAC;MAC3BH,OAAO,EAAE,CAACG,gBAAgB;IAC9B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAAS3P,cAAc,EAAEZ,eAAe,EAAE0P,mBAAmB,EAAEsB,+BAA+B,EAAExN,8BAA8B,EAAE+D,OAAO,EAAErB,gBAAgB,EAAEmB,UAAU,EAAErC,YAAY,EAAE2G,KAAK,EAAE/M,uBAAuB,EAAE2R,gBAAgB,EAAEU,sBAAsB,EAAEvO,YAAY,EAAEvB,QAAQ,EAAEkP,yBAAyB,EAAET,YAAY,EAAEvH,aAAa,EAAEsH,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}