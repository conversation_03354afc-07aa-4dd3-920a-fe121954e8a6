{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FlexModule } from '@angular/flex-layout';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatRadioModule } from '@angular/material/radio';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiControlMessagesModule, SwuiPagePanelModule, SwuiSelectModule } from '@skywind-group/lib-swui';\nimport { BoConfirmationModule } from '../../../common/components/bo-confirmation/bo-confirmation.module';\nimport { ControlMessagesModule } from '../../../common/components/control-messages/control-messages.module';\nimport { TrimInputValueModule } from '../../../common/directives/trim-input-value/trim-input-value.module';\nimport { GameGroupService } from '../../../common/services/game-group.service';\nimport { BriefResolver } from '../../../common/services/resolvers/brief.resolver';\nimport { AddLevelModalComponent } from './forms/add-level-modal.component';\nimport { CloneLiveLimitsComponent } from './forms/clone-live-limits.component';\nimport { EditLiveLimitsComponent } from './forms/edit-live-limits.component';\nimport { RenameLevelModalComponent } from './forms/rename-level-modal.component';\nimport { LiveLimitsComponent } from './live-limits.component';\nimport * as i0 from \"@angular/core\";\nexport class LiveLimitsModule {\n  static {\n    this.ɵfac = function LiveLimitsModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LiveLimitsModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: LiveLimitsModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [BriefResolver, GameGroupService],\n      imports: [TranslateModule, CommonModule, ReactiveFormsModule, ControlMessagesModule, BoConfirmationModule, MatDialogModule, SwuiPagePanelModule, FlexModule, MatButtonModule, MatFormFieldModule, SwuiSelectModule, MatInputModule, SwuiControlMessagesModule, MatIconModule, MatTooltipModule, MatRadioModule, MatCheckboxModule, FormsModule, TrimInputValueModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(LiveLimitsModule, {\n    declarations: [LiveLimitsComponent, EditLiveLimitsComponent, CloneLiveLimitsComponent, AddLevelModalComponent, RenameLevelModalComponent],\n    imports: [TranslateModule, CommonModule, ReactiveFormsModule, ControlMessagesModule, BoConfirmationModule, MatDialogModule, SwuiPagePanelModule, FlexModule, MatButtonModule, MatFormFieldModule, SwuiSelectModule, MatInputModule, SwuiControlMessagesModule, MatIconModule, MatTooltipModule, MatRadioModule, MatCheckboxModule, FormsModule, TrimInputValueModule],\n    exports: [LiveLimitsComponent]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FlexModule", "FormsModule", "ReactiveFormsModule", "MatButtonModule", "MatCheckboxModule", "MatDialogModule", "MatFormFieldModule", "MatIconModule", "MatInputModule", "MatRadioModule", "MatTooltipModule", "TranslateModule", "SwuiControlMessagesModule", "SwuiPagePanelModule", "SwuiSelectModule", "BoConfirmationModule", "ControlMessagesModule", "TrimInputValueModule", "GameGroupService", "BriefResolver", "AddLevelModalComponent", "CloneLiveLimitsComponent", "EditLiveLimitsComponent", "RenameLevelModalComponent", "LiveLimitsComponent", "LiveLimitsModule", "imports", "declarations", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/limits/live-limits/live-limits.module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { FlexModule } from '@angular/flex-layout';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatRadioModule } from '@angular/material/radio';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiControlMessagesModule, SwuiPagePanelModule, SwuiSelectModule } from '@skywind-group/lib-swui';\nimport { BoConfirmationModule } from '../../../common/components/bo-confirmation/bo-confirmation.module';\nimport { ControlMessagesModule } from '../../../common/components/control-messages/control-messages.module';\nimport { TrimInputValueModule } from '../../../common/directives/trim-input-value/trim-input-value.module';\nimport { GameGroupService } from '../../../common/services/game-group.service';\nimport { BriefResolver } from '../../../common/services/resolvers/brief.resolver';\nimport { AddLevelModalComponent } from './forms/add-level-modal.component';\nimport { CloneLiveLimitsComponent } from './forms/clone-live-limits.component';\nimport { EditLiveLimitsComponent } from './forms/edit-live-limits.component';\nimport { RenameLevelModalComponent } from './forms/rename-level-modal.component';\nimport { LiveLimitsComponent } from './live-limits.component';\n\n@NgModule({\n    imports: [\n        TranslateModule,\n        CommonModule,\n        ReactiveFormsModule,\n        ControlMessagesModule,\n        BoConfirmationModule,\n        MatDialogModule,\n        SwuiPagePanelModule,\n        FlexModule,\n        MatButtonModule,\n        MatFormFieldModule,\n        SwuiSelectModule,\n        MatInputModule,\n        SwuiControlMessagesModule,\n        MatIconModule,\n        MatTooltipModule,\n        MatRadioModule,\n        MatCheckboxModule,\n        FormsModule,\n        TrimInputValueModule,\n    ],\n  declarations: [\n    LiveLimitsComponent,\n    EditLiveLimitsComponent,\n    CloneLiveLimitsComponent,\n    AddLevelModalComponent,\n    RenameLevelModalComponent,\n  ],\n  exports: [\n    LiveLimitsComponent,\n  ],\n  providers: [\n    BriefResolver,\n    GameGroupService,\n  ],\n})\nexport class LiveLimitsModule {\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,UAAU,QAAQ,sBAAsB;AACjD,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,yBAAyB,EAAEC,mBAAmB,EAAEC,gBAAgB,QAAQ,yBAAyB;AAC1G,SAASC,oBAAoB,QAAQ,mEAAmE;AACxG,SAASC,qBAAqB,QAAQ,qEAAqE;AAC3G,SAASC,oBAAoB,QAAQ,qEAAqE;AAC1G,SAASC,gBAAgB,QAAQ,6CAA6C;AAC9E,SAASC,aAAa,QAAQ,mDAAmD;AACjF,SAASC,sBAAsB,QAAQ,mCAAmC;AAC1E,SAASC,wBAAwB,QAAQ,qCAAqC;AAC9E,SAASC,uBAAuB,QAAQ,oCAAoC;AAC5E,SAASC,yBAAyB,QAAQ,sCAAsC;AAChF,SAASC,mBAAmB,QAAQ,yBAAyB;;AAuC7D,OAAM,MAAOC,gBAAgB;;;uCAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;iBALhB,CACTN,aAAa,EACbD,gBAAgB,CACjB;MAAAQ,OAAA,GAjCKf,eAAe,EACfZ,YAAY,EACZG,mBAAmB,EACnBc,qBAAqB,EACrBD,oBAAoB,EACpBV,eAAe,EACfQ,mBAAmB,EACnBb,UAAU,EACVG,eAAe,EACfG,kBAAkB,EAClBQ,gBAAgB,EAChBN,cAAc,EACdI,yBAAyB,EACzBL,aAAa,EACbG,gBAAgB,EAChBD,cAAc,EACdL,iBAAiB,EACjBH,WAAW,EACXgB,oBAAoB;IAAA;EAAA;;;2EAiBfQ,gBAAgB;IAAAE,YAAA,GAdzBH,mBAAmB,EACnBF,uBAAuB,EACvBD,wBAAwB,EACxBD,sBAAsB,EACtBG,yBAAyB;IAAAG,OAAA,GAzBrBf,eAAe,EACfZ,YAAY,EACZG,mBAAmB,EACnBc,qBAAqB,EACrBD,oBAAoB,EACpBV,eAAe,EACfQ,mBAAmB,EACnBb,UAAU,EACVG,eAAe,EACfG,kBAAkB,EAClBQ,gBAAgB,EAChBN,cAAc,EACdI,yBAAyB,EACzBL,aAAa,EACbG,gBAAgB,EAChBD,cAAc,EACdL,iBAAiB,EACjBH,WAAW,EACXgB,oBAAoB;IAAAW,OAAA,GAUxBJ,mBAAmB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}