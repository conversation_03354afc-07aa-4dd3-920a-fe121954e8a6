{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { FormControl, FormGroup, Validators } from '@angular/forms';\nimport { buildOptions, upgradeLobbyThemeOptions } from '../lobby.model';\nimport { THEMES } from '../theme.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../common/services/lobby.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"./lobby-settings/lobby-settings.component\";\nimport * as i6 from \"./template-settings/template-settings.component\";\nimport * as i7 from \"./templates-list/templates-list.component\";\nimport * as i8 from \"./lobby-menu-items/lobby-menu-items.component\";\nimport * as i9 from \"@angular/material/card\";\nimport * as i10 from \"@skywind-group/lib-swui\";\nimport * as i11 from \"@angular/material/tabs\";\nimport * as i12 from \"@angular/flex-layout/flex\";\nimport * as i13 from \"@angular/material/icon\";\nimport * as i14 from \"@angular/material/button\";\nimport * as i15 from \"@angular/material/input\";\nimport * as i16 from \"@angular/material/form-field\";\nimport * as i17 from \"@angular/cdk/text-field\";\nimport * as i18 from \"../../../common/directives/trim-input-value/trim-input-value.component\";\nimport * as i19 from \"@ngx-translate/core\";\nconst _c0 = [\"tabsTemplates\"];\nconst _c1 = [[[\"\", 8, \"submit-button\"]]];\nconst _c2 = [\".submit-button\"];\nconst _c3 = () => [\"/pages/lobby/layouts\"];\nfunction LobbyFormComponent_ng_template_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n    i0.ɵɵpipe(1, \"translate\");\n  }\n  if (rf & 2) {\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(1, 1, \"LOBBY.FORM.template\"), \" \");\n  }\n}\nfunction LobbyFormComponent_ng_template_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 21);\n    i0.ɵɵtext(1, \"settings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"LOBBY.FORM.templateSetup\"), \" \");\n  }\n}\nfunction LobbyFormComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 23);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 24);\n    i0.ɵɵelement(4, \"img\", 25);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.theme.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.theme.thumbnailUrl, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.theme.name);\n  }\n}\nfunction LobbyFormComponent_ng_template_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 21);\n    i0.ɵɵtext(1, \"important_devices\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"LOBBY.FORM.templateList\"), \" \");\n  }\n}\nexport class LobbyFormComponent {\n  set setLobby(value) {\n    if (value?.theme) {\n      value.theme.options = upgradeLobbyThemeOptions(value.theme.options ?? []);\n    }\n    if ('theme' in value && value.theme !== null) {\n      this.setTheme(value.theme.key);\n    } else {\n      this.setTheme('bioshock');\n    }\n    this.form.patchValue({\n      title: value?.title ?? '',\n      description: value?.description ? `${value?.description}${value.description.slice(-1) === '\\n' ? '' : '\\n'}` : '',\n      menuItems: value?.info?.menuItems ?? []\n    });\n    this.lobby = {\n      title: value?.title,\n      description: value?.description,\n      info: value?.info,\n      theme: value?.theme\n    };\n  }\n  constructor({\n    snapshot: {\n      queryParams: {\n        path\n      },\n      data: {\n        widgets\n      }\n    }\n  }, service) {\n    this.service = service;\n    this.themes = THEMES;\n    this.activeTabIndex = 0;\n    this.onSubmit = new EventEmitter();\n    this.options = {};\n    this.path = path;\n    this.widgets = widgets;\n    this.form = new FormGroup({\n      title: new FormControl('', Validators.required),\n      description: new FormControl(''),\n      settings: new FormGroup({}),\n      theme: new FormGroup({\n        options: new FormGroup({})\n      }),\n      menuItems: new FormControl([])\n    });\n  }\n  ngAfterViewChecked() {\n    if (this.tabsRef) {\n      this.tabsRef.realignInkBar();\n    }\n  }\n  get titleControl() {\n    return this.form.get('title');\n  }\n  get lobbyOptions() {\n    return this.lobby && this.lobby.theme && this.lobby.theme.options || [];\n  }\n  get themeOptions() {\n    return this.options[this.theme.key] || this.lobbyOptions;\n  }\n  onSelectedIndexChange(index) {\n    this.activeTabIndex = index;\n  }\n  submit() {\n    this.service.formSubmitted = true;\n    if (this.form.invalid) {\n      if (['title', 'description', 'settings'].map(name => this.form.get(name).invalid).some(invalid => invalid)) {\n        this.activeTabIndex = 0;\n      } else if (this.form.get('theme').invalid) {\n        this.activeTabIndex = 1;\n      } else {\n        this.activeTabIndex = 2;\n      }\n    }\n    this.submitted = true;\n    this.form.markAllAsTouched();\n    if (this.form.valid) {\n      const {\n        title,\n        description,\n        settings = {},\n        menuItems = [],\n        theme: {\n          options: params = {}\n        } = {}\n      } = this.form.value;\n      const options = Object.entries({\n        ...params,\n        ...settings\n      }).reduce((result, [key, value]) => [...result, {\n        key,\n        value\n      }], []);\n      const {\n        info: infoOptions,\n        theme: themeOptions\n      } = buildOptions(this.theme.key, options);\n      this.onSubmit.emit({\n        title,\n        description: description || null,\n        theme: {\n          key: this.theme.key,\n          options: themeOptions\n        },\n        info: {\n          ...(this.lobby && this.lobby.info ? this.lobby.info : {}),\n          theme: {\n            key: this.theme.key\n          },\n          options: infoOptions,\n          menuItems,\n          widgets: this.widgets.filter(({\n            tag\n          }) => tag === 'x-widget-scoreboard').reduce((result, widget) => ({\n            ...result,\n            [widget.tag]: widget\n          }), {})\n        }\n      });\n    }\n  }\n  handleActivateTemplate(theme) {\n    const {\n      theme: {\n        options = {}\n      } = {}\n    } = this.form.value;\n    this.options[this.theme.key] = Object.entries(options).reduce((result, [key, value]) => [...result, {\n      key,\n      value\n    }], []);\n    this.setTheme(theme.key);\n  }\n  setTheme(themeKey) {\n    const key = themeKey in this.themes ? themeKey : 'bioshock';\n    this.theme = {\n      key,\n      ...this.themes[key]\n    };\n  }\n  static {\n    this.ɵfac = function LobbyFormComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LobbyFormComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.LobbyService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LobbyFormComponent,\n      selectors: [[\"lobby-form\"]],\n      viewQuery: function LobbyFormComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.tabsRef = _t.first);\n        }\n      },\n      inputs: {\n        setLobby: [0, \"lobby\", \"setLobby\"]\n      },\n      outputs: {\n        onSubmit: \"onSubmit\"\n      },\n      ngContentSelectors: _c2,\n      decls: 45,\n      vars: 38,\n      consts: [[\"tabsTemplates\", \"\"], [1, \"p-32\", 3, \"formGroup\"], [\"animationDuration\", \"0ms\", 3, \"selectedIndexChange\", \"selectedIndex\"], [3, \"label\"], [1, \"mat-elevation-z0\"], [\"fxLayout\", \"column\", 2, \"max-width\", \"1000px\"], [\"appearance\", \"outline\"], [\"matInput\", \"\", \"trimValue\", \"\", \"type\", \"text\", \"formControlName\", \"title\"], [3, \"control\", \"force\"], [\"matInput\", \"\", \"rows\", \"5\", \"cols\", \"5\", \"formControlName\", \"description\", 3, \"cdkTextareaAutosize\"], [\"name\", \"settings\", 3, \"form\", \"values\", \"submitted\"], [\"matTabLabel\", \"\"], [\"animationDuration\", \"0ms\", 1, \"nested\"], [1, \"tpl-settings\"], [\"class\", \"tpl-settings__header\", 4, \"ngIf\"], [1, \"tpl-settings__body\"], [\"name\", \"options\", 3, \"themeTitle\", \"options\", \"values\", \"submitted\", \"template-settings\"], [3, \"onActivate\", \"templates-list\", \"active\"], [\"formControlName\", \"menuItems\", 3, \"themeKey\", \"widgets\", \"submitted\"], [\"fxLayout\", \"row\", \"fxLayoutAlign\", \"end center\", 2, \"padding\", \"32px 0\"], [\"mat-stroked-button\", \"\", \"color\", \"primary\", 2, \"margin-right\", \"8px\", 3, \"routerLink\"], [\"fontSet\", \"material-icons-outline\", 1, \"tab-icon\"], [1, \"tpl-settings__header\"], [1, \"tpl-settings__title\"], [1, \"tpl-settings__image\"], [3, \"src\", \"alt\"]],\n      template: function LobbyFormComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵprojectionDef(_c1);\n          i0.ɵɵelementStart(0, \"form\", 1)(1, \"mat-tab-group\", 2);\n          i0.ɵɵlistener(\"selectedIndexChange\", function LobbyFormComponent_Template_mat_tab_group_selectedIndexChange_1_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSelectedIndexChange($event));\n          });\n          i0.ɵɵelementStart(2, \"mat-tab\", 3);\n          i0.ɵɵpipe(3, \"translate\");\n          i0.ɵɵelementStart(4, \"mat-card\", 4)(5, \"div\", 5)(6, \"mat-form-field\", 6)(7, \"mat-label\");\n          i0.ɵɵtext(8);\n          i0.ɵɵpipe(9, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(10, \"input\", 7);\n          i0.ɵɵelementStart(11, \"mat-error\");\n          i0.ɵɵelement(12, \"lib-swui-control-messages\", 8);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"mat-form-field\", 6)(14, \"mat-label\");\n          i0.ɵɵtext(15);\n          i0.ɵɵpipe(16, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(17, \"textarea\", 9);\n          i0.ɵɵelementStart(18, \"mat-error\");\n          i0.ɵɵelement(19, \"lib-swui-control-messages\", 8);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(20, \"lobby-settings\", 10);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(21, \"mat-tab\");\n          i0.ɵɵtemplate(22, LobbyFormComponent_ng_template_22_Template, 2, 3, \"ng-template\", 11);\n          i0.ɵɵelementStart(23, \"mat-card\", 4)(24, \"mat-tab-group\", 12, 0)(26, \"mat-tab\");\n          i0.ɵɵtemplate(27, LobbyFormComponent_ng_template_27_Template, 4, 3, \"ng-template\", 11);\n          i0.ɵɵelementStart(28, \"div\", 13);\n          i0.ɵɵtemplate(29, LobbyFormComponent_div_29_Template, 5, 3, \"div\", 14);\n          i0.ɵɵelementStart(30, \"div\", 15);\n          i0.ɵɵelement(31, \"div\", 16);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(32, \"mat-tab\");\n          i0.ɵɵtemplate(33, LobbyFormComponent_ng_template_33_Template, 4, 3, \"ng-template\", 11);\n          i0.ɵɵelementStart(34, \"div\", 17);\n          i0.ɵɵlistener(\"onActivate\", function LobbyFormComponent_Template_div_onActivate_34_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.handleActivateTemplate($event));\n          });\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(35, \"mat-tab\", 3);\n          i0.ɵɵpipe(36, \"translate\");\n          i0.ɵɵelement(37, \"lobby-menu-items\", 18);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(38, \"div\", 19)(39, \"button\", 20)(40, \"mat-icon\");\n          i0.ɵɵtext(41, \"keyboard_arrow_left\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(42);\n          i0.ɵɵpipe(43, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵprojection(44);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"formGroup\", ctx.form);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"selectedIndex\", ctx.activeTabIndex);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"label\", i0.ɵɵpipeBind1(3, 27, \"LOBBY.FORM.settings\"));\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(9, 29, \"LOBBY.FORM.title\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"control\", ctx.form.get(\"title\"))(\"force\", ctx.submitted);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(16, 31, \"LOBBY.FORM.description\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"cdkTextareaAutosize\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"control\", ctx.form.get(\"description\"))(\"force\", ctx.submitted);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"form\", ctx.form)(\"values\", ctx.lobbyOptions)(\"submitted\", ctx.submitted);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ctx.theme);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"themeTitle\", ctx.titleControl.value)(\"options\", ctx.theme == null ? null : ctx.theme.options)(\"values\", ctx.themeOptions)(\"submitted\", ctx.submitted)(\"template-settings\", ctx.form.get(\"theme\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"templates-list\", ctx.themes)(\"active\", ctx.theme);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"label\", i0.ɵɵpipeBind1(36, 33, \"LOBBY.MENU_ITEMS.menuItems\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"themeKey\", ctx.theme.key)(\"widgets\", ctx.widgets)(\"submitted\", ctx.submitted);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(37, _c3));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(43, 35, \"LOBBY.FORM.back\"), \" \");\n        }\n      },\n      dependencies: [i3.NgIf, i4.ɵNgNoValidate, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.FormGroupDirective, i4.FormControlName, i5.LobbySettingsComponent, i6.TemplateSettingsComponent, i7.TemplatesListComponent, i1.RouterLink, i8.LobbyMenuItemsComponent, i9.MatCard, i10.SwuiControlMessagesComponent, i11.MatTabLabel, i11.MatTab, i11.MatTabGroup, i12.DefaultLayoutDirective, i12.DefaultLayoutAlignDirective, i13.MatIcon, i14.MatButton, i15.MatInput, i16.MatFormField, i16.MatLabel, i16.MatError, i17.CdkTextareaAutosize, i18.TrimInputValueComponent, i19.TranslatePipe],\n      styles: [\"textarea[_ngcontent-%COMP%] {\\n  max-width: 100%;\\n  min-width: 100%;\\n}\\n\\n.tab-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n\\n.tpl-settings__header[_ngcontent-%COMP%] {\\n  max-width: 1000px;\\n  margin-bottom: 32px;\\n}\\n.tpl-settings__image[_ngcontent-%COMP%] {\\n  border-radius: 5px;\\n  max-width: 400px;\\n  overflow: hidden;\\n}\\n.tpl-settings__image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  display: block;\\n  width: 100%;\\n  height: auto;\\n}\\n.tpl-settings__body[_ngcontent-%COMP%] {\\n  max-width: 1000px;\\n}\\n.tpl-settings__title[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  padding: 16px 16px 16px 0;\\n  text-transform: uppercase;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvbG9iYnkvbG9iYnktZm9ybS9sb2JieS1mb3JtLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsZUFBQTtFQUNBLGVBQUE7QUFDRjs7QUFFQTtFQUNFLGlCQUFBO0FBQ0Y7O0FBR0U7RUFDRSxpQkFBQTtFQUNBLG1CQUFBO0FBQUo7QUFFRTtFQUNFLGtCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxnQkFBQTtBQUFKO0FBQ0k7RUFDRSxjQUFBO0VBQ0EsV0FBQTtFQUNBLFlBQUE7QUFDTjtBQUVFO0VBQ0UsaUJBQUE7QUFBSjtBQUVFO0VBQ0UsZ0JBQUE7RUFDQSx5QkFBQTtFQUNBLHlCQUFBO0FBQUoiLCJzb3VyY2VzQ29udGVudCI6WyJ0ZXh0YXJlYSB7XG4gIG1heC13aWR0aDogMTAwJTtcbiAgbWluLXdpZHRoOiAxMDAlO1xufVxuXG4udGFiLWljb24ge1xuICBtYXJnaW4tcmlnaHQ6IDhweDtcbn1cblxuLnRwbC1zZXR0aW5ncyB7XG4gICZfX2hlYWRlciB7XG4gICAgbWF4LXdpZHRoOiAxMDAwcHg7XG4gICAgbWFyZ2luLWJvdHRvbTogMzJweDtcbiAgfVxuICAmX19pbWFnZSB7XG4gICAgYm9yZGVyLXJhZGl1czogNXB4O1xuICAgIG1heC13aWR0aDogNDAwcHg7XG4gICAgb3ZlcmZsb3c6IGhpZGRlbjtcbiAgICBpbWcge1xuICAgICAgZGlzcGxheTogYmxvY2s7XG4gICAgICB3aWR0aDogMTAwJTtcbiAgICAgIGhlaWdodDogYXV0bztcbiAgICB9XG4gIH1cbiAgJl9fYm9keSB7XG4gICAgbWF4LXdpZHRoOiAxMDAwcHg7XG4gIH1cbiAgJl9fdGl0bGUge1xuICAgIGZvbnQtd2VpZ2h0OiA1MDA7XG4gICAgcGFkZGluZzogMTZweCAxNnB4IDE2cHggMDtcbiAgICB0ZXh0LXRyYW5zZm9ybTogdXBwZXJjYXNlO1xuICB9XG59XG4iXSwic291cmNlUm9vdCI6IiJ9 */\"],\n      changeDetection: 0\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "FormControl", "FormGroup", "Validators", "buildOptions", "upgradeLobbyThemeOptions", "THEMES", "i0", "ɵɵtext", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "ɵɵelementStart", "ɵɵelementEnd", "ɵɵadvance", "ɵɵelement", "ɵɵtextInterpolate", "ctx_r1", "theme", "name", "ɵɵproperty", "thumbnailUrl", "ɵɵsanitizeUrl", "LobbyFormComponent", "<PERSON><PERSON><PERSON><PERSON>", "value", "options", "setTheme", "key", "form", "patchValue", "title", "description", "slice", "menuItems", "info", "lobby", "constructor", "snapshot", "queryParams", "path", "data", "widgets", "service", "themes", "activeTabIndex", "onSubmit", "required", "settings", "ngAfterViewChecked", "tabsRef", "realignInkBar", "titleControl", "get", "lobbyOptions", "themeOptions", "onSelectedIndexChange", "index", "submit", "formSubmitted", "invalid", "map", "some", "submitted", "mark<PERSON>llAsTouched", "valid", "params", "Object", "entries", "reduce", "result", "infoOptions", "emit", "filter", "tag", "widget", "handleActivateTemplate", "<PERSON><PERSON><PERSON>", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "i2", "LobbyService", "selectors", "viewQuery", "LobbyFormComponent_Query", "rf", "ctx", "ɵɵlistener", "LobbyFormComponent_Template_mat_tab_group_selectedIndexChange_1_listener", "$event", "ɵɵrestoreView", "_r1", "ɵɵresetView", "ɵɵtemplate", "LobbyFormComponent_ng_template_22_Template", "LobbyFormComponent_ng_template_27_Template", "LobbyFormComponent_div_29_Template", "LobbyFormComponent_ng_template_33_Template", "LobbyFormComponent_Template_div_onActivate_34_listener", "ɵɵprojection", "ɵɵpureFunction0", "_c3"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/lobby/lobby-form/lobby-form.component.ts", "/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/lobby/lobby-form/lobby-form.component.html"], "sourcesContent": ["import { AfterViewChecked, ChangeDetectionStrategy, Component, EventEmitter, Input, Output, ViewChild } from '@angular/core';\nimport { FormControl, FormGroup, Validators } from '@angular/forms';\nimport { MatTabGroup } from '@angular/material/tabs';\n\nimport { ActivatedRoute } from '@angular/router';\nimport { LobbyService } from '../../../common/services/lobby.service';\nimport { buildOptions, LobbyExtendedData, LobbyThemeOption, UpdateLobbyData, upgradeLobbyThemeOptions } from '../lobby.model';\nimport { Theme, Themes, THEMES } from '../theme.model';\nimport { LobbyWidget } from '../../../common/services/lobby-widgets.service';\n\n@Component({\n  selector: 'lobby-form',\n  templateUrl: './lobby-form.component.html',\n  styleUrls: ['./lobby-form.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class LobbyFormComponent implements AfterViewChecked {\n  readonly themes: Themes = THEMES;\n  readonly path: string;\n  readonly widgets: LobbyWidget[];\n  activeTabIndex = 0;\n\n  @Output() onSubmit = new EventEmitter<UpdateLobbyData>();\n\n  readonly form: FormGroup;\n  submitted: boolean;\n  theme: Theme;\n  @ViewChild('tabsTemplates') tabsRef: MatTabGroup;\n\n  private lobby: UpdateLobbyData;\n  private options: { [key: string]: LobbyThemeOption[] } = {};\n\n  @Input('lobby')\n  set setLobby( value: LobbyExtendedData | undefined ) {\n    if (value?.theme) {\n      value.theme.options = upgradeLobbyThemeOptions(value.theme.options ?? []);\n    }\n    if (('theme' in value) && value.theme !== null) {\n      this.setTheme(value.theme.key);\n    } else {\n      this.setTheme('bioshock');\n    }\n    this.form.patchValue({\n      title: value?.title ?? '',\n      description: value?.description ? `${value?.description}${value.description.slice(-1) === '\\n' ? '' : '\\n'}` : '',\n      menuItems: value?.info?.menuItems ?? [],\n    });\n    this.lobby = {\n      title: value?.title,\n      description: value?.description,\n      info: value?.info,\n      theme: value?.theme\n    };\n  }\n\n  constructor( { snapshot: { queryParams: { path }, data: { widgets } } }: ActivatedRoute,\n               private readonly service: LobbyService ) {\n    this.path = path;\n    this.widgets = widgets;\n    this.form = new FormGroup({\n      title: new FormControl('', Validators.required),\n      description: new FormControl(''),\n      settings: new FormGroup({}),\n      theme: new FormGroup({\n          options: new FormGroup({}),\n        }\n      ),\n      menuItems: new FormControl([]),\n    });\n  }\n\n  ngAfterViewChecked(): void {\n    if (this.tabsRef) {\n      this.tabsRef.realignInkBar();\n    }\n  }\n\n  get titleControl(): FormControl {\n    return this.form.get('title') as FormControl;\n  }\n\n  get lobbyOptions(): LobbyThemeOption[] {\n    return this.lobby && this.lobby.theme && this.lobby.theme.options || [];\n  }\n\n  get themeOptions(): LobbyThemeOption[] {\n    return this.options[this.theme.key] || this.lobbyOptions;\n  }\n\n  onSelectedIndexChange( index: number ) {\n    this.activeTabIndex = index;\n  }\n\n  submit() {\n    this.service.formSubmitted = true;\n\n    if (this.form.invalid) {\n      if (['title', 'description', 'settings'].map(name => this.form.get(name).invalid).some(invalid => invalid)) {\n        this.activeTabIndex = 0;\n      } else if (this.form.get('theme').invalid) {\n        this.activeTabIndex = 1;\n      } else {\n        this.activeTabIndex = 2;\n      }\n    }\n    this.submitted = true;\n    this.form.markAllAsTouched();\n    if (this.form.valid) {\n      const {\n        title,\n        description,\n        settings = {},\n        menuItems = [],\n        theme: { options: params = {} } = {}\n      } = this.form.value;\n\n      const options: LobbyThemeOption[] = Object.entries({ ...params, ...settings })\n        .reduce(( result, [key, value] ) => [...result, { key, value }], []);\n      const { info: infoOptions, theme: themeOptions } = buildOptions(this.theme.key, options);\n\n      this.onSubmit.emit({\n        title,\n        description: description || null,\n        theme: {\n          key: this.theme.key,\n          options: themeOptions\n        },\n        info: {\n          ...(this.lobby && this.lobby.info ? this.lobby.info : {}),\n          theme: {\n            key: this.theme.key\n          },\n          options: infoOptions,\n          menuItems,\n          widgets: this.widgets.filter(( { tag } ) => tag === 'x-widget-scoreboard').reduce(( result, widget ) => ({\n            ...result,\n            [widget.tag]: widget\n          }), {})\n        }\n      });\n    }\n  }\n\n  handleActivateTemplate( theme ) {\n    const { theme: { options = {} } = {} } = this.form.value;\n    this.options[this.theme.key] = Object.entries(options).reduce(( result, [key, value] ) => [...result, {\n      key,\n      value\n    }], []);\n\n    this.setTheme(theme.key);\n  }\n\n  private setTheme( themeKey: string ) {\n    const key = themeKey in this.themes ? themeKey : 'bioshock';\n    this.theme = { key, ...this.themes[key] };\n  }\n}\n", "<form [formGroup]=\"form\" class=\"p-32\">\n  <mat-tab-group animationDuration=\"0ms\" (selectedIndexChange)=\"onSelectedIndexChange($event)\" [selectedIndex]=\"activeTabIndex\">\n    <mat-tab [label]=\"'LOBBY.FORM.settings' | translate\">\n      <mat-card class=\"mat-elevation-z0\">\n        <div fxLayout=\"column\" style=\"max-width: 1000px\">\n          <mat-form-field appearance=\"outline\">\n            <mat-label>{{'LOBBY.FORM.title' | translate}}</mat-label>\n            <input matInput trimValue type=\"text\" formControlName=\"title\">\n            <mat-error>\n              <lib-swui-control-messages [control]=\"form.get('title')\" [force]=\"submitted\"></lib-swui-control-messages>\n            </mat-error>\n          </mat-form-field>\n\n          <mat-form-field appearance=\"outline\">\n            <mat-label>{{'LOBBY.FORM.description' | translate}}</mat-label>\n            <textarea matInput [cdkTextareaAutosize]=\"true\" rows=\"5\" cols=\"5\" formControlName=\"description\"></textarea>\n            <mat-error>\n              <lib-swui-control-messages [control]=\"form.get('description')\" [force]=\"submitted\"></lib-swui-control-messages>\n            </mat-error>\n          </mat-form-field>\n\n          <lobby-settings [form]=\"form\" [values]=\"lobbyOptions\" [submitted]=\"submitted\" name=\"settings\"></lobby-settings>\n        </div>\n      </mat-card>\n    </mat-tab>\n\n    <mat-tab>\n      <ng-template matTabLabel>\n        {{'LOBBY.FORM.template' | translate}}\n      </ng-template>\n      <mat-card class=\"mat-elevation-z0\">\n\n        <mat-tab-group #tabsTemplates animationDuration=\"0ms\" class=\"nested\">\n          <mat-tab>\n            <ng-template matTabLabel>\n              <mat-icon fontSet=\"material-icons-outline\" class=\"tab-icon\">settings</mat-icon> {{'LOBBY.FORM.templateSetup' | translate}}\n            </ng-template>\n            <div class=\"tpl-settings\">\n              <div *ngIf=\"theme\" class=\"tpl-settings__header\">\n                <div class=\"tpl-settings__title\">{{theme.name}}</div>\n                <div class=\"tpl-settings__image\">\n                  <img [src]=\"theme.thumbnailUrl\" [alt]=\"theme.name\">\n                </div>\n              </div>\n              <div class=\"tpl-settings__body\">\n                <div [themeTitle]=\"titleControl.value\" [options]=\"theme?.options\" [values]=\"themeOptions\"\n                     [submitted]=\"submitted\" name=\"options\" [template-settings]=\"form.get('theme')\"></div>\n              </div>\n            </div>\n\n          </mat-tab>\n\n          <mat-tab>\n            <ng-template matTabLabel>\n              <mat-icon fontSet=\"material-icons-outline\" class=\"tab-icon\">important_devices</mat-icon> {{'LOBBY.FORM.templateList' | translate}}\n            </ng-template>\n\n            <div [templates-list]=\"themes\" [active]=\"theme\" (onActivate)=\"handleActivateTemplate($event)\"></div>\n          </mat-tab>\n        </mat-tab-group>\n\n      </mat-card>\n    </mat-tab>\n\n    <mat-tab [label]=\"'LOBBY.MENU_ITEMS.menuItems' | translate\">\n      <lobby-menu-items\n        formControlName=\"menuItems\"\n        [themeKey]=\"theme.key\"\n        [widgets]=\"widgets\"\n        [submitted]=\"submitted\">\n      </lobby-menu-items>\n    </mat-tab>\n  </mat-tab-group>\n\n  <div style=\"padding: 32px 0\" fxLayout=\"row\" fxLayoutAlign=\"end center\">\n    <button [routerLink]=\"['/pages/lobby/layouts']\" mat-stroked-button color=\"primary\" style=\"margin-right: 8px;\">\n      <mat-icon>keyboard_arrow_left</mat-icon>\n      {{ 'LOBBY.FORM.back' | translate }}\n    </button>\n    <ng-content select=\".submit-button\"></ng-content>\n  </div>\n</form>\n"], "mappings": "AAAA,SAA+DA,YAAY,QAAkC,eAAe;AAC5H,SAASC,WAAW,EAAEC,SAAS,EAAEC,UAAU,QAAQ,gBAAgB;AAKnE,SAASC,YAAY,EAAwDC,wBAAwB,QAAQ,gBAAgB;AAC7H,SAAwBC,MAAM,QAAQ,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;ICqB9CC,EAAA,CAAAC,MAAA,GACF;;;;IADED,EAAA,CAAAE,kBAAA,MAAAF,EAAA,CAAAG,WAAA,mCACF;;;;;IAMQH,EAAA,CAAAI,cAAA,mBAA4D;IAAAJ,EAAA,CAAAC,MAAA,eAAQ;IAAAD,EAAA,CAAAK,YAAA,EAAW;IAACL,EAAA,CAAAC,MAAA,GAClF;;;;IADkFD,EAAA,CAAAM,SAAA,GAClF;IADkFN,EAAA,CAAAE,kBAAA,MAAAF,EAAA,CAAAG,WAAA,wCAClF;;;;;IAGIH,EADF,CAAAI,cAAA,cAAgD,cACb;IAAAJ,EAAA,CAAAC,MAAA,GAAc;IAAAD,EAAA,CAAAK,YAAA,EAAM;IACrDL,EAAA,CAAAI,cAAA,cAAiC;IAC/BJ,EAAA,CAAAO,SAAA,cAAmD;IAEvDP,EADE,CAAAK,YAAA,EAAM,EACF;;;;IAJ6BL,EAAA,CAAAM,SAAA,GAAc;IAAdN,EAAA,CAAAQ,iBAAA,CAAAC,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAc;IAExCX,EAAA,CAAAM,SAAA,GAA0B;IAACN,EAA3B,CAAAY,UAAA,QAAAH,MAAA,CAAAC,KAAA,CAAAG,YAAA,EAAAb,EAAA,CAAAc,aAAA,CAA0B,QAAAL,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAmB;;;;;IAatDX,EAAA,CAAAI,cAAA,mBAA4D;IAAAJ,EAAA,CAAAC,MAAA,wBAAiB;IAAAD,EAAA,CAAAK,YAAA,EAAW;IAACL,EAAA,CAAAC,MAAA,GAC3F;;;;IAD2FD,EAAA,CAAAM,SAAA,GAC3F;IAD2FN,EAAA,CAAAE,kBAAA,MAAAF,EAAA,CAAAG,WAAA,uCAC3F;;;ADvCZ,OAAM,MAAOY,kBAAkB;EAgB7B,IACIC,QAAQA,CAAEC,KAAoC;IAChD,IAAIA,KAAK,EAAEP,KAAK,EAAE;MAChBO,KAAK,CAACP,KAAK,CAACQ,OAAO,GAAGpB,wBAAwB,CAACmB,KAAK,CAACP,KAAK,CAACQ,OAAO,IAAI,EAAE,CAAC;IAC3E;IACA,IAAK,OAAO,IAAID,KAAK,IAAKA,KAAK,CAACP,KAAK,KAAK,IAAI,EAAE;MAC9C,IAAI,CAACS,QAAQ,CAACF,KAAK,CAACP,KAAK,CAACU,GAAG,CAAC;IAChC,CAAC,MAAM;MACL,IAAI,CAACD,QAAQ,CAAC,UAAU,CAAC;IAC3B;IACA,IAAI,CAACE,IAAI,CAACC,UAAU,CAAC;MACnBC,KAAK,EAAEN,KAAK,EAAEM,KAAK,IAAI,EAAE;MACzBC,WAAW,EAAEP,KAAK,EAAEO,WAAW,GAAG,GAAGP,KAAK,EAAEO,WAAW,GAAGP,KAAK,CAACO,WAAW,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,GAAG,EAAE,GAAG,IAAI,EAAE,GAAG,EAAE;MACjHC,SAAS,EAAET,KAAK,EAAEU,IAAI,EAAED,SAAS,IAAI;KACtC,CAAC;IACF,IAAI,CAACE,KAAK,GAAG;MACXL,KAAK,EAAEN,KAAK,EAAEM,KAAK;MACnBC,WAAW,EAAEP,KAAK,EAAEO,WAAW;MAC/BG,IAAI,EAAEV,KAAK,EAAEU,IAAI;MACjBjB,KAAK,EAAEO,KAAK,EAAEP;KACf;EACH;EAEAmB,YAAa;IAAEC,QAAQ,EAAE;MAAEC,WAAW,EAAE;QAAEC;MAAI,CAAE;MAAEC,IAAI,EAAE;QAAEC;MAAO;IAAE;EAAE,CAAkB,EACzDC,OAAqB;IAArB,KAAAA,OAAO,GAAPA,OAAO;IAvC5B,KAAAC,MAAM,GAAWrC,MAAM;IAGhC,KAAAsC,cAAc,GAAG,CAAC;IAER,KAAAC,QAAQ,GAAG,IAAI7C,YAAY,EAAmB;IAQhD,KAAAyB,OAAO,GAA0C,EAAE;IA2BzD,IAAI,CAACc,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACE,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACb,IAAI,GAAG,IAAI1B,SAAS,CAAC;MACxB4B,KAAK,EAAE,IAAI7B,WAAW,CAAC,EAAE,EAAEE,UAAU,CAAC2C,QAAQ,CAAC;MAC/Cf,WAAW,EAAE,IAAI9B,WAAW,CAAC,EAAE,CAAC;MAChC8C,QAAQ,EAAE,IAAI7C,SAAS,CAAC,EAAE,CAAC;MAC3Be,KAAK,EAAE,IAAIf,SAAS,CAAC;QACjBuB,OAAO,EAAE,IAAIvB,SAAS,CAAC,EAAE;OAC1B,CACF;MACD+B,SAAS,EAAE,IAAIhC,WAAW,CAAC,EAAE;KAC9B,CAAC;EACJ;EAEA+C,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACC,OAAO,EAAE;MAChB,IAAI,CAACA,OAAO,CAACC,aAAa,EAAE;IAC9B;EACF;EAEA,IAAIC,YAAYA,CAAA;IACd,OAAO,IAAI,CAACvB,IAAI,CAACwB,GAAG,CAAC,OAAO,CAAgB;EAC9C;EAEA,IAAIC,YAAYA,CAAA;IACd,OAAO,IAAI,CAAClB,KAAK,IAAI,IAAI,CAACA,KAAK,CAAClB,KAAK,IAAI,IAAI,CAACkB,KAAK,CAAClB,KAAK,CAACQ,OAAO,IAAI,EAAE;EACzE;EAEA,IAAI6B,YAAYA,CAAA;IACd,OAAO,IAAI,CAAC7B,OAAO,CAAC,IAAI,CAACR,KAAK,CAACU,GAAG,CAAC,IAAI,IAAI,CAAC0B,YAAY;EAC1D;EAEAE,qBAAqBA,CAAEC,KAAa;IAClC,IAAI,CAACZ,cAAc,GAAGY,KAAK;EAC7B;EAEAC,MAAMA,CAAA;IACJ,IAAI,CAACf,OAAO,CAACgB,aAAa,GAAG,IAAI;IAEjC,IAAI,IAAI,CAAC9B,IAAI,CAAC+B,OAAO,EAAE;MACrB,IAAI,CAAC,OAAO,EAAE,aAAa,EAAE,UAAU,CAAC,CAACC,GAAG,CAAC1C,IAAI,IAAI,IAAI,CAACU,IAAI,CAACwB,GAAG,CAAClC,IAAI,CAAC,CAACyC,OAAO,CAAC,CAACE,IAAI,CAACF,OAAO,IAAIA,OAAO,CAAC,EAAE;QAC1G,IAAI,CAACf,cAAc,GAAG,CAAC;MACzB,CAAC,MAAM,IAAI,IAAI,CAAChB,IAAI,CAACwB,GAAG,CAAC,OAAO,CAAC,CAACO,OAAO,EAAE;QACzC,IAAI,CAACf,cAAc,GAAG,CAAC;MACzB,CAAC,MAAM;QACL,IAAI,CAACA,cAAc,GAAG,CAAC;MACzB;IACF;IACA,IAAI,CAACkB,SAAS,GAAG,IAAI;IACrB,IAAI,CAAClC,IAAI,CAACmC,gBAAgB,EAAE;IAC5B,IAAI,IAAI,CAACnC,IAAI,CAACoC,KAAK,EAAE;MACnB,MAAM;QACJlC,KAAK;QACLC,WAAW;QACXgB,QAAQ,GAAG,EAAE;QACbd,SAAS,GAAG,EAAE;QACdhB,KAAK,EAAE;UAAEQ,OAAO,EAAEwC,MAAM,GAAG;QAAE,CAAE,GAAG;MAAE,CACrC,GAAG,IAAI,CAACrC,IAAI,CAACJ,KAAK;MAEnB,MAAMC,OAAO,GAAuByC,MAAM,CAACC,OAAO,CAAC;QAAE,GAAGF,MAAM;QAAE,GAAGlB;MAAQ,CAAE,CAAC,CAC3EqB,MAAM,CAAC,CAAEC,MAAM,EAAE,CAAC1C,GAAG,EAAEH,KAAK,CAAC,KAAM,CAAC,GAAG6C,MAAM,EAAE;QAAE1C,GAAG;QAAEH;MAAK,CAAE,CAAC,EAAE,EAAE,CAAC;MACtE,MAAM;QAAEU,IAAI,EAAEoC,WAAW;QAAErD,KAAK,EAAEqC;MAAY,CAAE,GAAGlD,YAAY,CAAC,IAAI,CAACa,KAAK,CAACU,GAAG,EAAEF,OAAO,CAAC;MAExF,IAAI,CAACoB,QAAQ,CAAC0B,IAAI,CAAC;QACjBzC,KAAK;QACLC,WAAW,EAAEA,WAAW,IAAI,IAAI;QAChCd,KAAK,EAAE;UACLU,GAAG,EAAE,IAAI,CAACV,KAAK,CAACU,GAAG;UACnBF,OAAO,EAAE6B;SACV;QACDpB,IAAI,EAAE;UACJ,IAAI,IAAI,CAACC,KAAK,IAAI,IAAI,CAACA,KAAK,CAACD,IAAI,GAAG,IAAI,CAACC,KAAK,CAACD,IAAI,GAAG,EAAE,CAAC;UACzDjB,KAAK,EAAE;YACLU,GAAG,EAAE,IAAI,CAACV,KAAK,CAACU;WACjB;UACDF,OAAO,EAAE6C,WAAW;UACpBrC,SAAS;UACTQ,OAAO,EAAE,IAAI,CAACA,OAAO,CAAC+B,MAAM,CAAC,CAAE;YAAEC;UAAG,CAAE,KAAMA,GAAG,KAAK,qBAAqB,CAAC,CAACL,MAAM,CAAC,CAAEC,MAAM,EAAEK,MAAM,MAAO;YACvG,GAAGL,MAAM;YACT,CAACK,MAAM,CAACD,GAAG,GAAGC;WACf,CAAC,EAAE,EAAE;;OAET,CAAC;IACJ;EACF;EAEAC,sBAAsBA,CAAE1D,KAAK;IAC3B,MAAM;MAAEA,KAAK,EAAE;QAAEQ,OAAO,GAAG;MAAE,CAAE,GAAG;IAAE,CAAE,GAAG,IAAI,CAACG,IAAI,CAACJ,KAAK;IACxD,IAAI,CAACC,OAAO,CAAC,IAAI,CAACR,KAAK,CAACU,GAAG,CAAC,GAAGuC,MAAM,CAACC,OAAO,CAAC1C,OAAO,CAAC,CAAC2C,MAAM,CAAC,CAAEC,MAAM,EAAE,CAAC1C,GAAG,EAAEH,KAAK,CAAC,KAAM,CAAC,GAAG6C,MAAM,EAAE;MACpG1C,GAAG;MACHH;KACD,CAAC,EAAE,EAAE,CAAC;IAEP,IAAI,CAACE,QAAQ,CAACT,KAAK,CAACU,GAAG,CAAC;EAC1B;EAEQD,QAAQA,CAAEkD,QAAgB;IAChC,MAAMjD,GAAG,GAAGiD,QAAQ,IAAI,IAAI,CAACjC,MAAM,GAAGiC,QAAQ,GAAG,UAAU;IAC3D,IAAI,CAAC3D,KAAK,GAAG;MAAEU,GAAG;MAAE,GAAG,IAAI,CAACgB,MAAM,CAAChB,GAAG;IAAC,CAAE;EAC3C;;;uCA5IWL,kBAAkB,EAAAf,EAAA,CAAAsE,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAxE,EAAA,CAAAsE,iBAAA,CAAAG,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAAlB3D,kBAAkB;MAAA4D,SAAA;MAAAC,SAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;;;;UCf7B9E,EADF,CAAAI,cAAA,cAAsC,uBAC0F;UAAvFJ,EAAA,CAAAgF,UAAA,iCAAAC,yEAAAC,MAAA;YAAAlF,EAAA,CAAAmF,aAAA,CAAAC,GAAA;YAAA,OAAApF,EAAA,CAAAqF,WAAA,CAAuBN,GAAA,CAAA/B,qBAAA,CAAAkC,MAAA,CAA6B;UAAA,EAAC;UAC1FlF,EAAA,CAAAI,cAAA,iBAAqD;;UAI7CJ,EAHN,CAAAI,cAAA,kBAAmC,aACgB,wBACV,gBACxB;UAAAJ,EAAA,CAAAC,MAAA,GAAkC;;UAAAD,EAAA,CAAAK,YAAA,EAAY;UACzDL,EAAA,CAAAO,SAAA,gBAA8D;UAC9DP,EAAA,CAAAI,cAAA,iBAAW;UACTJ,EAAA,CAAAO,SAAA,oCAAyG;UAE7GP,EADE,CAAAK,YAAA,EAAY,EACG;UAGfL,EADF,CAAAI,cAAA,yBAAqC,iBACxB;UAAAJ,EAAA,CAAAC,MAAA,IAAwC;;UAAAD,EAAA,CAAAK,YAAA,EAAY;UAC/DL,EAAA,CAAAO,SAAA,mBAA2G;UAC3GP,EAAA,CAAAI,cAAA,iBAAW;UACTJ,EAAA,CAAAO,SAAA,oCAA+G;UAEnHP,EADE,CAAAK,YAAA,EAAY,EACG;UAEjBL,EAAA,CAAAO,SAAA,0BAA+G;UAGrHP,EAFI,CAAAK,YAAA,EAAM,EACG,EACH;UAEVL,EAAA,CAAAI,cAAA,eAAS;UACPJ,EAAA,CAAAsF,UAAA,KAAAC,0CAAA,0BAAyB;UAMrBvF,EAHJ,CAAAI,cAAA,mBAAmC,4BAEoC,eAC1D;UACPJ,EAAA,CAAAsF,UAAA,KAAAE,0CAAA,0BAAyB;UAGzBxF,EAAA,CAAAI,cAAA,eAA0B;UACxBJ,EAAA,CAAAsF,UAAA,KAAAG,kCAAA,kBAAgD;UAMhDzF,EAAA,CAAAI,cAAA,eAAgC;UAC9BJ,EAAA,CAAAO,SAAA,eAC0F;UAIhGP,EAHI,CAAAK,YAAA,EAAM,EACF,EAEE;UAEVL,EAAA,CAAAI,cAAA,eAAS;UACPJ,EAAA,CAAAsF,UAAA,KAAAI,0CAAA,0BAAyB;UAIzB1F,EAAA,CAAAI,cAAA,eAA8F;UAA9CJ,EAAA,CAAAgF,UAAA,wBAAAW,uDAAAT,MAAA;YAAAlF,EAAA,CAAAmF,aAAA,CAAAC,GAAA;YAAA,OAAApF,EAAA,CAAAqF,WAAA,CAAcN,GAAA,CAAAX,sBAAA,CAAAc,MAAA,CAA8B;UAAA,EAAC;UAKrGlF,EALsG,CAAAK,YAAA,EAAM,EAC5F,EACI,EAEP,EACH;UAEVL,EAAA,CAAAI,cAAA,kBAA4D;;UAC1DJ,EAAA,CAAAO,SAAA,4BAKmB;UAEvBP,EADE,CAAAK,YAAA,EAAU,EACI;UAIZL,EAFJ,CAAAI,cAAA,eAAuE,kBACyC,gBAClG;UAAAJ,EAAA,CAAAC,MAAA,2BAAmB;UAAAD,EAAA,CAAAK,YAAA,EAAW;UACxCL,EAAA,CAAAC,MAAA,IACF;;UAAAD,EAAA,CAAAK,YAAA,EAAS;UACTL,EAAA,CAAA4F,YAAA,IAAiD;UAErD5F,EADE,CAAAK,YAAA,EAAM,EACD;;;UAjFDL,EAAA,CAAAY,UAAA,cAAAmE,GAAA,CAAA1D,IAAA,CAAkB;UACuErB,EAAA,CAAAM,SAAA,EAAgC;UAAhCN,EAAA,CAAAY,UAAA,kBAAAmE,GAAA,CAAA1C,cAAA,CAAgC;UAClHrC,EAAA,CAAAM,SAAA,EAA2C;UAA3CN,EAAA,CAAAY,UAAA,UAAAZ,EAAA,CAAAG,WAAA,+BAA2C;UAIjCH,EAAA,CAAAM,SAAA,GAAkC;UAAlCN,EAAA,CAAAQ,iBAAA,CAAAR,EAAA,CAAAG,WAAA,4BAAkC;UAGhBH,EAAA,CAAAM,SAAA,GAA6B;UAACN,EAA9B,CAAAY,UAAA,YAAAmE,GAAA,CAAA1D,IAAA,CAAAwB,GAAA,UAA6B,UAAAkC,GAAA,CAAAxB,SAAA,CAAoB;UAKnEvD,EAAA,CAAAM,SAAA,GAAwC;UAAxCN,EAAA,CAAAQ,iBAAA,CAAAR,EAAA,CAAAG,WAAA,mCAAwC;UAChCH,EAAA,CAAAM,SAAA,GAA4B;UAA5BN,EAAA,CAAAY,UAAA,6BAA4B;UAElBZ,EAAA,CAAAM,SAAA,GAAmC;UAACN,EAApC,CAAAY,UAAA,YAAAmE,GAAA,CAAA1D,IAAA,CAAAwB,GAAA,gBAAmC,UAAAkC,GAAA,CAAAxB,SAAA,CAAoB;UAItEvD,EAAA,CAAAM,SAAA,EAAa;UAAyBN,EAAtC,CAAAY,UAAA,SAAAmE,GAAA,CAAA1D,IAAA,CAAa,WAAA0D,GAAA,CAAAjC,YAAA,CAAwB,cAAAiC,GAAA,CAAAxB,SAAA,CAAwB;UAiBnEvD,EAAA,CAAAM,SAAA,GAAW;UAAXN,EAAA,CAAAY,UAAA,SAAAmE,GAAA,CAAArE,KAAA,CAAW;UAOVV,EAAA,CAAAM,SAAA,GAAiC;UACMN,EADvC,CAAAY,UAAA,eAAAmE,GAAA,CAAAnC,YAAA,CAAA3B,KAAA,CAAiC,YAAA8D,GAAA,CAAArE,KAAA,kBAAAqE,GAAA,CAAArE,KAAA,CAAAQ,OAAA,CAA2B,WAAA6D,GAAA,CAAAhC,YAAA,CAAwB,cAAAgC,GAAA,CAAAxB,SAAA,CAC7D,sBAAAwB,GAAA,CAAA1D,IAAA,CAAAwB,GAAA,UAAuD;UAWlF7C,EAAA,CAAAM,SAAA,GAAyB;UAACN,EAA1B,CAAAY,UAAA,mBAAAmE,GAAA,CAAA3C,MAAA,CAAyB,WAAA2C,GAAA,CAAArE,KAAA,CAAiB;UAO9CV,EAAA,CAAAM,SAAA,EAAkD;UAAlDN,EAAA,CAAAY,UAAA,UAAAZ,EAAA,CAAAG,WAAA,uCAAkD;UAGvDH,EAAA,CAAAM,SAAA,GAAsB;UAEtBN,EAFA,CAAAY,UAAA,aAAAmE,GAAA,CAAArE,KAAA,CAAAU,GAAA,CAAsB,YAAA2D,GAAA,CAAA7C,OAAA,CACH,cAAA6C,GAAA,CAAAxB,SAAA,CACI;UAMnBvD,EAAA,CAAAM,SAAA,GAAuC;UAAvCN,EAAA,CAAAY,UAAA,eAAAZ,EAAA,CAAA6F,eAAA,KAAAC,GAAA,EAAuC;UAE7C9F,EAAA,CAAAM,SAAA,GACF;UADEN,EAAA,CAAAE,kBAAA,MAAAF,EAAA,CAAAG,WAAA,iCACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}