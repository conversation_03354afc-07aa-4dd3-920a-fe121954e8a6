{"ast": null, "code": "import { FormControl } from '@angular/forms';\nimport { MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport { Subject } from 'rxjs';\nimport { filter, map, switchMap, takeUntil, tap } from 'rxjs/operators';\nimport { PERMISSIONS_LIST } from '../../../../../../app.constants';\nimport { GameGroup } from '../../../../../../common/models/game-group.model';\nimport { SelectOptionModel } from '../../../../../../common/models/select-option.model';\nimport { GAME_GROUP_CUSTOMIZATION } from '../../../../../../common/typings/flat-report';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"../../../../../../common/services/game-group.service\";\nimport * as i4 from \"../../../../../../common/services/game.service\";\nimport * as i5 from \"@skywind-group/lib-swui\";\nimport * as i6 from \"@ngx-translate/core\";\nimport * as i7 from \"../../../../../../common/services/excel.service\";\nimport * as i8 from \"../../../../../../common/services/entity-settings.service\";\nimport * as i9 from \"../../../../../../common/services/flat-reports.service\";\nimport * as i10 from \"@angular/common\";\nimport * as i11 from \"@angular/material/button\";\nimport * as i12 from \"@angular/material/icon\";\nimport * as i13 from \"@angular/material/form-field\";\nimport * as i14 from \"@angular/flex-layout/flex\";\nimport * as i15 from \"@angular/material/table\";\nimport * as i16 from \"@angular/material/sort\";\nimport * as i17 from \"../../structure-entity.model\";\nfunction ShowLimitsModalComponent_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function ShowLimitsModalComponent_button_7_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.exportAsXLSX());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"ENTITY_SETUP.GAME_LIMITS.exportToExcel\"), \" \");\n  }\n}\nfunction ShowLimitsModalComponent_div_8_div_29_th_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, \"ENTITY_SETUP.GAME_LIMITS.TABLE.currency\"));\n  }\n}\nfunction ShowLimitsModalComponent_div_8_div_29_td_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r5 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(row_r5 == null ? null : row_r5.currency);\n  }\n}\nfunction ShowLimitsModalComponent_div_8_div_29_th_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, \"ENTITY_SETUP.GAME_LIMITS.TABLE.coinBets\"));\n  }\n}\nfunction ShowLimitsModalComponent_div_8_div_29_td_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r6 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.joinStakeAll(row_r6 == null ? null : row_r6.stakeAll));\n  }\n}\nfunction ShowLimitsModalComponent_div_8_div_29_th_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, \"ENTITY_SETUP.GAME_LIMITS.TABLE.minCoinBet\"));\n  }\n}\nfunction ShowLimitsModalComponent_div_8_div_29_td_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r7 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(row_r7 == null ? null : row_r7.stakeMin);\n  }\n}\nfunction ShowLimitsModalComponent_div_8_div_29_th_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, \"ENTITY_SETUP.GAME_LIMITS.TABLE.maxCoinBet\"));\n  }\n}\nfunction ShowLimitsModalComponent_div_8_div_29_td_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r8 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(row_r8 == null ? null : row_r8.stakeMax);\n  }\n}\nfunction ShowLimitsModalComponent_div_8_div_29_th_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, \"ENTITY_SETUP.GAME_LIMITS.TABLE.maxTotalBet\"));\n  }\n}\nfunction ShowLimitsModalComponent_div_8_div_29_td_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r9 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(row_r9 == null ? null : row_r9.maxTotalStake);\n  }\n}\nfunction ShowLimitsModalComponent_div_8_div_29_th_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, \"ENTITY_SETUP.GAME_LIMITS.TABLE.stakeDef\"));\n  }\n}\nfunction ShowLimitsModalComponent_div_8_div_29_td_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r10 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(row_r10 == null ? null : row_r10.stakeDef);\n  }\n}\nfunction ShowLimitsModalComponent_div_8_div_29_tr_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 37);\n  }\n}\nfunction ShowLimitsModalComponent_div_8_div_29_tr_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 38);\n  }\n}\nfunction ShowLimitsModalComponent_div_8_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 23)(2, \"table\", 24);\n    i0.ɵɵelementContainerStart(3, 25);\n    i0.ɵɵtemplate(4, ShowLimitsModalComponent_div_8_div_29_th_4_Template, 3, 3, \"th\", 26)(5, ShowLimitsModalComponent_div_8_div_29_td_5_Template, 2, 1, \"td\", 27);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(6, 28);\n    i0.ɵɵtemplate(7, ShowLimitsModalComponent_div_8_div_29_th_7_Template, 3, 3, \"th\", 26)(8, ShowLimitsModalComponent_div_8_div_29_td_8_Template, 2, 1, \"td\", 27);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(9, 29);\n    i0.ɵɵtemplate(10, ShowLimitsModalComponent_div_8_div_29_th_10_Template, 3, 3, \"th\", 26)(11, ShowLimitsModalComponent_div_8_div_29_td_11_Template, 2, 1, \"td\", 27);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(12, 30);\n    i0.ɵɵtemplate(13, ShowLimitsModalComponent_div_8_div_29_th_13_Template, 3, 3, \"th\", 26)(14, ShowLimitsModalComponent_div_8_div_29_td_14_Template, 2, 1, \"td\", 27);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(15, 31);\n    i0.ɵɵtemplate(16, ShowLimitsModalComponent_div_8_div_29_th_16_Template, 3, 3, \"th\", 26)(17, ShowLimitsModalComponent_div_8_div_29_td_17_Template, 2, 1, \"td\", 27);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(18, 32);\n    i0.ɵɵtemplate(19, ShowLimitsModalComponent_div_8_div_29_th_19_Template, 3, 3, \"th\", 26)(20, ShowLimitsModalComponent_div_8_div_29_td_20_Template, 2, 1, \"td\", 27);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵtemplate(21, ShowLimitsModalComponent_div_8_div_29_tr_21_Template, 1, 0, \"tr\", 33)(22, ShowLimitsModalComponent_div_8_div_29_tr_22_Template, 1, 0, \"tr\", 34);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"dataSource\", ctx_r2.limits);\n    i0.ɵɵadvance(19);\n    i0.ɵɵproperty(\"matHeaderRowDef\", ctx_r2.displayedColumns)(\"matHeaderRowDefSticky\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"matRowDefColumns\", ctx_r2.displayedColumns);\n  }\n}\nfunction ShowLimitsModalComponent_div_8_div_30_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, \"ENTITY_SETUP.GAME_LIMITS.noLimitsFound\"));\n  }\n}\nfunction ShowLimitsModalComponent_div_8_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 39);\n    i0.ɵɵtemplate(2, ShowLimitsModalComponent_div_8_div_30_span_2_Template, 3, 3, \"span\", 21);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.loading);\n  }\n}\nfunction ShowLimitsModalComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 9)(2, \"mat-form-field\", 10)(3, \"mat-label\");\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(6, \"lib-swui-select\", 11);\n    i0.ɵɵpipe(7, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 12)(9, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function ShowLimitsModalComponent_div_8_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.submit());\n    });\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"translate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"form\", 14)(13, \"div\", 15)(14, \"div\", 16)(15, \"div\", 17)(16, \"mat-form-field\", 10)(17, \"mat-label\");\n    i0.ɵɵtext(18);\n    i0.ɵɵpipe(19, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(20, \"lib-swui-select\", 18);\n    i0.ɵɵpipe(21, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 19)(23, \"mat-form-field\", 10)(24, \"mat-label\");\n    i0.ɵɵtext(25);\n    i0.ɵɵpipe(26, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(27, \"lib-swui-select\", 18);\n    i0.ɵɵpipe(28, \"translate\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵtemplate(29, ShowLimitsModalComponent_div_8_div_29_Template, 23, 4, \"div\", 20)(30, ShowLimitsModalComponent_div_8_div_30_Template, 3, 1, \"div\", 21);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 24, \"ENTITY_SETUP.GAME_LIMITS.defaultGameGroupLabel\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"placeholder\", i0.ɵɵpipeBind1(7, 26, \"ENTITY_SETUP.GAME_LIMITS.defaultGameGroupLabel\"))(\"data\", ctx_r2.gameGroupsSelectOptions)(\"showSearch\", true)(\"disableEmptyOption\", true)(\"disabled\", !ctx_r2.isEditGameGroupAllowed())(\"formControl\", ctx_r2.defaultGameGroup);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", !ctx_r2.isEditGameGroupAllowed());\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(11, 28, \"DIALOG.save\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"formGroup\", ctx_r2.form);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(19, 30, \"ENTITY_SETUP.GAME_LIMITS.gameGroupLabel\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"placeholder\", i0.ɵɵpipeBind1(21, 32, \"ENTITY_SETUP.GAME_LIMITS.gameGroupLabel\"))(\"data\", ctx_r2.gameGroupsSelectOptions)(\"showSearch\", true)(\"disableEmptyOption\", true)(\"formControl\", ctx_r2.gameGroupControl);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(26, 34, \"ENTITY_SETUP.GAME_LIMITS.gameLabel\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"placeholder\", i0.ɵɵpipeBind1(28, 36, \"ENTITY_SETUP.GAME_LIMITS.gameLabel\"))(\"data\", ctx_r2.gamesSelectOptions)(\"showSearch\", true)(\"disableEmptyOption\", true)(\"formControl\", ctx_r2.gameControl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.limits == null ? null : ctx_r2.limits.length);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !(ctx_r2.limits == null ? null : ctx_r2.limits.length) && !ctx_r2.loading);\n  }\n}\nfunction ShowLimitsModalComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"ENTITY_SETUP.GAME_LIMITS.noLimitsAvailable\"), \" \");\n  }\n}\nexport class ShowLimitsModalComponent {\n  constructor(dialogRef, initialState, fb, gameGroupService, gameService, notifications, translate, authService, excelService, entitySettingsService, flatReportsService) {\n    this.dialogRef = dialogRef;\n    this.initialState = initialState;\n    this.fb = fb;\n    this.gameGroupService = gameGroupService;\n    this.gameService = gameService;\n    this.notifications = notifications;\n    this.translate = translate;\n    this.authService = authService;\n    this.excelService = excelService;\n    this.entitySettingsService = entitySettingsService;\n    this.flatReportsService = flatReportsService;\n    this.operatorColumn = 'Operator';\n    this.gameGroupColumn = 'Game Group';\n    this.defaultGameGroupColumn = 'Default Game Group';\n    this.gameColumn = 'Game';\n    this.currencyColumn = 'Currency';\n    this.customizationColumn = 'Default/Customized';\n    this.coinBets = 'Coin Bets';\n    this.minCoinBet = 'Min Coin Bet';\n    this.maxCoinBet = 'Max Coin Bet';\n    this.maxTotalBet = 'Max Total Bet';\n    this.stakeDef = 'Stake Def';\n    this.displayedColumns = ['currency', 'stakeAll', 'stakeMin', 'stakeMax', 'maxTotalStake', 'stakeDef'];\n    this.limits = [];\n    this.gameGroupsSelectOptions = [];\n    this.initialGamesSelectOptions = [];\n    this.gamesSelectOptions = [];\n    this.defaultGameGroup = new FormControl('');\n    this.loading = false;\n    this.isExportLimitsAllowed = false;\n    this.destroyed$ = new Subject();\n    this.entity = this.initialState;\n    this.isExportLimitsAllowed = this.authService.allowedTo(PERMISSIONS_LIST.FLAT_REPORTS);\n    this.entitySettingsService.getSettings(this.entity.path).pipe(takeUntil(this.destroyed$)).subscribe(data => this.entitySettings = data);\n  }\n  ngOnInit() {\n    this.buildGameGroupsSelectOptions();\n    this.buildGamesSelectOptions();\n    this.initForm();\n  }\n  ngOnDestroy() {\n    this.destroyed$.next();\n    this.destroyed$.complete();\n  }\n  get gameGroupControl() {\n    return this.form.get('gameGroup');\n  }\n  get gameControl() {\n    return this.form.get('game');\n  }\n  isEditGameGroupAllowed() {\n    return this.authService.allowedTo(PERMISSIONS_LIST.GAME_GROUP_EDIT);\n  }\n  joinStakeAll(stakeAll) {\n    return stakeAll.join(', ');\n  }\n  submit() {\n    const defaultGameGroupName = this.gameGroupsSelectOptions.find(group => group.id === this.defaultGameGroup.value).text;\n    this.gameGroupService.setDefaultGameGroup(this.entity.path, defaultGameGroupName).subscribe(() => {\n      this.notifications.success(this.translate.instant('ENTITY_SETUP.GAME_GROUP.notificationDefaultGameGroupChanged', {\n        gameGroup: this.defaultGameGroup.value\n      }));\n    });\n  }\n  onNoClick() {\n    this.dialogRef.close(null);\n  }\n  exportAsXLSX() {\n    this.flatReportsService.getTheLatestUpdatedFlatReport(this.entity.path).pipe(switchMap(flatReport => {\n      return this.gameService.getAllGames(this.entity.path, false, true).pipe(map(games => ({\n        flatReport,\n        games\n      })));\n    }), takeUntil(this.destroyed$)).subscribe(({\n      flatReport,\n      games\n    }) => {\n      if (flatReport) {\n        const resultArray = [];\n        const report = flatReport.report;\n        Object.entries(report)?.map(([gameCode, gameGroups]) => {\n          Object.entries(gameGroups)?.map(([gameGroup, currencies]) => {\n            Object.entries(currencies)?.map(([currency, limits]) => {\n              let row = {};\n              let customization = 'Default';\n              if (Array.isArray(limits?.limitsCustomizations) && limits?.limitsCustomizations.length) {\n                if (limits?.limitsCustomizations.length === 1) {\n                  customization = limits?.limitsCustomizations[0] === GAME_GROUP_CUSTOMIZATION ? 'Customized by game group' : 'Customized by filters';\n                } else {\n                  customization = 'Customized by game group and filters';\n                }\n              }\n              row[this.operatorColumn] = this.entity.name;\n              row[this.gameGroupColumn] = gameGroup;\n              row[this.defaultGameGroupColumn] = gameGroup === this.defaultGameGroup.value ? 'Yes' : 'No';\n              row[this.gameColumn] = games?.find(game => game.code === gameCode)?.title;\n              row[this.currencyColumn] = currency;\n              row[this.customizationColumn] = customization;\n              row[this.coinBets] = limits?.limits?.stakeAll.join(', ');\n              row[this.minCoinBet] = limits?.limits?.stakeMin;\n              row[this.maxCoinBet] = limits?.limits?.stakeMax;\n              row[this.maxTotalBet] = limits?.limits?.maxTotalStake;\n              row[this.stakeDef] = limits?.limits?.stakeDef;\n              resultArray.push(row);\n            });\n          });\n        });\n        resultArray.sort((prev, curr) => prev[this.gameGroupColumn].localeCompare(curr[this.gameGroupColumn]));\n        this.excelService.exportAsExcelFile(resultArray, this.entity.path.replace(':', '') + '_limits');\n        this.notifications.success(this.translate.instant('ENTITY_SETUP.GAME_LIMITS.flatReportWasExported'));\n      } else {\n        this.notifications.warning(this.translate.instant('ENTITY_SETUP.GAME_LIMITS.flatReportNotFound'));\n      }\n    });\n  }\n  buildGameGroupsSelectOptions() {\n    this.gameGroupService.getGameGroupsList(this.entity.path).pipe(filter(data => !!data && data.length > 0), takeUntil(this.destroyed$)).subscribe(data => {\n      this.gameGroupsSelectOptions = data.map(gameGroup => new GameGroup(gameGroup).toSelectOption());\n      const defaultGameGroupId = this.gameGroupsSelectOptions.find(group => group.data)?.id;\n      if (defaultGameGroupId) {\n        this.defaultGameGroup.patchValue(defaultGameGroupId);\n      }\n    });\n  }\n  buildGamesSelectOptions() {\n    this.gameService.getAllGames(this.entity.path, false, true).pipe(takeUntil(this.destroyed$)).subscribe(data => {\n      this.gamesSelectOptions = data.map(game => new SelectOptionModel(game.code, game.title));\n      this.initialGamesSelectOptions = this.gamesSelectOptions;\n    });\n  }\n  initForm() {\n    this.form = this.fb.group({\n      gameGroup: [''],\n      game: ['']\n    });\n    this.gameGroupControl.valueChanges.pipe(filter(gameGroup => !!gameGroup), switchMap(gameGroup => this.gameGroupService.getGameGroupGames(this.entity.path, gameGroup)), takeUntil(this.destroyed$)).subscribe(gameCodes => this.filterGamesSelectOptions(gameCodes));\n    this.getLimits();\n  }\n  filterGamesSelectOptions(gameCodes) {\n    this.gamesSelectOptions = [];\n    if (!gameCodes) {\n      this.gameControl.setValue(null, {\n        emitEvent: false\n      });\n      this.gamesSelectOptions = this.initialGamesSelectOptions;\n      return;\n    }\n    if (gameCodes.length) {\n      let gameCodesArray = gameCodes.map(item => item.code);\n      this.gamesSelectOptions = this.initialGamesSelectOptions.filter(game => gameCodesArray.includes(game.id));\n    }\n  }\n  getLimits() {\n    this.form.valueChanges.pipe(tap(() => {\n      this.loading = true;\n      this.limits = [];\n    }), filter(val => !!val.gameGroup && !!val.game), map(data => {\n      return {\n        gameGroup: this.gameGroupsSelectOptions.find(group => group.id === data.gameGroup).text,\n        game: data.game\n      };\n    }), switchMap(params => this.gameGroupService.getGameLimits(this.entity.path, params.gameGroup, params.game)), map(data => data ? Object.entries(data).map(([currency, limit]) => {\n      return {\n        ...limit,\n        currency\n      };\n    }) : [])).subscribe(data => {\n      this.loading = !this.loading;\n      this.limits = data;\n    });\n  }\n  static {\n    this.ɵfac = function ShowLimitsModalComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ShowLimitsModalComponent)(i0.ɵɵdirectiveInject(i1.MatDialogRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.GameGroupService), i0.ɵɵdirectiveInject(i4.GameService), i0.ɵɵdirectiveInject(i5.SwuiNotificationsService), i0.ɵɵdirectiveInject(i6.TranslateService), i0.ɵɵdirectiveInject(i5.SwHubAuthService), i0.ɵɵdirectiveInject(i7.ExcelService), i0.ɵɵdirectiveInject(i8.EntitySettingsService), i0.ɵɵdirectiveInject(i9.FlatReportsService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ShowLimitsModalComponent,\n      selectors: [[\"show-limits-modal\"]],\n      decls: 11,\n      vars: 6,\n      consts: [[\"noLimitsAvailable\", \"\"], [\"tabindex\", \"-1\", \"mat-icon-button\", \"\", 1, \"close-button-x\", 3, \"click\"], [1, \"close-icon\"], [\"mat-dialog-title\", \"\"], [\"fxLayout\", \"row\", \"fxLayoutAlign\", \"end center\"], [\"mat-flat-button\", \"\", \"class\", \"mat-button-md export-button\", 3, \"click\", 4, \"ngIf\"], [4, \"ngIf\", \"ngIfElse\"], [1, \"block-message\"], [\"mat-flat-button\", \"\", 1, \"mat-button-md\", \"export-button\", 3, \"click\"], [\"fxLayout\", \"row\", \"fxLayoutAlign\", \"start center\"], [\"appearance\", \"outline\", 1, \"width100\"], [3, \"placeholder\", \"data\", \"showSearch\", \"disableEmptyOption\", \"disabled\", \"formControl\"], [1, \"margin-bottom24\", \"margin-left12\"], [\"mat-flat-button\", \"\", \"color\", \"primary\", 3, \"click\", \"disabled\"], [3, \"formGroup\"], [\"fxLayout\", \"row\"], [\"fxLayout\", \"row\", \"fxLayoutAlign\", \"start center\", 1, \"margin-left12\", \"margin-right24\", \"width100\"], [\"fxLayout\", \"row\", \"fxFlex\", \"50\", \"fxLayoutAlign\", \"start center\"], [3, \"placeholder\", \"data\", \"showSearch\", \"disableEmptyOption\", \"formControl\"], [\"fxLayout\", \"row\", \"fxFlex\", \"50\", \"fxLayoutAlign\", \"start center\", 1, \"margin-left24\"], [\"class\", \"example-container mat-elevation-z0\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"example-container\", \"mat-elevation-z0\"], [1, \"example-table-container\"], [\"mat-table\", \"\", \"matSort\", \"\", \"matSortActive\", \"created\", \"matSortDisableClear\", \"\", \"matSortDirection\", \"desc\", 1, \"example-table\", 3, \"dataSource\"], [\"matColumnDef\", \"currency\"], [\"mat-header-cell\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"stakeAll\"], [\"matColumnDef\", \"stakeMin\"], [\"matColumnDef\", \"stakeMax\"], [\"matColumnDef\", \"maxTotalStake\"], [\"matColumnDef\", \"stakeDef\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\", \"matHeaderRowDefSticky\"], [\"mat-row\", \"\", 4, \"matRowDef\", \"matRowDefColumns\"], [\"mat-header-cell\", \"\"], [\"mat-cell\", \"\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\"], [1, \"custom-error\", \"custom-error--warn\", \"margin-bottom16\"], [1, \"custom-error\", \"custom-error--warn\", \"margin-bottom32\"]],\n      template: function ShowLimitsModalComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"button\", 1);\n          i0.ɵɵlistener(\"click\", function ShowLimitsModalComponent_Template_button_click_0_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onNoClick());\n          });\n          i0.ɵɵelementStart(1, \"mat-icon\", 2);\n          i0.ɵɵtext(2, \"close\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(3, \"h2\", 3);\n          i0.ɵɵtext(4);\n          i0.ɵɵpipe(5, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 4);\n          i0.ɵɵtemplate(7, ShowLimitsModalComponent_button_7_Template, 3, 3, \"button\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(8, ShowLimitsModalComponent_div_8_Template, 31, 38, \"div\", 6)(9, ShowLimitsModalComponent_ng_template_9_Template, 3, 3, \"ng-template\", 7, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          const noLimitsAvailable_r11 = i0.ɵɵreference(10);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(5, 4, \"ENTITY_SETUP.GAME_LIMITS.showGameLimits\"), \"\\n\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.isExportLimitsAllowed && (ctx.entitySettings == null ? null : ctx.entitySettings.flatReportsEnabled));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.gameGroupsSelectOptions == null ? null : ctx.gameGroupsSelectOptions.length)(\"ngIfElse\", noLimitsAvailable_r11);\n        }\n      },\n      dependencies: [i10.NgIf, i2.ɵNgNoValidate, i2.NgControlStatus, i2.NgControlStatusGroup, i2.FormControlDirective, i2.FormGroupDirective, i11.MatButton, i11.MatIconButton, i12.MatIcon, i1.MatDialogTitle, i5.SwuiSelectComponent, i13.MatFormField, i13.MatLabel, i14.DefaultLayoutDirective, i14.DefaultLayoutAlignDirective, i14.DefaultFlexDirective, i15.MatTable, i15.MatHeaderCellDef, i15.MatHeaderRowDef, i15.MatColumnDef, i15.MatCellDef, i15.MatRowDef, i15.MatHeaderCell, i15.MatCell, i15.MatHeaderRow, i15.MatRow, i16.MatSort, i6.TranslatePipe],\n      styles: [\".example-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  min-height: 200px;\\n}\\n\\n.example-table-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  max-height: calc(70vh - 250px);\\n  overflow: auto;\\n}\\n\\ntable[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\ntable[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 10px;\\n}\\ntable[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  padding: 10px;\\n  white-space: normal;\\n}\\n\\n.example-loading-shade[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  bottom: 56px;\\n  right: 0;\\n  background: rgba(0, 0, 0, 0.15);\\n  z-index: 1;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.example-rate-limit-reached[_ngcontent-%COMP%] {\\n  color: #980000;\\n  max-width: 360px;\\n  text-align: center;\\n}\\n\\n\\n\\n.mat-column-number[_ngcontent-%COMP%], \\n.mat-column-state[_ngcontent-%COMP%] {\\n  max-width: 64px;\\n}\\n\\n.mat-column-created[_ngcontent-%COMP%] {\\n  max-width: 124px;\\n}\\n\\ntr[_ngcontent-%COMP%]:nth-child(even) {\\n  background-color: #f2f2f2;\\n}\\n\\n.block-message[_ngcontent-%COMP%] {\\n  display: block;\\n  width: 100%;\\n  text-align: center;\\n}\\n\\n.example-rate-limit-reached[_ngcontent-%COMP%] {\\n  color: #980000;\\n  max-width: 360px;\\n  text-align: center;\\n}\\n\\n\\n\\n.mat-column-number[_ngcontent-%COMP%], \\n.mat-column-state[_ngcontent-%COMP%] {\\n  max-width: 64px;\\n}\\n\\n.mat-column-created[_ngcontent-%COMP%] {\\n  max-width: 124px;\\n}\\n\\ntr[_ngcontent-%COMP%]:nth-child(even) {\\n  background-color: #f2f2f2;\\n}\\n\\n.block-message[_ngcontent-%COMP%] {\\n  display: block;\\n  width: 100%;\\n  text-align: center;\\n}\\n\\n.export-button[_ngcontent-%COMP%] {\\n  margin: -5px 0 5px 0;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["FormControl", "MAT_DIALOG_DATA", "Subject", "filter", "map", "switchMap", "takeUntil", "tap", "PERMISSIONS_LIST", "GameGroup", "SelectOptionModel", "GAME_GROUP_CUSTOMIZATION", "i0", "ɵɵelementStart", "ɵɵlistener", "ShowLimitsModalComponent_button_7_Template_button_click_0_listener", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "exportAsXLSX", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "ɵɵtextInterpolate", "row_r5", "currency", "joinStakeAll", "row_r6", "stakeAll", "row_r7", "stakeMin", "row_r8", "stakeMax", "row_r9", "maxTotalStake", "row_r10", "stakeDef", "ɵɵelement", "ɵɵelementContainerStart", "ɵɵtemplate", "ShowLimitsModalComponent_div_8_div_29_th_4_Template", "ShowLimitsModalComponent_div_8_div_29_td_5_Template", "ShowLimitsModalComponent_div_8_div_29_th_7_Template", "ShowLimitsModalComponent_div_8_div_29_td_8_Template", "ShowLimitsModalComponent_div_8_div_29_th_10_Template", "ShowLimitsModalComponent_div_8_div_29_td_11_Template", "ShowLimitsModalComponent_div_8_div_29_th_13_Template", "ShowLimitsModalComponent_div_8_div_29_td_14_Template", "ShowLimitsModalComponent_div_8_div_29_th_16_Template", "ShowLimitsModalComponent_div_8_div_29_td_17_Template", "ShowLimitsModalComponent_div_8_div_29_th_19_Template", "ShowLimitsModalComponent_div_8_div_29_td_20_Template", "ShowLimitsModalComponent_div_8_div_29_tr_21_Template", "ShowLimitsModalComponent_div_8_div_29_tr_22_Template", "ɵɵproperty", "limits", "displayedColumns", "ShowLimitsModalComponent_div_8_div_30_span_2_Template", "loading", "ShowLimitsModalComponent_div_8_Template_button_click_9_listener", "_r4", "submit", "ShowLimitsModalComponent_div_8_div_29_Template", "ShowLimitsModalComponent_div_8_div_30_Template", "gameGroupsSelectOptions", "isEditGameGroupAllowed", "defaultGameGroup", "form", "gameGroupControl", "gamesSelectOptions", "gameControl", "length", "ShowLimitsModalComponent", "constructor", "dialogRef", "initialState", "fb", "gameGroupService", "gameService", "notifications", "translate", "authService", "excelService", "entitySettingsService", "flatReportsService", "operatorColumn", "gameGroupColumn", "defaultGameGroupColumn", "gameColumn", "currencyColumn", "customizationColumn", "coinBets", "minCoinBet", "maxCoinBet", "maxTotalBet", "initialGamesSelectOptions", "isExportLimitsAllowed", "destroyed$", "entity", "allowedTo", "FLAT_REPORTS", "getSettings", "path", "pipe", "subscribe", "data", "entitySettings", "ngOnInit", "buildGameGroupsSelectOptions", "buildGamesSelectOptions", "initForm", "ngOnDestroy", "next", "complete", "get", "GAME_GROUP_EDIT", "join", "defaultGameGroupName", "find", "group", "id", "value", "text", "setDefaultGameGroup", "success", "instant", "gameGroup", "onNoClick", "close", "getTheLatestUpdatedFlatReport", "flatReport", "getAllGames", "games", "resultArray", "report", "Object", "entries", "gameCode", "gameGroups", "currencies", "row", "customization", "Array", "isArray", "limitsCustomizations", "name", "game", "code", "title", "push", "sort", "prev", "curr", "localeCompare", "exportAsExcelFile", "replace", "warning", "getGameGroupsList", "toSelectOption", "defaultGameGroupId", "patchValue", "valueChanges", "getGameGroupGames", "gameCodes", "filterGamesSelectOptions", "getLimits", "setValue", "emitEvent", "gameCodesArray", "item", "includes", "val", "params", "getGameLimits", "limit", "ɵɵdirectiveInject", "i1", "MatDialogRef", "i2", "FormBuilder", "i3", "GameGroupService", "i4", "GameService", "i5", "SwuiNotificationsService", "i6", "TranslateService", "SwHubAuthService", "i7", "ExcelService", "i8", "EntitySettingsService", "i9", "FlatReportsService", "selectors", "decls", "vars", "consts", "template", "ShowLimitsModalComponent_Template", "rf", "ctx", "ShowLimitsModalComponent_Template_button_click_0_listener", "_r1", "ShowLimitsModalComponent_button_7_Template", "ShowLimitsModalComponent_div_8_Template", "ShowLimitsModalComponent_ng_template_9_Template", "ɵɵtemplateRefExtractor", "flatReportsEnabled", "noLimitsAvailable_r11"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/mat-business-structure/dialogs/show-limits-modal/show-limits-modal.component.ts", "/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/mat-business-structure/dialogs/show-limits-modal/show-limits-modal.component.html"], "sourcesContent": ["import { Component, Inject, OnInit } from '@angular/core';\nimport { FormBuilder, FormControl, FormGroup } from '@angular/forms';\nimport { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';\nimport { TranslateService } from '@ngx-translate/core';\nimport { SwHubAuthService, SwuiNotificationsService } from '@skywind-group/lib-swui';\nimport { Subject } from 'rxjs';\nimport { filter, map, switchMap, takeUntil, tap } from 'rxjs/operators';\n\nimport { PERMISSIONS_LIST } from '../../../../../../app.constants';\nimport { EntitySettingsModel } from '../../../../../../common/models/entity-settings.model';\nimport { Entity } from '../../../../../../common/models/entity.model';\nimport { GameGroup } from '../../../../../../common/models/game-group.model';\nimport { SelectOptionModel } from '../../../../../../common/models/select-option.model';\nimport { EntitySettingsService } from '../../../../../../common/services/entity-settings.service';\nimport { ExcelService } from '../../../../../../common/services/excel.service';\nimport { FlatReportsService } from '../../../../../../common/services/flat-reports.service';\nimport { GameGroupService } from '../../../../../../common/services/game-group.service';\nimport { GameService } from '../../../../../../common/services/game.service';\nimport { Game } from '../../../../../../common/typings';\nimport { FlatReport, FlatReportGameGroupInfo, FlatReportGameInfo, FlatReportLimits, GAME_GROUP_CUSTOMIZATION } from '../../../../../../common/typings/flat-report';\nimport { StructureEntityModel } from '../../structure-entity.model';\n\n\n@Component({\n  selector: 'show-limits-modal',\n  templateUrl: './show-limits-modal.component.html',\n  styleUrls: ['./show-limits-modal.component.scss'],\n})\nexport class ShowLimitsModalComponent implements OnInit {\n\n  operatorColumn = 'Operator';\n  gameGroupColumn = 'Game Group';\n  defaultGameGroupColumn = 'Default Game Group';\n  gameColumn = 'Game';\n  currencyColumn = 'Currency';\n  customizationColumn = 'Default/Customized';\n  coinBets = 'Coin Bets';\n  minCoinBet = 'Min Coin Bet';\n  maxCoinBet = 'Max Coin Bet';\n  maxTotalBet = 'Max Total Bet';\n  stakeDef = 'Stake Def';\n\n  entitySettings: EntitySettingsModel;\n\n  displayedColumns: string[] = ['currency', 'stakeAll', 'stakeMin', 'stakeMax', 'maxTotalStake', 'stakeDef'];\n  entity: Entity;\n  limits: any[] = [];\n  gameGroupsSelectOptions: SelectOptionModel[] = [];\n  initialGamesSelectOptions: SelectOptionModel[] = [];\n  gamesSelectOptions: SelectOptionModel[] = [];\n\n  form: FormGroup;\n  defaultGameGroup = new FormControl('');\n\n  loading = false;\n\n  isExportLimitsAllowed = false;\n\n  private readonly destroyed$ = new Subject<void>();\n\n  constructor(public dialogRef: MatDialogRef<ShowLimitsModalComponent>,\n    @Inject(MAT_DIALOG_DATA) public initialState: StructureEntityModel,\n    private fb: FormBuilder,\n    private gameGroupService: GameGroupService,\n    private gameService: GameService,\n    private notifications: SwuiNotificationsService,\n    private translate: TranslateService,\n    private authService: SwHubAuthService,\n    private excelService: ExcelService,\n    private entitySettingsService: EntitySettingsService<EntitySettingsModel>,\n    private flatReportsService: FlatReportsService,\n  ) {\n    this.entity = this.initialState;\n\n    this.isExportLimitsAllowed = this.authService.allowedTo(PERMISSIONS_LIST.FLAT_REPORTS);\n\n    this.entitySettingsService.getSettings(this.entity.path)\n      .pipe(takeUntil(this.destroyed$))\n      .subscribe((data: EntitySettingsModel) => this.entitySettings = data);\n  }\n\n  ngOnInit() {\n    this.buildGameGroupsSelectOptions();\n    this.buildGamesSelectOptions();\n    this.initForm();\n  }\n\n  ngOnDestroy(): void {\n    this.destroyed$.next();\n    this.destroyed$.complete();\n  }\n\n  get gameGroupControl(): FormControl {\n    return this.form.get('gameGroup') as FormControl;\n  }\n\n  get gameControl(): FormControl {\n    return this.form.get('game') as FormControl;\n  }\n\n  isEditGameGroupAllowed(): boolean {\n    return this.authService.allowedTo(PERMISSIONS_LIST.GAME_GROUP_EDIT);\n  }\n\n  joinStakeAll(stakeAll: number[]): string {\n    return stakeAll.join(', ');\n  }\n\n  submit() {\n    const defaultGameGroupName = this.gameGroupsSelectOptions.find(group => group.id === this.defaultGameGroup.value).text;\n    this.gameGroupService.setDefaultGameGroup(this.entity.path, defaultGameGroupName).subscribe(\n      () => {\n        this.notifications.success(\n          this.translate.instant('ENTITY_SETUP.GAME_GROUP.notificationDefaultGameGroupChanged',\n            { gameGroup: this.defaultGameGroup.value })\n        );\n      }\n    );\n  }\n\n  onNoClick() {\n    this.dialogRef.close(null);\n  }\n\n  exportAsXLSX() {\n    this.flatReportsService.getTheLatestUpdatedFlatReport(this.entity.path)\n      .pipe(\n        switchMap((flatReport: FlatReport) => {\n          return this.gameService.getAllGames(this.entity.path, false, true)\n            .pipe(\n              map((games: Game[]) => ({ flatReport, games })),\n            );\n        }),\n        takeUntil(this.destroyed$),\n      )\n      .subscribe(({ flatReport, games }) => {\n        if (flatReport) {\n          const resultArray = [];\n          const report = flatReport.report;\n\n          Object.entries(report)?.map(([gameCode, gameGroups]: [string, FlatReportGameInfo]) => {\n            Object.entries(gameGroups)?.map(([gameGroup, currencies]: [string, FlatReportGameGroupInfo]) => {\n              Object.entries(currencies)?.map(([currency, limits]: [string, FlatReportLimits]) => {\n                let row = {};\n\n                let customization = 'Default';\n                if (Array.isArray(limits?.limitsCustomizations) && limits?.limitsCustomizations.length) {\n                  if (limits?.limitsCustomizations.length === 1) {\n                    customization = limits?.limitsCustomizations[0] === GAME_GROUP_CUSTOMIZATION ?\n                      'Customized by game group' :\n                      'Customized by filters';\n                  } else {\n                    customization = 'Customized by game group and filters';\n                  }\n                }\n\n                row[this.operatorColumn] = this.entity.name;\n                row[this.gameGroupColumn] = gameGroup;\n                row[this.defaultGameGroupColumn] = gameGroup === this.defaultGameGroup.value ? 'Yes' : 'No';\n                row[this.gameColumn] = games?.find((game: Game) => game.code === gameCode)?.title;\n                row[this.currencyColumn] = currency;\n                row[this.customizationColumn] = customization;\n                row[this.coinBets] = limits?.limits?.stakeAll.join(', ');\n                row[this.minCoinBet] = limits?.limits?.stakeMin;\n                row[this.maxCoinBet] = limits?.limits?.stakeMax;\n                row[this.maxTotalBet] = limits?.limits?.maxTotalStake;\n                row[this.stakeDef] = limits?.limits?.stakeDef;\n\n                resultArray.push(row);\n              });\n            });\n          });\n\n          resultArray.sort((prev, curr) => prev[this.gameGroupColumn].localeCompare(curr[this.gameGroupColumn]));\n\n          this.excelService.exportAsExcelFile(resultArray, this.entity.path.replace(':', '') + '_limits');\n\n          this.notifications.success(this.translate.instant('ENTITY_SETUP.GAME_LIMITS.flatReportWasExported'));\n        } else {\n          this.notifications.warning(this.translate.instant('ENTITY_SETUP.GAME_LIMITS.flatReportNotFound'));\n        }\n      });\n  }\n\n  private buildGameGroupsSelectOptions() {\n    this.gameGroupService.getGameGroupsList(this.entity.path)\n      .pipe(\n        filter(data => !!data && data.length > 0),\n        takeUntil(this.destroyed$)\n      ).subscribe(\n        data => {\n          this.gameGroupsSelectOptions = data.map((gameGroup: GameGroup) => new GameGroup(gameGroup).toSelectOption());\n          const defaultGameGroupId = this.gameGroupsSelectOptions.find(group => group.data)?.id;\n          if (defaultGameGroupId) {\n            this.defaultGameGroup.patchValue(defaultGameGroupId);\n          }\n        }\n      );\n  }\n\n  private buildGamesSelectOptions() {\n    this.gameService.getAllGames(this.entity.path, false, true).pipe(\n      takeUntil(this.destroyed$)\n    ).subscribe(data => {\n      this.gamesSelectOptions = data.map(game => new SelectOptionModel(game.code, game.title));\n      this.initialGamesSelectOptions = this.gamesSelectOptions;\n    });\n  }\n\n  private initForm() {\n    this.form = this.fb.group({\n      gameGroup: [''],\n      game: [''],\n    });\n\n    this.gameGroupControl.valueChanges\n      .pipe(\n        filter(gameGroup => !!gameGroup),\n        switchMap(gameGroup => this.gameGroupService.getGameGroupGames(this.entity.path, gameGroup)),\n        takeUntil(this.destroyed$)\n      ).subscribe(gameCodes => this.filterGamesSelectOptions(gameCodes));\n\n    this.getLimits();\n  }\n\n  private filterGamesSelectOptions(gameCodes: any[]) {\n    this.gamesSelectOptions = [];\n\n    if (!gameCodes) {\n      this.gameControl.setValue(null, { emitEvent: false });\n      this.gamesSelectOptions = this.initialGamesSelectOptions;\n\n      return;\n    }\n\n    if (gameCodes.length) {\n      let gameCodesArray = gameCodes.map(item => item.code);\n      this.gamesSelectOptions = this.initialGamesSelectOptions.filter(\n        game => gameCodesArray.includes(game.id));\n    }\n  }\n\n  private getLimits() {\n    this.form.valueChanges.pipe(\n      tap(() => {\n        this.loading = true;\n        this.limits = [];\n      }),\n      filter(val => !!val.gameGroup && !!val.game),\n      map(data => {\n        return {\n          gameGroup: this.gameGroupsSelectOptions.find(group => group.id === data.gameGroup).text,\n          game: data.game\n        };\n      }),\n      switchMap(params => this.gameGroupService.getGameLimits(this.entity.path, params.gameGroup, params.game)),\n      map(data => data ? Object.entries(data).map(([currency, limit]) => {\n        return ({ ...limit as object, currency });\n      }) : []),\n    ).subscribe(data => {\n      this.loading = !this.loading;\n      this.limits = data;\n    });\n  }\n}\n", "<button tabindex=\"-1\" mat-icon-button class=\"close-button-x\" (click)=\"onNoClick()\">\n  <mat-icon class=\"close-icon\">close</mat-icon>\n</button>\n<h2 mat-dialog-title>\n  {{ 'ENTITY_SETUP.GAME_LIMITS.showGameLimits' | translate }}\n</h2>\n\n<div fxLayout=\"row\" fxLayoutAlign=\"end center\">\n  <button *ngIf=\"isExportLimitsAllowed && entitySettings?.flatReportsEnabled\"\n          mat-flat-button\n          class=\"mat-button-md export-button\"\n          (click)=\"exportAsXLSX()\">\n    {{ 'ENTITY_SETUP.GAME_LIMITS.exportToExcel' | translate }}\n  </button>\n</div>\n\n<div *ngIf=\"gameGroupsSelectOptions?.length; else noLimitsAvailable\">\n  <div fxLayout=\"row\" fxLayoutAlign=\"start center\">\n    <mat-form-field appearance=\"outline\" class=\"width100\">\n      <mat-label>{{'ENTITY_SETUP.GAME_LIMITS.defaultGameGroupLabel' | translate}}</mat-label>\n      <lib-swui-select\n        [placeholder]=\"'ENTITY_SETUP.GAME_LIMITS.defaultGameGroupLabel' | translate\"\n        [data]=\"gameGroupsSelectOptions\"\n        [showSearch]=\"true\"\n        [disableEmptyOption]=\"true\"\n        [disabled]=\"!isEditGameGroupAllowed()\"\n        [formControl]=\"defaultGameGroup\">\n      </lib-swui-select>\n    </mat-form-field>\n    <div class=\"margin-bottom24 margin-left12\">\n      <button mat-flat-button color=\"primary\"\n              (click)=\"submit()\"\n              [disabled]=\"!isEditGameGroupAllowed()\">\n        {{ 'DIALOG.save' | translate }}\n      </button>\n    </div>\n  </div>\n\n  <form [formGroup]=\"form\">\n    <div fxLayout=\"row\">\n      <div fxLayout=\"row\" fxLayoutAlign=\"start center\" class=\"margin-left12 margin-right24 width100\">\n        <div fxLayout=\"row\" fxFlex=\"50\" fxLayoutAlign=\"start center\">\n          <mat-form-field appearance=\"outline\" class=\"width100\">\n            <mat-label>{{ 'ENTITY_SETUP.GAME_LIMITS.gameGroupLabel' | translate }}</mat-label>\n            <lib-swui-select\n              [placeholder]=\"'ENTITY_SETUP.GAME_LIMITS.gameGroupLabel' | translate\"\n              [data]=\"gameGroupsSelectOptions\"\n              [showSearch]=\"true\"\n              [disableEmptyOption]=\"true\"\n              [formControl]=\"gameGroupControl\">\n            </lib-swui-select>\n          </mat-form-field>\n        </div>\n        <div fxLayout=\"row\" fxFlex=\"50\" fxLayoutAlign=\"start center\" class=\"margin-left24\">\n          <mat-form-field appearance=\"outline\" class=\"width100\">\n            <mat-label>{{ 'ENTITY_SETUP.GAME_LIMITS.gameLabel' | translate }}</mat-label>\n            <lib-swui-select\n              [placeholder]=\"'ENTITY_SETUP.GAME_LIMITS.gameLabel' | translate\"\n              [data]=\"gamesSelectOptions\"\n              [showSearch]=\"true\"\n              [disableEmptyOption]=\"true\"\n              [formControl]=\"gameControl\">\n            </lib-swui-select>\n          </mat-form-field>\n        </div>\n      </div>\n    </div>\n  </form>\n\n  <div class=\"example-container mat-elevation-z0\" *ngIf=\"limits?.length;\">\n    <div class=\"example-table-container\">\n      <table mat-table [dataSource]=\"limits\" class=\"example-table\"\n             matSort matSortActive=\"created\" matSortDisableClear matSortDirection=\"desc\">\n\n        <ng-container matColumnDef=\"currency\">\n          <th mat-header-cell *matHeaderCellDef>{{ 'ENTITY_SETUP.GAME_LIMITS.TABLE.currency' | translate }}</th>\n          <td mat-cell *matCellDef=\"let row\">{{row?.currency}}</td>\n        </ng-container>\n        <ng-container matColumnDef=\"stakeAll\">\n          <th mat-header-cell *matHeaderCellDef>{{ 'ENTITY_SETUP.GAME_LIMITS.TABLE.coinBets' | translate }}</th>\n          <td mat-cell *matCellDef=\"let row\">{{joinStakeAll(row?.stakeAll)}}</td>\n        </ng-container>\n        <ng-container matColumnDef=\"stakeMin\">\n          <th mat-header-cell *matHeaderCellDef>{{ 'ENTITY_SETUP.GAME_LIMITS.TABLE.minCoinBet' | translate }}</th>\n          <td mat-cell *matCellDef=\"let row\">{{row?.stakeMin}}</td>\n        </ng-container>\n        <ng-container matColumnDef=\"stakeMax\">\n          <th mat-header-cell *matHeaderCellDef>{{ 'ENTITY_SETUP.GAME_LIMITS.TABLE.maxCoinBet' | translate }}</th>\n          <td mat-cell *matCellDef=\"let row\">{{row?.stakeMax}}</td>\n        </ng-container>\n        <ng-container matColumnDef=\"maxTotalStake\">\n          <th mat-header-cell *matHeaderCellDef>{{ 'ENTITY_SETUP.GAME_LIMITS.TABLE.maxTotalBet' | translate }}</th>\n          <td mat-cell *matCellDef=\"let row\">{{row?.maxTotalStake}}</td>\n        </ng-container>\n        <ng-container matColumnDef=\"stakeDef\">\n          <th mat-header-cell *matHeaderCellDef>{{ 'ENTITY_SETUP.GAME_LIMITS.TABLE.stakeDef' | translate }}</th>\n          <td mat-cell *matCellDef=\"let row\">{{row?.stakeDef}}</td>\n        </ng-container>\n\n        <tr mat-header-row *matHeaderRowDef=\"displayedColumns; sticky: true\"></tr>\n        <tr mat-row *matRowDef=\"let row; columns: displayedColumns;\"></tr>\n      </table>\n    </div>\n  </div>\n  <div *ngIf=\"!limits?.length && !loading\">\n    <div class=\"custom-error custom-error--warn margin-bottom16\">\n      <span *ngIf=\"!loading\">{{ 'ENTITY_SETUP.GAME_LIMITS.noLimitsFound' | translate }}</span>\n    </div>\n  </div>\n</div>\n\n<ng-template #noLimitsAvailable class=\"block-message\">\n  <div class=\"custom-error custom-error--warn margin-bottom32\">\n    {{ 'ENTITY_SETUP.GAME_LIMITS.noLimitsAvailable' | translate }}\n  </div>\n</ng-template>\n"], "mappings": "AACA,SAAsBA,WAAW,QAAmB,gBAAgB;AACpE,SAASC,eAAe,QAAsB,0BAA0B;AAGxE,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,MAAM,EAAEC,GAAG,EAAEC,SAAS,EAAEC,SAAS,EAAEC,GAAG,QAAQ,gBAAgB;AAEvE,SAASC,gBAAgB,QAAQ,iCAAiC;AAGlE,SAASC,SAAS,QAAQ,kDAAkD;AAC5E,SAASC,iBAAiB,QAAQ,qDAAqD;AAOvF,SAAoFC,wBAAwB,QAAQ,8CAA8C;;;;;;;;;;;;;;;;;;;;;;ICXhKC,EAAA,CAAAC,cAAA,gBAGiC;IAAzBD,EAAA,CAAAE,UAAA,mBAAAC,mEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,YAAA,EAAc;IAAA,EAAC;IAC9BT,EAAA,CAAAU,MAAA,GACF;;IAAAV,EAAA,CAAAW,YAAA,EAAS;;;IADPX,EAAA,CAAAY,SAAA,EACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAb,EAAA,CAAAc,WAAA,sDACF;;;;;IA8DQd,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAU,MAAA,GAA2D;;IAAAV,EAAA,CAAAW,YAAA,EAAK;;;IAAhEX,EAAA,CAAAY,SAAA,EAA2D;IAA3DZ,EAAA,CAAAe,iBAAA,CAAAf,EAAA,CAAAc,WAAA,kDAA2D;;;;;IACjGd,EAAA,CAAAC,cAAA,aAAmC;IAAAD,EAAA,CAAAU,MAAA,GAAiB;IAAAV,EAAA,CAAAW,YAAA,EAAK;;;;IAAtBX,EAAA,CAAAY,SAAA,EAAiB;IAAjBZ,EAAA,CAAAe,iBAAA,CAAAC,MAAA,kBAAAA,MAAA,CAAAC,QAAA,CAAiB;;;;;IAGpDjB,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAU,MAAA,GAA2D;;IAAAV,EAAA,CAAAW,YAAA,EAAK;;;IAAhEX,EAAA,CAAAY,SAAA,EAA2D;IAA3DZ,EAAA,CAAAe,iBAAA,CAAAf,EAAA,CAAAc,WAAA,kDAA2D;;;;;IACjGd,EAAA,CAAAC,cAAA,aAAmC;IAAAD,EAAA,CAAAU,MAAA,GAA+B;IAAAV,EAAA,CAAAW,YAAA,EAAK;;;;;IAApCX,EAAA,CAAAY,SAAA,EAA+B;IAA/BZ,EAAA,CAAAe,iBAAA,CAAAT,MAAA,CAAAY,YAAA,CAAAC,MAAA,kBAAAA,MAAA,CAAAC,QAAA,EAA+B;;;;;IAGlEpB,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAU,MAAA,GAA6D;;IAAAV,EAAA,CAAAW,YAAA,EAAK;;;IAAlEX,EAAA,CAAAY,SAAA,EAA6D;IAA7DZ,EAAA,CAAAe,iBAAA,CAAAf,EAAA,CAAAc,WAAA,oDAA6D;;;;;IACnGd,EAAA,CAAAC,cAAA,aAAmC;IAAAD,EAAA,CAAAU,MAAA,GAAiB;IAAAV,EAAA,CAAAW,YAAA,EAAK;;;;IAAtBX,EAAA,CAAAY,SAAA,EAAiB;IAAjBZ,EAAA,CAAAe,iBAAA,CAAAM,MAAA,kBAAAA,MAAA,CAAAC,QAAA,CAAiB;;;;;IAGpDtB,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAU,MAAA,GAA6D;;IAAAV,EAAA,CAAAW,YAAA,EAAK;;;IAAlEX,EAAA,CAAAY,SAAA,EAA6D;IAA7DZ,EAAA,CAAAe,iBAAA,CAAAf,EAAA,CAAAc,WAAA,oDAA6D;;;;;IACnGd,EAAA,CAAAC,cAAA,aAAmC;IAAAD,EAAA,CAAAU,MAAA,GAAiB;IAAAV,EAAA,CAAAW,YAAA,EAAK;;;;IAAtBX,EAAA,CAAAY,SAAA,EAAiB;IAAjBZ,EAAA,CAAAe,iBAAA,CAAAQ,MAAA,kBAAAA,MAAA,CAAAC,QAAA,CAAiB;;;;;IAGpDxB,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAU,MAAA,GAA8D;;IAAAV,EAAA,CAAAW,YAAA,EAAK;;;IAAnEX,EAAA,CAAAY,SAAA,EAA8D;IAA9DZ,EAAA,CAAAe,iBAAA,CAAAf,EAAA,CAAAc,WAAA,qDAA8D;;;;;IACpGd,EAAA,CAAAC,cAAA,aAAmC;IAAAD,EAAA,CAAAU,MAAA,GAAsB;IAAAV,EAAA,CAAAW,YAAA,EAAK;;;;IAA3BX,EAAA,CAAAY,SAAA,EAAsB;IAAtBZ,EAAA,CAAAe,iBAAA,CAAAU,MAAA,kBAAAA,MAAA,CAAAC,aAAA,CAAsB;;;;;IAGzD1B,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAU,MAAA,GAA2D;;IAAAV,EAAA,CAAAW,YAAA,EAAK;;;IAAhEX,EAAA,CAAAY,SAAA,EAA2D;IAA3DZ,EAAA,CAAAe,iBAAA,CAAAf,EAAA,CAAAc,WAAA,kDAA2D;;;;;IACjGd,EAAA,CAAAC,cAAA,aAAmC;IAAAD,EAAA,CAAAU,MAAA,GAAiB;IAAAV,EAAA,CAAAW,YAAA,EAAK;;;;IAAtBX,EAAA,CAAAY,SAAA,EAAiB;IAAjBZ,EAAA,CAAAe,iBAAA,CAAAY,OAAA,kBAAAA,OAAA,CAAAC,QAAA,CAAiB;;;;;IAGtD5B,EAAA,CAAA6B,SAAA,aAA0E;;;;;IAC1E7B,EAAA,CAAA6B,SAAA,aAAkE;;;;;IA7BpE7B,EAFJ,CAAAC,cAAA,cAAwE,cACjC,gBAEgD;IAEjFD,EAAA,CAAA8B,uBAAA,OAAsC;IAEpC9B,EADA,CAAA+B,UAAA,IAAAC,mDAAA,iBAAsC,IAAAC,mDAAA,iBACH;;IAErCjC,EAAA,CAAA8B,uBAAA,OAAsC;IAEpC9B,EADA,CAAA+B,UAAA,IAAAG,mDAAA,iBAAsC,IAAAC,mDAAA,iBACH;;IAErCnC,EAAA,CAAA8B,uBAAA,OAAsC;IAEpC9B,EADA,CAAA+B,UAAA,KAAAK,oDAAA,iBAAsC,KAAAC,oDAAA,iBACH;;IAErCrC,EAAA,CAAA8B,uBAAA,QAAsC;IAEpC9B,EADA,CAAA+B,UAAA,KAAAO,oDAAA,iBAAsC,KAAAC,oDAAA,iBACH;;IAErCvC,EAAA,CAAA8B,uBAAA,QAA2C;IAEzC9B,EADA,CAAA+B,UAAA,KAAAS,oDAAA,iBAAsC,KAAAC,oDAAA,iBACH;;IAErCzC,EAAA,CAAA8B,uBAAA,QAAsC;IAEpC9B,EADA,CAAA+B,UAAA,KAAAW,oDAAA,iBAAsC,KAAAC,oDAAA,iBACH;;IAIrC3C,EADA,CAAA+B,UAAA,KAAAa,oDAAA,iBAAqE,KAAAC,oDAAA,iBACR;IAGnE7C,EAFI,CAAAW,YAAA,EAAQ,EACJ,EACF;;;;IAhCeX,EAAA,CAAAY,SAAA,GAAqB;IAArBZ,EAAA,CAAA8C,UAAA,eAAAxC,MAAA,CAAAyC,MAAA,CAAqB;IA4BhB/C,EAAA,CAAAY,SAAA,IAAmC;IAAAZ,EAAnC,CAAA8C,UAAA,oBAAAxC,MAAA,CAAA0C,gBAAA,CAAmC,+BAAY;IAClChD,EAAA,CAAAY,SAAA,EAA0B;IAA1BZ,EAAA,CAAA8C,UAAA,qBAAAxC,MAAA,CAAA0C,gBAAA,CAA0B;;;;;IAM7DhD,EAAA,CAAAC,cAAA,WAAuB;IAAAD,EAAA,CAAAU,MAAA,GAA0D;;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;IAAjEX,EAAA,CAAAY,SAAA,EAA0D;IAA1DZ,EAAA,CAAAe,iBAAA,CAAAf,EAAA,CAAAc,WAAA,iDAA0D;;;;;IADnFd,EADF,CAAAC,cAAA,UAAyC,cACsB;IAC3DD,EAAA,CAAA+B,UAAA,IAAAkB,qDAAA,mBAAuB;IAE3BjD,EADE,CAAAW,YAAA,EAAM,EACF;;;;IAFKX,EAAA,CAAAY,SAAA,GAAc;IAAdZ,EAAA,CAAA8C,UAAA,UAAAxC,MAAA,CAAA4C,OAAA,CAAc;;;;;;IAvFrBlD,EAHN,CAAAC,cAAA,UAAqE,aAClB,yBACO,gBACzC;IAAAD,EAAA,CAAAU,MAAA,GAAgE;;IAAAV,EAAA,CAAAW,YAAA,EAAY;IACvFX,EAAA,CAAA6B,SAAA,0BAOkB;;IACpB7B,EAAA,CAAAW,YAAA,EAAiB;IAEfX,EADF,CAAAC,cAAA,cAA2C,iBAGM;IADvCD,EAAA,CAAAE,UAAA,mBAAAiD,gEAAA;MAAAnD,EAAA,CAAAI,aAAA,CAAAgD,GAAA;MAAA,MAAA9C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA+C,MAAA,EAAQ;IAAA,EAAC;IAExBrD,EAAA,CAAAU,MAAA,IACF;;IAEJV,EAFI,CAAAW,YAAA,EAAS,EACL,EACF;IAOIX,EALV,CAAAC,cAAA,gBAAyB,eACH,eAC6E,eAChC,0BACL,iBACzC;IAAAD,EAAA,CAAAU,MAAA,IAA2D;;IAAAV,EAAA,CAAAW,YAAA,EAAY;IAClFX,EAAA,CAAA6B,SAAA,2BAMkB;;IAEtB7B,EADE,CAAAW,YAAA,EAAiB,EACb;IAGFX,EAFJ,CAAAC,cAAA,eAAmF,0BAC3B,iBACzC;IAAAD,EAAA,CAAAU,MAAA,IAAsD;;IAAAV,EAAA,CAAAW,YAAA,EAAY;IAC7EX,EAAA,CAAA6B,SAAA,2BAMkB;;IAK5B7B,EAJQ,CAAAW,YAAA,EAAiB,EACb,EACF,EACF,EACD;IAqCPX,EAnCA,CAAA+B,UAAA,KAAAuB,8CAAA,mBAAwE,KAAAC,8CAAA,kBAmC/B;IAK3CvD,EAAA,CAAAW,YAAA,EAAM;;;;IA1FWX,EAAA,CAAAY,SAAA,GAAgE;IAAhEZ,EAAA,CAAAe,iBAAA,CAAAf,EAAA,CAAAc,WAAA,0DAAgE;IAEzEd,EAAA,CAAAY,SAAA,GAA4E;IAK5EZ,EALA,CAAA8C,UAAA,gBAAA9C,EAAA,CAAAc,WAAA,0DAA4E,SAAAR,MAAA,CAAAkD,uBAAA,CAC5C,oBACb,4BACQ,cAAAlD,MAAA,CAAAmD,sBAAA,GACW,gBAAAnD,MAAA,CAAAoD,gBAAA,CACN;IAM1B1D,EAAA,CAAAY,SAAA,GAAsC;IAAtCZ,EAAA,CAAA8C,UAAA,cAAAxC,MAAA,CAAAmD,sBAAA,GAAsC;IAC5CzD,EAAA,CAAAY,SAAA,EACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAb,EAAA,CAAAc,WAAA,6BACF;IAIEd,EAAA,CAAAY,SAAA,GAAkB;IAAlBZ,EAAA,CAAA8C,UAAA,cAAAxC,MAAA,CAAAqD,IAAA,CAAkB;IAKH3D,EAAA,CAAAY,SAAA,GAA2D;IAA3DZ,EAAA,CAAAe,iBAAA,CAAAf,EAAA,CAAAc,WAAA,oDAA2D;IAEpEd,EAAA,CAAAY,SAAA,GAAqE;IAIrEZ,EAJA,CAAA8C,UAAA,gBAAA9C,EAAA,CAAAc,WAAA,oDAAqE,SAAAR,MAAA,CAAAkD,uBAAA,CACrC,oBACb,4BACQ,gBAAAlD,MAAA,CAAAsD,gBAAA,CACK;IAMvB5D,EAAA,CAAAY,SAAA,GAAsD;IAAtDZ,EAAA,CAAAe,iBAAA,CAAAf,EAAA,CAAAc,WAAA,+CAAsD;IAE/Dd,EAAA,CAAAY,SAAA,GAAgE;IAIhEZ,EAJA,CAAA8C,UAAA,gBAAA9C,EAAA,CAAAc,WAAA,+CAAgE,SAAAR,MAAA,CAAAuD,kBAAA,CACrC,oBACR,4BACQ,gBAAAvD,MAAA,CAAAwD,WAAA,CACA;IAQU9D,EAAA,CAAAY,SAAA,GAAqB;IAArBZ,EAAA,CAAA8C,UAAA,SAAAxC,MAAA,CAAAyC,MAAA,kBAAAzC,MAAA,CAAAyC,MAAA,CAAAgB,MAAA,CAAqB;IAmChE/D,EAAA,CAAAY,SAAA,EAAiC;IAAjCZ,EAAA,CAAA8C,UAAA,WAAAxC,MAAA,CAAAyC,MAAA,kBAAAzC,MAAA,CAAAyC,MAAA,CAAAgB,MAAA,MAAAzD,MAAA,CAAA4C,OAAA,CAAiC;;;;;IAQvClD,EAAA,CAAAC,cAAA,cAA6D;IAC3DD,EAAA,CAAAU,MAAA,GACF;;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;IADJX,EAAA,CAAAY,SAAA,EACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAb,EAAA,CAAAc,WAAA,0DACF;;;ADtFF,OAAM,MAAOkD,wBAAwB;EAgCnCC,YAAmBC,SAAiD,EAClCC,YAAkC,EAC1DC,EAAe,EACfC,gBAAkC,EAClCC,WAAwB,EACxBC,aAAuC,EACvCC,SAA2B,EAC3BC,WAA6B,EAC7BC,YAA0B,EAC1BC,qBAAiE,EACjEC,kBAAsC;IAV7B,KAAAV,SAAS,GAATA,SAAS;IACM,KAAAC,YAAY,GAAZA,YAAY;IACpC,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAxC5B,KAAAC,cAAc,GAAG,UAAU;IAC3B,KAAAC,eAAe,GAAG,YAAY;IAC9B,KAAAC,sBAAsB,GAAG,oBAAoB;IAC7C,KAAAC,UAAU,GAAG,MAAM;IACnB,KAAAC,cAAc,GAAG,UAAU;IAC3B,KAAAC,mBAAmB,GAAG,oBAAoB;IAC1C,KAAAC,QAAQ,GAAG,WAAW;IACtB,KAAAC,UAAU,GAAG,cAAc;IAC3B,KAAAC,UAAU,GAAG,cAAc;IAC3B,KAAAC,WAAW,GAAG,eAAe;IAC7B,KAAA1D,QAAQ,GAAG,WAAW;IAItB,KAAAoB,gBAAgB,GAAa,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,eAAe,EAAE,UAAU,CAAC;IAE1G,KAAAD,MAAM,GAAU,EAAE;IAClB,KAAAS,uBAAuB,GAAwB,EAAE;IACjD,KAAA+B,yBAAyB,GAAwB,EAAE;IACnD,KAAA1B,kBAAkB,GAAwB,EAAE;IAG5C,KAAAH,gBAAgB,GAAG,IAAItE,WAAW,CAAC,EAAE,CAAC;IAEtC,KAAA8D,OAAO,GAAG,KAAK;IAEf,KAAAsC,qBAAqB,GAAG,KAAK;IAEZ,KAAAC,UAAU,GAAG,IAAInG,OAAO,EAAQ;IAc/C,IAAI,CAACoG,MAAM,GAAG,IAAI,CAACvB,YAAY;IAE/B,IAAI,CAACqB,qBAAqB,GAAG,IAAI,CAACf,WAAW,CAACkB,SAAS,CAAC/F,gBAAgB,CAACgG,YAAY,CAAC;IAEtF,IAAI,CAACjB,qBAAqB,CAACkB,WAAW,CAAC,IAAI,CAACH,MAAM,CAACI,IAAI,CAAC,CACrDC,IAAI,CAACrG,SAAS,CAAC,IAAI,CAAC+F,UAAU,CAAC,CAAC,CAChCO,SAAS,CAAEC,IAAyB,IAAK,IAAI,CAACC,cAAc,GAAGD,IAAI,CAAC;EACzE;EAEAE,QAAQA,CAAA;IACN,IAAI,CAACC,4BAA4B,EAAE;IACnC,IAAI,CAACC,uBAAuB,EAAE;IAC9B,IAAI,CAACC,QAAQ,EAAE;EACjB;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACd,UAAU,CAACe,IAAI,EAAE;IACtB,IAAI,CAACf,UAAU,CAACgB,QAAQ,EAAE;EAC5B;EAEA,IAAI7C,gBAAgBA,CAAA;IAClB,OAAO,IAAI,CAACD,IAAI,CAAC+C,GAAG,CAAC,WAAW,CAAgB;EAClD;EAEA,IAAI5C,WAAWA,CAAA;IACb,OAAO,IAAI,CAACH,IAAI,CAAC+C,GAAG,CAAC,MAAM,CAAgB;EAC7C;EAEAjD,sBAAsBA,CAAA;IACpB,OAAO,IAAI,CAACgB,WAAW,CAACkB,SAAS,CAAC/F,gBAAgB,CAAC+G,eAAe,CAAC;EACrE;EAEAzF,YAAYA,CAACE,QAAkB;IAC7B,OAAOA,QAAQ,CAACwF,IAAI,CAAC,IAAI,CAAC;EAC5B;EAEAvD,MAAMA,CAAA;IACJ,MAAMwD,oBAAoB,GAAG,IAAI,CAACrD,uBAAuB,CAACsD,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACC,EAAE,KAAK,IAAI,CAACtD,gBAAgB,CAACuD,KAAK,CAAC,CAACC,IAAI;IACtH,IAAI,CAAC7C,gBAAgB,CAAC8C,mBAAmB,CAAC,IAAI,CAACzB,MAAM,CAACI,IAAI,EAAEe,oBAAoB,CAAC,CAACb,SAAS,CACzF,MAAK;MACH,IAAI,CAACzB,aAAa,CAAC6C,OAAO,CACxB,IAAI,CAAC5C,SAAS,CAAC6C,OAAO,CAAC,6DAA6D,EAClF;QAAEC,SAAS,EAAE,IAAI,CAAC5D,gBAAgB,CAACuD;MAAK,CAAE,CAAC,CAC9C;IACH,CAAC,CACF;EACH;EAEAM,SAASA,CAAA;IACP,IAAI,CAACrD,SAAS,CAACsD,KAAK,CAAC,IAAI,CAAC;EAC5B;EAEA/G,YAAYA,CAAA;IACV,IAAI,CAACmE,kBAAkB,CAAC6C,6BAA6B,CAAC,IAAI,CAAC/B,MAAM,CAACI,IAAI,CAAC,CACpEC,IAAI,CACHtG,SAAS,CAAEiI,UAAsB,IAAI;MACnC,OAAO,IAAI,CAACpD,WAAW,CAACqD,WAAW,CAAC,IAAI,CAACjC,MAAM,CAACI,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAC/DC,IAAI,CACHvG,GAAG,CAAEoI,KAAa,KAAM;QAAEF,UAAU;QAAEE;MAAK,CAAE,CAAC,CAAC,CAChD;IACL,CAAC,CAAC,EACFlI,SAAS,CAAC,IAAI,CAAC+F,UAAU,CAAC,CAC3B,CACAO,SAAS,CAAC,CAAC;MAAE0B,UAAU;MAAEE;IAAK,CAAE,KAAI;MACnC,IAAIF,UAAU,EAAE;QACd,MAAMG,WAAW,GAAG,EAAE;QACtB,MAAMC,MAAM,GAAGJ,UAAU,CAACI,MAAM;QAEhCC,MAAM,CAACC,OAAO,CAACF,MAAM,CAAC,EAAEtI,GAAG,CAAC,CAAC,CAACyI,QAAQ,EAAEC,UAAU,CAA+B,KAAI;UACnFH,MAAM,CAACC,OAAO,CAACE,UAAU,CAAC,EAAE1I,GAAG,CAAC,CAAC,CAAC8H,SAAS,EAAEa,UAAU,CAAoC,KAAI;YAC7FJ,MAAM,CAACC,OAAO,CAACG,UAAU,CAAC,EAAE3I,GAAG,CAAC,CAAC,CAACyB,QAAQ,EAAE8B,MAAM,CAA6B,KAAI;cACjF,IAAIqF,GAAG,GAAG,EAAE;cAEZ,IAAIC,aAAa,GAAG,SAAS;cAC7B,IAAIC,KAAK,CAACC,OAAO,CAACxF,MAAM,EAAEyF,oBAAoB,CAAC,IAAIzF,MAAM,EAAEyF,oBAAoB,CAACzE,MAAM,EAAE;gBACtF,IAAIhB,MAAM,EAAEyF,oBAAoB,CAACzE,MAAM,KAAK,CAAC,EAAE;kBAC7CsE,aAAa,GAAGtF,MAAM,EAAEyF,oBAAoB,CAAC,CAAC,CAAC,KAAKzI,wBAAwB,GAC1E,0BAA0B,GAC1B,uBAAuB;gBAC3B,CAAC,MAAM;kBACLsI,aAAa,GAAG,sCAAsC;gBACxD;cACF;cAEAD,GAAG,CAAC,IAAI,CAACvD,cAAc,CAAC,GAAG,IAAI,CAACa,MAAM,CAAC+C,IAAI;cAC3CL,GAAG,CAAC,IAAI,CAACtD,eAAe,CAAC,GAAGwC,SAAS;cACrCc,GAAG,CAAC,IAAI,CAACrD,sBAAsB,CAAC,GAAGuC,SAAS,KAAK,IAAI,CAAC5D,gBAAgB,CAACuD,KAAK,GAAG,KAAK,GAAG,IAAI;cAC3FmB,GAAG,CAAC,IAAI,CAACpD,UAAU,CAAC,GAAG4C,KAAK,EAAEd,IAAI,CAAE4B,IAAU,IAAKA,IAAI,CAACC,IAAI,KAAKV,QAAQ,CAAC,EAAEW,KAAK;cACjFR,GAAG,CAAC,IAAI,CAACnD,cAAc,CAAC,GAAGhE,QAAQ;cACnCmH,GAAG,CAAC,IAAI,CAAClD,mBAAmB,CAAC,GAAGmD,aAAa;cAC7CD,GAAG,CAAC,IAAI,CAACjD,QAAQ,CAAC,GAAGpC,MAAM,EAAEA,MAAM,EAAE3B,QAAQ,CAACwF,IAAI,CAAC,IAAI,CAAC;cACxDwB,GAAG,CAAC,IAAI,CAAChD,UAAU,CAAC,GAAGrC,MAAM,EAAEA,MAAM,EAAEzB,QAAQ;cAC/C8G,GAAG,CAAC,IAAI,CAAC/C,UAAU,CAAC,GAAGtC,MAAM,EAAEA,MAAM,EAAEvB,QAAQ;cAC/C4G,GAAG,CAAC,IAAI,CAAC9C,WAAW,CAAC,GAAGvC,MAAM,EAAEA,MAAM,EAAErB,aAAa;cACrD0G,GAAG,CAAC,IAAI,CAACxG,QAAQ,CAAC,GAAGmB,MAAM,EAAEA,MAAM,EAAEnB,QAAQ;cAE7CiG,WAAW,CAACgB,IAAI,CAACT,GAAG,CAAC;YACvB,CAAC,CAAC;UACJ,CAAC,CAAC;QACJ,CAAC,CAAC;QAEFP,WAAW,CAACiB,IAAI,CAAC,CAACC,IAAI,EAAEC,IAAI,KAAKD,IAAI,CAAC,IAAI,CAACjE,eAAe,CAAC,CAACmE,aAAa,CAACD,IAAI,CAAC,IAAI,CAAClE,eAAe,CAAC,CAAC,CAAC;QAEtG,IAAI,CAACJ,YAAY,CAACwE,iBAAiB,CAACrB,WAAW,EAAE,IAAI,CAACnC,MAAM,CAACI,IAAI,CAACqD,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,SAAS,CAAC;QAE/F,IAAI,CAAC5E,aAAa,CAAC6C,OAAO,CAAC,IAAI,CAAC5C,SAAS,CAAC6C,OAAO,CAAC,gDAAgD,CAAC,CAAC;MACtG,CAAC,MAAM;QACL,IAAI,CAAC9C,aAAa,CAAC6E,OAAO,CAAC,IAAI,CAAC5E,SAAS,CAAC6C,OAAO,CAAC,6CAA6C,CAAC,CAAC;MACnG;IACF,CAAC,CAAC;EACN;EAEQjB,4BAA4BA,CAAA;IAClC,IAAI,CAAC/B,gBAAgB,CAACgF,iBAAiB,CAAC,IAAI,CAAC3D,MAAM,CAACI,IAAI,CAAC,CACtDC,IAAI,CACHxG,MAAM,CAAC0G,IAAI,IAAI,CAAC,CAACA,IAAI,IAAIA,IAAI,CAAClC,MAAM,GAAG,CAAC,CAAC,EACzCrE,SAAS,CAAC,IAAI,CAAC+F,UAAU,CAAC,CAC3B,CAACO,SAAS,CACTC,IAAI,IAAG;MACL,IAAI,CAACzC,uBAAuB,GAAGyC,IAAI,CAACzG,GAAG,CAAE8H,SAAoB,IAAK,IAAIzH,SAAS,CAACyH,SAAS,CAAC,CAACgC,cAAc,EAAE,CAAC;MAC5G,MAAMC,kBAAkB,GAAG,IAAI,CAAC/F,uBAAuB,CAACsD,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACd,IAAI,CAAC,EAAEe,EAAE;MACrF,IAAIuC,kBAAkB,EAAE;QACtB,IAAI,CAAC7F,gBAAgB,CAAC8F,UAAU,CAACD,kBAAkB,CAAC;MACtD;IACF,CAAC,CACF;EACL;EAEQlD,uBAAuBA,CAAA;IAC7B,IAAI,CAAC/B,WAAW,CAACqD,WAAW,CAAC,IAAI,CAACjC,MAAM,CAACI,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAACC,IAAI,CAC9DrG,SAAS,CAAC,IAAI,CAAC+F,UAAU,CAAC,CAC3B,CAACO,SAAS,CAACC,IAAI,IAAG;MACjB,IAAI,CAACpC,kBAAkB,GAAGoC,IAAI,CAACzG,GAAG,CAACkJ,IAAI,IAAI,IAAI5I,iBAAiB,CAAC4I,IAAI,CAACC,IAAI,EAAED,IAAI,CAACE,KAAK,CAAC,CAAC;MACxF,IAAI,CAACrD,yBAAyB,GAAG,IAAI,CAAC1B,kBAAkB;IAC1D,CAAC,CAAC;EACJ;EAEQyC,QAAQA,CAAA;IACd,IAAI,CAAC3C,IAAI,GAAG,IAAI,CAACS,EAAE,CAAC2C,KAAK,CAAC;MACxBO,SAAS,EAAE,CAAC,EAAE,CAAC;MACfoB,IAAI,EAAE,CAAC,EAAE;KACV,CAAC;IAEF,IAAI,CAAC9E,gBAAgB,CAAC6F,YAAY,CAC/B1D,IAAI,CACHxG,MAAM,CAAC+H,SAAS,IAAI,CAAC,CAACA,SAAS,CAAC,EAChC7H,SAAS,CAAC6H,SAAS,IAAI,IAAI,CAACjD,gBAAgB,CAACqF,iBAAiB,CAAC,IAAI,CAAChE,MAAM,CAACI,IAAI,EAAEwB,SAAS,CAAC,CAAC,EAC5F5H,SAAS,CAAC,IAAI,CAAC+F,UAAU,CAAC,CAC3B,CAACO,SAAS,CAAC2D,SAAS,IAAI,IAAI,CAACC,wBAAwB,CAACD,SAAS,CAAC,CAAC;IAEpE,IAAI,CAACE,SAAS,EAAE;EAClB;EAEQD,wBAAwBA,CAACD,SAAgB;IAC/C,IAAI,CAAC9F,kBAAkB,GAAG,EAAE;IAE5B,IAAI,CAAC8F,SAAS,EAAE;MACd,IAAI,CAAC7F,WAAW,CAACgG,QAAQ,CAAC,IAAI,EAAE;QAAEC,SAAS,EAAE;MAAK,CAAE,CAAC;MACrD,IAAI,CAAClG,kBAAkB,GAAG,IAAI,CAAC0B,yBAAyB;MAExD;IACF;IAEA,IAAIoE,SAAS,CAAC5F,MAAM,EAAE;MACpB,IAAIiG,cAAc,GAAGL,SAAS,CAACnK,GAAG,CAACyK,IAAI,IAAIA,IAAI,CAACtB,IAAI,CAAC;MACrD,IAAI,CAAC9E,kBAAkB,GAAG,IAAI,CAAC0B,yBAAyB,CAAChG,MAAM,CAC7DmJ,IAAI,IAAIsB,cAAc,CAACE,QAAQ,CAACxB,IAAI,CAAC1B,EAAE,CAAC,CAAC;IAC7C;EACF;EAEQ6C,SAASA,CAAA;IACf,IAAI,CAAClG,IAAI,CAAC8F,YAAY,CAAC1D,IAAI,CACzBpG,GAAG,CAAC,MAAK;MACP,IAAI,CAACuD,OAAO,GAAG,IAAI;MACnB,IAAI,CAACH,MAAM,GAAG,EAAE;IAClB,CAAC,CAAC,EACFxD,MAAM,CAAC4K,GAAG,IAAI,CAAC,CAACA,GAAG,CAAC7C,SAAS,IAAI,CAAC,CAAC6C,GAAG,CAACzB,IAAI,CAAC,EAC5ClJ,GAAG,CAACyG,IAAI,IAAG;MACT,OAAO;QACLqB,SAAS,EAAE,IAAI,CAAC9D,uBAAuB,CAACsD,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACC,EAAE,KAAKf,IAAI,CAACqB,SAAS,CAAC,CAACJ,IAAI;QACvFwB,IAAI,EAAEzC,IAAI,CAACyC;OACZ;IACH,CAAC,CAAC,EACFjJ,SAAS,CAAC2K,MAAM,IAAI,IAAI,CAAC/F,gBAAgB,CAACgG,aAAa,CAAC,IAAI,CAAC3E,MAAM,CAACI,IAAI,EAAEsE,MAAM,CAAC9C,SAAS,EAAE8C,MAAM,CAAC1B,IAAI,CAAC,CAAC,EACzGlJ,GAAG,CAACyG,IAAI,IAAIA,IAAI,GAAG8B,MAAM,CAACC,OAAO,CAAC/B,IAAI,CAAC,CAACzG,GAAG,CAAC,CAAC,CAACyB,QAAQ,EAAEqJ,KAAK,CAAC,KAAI;MAChE,OAAQ;QAAE,GAAGA,KAAe;QAAErJ;MAAQ,CAAE;IAC1C,CAAC,CAAC,GAAG,EAAE,CAAC,CACT,CAAC+E,SAAS,CAACC,IAAI,IAAG;MACjB,IAAI,CAAC/C,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;MAC5B,IAAI,CAACH,MAAM,GAAGkD,IAAI;IACpB,CAAC,CAAC;EACJ;;;uCA3OWjC,wBAAwB,EAAAhE,EAAA,CAAAuK,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAAzK,EAAA,CAAAuK,iBAAA,CAiCzBlL,eAAe,GAAAW,EAAA,CAAAuK,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA3K,EAAA,CAAAuK,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAA7K,EAAA,CAAAuK,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAA/K,EAAA,CAAAuK,iBAAA,CAAAS,EAAA,CAAAC,wBAAA,GAAAjL,EAAA,CAAAuK,iBAAA,CAAAW,EAAA,CAAAC,gBAAA,GAAAnL,EAAA,CAAAuK,iBAAA,CAAAS,EAAA,CAAAI,gBAAA,GAAApL,EAAA,CAAAuK,iBAAA,CAAAc,EAAA,CAAAC,YAAA,GAAAtL,EAAA,CAAAuK,iBAAA,CAAAgB,EAAA,CAAAC,qBAAA,GAAAxL,EAAA,CAAAuK,iBAAA,CAAAkB,EAAA,CAAAC,kBAAA;IAAA;EAAA;;;YAjCd1H,wBAAwB;MAAA2H,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UC5BrCjM,EAAA,CAAAC,cAAA,gBAAmF;UAAtBD,EAAA,CAAAE,UAAA,mBAAAiM,0DAAA;YAAAnM,EAAA,CAAAI,aAAA,CAAAgM,GAAA;YAAA,OAAApM,EAAA,CAAAQ,WAAA,CAAS0L,GAAA,CAAA3E,SAAA,EAAW;UAAA,EAAC;UAChFvH,EAAA,CAAAC,cAAA,kBAA6B;UAAAD,EAAA,CAAAU,MAAA,YAAK;UACpCV,EADoC,CAAAW,YAAA,EAAW,EACtC;UACTX,EAAA,CAAAC,cAAA,YAAqB;UACnBD,EAAA,CAAAU,MAAA,GACF;;UAAAV,EAAA,CAAAW,YAAA,EAAK;UAELX,EAAA,CAAAC,cAAA,aAA+C;UAC7CD,EAAA,CAAA+B,UAAA,IAAAsK,0CAAA,oBAGiC;UAGnCrM,EAAA,CAAAW,YAAA,EAAM;UAiGNX,EA/FA,CAAA+B,UAAA,IAAAuK,uCAAA,mBAAqE,IAAAC,+CAAA,6BAAAvM,EAAA,CAAAwM,sBAAA,CA+Ff;;;;UA3GpDxM,EAAA,CAAAY,SAAA,GACF;UADEZ,EAAA,CAAAa,kBAAA,MAAAb,EAAA,CAAAc,WAAA,wDACF;UAGWd,EAAA,CAAAY,SAAA,GAAiE;UAAjEZ,EAAA,CAAA8C,UAAA,SAAAoJ,GAAA,CAAA1G,qBAAA,KAAA0G,GAAA,CAAAhG,cAAA,kBAAAgG,GAAA,CAAAhG,cAAA,CAAAuG,kBAAA,EAAiE;UAQtEzM,EAAA,CAAAY,SAAA,EAAuC;UAAAZ,EAAvC,CAAA8C,UAAA,SAAAoJ,GAAA,CAAA1I,uBAAA,kBAAA0I,GAAA,CAAA1I,uBAAA,CAAAO,MAAA,CAAuC,aAAA2I,qBAAA,CAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}