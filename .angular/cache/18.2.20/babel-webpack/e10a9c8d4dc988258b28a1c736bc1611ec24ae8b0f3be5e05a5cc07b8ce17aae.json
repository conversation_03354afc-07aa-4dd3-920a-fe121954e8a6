{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FlexLayoutModule } from '@angular/flex-layout';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiGridModule, SwuiNotificationsModule, SwuiPagePanelModule, SwuiSchemaTopFilterModule } from '@skywind-group/lib-swui';\nimport { ReportRgService } from '../../../../common/services/reports/report-rg.service';\nimport { ReportRgComponent } from './report-rg.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@skywind-group/lib-swui\";\nexport class ReportRgModule {\n  static {\n    this.ɵfac = function ReportRgModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ReportRgModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ReportRgModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [ReportRgService],\n      imports: [CommonModule, SwuiPagePanelModule, SwuiGridModule, SwuiNotificationsModule.forRoot(), TranslateModule, FlexLayoutModule, MatTooltipModule, SwuiSchemaTopFilterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ReportRgModule, {\n    declarations: [ReportRgComponent],\n    imports: [CommonModule, SwuiPagePanelModule, SwuiGridModule, i1.SwuiNotificationsModule, TranslateModule, FlexLayoutModule, MatTooltipModule, SwuiSchemaTopFilterModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FlexLayoutModule", "MatTooltipModule", "TranslateModule", "SwuiGridModule", "SwuiNotificationsModule", "SwuiPagePanelModule", "SwuiSchemaTopFilterModule", "ReportRgService", "ReportRgComponent", "ReportRgModule", "imports", "forRoot", "declarations", "i1"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/reports/components/report-rg/report-rg.module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { FlexLayoutModule } from '@angular/flex-layout';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiGridModule, SwuiNotificationsModule, SwuiPagePanelModule, SwuiSchemaTopFilterModule } from '@skywind-group/lib-swui';\n\nimport { ReportRgService } from '../../../../common/services/reports/report-rg.service';\nimport { ReportRgComponent } from './report-rg.component';\n\n\n@NgModule({\n  imports: [\n    CommonModule,\n    SwuiPagePanelModule,\n    SwuiGridModule,\n    SwuiNotificationsModule.forRoot(),\n    TranslateModule,\n    FlexLayoutModule,\n    MatTooltipModule,\n    SwuiSchemaTopFilterModule\n  ],\n  declarations: [\n    ReportRgComponent\n  ],\n  providers: [ReportRgService]\n})\nexport class ReportRgModule {\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,cAAc,EAAEC,uBAAuB,EAAEC,mBAAmB,EAAEC,yBAAyB,QAAQ,yBAAyB;AAEjI,SAASC,eAAe,QAAQ,uDAAuD;AACvF,SAASC,iBAAiB,QAAQ,uBAAuB;;;AAmBzD,OAAM,MAAOC,cAAc;;;uCAAdA,cAAc;IAAA;EAAA;;;YAAdA;IAAc;EAAA;;;iBAFd,CAACF,eAAe,CAAC;MAAAG,OAAA,GAZ1BX,YAAY,EACZM,mBAAmB,EACnBF,cAAc,EACdC,uBAAuB,CAACO,OAAO,EAAE,EACjCT,eAAe,EACfF,gBAAgB,EAChBC,gBAAgB,EAChBK,yBAAyB;IAAA;EAAA;;;2EAOhBG,cAAc;IAAAG,YAAA,GAJvBJ,iBAAiB;IAAAE,OAAA,GAVjBX,YAAY,EACZM,mBAAmB,EACnBF,cAAc,EAAAU,EAAA,CAAAT,uBAAA,EAEdF,eAAe,EACfF,gBAAgB,EAChBC,gBAAgB,EAChBK,yBAAyB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}