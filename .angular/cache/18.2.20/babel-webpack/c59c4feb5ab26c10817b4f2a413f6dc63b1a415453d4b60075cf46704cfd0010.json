{"ast": null, "code": "import { MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"@angular/material/button\";\nimport * as i3 from \"@ngx-translate/core\";\nexport class RemoveConfirmDialogComponent {\n  constructor(data, dialogRef) {\n    this.dialogRef = dialogRef;\n    this.removeCode = data.removeCode;\n  }\n  ngOnInit() {}\n  submitRemove() {\n    this.dialogRef.close(this.removeCode);\n  }\n  static {\n    this.ɵfac = function RemoveConfirmDialogComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RemoveConfirmDialogComponent)(i0.ɵɵdirectiveInject(MAT_DIALOG_DATA), i0.ɵɵdirectiveInject(i1.MatDialogRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RemoveConfirmDialogComponent,\n      selectors: [[\"remove-confirm-dialog\"]],\n      decls: 11,\n      vars: 9,\n      consts: [[\"mat-dialog-title\", \"\"], [\"align\", \"end\"], [\"mat-button\", \"\", \"mat-dialog-close\", \"\"], [\"mat-button\", \"\", \"cdkFocusInitial\", \"\", 3, \"click\"]],\n      template: function RemoveConfirmDialogComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"h2\", 0);\n          i0.ɵɵtext(1);\n          i0.ɵɵpipe(2, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(3, \"mat-dialog-content\");\n          i0.ɵɵelementStart(4, \"mat-dialog-actions\", 1)(5, \"button\", 2);\n          i0.ɵɵtext(6);\n          i0.ɵɵpipe(7, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"button\", 3);\n          i0.ɵɵlistener(\"click\", function RemoveConfirmDialogComponent_Template_button_click_8_listener() {\n            return ctx.submitRemove();\n          });\n          i0.ɵɵtext(9);\n          i0.ɵɵpipe(10, \"translate\");\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 3, \"ENTITY_SETUP.REGIONAL.pleaseConfirm\"));\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(7, 5, \"ENTITY_SETUP.REGIONAL.btnClose\"), \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(10, 7, \"ENTITY_SETUP.REGIONAL.btnConfirmDelete\"), \" \");\n        }\n      },\n      dependencies: [i1.MatDialogClose, i1.MatDialogTitle, i1.MatDialogActions, i1.MatDialogContent, i2.MatButton, i3.TranslatePipe],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["MAT_DIALOG_DATA", "RemoveConfirmDialogComponent", "constructor", "data", "dialogRef", "removeCode", "ngOnInit", "submitRemove", "close", "i0", "ɵɵdirectiveInject", "i1", "MatDialogRef", "selectors", "decls", "vars", "consts", "template", "RemoveConfirmDialogComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵlistener", "RemoveConfirmDialogComponent_Template_button_click_8_listener", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpipeBind1", "ɵɵtextInterpolate1"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/dialogs/remove-confirm-dialog.component.ts", "/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/dialogs/remove-confirm-dialog.component.html"], "sourcesContent": ["import { Component, Inject, OnInit } from '@angular/core';\nimport { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';\n\nexport interface RemoveConfirmDialogData {\n  removeCode: string;\n}\n\n@Component({\n  selector: 'remove-confirm-dialog',\n  templateUrl: 'remove-confirm-dialog.component.html'\n})\nexport class RemoveConfirmDialogComponent implements OnInit {\n  removeCode: string;\n\n  constructor(\n    @Inject(MAT_DIALOG_DATA) data: RemoveConfirmDialogData,\n    public dialogRef: MatDialogRef<RemoveConfirmDialogComponent>,\n  ) {\n    this.removeCode = data.removeCode;\n  }\n\n  ngOnInit() {\n  }\n\n  submitRemove() {\n    this.dialogRef.close(this.removeCode);\n  }\n}\n", "<h2 mat-dialog-title>{{ 'ENTITY_SETUP.REGIONAL.pleaseConfirm' | translate }}</h2>\n<mat-dialog-content>\n</mat-dialog-content>\n<mat-dialog-actions align=\"end\">\n  <button mat-button mat-dialog-close>\n    {{ 'ENTITY_SETUP.REGIONAL.btnClose' | translate }}\n  </button>\n  <button mat-button cdkFocusInitial (click)=\"submitRemove()\">\n    {{ 'ENTITY_SETUP.REGIONAL.btnConfirmDelete' | translate }}\n  </button>\n</mat-dialog-actions>\n"], "mappings": "AACA,SAASA,eAAe,QAAsB,0BAA0B;;;;;AAUxE,OAAM,MAAOC,4BAA4B;EAGvCC,YAC2BC,IAA6B,EAC/CC,SAAqD;IAArD,KAAAA,SAAS,GAATA,SAAS;IAEhB,IAAI,CAACC,UAAU,GAAGF,IAAI,CAACE,UAAU;EACnC;EAEAC,QAAQA,CAAA,GACR;EAEAC,YAAYA,CAAA;IACV,IAAI,CAACH,SAAS,CAACI,KAAK,CAAC,IAAI,CAACH,UAAU,CAAC;EACvC;;;uCAfWJ,4BAA4B,EAAAQ,EAAA,CAAAC,iBAAA,CAI7BV,eAAe,GAAAS,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAJdX,4BAA4B;MAAAY,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCXzCV,EAAA,CAAAY,cAAA,YAAqB;UAAAZ,EAAA,CAAAa,MAAA,GAAuD;;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACjFd,EAAA,CAAAe,SAAA,yBACqB;UAEnBf,EADF,CAAAY,cAAA,4BAAgC,gBACM;UAClCZ,EAAA,CAAAa,MAAA,GACF;;UAAAb,EAAA,CAAAc,YAAA,EAAS;UACTd,EAAA,CAAAY,cAAA,gBAA4D;UAAzBZ,EAAA,CAAAgB,UAAA,mBAAAC,8DAAA;YAAA,OAASN,GAAA,CAAAb,YAAA,EAAc;UAAA,EAAC;UACzDE,EAAA,CAAAa,MAAA,GACF;;UACFb,EADE,CAAAc,YAAA,EAAS,EACU;;;UAVAd,EAAA,CAAAkB,SAAA,EAAuD;UAAvDlB,EAAA,CAAAmB,iBAAA,CAAAnB,EAAA,CAAAoB,WAAA,8CAAuD;UAKxEpB,EAAA,CAAAkB,SAAA,GACF;UADElB,EAAA,CAAAqB,kBAAA,MAAArB,EAAA,CAAAoB,WAAA,8CACF;UAEEpB,EAAA,CAAAkB,SAAA,GACF;UADElB,EAAA,CAAAqB,kBAAA,MAAArB,EAAA,CAAAoB,WAAA,uDACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}