{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport * as autoScroll from 'dom-autoscroller';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { tagClassMap } from '../../../../app.constants';\nimport { GameSelectItem, gameSelectItemTypes } from '../games-select-manager/game-select-item.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../common/services/cdn.service\";\nimport * as i2 from \"ng2-dragula\";\nimport * as i3 from \"@angular/material/dialog\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/flex-layout/extended\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/card\";\nimport * as i8 from \"@angular/material/chips\";\nimport * as i9 from \"@ngx-translate/core\";\nconst _c0 = [\"scrollTable\"];\nconst _c1 = a0 => ({\n  count: a0\n});\nfunction GameCategoryPreviewModalComponent_tr_13_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\", 18);\n    i0.ɵɵtext(2, \"\\u00A0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction GameCategoryPreviewModalComponent_tr_13_td_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", item_r3.title, \" \");\n  }\n}\nfunction GameCategoryPreviewModalComponent_tr_13_td_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"mat-chip-list\")(2, \"mat-chip\", 19);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 20);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.getLabelClass(item_r3));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", item_r3.title, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", item_r3.items.length, \")\");\n  }\n}\nfunction GameCategoryPreviewModalComponent_tr_13_td_4_mat_chip_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-chip\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const label_r5 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.getLabelClass(label_r5));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", label_r5.title, \" \");\n  }\n}\nfunction GameCategoryPreviewModalComponent_tr_13_td_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"mat-chip-list\");\n    i0.ɵɵtemplate(2, GameCategoryPreviewModalComponent_tr_13_td_4_mat_chip_2_Template, 2, 2, \"mat-chip\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 20);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", item_r3.items);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" (\", item_r3.data[\"games\"].length, \")\");\n  }\n}\nfunction GameCategoryPreviewModalComponent_tr_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 15);\n    i0.ɵɵlistener(\"mouseover\", function GameCategoryPreviewModalComponent_tr_13_Template_tr_mouseover_0_listener() {\n      const item_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.highlightGames(item_r3));\n    })(\"mouseout\", function GameCategoryPreviewModalComponent_tr_13_Template_tr_mouseout_0_listener() {\n      const item_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.removeHighlight(item_r3));\n    });\n    i0.ɵɵtemplate(1, GameCategoryPreviewModalComponent_tr_13_ng_container_1_Template, 3, 0, \"ng-container\", 16)(2, GameCategoryPreviewModalComponent_tr_13_td_2_Template, 2, 1, \"td\", 17)(3, GameCategoryPreviewModalComponent_tr_13_td_3_Template, 6, 3, \"td\", 17)(4, GameCategoryPreviewModalComponent_tr_13_td_4_Template, 5, 2, \"td\", 17);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngSwitch\", item_r3.type);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isEntityOwner);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", ctx_r3.itemTypes.GAME);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", item_r3.type === ctx_r3.itemTypes.LABEL || item_r3.type === ctx_r3.itemTypes.PROVIDER ? item_r3.type : \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", ctx_r3.itemTypes.INTERSECTION);\n  }\n}\nfunction GameCategoryPreviewModalComponent_div_15_img_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 25);\n  }\n  if (rf & 2) {\n    const game_r6 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"src\", ctx_r3.getGameImageUrl(game_r6), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction GameCategoryPreviewModalComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"mat-card\");\n    i0.ɵɵtemplate(2, GameCategoryPreviewModalComponent_div_15_img_2_Template, 1, 1, \"img\", 23);\n    i0.ɵɵelementStart(3, \"mat-card-title\", 24);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const game_r6 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    const defaultPlaceholder_r7 = i0.ɵɵreference(24);\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.getGameBackgroundCssClass(game_r6));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.hasGameImageUrl(game_r6))(\"ngIfElse\", defaultPlaceholder_r7);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", game_r6.title, \" \");\n  }\n}\nfunction GameCategoryPreviewModalComponent_ng_template_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 26);\n  }\n}\nexport const DRAGULA_BAG_NAME = 'bag-preview-games';\nexport class GameCategoryPreviewModalComponent {\n  constructor(cdnService, dragulaService, zone, dialogRef, data) {\n    this.cdnService = cdnService;\n    this.dragulaService = dragulaService;\n    this.zone = zone;\n    this.dialogRef = dialogRef;\n    this.data = data;\n    this.previewApplied = new EventEmitter();\n    this.itemTypes = gameSelectItemTypes;\n    this.dragulaBagName = DRAGULA_BAG_NAME;\n    this.destroyed$ = new Subject();\n    this.isEntityOwner = data.isEntityOwner;\n    this.items = data.items;\n    this.setupDragula();\n  }\n  ngOnInit() {\n    this.buildGamesArray();\n    this.cdnService.gameImages.pipe(takeUntil(this.destroyed$)).subscribe(data => {\n      this.gameImages = data;\n    });\n    this.zone.runOutsideAngular(() => {\n      let drake = this.dragulaService.find(DRAGULA_BAG_NAME);\n      autoScroll(this.table.nativeElement, {\n        margin: 20,\n        maxSpeed: 5,\n        scrollWhenOutside: true,\n        autoScroll: function () {\n          return this.down && drake.drake.dragging;\n        }\n      });\n    });\n  }\n  ngOnDestroy() {\n    this.destroyed$.next();\n    this.destroyed$.complete();\n  }\n  hasGameImageUrl({\n    id,\n    type\n  }) {\n    return type === gameSelectItemTypes.GAME && this.gameImages && id in this.gameImages;\n  }\n  getGameImageUrl(item) {\n    return this.hasGameImageUrl(item) ? this.gameImages[item.id] : '';\n  }\n  applyPreview() {\n    this.dialogRef.close(this.items);\n  }\n  getLabelClass(label) {\n    let classes = [];\n    let group = label.data ? label.data['group'] : label.group;\n    let labelClass = tagClassMap.hasOwnProperty(group) ? tagClassMap[group] : 'border-left-grey';\n    classes.push(labelClass);\n    return classes;\n  }\n  getGameBackgroundCssClass(item) {\n    let cssClass = '';\n    if ('cssClass' in item.data) {\n      cssClass = item.data['cssClass'];\n    }\n    return cssClass;\n  }\n  highlightGames(item) {\n    if (item.type === gameSelectItemTypes.GAME) {\n      this.toggleHighlightClass(item);\n    } else {\n      if ('previewGames' in item) {\n        item['previewGames'].forEach(i => this.toggleHighlightClass(i));\n      }\n    }\n  }\n  removeHighlight(item) {\n    if (item.type === gameSelectItemTypes.GAME) {\n      this.toggleHighlightClass(item, false);\n    } else {\n      if ('previewGames' in item) {\n        item['previewGames'].forEach(i => this.toggleHighlightClass(i, false));\n      }\n    }\n  }\n  toggleHighlightClass(item, add = true) {\n    item.data['cssClass'] = add ? 'game-bg-warning' : '';\n  }\n  setupDragula() {\n    const bag = this.dragulaService.find(this.dragulaBagName);\n    if (bag !== undefined) this.dragulaService.destroy(this.dragulaBagName);\n    this.dragulaService.createGroup(this.dragulaBagName, {\n      moves: (_, __, handle) => handle.classList.contains('handle')\n    });\n    this.dragulaService.dragend(this.dragulaBagName).subscribe(() => this.buildGamesArray());\n  }\n  buildGamesArray() {\n    this.games = this.items.map(item => {\n      if ('previewGames' in item) {\n        item['previewGames'] = [];\n      }\n      return item;\n    }).reduce((games, item) => {\n      let itemGames = [item];\n      if (item.type === gameSelectItemTypes.LABEL || item.type === gameSelectItemTypes.PROVIDER) {\n        itemGames = item.items.map(game => this.convertToPreviewGame(item, game));\n      } else if (item.type === gameSelectItemTypes.INTERSECTION) {\n        itemGames = item.data['games'].map(game => this.convertToPreviewGame(item, game));\n      }\n      return [...games, ...itemGames];\n    }, []);\n  }\n  convertToPreviewGame(item, game) {\n    const gameItem = GameSelectItem.createFromGameInfo({\n      ...game.data\n    });\n    if ('previewGames' in item === false) {\n      item['previewGames'] = [];\n    }\n    item['previewGames'].push(gameItem);\n    return gameItem;\n  }\n  static {\n    this.ɵfac = function GameCategoryPreviewModalComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || GameCategoryPreviewModalComponent)(i0.ɵɵdirectiveInject(i1.CdnService), i0.ɵɵdirectiveInject(i2.DragulaService), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i3.MatDialogRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: GameCategoryPreviewModalComponent,\n      selectors: [[\"game-category-preview-modal\"]],\n      viewQuery: function GameCategoryPreviewModalComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.table = _t.first);\n        }\n      },\n      decls: 25,\n      vars: 20,\n      consts: [[\"scrollTable\", \"\"], [\"defaultPlaceholder\", \"\"], [\"mat-dialog-title\", \"\", 1, \"no-margin-top\", \"mb-20\"], [1, \"preview\"], [1, \"preview__list\"], [1, \"preview__title\"], [1, \"preview__scroll\"], [1, \"preview__table\"], [3, \"dragulaModelChange\", \"dragula\", \"dragulaModel\"], [3, \"ngSwitch\", \"mouseover\", \"mouseout\", 4, \"ngFor\", \"ngForOf\"], [1, \"preview__grid\"], [\"class\", \"preview__thumb\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [\"align\", \"end\"], [\"mat-button\", \"\", \"color\", \"primary\", \"mat-dialog-close\", \"\", 1, \"mat-button-md\"], [\"mat-flat-button\", \"\", \"color\", \"primary\", \"cdkFocusInitial\", \"\", 1, \"mat-button-md\", 3, \"click\", \"disabled\"], [3, \"mouseover\", \"mouseout\", \"ngSwitch\"], [4, \"ngIf\"], [4, \"ngSwitchCase\"], [1, \"handle\"], [3, \"ngClass\"], [1, \"item-count\", \"ml-10\"], [3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"preview__thumb\", 3, \"ngClass\"], [\"mat-card-image\", \"\", 3, \"src\", 4, \"ngIf\", \"ngIfElse\"], [1, \"preview__caption\"], [\"mat-card-image\", \"\", 3, \"src\"], [\"src\", \"/img/game-no-cover.jpg\", \"mat-card-image\", \"\"]],\n      template: function GameCategoryPreviewModalComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"h3\", 2);\n          i0.ɵɵtext(1);\n          i0.ɵɵpipe(2, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"mat-dialog-content\")(4, \"div\", 3)(5, \"div\", 4)(6, \"h6\", 5);\n          i0.ɵɵtext(7);\n          i0.ɵɵpipe(8, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"div\", 6, 0)(11, \"table\", 7)(12, \"tbody\", 8);\n          i0.ɵɵtwoWayListener(\"dragulaModelChange\", function GameCategoryPreviewModalComponent_Template_tbody_dragulaModelChange_12_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.items, $event) || (ctx.items = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(13, GameCategoryPreviewModalComponent_tr_13_Template, 5, 5, \"tr\", 9);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(14, \"div\", 10);\n          i0.ɵɵtemplate(15, GameCategoryPreviewModalComponent_div_15_Template, 5, 4, \"div\", 11);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(16, \"mat-dialog-actions\", 12)(17, \"button\", 13);\n          i0.ɵɵtext(18);\n          i0.ɵɵpipe(19, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"button\", 14);\n          i0.ɵɵlistener(\"click\", function GameCategoryPreviewModalComponent_Template_button_click_20_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.applyPreview());\n          });\n          i0.ɵɵtext(21);\n          i0.ɵɵpipe(22, \"translate\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(23, GameCategoryPreviewModalComponent_ng_template_23_Template, 1, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 9, \"COMPONENTS.GAMES_SELECT_MANAGER.PREVIEW.title\"), \"\\n\");\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(8, 11, \"COMPONENTS.GAMES_SELECT_MANAGER.PREVIEW.subtitle\", i0.ɵɵpureFunction1(18, _c1, ctx.games.length)), \" \");\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"dragula\", ctx.dragulaBagName);\n          i0.ɵɵtwoWayProperty(\"dragulaModel\", ctx.items);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.items);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.games);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(19, 14, \"DIALOG.cancel\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", !ctx.isEntityOwner);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(22, 16, \"DIALOG.saveChanges\"), \" \");\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i4.NgSwitch, i4.NgSwitchCase, i2.DragulaDirective, i5.DefaultClassDirective, i6.MatButton, i7.MatCard, i7.MatCardImage, i7.MatCardTitle, i8.MatChip, i3.MatDialogClose, i3.MatDialogTitle, i3.MatDialogActions, i3.MatDialogContent, i9.TranslatePipe],\n      styles: [\".preview[_ngcontent-%COMP%] {\\n  display: flex;\\n  width: 100%;\\n  height: 60vh;\\n}\\n.preview__list[_ngcontent-%COMP%] {\\n  height: 100%;\\n  width: 300px;\\n  flex-grow: 0;\\n  flex-shrink: 0;\\n  border: 1px solid rgba(0, 0, 0, 0.12);\\n  border-radius: 3px;\\n}\\n.preview__scroll[_ngcontent-%COMP%] {\\n  height: calc(100% - 48px);\\n  overflow-y: auto;\\n}\\n.preview__table[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.preview__table[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  text-transform: uppercase;\\n  padding: 10px 15px;\\n  background-color: #fff;\\n}\\n.preview__table[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  height: 48px;\\n  border-bottom: 1px solid rgba(0, 0, 0, 0.12);\\n  font-size: 14px;\\n  vertical-align: middle;\\n  padding: 0 6px;\\n}\\n.preview__table[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%] {\\n  margin: 0 6px 2px 0;\\n}\\n.preview__table[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td.handle[_ngcontent-%COMP%] {\\n  padding: 0;\\n  width: 20px;\\n  background: url(\\\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAANCAYAAACKCx+LAAABS2lUWHRYTUw6Y29tLmFkb2JlLnhtcAAAAAAAPD94cGFja2V0IGJlZ2luPSLvu78iIGlkPSJXNU0wTXBDZWhpSHpyZVN6TlRjemtjOWQiPz4KPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iQWRvYmUgWE1QIENvcmUgNS42LWMxNDIgNzkuMTYwOTI0LCAyMDE3LzA3LzEzLTAxOjA2OjM5ICAgICAgICAiPgogPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4KICA8cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIi8+CiA8L3JkZjpSREY+CjwveDp4bXBtZXRhPgo8P3hwYWNrZXQgZW5kPSJyIj8+nhxg7wAAACZJREFUGJVj/P//vwkDNlBiXPJ/4/+NJqj0fxNGOumIHZSuwqoDAMKBbKoe7/dzAAAAAElFTkSuQmCC\\\") no-repeat center;\\n  cursor: grab;\\n}\\n.preview__table[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td.handle--disabled[_ngcontent-%COMP%] {\\n  padding: 0;\\n  width: 20px;\\n  background: #F8F8F8;\\n  cursor: default;\\n}\\n.preview__table[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:first-child   td[_ngcontent-%COMP%] {\\n  border-top: none;\\n}\\n.preview__table[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:last-child   td[_ngcontent-%COMP%] {\\n  border-bottom: none;\\n}\\n.preview__title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  height: 48px;\\n  margin: 0;\\n  padding: 0 16px;\\n  text-transform: uppercase;\\n  font-weight: 500;\\n  font-size: 14px;\\n  border-bottom: 1px solid rgba(0, 0, 0, 0.12);\\n}\\n.preview__grid[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 100%;\\n  width: calc(100% - 300px);\\n  padding: 0 20px 0 45px;\\n  overflow-y: auto;\\n}\\n.preview__thumb[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  width: 25%;\\n  height: auto;\\n  padding: 12px 10px;\\n}\\n.preview__caption[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  line-height: 1;\\n  display: block;\\n  white-space: nowrap;\\n  text-overflow: ellipsis;\\n  overflow: hidden;\\n}\\n@media (max-width: 1024px) {\\n  .preview__thumb[_ngcontent-%COMP%] {\\n    width: 33.3%;\\n  }\\n}\\n@media (max-width: 767px) {\\n  .preview[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .preview__list[_ngcontent-%COMP%] {\\n    width: 100%;\\n    height: auto;\\n    margin-bottom: 20px;\\n  }\\n  .preview__grid[_ngcontent-%COMP%] {\\n    width: 100%;\\n    padding-left: 0;\\n  }\\n}\\n@media (max-width: 767px) and (orientation: portrait) {\\n  .preview__thumb[_ngcontent-%COMP%] {\\n    width: 50%;\\n  }\\n}\\n\\n.game-bg-grey[_ngcontent-%COMP%] {\\n  background-color: rgba(119, 119, 119, 0.2);\\n}\\n\\n.game-bg-info[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 188, 212, 0.2);\\n}\\n\\n.game-bg-violet[_ngcontent-%COMP%] {\\n  background-color: rgba(156, 39, 176, 0.2);\\n}\\n\\n.game-bg-warning[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 87, 34, 0.2);\\n}\\n\\n.stripe-grey[_ngcontent-%COMP%] {\\n  background: repeating-linear-gradient(90deg, rgba(119, 119, 119, 0.2) 0px, rgba(119, 119, 119, 0.2) 15px, rgba(255, 255, 255, 0.2) 15px, rgba(255, 255, 255, 0.2) 30px);\\n}\\n.stripe-grey-info[_ngcontent-%COMP%] {\\n  background: repeating-linear-gradient(90deg, rgba(119, 119, 119, 0.2) 0px, rgba(119, 119, 119, 0.2) 15px, rgba(0, 188, 212, 0.2) 15px, rgba(0, 188, 212, 0.2) 30px);\\n}\\n.stripe-grey-info-violet[_ngcontent-%COMP%] {\\n  background: repeating-linear-gradient(90deg, rgba(119, 119, 119, 0.2) 0px, rgba(119, 119, 119, 0.2) 15px, rgba(0, 188, 212, 0.2) 15px, rgba(0, 188, 212, 0.2) 30px, rgba(156, 39, 176, 0.2) 30px, rgba(156, 39, 176, 0.2) 45px);\\n}\\n.stripe-grey-info-violet-warning[_ngcontent-%COMP%] {\\n  background: repeating-linear-gradient(90deg, rgba(119, 119, 119, 0.2) 0px, rgba(119, 119, 119, 0.2) 15px, rgba(0, 188, 212, 0.2) 15px, rgba(0, 188, 212, 0.2) 30px, rgba(156, 39, 176, 0.2) 30px, rgba(156, 39, 176, 0.2) 45px, rgba(255, 87, 34, 0.2) 45px, rgba(255, 87, 34, 0.2) 60px);\\n}\\n.stripe-grey-info-warning[_ngcontent-%COMP%] {\\n  background: repeating-linear-gradient(90deg, rgba(119, 119, 119, 0.2) 0px, rgba(119, 119, 119, 0.2) 15px, rgba(0, 188, 212, 0.2) 15px, rgba(0, 188, 212, 0.2) 30px, rgba(255, 87, 34, 0.2) 30px, rgba(255, 87, 34, 0.2) 45px);\\n}\\n.stripe-grey-violet[_ngcontent-%COMP%] {\\n  background: repeating-linear-gradient(90deg, rgba(119, 119, 119, 0.2) 0px, rgba(119, 119, 119, 0.2) 15px, rgba(156, 39, 176, 0.2) 15px, rgba(156, 39, 176, 0.2) 30px);\\n}\\n.stripe-grey-violet-warning[_ngcontent-%COMP%] {\\n  background: repeating-linear-gradient(90deg, rgba(119, 119, 119, 0.2) 0px, rgba(119, 119, 119, 0.2) 15px, rgba(156, 39, 176, 0.2) 15px, rgba(156, 39, 176, 0.2) 30px, rgba(255, 87, 34, 0.2) 30px, rgba(255, 87, 34, 0.2) 45px);\\n}\\n.stripe-grey-warning[_ngcontent-%COMP%] {\\n  background: repeating-linear-gradient(90deg, rgba(119, 119, 119, 0.2) 0px, rgba(119, 119, 119, 0.2) 15px, rgba(255, 87, 34, 0.2) 15px, rgba(255, 87, 34, 0.2) 30px);\\n}\\n.stripe-info[_ngcontent-%COMP%] {\\n  background: repeating-linear-gradient(90deg, rgba(0, 188, 212, 0.2) 0px, rgba(0, 188, 212, 0.2) 15px, rgba(255, 255, 255, 0.2) 15px, rgba(255, 255, 255, 0.2) 30px);\\n}\\n.stripe-info-violet[_ngcontent-%COMP%] {\\n  background: repeating-linear-gradient(90deg, rgba(0, 188, 212, 0.2) 0px, rgba(0, 188, 212, 0.2) 15px, rgba(156, 39, 176, 0.2) 15px, rgba(156, 39, 176, 0.2) 30px);\\n}\\n.stripe-info-violet-warning[_ngcontent-%COMP%] {\\n  background: repeating-linear-gradient(90deg, rgba(0, 188, 212, 0.2) 0px, rgba(0, 188, 212, 0.2) 15px, rgba(156, 39, 176, 0.2) 15px, rgba(156, 39, 176, 0.2) 30px, rgba(255, 87, 34, 0.2) 30px, rgba(255, 87, 34, 0.2) 45px);\\n}\\n.stripe-info-warning[_ngcontent-%COMP%] {\\n  background: repeating-linear-gradient(90deg, rgba(0, 188, 212, 0.2) 0px, rgba(0, 188, 212, 0.2) 15px, rgba(255, 87, 34, 0.2) 15px, rgba(255, 87, 34, 0.2) 30px);\\n}\\n.stripe-violet[_ngcontent-%COMP%] {\\n  background: repeating-linear-gradient(90deg, rgba(156, 39, 176, 0.2) 0px, rgba(156, 39, 176, 0.2) 15px, rgba(255, 255, 255, 0.2) 15px, rgba(255, 255, 255, 0.2) 30px);\\n}\\n.stripe-violet-warning[_ngcontent-%COMP%] {\\n  background: repeating-linear-gradient(90deg, rgba(156, 39, 176, 0.2) 0px, rgba(156, 39, 176, 0.2) 15px, rgba(255, 87, 34, 0.2) 15px, rgba(255, 87, 34, 0.2) 30px);\\n}\\n.stripe-warning[_ngcontent-%COMP%] {\\n  background: repeating-linear-gradient(90deg, rgba(255, 87, 34, 0.2) 0px, rgba(255, 87, 34, 0.2) 15px, rgba(255, 255, 255, 0.2) 15px, rgba(255, 255, 255, 0.2) 30px);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "MAT_DIALOG_DATA", "autoScroll", "Subject", "takeUntil", "tagClassMap", "GameSelectItem", "gameSelectItemTypes", "i0", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "item_r3", "title", "ɵɵproperty", "ctx_r3", "getLabelClass", "items", "length", "label_r5", "ɵɵtemplate", "GameCategoryPreviewModalComponent_tr_13_td_4_mat_chip_2_Template", "data", "ɵɵlistener", "GameCategoryPreviewModalComponent_tr_13_Template_tr_mouseover_0_listener", "ɵɵrestoreView", "_r2", "$implicit", "ɵɵnextContext", "ɵɵresetView", "highlightGames", "GameCategoryPreviewModalComponent_tr_13_Template_tr_mouseout_0_listener", "removeHighlight", "GameCategoryPreviewModalComponent_tr_13_ng_container_1_Template", "GameCategoryPreviewModalComponent_tr_13_td_2_Template", "GameCategoryPreviewModalComponent_tr_13_td_3_Template", "GameCategoryPreviewModalComponent_tr_13_td_4_Template", "type", "isEntityOwner", "itemTypes", "GAME", "LABEL", "PROVIDER", "INTERSECTION", "ɵɵelement", "getGameImageUrl", "game_r6", "ɵɵsanitizeUrl", "GameCategoryPreviewModalComponent_div_15_img_2_Template", "getGameBackgroundCssClass", "hasGameImageUrl", "defaultPlaceholder_r7", "DRAGULA_BAG_NAME", "GameCategoryPreviewModalComponent", "constructor", "cdnService", "dragulaService", "zone", "dialogRef", "previewApplied", "dragulaBagName", "destroyed$", "setupDragula", "ngOnInit", "buildGamesArray", "gameImages", "pipe", "subscribe", "runOutsideAngular", "drake", "find", "table", "nativeElement", "margin", "maxSpeed", "scrollWhenOutside", "down", "dragging", "ngOnDestroy", "next", "complete", "id", "item", "applyPreview", "close", "label", "classes", "group", "labelClass", "hasOwnProperty", "push", "cssClass", "toggleHighlightClass", "for<PERSON>ach", "i", "add", "bag", "undefined", "destroy", "createGroup", "moves", "_", "__", "handle", "classList", "contains", "dragend", "games", "map", "reduce", "itemGames", "game", "convertToPreviewGame", "gameItem", "createFromGameInfo", "ɵɵdirectiveInject", "i1", "CdnService", "i2", "DragulaService", "NgZone", "i3", "MatDialogRef", "selectors", "viewQuery", "GameCategoryPreviewModalComponent_Query", "rf", "ctx", "ɵɵtwoWayListener", "GameCategoryPreviewModalComponent_Template_tbody_dragulaModelChange_12_listener", "$event", "_r1", "ɵɵtwoWayBindingSet", "GameCategoryPreviewModalComponent_tr_13_Template", "GameCategoryPreviewModalComponent_div_15_Template", "GameCategoryPreviewModalComponent_Template_button_click_20_listener", "GameCategoryPreviewModalComponent_ng_template_23_Template", "ɵɵtemplateRefExtractor", "ɵɵpipeBind1", "ɵɵpipeBind2", "ɵɵpureFunction1", "_c1", "ɵɵtwoWayProperty"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/games-categories-management/game-category-details/game-category-preview/game-category-preview-modal.component.ts", "/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/games-categories-management/game-category-details/game-category-preview/game-category-preview-modal.component.html"], "sourcesContent": ["import { Component, ElementRef, EventEmitter, Inject, <PERSON>Z<PERSON>, OnDestroy, OnInit, ViewChild } from '@angular/core';\nimport { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';\nimport * as autoScroll from 'dom-autoscroller';\nimport { DragulaService } from 'ng2-dragula';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { tagClassMap } from '../../../../app.constants';\nimport { CdnService } from '../../../../common/services/cdn.service';\nimport { GameInfo } from '../../../../common/typings';\nimport { GameSelectItem, gameSelectItemTypes } from '../games-select-manager/game-select-item.model';\n\nexport const DRAGULA_BAG_NAME: string = 'bag-preview-games';\n\n@Component({\n  selector: 'game-category-preview-modal',\n  templateUrl: 'game-category-preview-modal.component.html',\n  styleUrls: ['./games-category-preview-modal.component.scss']\n})\nexport class GameCategoryPreviewModalComponent implements OnInit, OnDestroy {\n\n  @ViewChild('scrollTable', { static: true }) table: ElementRef;\n\n  public isEntityOwner: boolean;\n  public items: GameSelectItem[];\n  public games: GameSelectItem[];\n  public previewApplied: EventEmitter<GameSelectItem[]> = new EventEmitter();\n\n  public itemTypes = gameSelectItemTypes;\n  public dragulaBagName = DRAGULA_BAG_NAME;\n  private gameImages: { [key: string]: string; };\n\n  private readonly destroyed$ = new Subject<void>();\n\n  constructor(private cdnService: CdnService,\n    private dragulaService: DragulaService,\n    private zone: NgZone,\n    private dialogRef: MatDialogRef<GameCategoryPreviewModalComponent>,\n    @Inject(MAT_DIALOG_DATA) public data: { items: GameSelectItem[], isEntityOwner: boolean; }\n  ) {\n    this.isEntityOwner = data.isEntityOwner;\n    this.items = data.items;\n    this.setupDragula();\n  }\n\n  ngOnInit() {\n    this.buildGamesArray();\n    this.cdnService.gameImages.pipe(\n      takeUntil(this.destroyed$)\n    ).subscribe(data => {\n      this.gameImages = data;\n    });\n\n    this.zone.runOutsideAngular(() => {\n      let drake = this.dragulaService.find(DRAGULA_BAG_NAME);\n      autoScroll(this.table.nativeElement, {\n        margin: 20,\n        maxSpeed: 5,\n        scrollWhenOutside: true,\n        autoScroll: function () {\n          return this.down && drake.drake.dragging;\n        },\n      });\n    });\n  }\n\n  ngOnDestroy(): void {\n    this.destroyed$.next();\n    this.destroyed$.complete();\n  }\n\n  hasGameImageUrl({ id, type }: GameSelectItem): boolean {\n    return type === gameSelectItemTypes.GAME && this.gameImages && id in this.gameImages;\n  }\n\n  getGameImageUrl(item: GameSelectItem): string {\n    return this.hasGameImageUrl(item) ? this.gameImages[(item.id)] : '';\n  }\n\n  applyPreview() {\n    this.dialogRef.close(this.items);\n  }\n\n  getLabelClass(label): string[] {\n    let classes = [];\n    let group = label.data ? label.data['group'] : label.group;\n    let labelClass = tagClassMap.hasOwnProperty(group) ? tagClassMap[group] : 'border-left-grey';\n\n    classes.push(labelClass);\n\n    return classes;\n  }\n\n  getGameBackgroundCssClass(item: GameSelectItem) {\n    let cssClass = '';\n    if ('cssClass' in item.data) {\n      cssClass = item.data['cssClass'] as string;\n    }\n    return cssClass;\n  }\n\n  highlightGames(item: GameSelectItem) {\n    if (item.type === gameSelectItemTypes.GAME) {\n      this.toggleHighlightClass(item);\n    } else {\n      if ('previewGames' in item) {\n        (item['previewGames'] as any[]).forEach(i => this.toggleHighlightClass(i));\n      }\n    }\n  }\n\n  removeHighlight(item: GameSelectItem) {\n    if (item.type === gameSelectItemTypes.GAME) {\n      this.toggleHighlightClass(item, false);\n    } else {\n      if ('previewGames' in item) {\n        (item['previewGames'] as any[]).forEach(i => this.toggleHighlightClass(i, false));\n      }\n    }\n  }\n\n  private toggleHighlightClass(item, add = true) {\n    item.data['cssClass'] = add ? 'game-bg-warning' : '';\n  }\n\n  private setupDragula() {\n    const bag: any = this.dragulaService.find(this.dragulaBagName);\n    if (bag !== undefined) this.dragulaService.destroy(this.dragulaBagName);\n\n    this.dragulaService.createGroup(this.dragulaBagName, {\n      moves: (_, __, handle) => handle.classList.contains('handle')\n    });\n\n    this.dragulaService.dragend(this.dragulaBagName).subscribe(() => this.buildGamesArray());\n  }\n\n  private buildGamesArray() {\n    this.games = this.items.map((item) => {\n      if ('previewGames' in item) {\n        (item['previewGames'] as any[]) = [];\n      }\n      return item;\n    }).reduce((games, item) => {\n      let itemGames = [item];\n\n      if (item.type === gameSelectItemTypes.LABEL || item.type === gameSelectItemTypes.PROVIDER) {\n        itemGames = item.items\n          .map((game: GameSelectItem) => this.convertToPreviewGame(item, game));\n      } else if (item.type === gameSelectItemTypes.INTERSECTION) {\n        itemGames = (item.data['games'] as GameSelectItem[])\n          .map((game: GameSelectItem) => this.convertToPreviewGame(item, game));\n      }\n      return [...games, ...itemGames];\n    }, []);\n  }\n\n  private convertToPreviewGame(item: GameSelectItem, game: GameSelectItem) {\n    const gameItem = GameSelectItem.createFromGameInfo(<GameInfo>{ ...game.data });\n    if ('previewGames' in item === false) {\n      item['previewGames'] = [];\n    }\n    item['previewGames'].push(gameItem);\n    return gameItem;\n  }\n}\n", "<h3 mat-dialog-title class=\"no-margin-top mb-20\">\n  {{ 'COMPONENTS.GAMES_SELECT_MANAGER.PREVIEW.title' | translate }}\n</h3>\n\n<mat-dialog-content>\n  <div class=\"preview\">\n\n    <div class=\"preview__list\">\n      <h6 class=\"preview__title\">\n        {{ 'COMPONENTS.GAMES_SELECT_MANAGER.PREVIEW.subtitle' | translate:{ count: games.length } }}\n      </h6>\n      <div class=\"preview__scroll\" #scrollTable >\n        <table class=\"preview__table\">\n          <tbody [dragula]=\"dragulaBagName\" [(dragulaModel)]=\"items\">\n          <tr *ngFor=\"let item of items\" [ngSwitch]=\"item.type\"\n              (mouseover)=\"highlightGames(item)\" (mouseout)=\"removeHighlight(item)\">\n            <ng-container *ngIf=\"isEntityOwner\">\n              <td class=\"handle\">&nbsp;</td>\n            </ng-container>\n\n            <td *ngSwitchCase=\"itemTypes.GAME\">\n              {{ item.title }}\n            </td>\n\n            <td *ngSwitchCase=\"item.type === itemTypes.LABEL || item.type === itemTypes.PROVIDER ? item.type : ''\">\n              <mat-chip-list>\n                <mat-chip [ngClass]=\"getLabelClass(item)\">\n                  {{ item.title }}\n                </mat-chip>\n                <span class=\"item-count ml-10\">({{ item.items.length }})</span>\n              </mat-chip-list>\n            </td>\n\n            <td *ngSwitchCase=\"itemTypes.INTERSECTION\">\n              <mat-chip-list>\n                <mat-chip *ngFor=\"let label of item.items;\" [ngClass]=\"getLabelClass(label)\">\n                  {{ label.title }}\n                </mat-chip>\n              </mat-chip-list>\n              <span class=\"item-count ml-10\"> ({{ item.data['games'].length }})</span>\n            </td>\n          </tr>\n          </tbody>\n        </table>\n      </div>\n    </div>\n\n    <div class=\"preview__grid\">\n      <div class=\"preview__thumb\" *ngFor=\"let game of games\" [ngClass]=\"getGameBackgroundCssClass(game)\">\n        <mat-card>\n          <img mat-card-image *ngIf=\"hasGameImageUrl(game); else defaultPlaceholder\" [src]=\"getGameImageUrl(game)\">\n          <mat-card-title class=\"preview__caption\">\n            {{ game.title }}\n          </mat-card-title>\n        </mat-card>\n      </div>\n    </div>\n  </div>\n</mat-dialog-content>\n\n<mat-dialog-actions align=\"end\">\n  <button mat-button color=\"primary\" class=\"mat-button-md\" mat-dialog-close>\n    {{ 'DIALOG.cancel' | translate }}\n  </button>\n  <button mat-flat-button color=\"primary\" class=\"mat-button-md\" [disabled]=\"!isEntityOwner\"  cdkFocusInitial (click)=\"applyPreview()\">\n    {{ 'DIALOG.saveChanges' | translate }}\n  </button>\n</mat-dialog-actions>\n\n<ng-template #defaultPlaceholder>\n  <img src=\"/img/game-no-cover.jpg\" mat-card-image>\n</ng-template>\n"], "mappings": "AAAA,SAAgCA,YAAY,QAAsD,eAAe;AACjH,SAASC,eAAe,QAAsB,0BAA0B;AACxE,OAAO,KAAKC,UAAU,MAAM,kBAAkB;AAE9C,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,WAAW,QAAQ,2BAA2B;AAGvD,SAASC,cAAc,EAAEC,mBAAmB,QAAQ,gDAAgD;;;;;;;;;;;;;;;;;ICOxFC,EAAA,CAAAC,uBAAA,GAAoC;IAClCD,EAAA,CAAAE,cAAA,aAAmB;IAAAF,EAAA,CAAAG,MAAA,aAAM;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;;;IAGhCJ,EAAA,CAAAE,cAAA,SAAmC;IACjCF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;IADHJ,EAAA,CAAAK,SAAA,EACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,OAAA,CAAAC,KAAA,MACF;;;;;IAIIR,EAFJ,CAAAE,cAAA,SAAuG,oBACtF,mBAC6B;IACxCF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACXJ,EAAA,CAAAE,cAAA,eAA+B;IAAAF,EAAA,CAAAG,MAAA,GAAyB;IAE5DH,EAF4D,CAAAI,YAAA,EAAO,EACjD,EACb;;;;;IALSJ,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAS,UAAA,YAAAC,MAAA,CAAAC,aAAA,CAAAJ,OAAA,EAA+B;IACvCP,EAAA,CAAAK,SAAA,EACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,OAAA,CAAAC,KAAA,MACF;IAC+BR,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAAM,kBAAA,MAAAC,OAAA,CAAAK,KAAA,CAAAC,MAAA,MAAyB;;;;;IAMxDb,EAAA,CAAAE,cAAA,mBAA6E;IAC3EF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAW;;;;;IAFiCJ,EAAA,CAAAS,UAAA,YAAAC,MAAA,CAAAC,aAAA,CAAAG,QAAA,EAAgC;IAC1Ed,EAAA,CAAAK,SAAA,EACF;IADEL,EAAA,CAAAM,kBAAA,MAAAQ,QAAA,CAAAN,KAAA,MACF;;;;;IAHFR,EADF,CAAAE,cAAA,SAA2C,oBAC1B;IACbF,EAAA,CAAAe,UAAA,IAAAC,gEAAA,uBAA6E;IAG/EhB,EAAA,CAAAI,YAAA,EAAgB;IAChBJ,EAAA,CAAAE,cAAA,eAA+B;IAACF,EAAA,CAAAG,MAAA,GAAiC;IACnEH,EADmE,CAAAI,YAAA,EAAO,EACrE;;;;IAL2BJ,EAAA,CAAAK,SAAA,GAAc;IAAdL,EAAA,CAAAS,UAAA,YAAAF,OAAA,CAAAK,KAAA,CAAc;IAIZZ,EAAA,CAAAK,SAAA,GAAiC;IAAjCL,EAAA,CAAAM,kBAAA,OAAAC,OAAA,CAAAU,IAAA,UAAAJ,MAAA,MAAiC;;;;;;IAzBrEb,EAAA,CAAAE,cAAA,aAC0E;IAAnCF,EAAnC,CAAAkB,UAAA,uBAAAC,yEAAA;MAAA,MAAAZ,OAAA,GAAAP,EAAA,CAAAoB,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAZ,MAAA,GAAAV,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAAad,MAAA,CAAAe,cAAA,CAAAlB,OAAA,CAAoB;IAAA,EAAC,sBAAAmB,wEAAA;MAAA,MAAAnB,OAAA,GAAAP,EAAA,CAAAoB,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAZ,MAAA,GAAAV,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAAad,MAAA,CAAAiB,eAAA,CAAApB,OAAA,CAAqB;IAAA,EAAC;IAkBvEP,EAjBA,CAAAe,UAAA,IAAAa,+DAAA,2BAAoC,IAAAC,qDAAA,iBAID,IAAAC,qDAAA,iBAIoE,IAAAC,qDAAA,iBAS5D;IAQ7C/B,EAAA,CAAAI,YAAA,EAAK;;;;;IA3B0BJ,EAAA,CAAAS,UAAA,aAAAF,OAAA,CAAAyB,IAAA,CAAsB;IAEpChC,EAAA,CAAAK,SAAA,EAAmB;IAAnBL,EAAA,CAAAS,UAAA,SAAAC,MAAA,CAAAuB,aAAA,CAAmB;IAI7BjC,EAAA,CAAAK,SAAA,EAA4B;IAA5BL,EAAA,CAAAS,UAAA,iBAAAC,MAAA,CAAAwB,SAAA,CAAAC,IAAA,CAA4B;IAI5BnC,EAAA,CAAAK,SAAA,EAAgG;IAAhGL,EAAA,CAAAS,UAAA,iBAAAF,OAAA,CAAAyB,IAAA,KAAAtB,MAAA,CAAAwB,SAAA,CAAAE,KAAA,IAAA7B,OAAA,CAAAyB,IAAA,KAAAtB,MAAA,CAAAwB,SAAA,CAAAG,QAAA,GAAA9B,OAAA,CAAAyB,IAAA,MAAgG;IAShGhC,EAAA,CAAAK,SAAA,EAAoC;IAApCL,EAAA,CAAAS,UAAA,iBAAAC,MAAA,CAAAwB,SAAA,CAAAI,YAAA,CAAoC;;;;;IAiB3CtC,EAAA,CAAAuC,SAAA,cAAyG;;;;;IAA9BvC,EAAA,CAAAS,UAAA,QAAAC,MAAA,CAAA8B,eAAA,CAAAC,OAAA,GAAAzC,EAAA,CAAA0C,aAAA,CAA6B;;;;;IAD1G1C,EADF,CAAAE,cAAA,cAAmG,eACvF;IACRF,EAAA,CAAAe,UAAA,IAAA4B,uDAAA,kBAAyG;IACzG3C,EAAA,CAAAE,cAAA,yBAAyC;IACvCF,EAAA,CAAAG,MAAA,GACF;IAEJH,EAFI,CAAAI,YAAA,EAAiB,EACR,EACP;;;;;;IAPiDJ,EAAA,CAAAS,UAAA,YAAAC,MAAA,CAAAkC,yBAAA,CAAAH,OAAA,EAA2C;IAEzEzC,EAAA,CAAAK,SAAA,GAA6B;IAAAL,EAA7B,CAAAS,UAAA,SAAAC,MAAA,CAAAmC,eAAA,CAAAJ,OAAA,EAA6B,aAAAK,qBAAA,CAAuB;IAEvE9C,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAmC,OAAA,CAAAjC,KAAA,MACF;;;;;IAiBRR,EAAA,CAAAuC,SAAA,cAAiD;;;AD3DnD,OAAO,MAAMQ,gBAAgB,GAAW,mBAAmB;AAO3D,OAAM,MAAOC,iCAAiC;EAe5CC,YAAoBC,UAAsB,EAChCC,cAA8B,EAC9BC,IAAY,EACZC,SAA0D,EAClCpC,IAA0D;IAJxE,KAAAiC,UAAU,GAAVA,UAAU;IACpB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,SAAS,GAATA,SAAS;IACe,KAAApC,IAAI,GAAJA,IAAI;IAZ/B,KAAAqC,cAAc,GAAmC,IAAI9D,YAAY,EAAE;IAEnE,KAAA0C,SAAS,GAAGnC,mBAAmB;IAC/B,KAAAwD,cAAc,GAAGR,gBAAgB;IAGvB,KAAAS,UAAU,GAAG,IAAI7D,OAAO,EAAQ;IAQ/C,IAAI,CAACsC,aAAa,GAAGhB,IAAI,CAACgB,aAAa;IACvC,IAAI,CAACrB,KAAK,GAAGK,IAAI,CAACL,KAAK;IACvB,IAAI,CAAC6C,YAAY,EAAE;EACrB;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACT,UAAU,CAACU,UAAU,CAACC,IAAI,CAC7BjE,SAAS,CAAC,IAAI,CAAC4D,UAAU,CAAC,CAC3B,CAACM,SAAS,CAAC7C,IAAI,IAAG;MACjB,IAAI,CAAC2C,UAAU,GAAG3C,IAAI;IACxB,CAAC,CAAC;IAEF,IAAI,CAACmC,IAAI,CAACW,iBAAiB,CAAC,MAAK;MAC/B,IAAIC,KAAK,GAAG,IAAI,CAACb,cAAc,CAACc,IAAI,CAAClB,gBAAgB,CAAC;MACtDrD,UAAU,CAAC,IAAI,CAACwE,KAAK,CAACC,aAAa,EAAE;QACnCC,MAAM,EAAE,EAAE;QACVC,QAAQ,EAAE,CAAC;QACXC,iBAAiB,EAAE,IAAI;QACvB5E,UAAU,EAAE,SAAAA,CAAA;UACV,OAAO,IAAI,CAAC6E,IAAI,IAAIP,KAAK,CAACA,KAAK,CAACQ,QAAQ;QAC1C;OACD,CAAC;IACJ,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACjB,UAAU,CAACkB,IAAI,EAAE;IACtB,IAAI,CAAClB,UAAU,CAACmB,QAAQ,EAAE;EAC5B;EAEA9B,eAAeA,CAAC;IAAE+B,EAAE;IAAE5C;EAAI,CAAkB;IAC1C,OAAOA,IAAI,KAAKjC,mBAAmB,CAACoC,IAAI,IAAI,IAAI,CAACyB,UAAU,IAAIgB,EAAE,IAAI,IAAI,CAAChB,UAAU;EACtF;EAEApB,eAAeA,CAACqC,IAAoB;IAClC,OAAO,IAAI,CAAChC,eAAe,CAACgC,IAAI,CAAC,GAAG,IAAI,CAACjB,UAAU,CAAEiB,IAAI,CAACD,EAAE,CAAE,GAAG,EAAE;EACrE;EAEAE,YAAYA,CAAA;IACV,IAAI,CAACzB,SAAS,CAAC0B,KAAK,CAAC,IAAI,CAACnE,KAAK,CAAC;EAClC;EAEAD,aAAaA,CAACqE,KAAK;IACjB,IAAIC,OAAO,GAAG,EAAE;IAChB,IAAIC,KAAK,GAAGF,KAAK,CAAC/D,IAAI,GAAG+D,KAAK,CAAC/D,IAAI,CAAC,OAAO,CAAC,GAAG+D,KAAK,CAACE,KAAK;IAC1D,IAAIC,UAAU,GAAGtF,WAAW,CAACuF,cAAc,CAACF,KAAK,CAAC,GAAGrF,WAAW,CAACqF,KAAK,CAAC,GAAG,kBAAkB;IAE5FD,OAAO,CAACI,IAAI,CAACF,UAAU,CAAC;IAExB,OAAOF,OAAO;EAChB;EAEArC,yBAAyBA,CAACiC,IAAoB;IAC5C,IAAIS,QAAQ,GAAG,EAAE;IACjB,IAAI,UAAU,IAAIT,IAAI,CAAC5D,IAAI,EAAE;MAC3BqE,QAAQ,GAAGT,IAAI,CAAC5D,IAAI,CAAC,UAAU,CAAW;IAC5C;IACA,OAAOqE,QAAQ;EACjB;EAEA7D,cAAcA,CAACoD,IAAoB;IACjC,IAAIA,IAAI,CAAC7C,IAAI,KAAKjC,mBAAmB,CAACoC,IAAI,EAAE;MAC1C,IAAI,CAACoD,oBAAoB,CAACV,IAAI,CAAC;IACjC,CAAC,MAAM;MACL,IAAI,cAAc,IAAIA,IAAI,EAAE;QACzBA,IAAI,CAAC,cAAc,CAAW,CAACW,OAAO,CAACC,CAAC,IAAI,IAAI,CAACF,oBAAoB,CAACE,CAAC,CAAC,CAAC;MAC5E;IACF;EACF;EAEA9D,eAAeA,CAACkD,IAAoB;IAClC,IAAIA,IAAI,CAAC7C,IAAI,KAAKjC,mBAAmB,CAACoC,IAAI,EAAE;MAC1C,IAAI,CAACoD,oBAAoB,CAACV,IAAI,EAAE,KAAK,CAAC;IACxC,CAAC,MAAM;MACL,IAAI,cAAc,IAAIA,IAAI,EAAE;QACzBA,IAAI,CAAC,cAAc,CAAW,CAACW,OAAO,CAACC,CAAC,IAAI,IAAI,CAACF,oBAAoB,CAACE,CAAC,EAAE,KAAK,CAAC,CAAC;MACnF;IACF;EACF;EAEQF,oBAAoBA,CAACV,IAAI,EAAEa,GAAG,GAAG,IAAI;IAC3Cb,IAAI,CAAC5D,IAAI,CAAC,UAAU,CAAC,GAAGyE,GAAG,GAAG,iBAAiB,GAAG,EAAE;EACtD;EAEQjC,YAAYA,CAAA;IAClB,MAAMkC,GAAG,GAAQ,IAAI,CAACxC,cAAc,CAACc,IAAI,CAAC,IAAI,CAACV,cAAc,CAAC;IAC9D,IAAIoC,GAAG,KAAKC,SAAS,EAAE,IAAI,CAACzC,cAAc,CAAC0C,OAAO,CAAC,IAAI,CAACtC,cAAc,CAAC;IAEvE,IAAI,CAACJ,cAAc,CAAC2C,WAAW,CAAC,IAAI,CAACvC,cAAc,EAAE;MACnDwC,KAAK,EAAEA,CAACC,CAAC,EAAEC,EAAE,EAAEC,MAAM,KAAKA,MAAM,CAACC,SAAS,CAACC,QAAQ,CAAC,QAAQ;KAC7D,CAAC;IAEF,IAAI,CAACjD,cAAc,CAACkD,OAAO,CAAC,IAAI,CAAC9C,cAAc,CAAC,CAACO,SAAS,CAAC,MAAM,IAAI,CAACH,eAAe,EAAE,CAAC;EAC1F;EAEQA,eAAeA,CAAA;IACrB,IAAI,CAAC2C,KAAK,GAAG,IAAI,CAAC1F,KAAK,CAAC2F,GAAG,CAAE1B,IAAI,IAAI;MACnC,IAAI,cAAc,IAAIA,IAAI,EAAE;QACzBA,IAAI,CAAC,cAAc,CAAW,GAAG,EAAE;MACtC;MACA,OAAOA,IAAI;IACb,CAAC,CAAC,CAAC2B,MAAM,CAAC,CAACF,KAAK,EAAEzB,IAAI,KAAI;MACxB,IAAI4B,SAAS,GAAG,CAAC5B,IAAI,CAAC;MAEtB,IAAIA,IAAI,CAAC7C,IAAI,KAAKjC,mBAAmB,CAACqC,KAAK,IAAIyC,IAAI,CAAC7C,IAAI,KAAKjC,mBAAmB,CAACsC,QAAQ,EAAE;QACzFoE,SAAS,GAAG5B,IAAI,CAACjE,KAAK,CACnB2F,GAAG,CAAEG,IAAoB,IAAK,IAAI,CAACC,oBAAoB,CAAC9B,IAAI,EAAE6B,IAAI,CAAC,CAAC;MACzE,CAAC,MAAM,IAAI7B,IAAI,CAAC7C,IAAI,KAAKjC,mBAAmB,CAACuC,YAAY,EAAE;QACzDmE,SAAS,GAAI5B,IAAI,CAAC5D,IAAI,CAAC,OAAO,CAAsB,CACjDsF,GAAG,CAAEG,IAAoB,IAAK,IAAI,CAACC,oBAAoB,CAAC9B,IAAI,EAAE6B,IAAI,CAAC,CAAC;MACzE;MACA,OAAO,CAAC,GAAGJ,KAAK,EAAE,GAAGG,SAAS,CAAC;IACjC,CAAC,EAAE,EAAE,CAAC;EACR;EAEQE,oBAAoBA,CAAC9B,IAAoB,EAAE6B,IAAoB;IACrE,MAAME,QAAQ,GAAG9G,cAAc,CAAC+G,kBAAkB,CAAW;MAAE,GAAGH,IAAI,CAACzF;IAAI,CAAE,CAAC;IAC9E,IAAI,cAAc,IAAI4D,IAAI,KAAK,KAAK,EAAE;MACpCA,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE;IAC3B;IACAA,IAAI,CAAC,cAAc,CAAC,CAACQ,IAAI,CAACuB,QAAQ,CAAC;IACnC,OAAOA,QAAQ;EACjB;;;uCAhJW5D,iCAAiC,EAAAhD,EAAA,CAAA8G,iBAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAhH,EAAA,CAAA8G,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAlH,EAAA,CAAA8G,iBAAA,CAAA9G,EAAA,CAAAmH,MAAA,GAAAnH,EAAA,CAAA8G,iBAAA,CAAAM,EAAA,CAAAC,YAAA,GAAArH,EAAA,CAAA8G,iBAAA,CAmBlCrH,eAAe;IAAA;EAAA;;;YAnBduD,iCAAiC;MAAAsE,SAAA;MAAAC,SAAA,WAAAC,wCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;UClB9CzH,EAAA,CAAAE,cAAA,YAAiD;UAC/CF,EAAA,CAAAG,MAAA,GACF;;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAMCJ,EAJN,CAAAE,cAAA,yBAAoB,aACG,aAEQ,YACE;UACzBF,EAAA,CAAAG,MAAA,GACF;;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAGDJ,EAFJ,CAAAE,cAAA,gBAA2C,gBACX,gBAC+B;UAAzBF,EAAA,CAAA2H,gBAAA,gCAAAC,gFAAAC,MAAA;YAAA7H,EAAA,CAAAoB,aAAA,CAAA0G,GAAA;YAAA9H,EAAA,CAAA+H,kBAAA,CAAAL,GAAA,CAAA9G,KAAA,EAAAiH,MAAA,MAAAH,GAAA,CAAA9G,KAAA,GAAAiH,MAAA;YAAA,OAAA7H,EAAA,CAAAwB,WAAA,CAAAqG,MAAA;UAAA,EAAwB;UAC1D7H,EAAA,CAAAe,UAAA,KAAAiH,gDAAA,gBAC0E;UA8BhFhI,EAHM,CAAAI,YAAA,EAAQ,EACF,EACJ,EACF;UAENJ,EAAA,CAAAE,cAAA,eAA2B;UACzBF,EAAA,CAAAe,UAAA,KAAAkH,iDAAA,kBAAmG;UAUzGjI,EAFI,CAAAI,YAAA,EAAM,EACF,EACa;UAGnBJ,EADF,CAAAE,cAAA,8BAAgC,kBAC4C;UACxEF,EAAA,CAAAG,MAAA,IACF;;UAAAH,EAAA,CAAAI,YAAA,EAAS;UACTJ,EAAA,CAAAE,cAAA,kBAAoI;UAAzBF,EAAA,CAAAkB,UAAA,mBAAAgH,oEAAA;YAAAlI,EAAA,CAAAoB,aAAA,CAAA0G,GAAA;YAAA,OAAA9H,EAAA,CAAAwB,WAAA,CAASkG,GAAA,CAAA5C,YAAA,EAAc;UAAA,EAAC;UACjI9E,EAAA,CAAAG,MAAA,IACF;;UACFH,EADE,CAAAI,YAAA,EAAS,EACU;UAErBJ,EAAA,CAAAe,UAAA,KAAAoH,yDAAA,gCAAAnI,EAAA,CAAAoI,sBAAA,CAAiC;;;UApE/BpI,EAAA,CAAAK,SAAA,EACF;UADEL,EAAA,CAAAM,kBAAA,MAAAN,EAAA,CAAAqI,WAAA,8DACF;UAOQrI,EAAA,CAAAK,SAAA,GACF;UADEL,EAAA,CAAAM,kBAAA,MAAAN,EAAA,CAAAsI,WAAA,4DAAAtI,EAAA,CAAAuI,eAAA,KAAAC,GAAA,EAAAd,GAAA,CAAApB,KAAA,CAAAzF,MAAA,QACF;UAGWb,EAAA,CAAAK,SAAA,GAA0B;UAA1BL,EAAA,CAAAS,UAAA,YAAAiH,GAAA,CAAAnE,cAAA,CAA0B;UAACvD,EAAA,CAAAyI,gBAAA,iBAAAf,GAAA,CAAA9G,KAAA,CAAwB;UACrCZ,EAAA,CAAAK,SAAA,EAAQ;UAARL,EAAA,CAAAS,UAAA,YAAAiH,GAAA,CAAA9G,KAAA,CAAQ;UAkCYZ,EAAA,CAAAK,SAAA,GAAQ;UAARL,EAAA,CAAAS,UAAA,YAAAiH,GAAA,CAAApB,KAAA,CAAQ;UAcvDtG,EAAA,CAAAK,SAAA,GACF;UADEL,EAAA,CAAAM,kBAAA,MAAAN,EAAA,CAAAqI,WAAA,+BACF;UAC8DrI,EAAA,CAAAK,SAAA,GAA2B;UAA3BL,EAAA,CAAAS,UAAA,cAAAiH,GAAA,CAAAzF,aAAA,CAA2B;UACvFjC,EAAA,CAAAK,SAAA,EACF;UADEL,EAAA,CAAAM,kBAAA,MAAAN,EAAA,CAAAqI,WAAA,oCACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}