{"ast": null, "code": "import { forwardRef } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { Subject } from 'rxjs';\nimport { filter, takeUntil, tap } from 'rxjs/operators';\nimport { BoConfirmationComponent } from 'src/app/common/components/bo-confirmation/bo-confirmation.component';\nimport { LobbyMenuItemsRibbonsModalComponent } from './lobby-menu-items-ribbons-modal/lobby-menu-items-ribbons-modal.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/material/icon\";\nimport * as i4 from \"@angular/material/button\";\nimport * as i5 from \"@angular/material/menu\";\nimport * as i6 from \"@angular/flex-layout/extended\";\nimport * as i7 from \"@ngx-translate/core\";\nconst _c0 = [\"ribbonLabel\"];\nconst _c1 = a0 => ({\n  \"value\": a0\n});\nconst _c2 = a0 => ({\n  \"up\": a0\n});\nfunction LobbyMenuItemsRibbonsComponent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 15, 2)(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.selectedRibbon.text);\n  }\n}\nfunction LobbyMenuItemsRibbonsComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c2, ctx_r1.selectedRibbon));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, ctx_r1.placeholder), \" \");\n  }\n}\nfunction LobbyMenuItemsRibbonsComponent_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function LobbyMenuItemsRibbonsComponent_button_4_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onClear($event));\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"clear\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction LobbyMenuItemsRibbonsComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵlistener(\"click\", function LobbyMenuItemsRibbonsComponent_div_24_Template_div_click_0_listener() {\n      const option_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSelect(option_r5));\n    });\n    i0.ɵɵelementStart(1, \"div\", 19)(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const option_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", ctx_r1.getRibbonBg(option_r5));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(option_r5.text);\n  }\n}\nexport class LobbyMenuItemsRibbonsComponent {\n  set ribbons(val) {\n    this.options = val;\n    this._ribbons = val;\n  }\n  get ribbons() {\n    return this._ribbons;\n  }\n  constructor(dialog) {\n    this.dialog = dialog;\n    this.placeholder = 'Ribbon';\n    this.options = [];\n    this._onChange = () => {};\n    this._ribbons = [];\n    this.destroyed$ = new Subject();\n    this.onTouched = () => {};\n  }\n  ngOnDestroy() {\n    this.destroyed$.next();\n    this.destroyed$.complete();\n  }\n  writeValue(value) {\n    this.changeRibbon(value);\n  }\n  setDisabledState(isDisabled) {\n    this.isDisabled = !!isDisabled;\n  }\n  registerOnChange(fn) {\n    this._onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  getRibbonBg(option) {\n    return {\n      'background': option.bg,\n      'color': option.color\n    };\n  }\n  onSelect(option) {\n    this.selectRibbon(option);\n  }\n  onClear(event) {\n    event.preventDefault();\n    event.stopPropagation();\n    this.selectRibbon(undefined);\n  }\n  openModal(ribbon) {\n    this.dialog.open(LobbyMenuItemsRibbonsModalComponent, {\n      width: '600px',\n      data: {\n        ribbon: ribbon,\n        maxLength: this.maxLength\n      },\n      disableClose: true\n    }).afterClosed().pipe(filter(data => !!data), takeUntil(this.destroyed$)).subscribe(val => {\n      this.ribbons.push(val);\n      this.selectRibbon(val);\n    });\n  }\n  remove() {\n    this.dialog.open(BoConfirmationComponent, {\n      width: '500px',\n      disableClose: true,\n      data: {\n        message: 'Are you sure you want to remove this ribbon?'\n      }\n    }).afterClosed().pipe(filter(isConfirmed => isConfirmed), tap(() => {\n      const ribbonToRemove = this.selectedRibbon;\n      this.ribbons = this.ribbons.filter(el => JSON.stringify(el) !== JSON.stringify(ribbonToRemove));\n      this.selectRibbon(undefined);\n    }), takeUntil(this.destroyed$)).subscribe();\n  }\n  selectRibbon(val) {\n    this.changeRibbon(val);\n    this._onChange(val);\n  }\n  changeRibbon(val) {\n    this.selectedRibbon = val;\n    this.options = val ? this.ribbons.filter(el => JSON.stringify(el) !== JSON.stringify(val)) : this.ribbons;\n    setTimeout(() => {\n      if (this.ribbonLabelRef) {\n        const label = this.ribbonLabelRef.nativeElement;\n        label.setAttribute('style', `background: ${val ? val.bg : ''}; color: ${val ? val.color : ''}`);\n      }\n    });\n  }\n  static {\n    this.ɵfac = function LobbyMenuItemsRibbonsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LobbyMenuItemsRibbonsComponent)(i0.ɵɵdirectiveInject(i1.MatDialog));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LobbyMenuItemsRibbonsComponent,\n      selectors: [[\"lobby-menu-items-ribbons\"]],\n      viewQuery: function LobbyMenuItemsRibbonsComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.ribbonLabelRef = _t.first);\n        }\n      },\n      inputs: {\n        ribbons: \"ribbons\",\n        maxLength: \"maxLength\",\n        placeholder: \"placeholder\"\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => LobbyMenuItemsRibbonsComponent),\n        multi: true\n      }])],\n      decls: 25,\n      vars: 11,\n      consts: [[\"menuActions\", \"matMenu\"], [\"menuRibbons\", \"matMenu\"], [\"ribbonLabel\", \"\"], [1, \"ribbons\", 3, \"ngClass\"], [1, \"ribbons__active\", 3, \"matMenuTriggerFor\"], [4, \"ngIf\"], [\"class\", \"ribbons__placeholder\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"ribbons__clear\", \"mat-icon-button\", \"\", 3, \"click\", 4, \"ngIf\"], [\"mat-icon-button\", \"\", 1, \"ribbons__menu\", 3, \"matMenuTriggerFor\"], [\"xPosition\", \"before\"], [\"mat-menu-item\", \"\", 3, \"click\"], [\"mat-menu-item\", \"\", 3, \"click\", \"disabled\"], [\"fontSet\", \"material-icons-outline\"], [\"xPosition\", \"after\"], [\"class\", \"ribbons__item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"ribbons__label\"], [1, \"ribbons__placeholder\", 3, \"ngClass\"], [\"mat-icon-button\", \"\", 1, \"ribbons__clear\", 3, \"click\"], [1, \"ribbons__item\", 3, \"click\"], [1, \"ribbons__label\", 3, \"ngStyle\"]],\n      template: function LobbyMenuItemsRibbonsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4);\n          i0.ɵɵtemplate(2, LobbyMenuItemsRibbonsComponent_ng_container_2_Template, 5, 1, \"ng-container\", 5)(3, LobbyMenuItemsRibbonsComponent_div_3_Template, 3, 6, \"div\", 6)(4, LobbyMenuItemsRibbonsComponent_button_4_Template, 3, 0, \"button\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"button\", 8)(6, \"mat-icon\");\n          i0.ɵɵtext(7, \"menu\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"mat-menu\", 9, 0)(10, \"button\", 10);\n          i0.ɵɵlistener(\"click\", function LobbyMenuItemsRibbonsComponent_Template_button_click_10_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.openModal());\n          });\n          i0.ɵɵelementStart(11, \"mat-icon\");\n          i0.ɵɵtext(12, \"playlist_add\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(13, \" Add new ribbon \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function LobbyMenuItemsRibbonsComponent_Template_button_click_14_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.openModal(ctx.selectedRibbon));\n          });\n          i0.ɵɵelementStart(15, \"mat-icon\");\n          i0.ɵɵtext(16, \"edit\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(17, \" Edit ribbon \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function LobbyMenuItemsRibbonsComponent_Template_button_click_18_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.remove());\n          });\n          i0.ɵɵelementStart(19, \"mat-icon\", 12);\n          i0.ɵɵtext(20, \"delete\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(21, \" Remove ribbon \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(22, \"mat-menu\", 13, 1);\n          i0.ɵɵtemplate(24, LobbyMenuItemsRibbonsComponent_div_24_Template, 4, 2, \"div\", 14);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          const menuActions_r6 = i0.ɵɵreference(9);\n          const menuRibbons_r7 = i0.ɵɵreference(23);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(9, _c1, ctx.selectedRibbon));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"matMenuTriggerFor\", menuRibbons_r7);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedRibbon);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.placeholder);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedRibbon);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"matMenuTriggerFor\", menuActions_r6);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"disabled\", !ctx.selectedRibbon);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"disabled\", !ctx.selectedRibbon);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngForOf\", ctx.options);\n        }\n      },\n      dependencies: [i2.NgClass, i2.NgForOf, i2.NgIf, i2.NgStyle, i3.MatIcon, i4.MatIconButton, i5.MatMenu, i5.MatMenuItem, i5.MatMenuTrigger, i6.DefaultClassDirective, i6.DefaultStyleDirective, i7.TranslatePipe],\n      styles: [\".ribbons[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  color: #333;\\n}\\n.ribbons__active[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  width: 100%;\\n  height: 44px;\\n  padding: 0 3.1em 0 0.75em;\\n  line-height: 1.5384616;\\n  color: #333333;\\n  background-image: none;\\n  border: 1px solid #ddd;\\n  border-radius: 5px;\\n  border-top-right-radius: 0;\\n  border-bottom-right-radius: 0;\\n  box-shadow: none;\\n  transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;\\n  cursor: pointer;\\n}\\n.ribbons__active[_ngcontent-%COMP%]:after {\\n  content: \\\"\\\\e9c5\\\";\\n  font-family: Icomoon;\\n  display: inline-block;\\n  position: absolute;\\n  top: 50%;\\n  right: 12px;\\n  margin-top: -8px;\\n  font-size: 16px;\\n  line-height: 1;\\n  color: inherit;\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n  transition: transform 0.15s ease-in-out;\\n}\\n.ribbons__active[_ngcontent-%COMP%]:hover {\\n  cursor: pointer;\\n}\\n.ribbons__dropdown[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.ribbons__item[_ngcontent-%COMP%] {\\n  width: 200px;\\n  padding: 6px 12px;\\n  cursor: pointer;\\n}\\n.ribbons__item[_ngcontent-%COMP%]:hover {\\n  background: #fafafa;\\n  transition: background-color 0.15s ease-in-out;\\n}\\n.ribbons__clear[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 24px;\\n  top: 8px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 20px;\\n  height: 20px;\\n  opacity: 0.9;\\n}\\n.ribbons__clear[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n}\\n.ribbons__clear[_ngcontent-%COMP%]:hover {\\n  cursor: pointer;\\n  opacity: 1;\\n  transition: opacity 0.15s ease-in-out;\\n}\\n.ribbons__label[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  height: 24px;\\n  width: 100%;\\n  padding: 0 4px;\\n  font-size: 12px;\\n  line-height: 1;\\n  text-transform: uppercase;\\n  border-radius: 2px;\\n}\\n.ribbons__label[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  display: block;\\n  width: 100%;\\n  overflow: hidden;\\n  white-space: nowrap;\\n  text-overflow: ellipsis;\\n}\\n.ribbons__action[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  flex-grow: 0;\\n  flex-shrink: 0;\\n  margin-left: -1px;\\n  margin-bottom: 0;\\n  padding: 0.4375rem 0.875rem;\\n  font-size: 0.8125rem;\\n  font-weight: 400;\\n  line-height: 1.5385;\\n  text-align: center;\\n  white-space: nowrap;\\n  border: 1px solid #ddd;\\n  border-radius: 0;\\n  cursor: pointer;\\n}\\n.ribbons__action[_ngcontent-%COMP%]:last-child {\\n  border-top-right-radius: 5px;\\n  border-bottom-right-radius: 5px;\\n}\\n.ribbons__placeholder[_ngcontent-%COMP%] {\\n  color: rgba(0, 0, 0, 0.6);\\n}\\n.ribbons__placeholder.up[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  position: absolute;\\n  left: 0.75em;\\n  top: -10px;\\n  background: #fff;\\n  padding: 0 4px;\\n}\\n.ribbons__menu[_ngcontent-%COMP%] {\\n  border: 1px solid #ddd;\\n  border-radius: 0;\\n  border-left: none;\\n  border-bottom-right-radius: 5px;\\n  border-top-right-radius: 5px;\\n}\\n.ribbons__clear[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n  line-height: 24px;\\n}\\n.ribbons__clear[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  width: 16px;\\n  height: 16px;\\n  font-size: 16px;\\n  line-height: 16px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["forwardRef", "NG_VALUE_ACCESSOR", "Subject", "filter", "takeUntil", "tap", "BoConfirmationComponent", "LobbyMenuItemsRibbonsModalComponent", "i0", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r1", "selectedRibbon", "text", "ɵɵproperty", "ɵɵpureFunction1", "_c2", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "placeholder", "ɵɵlistener", "LobbyMenuItemsRibbonsComponent_button_4_Template_button_click_0_listener", "$event", "ɵɵrestoreView", "_r3", "ɵɵnextContext", "ɵɵresetView", "onClear", "LobbyMenuItemsRibbonsComponent_div_24_Template_div_click_0_listener", "option_r5", "_r4", "$implicit", "onSelect", "getRibbonBg", "LobbyMenuItemsRibbonsComponent", "ribbons", "val", "options", "_ribbons", "constructor", "dialog", "_onChange", "destroyed$", "onTouched", "ngOnDestroy", "next", "complete", "writeValue", "value", "changeRibbon", "setDisabledState", "isDisabled", "registerOnChange", "fn", "registerOnTouched", "option", "bg", "color", "selectRibbon", "event", "preventDefault", "stopPropagation", "undefined", "openModal", "ribbon", "open", "width", "data", "max<PERSON><PERSON><PERSON>", "disableClose", "afterClosed", "pipe", "subscribe", "push", "remove", "message", "isConfirmed", "ribbonToRemove", "el", "JSON", "stringify", "setTimeout", "ribbonLabelRef", "label", "nativeElement", "setAttribute", "ɵɵdirectiveInject", "i1", "MatDialog", "selectors", "viewQuery", "LobbyMenuItemsRibbonsComponent_Query", "rf", "ctx", "provide", "useExisting", "multi", "decls", "vars", "consts", "template", "LobbyMenuItemsRibbonsComponent_Template", "ɵɵtemplate", "LobbyMenuItemsRibbonsComponent_ng_container_2_Template", "LobbyMenuItemsRibbonsComponent_div_3_Template", "LobbyMenuItemsRibbonsComponent_button_4_Template", "LobbyMenuItemsRibbonsComponent_Template_button_click_10_listener", "_r1", "LobbyMenuItemsRibbonsComponent_Template_button_click_14_listener", "LobbyMenuItemsRibbonsComponent_Template_button_click_18_listener", "LobbyMenuItemsRibbonsComponent_div_24_Template", "_c1", "menuRibbons_r7", "menuActions_r6"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/lobby/lobby-form/lobby-menu-items/lobby-menu-items-ribbons/lobby-menu-items-ribbons.component.ts", "/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/lobby/lobby-form/lobby-menu-items/lobby-menu-items-ribbons/lobby-menu-items-ribbons.component.html"], "sourcesContent": ["import { Component, ElementRef, forwardRef, Input, ViewChild } from '@angular/core';\nimport { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { MatDialog } from '@angular/material/dialog';\nimport { Subject } from 'rxjs';\nimport { filter, takeUntil, tap } from 'rxjs/operators';\nimport { BoConfirmationComponent } from 'src/app/common/components/bo-confirmation/bo-confirmation.component';\n\nimport { LobbyMenuItemRibbon } from '../../../lobby.model';\nimport { LobbyMenuItemsRibbonsModalComponent } from './lobby-menu-items-ribbons-modal/lobby-menu-items-ribbons-modal.component';\n\n@Component({\n  selector: 'lobby-menu-items-ribbons',\n  templateUrl: './lobby-menu-items-ribbons.component.html',\n  styleUrls: ['./lobby-menu-items-ribbons.component.scss'],\n  providers: [\n    {\n      provide: NG_VALUE_ACCESSOR,\n      useExisting: forwardRef(() => LobbyMenuItemsRibbonsComponent),\n      multi: true\n    }\n  ],\n})\n\nexport class LobbyMenuItemsRibbonsComponent implements ControlValueAccessor {\n  @Input()\n  set ribbons( val: LobbyMenuItemRibbon[] ) {\n    this.options = val;\n    this._ribbons = val;\n  }\n\n  get ribbons(): LobbyMenuItemRibbon[] {\n    return this._ribbons;\n  }\n\n  @Input() maxLength: number;\n  @Input() placeholder = 'Ribbon';\n\n  isDisabled: boolean;\n  selectedRibbon?: LobbyMenuItemRibbon;\n  options: LobbyMenuItemRibbon[] = [];\n\n  @ViewChild('ribbonLabel') ribbonLabelRef: ElementRef | undefined;\n\n  private _onChange: ( _: any ) => void = (() => {\n  });\n  private _ribbons: LobbyMenuItemRibbon[] = [];\n  private readonly destroyed$ = new Subject<void>();\n\n  constructor( private dialog: MatDialog ) {\n  }\n\n  ngOnDestroy() {\n    this.destroyed$.next();\n    this.destroyed$.complete();\n  }\n\n  writeValue( value: LobbyMenuItemRibbon | undefined ) {\n    this.changeRibbon(value);\n  }\n\n  onTouched: any = () => {\n  }\n\n  setDisabledState( isDisabled: boolean ): void {\n    this.isDisabled = !!isDisabled;\n  }\n\n  registerOnChange( fn: ( _: any ) => void ): void {\n    this._onChange = fn;\n  }\n\n  registerOnTouched( fn: any ): void {\n    this.onTouched = fn;\n  }\n\n  getRibbonBg( option: LobbyMenuItemRibbon ): { [key: string]: string } {\n    return {\n      'background': option.bg,\n      'color': option.color\n    };\n  }\n\n  onSelect( option: LobbyMenuItemRibbon ) {\n    this.selectRibbon(option);\n  }\n\n  onClear( event: Event ) {\n    event.preventDefault();\n    event.stopPropagation();\n    this.selectRibbon(undefined);\n  }\n\n  openModal( ribbon?: LobbyMenuItemRibbon ) {\n    this.dialog.open(LobbyMenuItemsRibbonsModalComponent, {\n      width: '600px',\n      data: {\n        ribbon: ribbon,\n        maxLength: this.maxLength\n      },\n      disableClose: true\n    }).afterClosed()\n      .pipe(\n        filter(data => !!data),\n        takeUntil(this.destroyed$)\n      ).subscribe(( val: LobbyMenuItemRibbon ) => {\n      this.ribbons.push(val);\n      this.selectRibbon(val);\n    });\n  }\n\n  remove() {\n    this.dialog.open(BoConfirmationComponent, {\n      width: '500px',\n      disableClose: true,\n      data: { message: 'Are you sure you want to remove this ribbon?' }\n    }).afterClosed()\n      .pipe(\n        filter(isConfirmed => isConfirmed),\n        tap(() => {\n          const ribbonToRemove = this.selectedRibbon;\n          this.ribbons = this.ribbons.filter(( el: LobbyMenuItemRibbon ) =>\n            JSON.stringify(el) !== JSON.stringify(ribbonToRemove));\n          this.selectRibbon(undefined);\n        }),\n        takeUntil(this.destroyed$)\n      ).subscribe();\n  }\n\n  private selectRibbon( val: LobbyMenuItemRibbon ): void {\n    this.changeRibbon(val);\n    this._onChange(val);\n  }\n\n  private changeRibbon( val: LobbyMenuItemRibbon ): void {\n    this.selectedRibbon = val;\n\n    this.options = val\n      ? this.ribbons.filter(el => JSON.stringify(el) !== JSON.stringify(val))\n      : this.ribbons;\n\n    setTimeout(() => {\n      if (this.ribbonLabelRef) {\n        const label = this.ribbonLabelRef.nativeElement as HTMLElement;\n        label.setAttribute('style', `background: ${val ? val.bg : ''}; color: ${val ? val.color : ''}`);\n      }\n    });\n  }\n}\n", "<div class=\"ribbons\" [ngClass]=\"{'value': selectedRibbon}\">\n  <div [matMenuTriggerFor]=\"menuRibbons\" class=\"ribbons__active\">\n    <ng-container *ngIf=\"selectedRibbon\">\n      <div #ribbonLabel class=\"ribbons__label\">\n        <span>{{selectedRibbon.text}}</span>\n      </div>\n    </ng-container>\n    <div *ngIf=\"placeholder\" class=\"ribbons__placeholder\" [ngClass]=\"{'up': selectedRibbon}\">\n      {{placeholder | translate}}\n    </div>\n    <button class=\"ribbons__clear\" mat-icon-button *ngIf=\"selectedRibbon\" (click)=\"onClear($event)\">\n      <mat-icon>clear</mat-icon>\n    </button>\n  </div>\n\n  <button mat-icon-button class=\"ribbons__menu\" [matMenuTriggerFor]=\"menuActions\">\n    <mat-icon>menu</mat-icon>\n  </button>\n\n  <mat-menu #menuActions=\"matMenu\" xPosition=\"before\">\n    <button mat-menu-item (click)=\"openModal()\">\n      <mat-icon>playlist_add</mat-icon>\n      Add new ribbon\n    </button>\n\n    <button mat-menu-item [disabled]=\"!selectedRibbon\" (click)=\"openModal(selectedRibbon)\">\n      <mat-icon>edit</mat-icon>\n      Edit ribbon\n    </button>\n    <button mat-menu-item [disabled]=\"!selectedRibbon\" (click)=\"remove()\">\n      <mat-icon fontSet=\"material-icons-outline\">delete</mat-icon>\n      Remove ribbon\n    </button>\n  </mat-menu>\n\n  <mat-menu #menuRibbons=\"matMenu\" xPosition=\"after\">\n    <div *ngFor=\"let option of options\" class=\"ribbons__item\" (click)=\"onSelect(option)\">\n      <div class=\"ribbons__label \" [ngStyle]=\"getRibbonBg(option)\">\n        <span>{{option.text}}</span>\n      </div>\n    </div>\n  </mat-menu>\n</div>\n"], "mappings": "AAAA,SAAgCA,UAAU,QAA0B,eAAe;AACnF,SAA+BC,iBAAiB,QAAQ,gBAAgB;AAExE,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,MAAM,EAAEC,SAAS,EAAEC,GAAG,QAAQ,gBAAgB;AACvD,SAASC,uBAAuB,QAAQ,qEAAqE;AAG7G,SAASC,mCAAmC,QAAQ,2EAA2E;;;;;;;;;;;;;;;;;;ICN3HC,EAAA,CAAAC,uBAAA,GAAqC;IAEjCD,EADF,CAAAE,cAAA,iBAAyC,WACjC;IAAAF,EAAA,CAAAG,MAAA,GAAuB;IAC/BH,EAD+B,CAAAI,YAAA,EAAO,EAChC;;;;;IADEJ,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,cAAA,CAAAC,IAAA,CAAuB;;;;;IAGjCT,EAAA,CAAAE,cAAA,cAAyF;IACvFF,EAAA,CAAAG,MAAA,GACF;;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IAFgDJ,EAAA,CAAAU,UAAA,YAAAV,EAAA,CAAAW,eAAA,IAAAC,GAAA,EAAAL,MAAA,CAAAC,cAAA,EAAkC;IACtFR,EAAA,CAAAK,SAAA,EACF;IADEL,EAAA,CAAAa,kBAAA,MAAAb,EAAA,CAAAc,WAAA,OAAAP,MAAA,CAAAQ,WAAA,OACF;;;;;;IACAf,EAAA,CAAAE,cAAA,iBAAgG;IAA1BF,EAAA,CAAAgB,UAAA,mBAAAC,yEAAAC,MAAA;MAAAlB,EAAA,CAAAmB,aAAA,CAAAC,GAAA;MAAA,MAAAb,MAAA,GAAAP,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAASf,MAAA,CAAAgB,OAAA,CAAAL,MAAA,CAAe;IAAA,EAAC;IAC7FlB,EAAA,CAAAE,cAAA,eAAU;IAAAF,EAAA,CAAAG,MAAA,YAAK;IACjBH,EADiB,CAAAI,YAAA,EAAW,EACnB;;;;;;IAwBTJ,EAAA,CAAAE,cAAA,cAAqF;IAA3BF,EAAA,CAAAgB,UAAA,mBAAAQ,oEAAA;MAAA,MAAAC,SAAA,GAAAzB,EAAA,CAAAmB,aAAA,CAAAO,GAAA,EAAAC,SAAA;MAAA,MAAApB,MAAA,GAAAP,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAASf,MAAA,CAAAqB,QAAA,CAAAH,SAAA,CAAgB;IAAA,EAAC;IAEhFzB,EADF,CAAAE,cAAA,cAA6D,WACrD;IAAAF,EAAA,CAAAG,MAAA,GAAe;IAEzBH,EAFyB,CAAAI,YAAA,EAAO,EACxB,EACF;;;;;IAHyBJ,EAAA,CAAAK,SAAA,EAA+B;IAA/BL,EAAA,CAAAU,UAAA,YAAAH,MAAA,CAAAsB,WAAA,CAAAJ,SAAA,EAA+B;IACpDzB,EAAA,CAAAK,SAAA,GAAe;IAAfL,EAAA,CAAAM,iBAAA,CAAAmB,SAAA,CAAAhB,IAAA,CAAe;;;ADf7B,OAAM,MAAOqB,8BAA8B;EACzC,IACIC,OAAOA,CAAEC,GAA0B;IACrC,IAAI,CAACC,OAAO,GAAGD,GAAG;IAClB,IAAI,CAACE,QAAQ,GAAGF,GAAG;EACrB;EAEA,IAAID,OAAOA,CAAA;IACT,OAAO,IAAI,CAACG,QAAQ;EACtB;EAgBAC,YAAqBC,MAAiB;IAAjB,KAAAA,MAAM,GAANA,MAAM;IAblB,KAAArB,WAAW,GAAG,QAAQ;IAI/B,KAAAkB,OAAO,GAA0B,EAAE;IAI3B,KAAAI,SAAS,GAAwB,MAAK,CAC9C,CAAE;IACM,KAAAH,QAAQ,GAA0B,EAAE;IAC3B,KAAAI,UAAU,GAAG,IAAI5C,OAAO,EAAQ;IAcjD,KAAA6C,SAAS,GAAQ,MAAK,CACtB,CAAC;EAZD;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACF,UAAU,CAACG,IAAI,EAAE;IACtB,IAAI,CAACH,UAAU,CAACI,QAAQ,EAAE;EAC5B;EAEAC,UAAUA,CAAEC,KAAsC;IAChD,IAAI,CAACC,YAAY,CAACD,KAAK,CAAC;EAC1B;EAKAE,gBAAgBA,CAAEC,UAAmB;IACnC,IAAI,CAACA,UAAU,GAAG,CAAC,CAACA,UAAU;EAChC;EAEAC,gBAAgBA,CAAEC,EAAsB;IACtC,IAAI,CAACZ,SAAS,GAAGY,EAAE;EACrB;EAEAC,iBAAiBA,CAAED,EAAO;IACxB,IAAI,CAACV,SAAS,GAAGU,EAAE;EACrB;EAEApB,WAAWA,CAAEsB,MAA2B;IACtC,OAAO;MACL,YAAY,EAAEA,MAAM,CAACC,EAAE;MACvB,OAAO,EAAED,MAAM,CAACE;KACjB;EACH;EAEAzB,QAAQA,CAAEuB,MAA2B;IACnC,IAAI,CAACG,YAAY,CAACH,MAAM,CAAC;EAC3B;EAEA5B,OAAOA,CAAEgC,KAAY;IACnBA,KAAK,CAACC,cAAc,EAAE;IACtBD,KAAK,CAACE,eAAe,EAAE;IACvB,IAAI,CAACH,YAAY,CAACI,SAAS,CAAC;EAC9B;EAEAC,SAASA,CAAEC,MAA4B;IACrC,IAAI,CAACxB,MAAM,CAACyB,IAAI,CAAC9D,mCAAmC,EAAE;MACpD+D,KAAK,EAAE,OAAO;MACdC,IAAI,EAAE;QACJH,MAAM,EAAEA,MAAM;QACdI,SAAS,EAAE,IAAI,CAACA;OACjB;MACDC,YAAY,EAAE;KACf,CAAC,CAACC,WAAW,EAAE,CACbC,IAAI,CACHxE,MAAM,CAACoE,IAAI,IAAI,CAAC,CAACA,IAAI,CAAC,EACtBnE,SAAS,CAAC,IAAI,CAAC0C,UAAU,CAAC,CAC3B,CAAC8B,SAAS,CAAGpC,GAAwB,IAAK;MAC3C,IAAI,CAACD,OAAO,CAACsC,IAAI,CAACrC,GAAG,CAAC;MACtB,IAAI,CAACsB,YAAY,CAACtB,GAAG,CAAC;IACxB,CAAC,CAAC;EACJ;EAEAsC,MAAMA,CAAA;IACJ,IAAI,CAAClC,MAAM,CAACyB,IAAI,CAAC/D,uBAAuB,EAAE;MACxCgE,KAAK,EAAE,OAAO;MACdG,YAAY,EAAE,IAAI;MAClBF,IAAI,EAAE;QAAEQ,OAAO,EAAE;MAA8C;KAChE,CAAC,CAACL,WAAW,EAAE,CACbC,IAAI,CACHxE,MAAM,CAAC6E,WAAW,IAAIA,WAAW,CAAC,EAClC3E,GAAG,CAAC,MAAK;MACP,MAAM4E,cAAc,GAAG,IAAI,CAACjE,cAAc;MAC1C,IAAI,CAACuB,OAAO,GAAG,IAAI,CAACA,OAAO,CAACpC,MAAM,CAAG+E,EAAuB,IAC1DC,IAAI,CAACC,SAAS,CAACF,EAAE,CAAC,KAAKC,IAAI,CAACC,SAAS,CAACH,cAAc,CAAC,CAAC;MACxD,IAAI,CAACnB,YAAY,CAACI,SAAS,CAAC;IAC9B,CAAC,CAAC,EACF9D,SAAS,CAAC,IAAI,CAAC0C,UAAU,CAAC,CAC3B,CAAC8B,SAAS,EAAE;EACjB;EAEQd,YAAYA,CAAEtB,GAAwB;IAC5C,IAAI,CAACa,YAAY,CAACb,GAAG,CAAC;IACtB,IAAI,CAACK,SAAS,CAACL,GAAG,CAAC;EACrB;EAEQa,YAAYA,CAAEb,GAAwB;IAC5C,IAAI,CAACxB,cAAc,GAAGwB,GAAG;IAEzB,IAAI,CAACC,OAAO,GAAGD,GAAG,GACd,IAAI,CAACD,OAAO,CAACpC,MAAM,CAAC+E,EAAE,IAAIC,IAAI,CAACC,SAAS,CAACF,EAAE,CAAC,KAAKC,IAAI,CAACC,SAAS,CAAC5C,GAAG,CAAC,CAAC,GACrE,IAAI,CAACD,OAAO;IAEhB8C,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACC,cAAc,EAAE;QACvB,MAAMC,KAAK,GAAG,IAAI,CAACD,cAAc,CAACE,aAA4B;QAC9DD,KAAK,CAACE,YAAY,CAAC,OAAO,EAAE,eAAejD,GAAG,GAAGA,GAAG,CAACoB,EAAE,GAAG,EAAE,YAAYpB,GAAG,GAAGA,GAAG,CAACqB,KAAK,GAAG,EAAE,EAAE,CAAC;MACjG;IACF,CAAC,CAAC;EACJ;;;uCA3HWvB,8BAA8B,EAAA9B,EAAA,CAAAkF,iBAAA,CAAAC,EAAA,CAAAC,SAAA;IAAA;EAAA;;;YAA9BtD,8BAA8B;MAAAuD,SAAA;MAAAC,SAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;uCAT9B,CACT;QACEE,OAAO,EAAEjG,iBAAiB;QAC1BkG,WAAW,EAAEnG,UAAU,CAAC,MAAMsC,8BAA8B,CAAC;QAC7D8D,KAAK,EAAE;OACR,CACF;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wCAAAT,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCnBDxF,EADF,CAAAE,cAAA,aAA2D,aACM;UAS7DF,EARA,CAAAkG,UAAA,IAAAC,sDAAA,0BAAqC,IAAAC,6CAAA,iBAKoD,IAAAC,gDAAA,oBAGO;UAGlGrG,EAAA,CAAAI,YAAA,EAAM;UAGJJ,EADF,CAAAE,cAAA,gBAAgF,eACpE;UAAAF,EAAA,CAAAG,MAAA,WAAI;UAChBH,EADgB,CAAAI,YAAA,EAAW,EAClB;UAGPJ,EADF,CAAAE,cAAA,qBAAoD,kBACN;UAAtBF,EAAA,CAAAgB,UAAA,mBAAAsF,iEAAA;YAAAtG,EAAA,CAAAmB,aAAA,CAAAoF,GAAA;YAAA,OAAAvG,EAAA,CAAAsB,WAAA,CAASmE,GAAA,CAAA9B,SAAA,EAAW;UAAA,EAAC;UACzC3D,EAAA,CAAAE,cAAA,gBAAU;UAAAF,EAAA,CAAAG,MAAA,oBAAY;UAAAH,EAAA,CAAAI,YAAA,EAAW;UACjCJ,EAAA,CAAAG,MAAA,wBACF;UAAAH,EAAA,CAAAI,YAAA,EAAS;UAETJ,EAAA,CAAAE,cAAA,kBAAuF;UAApCF,EAAA,CAAAgB,UAAA,mBAAAwF,iEAAA;YAAAxG,EAAA,CAAAmB,aAAA,CAAAoF,GAAA;YAAA,OAAAvG,EAAA,CAAAsB,WAAA,CAASmE,GAAA,CAAA9B,SAAA,CAAA8B,GAAA,CAAAjF,cAAA,CAAyB;UAAA,EAAC;UACpFR,EAAA,CAAAE,cAAA,gBAAU;UAAAF,EAAA,CAAAG,MAAA,YAAI;UAAAH,EAAA,CAAAI,YAAA,EAAW;UACzBJ,EAAA,CAAAG,MAAA,qBACF;UAAAH,EAAA,CAAAI,YAAA,EAAS;UACTJ,EAAA,CAAAE,cAAA,kBAAsE;UAAnBF,EAAA,CAAAgB,UAAA,mBAAAyF,iEAAA;YAAAzG,EAAA,CAAAmB,aAAA,CAAAoF,GAAA;YAAA,OAAAvG,EAAA,CAAAsB,WAAA,CAASmE,GAAA,CAAAnB,MAAA,EAAQ;UAAA,EAAC;UACnEtE,EAAA,CAAAE,cAAA,oBAA2C;UAAAF,EAAA,CAAAG,MAAA,cAAM;UAAAH,EAAA,CAAAI,YAAA,EAAW;UAC5DJ,EAAA,CAAAG,MAAA,uBACF;UACFH,EADE,CAAAI,YAAA,EAAS,EACA;UAEXJ,EAAA,CAAAE,cAAA,uBAAmD;UACjDF,EAAA,CAAAkG,UAAA,KAAAQ,8CAAA,kBAAqF;UAMzF1G,EADE,CAAAI,YAAA,EAAW,EACP;;;;;UA1CeJ,EAAA,CAAAU,UAAA,YAAAV,EAAA,CAAAW,eAAA,IAAAgG,GAAA,EAAAlB,GAAA,CAAAjF,cAAA,EAAqC;UACnDR,EAAA,CAAAK,SAAA,EAAiC;UAAjCL,EAAA,CAAAU,UAAA,sBAAAkG,cAAA,CAAiC;UACrB5G,EAAA,CAAAK,SAAA,EAAoB;UAApBL,EAAA,CAAAU,UAAA,SAAA+E,GAAA,CAAAjF,cAAA,CAAoB;UAK7BR,EAAA,CAAAK,SAAA,EAAiB;UAAjBL,EAAA,CAAAU,UAAA,SAAA+E,GAAA,CAAA1E,WAAA,CAAiB;UAGyBf,EAAA,CAAAK,SAAA,EAAoB;UAApBL,EAAA,CAAAU,UAAA,SAAA+E,GAAA,CAAAjF,cAAA,CAAoB;UAKxBR,EAAA,CAAAK,SAAA,EAAiC;UAAjCL,EAAA,CAAAU,UAAA,sBAAAmG,cAAA,CAAiC;UAUvD7G,EAAA,CAAAK,SAAA,GAA4B;UAA5BL,EAAA,CAAAU,UAAA,cAAA+E,GAAA,CAAAjF,cAAA,CAA4B;UAI5BR,EAAA,CAAAK,SAAA,GAA4B;UAA5BL,EAAA,CAAAU,UAAA,cAAA+E,GAAA,CAAAjF,cAAA,CAA4B;UAO1BR,EAAA,CAAAK,SAAA,GAAU;UAAVL,EAAA,CAAAU,UAAA,YAAA+E,GAAA,CAAAxD,OAAA,CAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}