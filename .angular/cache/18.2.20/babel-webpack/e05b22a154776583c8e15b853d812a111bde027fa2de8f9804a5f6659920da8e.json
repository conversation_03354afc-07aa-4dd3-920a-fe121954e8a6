{"ast": null, "code": "export class CheckboxItem {\n  constructor() {\n    this.checked = false;\n  }\n  toggleCheck() {\n    this.checked = !this.checked;\n  }\n}\nexport const gameSelectItemTypes = {\n  GAME: 'game',\n  PROVIDER: 'provider',\n  LABEL: 'label',\n  INTERSECTION: 'intersection',\n  CORRUPTION: 'corruption'\n};\nexport const getUniqueLabelGames = labels => {\n  let codes = []; // array of codes is required for determining unique games\n  return labels.reduce((games, label) => {\n    let labelGames = label.items.filter(({\n      data\n    }) => {\n      let code = data.code;\n      return codes.indexOf(code) === -1;\n    });\n    codes = [...codes, ...labelGames.map(game => game.data['code'])];\n    return [...games, ...labelGames];\n  }, []);\n};\nexport const getIntersectedGames = labels => {\n  let labelIds = labels.map(label => label.data.id);\n  return getUniqueLabelGames(labels).filter(game => labelIds.every(id => game.data['labels'].map(l => l.id).indexOf(id) > -1));\n};\nexport class GameSelectItem extends CheckboxItem {\n  static createFromGameInfo(game) {\n    if (!game.labels.find(label => label.id === game.providerCode)) {\n      game.labels.push({\n        id: game.providerCode,\n        title: game.providerTitle,\n        group: 'provider'\n      }); // adding provider as label\n    }\n    return new GameSelectItem({\n      id: game.code,\n      type: gameSelectItemTypes.GAME,\n      data: game\n    });\n  }\n  static convertToGameInfo(item) {\n    return item.data;\n  }\n  static createFromLabel(label) {\n    return new GameSelectItem({\n      id: label.id,\n      type: label.group === 'provider' ? gameSelectItemTypes.PROVIDER : gameSelectItemTypes.LABEL,\n      data: label\n    });\n  }\n  static createLabelIntersection(items) {\n    const allowedIntersectionItems = [gameSelectItemTypes.PROVIDER, gameSelectItemTypes.LABEL];\n    let intersectionItems = items.filter(item => allowedIntersectionItems.indexOf(item.type) > -1);\n    return new GameSelectItem({\n      type: gameSelectItemTypes.INTERSECTION,\n      items: intersectionItems,\n      data: {\n        games: getIntersectedGames(intersectionItems)\n      }\n    });\n  }\n  constructor(obj) {\n    super();\n    if ('id' in obj) {\n      this.id = obj.id;\n    }\n    this.type = obj.type;\n    this.data = obj.data || {};\n    if (this.type === gameSelectItemTypes.INTERSECTION && obj.items) {\n      this.setIntersectionItems(obj.items);\n    } else {\n      this.items = [];\n    }\n  }\n  toCategoryItem() {\n    let allowedChildrenTypes = [gameSelectItemTypes.PROVIDER, gameSelectItemTypes.LABEL];\n    let item = {\n      id: this.id,\n      type: this.type\n    };\n    if (typeof item.id === 'undefined') {\n      delete item.id;\n    }\n    if (this.items && this.items.length) {\n      let items = this.items.filter(child => allowedChildrenTypes.indexOf(child.type) > -1).map(child => child.toCategoryItem());\n      if (items.length) {\n        item = Object.assign({}, item, {\n          items\n        });\n      }\n    }\n    return item;\n  }\n  addGameToLabel(game) {\n    let typesWithGames = [gameSelectItemTypes.LABEL, gameSelectItemTypes.PROVIDER];\n    if (typesWithGames.indexOf(this.type) > -1) {\n      this.items.push(game);\n    }\n  }\n  getGameLabels() {\n    if (this.type !== gameSelectItemTypes.GAME) return;\n    return this.data.labels;\n  }\n  get title() {\n    let title = '';\n    if ('title' in this.data) {\n      title = this.data['title'];\n    }\n    return title;\n  }\n  setIntersectionItems(items) {\n    this.items = items;\n  }\n}\nexport const isLabel = item => {\n  return [gameSelectItemTypes.LABEL, gameSelectItemTypes.PROVIDER].indexOf(item.type) > -1;\n};", "map": {"version": 3, "names": ["CheckboxItem", "constructor", "checked", "to<PERSON><PERSON><PERSON><PERSON>", "gameSelectItemTypes", "GAME", "PROVIDER", "LABEL", "INTERSECTION", "CORRUPTION", "getUniqueLabelGames", "labels", "codes", "reduce", "games", "label", "labelGames", "items", "filter", "data", "code", "indexOf", "map", "game", "getIntersectedGames", "labelIds", "id", "every", "l", "GameSelectItem", "createFromGameInfo", "find", "providerCode", "push", "title", "providerTitle", "group", "type", "convertToGameInfo", "item", "createFromLabel", "createLabelIntersection", "allowedIntersectionItems", "intersectionItems", "obj", "setIntersectionItems", "toCategoryItem", "allowedChildrenTypes", "length", "child", "Object", "assign", "addGameToLabel", "typesWithGames", "getGameLabels", "isLabel"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/games-categories-management/game-category-details/games-select-manager/game-select-item.model.ts"], "sourcesContent": ["import { GameInfo, GameLabel, Label } from '../../../../common/typings';\nimport { GameCategoryItem } from '../../game-category.model';\n\nexport interface IntersectionData {\n  games: GameSelectItem[];\n}\n\nexport class CheckboxItem {\n  checked: boolean;\n\n  constructor() {\n    this.checked = false;\n  }\n\n  toggleCheck() {\n    this.checked = !this.checked;\n  }\n}\n\nexport interface GameSelectItemTypes {\n  GAME: gameSelectItemType;\n  PROVIDER: gameSelectItemType;\n  LABEL: gameSelectItemType;\n  INTERSECTION: gameSelectItemType;\n  CORRUPTION: gameSelectItemType;\n}\n\nexport type gameSelectItemType = 'game' | 'provider' | 'label' | 'intersection' | 'corruption';\n\nexport const gameSelectItemTypes: GameSelectItemTypes = {\n  GAME: 'game',\n  PROVIDER: 'provider',\n  LABEL: 'label',\n  INTERSECTION: 'intersection',\n  CORRUPTION: 'corruption'\n};\n\nexport const getUniqueLabelGames = (labels: GameSelectItem[]): GameSelectItem[] => {\n  let codes = []; // array of codes is required for determining unique games\n\n  return labels.reduce((games, label) => {\n    let labelGames = label.items.filter(({ data }) => {\n      let code = (data as GameInfo).code;\n      return codes.indexOf(code) === -1;\n    });\n    codes = [...codes, ...labelGames.map(game => game.data['code'])];\n    return [...games, ...labelGames];\n  }, []);\n};\n\nexport const getIntersectedGames = (labels: GameSelectItem[]): GameSelectItem[] => {\n  let labelIds = labels.map(label => (label.data as Label).id);\n\n  return getUniqueLabelGames(labels)\n    .filter((game) => labelIds.every(id => game.data['labels'].map(l => l.id).indexOf(id) > -1));\n};\n\nexport class GameSelectItem extends CheckboxItem {\n\n  id?: string;\n  type: gameSelectItemType;\n  items?: GameSelectItem[]; // for labels and intersections\n  data?: Object; // can be GameInfo, Label, Provider, etc...\n\n  static createFromGameInfo(game: GameInfo): GameSelectItem {\n\n    if (!game.labels.find(label => label.id === game.providerCode)) {\n      game.labels.push({\n        id: game.providerCode,\n        title: game.providerTitle,\n        group: 'provider',\n      }); // adding provider as label\n    }\n\n    return new GameSelectItem({\n      id: game.code,\n      type: gameSelectItemTypes.GAME,\n      data: game,\n    });\n  }\n\n  static convertToGameInfo(item: GameSelectItem): GameInfo {\n    return item.data as GameInfo;\n  }\n\n  static createFromLabel(label: Label): GameSelectItem {\n    return new GameSelectItem({\n      id: label.id,\n      type: label.group === 'provider' ? gameSelectItemTypes.PROVIDER : gameSelectItemTypes.LABEL,\n      data: label,\n    });\n  }\n\n  static createLabelIntersection(items: GameSelectItem[]): GameSelectItem {\n    const allowedIntersectionItems = [ // we're not supporting nested intersections, only labels and providers\n      gameSelectItemTypes.PROVIDER,\n      gameSelectItemTypes.LABEL\n    ];\n\n    let intersectionItems = items.filter((item) => allowedIntersectionItems.indexOf(item.type) > -1);\n\n    return new GameSelectItem({\n      type: gameSelectItemTypes.INTERSECTION,\n      items: intersectionItems,\n      data: {\n        games: getIntersectedGames(intersectionItems)\n      }\n    });\n  }\n\n  constructor(obj) {\n    super();\n\n    if ('id' in obj) {\n      this.id = obj.id;\n    }\n\n    this.type = obj.type;\n    this.data = obj.data || {};\n\n    if (this.type === gameSelectItemTypes.INTERSECTION && obj.items) {\n      this.setIntersectionItems(obj.items);\n    } else {\n      this.items = [];\n    }\n  }\n\n  public toCategoryItem(): GameCategoryItem {\n    let allowedChildrenTypes = [\n      gameSelectItemTypes.PROVIDER,\n      gameSelectItemTypes.LABEL\n    ];\n\n    let item = {\n      id: this.id,\n      type: this.type\n    };\n\n    if (typeof item.id === 'undefined') {\n      delete item.id;\n    }\n\n    if (this.items && this.items.length) {\n      let items = this.items\n        .filter(child => allowedChildrenTypes.indexOf(child.type) > -1)\n        .map((child: GameSelectItem) => child.toCategoryItem()) as GameCategoryItem[];\n\n      if (items.length) {\n        item = Object.assign({}, item, { items });\n      }\n    }\n    return <GameCategoryItem>item;\n  }\n\n  public addGameToLabel(game: GameSelectItem) {\n    let typesWithGames = [\n      gameSelectItemTypes.LABEL,\n      gameSelectItemTypes.PROVIDER,\n    ];\n\n    if (typesWithGames.indexOf(this.type) > -1) {\n      this.items.push(game);\n    }\n  }\n\n  public getGameLabels(): GameLabel[] {\n    if (this.type !== gameSelectItemTypes.GAME) return;\n\n    return (this.data as GameInfo).labels;\n  }\n\n  public get title(): string {\n    let title: string = '';\n    if ('title' in this.data) {\n      title = this.data['title'] as string;\n    }\n    return title;\n  }\n\n  private setIntersectionItems(items: GameSelectItem[]) {\n    this.items = items;\n  }\n\n}\n\nexport const isLabel = (item: GameSelectItem): boolean => {\n  return [\n    gameSelectItemTypes.LABEL,\n    gameSelectItemTypes.PROVIDER,\n  ].indexOf(item.type) > -1;\n};\n"], "mappings": "AAOA,OAAM,MAAOA,YAAY;EAGvBC,YAAA;IACE,IAAI,CAACC,OAAO,GAAG,KAAK;EACtB;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACD,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;EAC9B;;AAaF,OAAO,MAAME,mBAAmB,GAAwB;EACtDC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,UAAU;EACpBC,KAAK,EAAE,OAAO;EACdC,YAAY,EAAE,cAAc;EAC5BC,UAAU,EAAE;CACb;AAED,OAAO,MAAMC,mBAAmB,GAAIC,MAAwB,IAAsB;EAChF,IAAIC,KAAK,GAAG,EAAE,CAAC,CAAC;EAEhB,OAAOD,MAAM,CAACE,MAAM,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAI;IACpC,IAAIC,UAAU,GAAGD,KAAK,CAACE,KAAK,CAACC,MAAM,CAAC,CAAC;MAAEC;IAAI,CAAE,KAAI;MAC/C,IAAIC,IAAI,GAAID,IAAiB,CAACC,IAAI;MAClC,OAAOR,KAAK,CAACS,OAAO,CAACD,IAAI,CAAC,KAAK,CAAC,CAAC;IACnC,CAAC,CAAC;IACFR,KAAK,GAAG,CAAC,GAAGA,KAAK,EAAE,GAAGI,UAAU,CAACM,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACJ,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IAChE,OAAO,CAAC,GAAGL,KAAK,EAAE,GAAGE,UAAU,CAAC;EAClC,CAAC,EAAE,EAAE,CAAC;AACR,CAAC;AAED,OAAO,MAAMQ,mBAAmB,GAAIb,MAAwB,IAAsB;EAChF,IAAIc,QAAQ,GAAGd,MAAM,CAACW,GAAG,CAACP,KAAK,IAAKA,KAAK,CAACI,IAAc,CAACO,EAAE,CAAC;EAE5D,OAAOhB,mBAAmB,CAACC,MAAM,CAAC,CAC/BO,MAAM,CAAEK,IAAI,IAAKE,QAAQ,CAACE,KAAK,CAACD,EAAE,IAAIH,IAAI,CAACJ,IAAI,CAAC,QAAQ,CAAC,CAACG,GAAG,CAACM,CAAC,IAAIA,CAAC,CAACF,EAAE,CAAC,CAACL,OAAO,CAACK,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAChG,CAAC;AAED,OAAM,MAAOG,cAAe,SAAQ7B,YAAY;EAO9C,OAAO8B,kBAAkBA,CAACP,IAAc;IAEtC,IAAI,CAACA,IAAI,CAACZ,MAAM,CAACoB,IAAI,CAAChB,KAAK,IAAIA,KAAK,CAACW,EAAE,KAAKH,IAAI,CAACS,YAAY,CAAC,EAAE;MAC9DT,IAAI,CAACZ,MAAM,CAACsB,IAAI,CAAC;QACfP,EAAE,EAAEH,IAAI,CAACS,YAAY;QACrBE,KAAK,EAAEX,IAAI,CAACY,aAAa;QACzBC,KAAK,EAAE;OACR,CAAC,CAAC,CAAC;IACN;IAEA,OAAO,IAAIP,cAAc,CAAC;MACxBH,EAAE,EAAEH,IAAI,CAACH,IAAI;MACbiB,IAAI,EAAEjC,mBAAmB,CAACC,IAAI;MAC9Bc,IAAI,EAAEI;KACP,CAAC;EACJ;EAEA,OAAOe,iBAAiBA,CAACC,IAAoB;IAC3C,OAAOA,IAAI,CAACpB,IAAgB;EAC9B;EAEA,OAAOqB,eAAeA,CAACzB,KAAY;IACjC,OAAO,IAAIc,cAAc,CAAC;MACxBH,EAAE,EAAEX,KAAK,CAACW,EAAE;MACZW,IAAI,EAAEtB,KAAK,CAACqB,KAAK,KAAK,UAAU,GAAGhC,mBAAmB,CAACE,QAAQ,GAAGF,mBAAmB,CAACG,KAAK;MAC3FY,IAAI,EAAEJ;KACP,CAAC;EACJ;EAEA,OAAO0B,uBAAuBA,CAACxB,KAAuB;IACpD,MAAMyB,wBAAwB,GAAG,CAC/BtC,mBAAmB,CAACE,QAAQ,EAC5BF,mBAAmB,CAACG,KAAK,CAC1B;IAED,IAAIoC,iBAAiB,GAAG1B,KAAK,CAACC,MAAM,CAAEqB,IAAI,IAAKG,wBAAwB,CAACrB,OAAO,CAACkB,IAAI,CAACF,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IAEhG,OAAO,IAAIR,cAAc,CAAC;MACxBQ,IAAI,EAAEjC,mBAAmB,CAACI,YAAY;MACtCS,KAAK,EAAE0B,iBAAiB;MACxBxB,IAAI,EAAE;QACJL,KAAK,EAAEU,mBAAmB,CAACmB,iBAAiB;;KAE/C,CAAC;EACJ;EAEA1C,YAAY2C,GAAG;IACb,KAAK,EAAE;IAEP,IAAI,IAAI,IAAIA,GAAG,EAAE;MACf,IAAI,CAAClB,EAAE,GAAGkB,GAAG,CAAClB,EAAE;IAClB;IAEA,IAAI,CAACW,IAAI,GAAGO,GAAG,CAACP,IAAI;IACpB,IAAI,CAAClB,IAAI,GAAGyB,GAAG,CAACzB,IAAI,IAAI,EAAE;IAE1B,IAAI,IAAI,CAACkB,IAAI,KAAKjC,mBAAmB,CAACI,YAAY,IAAIoC,GAAG,CAAC3B,KAAK,EAAE;MAC/D,IAAI,CAAC4B,oBAAoB,CAACD,GAAG,CAAC3B,KAAK,CAAC;IACtC,CAAC,MAAM;MACL,IAAI,CAACA,KAAK,GAAG,EAAE;IACjB;EACF;EAEO6B,cAAcA,CAAA;IACnB,IAAIC,oBAAoB,GAAG,CACzB3C,mBAAmB,CAACE,QAAQ,EAC5BF,mBAAmB,CAACG,KAAK,CAC1B;IAED,IAAIgC,IAAI,GAAG;MACTb,EAAE,EAAE,IAAI,CAACA,EAAE;MACXW,IAAI,EAAE,IAAI,CAACA;KACZ;IAED,IAAI,OAAOE,IAAI,CAACb,EAAE,KAAK,WAAW,EAAE;MAClC,OAAOa,IAAI,CAACb,EAAE;IAChB;IAEA,IAAI,IAAI,CAACT,KAAK,IAAI,IAAI,CAACA,KAAK,CAAC+B,MAAM,EAAE;MACnC,IAAI/B,KAAK,GAAG,IAAI,CAACA,KAAK,CACnBC,MAAM,CAAC+B,KAAK,IAAIF,oBAAoB,CAAC1B,OAAO,CAAC4B,KAAK,CAACZ,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAC9Df,GAAG,CAAE2B,KAAqB,IAAKA,KAAK,CAACH,cAAc,EAAE,CAAuB;MAE/E,IAAI7B,KAAK,CAAC+B,MAAM,EAAE;QAChBT,IAAI,GAAGW,MAAM,CAACC,MAAM,CAAC,EAAE,EAAEZ,IAAI,EAAE;UAAEtB;QAAK,CAAE,CAAC;MAC3C;IACF;IACA,OAAyBsB,IAAI;EAC/B;EAEOa,cAAcA,CAAC7B,IAAoB;IACxC,IAAI8B,cAAc,GAAG,CACnBjD,mBAAmB,CAACG,KAAK,EACzBH,mBAAmB,CAACE,QAAQ,CAC7B;IAED,IAAI+C,cAAc,CAAChC,OAAO,CAAC,IAAI,CAACgB,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE;MAC1C,IAAI,CAACpB,KAAK,CAACgB,IAAI,CAACV,IAAI,CAAC;IACvB;EACF;EAEO+B,aAAaA,CAAA;IAClB,IAAI,IAAI,CAACjB,IAAI,KAAKjC,mBAAmB,CAACC,IAAI,EAAE;IAE5C,OAAQ,IAAI,CAACc,IAAiB,CAACR,MAAM;EACvC;EAEA,IAAWuB,KAAKA,CAAA;IACd,IAAIA,KAAK,GAAW,EAAE;IACtB,IAAI,OAAO,IAAI,IAAI,CAACf,IAAI,EAAE;MACxBe,KAAK,GAAG,IAAI,CAACf,IAAI,CAAC,OAAO,CAAW;IACtC;IACA,OAAOe,KAAK;EACd;EAEQW,oBAAoBA,CAAC5B,KAAuB;IAClD,IAAI,CAACA,KAAK,GAAGA,KAAK;EACpB;;AAIF,OAAO,MAAMsC,OAAO,GAAIhB,IAAoB,IAAa;EACvD,OAAO,CACLnC,mBAAmB,CAACG,KAAK,EACzBH,mBAAmB,CAACE,QAAQ,CAC7B,CAACe,OAAO,CAACkB,IAAI,CAACF,IAAI,CAAC,GAAG,CAAC,CAAC;AAC3B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}