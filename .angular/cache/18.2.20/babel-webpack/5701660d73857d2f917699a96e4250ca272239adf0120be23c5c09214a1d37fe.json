{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FlexLayoutModule } from '@angular/flex-layout';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiControlMessagesModule } from '@skywind-group/lib-swui';\nimport { BsDropdownModule } from 'ngx-bootstrap/dropdown';\nimport { TooltipModule } from 'ngx-bootstrap/tooltip';\nimport { LobbyMenuItemsRibbonsModalComponent } from './lobby-menu-items-ribbons-modal/lobby-menu-items-ribbons-modal.component';\nimport { LobbyMenuItemsRibbonsComponent } from './lobby-menu-items-ribbons.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"ngx-bootstrap/dropdown\";\nimport * as i2 from \"ngx-bootstrap/tooltip\";\nexport class LobbyMenuItemsRibbonsModule {\n  static {\n    this.ɵfac = function LobbyMenuItemsRibbonsModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LobbyMenuItemsRibbonsModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: LobbyMenuItemsRibbonsModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, ReactiveFormsModule, BsDropdownModule.forRoot(), TooltipModule.forRoot(), TranslateModule, MatIconModule, MatButtonModule, MatInputModule, MatFormFieldModule, MatMenuModule, MatDialogModule, FlexLayoutModule, SwuiControlMessagesModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(LobbyMenuItemsRibbonsModule, {\n    declarations: [LobbyMenuItemsRibbonsComponent, LobbyMenuItemsRibbonsModalComponent],\n    imports: [CommonModule, ReactiveFormsModule, i1.BsDropdownModule, i2.TooltipModule, TranslateModule, MatIconModule, MatButtonModule, MatInputModule, MatFormFieldModule, MatMenuModule, MatDialogModule, FlexLayoutModule, SwuiControlMessagesModule],\n    exports: [LobbyMenuItemsRibbonsComponent]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FlexLayoutModule", "ReactiveFormsModule", "MatButtonModule", "MatDialogModule", "MatFormFieldModule", "MatIconModule", "MatInputModule", "MatMenuModule", "TranslateModule", "SwuiControlMessagesModule", "BsDropdownModule", "TooltipModule", "LobbyMenuItemsRibbonsModalComponent", "LobbyMenuItemsRibbonsComponent", "LobbyMenuItemsRibbonsModule", "forRoot", "declarations", "imports", "i1", "i2", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/lobby/lobby-form/lobby-menu-items/lobby-menu-items-ribbons/lobby-menu-items-ribbons.module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { FlexLayoutModule } from '@angular/flex-layout';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiControlMessagesModule } from '@skywind-group/lib-swui';\nimport { BsDropdownModule } from 'ngx-bootstrap/dropdown';\nimport { TooltipModule } from 'ngx-bootstrap/tooltip';\nimport { LobbyMenuItemsRibbonsModalComponent } from './lobby-menu-items-ribbons-modal/lobby-menu-items-ribbons-modal.component';\n\nimport { LobbyMenuItemsRibbonsComponent } from './lobby-menu-items-ribbons.component';\n\n\n@NgModule({\n  declarations: [\n    LobbyMenuItemsRibbonsComponent,\n    LobbyMenuItemsRibbonsModalComponent\n  ],\n  exports: [LobbyMenuItemsRibbonsComponent],\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    BsDropdownModule.forRoot(),\n    TooltipModule.forRoot(),\n    TranslateModule,\n    MatIconModule,\n    MatButtonModule,\n    MatInputModule,\n    MatFormFieldModule,\n    MatMenuModule,\n    MatDialogModule,\n    FlexLayoutModule,\n    SwuiControlMessagesModule,\n  ],\n})\nexport class LobbyMenuItemsRibbonsModule {\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,yBAAyB,QAAQ,yBAAyB;AACnE,SAASC,gBAAgB,QAAQ,wBAAwB;AACzD,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,mCAAmC,QAAQ,2EAA2E;AAE/H,SAASC,8BAA8B,QAAQ,sCAAsC;;;;AAyBrF,OAAM,MAAOC,2BAA2B;;;uCAA3BA,2BAA2B;IAAA;EAAA;;;YAA3BA;IAA2B;EAAA;;;gBAfpCf,YAAY,EACZE,mBAAmB,EACnBS,gBAAgB,CAACK,OAAO,EAAE,EAC1BJ,aAAa,CAACI,OAAO,EAAE,EACvBP,eAAe,EACfH,aAAa,EACbH,eAAe,EACfI,cAAc,EACdF,kBAAkB,EAClBG,aAAa,EACbJ,eAAe,EACfH,gBAAgB,EAChBS,yBAAyB;IAAA;EAAA;;;2EAGhBK,2BAA2B;IAAAE,YAAA,GApBpCH,8BAA8B,EAC9BD,mCAAmC;IAAAK,OAAA,GAInClB,YAAY,EACZE,mBAAmB,EAAAiB,EAAA,CAAAR,gBAAA,EAAAS,EAAA,CAAAR,aAAA,EAGnBH,eAAe,EACfH,aAAa,EACbH,eAAe,EACfI,cAAc,EACdF,kBAAkB,EAClBG,aAAa,EACbJ,eAAe,EACfH,gBAAgB,EAChBS,yBAAyB;IAAAW,OAAA,GAdjBP,8BAA8B;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}