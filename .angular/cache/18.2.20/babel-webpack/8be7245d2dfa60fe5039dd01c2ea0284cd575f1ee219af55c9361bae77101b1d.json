{"ast": null, "code": "import { MatDialogModule } from '@angular/material/dialog';\nimport { MatButtonModule } from '@angular/material/button';\nexport const matModules = [MatDialogModule, MatButtonModule];", "map": {"version": 3, "names": ["MatDialogModule", "MatButtonModule", "matModules"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/mat-business-structure/dialogs/status-confirm/status-confirm.module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { TranslateModule } from '@ngx-translate/core';\n\nimport { StatusConfirmComponent } from './status-confirm.component';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatButtonModule } from '@angular/material/button';\n\nexport const matModules = [\n  MatDialogModule,\n  MatButtonModule,\n];\n\n@NgModule({\n  imports: [\n    CommonModule,\n    TranslateModule,\n    ...matModules,\n  ],\n  declarations: [StatusConfirmComponent],\n"], "mappings": "AAKA,SAASA,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,eAAe,QAAQ,0BAA0B;AAE1D,OAAO,MAAMC,UAAU,GAAG,CACxBF,eAAe,EACfC,eAAe,CAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}