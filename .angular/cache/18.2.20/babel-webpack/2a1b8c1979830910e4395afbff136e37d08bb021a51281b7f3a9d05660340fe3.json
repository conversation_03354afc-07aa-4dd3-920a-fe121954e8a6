{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiPagePanelModule } from '@skywind-group/lib-swui';\nimport { ClipboardModule } from '../../common/components/clipboard/clipboard.module';\nimport { AuditService } from '../../common/services/audits.service';\nimport { CurrentUserResolver } from '../../common/services/resolvers/current-user.resolver';\nimport { RoleService } from '../../common/services/role.service';\nimport { UserService } from '../../common/services/user.service';\nimport { ActivityLogModule } from './components/activityLog/activityLog.module';\nimport { UsersListModule } from './components/list/user-list.module';\nimport { UsersComponent } from './users.component';\nimport { UsersRoutingModule } from './users.routing';\nimport * as i0 from \"@angular/core\";\nexport class UsersModule {\n  static {\n    this.ɵfac = function UsersModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || UsersModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: UsersModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [AuditService, RoleService, CurrentUserResolver, UserService],\n      imports: [CommonModule, TranslateModule, UsersListModule, UsersRoutingModule, FormsModule, ReactiveFormsModule, ClipboardModule, SwuiPagePanelModule, ActivityLogModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(UsersModule, {\n    declarations: [UsersComponent],\n    imports: [CommonModule, TranslateModule, UsersListModule, UsersRoutingModule, FormsModule, ReactiveFormsModule, ClipboardModule, SwuiPagePanelModule, ActivityLogModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "ReactiveFormsModule", "TranslateModule", "SwuiPagePanelModule", "ClipboardModule", "AuditService", "CurrentUserResolver", "RoleService", "UserService", "ActivityLogModule", "UsersListModule", "UsersComponent", "UsersRoutingModule", "UsersModule", "imports", "declarations"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/users/users.module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiPagePanelModule } from '@skywind-group/lib-swui';\nimport { ClipboardModule } from '../../common/components/clipboard/clipboard.module';\nimport { AuditService } from '../../common/services/audits.service';\nimport { CurrentUserResolver } from '../../common/services/resolvers/current-user.resolver';\nimport { RoleService } from '../../common/services/role.service';\nimport { UserService } from '../../common/services/user.service';\nimport { ActivityLogModule } from './components/activityLog/activityLog.module';\nimport { UsersListModule } from './components/list/user-list.module';\nimport { UsersComponent } from './users.component';\nimport { UsersRoutingModule } from './users.routing';\n\n@NgModule({\n  imports: [\n    CommonModule,\n    TranslateModule,\n    UsersListModule,\n    UsersRoutingModule,\n    FormsModule,\n    ReactiveFormsModule,\n    ClipboardModule,\n    SwuiPagePanelModule,\n    ActivityLogModule\n  ],\n  declarations: [\n    UsersComponent,\n  ],\n  providers: [\n    AuditService,\n    RoleService,\n    CurrentUserResolver,\n    UserService\n  ]\n})\nexport class UsersModule {\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,mBAAmB,QAAQ,yBAAyB;AAC7D,SAASC,eAAe,QAAQ,oDAAoD;AACpF,SAASC,YAAY,QAAQ,sCAAsC;AACnE,SAASC,mBAAmB,QAAQ,uDAAuD;AAC3F,SAASC,WAAW,QAAQ,oCAAoC;AAChE,SAASC,WAAW,QAAQ,oCAAoC;AAChE,SAASC,iBAAiB,QAAQ,6CAA6C;AAC/E,SAASC,eAAe,QAAQ,oCAAoC;AACpE,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,kBAAkB,QAAQ,iBAAiB;;AAwBpD,OAAM,MAAOC,WAAW;;;uCAAXA,WAAW;IAAA;EAAA;;;YAAXA;IAAW;EAAA;;;iBAPX,CACTR,YAAY,EACZE,WAAW,EACXD,mBAAmB,EACnBE,WAAW,CACZ;MAAAM,OAAA,GAlBCf,YAAY,EACZG,eAAe,EACfQ,eAAe,EACfE,kBAAkB,EAClBZ,WAAW,EACXC,mBAAmB,EACnBG,eAAe,EACfD,mBAAmB,EACnBM,iBAAiB;IAAA;EAAA;;;2EAYRI,WAAW;IAAAE,YAAA,GATpBJ,cAAc;IAAAG,OAAA,GAXdf,YAAY,EACZG,eAAe,EACfQ,eAAe,EACfE,kBAAkB,EAClBZ,WAAW,EACXC,mBAAmB,EACnBG,eAAe,EACfD,mBAAmB,EACnBM,iBAAiB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}