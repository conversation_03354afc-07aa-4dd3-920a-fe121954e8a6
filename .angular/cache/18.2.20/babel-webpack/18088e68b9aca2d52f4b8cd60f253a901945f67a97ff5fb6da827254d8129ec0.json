{"ast": null, "code": "import { forwardRef } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nconst _c0 = [\"content\"];\nexport class EditableComponent {\n  constructor(renderer) {\n    this.renderer = renderer;\n    this.propValueAccessor = 'textContent';\n  }\n  focusContentContainer() {\n    this.content.nativeElement.focus();\n  }\n  callOnChange() {\n    if (typeof this.onChange === 'function') {\n      this.onChange(this.content.nativeElement[this.propValueAccessor]);\n    }\n  }\n  callOnTouched() {\n    if (typeof this.onTouched === 'function') {\n      this.onTouched();\n    }\n  }\n  writeValue(value) {\n    this.value = value === null ? '' : value;\n    this.renderer.setProperty(this.content.nativeElement, this.propValueAccessor, this.value);\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  setDisabledState(isDisabled) {\n    if (isDisabled) {\n      this.renderer.setAttribute(this.content.nativeElement, 'disabled', 'true');\n      this.removeDisabledState = this.renderer.listen(this.content.nativeElement, 'keydown', this.listenerDisabledState);\n    } else {\n      if (this.removeDisabledState) {\n        this.renderer.removeAttribute(this.content.nativeElement, 'disabled');\n        this.removeDisabledState();\n      }\n    }\n  }\n  listenerDisabledState(e) {\n    e.preventDefault();\n  }\n  static {\n    this.ɵfac = function EditableComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || EditableComponent)(i0.ɵɵdirectiveInject(i0.Renderer2));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: EditableComponent,\n      selectors: [[\"editable\"]],\n      viewQuery: function EditableComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.content = _t.first);\n        }\n      },\n      inputs: {\n        placeholder: \"placeholder\"\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => EditableComponent),\n        multi: true\n      }])],\n      decls: 5,\n      vars: 1,\n      consts: [[\"content\", \"\"], [1, \"editable-group\"], [\"contenteditable\", \"true\", 3, \"input\", \"blur\"], [\"mat-stroked-button\", \"\", 1, \"editable-group__edit\", 3, \"click\"], [1, \"icon-pencil-square\"]],\n      template: function EditableComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2, 0);\n          i0.ɵɵlistener(\"input\", function EditableComponent_Template_div_input_1_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.callOnChange());\n          })(\"blur\", function EditableComponent_Template_div_blur_1_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.callOnTouched());\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"button\", 3);\n          i0.ɵɵlistener(\"click\", function EditableComponent_Template_button_click_3_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.focusContentContainer());\n          });\n          i0.ɵɵelement(4, \"i\", 4);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵattribute(\"placeholder\", ctx.placeholder);\n        }\n      },\n      styles: [\"[contenteditable=true][_ngcontent-%COMP%]:empty:before {\\n  content: attr(placeholder);\\n  display: block; \\n\\n  color: gray;\\n  font-style: italic;\\n}\\n\\n.editable-group[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.editable-group__edit[_ngcontent-%COMP%] {\\n  display: none;\\n  align-items: center;\\n  justify-content: flex-end;\\n  position: absolute;\\n  right: -28px;\\n  top: 2px;\\n  width: 28px;\\n  height: 18px;\\n  padding: 0 0 0 10px;\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n}\\n.editable-group__edit[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  line-height: normal;\\n  color: #333;\\n}\\n.editable-group[_ngcontent-%COMP%]:hover   .editable-group__edit[_ngcontent-%COMP%] {\\n  display: flex;\\n}\\n\\n.editable--inline[_nghost-%COMP%]   .editable-group__edit[_ngcontent-%COMP%] {\\n  right: 3px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY29tbW9uL2NvbXBvbmVudHMvZWRpdGFibGUvZWRpdGFibGUuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSwwQkFBQTtFQUNBLGNBQUEsRUFBQSxnQkFBQTtFQUNBLFdBQUE7RUFDQSxrQkFBQTtBQUNGOztBQUVBO0VBQ0Usa0JBQUE7QUFDRjtBQUNFO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EseUJBQUE7RUFDQSxrQkFBQTtFQUNBLFlBQUE7RUFDQSxRQUFBO0VBQ0EsV0FBQTtFQUNBLFlBQUE7RUFDQSxtQkFBQTtFQUNBLGdCQUFBO0VBQ0EsWUFBQTtFQUNBLGVBQUE7QUFDSjtBQUNJO0VBQ0UsZUFBQTtFQUNBLG1CQUFBO0VBQ0EsV0FBQTtBQUNOO0FBSUk7RUFDRSxhQUFBO0FBRk47O0FBU0k7RUFDRSxVQUFBO0FBTk4iLCJzb3VyY2VzQ29udGVudCI6WyJbY29udGVudGVkaXRhYmxlPXRydWVdOmVtcHR5OmJlZm9yZSB7XG4gIGNvbnRlbnQ6IGF0dHIocGxhY2Vob2xkZXIpO1xuICBkaXNwbGF5OiBibG9jazsgLyogRm9yIEZpcmVmb3ggKi9cbiAgY29sb3I6IGdyYXk7XG4gIGZvbnQtc3R5bGU6IGl0YWxpYztcbn1cblxuLmVkaXRhYmxlLWdyb3VwIHtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xuXG4gICZfX2VkaXQge1xuICAgIGRpc3BsYXk6IG5vbmU7XG4gICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtZW5kO1xuICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgICByaWdodDogLTI4cHg7XG4gICAgdG9wOiAycHg7XG4gICAgd2lkdGg6IDI4cHg7XG4gICAgaGVpZ2h0OiAxOHB4O1xuICAgIHBhZGRpbmc6IDAgMCAwIDEwcHg7XG4gICAgYmFja2dyb3VuZDogbm9uZTtcbiAgICBib3JkZXI6IG5vbmU7XG4gICAgY3Vyc29yOiBwb2ludGVyO1xuXG4gICAgaSB7XG4gICAgICBmb250LXNpemU6IDE4cHg7XG4gICAgICBsaW5lLWhlaWdodDogbm9ybWFsO1xuICAgICAgY29sb3I6ICMzMzM7XG4gICAgfVxuICB9XG5cbiAgJjpob3ZlciB7XG4gICAgLmVkaXRhYmxlLWdyb3VwX19lZGl0IHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgfVxuICB9XG59XG5cbjpob3N0LmVkaXRhYmxlLS1pbmxpbmUge1xuICAuZWRpdGFibGUtZ3JvdXAge1xuICAgICZfX2VkaXQge1xuICAgICAgcmlnaHQ6IDNweDtcbiAgICB9XG4gIH1cbn1cbiJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["forwardRef", "NG_VALUE_ACCESSOR", "EditableComponent", "constructor", "renderer", "propValueAccessor", "focusContentContainer", "content", "nativeElement", "focus", "callOnChange", "onChange", "callOnTouched", "onTouched", "writeValue", "value", "setProperty", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "isDisabled", "setAttribute", "removeDisabledState", "listen", "listenerDisabledState", "removeAttribute", "e", "preventDefault", "i0", "ɵɵdirectiveInject", "Renderer2", "selectors", "viewQuery", "EditableComponent_Query", "rf", "ctx", "provide", "useExisting", "multi", "decls", "vars", "consts", "template", "EditableComponent_Template", "ɵɵelementStart", "ɵɵlistener", "EditableComponent_Template_div_input_1_listener", "ɵɵrestoreView", "_r1", "ɵɵresetView", "EditableComponent_Template_div_blur_1_listener", "ɵɵelementEnd", "EditableComponent_Template_button_click_3_listener", "ɵɵelement", "ɵɵadvance"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/common/components/editable/editable.component.ts", "/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/common/components/editable/editable.component.html"], "sourcesContent": ["import {\n  Component,\n  ElementRef,\n  Input,\n  Renderer2,\n  ViewChild,\n  forwardRef\n} from '@angular/core';\nimport { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';\n\n@Component({\n  selector: 'editable',\n  templateUrl: './editable.component.html',\n  providers: [\n    { provide: NG_VALUE_ACCESSOR, useExisting: forwardRef(() => EditableComponent), multi: true }\n  ],\n  styleUrls: [\n    './editable.component.scss',\n  ]\n})\nexport class EditableComponent implements ControlValueAccessor {\n\n  @ViewChild('content', { static: true }) content: ElementRef;\n\n  @Input() public placeholder: string;\n\n  public value: string;\n\n  private onChange: (value: string) => void;\n  private onTouched: () => void;\n  private removeDisabledState: () => void;\n  private propValueAccessor: string = 'textContent';\n\n  constructor(private renderer: Renderer2\n  ) {\n  }\n\n  focusContentContainer() {\n    this.content.nativeElement.focus();\n  }\n\n  callOnChange() {\n    if (typeof this.onChange === 'function') {\n      this.onChange(this.content.nativeElement[this.propValueAccessor]);\n    }\n  }\n\n  callOnTouched() {\n    if (typeof this.onTouched === 'function') {\n      this.onTouched();\n    }\n  }\n\n  writeValue(value: any): void {\n    this.value = value === null ? '' : value;\n    this.renderer.setProperty(this.content.nativeElement, this.propValueAccessor, this.value);\n\n  }\n\n  registerOnChange(fn: () => void): void {\n    this.onChange = fn;\n  }\n\n  registerOnTouched(fn: () => void): void {\n    this.onTouched = fn;\n  }\n\n  setDisabledState(isDisabled: boolean): void {\n    if (isDisabled) {\n      this.renderer.setAttribute(this.content.nativeElement, 'disabled', 'true');\n      this.removeDisabledState = this.renderer\n        .listen(this.content.nativeElement, 'keydown', this.listenerDisabledState);\n    } else {\n      if (this.removeDisabledState) {\n        this.renderer.removeAttribute(this.content.nativeElement, 'disabled');\n        this.removeDisabledState();\n      }\n    }\n  }\n\n  private listenerDisabledState(e: KeyboardEvent) {\n    e.preventDefault();\n  }\n}\n", "<div class=\"editable-group\">\n  <div #content contenteditable=\"true\" (input)=\"callOnChange()\" (blur)=\"callOnTouched()\"\n       [attr.placeholder]=\"placeholder\"></div>\n  <button mat-stroked-button class=\"editable-group__edit\" (click)=\"focusContentContainer()\">\n    <i class=\"icon-pencil-square\"></i>\n  </button>\n</div>\n"], "mappings": "AAAA,SAMEA,UAAU,QACL,eAAe;AACtB,SAA+BC,iBAAiB,QAAQ,gBAAgB;;;AAYxE,OAAM,MAAOC,iBAAiB;EAa5BC,YAAoBC,QAAmB;IAAnB,KAAAA,QAAQ,GAARA,QAAQ;IAFpB,KAAAC,iBAAiB,GAAW,aAAa;EAIjD;EAEAC,qBAAqBA,CAAA;IACnB,IAAI,CAACC,OAAO,CAACC,aAAa,CAACC,KAAK,EAAE;EACpC;EAEAC,YAAYA,CAAA;IACV,IAAI,OAAO,IAAI,CAACC,QAAQ,KAAK,UAAU,EAAE;MACvC,IAAI,CAACA,QAAQ,CAAC,IAAI,CAACJ,OAAO,CAACC,aAAa,CAAC,IAAI,CAACH,iBAAiB,CAAC,CAAC;IACnE;EACF;EAEAO,aAAaA,CAAA;IACX,IAAI,OAAO,IAAI,CAACC,SAAS,KAAK,UAAU,EAAE;MACxC,IAAI,CAACA,SAAS,EAAE;IAClB;EACF;EAEAC,UAAUA,CAACC,KAAU;IACnB,IAAI,CAACA,KAAK,GAAGA,KAAK,KAAK,IAAI,GAAG,EAAE,GAAGA,KAAK;IACxC,IAAI,CAACX,QAAQ,CAACY,WAAW,CAAC,IAAI,CAACT,OAAO,CAACC,aAAa,EAAE,IAAI,CAACH,iBAAiB,EAAE,IAAI,CAACU,KAAK,CAAC;EAE3F;EAEAE,gBAAgBA,CAACC,EAAc;IAC7B,IAAI,CAACP,QAAQ,GAAGO,EAAE;EACpB;EAEAC,iBAAiBA,CAACD,EAAc;IAC9B,IAAI,CAACL,SAAS,GAAGK,EAAE;EACrB;EAEAE,gBAAgBA,CAACC,UAAmB;IAClC,IAAIA,UAAU,EAAE;MACd,IAAI,CAACjB,QAAQ,CAACkB,YAAY,CAAC,IAAI,CAACf,OAAO,CAACC,aAAa,EAAE,UAAU,EAAE,MAAM,CAAC;MAC1E,IAAI,CAACe,mBAAmB,GAAG,IAAI,CAACnB,QAAQ,CACrCoB,MAAM,CAAC,IAAI,CAACjB,OAAO,CAACC,aAAa,EAAE,SAAS,EAAE,IAAI,CAACiB,qBAAqB,CAAC;IAC9E,CAAC,MAAM;MACL,IAAI,IAAI,CAACF,mBAAmB,EAAE;QAC5B,IAAI,CAACnB,QAAQ,CAACsB,eAAe,CAAC,IAAI,CAACnB,OAAO,CAACC,aAAa,EAAE,UAAU,CAAC;QACrE,IAAI,CAACe,mBAAmB,EAAE;MAC5B;IACF;EACF;EAEQE,qBAAqBA,CAACE,CAAgB;IAC5CA,CAAC,CAACC,cAAc,EAAE;EACpB;;;uCA9DW1B,iBAAiB,EAAA2B,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAE,SAAA;IAAA;EAAA;;;YAAjB7B,iBAAiB;MAAA8B,SAAA;MAAAC,SAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;uCAPjB,CACT;QAAEE,OAAO,EAAEpC,iBAAiB;QAAEqC,WAAW,EAAEtC,UAAU,CAAC,MAAME,iBAAiB,CAAC;QAAEqC,KAAK,EAAE;MAAI,CAAE,CAC9F;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAT,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCdDN,EADF,CAAAgB,cAAA,aAA4B,gBAEY;UADwBhB,EAAzB,CAAAiB,UAAA,mBAAAC,gDAAA;YAAAlB,EAAA,CAAAmB,aAAA,CAAAC,GAAA;YAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASd,GAAA,CAAA1B,YAAA,EAAc;UAAA,EAAC,kBAAAyC,+CAAA;YAAAtB,EAAA,CAAAmB,aAAA,CAAAC,GAAA;YAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASd,GAAA,CAAAxB,aAAA,EAAe;UAAA,EAAC;UAChDiB,EAAA,CAAAuB,YAAA,EAAM;UAC5CvB,EAAA,CAAAgB,cAAA,gBAA0F;UAAlChB,EAAA,CAAAiB,UAAA,mBAAAO,mDAAA;YAAAxB,EAAA,CAAAmB,aAAA,CAAAC,GAAA;YAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASd,GAAA,CAAA9B,qBAAA,EAAuB;UAAA,EAAC;UACvFuB,EAAA,CAAAyB,SAAA,WAAkC;UAEtCzB,EADE,CAAAuB,YAAA,EAAS,EACL;;;UAJCvB,EAAA,CAAA0B,SAAA,EAAgC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}