{"ast": null, "code": "import { NG_VALIDATORS, NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { forwardRef } from '@angular/core';\nimport { BehaviorSubject, Subject } from 'rxjs';\nimport { map, takeUntil } from 'rxjs/operators';\nimport { SW_FORM_SERVICE } from './form-service.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"../tagged-items/tagged-items.component\";\nimport * as i3 from \"@ngx-translate/core\";\nfunction GamesFormComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"COMPONENTS.GAMES_SELECT_MANAGER.validationMessage\"), \" \");\n  }\n}\nfunction GamesFormComponent_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 7);\n    i0.ɵɵlistener(\"click\", function GamesFormComponent_span_9_Template_span_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleVisible());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, ctx_r1.toggleTexts[ctx_r1.checkedOnly]));\n  }\n}\nfunction from(game) {\n  if (!game.labels.find(({\n    id\n  }) => id === game.providerCode)) {\n    game.labels.push({\n      id: game.providerCode,\n      title: game.providerTitle,\n      group: 'provider'\n    });\n  }\n  if (game?.code && game?.features?.supportsRtpConfigurator && !!game?.features?.baseRTP) {\n    return {\n      id: game.code,\n      title: game.title || '',\n      labels: game.labels,\n      currentRtp: Number(game.features.baseRTP),\n      checked: false\n    };\n  } else if (game?.code && game?.features?.supportsRtpConfigurator && !!game?.features?.baseRTPRange) {\n    const {\n      max,\n      min\n    } = game.features?.baseRTPRange;\n    return {\n      id: game.code,\n      title: game.title || '',\n      labels: game.labels,\n      currentRtpMax: Number(max) || 0,\n      currentRtpMin: Number(min) || 0,\n      checked: false\n    };\n  }\n}\nexport class GamesFormComponent {\n  set sourceGames(games) {\n    if (games && Array.isArray(games)) {\n      this._sourceGames$.next(games && Array.isArray(games) ? games : []);\n    }\n  }\n  constructor(formService) {\n    this.formService = formService;\n    this.submitted = false;\n    this.gameCodes = [];\n    this.disabled = false;\n    this.isValid = true;\n    this.isTouched = false;\n    this.gameCodesCounter = 0;\n    this.checkedOnly = false;\n    this.toggleTexts = {\n      true: 'COMPONENTS.GAMES_SELECT_MANAGER.view_all',\n      false: 'COMPONENTS.GAMES_SELECT_MANAGER.view_selected'\n    };\n    this.onChange = () => {};\n    this._sourceGames$ = new BehaviorSubject([]);\n    this.destroyed = new Subject();\n    this.onTouched = () => {};\n    this.items$ = this._sourceGames$.pipe(map(games => {\n      const selectItems = [];\n      games.forEach(game => {\n        const item = from(game);\n        if (item) {\n          selectItems.push(item);\n        }\n      });\n      return selectItems;\n    }));\n    this.totalItems$ = this.items$.pipe(map(items => items.length));\n  }\n  onblur() {\n    this.isTouched = true;\n    this.onTouched();\n  }\n  ngOnInit() {\n    if (this.formService) {\n      this.formService.formSubmitted$.pipe(takeUntil(this.destroyed)).subscribe(val => {\n        this.isTouched = val;\n      });\n    }\n  }\n  ngOnDestroy() {\n    this.destroyed.next();\n    this.destroyed.complete();\n  }\n  writeValue(val) {\n    this.gameCodes = Array.isArray(val) ? val : [];\n    this.gameCodesCounter = this.gameCodes.length;\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n  }\n  validate() {\n    return this.isValid ? null : {\n      invalidForm: {\n        valid: false\n      }\n    };\n  }\n  onSelectedChange(gameCodes) {\n    this.isValid = gameCodes.length > 0;\n    this.gameCodesCounter = gameCodes.length;\n    if (!this.gameCodesCounter) {\n      this.checkedOnly = false;\n    }\n    setTimeout(() => {\n      this.onChange(gameCodes);\n    }, 0);\n  }\n  toggleVisible() {\n    this.checkedOnly = !this.checkedOnly;\n  }\n  static {\n    this.ɵfac = function GamesFormComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || GamesFormComponent)(i0.ɵɵdirectiveInject(SW_FORM_SERVICE, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: GamesFormComponent,\n      selectors: [[\"sw-games-form\"]],\n      hostBindings: function GamesFormComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"blur\", function GamesFormComponent_blur_HostBindingHandler() {\n            return ctx.onblur();\n          });\n        }\n      },\n      inputs: {\n        submitted: \"submitted\",\n        sourceGames: \"sourceGames\"\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => GamesFormComponent),\n        multi: true\n      }, {\n        provide: NG_VALIDATORS,\n        useExisting: forwardRef(() => GamesFormComponent),\n        multi: true\n      }])],\n      decls: 12,\n      vars: 15,\n      consts: [[\"class\", \"error-message margin-bottom16\", 4, \"ngIf\"], [1, \"margin-bottom12\"], [1, \"games-label\"], [1, \"margin-bottom12\", \"no-margin-top\"], [\"class\", \"checked-toggle\", 3, \"click\", 4, \"ngIf\"], [3, \"changed\", \"items\", \"selectedItems\", \"disabled\", \"checkedOnly\"], [1, \"error-message\", \"margin-bottom16\"], [1, \"checked-toggle\", 3, \"click\"]],\n      template: function GamesFormComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\");\n          i0.ɵɵtemplate(1, GamesFormComponent_div_1_Template, 3, 3, \"div\", 0);\n          i0.ɵɵelementStart(2, \"div\", 1);\n          i0.ɵɵtext(3);\n          i0.ɵɵpipe(4, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 2)(6, \"h4\", 3);\n          i0.ɵɵtext(7);\n          i0.ɵɵpipe(8, \"async\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(9, GamesFormComponent_span_9_Template, 3, 3, \"span\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"sw-tagged-items\", 5);\n          i0.ɵɵpipe(11, \"async\");\n          i0.ɵɵlistener(\"changed\", function GamesFormComponent_Template_sw_tagged_items_changed_10_listener($event) {\n            return ctx.onSelectedChange($event);\n          });\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.gameCodesCounter && ctx.submitted && !ctx.disabled);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 9, \"COMPONENTS.GAMES_SELECT_MANAGER.chooseTheGames\"), \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate2(\"Selected: \", ctx.gameCodesCounter, \" of \", i0.ɵɵpipeBind1(8, 11, ctx.totalItems$), \"\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.gameCodesCounter);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(11, 13, ctx.items$))(\"selectedItems\", ctx.gameCodes)(\"disabled\", ctx.disabled)(\"checkedOnly\", ctx.checkedOnly);\n        }\n      },\n      dependencies: [i1.NgIf, i2.TaggedItemsComponent, i1.AsyncPipe, i3.TranslatePipe],\n      styles: [\".error-message[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  border-radius: 4px;\\n  background-color: #ff8a80;\\n  margin-bottom: 20px;\\n}\\n\\n.games-label[_ngcontent-%COMP%] {\\n  display: flex;\\n}\\n\\n.checked-toggle[_ngcontent-%COMP%] {\\n  color: #1373d5;\\n  transition: opacity 0.5s;\\n  cursor: pointer;\\n  margin-left: 10px;\\n}\\n.checked-toggle[_ngcontent-%COMP%]:hover {\\n  opacity: 0.5;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvYnVzaW5lc3MtbWFuYWdlbWVudC9jb21wb25lbnRzL2VudGl0aWVzL3RhYi1ydHAtcmVkdWNlci9ydHAtcmVkdWNlLW1vZGFsL2dhbWVzLWZvcm0vZ2FtZXMtZm9ybS5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGFBQUE7RUFDQSxrQkFBQTtFQUNBLHlCQUFBO0VBQ0EsbUJBQUE7QUFDRjs7QUFFQTtFQUNFLGFBQUE7QUFDRjs7QUFFQTtFQUNFLGNBQUE7RUFDQSx3QkFBQTtFQUNBLGVBQUE7RUFDQSxpQkFBQTtBQUNGO0FBQ0U7RUFDRSxZQUFBO0FBQ0oiLCJzb3VyY2VzQ29udGVudCI6WyIuZXJyb3ItbWVzc2FnZSB7XG4gIHBhZGRpbmc6IDIwcHg7XG4gIGJvcmRlci1yYWRpdXM6IDRweDtcbiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmOGE4MDtcbiAgbWFyZ2luLWJvdHRvbTogMjBweDtcbn1cblxuLmdhbWVzLWxhYmVsIHtcbiAgZGlzcGxheTogZmxleDtcbn1cblxuLmNoZWNrZWQtdG9nZ2xlIHtcbiAgY29sb3I6ICMxMzczZDU7XG4gIHRyYW5zaXRpb246IG9wYWNpdHkgLjVzO1xuICBjdXJzb3I6IHBvaW50ZXI7XG4gIG1hcmdpbi1sZWZ0OiAxMHB4O1xuXG4gICY6aG92ZXIge1xuICAgIG9wYWNpdHk6IDAuNTtcbiAgfVxufVxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["NG_VALIDATORS", "NG_VALUE_ACCESSOR", "forwardRef", "BehaviorSubject", "Subject", "map", "takeUntil", "SW_FORM_SERVICE", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "ɵɵlistener", "GamesFormComponent_span_9_Template_span_click_0_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "toggleVisible", "ɵɵtextInterpolate", "toggleTexts", "checkedOnly", "from", "game", "labels", "find", "id", "providerCode", "push", "title", "providerTitle", "group", "code", "features", "supportsRtpConfigurator", "baseRTP", "currentRtp", "Number", "checked", "baseRTPRange", "max", "min", "currentRtpMax", "currentRtpMin", "GamesFormComponent", "sourceGames", "games", "Array", "isArray", "_sourceGames$", "next", "constructor", "formService", "submitted", "gameCodes", "disabled", "<PERSON><PERSON><PERSON><PERSON>", "isTouched", "gameCodesCounter", "true", "false", "onChange", "destroyed", "onTouched", "items$", "pipe", "selectItems", "for<PERSON>ach", "item", "totalItems$", "items", "length", "onblur", "ngOnInit", "formSubmitted$", "subscribe", "val", "ngOnDestroy", "complete", "writeValue", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "isDisabled", "validate", "invalidForm", "valid", "onSelectedChange", "setTimeout", "ɵɵdirectiveInject", "selectors", "hostBindings", "GamesFormComponent_HostBindings", "rf", "ctx", "GamesFormComponent_blur_HostBindingHandler", "provide", "useExisting", "multi", "decls", "vars", "consts", "template", "GamesFormComponent_Template", "ɵɵtemplate", "GamesFormComponent_div_1_Template", "GamesFormComponent_span_9_Template", "GamesFormComponent_Template_sw_tagged_items_changed_10_listener", "$event", "ɵɵproperty", "ɵɵtextInterpolate2"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-rtp-reducer/rtp-reduce-modal/games-form/games-form.component.ts", "/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-rtp-reducer/rtp-reduce-modal/games-form/games-form.component.html"], "sourcesContent": ["import { <PERSON><PERSON><PERSON>ueAccessor, NG_VALIDATORS, NG_VALUE_ACCESSOR, ValidationErrors, Validator } from '@angular/forms';\nimport { Component, forwardRef, HostListener, Inject, Input, OnDestroy, OnInit, Optional } from '@angular/core';\nimport { BehaviorSubject, Observable, Subject } from 'rxjs';\nimport { map, takeUntil } from 'rxjs/operators';\n\nimport { TaggedItem } from '../tagged-items/tagged-items.component';\nimport { FormService, SW_FORM_SERVICE } from './form-service.model';\nimport { GameInfo } from '../../../../../../../common/typings';\n\nfunction from( game: GameInfo ): TaggedItem | undefined {\n  if (!game.labels.find(( { id } ) => id === game.providerCode)) {\n    game.labels.push({\n      id: game.providerCode,\n      title: game.providerTitle,\n      group: 'provider',\n    });\n  }\n  if (game?.code && game?.features?.supportsRtpConfigurator && !!game?.features?.baseRTP) {\n    return {\n      id: game.code,\n      title: game.title || '',\n      labels: game.labels,\n      currentRtp: Number(game.features.baseRTP),\n      checked: false\n    };\n  } else if (game?.code && game?.features?.supportsRtpConfigurator && !!game?.features?.baseRTPRange) {\n    const { max, min } = game.features?.baseRTPRange;\n    return {\n      id: game.code,\n      title: game.title || '',\n      labels: game.labels,\n      currentRtpMax: Number(max) || 0,\n      currentRtpMin: Number(min) || 0,\n      checked: false\n    };\n  }\n}\n\n@Component({\n  selector: 'sw-games-form',\n  templateUrl: './games-form.component.html',\n  styleUrls: ['./games-form.component.scss'],\n  providers: [\n    {\n      provide: NG_VALUE_ACCESSOR,\n      useExisting: forwardRef(() => GamesFormComponent),\n      multi: true\n    },\n    {\n      provide: NG_VALIDATORS,\n      useExisting: forwardRef(() => GamesFormComponent),\n      multi: true\n    }\n  ]\n})\nexport class GamesFormComponent implements OnDestroy, OnInit, ControlValueAccessor, Validator {\n  @Input() submitted = false;\n\n  @Input() set sourceGames( games: GameInfo[] ) {\n    if (games && Array.isArray(games)) {\n      this._sourceGames$.next(games && Array.isArray(games) ? games : []);\n    }\n  }\n\n  gameCodes: string[] = [];\n  disabled = false;\n  isValid = true;\n  isTouched = false;\n  gameCodesCounter = 0;\n  checkedOnly = false;\n\n  toggleTexts = {\n    true: 'COMPONENTS.GAMES_SELECT_MANAGER.view_all',\n    false: 'COMPONENTS.GAMES_SELECT_MANAGER.view_selected'\n  };\n\n  items$: Observable<TaggedItem[]>;\n  totalItems$: Observable<number>;\n  onChange: ( _: any ) => void = (() => {\n  });\n\n  private _sourceGames$ = new BehaviorSubject<GameInfo[]>([]);\n  private readonly destroyed = new Subject<void>();\n\n  constructor( @Optional() @Inject(SW_FORM_SERVICE) private readonly formService: FormService ) {\n    this.items$ = this._sourceGames$.pipe(\n      map(( games: GameInfo[] ) => {\n        const selectItems: TaggedItem[] = [];\n        games.forEach(( game: GameInfo ) => {\n          const item = from(game);\n          if (item) {\n            selectItems.push(item);\n          }\n        });\n        return selectItems;\n      })\n    );\n    this.totalItems$ = this.items$.pipe(\n      map(items => items.length)\n    );\n  }\n\n  @HostListener('blur') onblur() {\n    this.isTouched = true;\n    this.onTouched();\n  }\n\n  onTouched: () => void = () => {\n  }\n\n  ngOnInit() {\n    if (this.formService) {\n      this.formService.formSubmitted$.pipe(\n        takeUntil(this.destroyed)\n      ).subscribe(val => {\n        this.isTouched = val;\n      });\n    }\n  }\n\n  ngOnDestroy() {\n    this.destroyed.next();\n    this.destroyed.complete();\n  }\n\n  writeValue( val: string[] ): void {\n    this.gameCodes = Array.isArray(val) ? val : [];\n    this.gameCodesCounter = this.gameCodes.length;\n  }\n\n  registerOnChange( fn: any ): void {\n    this.onChange = fn;\n  }\n\n  registerOnTouched( fn: any ): void {\n    this.onTouched = fn;\n  }\n\n  setDisabledState( isDisabled: boolean ): void {\n    this.disabled = isDisabled;\n  }\n\n  validate(): ValidationErrors | null {\n    return this.isValid ? null : { invalidForm: { valid: false } };\n  }\n\n  onSelectedChange( gameCodes: any[] ) {\n    this.isValid = gameCodes.length > 0;\n    this.gameCodesCounter = gameCodes.length;\n\n    if (!this.gameCodesCounter) {\n      this.checkedOnly = false;\n    }\n\n    setTimeout(() => {\n      this.onChange(gameCodes);\n    }, 0);\n  }\n\n  toggleVisible() {\n    this.checkedOnly = !this.checkedOnly;\n  }\n}\n", "<div>\n  <div class=\"error-message margin-bottom16\" *ngIf=\"!gameCodesCounter && submitted && !disabled\">\n    {{ 'COMPONENTS.GAMES_SELECT_MANAGER.validationMessage' | translate }}\n  </div>\n  <div class=\"margin-bottom12\">\n    {{ 'COMPONENTS.GAMES_SELECT_MANAGER.chooseTheGames' | translate }}\n  </div>\n  <div class=\"games-label\">\n    <h4 class=\"margin-bottom12 no-margin-top\">Selected: {{gameCodesCounter}} of {{(totalItems$ | async)}}</h4>\n    <span *ngIf=\"gameCodesCounter\" class=\"checked-toggle\" (click)=\"toggleVisible()\">{{ toggleTexts[checkedOnly] | translate }}</span>\n  </div>\n\n  <sw-tagged-items\n    [items]=\"items$ | async\"\n    [selectedItems]=\"gameCodes\"\n    [disabled]=\"disabled\"\n    [checkedOnly]=\"checkedOnly\"\n    (changed)=\"onSelectedChange($event)\">\n  </sw-tagged-items>\n</div>\n"], "mappings": "AAAA,SAA+BA,aAAa,EAAEC,iBAAiB,QAAqC,gBAAgB;AACpH,SAAoBC,UAAU,QAAkE,eAAe;AAC/G,SAASC,eAAe,EAAcC,OAAO,QAAQ,MAAM;AAC3D,SAASC,GAAG,EAAEC,SAAS,QAAQ,gBAAgB;AAG/C,SAAsBC,eAAe,QAAQ,sBAAsB;;;;;;;ICLjEC,EAAA,CAAAC,cAAA,aAA+F;IAC7FD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;IADJH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAL,EAAA,CAAAM,WAAA,iEACF;;;;;;IAMEN,EAAA,CAAAC,cAAA,cAAgF;IAA1BD,EAAA,CAAAO,UAAA,mBAAAC,yDAAA;MAAAR,EAAA,CAAAS,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAG,aAAA,EAAe;IAAA,EAAC;IAACd,EAAA,CAAAE,MAAA,GAA0C;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAjDH,EAAA,CAAAI,SAAA,EAA0C;IAA1CJ,EAAA,CAAAe,iBAAA,CAAAf,EAAA,CAAAM,WAAA,OAAAK,MAAA,CAAAK,WAAA,CAAAL,MAAA,CAAAM,WAAA,GAA0C;;;ADA9H,SAASC,IAAIA,CAAEC,IAAc;EAC3B,IAAI,CAACA,IAAI,CAACC,MAAM,CAACC,IAAI,CAAC,CAAE;IAAEC;EAAE,CAAE,KAAMA,EAAE,KAAKH,IAAI,CAACI,YAAY,CAAC,EAAE;IAC7DJ,IAAI,CAACC,MAAM,CAACI,IAAI,CAAC;MACfF,EAAE,EAAEH,IAAI,CAACI,YAAY;MACrBE,KAAK,EAAEN,IAAI,CAACO,aAAa;MACzBC,KAAK,EAAE;KACR,CAAC;EACJ;EACA,IAAIR,IAAI,EAAES,IAAI,IAAIT,IAAI,EAAEU,QAAQ,EAAEC,uBAAuB,IAAI,CAAC,CAACX,IAAI,EAAEU,QAAQ,EAAEE,OAAO,EAAE;IACtF,OAAO;MACLT,EAAE,EAAEH,IAAI,CAACS,IAAI;MACbH,KAAK,EAAEN,IAAI,CAACM,KAAK,IAAI,EAAE;MACvBL,MAAM,EAAED,IAAI,CAACC,MAAM;MACnBY,UAAU,EAAEC,MAAM,CAACd,IAAI,CAACU,QAAQ,CAACE,OAAO,CAAC;MACzCG,OAAO,EAAE;KACV;EACH,CAAC,MAAM,IAAIf,IAAI,EAAES,IAAI,IAAIT,IAAI,EAAEU,QAAQ,EAAEC,uBAAuB,IAAI,CAAC,CAACX,IAAI,EAAEU,QAAQ,EAAEM,YAAY,EAAE;IAClG,MAAM;MAAEC,GAAG;MAAEC;IAAG,CAAE,GAAGlB,IAAI,CAACU,QAAQ,EAAEM,YAAY;IAChD,OAAO;MACLb,EAAE,EAAEH,IAAI,CAACS,IAAI;MACbH,KAAK,EAAEN,IAAI,CAACM,KAAK,IAAI,EAAE;MACvBL,MAAM,EAAED,IAAI,CAACC,MAAM;MACnBkB,aAAa,EAAEL,MAAM,CAACG,GAAG,CAAC,IAAI,CAAC;MAC/BG,aAAa,EAAEN,MAAM,CAACI,GAAG,CAAC,IAAI,CAAC;MAC/BH,OAAO,EAAE;KACV;EACH;AACF;AAmBA,OAAM,MAAOM,kBAAkB;EAG7B,IAAaC,WAAWA,CAAEC,KAAiB;IACzC,IAAIA,KAAK,IAAIC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,EAAE;MACjC,IAAI,CAACG,aAAa,CAACC,IAAI,CAACJ,KAAK,IAAIC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,GAAGA,KAAK,GAAG,EAAE,CAAC;IACrE;EACF;EAsBAK,YAAmEC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;IA5BrE,KAAAC,SAAS,GAAG,KAAK;IAQ1B,KAAAC,SAAS,GAAa,EAAE;IACxB,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAAC,OAAO,GAAG,IAAI;IACd,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,gBAAgB,GAAG,CAAC;IACpB,KAAArC,WAAW,GAAG,KAAK;IAEnB,KAAAD,WAAW,GAAG;MACZuC,IAAI,EAAE,0CAA0C;MAChDC,KAAK,EAAE;KACR;IAID,KAAAC,QAAQ,GAAwB,MAAK,CACrC,CAAE;IAEM,KAAAZ,aAAa,GAAG,IAAIlD,eAAe,CAAa,EAAE,CAAC;IAC1C,KAAA+D,SAAS,GAAG,IAAI9D,OAAO,EAAQ;IAyBhD,KAAA+D,SAAS,GAAe,MAAK,CAC7B,CAAC;IAvBC,IAAI,CAACC,MAAM,GAAG,IAAI,CAACf,aAAa,CAACgB,IAAI,CACnChE,GAAG,CAAG6C,KAAiB,IAAK;MAC1B,MAAMoB,WAAW,GAAiB,EAAE;MACpCpB,KAAK,CAACqB,OAAO,CAAG5C,IAAc,IAAK;QACjC,MAAM6C,IAAI,GAAG9C,IAAI,CAACC,IAAI,CAAC;QACvB,IAAI6C,IAAI,EAAE;UACRF,WAAW,CAACtC,IAAI,CAACwC,IAAI,CAAC;QACxB;MACF,CAAC,CAAC;MACF,OAAOF,WAAW;IACpB,CAAC,CAAC,CACH;IACD,IAAI,CAACG,WAAW,GAAG,IAAI,CAACL,MAAM,CAACC,IAAI,CACjChE,GAAG,CAACqE,KAAK,IAAIA,KAAK,CAACC,MAAM,CAAC,CAC3B;EACH;EAEsBC,MAAMA,CAAA;IAC1B,IAAI,CAACf,SAAS,GAAG,IAAI;IACrB,IAAI,CAACM,SAAS,EAAE;EAClB;EAKAU,QAAQA,CAAA;IACN,IAAI,IAAI,CAACrB,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAACsB,cAAc,CAACT,IAAI,CAClC/D,SAAS,CAAC,IAAI,CAAC4D,SAAS,CAAC,CAC1B,CAACa,SAAS,CAACC,GAAG,IAAG;QAChB,IAAI,CAACnB,SAAS,GAAGmB,GAAG;MACtB,CAAC,CAAC;IACJ;EACF;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACf,SAAS,CAACZ,IAAI,EAAE;IACrB,IAAI,CAACY,SAAS,CAACgB,QAAQ,EAAE;EAC3B;EAEAC,UAAUA,CAAEH,GAAa;IACvB,IAAI,CAACtB,SAAS,GAAGP,KAAK,CAACC,OAAO,CAAC4B,GAAG,CAAC,GAAGA,GAAG,GAAG,EAAE;IAC9C,IAAI,CAAClB,gBAAgB,GAAG,IAAI,CAACJ,SAAS,CAACiB,MAAM;EAC/C;EAEAS,gBAAgBA,CAAEC,EAAO;IACvB,IAAI,CAACpB,QAAQ,GAAGoB,EAAE;EACpB;EAEAC,iBAAiBA,CAAED,EAAO;IACxB,IAAI,CAAClB,SAAS,GAAGkB,EAAE;EACrB;EAEAE,gBAAgBA,CAAEC,UAAmB;IACnC,IAAI,CAAC7B,QAAQ,GAAG6B,UAAU;EAC5B;EAEAC,QAAQA,CAAA;IACN,OAAO,IAAI,CAAC7B,OAAO,GAAG,IAAI,GAAG;MAAE8B,WAAW,EAAE;QAAEC,KAAK,EAAE;MAAK;IAAE,CAAE;EAChE;EAEAC,gBAAgBA,CAAElC,SAAgB;IAChC,IAAI,CAACE,OAAO,GAAGF,SAAS,CAACiB,MAAM,GAAG,CAAC;IACnC,IAAI,CAACb,gBAAgB,GAAGJ,SAAS,CAACiB,MAAM;IAExC,IAAI,CAAC,IAAI,CAACb,gBAAgB,EAAE;MAC1B,IAAI,CAACrC,WAAW,GAAG,KAAK;IAC1B;IAEAoE,UAAU,CAAC,MAAK;MACd,IAAI,CAAC5B,QAAQ,CAACP,SAAS,CAAC;IAC1B,CAAC,EAAE,CAAC,CAAC;EACP;EAEApC,aAAaA,CAAA;IACX,IAAI,CAACG,WAAW,GAAG,CAAC,IAAI,CAACA,WAAW;EACtC;;;uCA1GWuB,kBAAkB,EAAAxC,EAAA,CAAAsF,iBAAA,CA6BIvF,eAAe;IAAA;EAAA;;;YA7BrCyC,kBAAkB;MAAA+C,SAAA;MAAAC,YAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAlB1F,EAAA,CAAAO,UAAA,kBAAAqF,2CAAA;YAAA,OAAAD,GAAA,CAAAvB,MAAA,EAAQ;UAAA,EAAU;;;;;;;uCAblB,CACT;QACEyB,OAAO,EAAEpG,iBAAiB;QAC1BqG,WAAW,EAAEpG,UAAU,CAAC,MAAM8C,kBAAkB,CAAC;QACjDuD,KAAK,EAAE;OACR,EACD;QACEF,OAAO,EAAErG,aAAa;QACtBsG,WAAW,EAAEpG,UAAU,CAAC,MAAM8C,kBAAkB,CAAC;QACjDuD,KAAK,EAAE;OACR,CACF;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAV,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCrDH1F,EAAA,CAAAC,cAAA,UAAK;UACHD,EAAA,CAAAqG,UAAA,IAAAC,iCAAA,iBAA+F;UAG/FtG,EAAA,CAAAC,cAAA,aAA6B;UAC3BD,EAAA,CAAAE,MAAA,GACF;;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,aAAyB,YACmB;UAAAD,EAAA,CAAAE,MAAA,GAA2D;;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC1GH,EAAA,CAAAqG,UAAA,IAAAE,kCAAA,kBAAgF;UAClFvG,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,0BAKuC;;UAArCD,EAAA,CAAAO,UAAA,qBAAAiG,gEAAAC,MAAA;YAAA,OAAWd,GAAA,CAAAP,gBAAA,CAAAqB,MAAA,CAAwB;UAAA,EAAC;UAExCzG,EADE,CAAAG,YAAA,EAAkB,EACd;;;UAlBwCH,EAAA,CAAAI,SAAA,EAAiD;UAAjDJ,EAAA,CAAA0G,UAAA,UAAAf,GAAA,CAAArC,gBAAA,IAAAqC,GAAA,CAAA1C,SAAA,KAAA0C,GAAA,CAAAxC,QAAA,CAAiD;UAI3FnD,EAAA,CAAAI,SAAA,GACF;UADEJ,EAAA,CAAAK,kBAAA,MAAAL,EAAA,CAAAM,WAAA,8DACF;UAE4CN,EAAA,CAAAI,SAAA,GAA2D;UAA3DJ,EAAA,CAAA2G,kBAAA,eAAAhB,GAAA,CAAArC,gBAAA,UAAAtD,EAAA,CAAAM,WAAA,QAAAqF,GAAA,CAAA1B,WAAA,MAA2D;UAC9FjE,EAAA,CAAAI,SAAA,GAAsB;UAAtBJ,EAAA,CAAA0G,UAAA,SAAAf,GAAA,CAAArC,gBAAA,CAAsB;UAI7BtD,EAAA,CAAAI,SAAA,EAAwB;UAGxBJ,EAHA,CAAA0G,UAAA,UAAA1G,EAAA,CAAAM,WAAA,SAAAqF,GAAA,CAAA/B,MAAA,EAAwB,kBAAA+B,GAAA,CAAAzC,SAAA,CACG,aAAAyC,GAAA,CAAAxC,QAAA,CACN,gBAAAwC,GAAA,CAAA1E,WAAA,CACM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}