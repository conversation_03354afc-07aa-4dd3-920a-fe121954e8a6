{"ast": null, "code": "import { PERMISSIONS_NAMES, RowAction, SwuiGridComponent, SwuiGridDataService, SwuiTopFilterDataService } from '@skywind-group/lib-swui';\nimport * as moment from 'moment';\nimport { forkJoin, from, of, Subject, throwError } from 'rxjs';\nimport { catchError, filter, finalize, map, switchMap, take, takeUntil, tap } from 'rxjs/operators';\nimport { BoConfirmationComponent } from '../../../../../../common/components/bo-confirmation/bo-confirmation.component';\nimport { GameProviderService } from '../../../../../../common/services/game-provider.service';\nimport { GAME_STATUSES, GameService } from '../../../../../../common/services/game.service';\nimport { isLiveGame } from '../../../../../../common/typings';\nimport { GameForceRemoveDialogComponent } from '../dialogs/game-force-remove-dialog.component';\nimport { GameSettingsToRunComponent } from '../dialogs/game-settings-to-run.component';\nimport { ManageGamesDialogComponent } from '../dialogs/manage-games-dialog.component';\nimport { SCHEMA_FILTER, SCHEMA_LIST } from './games.schema';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../../../common/services/game.service\";\nimport * as i2 from \"@skywind-group/lib-swui\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"@angular/material/dialog\";\nimport * as i5 from \"../../../../../../common/services/labels.service\";\nimport * as i6 from \"../../setup-entity.service\";\nimport * as i7 from \"../games-refresh.service\";\nimport * as i8 from \"../../../../../../common/services/languages.service\";\nimport * as i9 from \"../../../../../../common/services/currency.service\";\nimport * as i10 from \"../../../../../../common/services/game-group.service\";\nimport * as i11 from \"@angular/common\";\nimport * as i12 from \"../../../../../../common/components/hints/hints.component\";\nimport * as i13 from \"../../../../../../common/components/download-csv/download-csv.component\";\nimport * as i14 from \"@angular/material/button\";\nimport * as i15 from \"@angular/material/tooltip\";\nimport * as i16 from \"@angular/material/icon\";\nfunction GeneralGamesInfoComponent_hints_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"hints\", 7);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"showCloseBtn\", false)(\"fontSize\", 16);\n  }\n}\nfunction GeneralGamesInfoComponent_button_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function GeneralGamesInfoComponent_button_3_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.setRunSettings());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"settings\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction GeneralGamesInfoComponent_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction GeneralGamesInfoComponent_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function GeneralGamesInfoComponent_ng_template_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.showManageGamesModal());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"ENTITY_SETUP.GAMES.btnManageGames\"), \" \");\n  }\n}\nexport class GeneralGamesInfoComponent {\n  set entity(value) {\n    if (!value) return;\n    this._entity = value;\n  }\n  get entity() {\n    return this._entity;\n  }\n  constructor(service, notifications, translate, dialog, swHubAuthService, gameLabelsService, setupService, gamesRefreshService, languagesService, currencyService, gameGroupService, dexieService) {\n    this.service = service;\n    this.notifications = notifications;\n    this.translate = translate;\n    this.dialog = dialog;\n    this.swHubAuthService = swHubAuthService;\n    this.gameLabelsService = gameLabelsService;\n    this.setupService = setupService;\n    this.gamesRefreshService = gamesRefreshService;\n    this.languagesService = languagesService;\n    this.currencyService = currencyService;\n    this.gameGroupService = gameGroupService;\n    this.dexieService = dexieService;\n    this.schema = SCHEMA_LIST;\n    this.filterSchema = SCHEMA_FILTER;\n    this.schemaItems = [];\n    this.loading = false;\n    this.isSuperAdmin = true;\n    this.schemaTypeName = 'entity-setup-games-tab';\n    this.runAvailable = false;\n    this.destroyed$ = new Subject();\n    this.isSuperAdmin = this.swHubAuthService.isSuperAdmin;\n    this.filterSchema = this.initFieldsFilterData(this.setupService.availableProviders, 'providerId');\n    this.gameLabelsService.getGameLabels().pipe(take(1)).subscribe(labels => {\n      this.filterSchema = this.initFieldsFilterData(labels, 'labelsId');\n    });\n    if (!this.isSuperAdmin) {\n      this.filterSchema = this.filterSchema.filter(schemaFields => schemaFields.field !== 'royalties');\n    }\n  }\n  ngOnInit() {\n    this.runAvailable = this.swHubAuthService.areGranted([PERMISSIONS_NAMES.ENTITY_GAME_URL]) && this.entity.type !== 'entity';\n    forkJoin([this.languagesService.getList(undefined, this.entity.path), this.currencyService.getList(undefined, this.entity.path), this.gameGroupService.getGameGroupsList(this.entity.path)]).pipe(take(1)).subscribe(([languages, currencies, gameGroups]) => {\n      this.languages = languages;\n      this.currencies = currencies;\n      this.gameGroups = gameGroups;\n    });\n    this.gamesRefreshService.listen('jp-info').subscribe(() => {\n      this.gridRef.dataSource.loadData();\n    });\n    this.gridRef.dataSource.requestData = {\n      path: this.entity.path,\n      isSuperAdmin: this.isSuperAdmin,\n      changeStateDisabled: this.swHubAuthService.areGranted([PERMISSIONS_NAMES.ENTITY_GAME_CHANGE_STATE, PERMISSIONS_NAMES.ENTITY_GAME_CHANGE_STATE_DISABLED]),\n      changeStateEnabled: this.swHubAuthService.areGranted([PERMISSIONS_NAMES.ENTITY_GAME_CHANGE_STATE, PERMISSIONS_NAMES.ENTITY_GAME_CHANGE_STATE_ENABLED]),\n      changeStateLiveDisabled: this.swHubAuthService.areGranted([PERMISSIONS_NAMES.ENTITY_LIVEGAME_CHANGE_STATE, PERMISSIONS_NAMES.ENTITY_LIVEGAME_CHANGE_STATE_DISABLED]),\n      changeStateLiveEnabled: this.swHubAuthService.areGranted([PERMISSIONS_NAMES.ENTITY_LIVEGAME_CHANGE_STATE, PERMISSIONS_NAMES.ENTITY_LIVEGAME_CHANGE_STATE_ENABLED]),\n      changeState: this.swHubAuthService.areGranted([PERMISSIONS_NAMES.ENTITY_GAME_CHANGE_STATE]),\n      changeStateLive: this.swHubAuthService.areGranted([PERMISSIONS_NAMES.ENTITY_LIVEGAME_CHANGE_STATE])\n    };\n    this.setActions();\n    this.initStreams();\n    this.checkAndModifyScheme();\n  }\n  ngOnDestroy() {\n    this.destroyed$.next();\n    this.destroyed$.complete();\n  }\n  initStreams() {\n    this.service._allGames.pipe(takeUntil(this.destroyed$)).subscribe(games => {\n      this.entityGames = games;\n    });\n    this.service.forceRemoveConfirm.pipe(takeUntil(this.destroyed$)).subscribe(code => this.showForceRemoveConfirmation(code));\n  }\n  checkStringBoolean(val) {\n    switch (val) {\n      case 'true':\n        return true;\n      case 'false':\n        return false;\n      default:\n        return val;\n    }\n  }\n  showManageGamesModal() {\n    this.service.getAllGames(this.entity.path, true, true).pipe(switchMap(games => this.dialog.open(ManageGamesDialogComponent, {\n      width: '1200px',\n      maxHeight: '90vh',\n      data: {\n        entity: this.entity,\n        entityGames: games,\n        allowFullManagement: this.allowFullManagement\n      },\n      disableClose: true\n    }).afterClosed())).pipe(filter(result => typeof result !== 'undefined'), tap(result => this.onGamesAppliedFn(result)), switchMap(() => this.service.getAllGames(this.entity.path, true, true)), map(games => {\n      const changeState = this.swHubAuthService.areGranted([PERMISSIONS_NAMES.ENTITY_GAME_CHANGE_STATE]);\n      const changeStateDisabled = this.swHubAuthService.areGranted([PERMISSIONS_NAMES.ENTITY_GAME_CHANGE_STATE, PERMISSIONS_NAMES.ENTITY_GAME_CHANGE_STATE_DISABLED]);\n      const changeStateEnabled = this.swHubAuthService.areGranted([PERMISSIONS_NAMES.ENTITY_GAME_CHANGE_STATE, PERMISSIONS_NAMES.ENTITY_GAME_CHANGE_STATE_ENABLED]);\n      const changeStateLive = this.swHubAuthService.areGranted([PERMISSIONS_NAMES.ENTITY_LIVEGAME_CHANGE_STATE]);\n      const changeStateLiveDisabled = this.swHubAuthService.areGranted([PERMISSIONS_NAMES.ENTITY_LIVEGAME_CHANGE_STATE, PERMISSIONS_NAMES.ENTITY_LIVEGAME_CHANGE_STATE_DISABLED]);\n      const changeStateLiveEnabled = this.swHubAuthService.areGranted([PERMISSIONS_NAMES.ENTITY_LIVEGAME_CHANGE_STATE, PERMISSIONS_NAMES.ENTITY_LIVEGAME_CHANGE_STATE_ENABLED]);\n      return games.map(game => {\n        game._meta = {\n          changeState,\n          changeStateLive,\n          isSuperAdmin: this.isSuperAdmin,\n          changeStateDisabled: changeStateDisabled,\n          changeStateEnabled: changeStateEnabled,\n          changeStateLiveDisabled,\n          changeStateLiveEnabled\n        };\n        return game;\n      });\n    }), takeUntil(this.destroyed$), finalize(() => {\n      this.gridRef.dataSource.loadData();\n      this.gamesRefreshService.refresh('general');\n    })).subscribe(games => this.entityGames = games);\n  }\n  onWidgetActionFn({\n    field,\n    row,\n    payload\n  }) {\n    switch (field) {\n      case 'status':\n        this.handleWidgetStatusAction(row, payload);\n        break;\n      case 'royalties':\n        this.handleRoyaltiesAction(row, payload);\n        break;\n      default:\n        break;\n    }\n  }\n  onGamesAppliedFn({\n    addedGames,\n    removedGames\n  }) {\n    if (addedGames && addedGames.length) {\n      this.showApplyNotification(addedGames.length);\n    }\n    if (removedGames && removedGames.length) {\n      this.showApplyNotification(removedGames.length, true);\n    }\n  }\n  removeGames(gameCode, force = false, isLive = false) {\n    const req$ = isLive ? this.service.removeEntityLiveGame(gameCode, this.entity.path, force) : this.service.removeEntityGame(gameCode, this.entity.path, force);\n    req$.pipe(takeUntil(this.destroyed$), tap(() => this.showApplyNotification(1, true, force))).subscribe(() => {\n      this.gridRef.dataSource.loadData();\n      this.gamesRefreshService.refresh('general');\n    });\n  }\n  downloadCsv() {\n    this.loading = true;\n    const fileName = `${this.entity.name} Export games list ${moment().format('YYYY-MM-DD HH:MM')}`;\n    this.service.downloadCsv(this.entity.path, fileName).pipe(catchError(err => {\n      this.loading = false;\n      return throwError(err);\n    }), take(1)).subscribe(() => {\n      this.loading = false;\n    });\n  }\n  setRunSettings() {\n    from(this.dexieService.getRunSettings(this.entity.path)).pipe(switchMap(data => this.dialog.open(GameSettingsToRunComponent, {\n      data: {\n        languages: this.languages,\n        currencies: this.currencies,\n        gameGroups: this.gameGroups,\n        language: this.entity.defaultLanguage,\n        currency: this.entity.defaultCurrency,\n        state: data\n      }\n    }).afterClosed()), filter(data => !!data), take(1)).subscribe(data => {\n      this.dexieService.putRunSettings(this.entity.path, data);\n    });\n  }\n  showApplyNotification(amount, removed = false, _ = false) {\n    let single = amount === 1;\n    let message;\n    if (removed) {\n      if (single) {\n        message = 'ENTITY_SETUP.GAMES.notificationSingleGameRemoved';\n      } else {\n        message = 'ENTITY_SETUP.GAMES.notificationMultipleGamesRemoved';\n      }\n    } else {\n      if (single) {\n        message = 'ENTITY_SETUP.GAMES.notificationSingleGameAdded';\n      } else {\n        message = 'ENTITY_SETUP.GAMES.notificationMultipleGamesAdded';\n      }\n    }\n    this.translate.get(message, {\n      amount: amount\n    }).subscribe(msg => this.notifications.success(msg, ''));\n  }\n  refreshGames() {\n    this.gridRef.loading$.next(true);\n    this.gridRef.dataSource.loadData();\n  }\n  setActions() {\n    this.actions = [];\n    if (this.runAvailable) {\n      this.actions.push(new RowAction({\n        icon: 'play_arrow',\n        title: 'Play fun game',\n        inMenu: false,\n        fn: row => {\n          this.runGame(row);\n        },\n        canActivateFn: ({\n          status\n        }) => ['normal', 'test'].includes(status)\n      }));\n    }\n    this.actions.push(new RowAction({\n      icon: 'delete',\n      title: 'Remove',\n      inMenu: false,\n      fn: row => this.removeGames(row.code, false, isLiveGame(row)),\n      canActivateFn: row => !isLiveGame(row) ? this.swHubAuthService.areGranted([PERMISSIONS_NAMES.ENTITY_GAME_CHANGE_STATE]) : this.swHubAuthService.areGranted([PERMISSIONS_NAMES.ENTITY_LIVEGAME, PERMISSIONS_NAMES.ENTITY_LIVEGAME_REMOVE])\n    }));\n  }\n  runGame(game) {\n    from(this.dexieService.getRunSettings(this.entity.path)).pipe(switchMap(data => {\n      if (data.doNotShow) {\n        return of(data);\n      }\n      return this.dialog.open(GameSettingsToRunComponent, {\n        data: {\n          languages: this.languages,\n          currencies: this.currencies,\n          gameGroups: this.gameGroups,\n          language: this.entity.defaultLanguage,\n          currency: this.entity.defaultCurrency,\n          state: data\n        }\n      }).afterClosed();\n    }), take(1), filter(data => !!data), switchMap(data => {\n      this.dexieService.putRunSettings(this.entity.path, data);\n      const value = Object.entries(data).reduce((res, [key, val]) => {\n        if (val && key !== 'doNotShow') {\n          res[key] = val;\n        }\n        return res;\n      }, {});\n      const settings = {\n        ...value,\n        path: this.entity.path,\n        gameCode: game.code\n      };\n      return this.service.getFunUrl(settings);\n    })).subscribe(url => {\n      window.open(url, '_blank');\n    });\n  }\n  handleWidgetStatusAction(row, payload) {\n    let statusGame;\n    if (payload.status === GAME_STATUSES.HIDDEN) {\n      statusGame = this.service.setStatus(row, payload.status, this._entity?.path);\n    } else if (payload.status === GAME_STATUSES.TEST) {\n      statusGame = this.service.setStatus(row, payload.status, this._entity?.path);\n    } else if (payload.status === GAME_STATUSES.SUSPENDED) {\n      statusGame = this.service.suspendedGame(this._entity?.path, row.code, true, isLiveGame(row));\n    } else if (payload.status === GAME_STATUSES.NORMAL) {\n      statusGame = this.service.unSuspendedGame(this._entity?.path, row.code, isLiveGame(row));\n    } else if (payload.status === GAME_STATUSES.KILL_SESSION) {\n      statusGame = this.setStatusWithoutKilling(row);\n    }\n    statusGame.pipe(map(response => Object.assign(row, response)), tap(() => this.notifications.success(this.translate.instant('ENTITY_SETUP.WHITELISTING.notificationChanged'))), takeUntil(this.destroyed$), finalize(() => {\n      this.refreshGames();\n      this.gamesRefreshService.refresh('general');\n      payload.onCompleteFn();\n    })).subscribe();\n  }\n  setStatusWithoutKilling(row) {\n    return this.dialog.open(BoConfirmationComponent, {\n      width: '500px',\n      data: {\n        message: 'ENTITY_SETUP.GAMES.MODALS.kill_sessions'\n      },\n      disableClose: true\n    }).afterClosed().pipe(filter(val => !!val), switchMap(() => this.service.suspendedGame(this._entity?.path, row.code, false, isLiveGame(row))));\n  }\n  handleRoyaltiesAction(row, payload) {\n    let royalties = parseFloat(parseFloat(payload.value).toFixed(2)) / 100;\n    let sub = this.service.updateRoyalties(row.code, this.entity.path, isLiveGame(row), royalties).subscribe(() => {}, () => {}, () => {\n      payload.onCompleteFn();\n      sub.unsubscribe();\n    });\n  }\n  checkAndModifyScheme() {\n    this.schemaItems = this.schema.filter(item => item.isList);\n    let exclude = [];\n    if (!this.allowFullManagement) {\n      exclude.push('royalties');\n    }\n    if (!this.entity?.jurisdiction || !this.entity?.jurisdiction[0] || this.entity?.jurisdiction[0].code !== 'IT') {\n      exclude.push('aamsCode');\n      exclude.push('mustWinJackpotBundled');\n    }\n    this.schemaItems = [...this.schemaItems.filter(item => exclude.indexOf(item.field) === -1)];\n  }\n  showForceRemoveConfirmation(code) {\n    this.dialog.open(GameForceRemoveDialogComponent, {\n      width: '600px',\n      data: {\n        entity: this.entity,\n        games: this.entityGames.filter(game => code === game.code)\n      },\n      disableClose: true\n    }).afterClosed().pipe(filter(data => !!data), take(1)).subscribe(({\n      slot,\n      live\n    }) => {\n      if (slot && slot.length) {\n        this.removeGames(slot, true);\n      }\n      if (live && live.length) {\n        this.removeGames(live, true, true);\n      }\n    });\n  }\n  initFieldsFilterData(items, fieldName) {\n    this.filterSchema.find(schemaFields => schemaFields.field === fieldName).data = items?.map(item => ({\n      id: item.id,\n      text: item.title\n    }));\n    return this.filterSchema;\n  }\n  static {\n    this.ɵfac = function GeneralGamesInfoComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || GeneralGamesInfoComponent)(i0.ɵɵdirectiveInject(i1.GameService), i0.ɵɵdirectiveInject(i2.SwuiNotificationsService), i0.ɵɵdirectiveInject(i3.TranslateService), i0.ɵɵdirectiveInject(i4.MatDialog), i0.ɵɵdirectiveInject(i2.SwHubAuthService), i0.ɵɵdirectiveInject(i5.LabelsService), i0.ɵɵdirectiveInject(i6.SetupEntityService), i0.ɵɵdirectiveInject(i7.GamesRefreshService), i0.ɵɵdirectiveInject(i8.LanguagesService), i0.ɵɵdirectiveInject(i9.CurrencyService), i0.ɵɵdirectiveInject(i10.GameGroupService), i0.ɵɵdirectiveInject(i2.SwDexieService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: GeneralGamesInfoComponent,\n      selectors: [[\"general-games-info\"]],\n      viewQuery: function GeneralGamesInfoComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(SwuiGridComponent, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.gridRef = _t.first);\n        }\n      },\n      inputs: {\n        allowFullManagement: \"allowFullManagement\",\n        entity: \"entity\"\n      },\n      features: [i0.ɵɵProvidersFeature([GameProviderService, SwuiTopFilterDataService, {\n        provide: SwuiGridDataService,\n        useExisting: GameService\n      }])],\n      decls: 8,\n      vars: 13,\n      consts: [[\"projectedButtons\", \"\"], [3, \"schema\"], [\"message\", \"Entity is in test mode\", 3, \"showCloseBtn\", \"fontSize\", 4, \"ngIf\"], [3, \"widgetActionEmitted\", \"schema\", \"rowActions\", \"ignorePlainLink\", \"gridId\", \"rowActionsColumnTitle\", \"savedFilteredPageName\", \"disableRefreshAction\", \"columnsManagement\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Run game settings\", 3, \"click\", 4, \"ngIf\"], [3, \"downloadCsv\", \"loading\"], [4, \"ngTemplateOutlet\"], [\"message\", \"Entity is in test mode\", 3, \"showCloseBtn\", \"fontSize\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Run game settings\", 3, \"click\"], [\"mat-flat-button\", \"\", \"color\", \"primary\", 1, \"ml-10\", \"mr-5\", 3, \"click\"]],\n      template: function GeneralGamesInfoComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelement(0, \"lib-swui-schema-top-filter\", 1);\n          i0.ɵɵtemplate(1, GeneralGamesInfoComponent_hints_1_Template, 1, 2, \"hints\", 2);\n          i0.ɵɵelementStart(2, \"lib-swui-grid\", 3);\n          i0.ɵɵlistener(\"widgetActionEmitted\", function GeneralGamesInfoComponent_Template_lib_swui_grid_widgetActionEmitted_2_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onWidgetActionFn($event));\n          });\n          i0.ɵɵtemplate(3, GeneralGamesInfoComponent_button_3_Template, 3, 0, \"button\", 4);\n          i0.ɵɵelementStart(4, \"download-csv\", 5);\n          i0.ɵɵlistener(\"downloadCsv\", function GeneralGamesInfoComponent_Template_download_csv_downloadCsv_4_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.downloadCsv());\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(5, GeneralGamesInfoComponent_ng_container_5_Template, 1, 0, \"ng-container\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(6, GeneralGamesInfoComponent_ng_template_6_Template, 3, 3, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          const projectedButtons_r5 = i0.ɵɵreference(7);\n          i0.ɵɵproperty(\"schema\", ctx.filterSchema);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.entity.status === \"test\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"schema\", ctx.schemaItems)(\"rowActions\", ctx.actions)(\"ignorePlainLink\", true)(\"gridId\", ctx.schemaTypeName)(\"rowActionsColumnTitle\", \"\")(\"savedFilteredPageName\", \"general-games-info\")(\"disableRefreshAction\", true)(\"columnsManagement\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.runAvailable);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"loading\", ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngTemplateOutlet\", projectedButtons_r5);\n        }\n      },\n      dependencies: [i11.NgIf, i11.NgTemplateOutlet, i2.SwuiSchemaTopFilterComponent, i12.HintsComponent, i2.SwuiGridComponent, i13.DownloadCsvComponent, i14.MatButton, i14.MatIconButton, i15.MatTooltip, i16.MatIcon, i3.TranslatePipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["PERMISSIONS_NAMES", "RowAction", "SwuiGridComponent", "SwuiGridDataService", "SwuiTopFilterDataService", "moment", "fork<PERSON><PERSON>n", "from", "of", "Subject", "throwError", "catchError", "filter", "finalize", "map", "switchMap", "take", "takeUntil", "tap", "BoConfirmationComponent", "GameProviderService", "GAME_STATUSES", "GameService", "isLiveGame", "GameForceRemoveDialogComponent", "GameSettingsToRunComponent", "ManageGamesDialogComponent", "SCHEMA_FILTER", "SCHEMA_LIST", "i0", "ɵɵelement", "ɵɵproperty", "ɵɵelementStart", "ɵɵlistener", "GeneralGamesInfoComponent_button_3_Template_button_click_0_listener", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "setRunSettings", "ɵɵtext", "ɵɵelementEnd", "ɵɵelementContainer", "GeneralGamesInfoComponent_ng_template_6_Template_button_click_0_listener", "_r4", "showManageGamesModal", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "GeneralGamesInfoComponent", "entity", "value", "_entity", "constructor", "service", "notifications", "translate", "dialog", "swHubAuthService", "gameLabelsService", "setupService", "gamesRefreshService", "languagesService", "currencyService", "gameGroupService", "dexieService", "schema", "filterSchema", "schemaItems", "loading", "isSuperAdmin", "schemaTypeName", "runAvailable", "destroyed$", "initFieldsFilterData", "availableProviders", "getGameLabels", "pipe", "subscribe", "labels", "schemaFields", "field", "ngOnInit", "areGranted", "ENTITY_GAME_URL", "type", "getList", "undefined", "path", "getGameGroupsList", "languages", "currencies", "gameGroups", "listen", "gridRef", "dataSource", "loadData", "requestData", "changeStateDisabled", "ENTITY_GAME_CHANGE_STATE", "ENTITY_GAME_CHANGE_STATE_DISABLED", "changeStateEnabled", "ENTITY_GAME_CHANGE_STATE_ENABLED", "changeStateLiveDisabled", "ENTITY_LIVEGAME_CHANGE_STATE", "ENTITY_LIVEGAME_CHANGE_STATE_DISABLED", "changeStateLiveEnabled", "ENTITY_LIVEGAME_CHANGE_STATE_ENABLED", "changeState", "changeStateLive", "setActions", "initStreams", "checkAndModifyScheme", "ngOnDestroy", "next", "complete", "_allGames", "games", "entityGames", "forceRemoveConfirm", "code", "showForceRemoveConfirmation", "checkStringBoolean", "val", "getAllGames", "open", "width", "maxHeight", "data", "allowFullManagement", "disableClose", "afterClosed", "result", "onGamesAppliedFn", "game", "_meta", "refresh", "onWidgetActionFn", "row", "payload", "handleWidgetStatusAction", "handleRoyaltiesAction", "addedGames", "removedGames", "length", "showApplyNotification", "removeGames", "gameCode", "force", "isLive", "req$", "removeEntityLiveGame", "removeEntityGame", "downloadCsv", "fileName", "name", "format", "err", "getRunSettings", "language", "defaultLanguage", "currency", "defaultCurrency", "state", "putRunSettings", "amount", "removed", "_", "single", "message", "get", "msg", "success", "refreshGames", "loading$", "actions", "push", "icon", "title", "inMenu", "fn", "runGame", "canActivateFn", "status", "includes", "ENTITY_LIVEGAME", "ENTITY_LIVEGAME_REMOVE", "doNotShow", "Object", "entries", "reduce", "res", "key", "settings", "getFunUrl", "url", "window", "statusGame", "HIDDEN", "setStatus", "TEST", "SUSPENDED", "suspendedGame", "NORMAL", "unSuspendedGame", "KILL_SESSION", "setStatusWithoutKilling", "response", "assign", "instant", "onCompleteFn", "royalties", "parseFloat", "toFixed", "sub", "updateRoyalties", "unsubscribe", "item", "isList", "exclude", "jurisdiction", "indexOf", "slot", "live", "items", "fieldName", "find", "id", "text", "ɵɵdirectiveInject", "i1", "i2", "SwuiNotificationsService", "i3", "TranslateService", "i4", "MatDialog", "SwHubAuthService", "i5", "LabelsService", "i6", "SetupEntityService", "i7", "GamesRefreshService", "i8", "LanguagesService", "i9", "CurrencyService", "i10", "GameGroupService", "SwDexieService", "selectors", "viewQuery", "GeneralGamesInfoComponent_Query", "rf", "ctx", "provide", "useExisting", "decls", "vars", "consts", "template", "GeneralGamesInfoComponent_Template", "ɵɵtemplate", "GeneralGamesInfoComponent_hints_1_Template", "GeneralGamesInfoComponent_Template_lib_swui_grid_widgetActionEmitted_2_listener", "$event", "_r1", "GeneralGamesInfoComponent_button_3_Template", "GeneralGamesInfoComponent_Template_download_csv_downloadCsv_4_listener", "GeneralGamesInfoComponent_ng_container_5_Template", "GeneralGamesInfoComponent_ng_template_6_Template", "ɵɵtemplateRefExtractor", "projectedButtons_r5"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-games/general-games-info/general-games-info.component.ts", "/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-games/general-games-info/general-games-info.component.html"], "sourcesContent": ["import { Component, Input, OnInit, ViewChild } from '@angular/core';\nimport { MatDialog } from '@angular/material/dialog';\nimport { TranslateService } from '@ngx-translate/core';\nimport {\n  PERMISSIONS_NAMES,\n  RowAction,\n  SwDexieService,\n  SwHubAuthService,\n  SwuiGridComponent,\n  SwuiGridDataService,\n  SwuiGridField,\n  SwuiNotificationsService,\n  SwuiTopFilterDataService\n} from '@skywind-group/lib-swui';\nimport * as moment from 'moment';\nimport { forkJoin, from, Observable, of, Subject, throwError } from 'rxjs';\nimport { catchError, filter, finalize, map, switchMap, take, takeUntil, tap } from 'rxjs/operators';\nimport { BoConfirmationComponent } from '../../../../../../common/components/bo-confirmation/bo-confirmation.component';\nimport { Entity } from '../../../../../../common/models/entity.model';\nimport { GameGroup } from '../../../../../../common/models/game-group.model';\nimport { CurrencyService } from '../../../../../../common/services/currency.service';\nimport { GameGroupService } from '../../../../../../common/services/game-group.service';\nimport { GameProviderService } from '../../../../../../common/services/game-provider.service';\nimport { GAME_STATUSES, GameService } from '../../../../../../common/services/game.service';\nimport { LabelsService } from '../../../../../../common/services/labels.service';\nimport { LanguagesService } from '../../../../../../common/services/languages.service';\nimport { Currency, Game, isLiveGame, Language } from '../../../../../../common/typings';\nimport { Label } from '../../../../../../common/typings/label';\nimport { SetupEntityService } from '../../setup-entity.service';\nimport { GameForceRemoveDialogComponent, GameForceRemoveDialogData } from '../dialogs/game-force-remove-dialog.component';\nimport { GameSettingsToRunComponent } from '../dialogs/game-settings-to-run.component';\nimport { ManageGamesDialogComponent, ManageGamesDialogData, ManageGamesDialogResult } from '../dialogs/manage-games-dialog.component';\nimport { GamesRefreshService } from '../games-refresh.service';\nimport { SCHEMA_FILTER, SCHEMA_LIST } from './games.schema';\n\n@Component({\n  selector: 'general-games-info',\n  templateUrl: './general-games-info.component.html',\n  styleUrls: ['./general-games-info.component.scss'],\n  providers: [\n    GameProviderService,\n    SwuiTopFilterDataService,\n    { provide: SwuiGridDataService, useExisting: GameService },\n  ]\n})\nexport class GeneralGamesInfoComponent implements OnInit {\n  @Input() allowFullManagement: boolean;\n\n  @Input()\n  set entity( value: Entity ) {\n    if (!value) return;\n    this._entity = value;\n  }\n\n  get entity(): Entity {\n    return this._entity;\n  }\n\n  schema: SwuiGridField[] = SCHEMA_LIST;\n  filterSchema: SwuiGridField[] = SCHEMA_FILTER;\n  schemaItems = [];\n  actions: RowAction[];\n  loading: boolean = false;\n  isSuperAdmin: boolean = true;\n  languages?: Language[];\n  currencies?: Currency[];\n  gameGroups: GameGroup[];\n\n  entityGames: Game[];\n  schemaTypeName = 'entity-setup-games-tab';\n  runAvailable = false;\n\n  @ViewChild(SwuiGridComponent, { static: true }) gridRef?: SwuiGridComponent<Game>;\n\n  private _entity: Entity;\n  private readonly destroyed$ = new Subject<void>();\n\n  constructor( private service: GameService,\n               private notifications: SwuiNotificationsService,\n               private translate: TranslateService,\n               private dialog: MatDialog,\n               private swHubAuthService: SwHubAuthService,\n               private gameLabelsService: LabelsService,\n               private setupService: SetupEntityService,\n               private gamesRefreshService: GamesRefreshService,\n               private languagesService: LanguagesService<Language>,\n               private currencyService: CurrencyService<Currency>,\n               private gameGroupService: GameGroupService,\n               private dexieService: SwDexieService\n  ) {\n    this.isSuperAdmin = this.swHubAuthService.isSuperAdmin;\n    this.filterSchema = this.initFieldsFilterData(this.setupService.availableProviders, 'providerId');\n\n    this.gameLabelsService.getGameLabels().pipe(\n      take(1))\n      .subscribe(( labels: Label[] ) => {\n        this.filterSchema = this.initFieldsFilterData(labels, 'labelsId');\n      });\n\n    if (!this.isSuperAdmin) {\n      this.filterSchema = this.filterSchema.filter(schemaFields => schemaFields.field !== 'royalties');\n    }\n  }\n\n  ngOnInit() {\n    this.runAvailable = this.swHubAuthService.areGranted([PERMISSIONS_NAMES.ENTITY_GAME_URL]) && this.entity.type !== 'entity';\n    forkJoin([\n      this.languagesService.getList(undefined, this.entity.path),\n      this.currencyService.getList(undefined, this.entity.path),\n      this.gameGroupService.getGameGroupsList(this.entity.path)\n    ])\n      .pipe(take(1))\n      .subscribe(( [languages, currencies, gameGroups] ) => {\n        this.languages = languages;\n        this.currencies = currencies;\n        this.gameGroups = gameGroups;\n      });\n\n    this.gamesRefreshService.listen('jp-info')\n      .subscribe(() => {\n        this.gridRef.dataSource.loadData();\n      });\n\n    this.gridRef.dataSource.requestData = {\n      path: this.entity.path,\n      isSuperAdmin: this.isSuperAdmin,\n      changeStateDisabled: this.swHubAuthService.areGranted([\n        PERMISSIONS_NAMES.ENTITY_GAME_CHANGE_STATE,\n        PERMISSIONS_NAMES.ENTITY_GAME_CHANGE_STATE_DISABLED\n      ]),\n      changeStateEnabled: this.swHubAuthService.areGranted([\n        PERMISSIONS_NAMES.ENTITY_GAME_CHANGE_STATE,\n        PERMISSIONS_NAMES.ENTITY_GAME_CHANGE_STATE_ENABLED\n      ]),\n      changeStateLiveDisabled: this.swHubAuthService.areGranted([\n        PERMISSIONS_NAMES.ENTITY_LIVEGAME_CHANGE_STATE,\n        PERMISSIONS_NAMES.ENTITY_LIVEGAME_CHANGE_STATE_DISABLED\n      ]),\n      changeStateLiveEnabled: this.swHubAuthService.areGranted([\n        PERMISSIONS_NAMES.ENTITY_LIVEGAME_CHANGE_STATE,\n        PERMISSIONS_NAMES.ENTITY_LIVEGAME_CHANGE_STATE_ENABLED\n      ]),\n      changeState: this.swHubAuthService.areGranted([\n        PERMISSIONS_NAMES.ENTITY_GAME_CHANGE_STATE\n      ]),\n      changeStateLive: this.swHubAuthService.areGranted([\n        PERMISSIONS_NAMES.ENTITY_LIVEGAME_CHANGE_STATE\n      ])\n    };\n\n    this.setActions();\n    this.initStreams();\n    this.checkAndModifyScheme();\n  }\n\n  ngOnDestroy(): void {\n    this.destroyed$.next();\n    this.destroyed$.complete();\n  }\n\n  initStreams() {\n    this.service._allGames\n      .pipe(takeUntil(this.destroyed$)).subscribe(( games: Game[] ) => {\n      this.entityGames = games;\n    });\n\n    this.service.forceRemoveConfirm\n      .pipe(takeUntil(this.destroyed$))\n      .subscribe(code => this.showForceRemoveConfirmation(code));\n  }\n\n  checkStringBoolean( val: any ) {\n    switch (val) {\n      case 'true':\n        return true;\n      case 'false':\n        return false;\n      default:\n        return val;\n    }\n  }\n\n  showManageGamesModal() {\n    this.service.getAllGames(this.entity.path, true, true).pipe(\n      switchMap(( games: Game[] ) => this.dialog.open(ManageGamesDialogComponent, {\n          width: '1200px',\n          maxHeight: '90vh',\n          data: <ManageGamesDialogData>{\n            entity: this.entity,\n            entityGames: games,\n            allowFullManagement: this.allowFullManagement\n          },\n          disableClose: true\n        }).afterClosed()\n      )\n    ).pipe(\n      filter(( result ) => typeof result !== 'undefined'),\n      tap(( result: ManageGamesDialogResult ) => this.onGamesAppliedFn(result)),\n      switchMap(() => this.service.getAllGames(this.entity.path, true, true)),\n      map(( games: Game[] ) => {\n        const changeState = this.swHubAuthService.areGranted([\n          PERMISSIONS_NAMES.ENTITY_GAME_CHANGE_STATE\n        ]);\n        const changeStateDisabled = this.swHubAuthService.areGranted([\n          PERMISSIONS_NAMES.ENTITY_GAME_CHANGE_STATE,\n          PERMISSIONS_NAMES.ENTITY_GAME_CHANGE_STATE_DISABLED\n        ]);\n        const changeStateEnabled = this.swHubAuthService.areGranted([\n          PERMISSIONS_NAMES.ENTITY_GAME_CHANGE_STATE,\n          PERMISSIONS_NAMES.ENTITY_GAME_CHANGE_STATE_ENABLED\n        ]);\n        const changeStateLive = this.swHubAuthService.areGranted([\n          PERMISSIONS_NAMES.ENTITY_LIVEGAME_CHANGE_STATE\n        ]);\n        const changeStateLiveDisabled = this.swHubAuthService.areGranted([\n          PERMISSIONS_NAMES.ENTITY_LIVEGAME_CHANGE_STATE,\n          PERMISSIONS_NAMES.ENTITY_LIVEGAME_CHANGE_STATE_DISABLED\n        ]);\n        const changeStateLiveEnabled = this.swHubAuthService.areGranted([\n          PERMISSIONS_NAMES.ENTITY_LIVEGAME_CHANGE_STATE,\n          PERMISSIONS_NAMES.ENTITY_LIVEGAME_CHANGE_STATE_ENABLED\n        ]);\n\n        return games.map(game => {\n          game._meta = {\n            changeState,\n            changeStateLive,\n            isSuperAdmin: this.isSuperAdmin,\n            changeStateDisabled: changeStateDisabled,\n            changeStateEnabled: changeStateEnabled,\n            changeStateLiveDisabled,\n            changeStateLiveEnabled\n          };\n          return game;\n        });\n      }),\n      takeUntil(this.destroyed$),\n      finalize(() => {\n        this.gridRef.dataSource.loadData();\n        this.gamesRefreshService.refresh('general');\n      })\n    ).subscribe(( games: Game[] ) => this.entityGames = games);\n  }\n\n  onWidgetActionFn( { field, row, payload } ) {\n    switch (field) {\n      case 'status':\n        this.handleWidgetStatusAction(row, payload);\n        break;\n\n      case 'royalties':\n        this.handleRoyaltiesAction(row, payload);\n        break;\n\n      default:\n        break;\n    }\n  }\n\n  onGamesAppliedFn( { addedGames, removedGames }: ManageGamesDialogResult ) {\n    if (addedGames && addedGames.length) {\n      this.showApplyNotification(addedGames.length);\n    }\n    if (removedGames && removedGames.length) {\n      this.showApplyNotification(removedGames.length, true);\n    }\n  }\n\n  removeGames(gameCode: string, force: boolean = false, isLive = false) {\n    const req$ = isLive\n      ? this.service.removeEntityLiveGame(gameCode, this.entity.path, force)\n      : this.service.removeEntityGame(gameCode, this.entity.path, force);\n\n    req$\n      .pipe(\n        takeUntil(this.destroyed$),\n        tap(() => this.showApplyNotification(1, true, force))\n      ).subscribe(() => {\n        this.gridRef.dataSource.loadData();\n        this.gamesRefreshService.refresh('general');\n      }\n    );\n  }\n\n  downloadCsv() {\n    this.loading = true;\n    const fileName = `${this.entity.name} Export games list ${moment().format('YYYY-MM-DD HH:MM')}`;\n    this.service.downloadCsv(this.entity.path, fileName).pipe(\n      catchError(( err ) => {\n        this.loading = false;\n        return throwError(err);\n      }),\n      take(1)\n    ).subscribe(() => {\n      this.loading = false;\n    });\n  }\n\n  setRunSettings() {\n    from(this.dexieService.getRunSettings(this.entity.path))\n      .pipe(\n        switchMap(data => this.dialog.open(GameSettingsToRunComponent, {\n          data: {\n            languages: this.languages,\n            currencies: this.currencies,\n            gameGroups: this.gameGroups,\n            language: this.entity.defaultLanguage,\n            currency: this.entity.defaultCurrency,\n            state: data\n          }\n        })\n          .afterClosed()),\n        filter(data => !!data),\n        take(1)\n      )\n      .subscribe(data => {\n        this.dexieService.putRunSettings(this.entity.path, data);\n      });\n  }\n\n  private showApplyNotification( amount: number, removed: boolean = false, _: boolean = false ) {\n    let single = amount === 1;\n    let message;\n    if (removed) {\n      if (single) {\n        message = 'ENTITY_SETUP.GAMES.notificationSingleGameRemoved';\n      } else {\n        message = 'ENTITY_SETUP.GAMES.notificationMultipleGamesRemoved';\n      }\n    } else {\n      if (single) {\n        message = 'ENTITY_SETUP.GAMES.notificationSingleGameAdded';\n      } else {\n        message = 'ENTITY_SETUP.GAMES.notificationMultipleGamesAdded';\n      }\n    }\n    this.translate.get(message, { amount: amount })\n      .subscribe(msg => this.notifications.success(msg, ''));\n  }\n\n  private refreshGames() {\n    this.gridRef.loading$.next(true);\n    this.gridRef.dataSource.loadData();\n  }\n\n  private setActions() {\n    this.actions = [];\n\n    if (this.runAvailable) {\n      this.actions.push(new RowAction({\n        icon: 'play_arrow',\n        title: 'Play fun game',\n        inMenu: false,\n        fn: ( row ) => {\n          this.runGame(row);\n        },\n        canActivateFn: ( { status } ) => ['normal', 'test'].includes(status)\n      }));\n    }\n\n    this.actions.push(new RowAction({\n      icon: 'delete',\n      title: 'Remove',\n      inMenu: false,\n      fn: ( row ) => this.removeGames(row.code, false, isLiveGame(row)),\n      canActivateFn: ( row: Game ) => !isLiveGame(row)\n        ? this.swHubAuthService.areGranted([PERMISSIONS_NAMES.ENTITY_GAME_CHANGE_STATE])\n        : this.swHubAuthService.areGranted([PERMISSIONS_NAMES.ENTITY_LIVEGAME, PERMISSIONS_NAMES.ENTITY_LIVEGAME_REMOVE]),\n    }));\n  }\n\n  private runGame( game: Game ) {\n    from(this.dexieService.getRunSettings(this.entity.path))\n      .pipe(\n        switchMap(data => {\n          if (data.doNotShow) {\n            return of(data);\n          }\n\n          return this.dialog.open(GameSettingsToRunComponent, {\n            data: {\n              languages: this.languages,\n              currencies: this.currencies,\n              gameGroups: this.gameGroups,\n              language: this.entity.defaultLanguage,\n              currency: this.entity.defaultCurrency,\n              state: data\n            }\n          })\n            .afterClosed();\n        }),\n        take(1),\n        filter(data => !!data),\n        switchMap(data => {\n          this.dexieService.putRunSettings(this.entity.path, data);\n\n          const value = Object.entries(data).reduce(( res: any, [key, val] ) => {\n            if (val && key !== 'doNotShow') {\n              res[key] = val;\n            }\n\n            return res;\n          }, {});\n\n          const settings = {\n            ...value,\n            path: this.entity.path,\n            gameCode: game.code\n          };\n\n          return this.service.getFunUrl(settings);\n        })\n      )\n      .subscribe(url => {\n        window.open(url, '_blank');\n      });\n  }\n\n  private handleWidgetStatusAction( row: Game, payload: any ) {\n    let statusGame;\n    if (payload.status === GAME_STATUSES.HIDDEN) {\n      statusGame = this.service.setStatus(row, payload.status, this._entity?.path);\n    } else if (payload.status === GAME_STATUSES.TEST) {\n      statusGame = this.service.setStatus(row, payload.status, this._entity?.path);\n    } else if (payload.status === GAME_STATUSES.SUSPENDED) {\n      statusGame = this.service.suspendedGame(this._entity?.path, row.code, true, isLiveGame(row));\n    } else if (payload.status === GAME_STATUSES.NORMAL) {\n      statusGame = this.service.unSuspendedGame(this._entity?.path, row.code, isLiveGame(row));\n    } else if (payload.status === GAME_STATUSES.KILL_SESSION) {\n      statusGame = this.setStatusWithoutKilling(row);\n    }\n\n    statusGame.pipe(\n      map(( response: Game ) => Object.assign(row, response)),\n      tap(() => this.notifications.success(this.translate.instant('ENTITY_SETUP.WHITELISTING.notificationChanged'))),\n      takeUntil(this.destroyed$),\n      finalize(() => {\n        this.refreshGames();\n        this.gamesRefreshService.refresh('general');\n        payload.onCompleteFn();\n      })\n    ).subscribe();\n  }\n\n  private setStatusWithoutKilling( row: Game ): Observable<any> {\n    return this.dialog.open(BoConfirmationComponent, {\n      width: '500px',\n      data: { message: 'ENTITY_SETUP.GAMES.MODALS.kill_sessions' },\n      disableClose: true\n    }).afterClosed().pipe(\n      filter(val => !!val),\n      switchMap(() => this.service.suspendedGame(this._entity?.path, row.code, false, isLiveGame(row)))\n    );\n  }\n\n  private handleRoyaltiesAction( row: Game, payload: any ) {\n    let royalties = parseFloat(parseFloat(payload.value).toFixed(2)) / 100;\n    let sub = this.service.updateRoyalties(row.code, this.entity.path, isLiveGame(row), royalties).subscribe(\n      () => {\n      },\n      () => {\n      },\n      () => {\n        payload.onCompleteFn();\n        sub.unsubscribe();\n      }\n    );\n  }\n\n  private checkAndModifyScheme() {\n    this.schemaItems = this.schema.filter(item => item.isList);\n    let exclude = [];\n\n    if (!this.allowFullManagement) {\n      exclude.push('royalties');\n    }\n\n    if (!this.entity?.jurisdiction || !this.entity?.jurisdiction[0] || this.entity?.jurisdiction[0].code !== 'IT') {\n      exclude.push('aamsCode');\n      exclude.push('mustWinJackpotBundled');\n    }\n\n    this.schemaItems = [...this.schemaItems.filter(item => exclude.indexOf(item.field) === -1)];\n  }\n\n  private showForceRemoveConfirmation( code: string ) {\n    this.dialog.open(GameForceRemoveDialogComponent, {\n      width: '600px',\n      data: <GameForceRemoveDialogData>{\n        entity: this.entity,\n        games: this.entityGames.filter(game => code === game.code),\n      },\n      disableClose: true\n    }).afterClosed().pipe(\n      filter(data => !!data),\n      take(1),\n    ).subscribe(( { slot, live } ) => {\n      if (slot && slot.length) {\n        this.removeGames(slot, true);\n      }\n\n      if (live && live.length) {\n        this.removeGames(live, true, true);\n      }\n    });\n  }\n\n  private initFieldsFilterData( items: any[], fieldName: string ): SwuiGridField[] {\n    this.filterSchema.find(schemaFields => schemaFields.field === fieldName).data =\n      items?.map(( item ) => ({ id: item.id, text: item.title }));\n    return this.filterSchema;\n  }\n}\n", "<lib-swui-schema-top-filter [schema]=\"filterSchema\"></lib-swui-schema-top-filter>\n<hints *ngIf=\"entity.status === 'test'\" [showCloseBtn]=\"false\" [fontSize]=\"16\" message=\"Entity is in test mode\"></hints>\n\n<lib-swui-grid [schema]=\"schemaItems\"\n               [rowActions]=\"actions\"\n               [ignorePlainLink]=\"true\"\n               [gridId]=\"schemaTypeName\"\n               [rowActionsColumnTitle]=\"''\"\n               [savedFilteredPageName]=\"'general-games-info'\"\n               [disableRefreshAction]=\"true\"\n               [columnsManagement]=\"true\"\n               (widgetActionEmitted)=\"onWidgetActionFn($event)\">\n  <button mat-icon-button\n          matTooltip=\"Run game settings\"\n          *ngIf=\"runAvailable\"\n          (click)=\"setRunSettings()\">\n    <mat-icon>settings</mat-icon>\n  </button>\n  <download-csv [loading]=\"loading\" (downloadCsv)=\"downloadCsv()\"></download-csv>\n  <ng-container *ngTemplateOutlet=\"projectedButtons\"></ng-container>\n</lib-swui-grid>\n\n<ng-template #projectedButtons>\n  <button\n    class=\"ml-10 mr-5\"\n    mat-flat-button\n    color=\"primary\"\n    (click)=\"showManageGamesModal()\">\n    {{ 'ENTITY_SETUP.GAMES.btnManageGames' | translate }}\n  </button>\n</ng-template>\n"], "mappings": "AAGA,SACEA,iBAAiB,EACjBC,SAAS,EAGTC,iBAAiB,EACjBC,mBAAmB,EAGnBC,wBAAwB,QACnB,yBAAyB;AAChC,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,SAASC,QAAQ,EAAEC,IAAI,EAAcC,EAAE,EAAEC,OAAO,EAAEC,UAAU,QAAQ,MAAM;AAC1E,SAASC,UAAU,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,SAAS,EAAEC,IAAI,EAAEC,SAAS,EAAEC,GAAG,QAAQ,gBAAgB;AACnG,SAASC,uBAAuB,QAAQ,+EAA+E;AAKvH,SAASC,mBAAmB,QAAQ,yDAAyD;AAC7F,SAASC,aAAa,EAAEC,WAAW,QAAQ,gDAAgD;AAG3F,SAAyBC,UAAU,QAAkB,kCAAkC;AAGvF,SAASC,8BAA8B,QAAmC,+CAA+C;AACzH,SAASC,0BAA0B,QAAQ,2CAA2C;AACtF,SAASC,0BAA0B,QAAwD,0CAA0C;AAErI,SAASC,aAAa,EAAEC,WAAW,QAAQ,gBAAgB;;;;;;;;;;;;;;;;;;;;IChC3DC,EAAA,CAAAC,SAAA,eAAwH;;;IAAzDD,EAAvB,CAAAE,UAAA,uBAAsB,gBAAgB;;;;;;IAW5EF,EAAA,CAAAG,cAAA,gBAGmC;IAA3BH,EAAA,CAAAI,UAAA,mBAAAC,oEAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAG,cAAA,EAAgB;IAAA,EAAC;IAChCX,EAAA,CAAAG,cAAA,eAAU;IAAAH,EAAA,CAAAY,MAAA,eAAQ;IACpBZ,EADoB,CAAAa,YAAA,EAAW,EACtB;;;;;IAETb,EAAA,CAAAc,kBAAA,GAAkE;;;;;;IAIlEd,EAAA,CAAAG,cAAA,gBAImC;IAAjCH,EAAA,CAAAI,UAAA,mBAAAW,yEAAA;MAAAf,EAAA,CAAAM,aAAA,CAAAU,GAAA;MAAA,MAAAR,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAS,oBAAA,EAAsB;IAAA,EAAC;IAChCjB,EAAA,CAAAY,MAAA,GACF;;IAAAZ,EAAA,CAAAa,YAAA,EAAS;;;IADPb,EAAA,CAAAkB,SAAA,EACF;IADElB,EAAA,CAAAmB,kBAAA,MAAAnB,EAAA,CAAAoB,WAAA,iDACF;;;ADgBF,OAAM,MAAOC,yBAAyB;EAGpC,IACIC,MAAMA,CAAEC,KAAa;IACvB,IAAI,CAACA,KAAK,EAAE;IACZ,IAAI,CAACC,OAAO,GAAGD,KAAK;EACtB;EAEA,IAAID,MAAMA,CAAA;IACR,OAAO,IAAI,CAACE,OAAO;EACrB;EAqBAC,YAAqBC,OAAoB,EACpBC,aAAuC,EACvCC,SAA2B,EAC3BC,MAAiB,EACjBC,gBAAkC,EAClCC,iBAAgC,EAChCC,YAAgC,EAChCC,mBAAwC,EACxCC,gBAA4C,EAC5CC,eAA0C,EAC1CC,gBAAkC,EAClCC,YAA4B;IAX5B,KAAAX,OAAO,GAAPA,OAAO;IACP,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,YAAY,GAAZA,YAAY;IA9BjC,KAAAC,MAAM,GAAoBvC,WAAW;IACrC,KAAAwC,YAAY,GAAoBzC,aAAa;IAC7C,KAAA0C,WAAW,GAAG,EAAE;IAEhB,KAAAC,OAAO,GAAY,KAAK;IACxB,KAAAC,YAAY,GAAY,IAAI;IAM5B,KAAAC,cAAc,GAAG,wBAAwB;IACzC,KAAAC,YAAY,GAAG,KAAK;IAKH,KAAAC,UAAU,GAAG,IAAIjE,OAAO,EAAQ;IAe/C,IAAI,CAAC8D,YAAY,GAAG,IAAI,CAACZ,gBAAgB,CAACY,YAAY;IACtD,IAAI,CAACH,YAAY,GAAG,IAAI,CAACO,oBAAoB,CAAC,IAAI,CAACd,YAAY,CAACe,kBAAkB,EAAE,YAAY,CAAC;IAEjG,IAAI,CAAChB,iBAAiB,CAACiB,aAAa,EAAE,CAACC,IAAI,CACzC9D,IAAI,CAAC,CAAC,CAAC,CAAC,CACP+D,SAAS,CAAGC,MAAe,IAAK;MAC/B,IAAI,CAACZ,YAAY,GAAG,IAAI,CAACO,oBAAoB,CAACK,MAAM,EAAE,UAAU,CAAC;IACnE,CAAC,CAAC;IAEJ,IAAI,CAAC,IAAI,CAACT,YAAY,EAAE;MACtB,IAAI,CAACH,YAAY,GAAG,IAAI,CAACA,YAAY,CAACxD,MAAM,CAACqE,YAAY,IAAIA,YAAY,CAACC,KAAK,KAAK,WAAW,CAAC;IAClG;EACF;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACV,YAAY,GAAG,IAAI,CAACd,gBAAgB,CAACyB,UAAU,CAAC,CAACpF,iBAAiB,CAACqF,eAAe,CAAC,CAAC,IAAI,IAAI,CAAClC,MAAM,CAACmC,IAAI,KAAK,QAAQ;IAC1HhF,QAAQ,CAAC,CACP,IAAI,CAACyD,gBAAgB,CAACwB,OAAO,CAACC,SAAS,EAAE,IAAI,CAACrC,MAAM,CAACsC,IAAI,CAAC,EAC1D,IAAI,CAACzB,eAAe,CAACuB,OAAO,CAACC,SAAS,EAAE,IAAI,CAACrC,MAAM,CAACsC,IAAI,CAAC,EACzD,IAAI,CAACxB,gBAAgB,CAACyB,iBAAiB,CAAC,IAAI,CAACvC,MAAM,CAACsC,IAAI,CAAC,CAC1D,CAAC,CACCX,IAAI,CAAC9D,IAAI,CAAC,CAAC,CAAC,CAAC,CACb+D,SAAS,CAAC,CAAE,CAACY,SAAS,EAAEC,UAAU,EAAEC,UAAU,CAAC,KAAK;MACnD,IAAI,CAACF,SAAS,GAAGA,SAAS;MAC1B,IAAI,CAACC,UAAU,GAAGA,UAAU;MAC5B,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC9B,CAAC,CAAC;IAEJ,IAAI,CAAC/B,mBAAmB,CAACgC,MAAM,CAAC,SAAS,CAAC,CACvCf,SAAS,CAAC,MAAK;MACd,IAAI,CAACgB,OAAO,CAACC,UAAU,CAACC,QAAQ,EAAE;IACpC,CAAC,CAAC;IAEJ,IAAI,CAACF,OAAO,CAACC,UAAU,CAACE,WAAW,GAAG;MACpCT,IAAI,EAAE,IAAI,CAACtC,MAAM,CAACsC,IAAI;MACtBlB,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/B4B,mBAAmB,EAAE,IAAI,CAACxC,gBAAgB,CAACyB,UAAU,CAAC,CACpDpF,iBAAiB,CAACoG,wBAAwB,EAC1CpG,iBAAiB,CAACqG,iCAAiC,CACpD,CAAC;MACFC,kBAAkB,EAAE,IAAI,CAAC3C,gBAAgB,CAACyB,UAAU,CAAC,CACnDpF,iBAAiB,CAACoG,wBAAwB,EAC1CpG,iBAAiB,CAACuG,gCAAgC,CACnD,CAAC;MACFC,uBAAuB,EAAE,IAAI,CAAC7C,gBAAgB,CAACyB,UAAU,CAAC,CACxDpF,iBAAiB,CAACyG,4BAA4B,EAC9CzG,iBAAiB,CAAC0G,qCAAqC,CACxD,CAAC;MACFC,sBAAsB,EAAE,IAAI,CAAChD,gBAAgB,CAACyB,UAAU,CAAC,CACvDpF,iBAAiB,CAACyG,4BAA4B,EAC9CzG,iBAAiB,CAAC4G,oCAAoC,CACvD,CAAC;MACFC,WAAW,EAAE,IAAI,CAAClD,gBAAgB,CAACyB,UAAU,CAAC,CAC5CpF,iBAAiB,CAACoG,wBAAwB,CAC3C,CAAC;MACFU,eAAe,EAAE,IAAI,CAACnD,gBAAgB,CAACyB,UAAU,CAAC,CAChDpF,iBAAiB,CAACyG,4BAA4B,CAC/C;KACF;IAED,IAAI,CAACM,UAAU,EAAE;IACjB,IAAI,CAACC,WAAW,EAAE;IAClB,IAAI,CAACC,oBAAoB,EAAE;EAC7B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACxC,UAAU,CAACyC,IAAI,EAAE;IACtB,IAAI,CAACzC,UAAU,CAAC0C,QAAQ,EAAE;EAC5B;EAEAJ,WAAWA,CAAA;IACT,IAAI,CAACzD,OAAO,CAAC8D,SAAS,CACnBvC,IAAI,CAAC7D,SAAS,CAAC,IAAI,CAACyD,UAAU,CAAC,CAAC,CAACK,SAAS,CAAGuC,KAAa,IAAK;MAChE,IAAI,CAACC,WAAW,GAAGD,KAAK;IAC1B,CAAC,CAAC;IAEF,IAAI,CAAC/D,OAAO,CAACiE,kBAAkB,CAC5B1C,IAAI,CAAC7D,SAAS,CAAC,IAAI,CAACyD,UAAU,CAAC,CAAC,CAChCK,SAAS,CAAC0C,IAAI,IAAI,IAAI,CAACC,2BAA2B,CAACD,IAAI,CAAC,CAAC;EAC9D;EAEAE,kBAAkBA,CAAEC,GAAQ;IAC1B,QAAQA,GAAG;MACT,KAAK,MAAM;QACT,OAAO,IAAI;MACb,KAAK,OAAO;QACV,OAAO,KAAK;MACd;QACE,OAAOA,GAAG;IACd;EACF;EAEA9E,oBAAoBA,CAAA;IAClB,IAAI,CAACS,OAAO,CAACsE,WAAW,CAAC,IAAI,CAAC1E,MAAM,CAACsC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAACX,IAAI,CACzD/D,SAAS,CAAGuG,KAAa,IAAM,IAAI,CAAC5D,MAAM,CAACoE,IAAI,CAACpG,0BAA0B,EAAE;MACxEqG,KAAK,EAAE,QAAQ;MACfC,SAAS,EAAE,MAAM;MACjBC,IAAI,EAAyB;QAC3B9E,MAAM,EAAE,IAAI,CAACA,MAAM;QACnBoE,WAAW,EAAED,KAAK;QAClBY,mBAAmB,EAAE,IAAI,CAACA;OAC3B;MACDC,YAAY,EAAE;KACf,CAAC,CAACC,WAAW,EAAE,CACjB,CACF,CAACtD,IAAI,CACJlE,MAAM,CAAGyH,MAAM,IAAM,OAAOA,MAAM,KAAK,WAAW,CAAC,EACnDnH,GAAG,CAAGmH,MAA+B,IAAM,IAAI,CAACC,gBAAgB,CAACD,MAAM,CAAC,CAAC,EACzEtH,SAAS,CAAC,MAAM,IAAI,CAACwC,OAAO,CAACsE,WAAW,CAAC,IAAI,CAAC1E,MAAM,CAACsC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,EACvE3E,GAAG,CAAGwG,KAAa,IAAK;MACtB,MAAMT,WAAW,GAAG,IAAI,CAAClD,gBAAgB,CAACyB,UAAU,CAAC,CACnDpF,iBAAiB,CAACoG,wBAAwB,CAC3C,CAAC;MACF,MAAMD,mBAAmB,GAAG,IAAI,CAACxC,gBAAgB,CAACyB,UAAU,CAAC,CAC3DpF,iBAAiB,CAACoG,wBAAwB,EAC1CpG,iBAAiB,CAACqG,iCAAiC,CACpD,CAAC;MACF,MAAMC,kBAAkB,GAAG,IAAI,CAAC3C,gBAAgB,CAACyB,UAAU,CAAC,CAC1DpF,iBAAiB,CAACoG,wBAAwB,EAC1CpG,iBAAiB,CAACuG,gCAAgC,CACnD,CAAC;MACF,MAAMO,eAAe,GAAG,IAAI,CAACnD,gBAAgB,CAACyB,UAAU,CAAC,CACvDpF,iBAAiB,CAACyG,4BAA4B,CAC/C,CAAC;MACF,MAAMD,uBAAuB,GAAG,IAAI,CAAC7C,gBAAgB,CAACyB,UAAU,CAAC,CAC/DpF,iBAAiB,CAACyG,4BAA4B,EAC9CzG,iBAAiB,CAAC0G,qCAAqC,CACxD,CAAC;MACF,MAAMC,sBAAsB,GAAG,IAAI,CAAChD,gBAAgB,CAACyB,UAAU,CAAC,CAC9DpF,iBAAiB,CAACyG,4BAA4B,EAC9CzG,iBAAiB,CAAC4G,oCAAoC,CACvD,CAAC;MAEF,OAAOU,KAAK,CAACxG,GAAG,CAACyH,IAAI,IAAG;QACtBA,IAAI,CAACC,KAAK,GAAG;UACX3B,WAAW;UACXC,eAAe;UACfvC,YAAY,EAAE,IAAI,CAACA,YAAY;UAC/B4B,mBAAmB,EAAEA,mBAAmB;UACxCG,kBAAkB,EAAEA,kBAAkB;UACtCE,uBAAuB;UACvBG;SACD;QACD,OAAO4B,IAAI;MACb,CAAC,CAAC;IACJ,CAAC,CAAC,EACFtH,SAAS,CAAC,IAAI,CAACyD,UAAU,CAAC,EAC1B7D,QAAQ,CAAC,MAAK;MACZ,IAAI,CAACkF,OAAO,CAACC,UAAU,CAACC,QAAQ,EAAE;MAClC,IAAI,CAACnC,mBAAmB,CAAC2E,OAAO,CAAC,SAAS,CAAC;IAC7C,CAAC,CAAC,CACH,CAAC1D,SAAS,CAAGuC,KAAa,IAAM,IAAI,CAACC,WAAW,GAAGD,KAAK,CAAC;EAC5D;EAEAoB,gBAAgBA,CAAE;IAAExD,KAAK;IAAEyD,GAAG;IAAEC;EAAO,CAAE;IACvC,QAAQ1D,KAAK;MACX,KAAK,QAAQ;QACX,IAAI,CAAC2D,wBAAwB,CAACF,GAAG,EAAEC,OAAO,CAAC;QAC3C;MAEF,KAAK,WAAW;QACd,IAAI,CAACE,qBAAqB,CAACH,GAAG,EAAEC,OAAO,CAAC;QACxC;MAEF;QACE;IACJ;EACF;EAEAN,gBAAgBA,CAAE;IAAES,UAAU;IAAEC;EAAY,CAA2B;IACrE,IAAID,UAAU,IAAIA,UAAU,CAACE,MAAM,EAAE;MACnC,IAAI,CAACC,qBAAqB,CAACH,UAAU,CAACE,MAAM,CAAC;IAC/C;IACA,IAAID,YAAY,IAAIA,YAAY,CAACC,MAAM,EAAE;MACvC,IAAI,CAACC,qBAAqB,CAACF,YAAY,CAACC,MAAM,EAAE,IAAI,CAAC;IACvD;EACF;EAEAE,WAAWA,CAACC,QAAgB,EAAEC,KAAA,GAAiB,KAAK,EAAEC,MAAM,GAAG,KAAK;IAClE,MAAMC,IAAI,GAAGD,MAAM,GACf,IAAI,CAAC/F,OAAO,CAACiG,oBAAoB,CAACJ,QAAQ,EAAE,IAAI,CAACjG,MAAM,CAACsC,IAAI,EAAE4D,KAAK,CAAC,GACpE,IAAI,CAAC9F,OAAO,CAACkG,gBAAgB,CAACL,QAAQ,EAAE,IAAI,CAACjG,MAAM,CAACsC,IAAI,EAAE4D,KAAK,CAAC;IAEpEE,IAAI,CACDzE,IAAI,CACH7D,SAAS,CAAC,IAAI,CAACyD,UAAU,CAAC,EAC1BxD,GAAG,CAAC,MAAM,IAAI,CAACgI,qBAAqB,CAAC,CAAC,EAAE,IAAI,EAAEG,KAAK,CAAC,CAAC,CACtD,CAACtE,SAAS,CAAC,MAAK;MACf,IAAI,CAACgB,OAAO,CAACC,UAAU,CAACC,QAAQ,EAAE;MAClC,IAAI,CAACnC,mBAAmB,CAAC2E,OAAO,CAAC,SAAS,CAAC;IAC7C,CAAC,CACF;EACH;EAEAiB,WAAWA,CAAA;IACT,IAAI,CAACpF,OAAO,GAAG,IAAI;IACnB,MAAMqF,QAAQ,GAAG,GAAG,IAAI,CAACxG,MAAM,CAACyG,IAAI,sBAAsBvJ,MAAM,EAAE,CAACwJ,MAAM,CAAC,kBAAkB,CAAC,EAAE;IAC/F,IAAI,CAACtG,OAAO,CAACmG,WAAW,CAAC,IAAI,CAACvG,MAAM,CAACsC,IAAI,EAAEkE,QAAQ,CAAC,CAAC7E,IAAI,CACvDnE,UAAU,CAAGmJ,GAAG,IAAK;MACnB,IAAI,CAACxF,OAAO,GAAG,KAAK;MACpB,OAAO5D,UAAU,CAACoJ,GAAG,CAAC;IACxB,CAAC,CAAC,EACF9I,IAAI,CAAC,CAAC,CAAC,CACR,CAAC+D,SAAS,CAAC,MAAK;MACf,IAAI,CAACT,OAAO,GAAG,KAAK;IACtB,CAAC,CAAC;EACJ;EAEA9B,cAAcA,CAAA;IACZjC,IAAI,CAAC,IAAI,CAAC2D,YAAY,CAAC6F,cAAc,CAAC,IAAI,CAAC5G,MAAM,CAACsC,IAAI,CAAC,CAAC,CACrDX,IAAI,CACH/D,SAAS,CAACkH,IAAI,IAAI,IAAI,CAACvE,MAAM,CAACoE,IAAI,CAACrG,0BAA0B,EAAE;MAC7DwG,IAAI,EAAE;QACJtC,SAAS,EAAE,IAAI,CAACA,SAAS;QACzBC,UAAU,EAAE,IAAI,CAACA,UAAU;QAC3BC,UAAU,EAAE,IAAI,CAACA,UAAU;QAC3BmE,QAAQ,EAAE,IAAI,CAAC7G,MAAM,CAAC8G,eAAe;QACrCC,QAAQ,EAAE,IAAI,CAAC/G,MAAM,CAACgH,eAAe;QACrCC,KAAK,EAAEnC;;KAEV,CAAC,CACCG,WAAW,EAAE,CAAC,EACjBxH,MAAM,CAACqH,IAAI,IAAI,CAAC,CAACA,IAAI,CAAC,EACtBjH,IAAI,CAAC,CAAC,CAAC,CACR,CACA+D,SAAS,CAACkD,IAAI,IAAG;MAChB,IAAI,CAAC/D,YAAY,CAACmG,cAAc,CAAC,IAAI,CAAClH,MAAM,CAACsC,IAAI,EAAEwC,IAAI,CAAC;IAC1D,CAAC,CAAC;EACN;EAEQiB,qBAAqBA,CAAEoB,MAAc,EAAEC,OAAA,GAAmB,KAAK,EAAEC,CAAA,GAAa,KAAK;IACzF,IAAIC,MAAM,GAAGH,MAAM,KAAK,CAAC;IACzB,IAAII,OAAO;IACX,IAAIH,OAAO,EAAE;MACX,IAAIE,MAAM,EAAE;QACVC,OAAO,GAAG,kDAAkD;MAC9D,CAAC,MAAM;QACLA,OAAO,GAAG,qDAAqD;MACjE;IACF,CAAC,MAAM;MACL,IAAID,MAAM,EAAE;QACVC,OAAO,GAAG,gDAAgD;MAC5D,CAAC,MAAM;QACLA,OAAO,GAAG,mDAAmD;MAC/D;IACF;IACA,IAAI,CAACjH,SAAS,CAACkH,GAAG,CAACD,OAAO,EAAE;MAAEJ,MAAM,EAAEA;IAAM,CAAE,CAAC,CAC5CvF,SAAS,CAAC6F,GAAG,IAAI,IAAI,CAACpH,aAAa,CAACqH,OAAO,CAACD,GAAG,EAAE,EAAE,CAAC,CAAC;EAC1D;EAEQE,YAAYA,CAAA;IAClB,IAAI,CAAC/E,OAAO,CAACgF,QAAQ,CAAC5D,IAAI,CAAC,IAAI,CAAC;IAChC,IAAI,CAACpB,OAAO,CAACC,UAAU,CAACC,QAAQ,EAAE;EACpC;EAEQc,UAAUA,CAAA;IAChB,IAAI,CAACiE,OAAO,GAAG,EAAE;IAEjB,IAAI,IAAI,CAACvG,YAAY,EAAE;MACrB,IAAI,CAACuG,OAAO,CAACC,IAAI,CAAC,IAAIhL,SAAS,CAAC;QAC9BiL,IAAI,EAAE,YAAY;QAClBC,KAAK,EAAE,eAAe;QACtBC,MAAM,EAAE,KAAK;QACbC,EAAE,EAAI1C,GAAG,IAAK;UACZ,IAAI,CAAC2C,OAAO,CAAC3C,GAAG,CAAC;QACnB,CAAC;QACD4C,aAAa,EAAEA,CAAE;UAAEC;QAAM,CAAE,KAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,CAACC,QAAQ,CAACD,MAAM;OACpE,CAAC,CAAC;IACL;IAEA,IAAI,CAACR,OAAO,CAACC,IAAI,CAAC,IAAIhL,SAAS,CAAC;MAC9BiL,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,QAAQ;MACfC,MAAM,EAAE,KAAK;MACbC,EAAE,EAAI1C,GAAG,IAAM,IAAI,CAACQ,WAAW,CAACR,GAAG,CAAClB,IAAI,EAAE,KAAK,EAAElG,UAAU,CAACoH,GAAG,CAAC,CAAC;MACjE4C,aAAa,EAAI5C,GAAS,IAAM,CAACpH,UAAU,CAACoH,GAAG,CAAC,GAC5C,IAAI,CAAChF,gBAAgB,CAACyB,UAAU,CAAC,CAACpF,iBAAiB,CAACoG,wBAAwB,CAAC,CAAC,GAC9E,IAAI,CAACzC,gBAAgB,CAACyB,UAAU,CAAC,CAACpF,iBAAiB,CAAC0L,eAAe,EAAE1L,iBAAiB,CAAC2L,sBAAsB,CAAC;KACnH,CAAC,CAAC;EACL;EAEQL,OAAOA,CAAE/C,IAAU;IACzBhI,IAAI,CAAC,IAAI,CAAC2D,YAAY,CAAC6F,cAAc,CAAC,IAAI,CAAC5G,MAAM,CAACsC,IAAI,CAAC,CAAC,CACrDX,IAAI,CACH/D,SAAS,CAACkH,IAAI,IAAG;MACf,IAAIA,IAAI,CAAC2D,SAAS,EAAE;QAClB,OAAOpL,EAAE,CAACyH,IAAI,CAAC;MACjB;MAEA,OAAO,IAAI,CAACvE,MAAM,CAACoE,IAAI,CAACrG,0BAA0B,EAAE;QAClDwG,IAAI,EAAE;UACJtC,SAAS,EAAE,IAAI,CAACA,SAAS;UACzBC,UAAU,EAAE,IAAI,CAACA,UAAU;UAC3BC,UAAU,EAAE,IAAI,CAACA,UAAU;UAC3BmE,QAAQ,EAAE,IAAI,CAAC7G,MAAM,CAAC8G,eAAe;UACrCC,QAAQ,EAAE,IAAI,CAAC/G,MAAM,CAACgH,eAAe;UACrCC,KAAK,EAAEnC;;OAEV,CAAC,CACCG,WAAW,EAAE;IAClB,CAAC,CAAC,EACFpH,IAAI,CAAC,CAAC,CAAC,EACPJ,MAAM,CAACqH,IAAI,IAAI,CAAC,CAACA,IAAI,CAAC,EACtBlH,SAAS,CAACkH,IAAI,IAAG;MACf,IAAI,CAAC/D,YAAY,CAACmG,cAAc,CAAC,IAAI,CAAClH,MAAM,CAACsC,IAAI,EAAEwC,IAAI,CAAC;MAExD,MAAM7E,KAAK,GAAGyI,MAAM,CAACC,OAAO,CAAC7D,IAAI,CAAC,CAAC8D,MAAM,CAAC,CAAEC,GAAQ,EAAE,CAACC,GAAG,EAAErE,GAAG,CAAC,KAAK;QACnE,IAAIA,GAAG,IAAIqE,GAAG,KAAK,WAAW,EAAE;UAC9BD,GAAG,CAACC,GAAG,CAAC,GAAGrE,GAAG;QAChB;QAEA,OAAOoE,GAAG;MACZ,CAAC,EAAE,EAAE,CAAC;MAEN,MAAME,QAAQ,GAAG;QACf,GAAG9I,KAAK;QACRqC,IAAI,EAAE,IAAI,CAACtC,MAAM,CAACsC,IAAI;QACtB2D,QAAQ,EAAEb,IAAI,CAACd;OAChB;MAED,OAAO,IAAI,CAAClE,OAAO,CAAC4I,SAAS,CAACD,QAAQ,CAAC;IACzC,CAAC,CAAC,CACH,CACAnH,SAAS,CAACqH,GAAG,IAAG;MACfC,MAAM,CAACvE,IAAI,CAACsE,GAAG,EAAE,QAAQ,CAAC;IAC5B,CAAC,CAAC;EACN;EAEQvD,wBAAwBA,CAAEF,GAAS,EAAEC,OAAY;IACvD,IAAI0D,UAAU;IACd,IAAI1D,OAAO,CAAC4C,MAAM,KAAKnK,aAAa,CAACkL,MAAM,EAAE;MAC3CD,UAAU,GAAG,IAAI,CAAC/I,OAAO,CAACiJ,SAAS,CAAC7D,GAAG,EAAEC,OAAO,CAAC4C,MAAM,EAAE,IAAI,CAACnI,OAAO,EAAEoC,IAAI,CAAC;IAC9E,CAAC,MAAM,IAAImD,OAAO,CAAC4C,MAAM,KAAKnK,aAAa,CAACoL,IAAI,EAAE;MAChDH,UAAU,GAAG,IAAI,CAAC/I,OAAO,CAACiJ,SAAS,CAAC7D,GAAG,EAAEC,OAAO,CAAC4C,MAAM,EAAE,IAAI,CAACnI,OAAO,EAAEoC,IAAI,CAAC;IAC9E,CAAC,MAAM,IAAImD,OAAO,CAAC4C,MAAM,KAAKnK,aAAa,CAACqL,SAAS,EAAE;MACrDJ,UAAU,GAAG,IAAI,CAAC/I,OAAO,CAACoJ,aAAa,CAAC,IAAI,CAACtJ,OAAO,EAAEoC,IAAI,EAAEkD,GAAG,CAAClB,IAAI,EAAE,IAAI,EAAElG,UAAU,CAACoH,GAAG,CAAC,CAAC;IAC9F,CAAC,MAAM,IAAIC,OAAO,CAAC4C,MAAM,KAAKnK,aAAa,CAACuL,MAAM,EAAE;MAClDN,UAAU,GAAG,IAAI,CAAC/I,OAAO,CAACsJ,eAAe,CAAC,IAAI,CAACxJ,OAAO,EAAEoC,IAAI,EAAEkD,GAAG,CAAClB,IAAI,EAAElG,UAAU,CAACoH,GAAG,CAAC,CAAC;IAC1F,CAAC,MAAM,IAAIC,OAAO,CAAC4C,MAAM,KAAKnK,aAAa,CAACyL,YAAY,EAAE;MACxDR,UAAU,GAAG,IAAI,CAACS,uBAAuB,CAACpE,GAAG,CAAC;IAChD;IAEA2D,UAAU,CAACxH,IAAI,CACbhE,GAAG,CAAGkM,QAAc,IAAMnB,MAAM,CAACoB,MAAM,CAACtE,GAAG,EAAEqE,QAAQ,CAAC,CAAC,EACvD9L,GAAG,CAAC,MAAM,IAAI,CAACsC,aAAa,CAACqH,OAAO,CAAC,IAAI,CAACpH,SAAS,CAACyJ,OAAO,CAAC,+CAA+C,CAAC,CAAC,CAAC,EAC9GjM,SAAS,CAAC,IAAI,CAACyD,UAAU,CAAC,EAC1B7D,QAAQ,CAAC,MAAK;MACZ,IAAI,CAACiK,YAAY,EAAE;MACnB,IAAI,CAAChH,mBAAmB,CAAC2E,OAAO,CAAC,SAAS,CAAC;MAC3CG,OAAO,CAACuE,YAAY,EAAE;IACxB,CAAC,CAAC,CACH,CAACpI,SAAS,EAAE;EACf;EAEQgI,uBAAuBA,CAAEpE,GAAS;IACxC,OAAO,IAAI,CAACjF,MAAM,CAACoE,IAAI,CAAC3G,uBAAuB,EAAE;MAC/C4G,KAAK,EAAE,OAAO;MACdE,IAAI,EAAE;QAAEyC,OAAO,EAAE;MAAyC,CAAE;MAC5DvC,YAAY,EAAE;KACf,CAAC,CAACC,WAAW,EAAE,CAACtD,IAAI,CACnBlE,MAAM,CAACgH,GAAG,IAAI,CAAC,CAACA,GAAG,CAAC,EACpB7G,SAAS,CAAC,MAAM,IAAI,CAACwC,OAAO,CAACoJ,aAAa,CAAC,IAAI,CAACtJ,OAAO,EAAEoC,IAAI,EAAEkD,GAAG,CAAClB,IAAI,EAAE,KAAK,EAAElG,UAAU,CAACoH,GAAG,CAAC,CAAC,CAAC,CAClG;EACH;EAEQG,qBAAqBA,CAAEH,GAAS,EAAEC,OAAY;IACpD,IAAIwE,SAAS,GAAGC,UAAU,CAACA,UAAU,CAACzE,OAAO,CAACxF,KAAK,CAAC,CAACkK,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG;IACtE,IAAIC,GAAG,GAAG,IAAI,CAAChK,OAAO,CAACiK,eAAe,CAAC7E,GAAG,CAAClB,IAAI,EAAE,IAAI,CAACtE,MAAM,CAACsC,IAAI,EAAElE,UAAU,CAACoH,GAAG,CAAC,EAAEyE,SAAS,CAAC,CAACrI,SAAS,CACtG,MAAK,CACL,CAAC,EACD,MAAK,CACL,CAAC,EACD,MAAK;MACH6D,OAAO,CAACuE,YAAY,EAAE;MACtBI,GAAG,CAACE,WAAW,EAAE;IACnB,CAAC,CACF;EACH;EAEQxG,oBAAoBA,CAAA;IAC1B,IAAI,CAAC5C,WAAW,GAAG,IAAI,CAACF,MAAM,CAACvD,MAAM,CAAC8M,IAAI,IAAIA,IAAI,CAACC,MAAM,CAAC;IAC1D,IAAIC,OAAO,GAAG,EAAE;IAEhB,IAAI,CAAC,IAAI,CAAC1F,mBAAmB,EAAE;MAC7B0F,OAAO,CAAC3C,IAAI,CAAC,WAAW,CAAC;IAC3B;IAEA,IAAI,CAAC,IAAI,CAAC9H,MAAM,EAAE0K,YAAY,IAAI,CAAC,IAAI,CAAC1K,MAAM,EAAE0K,YAAY,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC1K,MAAM,EAAE0K,YAAY,CAAC,CAAC,CAAC,CAACpG,IAAI,KAAK,IAAI,EAAE;MAC7GmG,OAAO,CAAC3C,IAAI,CAAC,UAAU,CAAC;MACxB2C,OAAO,CAAC3C,IAAI,CAAC,uBAAuB,CAAC;IACvC;IAEA,IAAI,CAAC5G,WAAW,GAAG,CAAC,GAAG,IAAI,CAACA,WAAW,CAACzD,MAAM,CAAC8M,IAAI,IAAIE,OAAO,CAACE,OAAO,CAACJ,IAAI,CAACxI,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;EAC7F;EAEQwC,2BAA2BA,CAAED,IAAY;IAC/C,IAAI,CAAC/D,MAAM,CAACoE,IAAI,CAACtG,8BAA8B,EAAE;MAC/CuG,KAAK,EAAE,OAAO;MACdE,IAAI,EAA6B;QAC/B9E,MAAM,EAAE,IAAI,CAACA,MAAM;QACnBmE,KAAK,EAAE,IAAI,CAACC,WAAW,CAAC3G,MAAM,CAAC2H,IAAI,IAAId,IAAI,KAAKc,IAAI,CAACd,IAAI;OAC1D;MACDU,YAAY,EAAE;KACf,CAAC,CAACC,WAAW,EAAE,CAACtD,IAAI,CACnBlE,MAAM,CAACqH,IAAI,IAAI,CAAC,CAACA,IAAI,CAAC,EACtBjH,IAAI,CAAC,CAAC,CAAC,CACR,CAAC+D,SAAS,CAAC,CAAE;MAAEgJ,IAAI;MAAEC;IAAI,CAAE,KAAK;MAC/B,IAAID,IAAI,IAAIA,IAAI,CAAC9E,MAAM,EAAE;QACvB,IAAI,CAACE,WAAW,CAAC4E,IAAI,EAAE,IAAI,CAAC;MAC9B;MAEA,IAAIC,IAAI,IAAIA,IAAI,CAAC/E,MAAM,EAAE;QACvB,IAAI,CAACE,WAAW,CAAC6E,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;MACpC;IACF,CAAC,CAAC;EACJ;EAEQrJ,oBAAoBA,CAAEsJ,KAAY,EAAEC,SAAiB;IAC3D,IAAI,CAAC9J,YAAY,CAAC+J,IAAI,CAAClJ,YAAY,IAAIA,YAAY,CAACC,KAAK,KAAKgJ,SAAS,CAAC,CAACjG,IAAI,GAC3EgG,KAAK,EAAEnN,GAAG,CAAG4M,IAAI,KAAO;MAAEU,EAAE,EAAEV,IAAI,CAACU,EAAE;MAAEC,IAAI,EAAEX,IAAI,CAACvC;IAAK,CAAE,CAAC,CAAC;IAC7D,OAAO,IAAI,CAAC/G,YAAY;EAC1B;;;uCAldWlB,yBAAyB,EAAArB,EAAA,CAAAyM,iBAAA,CAAAC,EAAA,CAAAjN,WAAA,GAAAO,EAAA,CAAAyM,iBAAA,CAAAE,EAAA,CAAAC,wBAAA,GAAA5M,EAAA,CAAAyM,iBAAA,CAAAI,EAAA,CAAAC,gBAAA,GAAA9M,EAAA,CAAAyM,iBAAA,CAAAM,EAAA,CAAAC,SAAA,GAAAhN,EAAA,CAAAyM,iBAAA,CAAAE,EAAA,CAAAM,gBAAA,GAAAjN,EAAA,CAAAyM,iBAAA,CAAAS,EAAA,CAAAC,aAAA,GAAAnN,EAAA,CAAAyM,iBAAA,CAAAW,EAAA,CAAAC,kBAAA,GAAArN,EAAA,CAAAyM,iBAAA,CAAAa,EAAA,CAAAC,mBAAA,GAAAvN,EAAA,CAAAyM,iBAAA,CAAAe,EAAA,CAAAC,gBAAA,GAAAzN,EAAA,CAAAyM,iBAAA,CAAAiB,EAAA,CAAAC,eAAA,GAAA3N,EAAA,CAAAyM,iBAAA,CAAAmB,GAAA,CAAAC,gBAAA,GAAA7N,EAAA,CAAAyM,iBAAA,CAAAE,EAAA,CAAAmB,cAAA;IAAA;EAAA;;;YAAzBzM,yBAAyB;MAAA0M,SAAA;MAAAC,SAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;yBA2BzB7P,iBAAiB;;;;;;;;;;;uCAjCjB,CACTkB,mBAAmB,EACnBhB,wBAAwB,EACxB;QAAE6P,OAAO,EAAE9P,mBAAmB;QAAE+P,WAAW,EAAE5O;MAAW,CAAE,CAC3D;MAAA6O,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mCAAAR,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UC3CHlO,EAAA,CAAAC,SAAA,oCAAiF;UACjFD,EAAA,CAAA2O,UAAA,IAAAC,0CAAA,mBAAgH;UAEhH5O,EAAA,CAAAG,cAAA,uBAQgE;UAAjDH,EAAA,CAAAI,UAAA,iCAAAyO,gFAAAC,MAAA;YAAA9O,EAAA,CAAAM,aAAA,CAAAyO,GAAA;YAAA,OAAA/O,EAAA,CAAAU,WAAA,CAAuByN,GAAA,CAAAtH,gBAAA,CAAAiI,MAAA,CAAwB;UAAA,EAAC;UAC7D9O,EAAA,CAAA2O,UAAA,IAAAK,2CAAA,oBAGmC;UAGnChP,EAAA,CAAAG,cAAA,sBAAgE;UAA9BH,EAAA,CAAAI,UAAA,yBAAA6O,uEAAA;YAAAjP,EAAA,CAAAM,aAAA,CAAAyO,GAAA;YAAA,OAAA/O,EAAA,CAAAU,WAAA,CAAeyN,GAAA,CAAAtG,WAAA,EAAa;UAAA,EAAC;UAAC7H,EAAA,CAAAa,YAAA,EAAe;UAC/Eb,EAAA,CAAA2O,UAAA,IAAAO,iDAAA,0BAAmD;UACrDlP,EAAA,CAAAa,YAAA,EAAgB;UAEhBb,EAAA,CAAA2O,UAAA,IAAAQ,gDAAA,gCAAAnP,EAAA,CAAAoP,sBAAA,CAA+B;;;;UAtBHpP,EAAA,CAAAE,UAAA,WAAAiO,GAAA,CAAA5L,YAAA,CAAuB;UAC3CvC,EAAA,CAAAkB,SAAA,EAA8B;UAA9BlB,EAAA,CAAAE,UAAA,SAAAiO,GAAA,CAAA7M,MAAA,CAAAqI,MAAA,YAA8B;UAEvB3J,EAAA,CAAAkB,SAAA,EAAsB;UAOtBlB,EAPA,CAAAE,UAAA,WAAAiO,GAAA,CAAA3L,WAAA,CAAsB,eAAA2L,GAAA,CAAAhF,OAAA,CACA,yBACE,WAAAgF,GAAA,CAAAxL,cAAA,CACC,6BACG,+CACkB,8BACjB,2BACH;UAI9B3C,EAAA,CAAAkB,SAAA,EAAkB;UAAlBlB,EAAA,CAAAE,UAAA,SAAAiO,GAAA,CAAAvL,YAAA,CAAkB;UAIb5C,EAAA,CAAAkB,SAAA,EAAmB;UAAnBlB,EAAA,CAAAE,UAAA,YAAAiO,GAAA,CAAA1L,OAAA,CAAmB;UAClBzC,EAAA,CAAAkB,SAAA,EAAkC;UAAlClB,EAAA,CAAAE,UAAA,qBAAAmP,mBAAA,CAAkC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}