{"ast": null, "code": "import moment from 'moment';\nimport { BehaviorSubject } from 'rxjs';\nimport transformHistoryResponse from './sw_live_roulette_transform/transform';\nexport var GAME_RENDERER;\n(function (GAME_RENDERER) {\n  GAME_RENDERER[GAME_RENDERER[\"plain\"] = 0] = \"plain\";\n})(GAME_RENDERER || (GAME_RENDERER = {}));\nexport class SWGamehistory {\n  static {\n    this.defaultConfig = {\n      config: {\n        language: 'en',\n        dpi: 'ldpi',\n        mobile: 'false'\n      }\n    };\n  }\n  static getInstance(ghAppPluginUrl, options) {\n    if (!SWGamehistory.instance) {\n      SWGamehistory.instance = new SWGamehistory(ghAppPluginUrl, options);\n    }\n    return SWGamehistory.instance;\n  }\n  constructor(url, options) {\n    this.isReady = false;\n    this.isLoaded = new BehaviorSubject(false);\n    this.frameMapArray = {};\n    Object.keys(SWGamehistory.defaultConfig).forEach(key => {\n      this[key] = SWGamehistory.defaultConfig[key];\n    });\n    if (options) {\n      Object.keys(options).forEach(key => {\n        this[key] = options[key];\n      });\n    }\n    this.ghAppPluginUrl = url;\n    this.windowCanvasMessageEventListener = this._windowCanvasMessageEventListener.bind(this);\n  }\n  _windowCanvasMessageEventListener(e) {\n    setTimeout(() => {\n      if (e.data.height !== undefined) {\n        if (e.data.height && this.iframe.height !== e.data.height + 'px') {\n          this.iframe.height = e.data.height + 'px';\n        }\n      }\n    }, 1000);\n    if (e.data.ready) {\n      if (this.isReady) {\n        return;\n      }\n      this.isReady = true;\n      const data = this.info;\n      const response = this.transformHistoryToInitResponse(data);\n      const msg = {\n        history: {\n          response: response,\n          showPreview: true,\n          showRewards: true\n        }\n      };\n      const targetOrigin = this.iframe.src;\n      this.iWindow.postMessage(msg, targetOrigin);\n    }\n  }\n  remove(selector) {\n    if (this.frameMapArray[selector]) {\n      const iframe = this.frameMapArray[selector].element;\n      if (iframe) {\n        iframe.parentNode.removeChild(iframe);\n      }\n      delete this.frameMapArray[selector];\n    }\n    window.removeEventListener('message', this.windowCanvasMessageEventListener);\n    if (SWGamehistory.instance) {\n      SWGamehistory.instance = undefined;\n    }\n    this.isReady = false;\n  }\n  commitData(iframeHolderID, data) {\n    this.info = data;\n    const gameCode = data.gameId || data.gameCode;\n    if (!gameCode) {\n      throw new Error('Empty gameCode');\n    }\n    data.iframeHolderID = iframeHolderID;\n    data.historyInfo.theme = this.config.theme;\n    this.getContainer(iframeHolderID, () => {\n      this.isLoaded.next(true);\n    });\n  }\n  getContainer(selector, onError) {\n    let iframe;\n    if (this.frameMapArray[selector]) {\n      iframe = this.frameMapArray[selector].element;\n    } else {\n      const language = this.language || this.config.language;\n      const currency = this.currency || this.config.currency;\n      iframe = document.createElement('iframe');\n      iframe.src = `${this.ghAppPluginUrl}?language=${language}` + `&mobile=${this.config.mobile}&dpi=${this.config.dpi}&t=${+new Date()}&currency=${currency}`;\n      iframe.setAttribute('class', 'gh-iframe');\n      iframe.onload = () => {\n        this.frameMapArray[selector].ready = true;\n        this.isLoaded.next(true);\n        this.iWindow = iframe.contentWindow;\n        this.iframe = iframe;\n        const url = this.info.historyInfo.url.replace('index.html', 'history.html');\n        if (this.info.historyInfo.historyRenderType === GAME_RENDERER.plain) {\n          this.isReady = true;\n          const msg = {\n            type: 'spin',\n            spin: JSON.stringify(this.info),\n            language: this.config.language\n          };\n          this.iWindow.postMessage(msg, url);\n        } else {\n          window.addEventListener('message', this.windowCanvasMessageEventListener);\n          this.iWindow.postMessage({\n            init: true\n          }, url);\n        }\n        return;\n      };\n      iframe.onerror = () => onError();\n      this.frameMapArray[selector] = {\n        element: iframe,\n        ready: false\n      };\n      const host = document.getElementById(selector);\n      host.appendChild(iframe);\n    }\n  }\n  // TODO remove, when working with Management API\n  transformHistoryToInitResponse(history) {\n    const data = {};\n    Object.keys(history.initSettings).forEach(key => {\n      data[key] = history.initSettings[key];\n    });\n    Object.keys(history.details).forEach(key => {\n      data[key] = history.details[key];\n    });\n    let fullResponse = {\n      ...history\n    };\n    if (fullResponse.insertedAt) {\n      fullResponse.insertedAt = moment(fullResponse.insertedAt).utcOffset(fullResponse.insertedAt).toJSON();\n    }\n    if (fullResponse.firstTs) {\n      fullResponse.firstTs = moment(fullResponse.firstTs).utcOffset(fullResponse.firstTs).toJSON();\n    }\n    const message = {\n      balance: {\n        currency: history.currency,\n        amount: 0,\n        real: {\n          amount: 0\n        },\n        bonus: {\n          amount: 0\n        }\n      },\n      result: data,\n      fullResponse\n    };\n    if (history.gameId.indexOf('sw_live_erol') !== -1) {\n      message['result'] = Object.assign({}, history.initSettings, transformHistoryResponse(history.details));\n    } else {\n      message['result'] = Object.assign({}, history.initSettings, history.details);\n    }\n    return message;\n  }\n}", "map": {"version": 3, "names": ["moment", "BehaviorSubject", "transformHistoryResponse", "GAME_RENDERER", "SWGamehistory", "defaultConfig", "config", "language", "dpi", "mobile", "getInstance", "ghAppPluginUrl", "options", "instance", "constructor", "url", "isReady", "isLoaded", "frameMapArray", "Object", "keys", "for<PERSON>ach", "key", "windowCanvasMessageEventListener", "_windowCanvasMessageEventListener", "bind", "e", "setTimeout", "data", "height", "undefined", "iframe", "ready", "info", "response", "transformHistoryToInitResponse", "msg", "history", "showPreview", "showRewards", "target<PERSON>rigin", "src", "iWindow", "postMessage", "remove", "selector", "element", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "window", "removeEventListener", "commitData", "iframeHolderID", "gameCode", "gameId", "Error", "historyInfo", "theme", "getContainer", "next", "onError", "currency", "document", "createElement", "Date", "setAttribute", "onload", "contentWindow", "replace", "historyRenderType", "plain", "type", "spin", "JSON", "stringify", "addEventListener", "init", "onerror", "host", "getElementById", "append<PERSON><PERSON><PERSON>", "initSettings", "details", "fullResponse", "insertedAt", "utcOffset", "toJSON", "firstTs", "message", "balance", "amount", "real", "bonus", "result", "indexOf", "assign"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/gamehistory/components/internal-game-history/spin-details/sw-history.plugin.ts"], "sourcesContent": ["import moment from 'moment';\nimport { BehaviorSubject } from 'rxjs';\n\nimport transformHistoryResponse from './sw_live_roulette_transform/transform';\n\nexport enum GAME_RENDERER {\n  plain,\n}\n\ninterface IIframeMapArray {\n  [key: string]: {\n    element: HTMLIFrameElement,\n    ready: boolean;\n  };\n}\n\ninterface ISpinItem {\n  [key: string]: any;\n}\n\ninterface ISpinItemJackpot {\n  [key: string]: any;\n}\n\ninterface ISpinDetails {\n  gameId?: string;\n  gameCode?: string;\n  details: ISpinItem | ISpinItemJackpot;\n  initionalData: { [key: string]: any };\n  historyInfo: { [key: string]: any };\n  iframeHolderID: string;\n}\n\nexport class SWGamehistory {\n  private static instance: SWGamehistory;\n  private static defaultConfig = {\n    config: {\n      language: 'en',\n      dpi: 'ldpi',\n      mobile: 'false',\n    },\n  };\n\n  public config;\n  public iWindow;\n  public iframe;\n  public info;\n  public isReady = false;\n  public isLoaded = new BehaviorSubject<boolean>(false);\n\n  private language;\n  private currency;\n  private frameMapArray: IIframeMapArray = <IIframeMapArray>{};\n\n  private readonly ghAppPluginUrl;\n  private readonly windowCanvasMessageEventListener;\n\n  static getInstance( ghAppPluginUrl: string, options? ) {\n    if (!SWGamehistory.instance) {\n      SWGamehistory.instance = new SWGamehistory(ghAppPluginUrl, options);\n    }\n    return SWGamehistory.instance;\n  }\n\n  constructor( url: string, options? ) {\n    Object.keys(SWGamehistory.defaultConfig).forEach(key => {\n      this[key] = SWGamehistory.defaultConfig[key];\n    });\n    if (options) {\n      Object.keys(options).forEach(key => {\n        this[key] = options[key];\n      });\n    }\n    this.ghAppPluginUrl = url;\n    this.windowCanvasMessageEventListener = this._windowCanvasMessageEventListener.bind(this);\n  }\n\n  public _windowCanvasMessageEventListener( e ) {\n    setTimeout(() => {\n      if (e.data.height !== undefined) {\n        if (e.data.height && this.iframe.height !== e.data.height + 'px') {\n          this.iframe.height = e.data.height + 'px';\n        }\n      }\n    }, 1000);\n\n    if (e.data.ready) {\n      if (this.isReady) {\n        return;\n      }\n      this.isReady = true;\n\n      const data = this.info;\n      const response = this.transformHistoryToInitResponse(data);\n      const msg = {\n        history: {\n          response: response,\n          showPreview: true,\n          showRewards: true\n        }\n      };\n      const targetOrigin = this.iframe.src;\n      this.iWindow.postMessage(msg, targetOrigin);\n    }\n  }\n\n  remove( selector: string ) {\n    if (this.frameMapArray[selector]) {\n      const iframe: HTMLIFrameElement = this.frameMapArray[selector].element;\n      if (iframe) {\n        iframe.parentNode.removeChild(iframe);\n      }\n      delete this.frameMapArray[selector];\n    }\n    window.removeEventListener('message', this.windowCanvasMessageEventListener);\n    if (SWGamehistory.instance) {\n      SWGamehistory.instance = undefined;\n    }\n    this.isReady = false;\n  }\n\n  commitData( iframeHolderID: string, data: ISpinDetails ) {\n    this.info = data;\n    const gameCode = data.gameId || data.gameCode;\n    if (!gameCode) {\n      throw new Error('Empty gameCode');\n    }\n\n    data.iframeHolderID = iframeHolderID;\n    data.historyInfo.theme = this.config.theme;\n\n    this.getContainer(iframeHolderID,\n      () => {\n        this.isLoaded.next(true);\n      });\n  }\n\n  private getContainer( selector: string, onError ) {\n    let iframe: HTMLIFrameElement;\n\n    if (this.frameMapArray[selector]) {\n      iframe = this.frameMapArray[selector].element;\n    } else {\n      const language = this.language || this.config.language;\n      const currency = this.currency || this.config.currency;\n\n      iframe = <HTMLIFrameElement>document.createElement('iframe');\n      iframe.src = `${this.ghAppPluginUrl}?language=${language}` +\n        `&mobile=${this.config.mobile}&dpi=${this.config.dpi}&t=${+new Date()}&currency=${currency}`;\n      iframe.setAttribute('class', 'gh-iframe');\n\n      iframe.onload = () => {\n        this.frameMapArray[selector].ready = true;\n        this.isLoaded.next(true);\n\n        this.iWindow = (<HTMLIFrameElement>iframe).contentWindow;\n        this.iframe = iframe;\n        const url = this.info.historyInfo.url.replace('index.html', 'history.html');\n\n        if (this.info.historyInfo.historyRenderType === GAME_RENDERER.plain) {\n          this.isReady = true;\n          const msg = {\n            type: 'spin',\n            spin: JSON.stringify(this.info),\n            language: this.config.language,\n          };\n          this.iWindow.postMessage(msg, url);\n        } else {\n          window.addEventListener('message', this.windowCanvasMessageEventListener);\n          this.iWindow.postMessage({ init: true }, url);\n        }\n\n        return;\n      };\n      iframe.onerror = () => onError();\n\n      this.frameMapArray[selector] = {\n        element: iframe,\n        ready: false\n      };\n\n      const host = document.getElementById(selector);\n      host.appendChild(iframe);\n    }\n  }\n\n  // TODO remove, when working with Management API\n  private transformHistoryToInitResponse( history ) {\n    const data = {};\n    Object.keys(history.initSettings).forEach(key => {\n      data[key] = history.initSettings[key];\n    });\n    Object.keys(history.details).forEach(key => {\n      data[key] = history.details[key];\n    });\n\n    let fullResponse = { ...history };\n\n    if (fullResponse.insertedAt) {\n      fullResponse.insertedAt = moment(fullResponse.insertedAt).utcOffset(fullResponse.insertedAt).toJSON();\n    }\n\n    if (fullResponse.firstTs) {\n      fullResponse.firstTs = moment(fullResponse.firstTs).utcOffset(fullResponse.firstTs).toJSON();\n    }\n\n    const message = {\n      balance: {\n        currency: history.currency,\n        amount: 0,\n        real: { amount: 0 },\n        bonus: { amount: 0 }\n      },\n      result: data,\n      fullResponse\n    };\n\n    if (history.gameId.indexOf('sw_live_erol') !== -1) {\n      message['result'] = Object.assign({}, history.initSettings, transformHistoryResponse(history.details));\n    } else {\n      message['result'] = Object.assign({}, history.initSettings, history.details);\n    }\n\n    return message;\n  }\n}\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,QAAQ;AAC3B,SAASC,eAAe,QAAQ,MAAM;AAEtC,OAAOC,wBAAwB,MAAM,wCAAwC;AAE7E,WAAYC,aAEX;AAFD,WAAYA,aAAa;EACvBA,aAAA,CAAAA,aAAA,wBAAK;AACP,CAAC,EAFWA,aAAa,KAAbA,aAAa;AA4BzB,OAAM,MAAOC,aAAa;;IAET,KAAAC,aAAa,GAAG;MAC7BC,MAAM,EAAE;QACNC,QAAQ,EAAE,IAAI;QACdC,GAAG,EAAE,MAAM;QACXC,MAAM,EAAE;;KAEX;EAAC;EAgBF,OAAOC,WAAWA,CAAEC,cAAsB,EAAEC,OAAQ;IAClD,IAAI,CAACR,aAAa,CAACS,QAAQ,EAAE;MAC3BT,aAAa,CAACS,QAAQ,GAAG,IAAIT,aAAa,CAACO,cAAc,EAAEC,OAAO,CAAC;IACrE;IACA,OAAOR,aAAa,CAACS,QAAQ;EAC/B;EAEAC,YAAaC,GAAW,EAAEH,OAAQ;IAjB3B,KAAAI,OAAO,GAAG,KAAK;IACf,KAAAC,QAAQ,GAAG,IAAIhB,eAAe,CAAU,KAAK,CAAC;IAI7C,KAAAiB,aAAa,GAAqC,EAAE;IAa1DC,MAAM,CAACC,IAAI,CAAChB,aAAa,CAACC,aAAa,CAAC,CAACgB,OAAO,CAACC,GAAG,IAAG;MACrD,IAAI,CAACA,GAAG,CAAC,GAAGlB,aAAa,CAACC,aAAa,CAACiB,GAAG,CAAC;IAC9C,CAAC,CAAC;IACF,IAAIV,OAAO,EAAE;MACXO,MAAM,CAACC,IAAI,CAACR,OAAO,CAAC,CAACS,OAAO,CAACC,GAAG,IAAG;QACjC,IAAI,CAACA,GAAG,CAAC,GAAGV,OAAO,CAACU,GAAG,CAAC;MAC1B,CAAC,CAAC;IACJ;IACA,IAAI,CAACX,cAAc,GAAGI,GAAG;IACzB,IAAI,CAACQ,gCAAgC,GAAG,IAAI,CAACC,iCAAiC,CAACC,IAAI,CAAC,IAAI,CAAC;EAC3F;EAEOD,iCAAiCA,CAAEE,CAAC;IACzCC,UAAU,CAAC,MAAK;MACd,IAAID,CAAC,CAACE,IAAI,CAACC,MAAM,KAAKC,SAAS,EAAE;QAC/B,IAAIJ,CAAC,CAACE,IAAI,CAACC,MAAM,IAAI,IAAI,CAACE,MAAM,CAACF,MAAM,KAAKH,CAAC,CAACE,IAAI,CAACC,MAAM,GAAG,IAAI,EAAE;UAChE,IAAI,CAACE,MAAM,CAACF,MAAM,GAAGH,CAAC,CAACE,IAAI,CAACC,MAAM,GAAG,IAAI;QAC3C;MACF;IACF,CAAC,EAAE,IAAI,CAAC;IAER,IAAIH,CAAC,CAACE,IAAI,CAACI,KAAK,EAAE;MAChB,IAAI,IAAI,CAAChB,OAAO,EAAE;QAChB;MACF;MACA,IAAI,CAACA,OAAO,GAAG,IAAI;MAEnB,MAAMY,IAAI,GAAG,IAAI,CAACK,IAAI;MACtB,MAAMC,QAAQ,GAAG,IAAI,CAACC,8BAA8B,CAACP,IAAI,CAAC;MAC1D,MAAMQ,GAAG,GAAG;QACVC,OAAO,EAAE;UACPH,QAAQ,EAAEA,QAAQ;UAClBI,WAAW,EAAE,IAAI;UACjBC,WAAW,EAAE;;OAEhB;MACD,MAAMC,YAAY,GAAG,IAAI,CAACT,MAAM,CAACU,GAAG;MACpC,IAAI,CAACC,OAAO,CAACC,WAAW,CAACP,GAAG,EAAEI,YAAY,CAAC;IAC7C;EACF;EAEAI,MAAMA,CAAEC,QAAgB;IACtB,IAAI,IAAI,CAAC3B,aAAa,CAAC2B,QAAQ,CAAC,EAAE;MAChC,MAAMd,MAAM,GAAsB,IAAI,CAACb,aAAa,CAAC2B,QAAQ,CAAC,CAACC,OAAO;MACtE,IAAIf,MAAM,EAAE;QACVA,MAAM,CAACgB,UAAU,CAACC,WAAW,CAACjB,MAAM,CAAC;MACvC;MACA,OAAO,IAAI,CAACb,aAAa,CAAC2B,QAAQ,CAAC;IACrC;IACAI,MAAM,CAACC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC3B,gCAAgC,CAAC;IAC5E,IAAInB,aAAa,CAACS,QAAQ,EAAE;MAC1BT,aAAa,CAACS,QAAQ,GAAGiB,SAAS;IACpC;IACA,IAAI,CAACd,OAAO,GAAG,KAAK;EACtB;EAEAmC,UAAUA,CAAEC,cAAsB,EAAExB,IAAkB;IACpD,IAAI,CAACK,IAAI,GAAGL,IAAI;IAChB,MAAMyB,QAAQ,GAAGzB,IAAI,CAAC0B,MAAM,IAAI1B,IAAI,CAACyB,QAAQ;IAC7C,IAAI,CAACA,QAAQ,EAAE;MACb,MAAM,IAAIE,KAAK,CAAC,gBAAgB,CAAC;IACnC;IAEA3B,IAAI,CAACwB,cAAc,GAAGA,cAAc;IACpCxB,IAAI,CAAC4B,WAAW,CAACC,KAAK,GAAG,IAAI,CAACnD,MAAM,CAACmD,KAAK;IAE1C,IAAI,CAACC,YAAY,CAACN,cAAc,EAC9B,MAAK;MACH,IAAI,CAACnC,QAAQ,CAAC0C,IAAI,CAAC,IAAI,CAAC;IAC1B,CAAC,CAAC;EACN;EAEQD,YAAYA,CAAEb,QAAgB,EAAEe,OAAO;IAC7C,IAAI7B,MAAyB;IAE7B,IAAI,IAAI,CAACb,aAAa,CAAC2B,QAAQ,CAAC,EAAE;MAChCd,MAAM,GAAG,IAAI,CAACb,aAAa,CAAC2B,QAAQ,CAAC,CAACC,OAAO;IAC/C,CAAC,MAAM;MACL,MAAMvC,QAAQ,GAAG,IAAI,CAACA,QAAQ,IAAI,IAAI,CAACD,MAAM,CAACC,QAAQ;MACtD,MAAMsD,QAAQ,GAAG,IAAI,CAACA,QAAQ,IAAI,IAAI,CAACvD,MAAM,CAACuD,QAAQ;MAEtD9B,MAAM,GAAsB+B,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;MAC5DhC,MAAM,CAACU,GAAG,GAAG,GAAG,IAAI,CAAC9B,cAAc,aAAaJ,QAAQ,EAAE,GACxD,WAAW,IAAI,CAACD,MAAM,CAACG,MAAM,QAAQ,IAAI,CAACH,MAAM,CAACE,GAAG,MAAM,CAAC,IAAIwD,IAAI,EAAE,aAAaH,QAAQ,EAAE;MAC9F9B,MAAM,CAACkC,YAAY,CAAC,OAAO,EAAE,WAAW,CAAC;MAEzClC,MAAM,CAACmC,MAAM,GAAG,MAAK;QACnB,IAAI,CAAChD,aAAa,CAAC2B,QAAQ,CAAC,CAACb,KAAK,GAAG,IAAI;QACzC,IAAI,CAACf,QAAQ,CAAC0C,IAAI,CAAC,IAAI,CAAC;QAExB,IAAI,CAACjB,OAAO,GAAuBX,MAAO,CAACoC,aAAa;QACxD,IAAI,CAACpC,MAAM,GAAGA,MAAM;QACpB,MAAMhB,GAAG,GAAG,IAAI,CAACkB,IAAI,CAACuB,WAAW,CAACzC,GAAG,CAACqD,OAAO,CAAC,YAAY,EAAE,cAAc,CAAC;QAE3E,IAAI,IAAI,CAACnC,IAAI,CAACuB,WAAW,CAACa,iBAAiB,KAAKlE,aAAa,CAACmE,KAAK,EAAE;UACnE,IAAI,CAACtD,OAAO,GAAG,IAAI;UACnB,MAAMoB,GAAG,GAAG;YACVmC,IAAI,EAAE,MAAM;YACZC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACzC,IAAI,CAAC;YAC/B1B,QAAQ,EAAE,IAAI,CAACD,MAAM,CAACC;WACvB;UACD,IAAI,CAACmC,OAAO,CAACC,WAAW,CAACP,GAAG,EAAErB,GAAG,CAAC;QACpC,CAAC,MAAM;UACLkC,MAAM,CAAC0B,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACpD,gCAAgC,CAAC;UACzE,IAAI,CAACmB,OAAO,CAACC,WAAW,CAAC;YAAEiC,IAAI,EAAE;UAAI,CAAE,EAAE7D,GAAG,CAAC;QAC/C;QAEA;MACF,CAAC;MACDgB,MAAM,CAAC8C,OAAO,GAAG,MAAMjB,OAAO,EAAE;MAEhC,IAAI,CAAC1C,aAAa,CAAC2B,QAAQ,CAAC,GAAG;QAC7BC,OAAO,EAAEf,MAAM;QACfC,KAAK,EAAE;OACR;MAED,MAAM8C,IAAI,GAAGhB,QAAQ,CAACiB,cAAc,CAAClC,QAAQ,CAAC;MAC9CiC,IAAI,CAACE,WAAW,CAACjD,MAAM,CAAC;IAC1B;EACF;EAEA;EACQI,8BAA8BA,CAAEE,OAAO;IAC7C,MAAMT,IAAI,GAAG,EAAE;IACfT,MAAM,CAACC,IAAI,CAACiB,OAAO,CAAC4C,YAAY,CAAC,CAAC5D,OAAO,CAACC,GAAG,IAAG;MAC9CM,IAAI,CAACN,GAAG,CAAC,GAAGe,OAAO,CAAC4C,YAAY,CAAC3D,GAAG,CAAC;IACvC,CAAC,CAAC;IACFH,MAAM,CAACC,IAAI,CAACiB,OAAO,CAAC6C,OAAO,CAAC,CAAC7D,OAAO,CAACC,GAAG,IAAG;MACzCM,IAAI,CAACN,GAAG,CAAC,GAAGe,OAAO,CAAC6C,OAAO,CAAC5D,GAAG,CAAC;IAClC,CAAC,CAAC;IAEF,IAAI6D,YAAY,GAAG;MAAE,GAAG9C;IAAO,CAAE;IAEjC,IAAI8C,YAAY,CAACC,UAAU,EAAE;MAC3BD,YAAY,CAACC,UAAU,GAAGpF,MAAM,CAACmF,YAAY,CAACC,UAAU,CAAC,CAACC,SAAS,CAACF,YAAY,CAACC,UAAU,CAAC,CAACE,MAAM,EAAE;IACvG;IAEA,IAAIH,YAAY,CAACI,OAAO,EAAE;MACxBJ,YAAY,CAACI,OAAO,GAAGvF,MAAM,CAACmF,YAAY,CAACI,OAAO,CAAC,CAACF,SAAS,CAACF,YAAY,CAACI,OAAO,CAAC,CAACD,MAAM,EAAE;IAC9F;IAEA,MAAME,OAAO,GAAG;MACdC,OAAO,EAAE;QACP5B,QAAQ,EAAExB,OAAO,CAACwB,QAAQ;QAC1B6B,MAAM,EAAE,CAAC;QACTC,IAAI,EAAE;UAAED,MAAM,EAAE;QAAC,CAAE;QACnBE,KAAK,EAAE;UAAEF,MAAM,EAAE;QAAC;OACnB;MACDG,MAAM,EAAEjE,IAAI;MACZuD;KACD;IAED,IAAI9C,OAAO,CAACiB,MAAM,CAACwC,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE;MACjDN,OAAO,CAAC,QAAQ,CAAC,GAAGrE,MAAM,CAAC4E,MAAM,CAAC,EAAE,EAAE1D,OAAO,CAAC4C,YAAY,EAAE/E,wBAAwB,CAACmC,OAAO,CAAC6C,OAAO,CAAC,CAAC;IACxG,CAAC,MAAM;MACLM,OAAO,CAAC,QAAQ,CAAC,GAAGrE,MAAM,CAAC4E,MAAM,CAAC,EAAE,EAAE1D,OAAO,CAAC4C,YAAY,EAAE5C,OAAO,CAAC6C,OAAO,CAAC;IAC9E;IAEA,OAAOM,OAAO;EAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}