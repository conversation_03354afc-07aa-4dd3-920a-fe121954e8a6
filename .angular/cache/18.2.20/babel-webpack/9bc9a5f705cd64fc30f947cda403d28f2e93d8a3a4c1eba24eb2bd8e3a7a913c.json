{"ast": null, "code": "var NativeCustomEvent = global.CustomEvent;\nfunction useNative() {\n  try {\n    var p = new NativeCustomEvent('cat', {\n      detail: {\n        foo: 'bar'\n      }\n    });\n    return 'cat' === p.type && 'bar' === p.detail.foo;\n  } catch (e) {}\n  return false;\n}\n\n/**\n * Cross-browser `CustomEvent` constructor.\n *\n * https://developer.mozilla.org/en-US/docs/Web/API/CustomEvent.CustomEvent\n *\n * @public\n */\n\nmodule.exports = useNative() ? NativeCustomEvent :\n// IE >= 9\n'function' === typeof document.createEvent ? function CustomEvent(type, params) {\n  var e = document.createEvent('CustomEvent');\n  if (params) {\n    e.initCustomEvent(type, params.bubbles, params.cancelable, params.detail);\n  } else {\n    e.initCustomEvent(type, false, false, void 0);\n  }\n  return e;\n} :\n// IE <= 8\nfunction CustomEvent(type, params) {\n  var e = document.createEventObject();\n  e.type = type;\n  if (params) {\n    e.bubbles = Boolean(params.bubbles);\n    e.cancelable = Boolean(params.cancelable);\n    e.detail = params.detail;\n  } else {\n    e.bubbles = false;\n    e.cancelable = false;\n    e.detail = void 0;\n  }\n  return e;\n};", "map": {"version": 3, "names": ["NativeCustomEvent", "global", "CustomEvent", "useNative", "p", "detail", "foo", "type", "e", "module", "exports", "document", "createEvent", "params", "initCustomEvent", "bubbles", "cancelable", "createEventObject", "Boolean"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/node_modules/custom-event/index.js"], "sourcesContent": ["\nvar NativeCustomEvent = global.CustomEvent;\n\nfunction useNative () {\n  try {\n    var p = new NativeCustomEvent('cat', { detail: { foo: 'bar' } });\n    return  'cat' === p.type && 'bar' === p.detail.foo;\n  } catch (e) {\n  }\n  return false;\n}\n\n/**\n * Cross-browser `CustomEvent` constructor.\n *\n * https://developer.mozilla.org/en-US/docs/Web/API/CustomEvent.CustomEvent\n *\n * @public\n */\n\nmodule.exports = useNative() ? NativeCustomEvent :\n\n// IE >= 9\n'function' === typeof document.createEvent ? function CustomEvent (type, params) {\n  var e = document.createEvent('CustomEvent');\n  if (params) {\n    e.initCustomEvent(type, params.bubbles, params.cancelable, params.detail);\n  } else {\n    e.initCustomEvent(type, false, false, void 0);\n  }\n  return e;\n} :\n\n// IE <= 8\nfunction CustomEvent (type, params) {\n  var e = document.createEventObject();\n  e.type = type;\n  if (params) {\n    e.bubbles = Boolean(params.bubbles);\n    e.cancelable = Boolean(params.cancelable);\n    e.detail = params.detail;\n  } else {\n    e.bubbles = false;\n    e.cancelable = false;\n    e.detail = void 0;\n  }\n  return e;\n}\n"], "mappings": "AACA,IAAIA,iBAAiB,GAAGC,MAAM,CAACC,WAAW;AAE1C,SAASC,SAASA,CAAA,EAAI;EACpB,IAAI;IACF,IAAIC,CAAC,GAAG,IAAIJ,iBAAiB,CAAC,KAAK,EAAE;MAAEK,MAAM,EAAE;QAAEC,GAAG,EAAE;MAAM;IAAE,CAAC,CAAC;IAChE,OAAQ,KAAK,KAAKF,CAAC,CAACG,IAAI,IAAI,KAAK,KAAKH,CAAC,CAACC,MAAM,CAACC,GAAG;EACpD,CAAC,CAAC,OAAOE,CAAC,EAAE,CACZ;EACA,OAAO,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEAC,MAAM,CAACC,OAAO,GAAGP,SAAS,CAAC,CAAC,GAAGH,iBAAiB;AAEhD;AACA,UAAU,KAAK,OAAOW,QAAQ,CAACC,WAAW,GAAG,SAASV,WAAWA,CAAEK,IAAI,EAAEM,MAAM,EAAE;EAC/E,IAAIL,CAAC,GAAGG,QAAQ,CAACC,WAAW,CAAC,aAAa,CAAC;EAC3C,IAAIC,MAAM,EAAE;IACVL,CAAC,CAACM,eAAe,CAACP,IAAI,EAAEM,MAAM,CAACE,OAAO,EAAEF,MAAM,CAACG,UAAU,EAAEH,MAAM,CAACR,MAAM,CAAC;EAC3E,CAAC,MAAM;IACLG,CAAC,CAACM,eAAe,CAACP,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;EAC/C;EACA,OAAOC,CAAC;AACV,CAAC;AAED;AACA,SAASN,WAAWA,CAAEK,IAAI,EAAEM,MAAM,EAAE;EAClC,IAAIL,CAAC,GAAGG,QAAQ,CAACM,iBAAiB,CAAC,CAAC;EACpCT,CAAC,CAACD,IAAI,GAAGA,IAAI;EACb,IAAIM,MAAM,EAAE;IACVL,CAAC,CAACO,OAAO,GAAGG,OAAO,CAACL,MAAM,CAACE,OAAO,CAAC;IACnCP,CAAC,CAACQ,UAAU,GAAGE,OAAO,CAACL,MAAM,CAACG,UAAU,CAAC;IACzCR,CAAC,CAACH,MAAM,GAAGQ,MAAM,CAACR,MAAM;EAC1B,CAAC,MAAM;IACLG,CAAC,CAACO,OAAO,GAAG,KAAK;IACjBP,CAAC,CAACQ,UAAU,GAAG,KAAK;IACpBR,CAAC,CAACH,MAAM,GAAG,KAAK,CAAC;EACnB;EACA,OAAOG,CAAC;AACV,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}