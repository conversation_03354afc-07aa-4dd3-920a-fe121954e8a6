{"ast": null, "code": "import { SwuiGridComponent, SwuiGridDataService, SwuiTopFilterDataService } from '@skywind-group/lib-swui';\nimport { Subject, throwError } from 'rxjs';\nimport { catchError, map, takeUntil } from 'rxjs/operators';\nimport { CsvService } from '../../../../common/services/csv.service';\nimport { TransfersService } from '../../../../common/services/transfers.service';\nimport { TRANSFERS_SCHEMA } from './schema';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../common/services/entity-data-source.service\";\nimport * as i2 from \"../../../../common/services/transfers.service\";\nimport * as i3 from \"@skywind-group/lib-swui\";\nfunction TransfersComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵelement(1, \"hints\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"showCloseBtn\", false)(\"fontSize\", 16);\n  }\n}\nconst COMPONENT_NAME = 'transfers-list';\nexport class TransfersComponent {\n  constructor(entityDataSourceService, service, hubEntityService) {\n    this.entityDataSourceService = entityDataSourceService;\n    this.service = service;\n    this.componentName = COMPONENT_NAME;\n    this.schemaFilter = TRANSFERS_SCHEMA.filter(el => el.isFilterable);\n    this.schema = TRANSFERS_SCHEMA.filter(el => el.isList);\n    this.loading = false;\n    this.isEntity = false;\n    this.destroyed$ = new Subject();\n    hubEntityService.items$.pipe(takeUntil(this.destroyed$)).subscribe(data => {\n      this.schema.find(item => item.field === 'playerCode').td.data = data;\n    });\n    hubEntityService.entitySelected$.pipe(map(entity => entity?.type === 'entity'), takeUntil(this.destroyed$)).subscribe(isEntity => {\n      this.isEntity = isEntity;\n    });\n  }\n  ngOnInit() {\n    this.entityDataSourceService.show();\n  }\n  ngOnDestroy() {\n    this.destroyed$.next();\n    this.destroyed$.complete();\n    this.entityDataSourceService.hide();\n  }\n  downloadCsv() {\n    this.loading = true;\n    this.service.downloadCsv().pipe(catchError(err => {\n      this.loading = false;\n      return throwError(err);\n    }), takeUntil(this.destroyed$)).subscribe(() => this.loading = false);\n  }\n  exportPage() {\n    this.service.exportPage(this.grid.dataSource.data, this.grid.displayedColumns, this.grid.paginator.pageIndex + 1);\n  }\n  static {\n    this.ɵfac = function TransfersComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TransfersComponent)(i0.ɵɵdirectiveInject(i1.EntityDataSourceService), i0.ɵɵdirectiveInject(i2.TransfersService), i0.ɵɵdirectiveInject(i3.SwHubEntityService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TransfersComponent,\n      selectors: [[\"transfers-list\"]],\n      viewQuery: function TransfersComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(SwuiGridComponent, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.grid = _t.first);\n        }\n      },\n      features: [i0.ɵɵProvidersFeature([CsvService, TransfersService, SwuiTopFilterDataService, {\n        provide: SwuiGridDataService,\n        useExisting: TransfersService\n      }])],\n      decls: 10,\n      vars: 10,\n      consts: [[3, \"title\"], [1, \"p-32\", \"sw-grid-layout\"], [1, \"sw-grid-layout__table\"], [\"class\", \"margin-bottom16\", 4, \"ngIf\"], [3, \"schema\"], [3, \"blindPaginator\", \"useHubEntity\", \"schema\", \"savedFilteredPageName\", \"columnsManagement\", \"gridId\"], [3, \"downloadCsv\", \"loading\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Export current page\", 3, \"click\"], [1, \"margin-bottom16\"], [\"message\", \"PAYMENTS_TRANSFERS.note\", 3, \"showCloseBtn\", \"fontSize\"]],\n      template: function TransfersComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"lib-swui-page-panel\", 0);\n          i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵtemplate(3, TransfersComponent_div_3_Template, 2, 2, \"div\", 3);\n          i0.ɵɵelement(4, \"lib-swui-schema-top-filter\", 4);\n          i0.ɵɵelementStart(5, \"lib-swui-grid\", 5)(6, \"download-csv\", 6);\n          i0.ɵɵlistener(\"downloadCsv\", function TransfersComponent_Template_download_csv_downloadCsv_6_listener() {\n            return ctx.downloadCsv();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"button\", 7);\n          i0.ɵɵlistener(\"click\", function TransfersComponent_Template_button_click_7_listener() {\n            return ctx.exportPage();\n          });\n          i0.ɵɵelementStart(8, \"mat-icon\");\n          i0.ɵɵtext(9, \"archive\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"title\", \"MENU_SECTIONS.paymentsTransfers\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.isEntity);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"schema\", ctx.schemaFilter);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"blindPaginator\", true)(\"useHubEntity\", true)(\"schema\", ctx.schema)(\"savedFilteredPageName\", \"transfers\")(\"columnsManagement\", true)(\"gridId\", ctx.componentName);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"loading\", ctx.loading);\n        }\n      },\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["SwuiGridComponent", "SwuiGridDataService", "SwuiTopFilterDataService", "Subject", "throwError", "catchError", "map", "takeUntil", "CsvService", "TransfersService", "TRANSFERS_SCHEMA", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "COMPONENT_NAME", "TransfersComponent", "constructor", "entityDataSourceService", "service", "hubEntityService", "componentName", "schemaFilter", "filter", "el", "isFilterable", "schema", "isList", "loading", "isEntity", "destroyed$", "items$", "pipe", "subscribe", "data", "find", "item", "field", "td", "entitySelected$", "entity", "type", "ngOnInit", "show", "ngOnDestroy", "next", "complete", "hide", "downloadCsv", "err", "exportPage", "grid", "dataSource", "displayedColumns", "paginator", "pageIndex", "ɵɵdirectiveInject", "i1", "EntityDataSourceService", "i2", "i3", "SwHubEntityService", "selectors", "viewQuery", "TransfersComponent_Query", "rf", "ctx", "provide", "useExisting", "decls", "vars", "consts", "template", "TransfersComponent_Template", "ɵɵtemplate", "TransfersComponent_div_3_Template", "ɵɵlistener", "TransfersComponent_Template_download_csv_downloadCsv_6_listener", "TransfersComponent_Template_button_click_7_listener", "ɵɵtext"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/payments/components/transfers/transfers.component.ts", "/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/payments/components/transfers/transfers.component.html"], "sourcesContent": ["import { Component, ViewChild } from '@angular/core';\nimport { SwHubEntityService, SwuiGridComponent, SwuiGridDataService, SwuiTopFilterDataService } from '@skywind-group/lib-swui';\nimport { Subject, throwError } from 'rxjs';\nimport { catchError, map, takeUntil } from 'rxjs/operators';\nimport { CsvService } from '../../../../common/services/csv.service';\nimport { EntityDataSourceService } from '../../../../common/services/entity-data-source.service';\nimport { TransfersService } from '../../../../common/services/transfers.service';\nimport { Payment } from '../../../../common/typings';\nimport { TRANSFERS_SCHEMA } from './schema';\n\n\nconst COMPONENT_NAME = 'transfers-list';\n\n@Component({\n  selector: 'transfers-list',\n  templateUrl: './transfers.component.html',\n  providers: [\n    CsvService,\n    TransfersService,\n    SwuiTopFilterDataService,\n    { provide: SwuiGridDataService, useExisting: TransfersService }\n  ]\n})\nexport class TransfersComponent {\n  @ViewChild(SwuiGridComponent, { static: true }) grid: SwuiGridComponent<Payment>;\n\n  readonly componentName = COMPONENT_NAME;\n  readonly schemaFilter = TRANSFERS_SCHEMA.filter(el => el.isFilterable);\n  readonly schema = TRANSFERS_SCHEMA.filter(el => el.isList);\n\n  loading: boolean = false;\n  isEntity = false;\n\n  private destroyed$ = new Subject<void>();\n\n  constructor( private readonly entityDataSourceService: EntityDataSourceService,\n               private readonly service: TransfersService,\n               hubEntityService: SwHubEntityService,\n  ) {\n    hubEntityService.items$.pipe(\n      takeUntil(this.destroyed$)\n    ).subscribe(( data ) => {\n      this.schema.find(( item ) => item.field === 'playerCode').td.data = data;\n    });\n\n    hubEntityService.entitySelected$.pipe(\n      map(entity => entity?.type === 'entity'),\n      takeUntil(this.destroyed$),\n    ).subscribe(isEntity => {\n      this.isEntity = isEntity;\n    });\n  }\n\n  ngOnInit(): void {\n    this.entityDataSourceService.show();\n  }\n\n  ngOnDestroy() {\n    this.destroyed$.next();\n    this.destroyed$.complete();\n    this.entityDataSourceService.hide();\n  }\n\n  downloadCsv() {\n    this.loading = true;\n    this.service.downloadCsv()\n      .pipe(\n        catchError(( err ) => {\n          this.loading = false;\n          return throwError(err);\n        }),\n        takeUntil(this.destroyed$)\n      ).subscribe(() => this.loading = false);\n  }\n\n  exportPage() {\n    this.service.exportPage(this.grid.dataSource.data, this.grid.displayedColumns, this.grid.paginator.pageIndex + 1);\n  }\n}\n", "<lib-swui-page-panel\n  [title]=\"'MENU_SECTIONS.paymentsTransfers'\">\n</lib-swui-page-panel>\n\n<div class=\"p-32 sw-grid-layout\">\n  <div class=\"sw-grid-layout__table\">\n    <div *ngIf=\"isEntity\" class=\"margin-bottom16\">\n      <hints message=\"PAYMENTS_TRANSFERS.note\" [showCloseBtn]=\"false\" [fontSize]=\"16\"></hints>\n    </div>\n    <lib-swui-schema-top-filter [schema]=\"schemaFilter\"></lib-swui-schema-top-filter>\n    <lib-swui-grid\n      [blindPaginator]=\"true\"\n      [useHubEntity]=\"true\"\n      [schema]=\"schema\"\n      [savedFilteredPageName]=\"'transfers'\"\n      [columnsManagement]=\"true\"\n      [gridId]=\"componentName\">\n      <download-csv [loading]=\"loading\" (downloadCsv)=\"downloadCsv()\"></download-csv>\n      <button mat-icon-button matTooltip=\"Export current page\" (click)=\"exportPage()\">\n        <mat-icon>archive</mat-icon>\n      </button>\n    </lib-swui-grid>\n  </div>\n</div>\n"], "mappings": "AACA,SAA6BA,iBAAiB,EAAEC,mBAAmB,EAAEC,wBAAwB,QAAQ,yBAAyB;AAC9H,SAASC,OAAO,EAAEC,UAAU,QAAQ,MAAM;AAC1C,SAASC,UAAU,EAAEC,GAAG,EAAEC,SAAS,QAAQ,gBAAgB;AAC3D,SAASC,UAAU,QAAQ,yCAAyC;AAEpE,SAASC,gBAAgB,QAAQ,+CAA+C;AAEhF,SAASC,gBAAgB,QAAQ,UAAU;;;;;;;ICFvCC,EAAA,CAAAC,cAAA,aAA8C;IAC5CD,EAAA,CAAAE,SAAA,eAAwF;IAC1FF,EAAA,CAAAG,YAAA,EAAM;;;IADqCH,EAAA,CAAAI,SAAA,EAAsB;IAACJ,EAAvB,CAAAK,UAAA,uBAAsB,gBAAgB;;;ADIrF,MAAMC,cAAc,GAAG,gBAAgB;AAYvC,OAAM,MAAOC,kBAAkB;EAY7BC,YAA8BC,uBAAgD,EAChDC,OAAyB,EAC1CC,gBAAoC;IAFnB,KAAAF,uBAAuB,GAAvBA,uBAAuB;IACvB,KAAAC,OAAO,GAAPA,OAAO;IAV5B,KAAAE,aAAa,GAAGN,cAAc;IAC9B,KAAAO,YAAY,GAAGd,gBAAgB,CAACe,MAAM,CAACC,EAAE,IAAIA,EAAE,CAACC,YAAY,CAAC;IAC7D,KAAAC,MAAM,GAAGlB,gBAAgB,CAACe,MAAM,CAACC,EAAE,IAAIA,EAAE,CAACG,MAAM,CAAC;IAE1D,KAAAC,OAAO,GAAY,KAAK;IACxB,KAAAC,QAAQ,GAAG,KAAK;IAER,KAAAC,UAAU,GAAG,IAAI7B,OAAO,EAAQ;IAMtCmB,gBAAgB,CAACW,MAAM,CAACC,IAAI,CAC1B3B,SAAS,CAAC,IAAI,CAACyB,UAAU,CAAC,CAC3B,CAACG,SAAS,CAAGC,IAAI,IAAK;MACrB,IAAI,CAACR,MAAM,CAACS,IAAI,CAAGC,IAAI,IAAMA,IAAI,CAACC,KAAK,KAAK,YAAY,CAAC,CAACC,EAAE,CAACJ,IAAI,GAAGA,IAAI;IAC1E,CAAC,CAAC;IAEFd,gBAAgB,CAACmB,eAAe,CAACP,IAAI,CACnC5B,GAAG,CAACoC,MAAM,IAAIA,MAAM,EAAEC,IAAI,KAAK,QAAQ,CAAC,EACxCpC,SAAS,CAAC,IAAI,CAACyB,UAAU,CAAC,CAC3B,CAACG,SAAS,CAACJ,QAAQ,IAAG;MACrB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IAC1B,CAAC,CAAC;EACJ;EAEAa,QAAQA,CAAA;IACN,IAAI,CAACxB,uBAAuB,CAACyB,IAAI,EAAE;EACrC;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACd,UAAU,CAACe,IAAI,EAAE;IACtB,IAAI,CAACf,UAAU,CAACgB,QAAQ,EAAE;IAC1B,IAAI,CAAC5B,uBAAuB,CAAC6B,IAAI,EAAE;EACrC;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACpB,OAAO,GAAG,IAAI;IACnB,IAAI,CAACT,OAAO,CAAC6B,WAAW,EAAE,CACvBhB,IAAI,CACH7B,UAAU,CAAG8C,GAAG,IAAK;MACnB,IAAI,CAACrB,OAAO,GAAG,KAAK;MACpB,OAAO1B,UAAU,CAAC+C,GAAG,CAAC;IACxB,CAAC,CAAC,EACF5C,SAAS,CAAC,IAAI,CAACyB,UAAU,CAAC,CAC3B,CAACG,SAAS,CAAC,MAAM,IAAI,CAACL,OAAO,GAAG,KAAK,CAAC;EAC3C;EAEAsB,UAAUA,CAAA;IACR,IAAI,CAAC/B,OAAO,CAAC+B,UAAU,CAAC,IAAI,CAACC,IAAI,CAACC,UAAU,CAAClB,IAAI,EAAE,IAAI,CAACiB,IAAI,CAACE,gBAAgB,EAAE,IAAI,CAACF,IAAI,CAACG,SAAS,CAACC,SAAS,GAAG,CAAC,CAAC;EACnH;;;uCAtDWvC,kBAAkB,EAAAP,EAAA,CAAA+C,iBAAA,CAAAC,EAAA,CAAAC,uBAAA,GAAAjD,EAAA,CAAA+C,iBAAA,CAAAG,EAAA,CAAApD,gBAAA,GAAAE,EAAA,CAAA+C,iBAAA,CAAAI,EAAA,CAAAC,kBAAA;IAAA;EAAA;;;YAAlB7C,kBAAkB;MAAA8C,SAAA;MAAAC,SAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;yBAClBnE,iBAAiB;;;;;;;uCARjB,CACTQ,UAAU,EACVC,gBAAgB,EAChBP,wBAAwB,EACxB;QAAEmE,OAAO,EAAEpE,mBAAmB;QAAEqE,WAAW,EAAE7D;MAAgB,CAAE,CAChE;MAAA8D,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAR,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCrBHxD,EAAA,CAAAE,SAAA,6BAEsB;UAGpBF,EADF,CAAAC,cAAA,aAAiC,aACI;UACjCD,EAAA,CAAAiE,UAAA,IAAAC,iCAAA,iBAA8C;UAG9ClE,EAAA,CAAAE,SAAA,oCAAiF;UAQ/EF,EAPF,CAAAC,cAAA,uBAM2B,sBACuC;UAA9BD,EAAA,CAAAmE,UAAA,yBAAAC,gEAAA;YAAA,OAAeX,GAAA,CAAAlB,WAAA,EAAa;UAAA,EAAC;UAACvC,EAAA,CAAAG,YAAA,EAAe;UAC/EH,EAAA,CAAAC,cAAA,gBAAgF;UAAvBD,EAAA,CAAAmE,UAAA,mBAAAE,oDAAA;YAAA,OAASZ,GAAA,CAAAhB,UAAA,EAAY;UAAA,EAAC;UAC7EzC,EAAA,CAAAC,cAAA,eAAU;UAAAD,EAAA,CAAAsE,MAAA,cAAO;UAIzBtE,EAJyB,CAAAG,YAAA,EAAW,EACrB,EACK,EACZ,EACF;;;UAtBJH,EAAA,CAAAK,UAAA,4CAA2C;UAKnCL,EAAA,CAAAI,SAAA,GAAc;UAAdJ,EAAA,CAAAK,UAAA,SAAAoD,GAAA,CAAArC,QAAA,CAAc;UAGQpB,EAAA,CAAAI,SAAA,EAAuB;UAAvBJ,EAAA,CAAAK,UAAA,WAAAoD,GAAA,CAAA5C,YAAA,CAAuB;UAEjDb,EAAA,CAAAI,SAAA,EAAuB;UAKvBJ,EALA,CAAAK,UAAA,wBAAuB,sBACF,WAAAoD,GAAA,CAAAxC,MAAA,CACJ,sCACoB,2BACX,WAAAwC,GAAA,CAAA7C,aAAA,CACF;UACVZ,EAAA,CAAAI,SAAA,EAAmB;UAAnBJ,EAAA,CAAAK,UAAA,YAAAoD,GAAA,CAAAtC,OAAA,CAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}