{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { RouterModule } from '@angular/router';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiControlMessagesModule, SwuiGridModule, SwuiNotificationsModule, SwuiPagePanelModule, SwuiSchemaTopFilterModule } from '@skywind-group/lib-swui';\nimport { JackpotService } from 'src/app/common/services/jackpot.service';\nimport { ControlMessagesModule } from '../../../common/components/control-messages/control-messages.module';\nimport { BaIfAllowedModule } from '../../../common/directives/baIfAllowed/baIfAllowed.module';\nimport { GameProviderService } from '../../../common/services/game-provider.service';\nimport { GameService } from '../../../common/services/game.service';\nimport { GamesListComponent } from './games-list.component';\nimport { CloneGameComponent } from './modals/clone-game/clone-game.component';\nimport { SetJackpotComponent } from './modals/set-jackpot/set-jackpot.component';\nimport * as i0 from \"@angular/core\";\nexport class GamesListModule {\n  static {\n    this.ɵfac = function GamesListModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || GamesListModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: GamesListModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [GameProviderService, GameService, JackpotService],\n      imports: [BaIfAllowedModule, CommonModule, ControlMessagesModule, ReactiveFormsModule, RouterModule, TranslateModule, SwuiGridModule, SwuiPagePanelModule, MatIconModule, MatTooltipModule, MatButtonModule, SwuiSchemaTopFilterModule, MatDialogModule, MatFormFieldModule, SwuiControlMessagesModule, MatInputModule, SwuiNotificationsModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(GamesListModule, {\n    declarations: [CloneGameComponent, GamesListComponent, SetJackpotComponent],\n    imports: [BaIfAllowedModule, CommonModule, ControlMessagesModule, ReactiveFormsModule, RouterModule, TranslateModule, SwuiGridModule, SwuiPagePanelModule, MatIconModule, MatTooltipModule, MatButtonModule, SwuiSchemaTopFilterModule, MatDialogModule, MatFormFieldModule, SwuiControlMessagesModule, MatInputModule, SwuiNotificationsModule],\n    exports: [GamesListComponent]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "ReactiveFormsModule", "MatButtonModule", "MatDialogModule", "MatFormFieldModule", "MatIconModule", "MatInputModule", "MatTooltipModule", "RouterModule", "TranslateModule", "SwuiControlMessagesModule", "SwuiGridModule", "SwuiNotificationsModule", "SwuiPagePanelModule", "SwuiSchemaTopFilterModule", "JackpotService", "ControlMessagesModule", "BaIfAllowedModule", "GameProviderService", "GameService", "GamesListComponent", "CloneGameComponent", "SetJackpotComponent", "GamesListModule", "imports", "declarations", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/games-management/games-list/games-list.module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { RouterModule } from '@angular/router';\nimport { TranslateModule } from '@ngx-translate/core';\nimport {\n  SwuiControlMessagesModule, SwuiGridModule, SwuiNotificationsModule, SwuiPagePanelModule, SwuiSchemaTopFilterModule\n} from '@skywind-group/lib-swui';\nimport { JackpotService } from 'src/app/common/services/jackpot.service';\nimport { ControlMessagesModule } from '../../../common/components/control-messages/control-messages.module';\n\nimport { BaIfAllowedModule } from '../../../common/directives/baIfAllowed/baIfAllowed.module';\nimport { GameProviderService } from '../../../common/services/game-provider.service';\nimport { GameService } from '../../../common/services/game.service';\nimport { GamesListComponent } from './games-list.component';\nimport { CloneGameComponent } from './modals/clone-game/clone-game.component';\nimport { SetJackpotComponent } from './modals/set-jackpot/set-jackpot.component';\n\n@NgModule({\n  imports: [\n    BaIfAllowedModule,\n    CommonModule,\n    ControlMessagesModule,\n    ReactiveFormsModule,\n    RouterModule,\n    TranslateModule,\n    SwuiGridModule,\n    SwuiPagePanelModule,\n    MatIconModule,\n    MatTooltipModule,\n    MatButtonModule,\n    SwuiSchemaTopFilterModule,\n    MatDialogModule,\n    MatFormFieldModule,\n    SwuiControlMessagesModule,\n    MatInputModule,\n    SwuiNotificationsModule\n  ],\n  exports: [\n    GamesListComponent\n  ],\n  declarations: [\n    CloneGameComponent,\n    GamesListComponent,\n    SetJackpotComponent,\n  ],\n  providers: [\n    GameProviderService,\n    GameService,\n    JackpotService\n  ],\n})\nexport class GamesListModule {\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SACEC,yBAAyB,EAAEC,cAAc,EAAEC,uBAAuB,EAAEC,mBAAmB,EAAEC,yBAAyB,QAC7G,yBAAyB;AAChC,SAASC,cAAc,QAAQ,yCAAyC;AACxE,SAASC,qBAAqB,QAAQ,qEAAqE;AAE3G,SAASC,iBAAiB,QAAQ,2DAA2D;AAC7F,SAASC,mBAAmB,QAAQ,gDAAgD;AACpF,SAASC,WAAW,QAAQ,uCAAuC;AACnE,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,SAASC,kBAAkB,QAAQ,0CAA0C;AAC7E,SAASC,mBAAmB,QAAQ,4CAA4C;;AAoChF,OAAM,MAAOC,eAAe;;;uCAAfA,eAAe;IAAA;EAAA;;;YAAfA;IAAe;EAAA;;;iBANf,CACTL,mBAAmB,EACnBC,WAAW,EACXJ,cAAc,CACf;MAAAS,OAAA,GA9BCP,iBAAiB,EACjBjB,YAAY,EACZgB,qBAAqB,EACrBf,mBAAmB,EACnBO,YAAY,EACZC,eAAe,EACfE,cAAc,EACdE,mBAAmB,EACnBR,aAAa,EACbE,gBAAgB,EAChBL,eAAe,EACfY,yBAAyB,EACzBX,eAAe,EACfC,kBAAkB,EAClBM,yBAAyB,EACzBJ,cAAc,EACdM,uBAAuB;IAAA;EAAA;;;2EAgBdW,eAAe;IAAAE,YAAA,GAVxBJ,kBAAkB,EAClBD,kBAAkB,EAClBE,mBAAmB;IAAAE,OAAA,GAxBnBP,iBAAiB,EACjBjB,YAAY,EACZgB,qBAAqB,EACrBf,mBAAmB,EACnBO,YAAY,EACZC,eAAe,EACfE,cAAc,EACdE,mBAAmB,EACnBR,aAAa,EACbE,gBAAgB,EAChBL,eAAe,EACfY,yBAAyB,EACzBX,eAAe,EACfC,kBAAkB,EAClBM,yBAAyB,EACzBJ,cAAc,EACdM,uBAAuB;IAAAc,OAAA,GAGvBN,kBAAkB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}