{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../manage-games.service\";\nimport * as i2 from \"@angular/common\";\nfunction ManageGamesPreviewComponent_ul_7_li_1_i_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 17);\n  }\n  if (rf & 2) {\n    const game_r1 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵpropertyInterpolate(\"title\", ctx_r1.getReadableSetitngs(game_r1.settings));\n  }\n}\nfunction ManageGamesPreviewComponent_ul_7_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 13)(1, \"ul\", 14)(2, \"li\");\n    i0.ɵɵtemplate(3, ManageGamesPreviewComponent_ul_7_li_1_i_3_Template, 1, 1, \"i\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"li\")(5, \"span\", 16);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"span\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const game_r1 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getGameItemClass(game_r1));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", game_r1 == null ? null : game_r1.settings);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getStatusClass(game_r1.status));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.getStatusName(game_r1.status));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", game_r1.title, \" (\", game_r1.code, \")\");\n  }\n}\nfunction ManageGamesPreviewComponent_ul_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\", 11);\n    i0.ɵɵtemplate(1, ManageGamesPreviewComponent_ul_7_li_1_Template, 9, 6, \"li\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.gamesToAdd);\n  }\n}\nfunction ManageGamesPreviewComponent_ul_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\", 11)(1, \"li\")(2, \"span\", 18);\n    i0.ɵɵtext(3, \"No games\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ManageGamesPreviewComponent_ul_21_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 20)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const game_r3 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", game_r3.title, \" (\", game_r3.code, \")\");\n  }\n}\nfunction ManageGamesPreviewComponent_ul_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\", 11);\n    i0.ɵɵtemplate(1, ManageGamesPreviewComponent_ul_21_li_1_Template, 3, 2, \"li\", 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.gamesToRemove);\n  }\n}\nfunction ManageGamesPreviewComponent_ul_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\", 11)(1, \"li\", 20)(2, \"span\", 18);\n    i0.ɵɵtext(3, \"No games\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nexport class ManageGamesPreviewComponent {\n  constructor(manageGamesService) {\n    this.manageGamesService = manageGamesService;\n    this.changesConfirm = new EventEmitter();\n    this.subscriptions = [];\n    this.addedGames = [];\n    this.rejectedGames = {};\n    this.statusMap = {\n      test: {\n        displayName: 'Test',\n        cssClass: 'bg-default'\n      },\n      normal: {\n        displayName: 'Active',\n        cssClass: 'bg-success'\n      },\n      suspended: {\n        displayName: 'Inactive',\n        cssClass: 'bg-warning'\n      }\n    };\n  }\n  ngOnInit() {\n    this.subscribeToManageGamesEvents();\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n  getStatusClass(statusCode) {\n    return this.statusMap.hasOwnProperty(statusCode) ? this.statusMap[statusCode].cssClass : 'bg-grey-300';\n  }\n  getStatusName(statusCode) {\n    let statusName = 'Unknown';\n    if (statusCode !== '' && this.statusMap.hasOwnProperty(statusCode)) {\n      statusName = this.statusMap[statusCode].displayName;\n    }\n    return statusName;\n  }\n  getReadableSetitngs(settings) {\n    return JSON.stringify(settings, null, 2);\n  }\n  confirmAllChanges(event) {\n    event.preventDefault();\n    this.changesConfirm.emit(true);\n  }\n  getGameItemClass(game) {\n    const added = this.addedGames.indexOf(game.code) > -1;\n    const rejected = game.code in this.rejectedGames;\n    const classes = {\n      'bg-success-300': added,\n      'text-success-800': added,\n      'bg-danger-300': rejected,\n      'text-white': rejected\n    };\n    return classes;\n  }\n  subscribeToManageGamesEvents() {\n    const addedSub = this.manageGamesService.added$.subscribe(game => {\n      this.addedGames.push(game.code);\n    });\n    const rejectedSub = this.manageGamesService.rejected$.subscribe(rejected => {\n      this.rejectedGames[rejected.game.code] = rejected.reason;\n    });\n    this.subscriptions.push(addedSub, rejectedSub);\n  }\n  static {\n    this.ɵfac = function ManageGamesPreviewComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ManageGamesPreviewComponent)(i0.ɵɵdirectiveInject(i1.ManageGamesService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ManageGamesPreviewComponent,\n      selectors: [[\"manage-games-preview\"]],\n      inputs: {\n        gamesToAdd: \"gamesToAdd\",\n        gamesToRemove: \"gamesToRemove\"\n      },\n      outputs: {\n        changesConfirm: \"changesConfirm\"\n      },\n      decls: 29,\n      vars: 6,\n      consts: [[1, \"row\"], [1, \"col-sm-6\"], [1, \"panel\", \"panel-default\"], [1, \"panel-heading\", \"pt-10\", \"pb-10\"], [1, \"text-semibold\", \"no-margin\"], [1, \"panel-body\", \"no-padding\"], [\"class\", \"list list-unstyled list-games\", 4, \"ngIf\"], [1, \"panel-footer\"], [1, \"ml-20\"], [1, \"text-muted\"], [1, \"text-semibold\", \"ml-5\"], [1, \"list\", \"list-unstyled\", \"list-games\"], [3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [3, \"ngClass\"], [1, \"list-inline\", \"pull-right\"], [\"class\", \"icon-gear position-left mr-5\", \"style\", \"cursor: help\", 3, \"title\", 4, \"ngIf\"], [1, \"label\", 3, \"ngClass\"], [1, \"icon-gear\", \"position-left\", \"mr-5\", 2, \"cursor\", \"help\", 3, \"title\"], [1, \"label\", \"label-info\", \"ml-10\"], [\"class\", \"no-margin\", 4, \"ngFor\", \"ngForOf\"], [1, \"no-margin\"]],\n      template: function ManageGamesPreviewComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h6\", 4);\n          i0.ɵɵtext(5, \"Games to add\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"div\", 5);\n          i0.ɵɵtemplate(7, ManageGamesPreviewComponent_ul_7_Template, 2, 1, \"ul\", 6)(8, ManageGamesPreviewComponent_ul_8_Template, 4, 0, \"ul\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"div\", 7)(10, \"span\", 8)(11, \"span\", 9);\n          i0.ɵɵtext(12, \"Total:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"span\", 10);\n          i0.ɵɵtext(14);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(15, \"div\", 1)(16, \"div\", 2)(17, \"div\", 3)(18, \"h6\", 4);\n          i0.ɵɵtext(19, \"Games to remove\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"div\", 5);\n          i0.ɵɵtemplate(21, ManageGamesPreviewComponent_ul_21_Template, 2, 1, \"ul\", 6)(22, ManageGamesPreviewComponent_ul_22_Template, 4, 0, \"ul\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"div\", 7)(24, \"span\", 8)(25, \"span\", 9);\n          i0.ɵɵtext(26, \"Total:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"span\", 10);\n          i0.ɵɵtext(28);\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ctx.gamesToAdd);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.gamesToAdd || ctx.gamesToAdd.length === 0);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(ctx.gamesToAdd ? ctx.gamesToAdd.length : 0);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ctx.gamesToRemove);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.gamesToRemove || ctx.gamesToRemove.length === 0);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(ctx.gamesToRemove ? ctx.gamesToRemove.length : 0);\n        }\n      },\n      dependencies: [i2.NgClass, i2.NgForOf, i2.NgIf],\n      styles: [\".list-games[_ngcontent-%COMP%] {\\n  max-height: 350px;\\n  margin-bottom: 0;\\n  overflow: auto;\\n  white-space: nowrap;\\n}\\n.list-games[_ngcontent-%COMP%]    > li[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding: 5px 20px;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n  border-bottom: 1px solid #ddd;\\n}\\n.list-games[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvYnVzaW5lc3MtbWFuYWdlbWVudC9jb21wb25lbnRzL2VudGl0aWVzL3RhYi1nYW1lcy9tYW5hZ2UtZ2FtZXMvbWFuYWdlLWdhbWVzLXByZXZpZXcvbWFuYWdlLWdhbWVzLXByZXZpZXcuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxpQkFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLG1CQUFBO0FBQ0Y7QUFBRTtFQUNFLFNBQUE7RUFDQSxpQkFBQTtFQUNBLGdCQUFBO0VBQ0EsdUJBQUE7RUFDQSxtQkFBQTtFQUNBLDZCQUFBO0FBRUo7QUFDSTtFQUNFLG1CQUFBO0FBQ04iLCJzb3VyY2VzQ29udGVudCI6WyIubGlzdC1nYW1lcyB7XG4gIG1heC1oZWlnaHQ6IDM1MHB4O1xuICBtYXJnaW4tYm90dG9tOiAwO1xuICBvdmVyZmxvdzogYXV0bztcbiAgd2hpdGUtc3BhY2U6IG5vd3JhcDtcbiAgJj5saSB7XG4gICAgbWFyZ2luOiAwO1xuICAgIHBhZGRpbmc6IDVweCAyMHB4O1xuICAgIG92ZXJmbG93OiBoaWRkZW47XG4gICAgdGV4dC1vdmVyZmxvdzogZWxsaXBzaXM7XG4gICAgd2hpdGUtc3BhY2U6IG5vd3JhcDtcbiAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2RkZDtcbiAgfVxuICBsaSB7XG4gICAgJjpsYXN0LWNoaWxkIHtcbiAgICAgIGJvcmRlci1ib3R0b206IG5vbmU7XG4gICAgfVxuICB9XG59XG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "i0", "ɵɵelement", "ɵɵpropertyInterpolate", "ctx_r1", "getReadableSetitngs", "game_r1", "settings", "ɵɵelementStart", "ɵɵtemplate", "ManageGamesPreviewComponent_ul_7_li_1_i_3_Template", "ɵɵelementEnd", "ɵɵtext", "ɵɵproperty", "getGameItemClass", "ɵɵadvance", "getStatusClass", "status", "ɵɵtextInterpolate", "getStatusName", "ɵɵtextInterpolate2", "title", "code", "ManageGamesPreviewComponent_ul_7_li_1_Template", "gamesToAdd", "game_r3", "ManageGamesPreviewComponent_ul_21_li_1_Template", "gamesToRemove", "ManageGamesPreviewComponent", "constructor", "manageGamesService", "changesConfirm", "subscriptions", "addedGames", "rejectedG<PERSON>s", "statusMap", "test", "displayName", "cssClass", "normal", "suspended", "ngOnInit", "subscribeToManageGamesEvents", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "statusCode", "hasOwnProperty", "statusName", "JSON", "stringify", "confirmAllChanges", "event", "preventDefault", "emit", "game", "added", "indexOf", "rejected", "classes", "addedSub", "added$", "subscribe", "push", "rejectedSub", "rejected$", "reason", "ɵɵdirectiveInject", "i1", "ManageGamesService", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "ManageGamesPreviewComponent_Template", "rf", "ctx", "ManageGamesPreviewComponent_ul_7_Template", "ManageGamesPreviewComponent_ul_8_Template", "ManageGamesPreviewComponent_ul_21_Template", "ManageGamesPreviewComponent_ul_22_Template", "length"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-games/manage-games/manage-games-preview/manage-games-preview.component.ts", "/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-games/manage-games/manage-games-preview/manage-games-preview.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';\nimport { Subscription } from 'rxjs';\nimport { Game } from '../../../../../../../common/typings';\nimport { ManageGamesService, RejectedGame } from '../../manage-games.service';\n\n@Component({\n  selector: 'manage-games-preview',\n  templateUrl: './manage-games-preview.component.html',\n  styleUrls: ['./manage-games-preview.component.scss'],\n})\nexport class ManageGamesPreviewComponent implements OnInit, OnDestroy {\n\n  @Input() gamesToAdd: Game[];\n  @Input() gamesToRemove: Game[];\n\n  @Output() changesConfirm: EventEmitter<boolean> = new EventEmitter();\n\n  subscriptions: Subscription[] = [];\n  addedGames: string[] = [];\n  rejectedGames: { [gamecode: string]: string } = {};\n\n  private statusMap = {\n    test: { displayName: 'Test', cssClass: 'bg-default' },\n    normal: { displayName: 'Active', cssClass: 'bg-success' },\n    suspended: { displayName: 'Inactive', cssClass: 'bg-warning' }\n  };\n\n  constructor(\n    private manageGamesService: ManageGamesService,\n  ) {\n\n  }\n\n  ngOnInit() {\n    this.subscribeToManageGamesEvents();\n  }\n\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n\n  getStatusClass( statusCode: string ): string {\n    return this.statusMap.hasOwnProperty(statusCode) ? this.statusMap[statusCode].cssClass : 'bg-grey-300';\n  }\n\n  getStatusName( statusCode: string ) {\n    let statusName = 'Unknown';\n    if (statusCode !== '' && this.statusMap.hasOwnProperty(statusCode)) {\n      statusName = this.statusMap[statusCode].displayName;\n    }\n    return statusName;\n  }\n\n  getReadableSetitngs( settings: Object ): string {\n    return JSON.stringify(settings, null, 2);\n  }\n\n  confirmAllChanges( event ) {\n    event.preventDefault();\n    this.changesConfirm.emit(true);\n  }\n\n  getGameItemClass( game ) {\n    const added: boolean = this.addedGames.indexOf(game.code) > -1;\n    const rejected: boolean = game.code in this.rejectedGames;\n    const classes = {\n      'bg-success-300': added,\n      'text-success-800': added,\n      'bg-danger-300': rejected,\n      'text-white': rejected,\n    };\n\n    return classes;\n  }\n\n  private subscribeToManageGamesEvents() {\n    const addedSub = this.manageGamesService.added$.subscribe(( game ) => {\n      this.addedGames.push(game.code);\n    });\n\n    const rejectedSub = this.manageGamesService.rejected$.subscribe(( rejected: RejectedGame ) => {\n      this.rejectedGames[rejected.game.code] = rejected.reason;\n    });\n\n    this.subscriptions.push(addedSub, rejectedSub);\n  }\n}\n", "<div class=\"row\">\n  <div class=\"col-sm-6\">\n    <div class=\"panel panel-default\">\n      <div class=\"panel-heading pt-10 pb-10\">\n        <h6 class=\"text-semibold no-margin\">Games to add</h6>\n      </div>\n      <div class=\"panel-body no-padding\">\n        <ul class=\"list list-unstyled list-games\" *ngIf=\"gamesToAdd\">\n          <li *ngFor=\"let game of gamesToAdd\" [ngClass]=\"getGameItemClass(game)\">\n            <ul class=\"list-inline pull-right\">\n              <li>\n                <i class=\"icon-gear position-left mr-5\" *ngIf=\"game?.settings\"\n                   title=\"{{ getReadableSetitngs(game.settings) }}\" style=\"cursor: help\"></i>\n              </li>\n              <li>\n                <span class=\"label\" [ngClass]=\"getStatusClass(game.status)\">{{ getStatusName(game.status) }}</span>\n              </li>\n            </ul>\n            <span>{{ game.title }} ({{ game.code }})</span>\n          </li>\n        </ul>\n        <ul class=\"list list-unstyled list-games\" *ngIf=\"!gamesToAdd || gamesToAdd.length === 0\">\n          <li>\n            <span class=\"label label-info ml-10\">No games</span>\n          </li>\n        </ul>\n      </div>\n      <div class=\"panel-footer\">\n        <span class=\"ml-20\">\n          <span class=\"text-muted\">Total:</span>\n          <span class=\"text-semibold ml-5\">{{ gamesToAdd ? gamesToAdd.length : 0 }}</span>\n        </span>\n      </div>\n    </div>\n  </div>\n  <div class=\"col-sm-6\">\n    <div class=\"panel panel-default\">\n      <div class=\"panel-heading pt-10 pb-10\">\n        <h6 class=\"text-semibold no-margin\">Games to remove</h6>\n      </div>\n      <div class=\"panel-body no-padding\">\n        <ul class=\"list list-unstyled list-games\" *ngIf=\"gamesToRemove\">\n          <li class=\"no-margin\" *ngFor=\"let game of gamesToRemove\">\n            <span>{{ game.title }} ({{ game.code }})</span>\n          </li>\n        </ul>\n        <ul class=\"list list-unstyled list-games\" *ngIf=\"!gamesToRemove || gamesToRemove.length === 0\">\n          <li class=\"no-margin\">\n            <span class=\"label label-info ml-10\">No games</span>\n          </li>\n        </ul>\n      </div>\n      <div class=\"panel-footer\">\n        <span class=\"ml-20\">\n          <span class=\"text-muted\">Total:</span>\n          <span class=\"text-semibold ml-5\">{{ gamesToRemove ? gamesToRemove.length : 0 }}</span>\n        </span>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAAA,SAAoBA,YAAY,QAA0C,eAAe;;;;;;ICWzEC,EAAA,CAAAC,SAAA,YAC6E;;;;;IAA1ED,EAAA,CAAAE,qBAAA,UAAAC,MAAA,CAAAC,mBAAA,CAAAC,OAAA,CAAAC,QAAA,EAAgD;;;;;IAFrDN,EAFJ,CAAAO,cAAA,aAAuE,aAClC,SAC7B;IACFP,EAAA,CAAAQ,UAAA,IAAAC,kDAAA,gBACyE;IAC3ET,EAAA,CAAAU,YAAA,EAAK;IAEHV,EADF,CAAAO,cAAA,SAAI,eAC0D;IAAAP,EAAA,CAAAW,MAAA,GAAgC;IAEhGX,EAFgG,CAAAU,YAAA,EAAO,EAChG,EACF;IACLV,EAAA,CAAAO,cAAA,WAAM;IAAAP,EAAA,CAAAW,MAAA,GAAkC;IAC1CX,EAD0C,CAAAU,YAAA,EAAO,EAC5C;;;;;IAX+BV,EAAA,CAAAY,UAAA,YAAAT,MAAA,CAAAU,gBAAA,CAAAR,OAAA,EAAkC;IAGvBL,EAAA,CAAAc,SAAA,GAAoB;IAApBd,EAAA,CAAAY,UAAA,SAAAP,OAAA,kBAAAA,OAAA,CAAAC,QAAA,CAAoB;IAIzCN,EAAA,CAAAc,SAAA,GAAuC;IAAvCd,EAAA,CAAAY,UAAA,YAAAT,MAAA,CAAAY,cAAA,CAAAV,OAAA,CAAAW,MAAA,EAAuC;IAAChB,EAAA,CAAAc,SAAA,EAAgC;IAAhCd,EAAA,CAAAiB,iBAAA,CAAAd,MAAA,CAAAe,aAAA,CAAAb,OAAA,CAAAW,MAAA,EAAgC;IAG1FhB,EAAA,CAAAc,SAAA,GAAkC;IAAlCd,EAAA,CAAAmB,kBAAA,KAAAd,OAAA,CAAAe,KAAA,QAAAf,OAAA,CAAAgB,IAAA,MAAkC;;;;;IAX5CrB,EAAA,CAAAO,cAAA,aAA6D;IAC3DP,EAAA,CAAAQ,UAAA,IAAAc,8CAAA,iBAAuE;IAYzEtB,EAAA,CAAAU,YAAA,EAAK;;;;IAZkBV,EAAA,CAAAc,SAAA,EAAa;IAAbd,EAAA,CAAAY,UAAA,YAAAT,MAAA,CAAAoB,UAAA,CAAa;;;;;IAehCvB,EAFJ,CAAAO,cAAA,aAAyF,SACnF,eACmC;IAAAP,EAAA,CAAAW,MAAA,eAAQ;IAEjDX,EAFiD,CAAAU,YAAA,EAAO,EACjD,EACF;;;;;IAkBDV,EADF,CAAAO,cAAA,aAAyD,WACjD;IAAAP,EAAA,CAAAW,MAAA,GAAkC;IAC1CX,EAD0C,CAAAU,YAAA,EAAO,EAC5C;;;;IADGV,EAAA,CAAAc,SAAA,GAAkC;IAAlCd,EAAA,CAAAmB,kBAAA,KAAAK,OAAA,CAAAJ,KAAA,QAAAI,OAAA,CAAAH,IAAA,MAAkC;;;;;IAF5CrB,EAAA,CAAAO,cAAA,aAAgE;IAC9DP,EAAA,CAAAQ,UAAA,IAAAiB,+CAAA,iBAAyD;IAG3DzB,EAAA,CAAAU,YAAA,EAAK;;;;IAHoCV,EAAA,CAAAc,SAAA,EAAgB;IAAhBd,EAAA,CAAAY,UAAA,YAAAT,MAAA,CAAAuB,aAAA,CAAgB;;;;;IAMrD1B,EAFJ,CAAAO,cAAA,aAA+F,aACvE,eACiB;IAAAP,EAAA,CAAAW,MAAA,eAAQ;IAEjDX,EAFiD,CAAAU,YAAA,EAAO,EACjD,EACF;;;ADxCb,OAAM,MAAOiB,2BAA2B;EAiBtCC,YACUC,kBAAsC;IAAtC,KAAAA,kBAAkB,GAAlBA,kBAAkB;IAblB,KAAAC,cAAc,GAA0B,IAAI/B,YAAY,EAAE;IAEpE,KAAAgC,aAAa,GAAmB,EAAE;IAClC,KAAAC,UAAU,GAAa,EAAE;IACzB,KAAAC,aAAa,GAAmC,EAAE;IAE1C,KAAAC,SAAS,GAAG;MAClBC,IAAI,EAAE;QAAEC,WAAW,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAY,CAAE;MACrDC,MAAM,EAAE;QAAEF,WAAW,EAAE,QAAQ;QAAEC,QAAQ,EAAE;MAAY,CAAE;MACzDE,SAAS,EAAE;QAAEH,WAAW,EAAE,UAAU;QAAEC,QAAQ,EAAE;MAAY;KAC7D;EAMD;EAEAG,QAAQA,CAAA;IACN,IAAI,CAACC,4BAA4B,EAAE;EACrC;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACX,aAAa,CAACY,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;EACtD;EAEA9B,cAAcA,CAAE+B,UAAkB;IAChC,OAAO,IAAI,CAACZ,SAAS,CAACa,cAAc,CAACD,UAAU,CAAC,GAAG,IAAI,CAACZ,SAAS,CAACY,UAAU,CAAC,CAACT,QAAQ,GAAG,aAAa;EACxG;EAEAnB,aAAaA,CAAE4B,UAAkB;IAC/B,IAAIE,UAAU,GAAG,SAAS;IAC1B,IAAIF,UAAU,KAAK,EAAE,IAAI,IAAI,CAACZ,SAAS,CAACa,cAAc,CAACD,UAAU,CAAC,EAAE;MAClEE,UAAU,GAAG,IAAI,CAACd,SAAS,CAACY,UAAU,CAAC,CAACV,WAAW;IACrD;IACA,OAAOY,UAAU;EACnB;EAEA5C,mBAAmBA,CAAEE,QAAgB;IACnC,OAAO2C,IAAI,CAACC,SAAS,CAAC5C,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;EAC1C;EAEA6C,iBAAiBA,CAAEC,KAAK;IACtBA,KAAK,CAACC,cAAc,EAAE;IACtB,IAAI,CAACvB,cAAc,CAACwB,IAAI,CAAC,IAAI,CAAC;EAChC;EAEAzC,gBAAgBA,CAAE0C,IAAI;IACpB,MAAMC,KAAK,GAAY,IAAI,CAACxB,UAAU,CAACyB,OAAO,CAACF,IAAI,CAAClC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC9D,MAAMqC,QAAQ,GAAYH,IAAI,CAAClC,IAAI,IAAI,IAAI,CAACY,aAAa;IACzD,MAAM0B,OAAO,GAAG;MACd,gBAAgB,EAAEH,KAAK;MACvB,kBAAkB,EAAEA,KAAK;MACzB,eAAe,EAAEE,QAAQ;MACzB,YAAY,EAAEA;KACf;IAED,OAAOC,OAAO;EAChB;EAEQlB,4BAA4BA,CAAA;IAClC,MAAMmB,QAAQ,GAAG,IAAI,CAAC/B,kBAAkB,CAACgC,MAAM,CAACC,SAAS,CAAGP,IAAI,IAAK;MACnE,IAAI,CAACvB,UAAU,CAAC+B,IAAI,CAACR,IAAI,CAAClC,IAAI,CAAC;IACjC,CAAC,CAAC;IAEF,MAAM2C,WAAW,GAAG,IAAI,CAACnC,kBAAkB,CAACoC,SAAS,CAACH,SAAS,CAAGJ,QAAsB,IAAK;MAC3F,IAAI,CAACzB,aAAa,CAACyB,QAAQ,CAACH,IAAI,CAAClC,IAAI,CAAC,GAAGqC,QAAQ,CAACQ,MAAM;IAC1D,CAAC,CAAC;IAEF,IAAI,CAACnC,aAAa,CAACgC,IAAI,CAACH,QAAQ,EAAEI,WAAW,CAAC;EAChD;;;uCA3EWrC,2BAA2B,EAAA3B,EAAA,CAAAmE,iBAAA,CAAAC,EAAA,CAAAC,kBAAA;IAAA;EAAA;;;YAA3B1C,2BAA2B;MAAA2C,SAAA;MAAAC,MAAA;QAAAhD,UAAA;QAAAG,aAAA;MAAA;MAAA8C,OAAA;QAAA1C,cAAA;MAAA;MAAA2C,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCNhC9E,EAJR,CAAAO,cAAA,aAAiB,aACO,aACa,aACQ,YACD;UAAAP,EAAA,CAAAW,MAAA,mBAAY;UAClDX,EADkD,CAAAU,YAAA,EAAK,EACjD;UACNV,EAAA,CAAAO,cAAA,aAAmC;UAejCP,EAdA,CAAAQ,UAAA,IAAAwE,yCAAA,gBAA6D,IAAAC,yCAAA,gBAc4B;UAK3FjF,EAAA,CAAAU,YAAA,EAAM;UAGFV,EAFJ,CAAAO,cAAA,aAA0B,eACJ,eACO;UAAAP,EAAA,CAAAW,MAAA,cAAM;UAAAX,EAAA,CAAAU,YAAA,EAAO;UACtCV,EAAA,CAAAO,cAAA,gBAAiC;UAAAP,EAAA,CAAAW,MAAA,IAAwC;UAIjFX,EAJiF,CAAAU,YAAA,EAAO,EAC3E,EACH,EACF,EACF;UAIAV,EAHN,CAAAO,cAAA,cAAsB,cACa,cACQ,aACD;UAAAP,EAAA,CAAAW,MAAA,uBAAe;UACrDX,EADqD,CAAAU,YAAA,EAAK,EACpD;UACNV,EAAA,CAAAO,cAAA,cAAmC;UAMjCP,EALA,CAAAQ,UAAA,KAAA0E,0CAAA,gBAAgE,KAAAC,0CAAA,gBAK+B;UAKjGnF,EAAA,CAAAU,YAAA,EAAM;UAGFV,EAFJ,CAAAO,cAAA,cAA0B,eACJ,eACO;UAAAP,EAAA,CAAAW,MAAA,cAAM;UAAAX,EAAA,CAAAU,YAAA,EAAO;UACtCV,EAAA,CAAAO,cAAA,gBAAiC;UAAAP,EAAA,CAAAW,MAAA,IAA8C;UAKzFX,EALyF,CAAAU,YAAA,EAAO,EACjF,EACH,EACF,EACF,EACF;;;UArD6CV,EAAA,CAAAc,SAAA,GAAgB;UAAhBd,EAAA,CAAAY,UAAA,SAAAmE,GAAA,CAAAxD,UAAA,CAAgB;UAchBvB,EAAA,CAAAc,SAAA,EAA4C;UAA5Cd,EAAA,CAAAY,UAAA,UAAAmE,GAAA,CAAAxD,UAAA,IAAAwD,GAAA,CAAAxD,UAAA,CAAA6D,MAAA,OAA4C;UASpDpF,EAAA,CAAAc,SAAA,GAAwC;UAAxCd,EAAA,CAAAiB,iBAAA,CAAA8D,GAAA,CAAAxD,UAAA,GAAAwD,GAAA,CAAAxD,UAAA,CAAA6D,MAAA,KAAwC;UAWhCpF,EAAA,CAAAc,SAAA,GAAmB;UAAnBd,EAAA,CAAAY,UAAA,SAAAmE,GAAA,CAAArD,aAAA,CAAmB;UAKnB1B,EAAA,CAAAc,SAAA,EAAkD;UAAlDd,EAAA,CAAAY,UAAA,UAAAmE,GAAA,CAAArD,aAAA,IAAAqD,GAAA,CAAArD,aAAA,CAAA0D,MAAA,OAAkD;UAS1DpF,EAAA,CAAAc,SAAA,GAA8C;UAA9Cd,EAAA,CAAAiB,iBAAA,CAAA8D,GAAA,CAAArD,aAAA,GAAAqD,GAAA,CAAArD,aAAA,CAAA0D,MAAA,KAA8C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}