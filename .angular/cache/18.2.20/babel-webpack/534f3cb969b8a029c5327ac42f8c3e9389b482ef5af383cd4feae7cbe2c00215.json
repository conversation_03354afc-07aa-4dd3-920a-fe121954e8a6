{"ast": null, "code": "'use strict';\n\nvar eventmap = [];\nvar eventname = '';\nvar ron = /^on/;\nfor (eventname in global) {\n  if (ron.test(eventname)) {\n    eventmap.push(eventname.slice(2));\n  }\n}\nmodule.exports = eventmap;", "map": {"version": 3, "names": ["eventmap", "eventname", "ron", "global", "test", "push", "slice", "module", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/node_modules/crossvent/src/eventmap.js"], "sourcesContent": ["'use strict';\n\nvar eventmap = [];\nvar eventname = '';\nvar ron = /^on/;\n\nfor (eventname in global) {\n  if (ron.test(eventname)) {\n    eventmap.push(eventname.slice(2));\n  }\n}\n\nmodule.exports = eventmap;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,QAAQ,GAAG,EAAE;AACjB,IAAIC,SAAS,GAAG,EAAE;AAClB,IAAIC,GAAG,GAAG,KAAK;AAEf,KAAKD,SAAS,IAAIE,MAAM,EAAE;EACxB,IAAID,GAAG,CAACE,IAAI,CAACH,SAAS,CAAC,EAAE;IACvBD,QAAQ,CAACK,IAAI,CAACJ,SAAS,CAACK,KAAK,CAAC,CAAC,CAAC,CAAC;EACnC;AACF;AAEAC,MAAM,CAACC,OAAO,GAAGR,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}