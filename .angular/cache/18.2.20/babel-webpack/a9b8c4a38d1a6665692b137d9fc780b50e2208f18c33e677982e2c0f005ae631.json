{"ast": null, "code": "import { of, throwError } from 'rxjs';\nimport { catchError, map, switchMap, tap } from 'rxjs/operators';\nimport { DOMAIN_TYPES } from '../../../../../../common/models/domain.model';\nimport { toDomainPool } from '../../../../../domains-management/domains-pool/domains-pool.service';\nimport { API_ENDPOINT } from '../../../../../../app.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@skywind-group/lib-swui\";\nfunction getUrl(path, poolType, id) {\n  return `${API_ENDPOINT}/entities/${path}/domain-pools/${id ? `${id}/` : ''}${poolType}`;\n}\nexport class EntityDomainPoolService {\n  constructor(http, notifications) {\n    this.http = http;\n    this.notifications = notifications;\n    this.cachedItem = {};\n  }\n  get(path, poolType, force = false) {\n    if (!force) {\n      if (this.cachedItem[path]) {\n        return of(this.cachedItem[path]);\n      }\n    }\n    return this.http.get(getUrl(path, poolType), {\n      params: {\n        inherited: true\n      }\n    }).pipe(catchError(() => of(undefined)), map(toDomainPool(DOMAIN_TYPES.static)), tap(data => {\n      this.cachedItem[path] = data;\n    }));\n  }\n  set(id, poolType, path) {\n    return this.http.put(getUrl(path, poolType, id), {}).pipe(catchError(error => {\n      this.notifications.error(error?.error?.message);\n      return throwError(error);\n    }), switchMap(() => this.get(path, poolType, true)));\n  }\n  remove(poolType, path) {\n    return this.http.delete(getUrl(path, poolType)).pipe(tap(() => {\n      delete this.cachedItem[path];\n    }), catchError(error => {\n      this.notifications.error(error?.error?.message);\n      return throwError(error);\n    }));\n  }\n  static {\n    this.ɵfac = function EntityDomainPoolService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || EntityDomainPoolService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.SwuiNotificationsService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: EntityDomainPoolService,\n      factory: EntityDomainPoolService.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["of", "throwError", "catchError", "map", "switchMap", "tap", "DOMAIN_TYPES", "toDomainPool", "API_ENDPOINT", "getUrl", "path", "poolType", "id", "EntityDomainPoolService", "constructor", "http", "notifications", "cachedItem", "get", "force", "params", "inherited", "pipe", "undefined", "static", "data", "set", "put", "error", "message", "remove", "delete", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "SwuiNotificationsService", "factory", "ɵfac"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-domains/manage-domains/entity-domain-pool.service.ts"], "sourcesContent": ["import { HttpClient, HttpErrorResponse } from '@angular/common/http';\nimport { Injectable } from '@angular/core';\nimport { SwuiNotificationsService } from '@skywind-group/lib-swui';\nimport { of, throwError } from 'rxjs';\nimport { catchError, map, switchMap, tap } from 'rxjs/operators';\nimport { DOMAIN_TYPES, DomainPool, DomainType } from '../../../../../../common/models/domain.model';\nimport { toDomainPool } from '../../../../../domains-management/domains-pool/domains-pool.service';\nimport { API_ENDPOINT } from '../../../../../../app.constants';\n\nfunction getUrl(path: string, poolType: DomainType, id?: string): string {\n  return `${API_ENDPOINT}/entities/${path}/domain-pools/${id ? `${id}/` : ''}${poolType}`;\n}\n\n@Injectable()\nexport class EntityDomainPoolService {\n  private readonly cachedItem: Record<string, DomainPool> = {};\n\n  constructor(private readonly http: HttpClient,\n              private readonly notifications: SwuiNotificationsService) {\n  }\n\n  get(path: string, poolType: DomainType, force = false) {\n    if (!force) {\n      if (this.cachedItem[path]) {\n        return of(this.cachedItem[path]);\n      }\n    }\n    return this.http.get(getUrl(path, poolType), { params: { inherited: true } }).pipe(\n      catchError(() => of(undefined)),\n      map(toDomainPool(DOMAIN_TYPES.static)),\n      tap((data) => {\n        this.cachedItem[path] = data;\n      }),\n    );\n  }\n\n  set(id: string, poolType: DomainType, path: string) {\n    return this.http.put(getUrl(path, poolType, id), {}).pipe(\n      catchError((error: HttpErrorResponse) => {\n        this.notifications.error(error?.error?.message);\n        return throwError(error);\n      }),\n      switchMap(() => this.get(path, poolType, true)),\n    );\n  }\n\n  remove(poolType: DomainType, path: string) {\n    return this.http.delete(getUrl(path, poolType)).pipe(\n      tap(() => {\n        delete this.cachedItem[path];\n      }),\n      catchError((error: HttpErrorResponse) => {\n        this.notifications.error(error?.error?.message);\n        return throwError(error);\n      }),\n    );\n  }\n}\n"], "mappings": "AAGA,SAASA,EAAE,EAAEC,UAAU,QAAQ,MAAM;AACrC,SAASC,UAAU,EAAEC,GAAG,EAAEC,SAAS,EAAEC,GAAG,QAAQ,gBAAgB;AAChE,SAASC,YAAY,QAAgC,8CAA8C;AACnG,SAASC,YAAY,QAAQ,qEAAqE;AAClG,SAASC,YAAY,QAAQ,iCAAiC;;;;AAE9D,SAASC,MAAMA,CAACC,IAAY,EAAEC,QAAoB,EAAEC,EAAW;EAC7D,OAAO,GAAGJ,YAAY,aAAaE,IAAI,iBAAiBE,EAAE,GAAG,GAAGA,EAAE,GAAG,GAAG,EAAE,GAAGD,QAAQ,EAAE;AACzF;AAGA,OAAM,MAAOE,uBAAuB;EAGlCC,YAA6BC,IAAgB,EAChBC,aAAuC;IADvC,KAAAD,IAAI,GAAJA,IAAI;IACJ,KAAAC,aAAa,GAAbA,aAAa;IAHzB,KAAAC,UAAU,GAA+B,EAAE;EAI5D;EAEAC,GAAGA,CAACR,IAAY,EAAEC,QAAoB,EAAEQ,KAAK,GAAG,KAAK;IACnD,IAAI,CAACA,KAAK,EAAE;MACV,IAAI,IAAI,CAACF,UAAU,CAACP,IAAI,CAAC,EAAE;QACzB,OAAOV,EAAE,CAAC,IAAI,CAACiB,UAAU,CAACP,IAAI,CAAC,CAAC;MAClC;IACF;IACA,OAAO,IAAI,CAACK,IAAI,CAACG,GAAG,CAACT,MAAM,CAACC,IAAI,EAAEC,QAAQ,CAAC,EAAE;MAAES,MAAM,EAAE;QAAEC,SAAS,EAAE;MAAI;IAAE,CAAE,CAAC,CAACC,IAAI,CAChFpB,UAAU,CAAC,MAAMF,EAAE,CAACuB,SAAS,CAAC,CAAC,EAC/BpB,GAAG,CAACI,YAAY,CAACD,YAAY,CAACkB,MAAM,CAAC,CAAC,EACtCnB,GAAG,CAAEoB,IAAI,IAAI;MACX,IAAI,CAACR,UAAU,CAACP,IAAI,CAAC,GAAGe,IAAI;IAC9B,CAAC,CAAC,CACH;EACH;EAEAC,GAAGA,CAACd,EAAU,EAAED,QAAoB,EAAED,IAAY;IAChD,OAAO,IAAI,CAACK,IAAI,CAACY,GAAG,CAAClB,MAAM,CAACC,IAAI,EAAEC,QAAQ,EAAEC,EAAE,CAAC,EAAE,EAAE,CAAC,CAACU,IAAI,CACvDpB,UAAU,CAAE0B,KAAwB,IAAI;MACtC,IAAI,CAACZ,aAAa,CAACY,KAAK,CAACA,KAAK,EAAEA,KAAK,EAAEC,OAAO,CAAC;MAC/C,OAAO5B,UAAU,CAAC2B,KAAK,CAAC;IAC1B,CAAC,CAAC,EACFxB,SAAS,CAAC,MAAM,IAAI,CAACc,GAAG,CAACR,IAAI,EAAEC,QAAQ,EAAE,IAAI,CAAC,CAAC,CAChD;EACH;EAEAmB,MAAMA,CAACnB,QAAoB,EAAED,IAAY;IACvC,OAAO,IAAI,CAACK,IAAI,CAACgB,MAAM,CAACtB,MAAM,CAACC,IAAI,EAAEC,QAAQ,CAAC,CAAC,CAACW,IAAI,CAClDjB,GAAG,CAAC,MAAK;MACP,OAAO,IAAI,CAACY,UAAU,CAACP,IAAI,CAAC;IAC9B,CAAC,CAAC,EACFR,UAAU,CAAE0B,KAAwB,IAAI;MACtC,IAAI,CAACZ,aAAa,CAACY,KAAK,CAACA,KAAK,EAAEA,KAAK,EAAEC,OAAO,CAAC;MAC/C,OAAO5B,UAAU,CAAC2B,KAAK,CAAC;IAC1B,CAAC,CAAC,CACH;EACH;;;uCA1CWf,uBAAuB,EAAAmB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,wBAAA;IAAA;EAAA;;;aAAvBxB,uBAAuB;MAAAyB,OAAA,EAAvBzB,uBAAuB,CAAA0B;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}