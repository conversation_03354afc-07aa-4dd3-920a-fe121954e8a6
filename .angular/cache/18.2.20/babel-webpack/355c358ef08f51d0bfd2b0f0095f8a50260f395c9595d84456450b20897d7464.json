{"ast": null, "code": "var prefix = ['webkit', 'moz', 'ms', 'o'];\nexport var requestAnimationFrame = function () {\n  for (var i = 0, limit = prefix.length; i < limit && !window.requestAnimationFrame; ++i) {\n    window.requestAnimationFrame = window[prefix[i] + 'RequestAnimationFrame'];\n  }\n  if (!window.requestAnimationFrame) {\n    var lastTime = 0;\n    window.requestAnimationFrame = function (callback) {\n      var now = new Date().getTime();\n      var ttc = Math.max(0, 16 - now - lastTime);\n      var timer = window.setTimeout(function () {\n        return callback(now + ttc);\n      }, ttc);\n      lastTime = now + ttc;\n      return timer;\n    };\n  }\n  return window.requestAnimationFrame.bind(window);\n}();\nexport var cancelAnimationFrame = function () {\n  for (var i = 0, limit = prefix.length; i < limit && !window.cancelAnimationFrame; ++i) {\n    window.cancelAnimationFrame = window[prefix[i] + 'CancelAnimationFrame'] || window[prefix[i] + 'CancelRequestAnimationFrame'];\n  }\n  if (!window.cancelAnimationFrame) {\n    window.cancelAnimationFrame = function (timer) {\n      window.clearTimeout(timer);\n    };\n  }\n  return window.cancelAnimationFrame.bind(window);\n}();", "map": {"version": 3, "names": ["prefix", "requestAnimationFrame", "i", "limit", "length", "window", "lastTime", "callback", "now", "Date", "getTime", "ttc", "Math", "max", "timer", "setTimeout", "bind", "cancelAnimationFrame", "clearTimeout"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/node_modules/animation-frame-polyfill/lib/animation-frame-polyfill.module.js"], "sourcesContent": ["var prefix = ['webkit', 'moz', 'ms', 'o'];\nexport var requestAnimationFrame = function () {\n  for (var i = 0, limit = prefix.length; i < limit && !window.requestAnimationFrame; ++i) {\n    window.requestAnimationFrame = window[prefix[i] + 'RequestAnimationFrame'];\n  }\n\n  if (!window.requestAnimationFrame) {\n    var lastTime = 0;\n\n    window.requestAnimationFrame = function (callback) {\n      var now = new Date().getTime();\n      var ttc = Math.max(0, 16 - now - lastTime);\n      var timer = window.setTimeout(function () {\n        return callback(now + ttc);\n      }, ttc);\n      lastTime = now + ttc;\n      return timer;\n    };\n  }\n\n  return window.requestAnimationFrame.bind(window);\n}();\nexport var cancelAnimationFrame = function () {\n  for (var i = 0, limit = prefix.length; i < limit && !window.cancelAnimationFrame; ++i) {\n    window.cancelAnimationFrame = window[prefix[i] + 'CancelAnimationFrame'] || window[prefix[i] + 'CancelRequestAnimationFrame'];\n  }\n\n  if (!window.cancelAnimationFrame) {\n    window.cancelAnimationFrame = function (timer) {\n      window.clearTimeout(timer);\n    };\n  }\n\n  return window.cancelAnimationFrame.bind(window);\n}();\n"], "mappings": "AAAA,IAAIA,MAAM,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,CAAC;AACzC,OAAO,IAAIC,qBAAqB,GAAG,YAAY;EAC7C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,KAAK,GAAGH,MAAM,CAACI,MAAM,EAAEF,CAAC,GAAGC,KAAK,IAAI,CAACE,MAAM,CAACJ,qBAAqB,EAAE,EAAEC,CAAC,EAAE;IACtFG,MAAM,CAACJ,qBAAqB,GAAGI,MAAM,CAACL,MAAM,CAACE,CAAC,CAAC,GAAG,uBAAuB,CAAC;EAC5E;EAEA,IAAI,CAACG,MAAM,CAACJ,qBAAqB,EAAE;IACjC,IAAIK,QAAQ,GAAG,CAAC;IAEhBD,MAAM,CAACJ,qBAAqB,GAAG,UAAUM,QAAQ,EAAE;MACjD,IAAIC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;MAC9B,IAAIC,GAAG,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,EAAE,GAAGL,GAAG,GAAGF,QAAQ,CAAC;MAC1C,IAAIQ,KAAK,GAAGT,MAAM,CAACU,UAAU,CAAC,YAAY;QACxC,OAAOR,QAAQ,CAACC,GAAG,GAAGG,GAAG,CAAC;MAC5B,CAAC,EAAEA,GAAG,CAAC;MACPL,QAAQ,GAAGE,GAAG,GAAGG,GAAG;MACpB,OAAOG,KAAK;IACd,CAAC;EACH;EAEA,OAAOT,MAAM,CAACJ,qBAAqB,CAACe,IAAI,CAACX,MAAM,CAAC;AAClD,CAAC,CAAC,CAAC;AACH,OAAO,IAAIY,oBAAoB,GAAG,YAAY;EAC5C,KAAK,IAAIf,CAAC,GAAG,CAAC,EAAEC,KAAK,GAAGH,MAAM,CAACI,MAAM,EAAEF,CAAC,GAAGC,KAAK,IAAI,CAACE,MAAM,CAACY,oBAAoB,EAAE,EAAEf,CAAC,EAAE;IACrFG,MAAM,CAACY,oBAAoB,GAAGZ,MAAM,CAACL,MAAM,CAACE,CAAC,CAAC,GAAG,sBAAsB,CAAC,IAAIG,MAAM,CAACL,MAAM,CAACE,CAAC,CAAC,GAAG,6BAA6B,CAAC;EAC/H;EAEA,IAAI,CAACG,MAAM,CAACY,oBAAoB,EAAE;IAChCZ,MAAM,CAACY,oBAAoB,GAAG,UAAUH,KAAK,EAAE;MAC7CT,MAAM,CAACa,YAAY,CAACJ,KAAK,CAAC;IAC5B,CAAC;EACH;EAEA,OAAOT,MAAM,CAACY,oBAAoB,CAACD,IAAI,CAACX,MAAM,CAAC;AACjD,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}