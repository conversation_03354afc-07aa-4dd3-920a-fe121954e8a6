{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { FormControl, FormGroup } from '@angular/forms';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { AVAILABLE_LANGUAGES } from '../../../../lobby.model';\nimport slugify from 'slugify';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"@skywind-group/lib-swui\";\nimport * as i4 from \"@angular/material/input\";\nimport * as i5 from \"@angular/material/form-field\";\nimport * as i6 from \"../../../../../../common/directives/trim-input-value/trim-input-value.component\";\nimport * as i7 from \"./lobby-menu-items-info/lobby-menu-items-info.component\";\nimport * as i8 from \"@ngx-translate/core\";\nfunction LobbyMenuItemsGeneralComponent_mat_form_field_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-form-field\", 5)(1, \"mat-label\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 1, \"LOBBY.FORM.slug\"));\n  }\n}\nexport class LobbyMenuItemsGeneralComponent {\n  set setSubmitted(val) {\n    this.submitted = !!val;\n    this.form.markAllAsTouched();\n  }\n  set setMenuItem(value) {\n    this.menuItem = value;\n    if (value) {\n      const {\n        slug,\n        translations,\n        title,\n        description,\n        icon\n      } = value;\n      this.hasSlug = slug !== undefined;\n      this.form.patchValue({\n        slug,\n        translations: {\n          en: {\n            title: title ?? '',\n            description: description ?? '',\n            icon: icon ?? ''\n          },\n          ...(translations || {})\n        }\n      }, {\n        emitEvent: false\n      });\n    }\n  }\n  constructor() {\n    this.settingsChange = new EventEmitter();\n    this.validStatusChange = new EventEmitter();\n    this.isChild = false;\n    this.isRibbonsEnabled = false;\n    this.availableLanguages = AVAILABLE_LANGUAGES;\n    this.hasSlug = false;\n    this.destroyed$ = new Subject();\n    this.form = new FormGroup({\n      slug: new FormControl(),\n      translations: new FormControl()\n    });\n  }\n  ngOnInit() {\n    this.form.valueChanges.pipe(takeUntil(this.destroyed$)).subscribe(() => {\n      const value = this.form.getRawValue() ?? {};\n      const translations = value.translations ?? {};\n      if (!this.hasSlug && !this.isChild && this.menuItemIndex !== undefined && Boolean(translations?.en?.title)) {\n        if (!this.form.get('slug').dirty) {\n          const slug = slugify(translations?.en?.title, {\n            replacement: '_',\n            lower: true\n          }) + '_' + this.menuItemIndex;\n          this.form.patchValue({\n            slug\n          }, {\n            emitEvent: false\n          });\n          value.slug = slug;\n        }\n      }\n      this.settingsChange.emit({\n        slug: value.slug,\n        title: '',\n        ...(translations.en ?? {}),\n        translations\n      });\n    });\n    this.form.statusChanges.pipe(takeUntil(this.destroyed$)).subscribe(result => {\n      this.validStatusChange.emit(result === 'VALID');\n    });\n  }\n  ngOnDestroy() {\n    this.destroyed$.next();\n    this.destroyed$.complete();\n  }\n  get slugControl() {\n    return this.form.get('slug');\n  }\n  get translationsControl() {\n    return this.form.get('translations');\n  }\n  static {\n    this.ɵfac = function LobbyMenuItemsGeneralComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LobbyMenuItemsGeneralComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LobbyMenuItemsGeneralComponent,\n      selectors: [[\"lobby-menu-items-general\"]],\n      inputs: {\n        menuItemIndex: \"menuItemIndex\",\n        isChild: \"isChild\",\n        isRibbonsEnabled: \"isRibbonsEnabled\",\n        themeKey: \"themeKey\",\n        setSubmitted: [0, \"submitted\", \"setSubmitted\"],\n        setMenuItem: [0, \"menuItem\", \"setMenuItem\"]\n      },\n      outputs: {\n        settingsChange: \"settingsChange\",\n        validStatusChange: \"validStatusChange\"\n      },\n      decls: 5,\n      vars: 9,\n      consts: [[\"childFormComponent\", \"\"], [3, \"formGroup\"], [\"appearance\", \"outline\", 4, \"ngIf\"], [3, \"submitted\", \"formControl\", \"languages\", \"component\"], [3, \"isIconEnabled\", \"isRibbonsEnabled\", \"submitted\"], [\"appearance\", \"outline\"], [\"matInput\", \"\", \"trimValue\", \"\", \"type\", \"text\", \"formControlName\", \"slug\"]],\n      template: function LobbyMenuItemsGeneralComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"form\", 1);\n          i0.ɵɵtemplate(1, LobbyMenuItemsGeneralComponent_mat_form_field_1_Template, 5, 3, \"mat-form-field\", 2);\n          i0.ɵɵelementStart(2, \"lib-swui-translations-manager\", 3);\n          i0.ɵɵelement(3, \"lobby-menu-items-info\", 4, 0);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          const childFormComponent_r1 = i0.ɵɵreference(4);\n          i0.ɵɵproperty(\"formGroup\", ctx.form);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isChild);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"submitted\", ctx.submitted)(\"formControl\", ctx.translationsControl)(\"languages\", ctx.availableLanguages)(\"component\", childFormComponent_r1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"isIconEnabled\", !ctx.isChild)(\"isRibbonsEnabled\", ctx.isRibbonsEnabled)(\"submitted\", ctx.submitted);\n        }\n      },\n      dependencies: [i1.NgIf, i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.FormControlDirective, i2.FormGroupDirective, i2.FormControlName, i3.SwuiTranslationsManagerComponent, i4.MatInput, i5.MatFormField, i5.MatLabel, i6.TrimInputValueComponent, i7.LobbyMenuItemsInfoComponent, i8.TranslatePipe],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "FormControl", "FormGroup", "Subject", "takeUntil", "AVAILABLE_LANGUAGES", "slugify", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpipeBind1", "LobbyMenuItemsGeneralComponent", "setSubmitted", "val", "submitted", "form", "mark<PERSON>llAsTouched", "setMenuItem", "value", "menuItem", "slug", "translations", "title", "description", "icon", "<PERSON><PERSON><PERSON>", "undefined", "patchValue", "en", "emitEvent", "constructor", "settingsChange", "validStatus<PERSON>hange", "<PERSON><PERSON><PERSON><PERSON>", "isRibbonsEnabled", "availableLanguages", "destroyed$", "ngOnInit", "valueChanges", "pipe", "subscribe", "getRawValue", "menuItemIndex", "Boolean", "get", "dirty", "replacement", "lower", "emit", "statusChanges", "result", "ngOnDestroy", "next", "complete", "slugControl", "translationsControl", "selectors", "inputs", "<PERSON><PERSON><PERSON>", "outputs", "decls", "vars", "consts", "template", "LobbyMenuItemsGeneralComponent_Template", "rf", "ctx", "ɵɵtemplate", "LobbyMenuItemsGeneralComponent_mat_form_field_1_Template", "ɵɵproperty", "childFormComponent_r1"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/lobby/lobby-form/lobby-menu-items/lobby-menu-items-setup/lobby-menu-items-general/lobby-menu-items-general.component.ts", "/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/lobby/lobby-form/lobby-menu-items/lobby-menu-items-setup/lobby-menu-items-general/lobby-menu-items-general.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, OnD<PERSON>roy, OnInit, Output } from '@angular/core';\nimport { FormControl, FormGroup } from '@angular/forms';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { AVAILABLE_LANGUAGES, LobbyMenuItem, LobbyMenuItemSettings } from '../../../../lobby.model';\nimport slugify from 'slugify';\n\n\n@Component({\n  selector: 'lobby-menu-items-general',\n  templateUrl: './lobby-menu-items-general.component.html'\n})\n\nexport class LobbyMenuItemsGeneralComponent implements OnInit, OnDestroy {\n  @Output() settingsChange = new EventEmitter<LobbyMenuItemSettings>();\n  @Output() validStatusChange = new EventEmitter<boolean>();\n\n  @Input() menuItemIndex?: number;\n  @Input() isChild = false;\n  @Input() isRibbonsEnabled = false;\n  @Input() themeKey?: string;\n\n  @Input('submitted')\n  set setSubmitted( val: boolean ) {\n    this.submitted = !!val;\n    this.form.markAllAsTouched();\n  }\n\n  @Input('menuItem')\n  set setMenuItem( value: LobbyMenuItem | undefined ) {\n    this.menuItem = value;\n    if (value) {\n      const { slug, translations, title, description, icon } = value;\n      this.hasSlug = slug !== undefined;\n      this.form.patchValue({\n        slug,\n        translations: {\n          en: {\n            title: title ?? '',\n            description: description ?? '',\n            icon: icon ?? ''\n          },\n          ...(translations || {})\n        }\n      }, { emitEvent: false });\n    }\n  }\n\n  readonly availableLanguages = AVAILABLE_LANGUAGES;\n  readonly form: FormGroup;\n\n  menuItem?: LobbyMenuItem;\n  submitted?: boolean;\n\n  private hasSlug = false;\n\n  private readonly destroyed$ = new Subject<void>();\n\n  constructor() {\n    this.form = new FormGroup({\n      slug: new FormControl(),\n      translations: new FormControl()\n    });\n  }\n\n  ngOnInit(): void {\n    this.form.valueChanges.pipe(\n      takeUntil(this.destroyed$)\n    ).subscribe(() => {\n      const value = this.form.getRawValue() ?? {};\n      const translations = value.translations ?? {};\n      if (!this.hasSlug && !this.isChild && this.menuItemIndex !== undefined && Boolean(translations?.en?.title)) {\n        if (!this.form.get('slug').dirty) {\n          const slug = slugify(translations?.en?.title, { replacement: '_', lower: true }) + '_' + this.menuItemIndex;\n          this.form.patchValue({ slug }, { emitEvent: false });\n          value.slug = slug;\n        }\n      }\n      this.settingsChange.emit({\n        slug: value.slug,\n        title: '',\n        ...(translations.en ?? {}),\n        translations\n      });\n    });\n\n    this.form.statusChanges.pipe(\n      takeUntil(this.destroyed$)\n    ).subscribe(result => {\n      this.validStatusChange.emit(result === 'VALID');\n    });\n  }\n\n  ngOnDestroy(): void {\n    this.destroyed$.next();\n    this.destroyed$.complete();\n  }\n\n  get slugControl(): FormControl {\n    return this.form.get('slug') as FormControl;\n  }\n\n  get translationsControl(): FormControl {\n    return this.form.get('translations') as FormControl;\n  }\n}\n", "<form [formGroup]=\"form\">\n  <mat-form-field appearance=\"outline\" *ngIf=\"!isChild\">\n    <mat-label>{{'LOBBY.FORM.slug' | translate}}</mat-label>\n    <input matInput trimValue type=\"text\" formControlName=\"slug\">\n  </mat-form-field>\n\n  <lib-swui-translations-manager\n    [submitted]=\"submitted\"\n    [formControl]=\"translationsControl\"\n    [languages]=\"availableLanguages\"\n    [component]=\"childFormComponent\">\n\n    <lobby-menu-items-info\n      #childFormComponent\n      [isIconEnabled]=\"!isChild\"\n      [isRibbonsEnabled]=\"isRibbonsEnabled\"\n      [submitted]=\"submitted\">\n    </lobby-menu-items-info>\n  </lib-swui-translations-manager>\n</form>\n"], "mappings": "AAAA,SAAoBA,YAAY,QAA0C,eAAe;AACzF,SAASC,WAAW,EAAEC,SAAS,QAAQ,gBAAgB;AACvD,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,mBAAmB,QAA8C,yBAAyB;AACnG,OAAOC,OAAO,MAAM,SAAS;;;;;;;;;;;;ICHzBC,EADF,CAAAC,cAAA,wBAAsD,gBACzC;IAAAD,EAAA,CAAAE,MAAA,GAAiC;;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACxDH,EAAA,CAAAI,SAAA,eAA6D;IAC/DJ,EAAA,CAAAG,YAAA,EAAiB;;;IAFJH,EAAA,CAAAK,SAAA,GAAiC;IAAjCL,EAAA,CAAAM,iBAAA,CAAAN,EAAA,CAAAO,WAAA,0BAAiC;;;ADWhD,OAAM,MAAOC,8BAA8B;EASzC,IACIC,YAAYA,CAAEC,GAAY;IAC5B,IAAI,CAACC,SAAS,GAAG,CAAC,CAACD,GAAG;IACtB,IAAI,CAACE,IAAI,CAACC,gBAAgB,EAAE;EAC9B;EAEA,IACIC,WAAWA,CAAEC,KAAgC;IAC/C,IAAI,CAACC,QAAQ,GAAGD,KAAK;IACrB,IAAIA,KAAK,EAAE;MACT,MAAM;QAAEE,IAAI;QAAEC,YAAY;QAAEC,KAAK;QAAEC,WAAW;QAAEC;MAAI,CAAE,GAAGN,KAAK;MAC9D,IAAI,CAACO,OAAO,GAAGL,IAAI,KAAKM,SAAS;MACjC,IAAI,CAACX,IAAI,CAACY,UAAU,CAAC;QACnBP,IAAI;QACJC,YAAY,EAAE;UACZO,EAAE,EAAE;YACFN,KAAK,EAAEA,KAAK,IAAI,EAAE;YAClBC,WAAW,EAAEA,WAAW,IAAI,EAAE;YAC9BC,IAAI,EAAEA,IAAI,IAAI;WACf;UACD,IAAIH,YAAY,IAAI,EAAE;;OAEzB,EAAE;QAAEQ,SAAS,EAAE;MAAK,CAAE,CAAC;IAC1B;EACF;EAYAC,YAAA;IA5CU,KAAAC,cAAc,GAAG,IAAInC,YAAY,EAAyB;IAC1D,KAAAoC,iBAAiB,GAAG,IAAIpC,YAAY,EAAW;IAGhD,KAAAqC,OAAO,GAAG,KAAK;IACf,KAAAC,gBAAgB,GAAG,KAAK;IA6BxB,KAAAC,kBAAkB,GAAGlC,mBAAmB;IAMzC,KAAAwB,OAAO,GAAG,KAAK;IAEN,KAAAW,UAAU,GAAG,IAAIrC,OAAO,EAAQ;IAG/C,IAAI,CAACgB,IAAI,GAAG,IAAIjB,SAAS,CAAC;MACxBsB,IAAI,EAAE,IAAIvB,WAAW,EAAE;MACvBwB,YAAY,EAAE,IAAIxB,WAAW;KAC9B,CAAC;EACJ;EAEAwC,QAAQA,CAAA;IACN,IAAI,CAACtB,IAAI,CAACuB,YAAY,CAACC,IAAI,CACzBvC,SAAS,CAAC,IAAI,CAACoC,UAAU,CAAC,CAC3B,CAACI,SAAS,CAAC,MAAK;MACf,MAAMtB,KAAK,GAAG,IAAI,CAACH,IAAI,CAAC0B,WAAW,EAAE,IAAI,EAAE;MAC3C,MAAMpB,YAAY,GAAGH,KAAK,CAACG,YAAY,IAAI,EAAE;MAC7C,IAAI,CAAC,IAAI,CAACI,OAAO,IAAI,CAAC,IAAI,CAACQ,OAAO,IAAI,IAAI,CAACS,aAAa,KAAKhB,SAAS,IAAIiB,OAAO,CAACtB,YAAY,EAAEO,EAAE,EAAEN,KAAK,CAAC,EAAE;QAC1G,IAAI,CAAC,IAAI,CAACP,IAAI,CAAC6B,GAAG,CAAC,MAAM,CAAC,CAACC,KAAK,EAAE;UAChC,MAAMzB,IAAI,GAAGlB,OAAO,CAACmB,YAAY,EAAEO,EAAE,EAAEN,KAAK,EAAE;YAAEwB,WAAW,EAAE,GAAG;YAAEC,KAAK,EAAE;UAAI,CAAE,CAAC,GAAG,GAAG,GAAG,IAAI,CAACL,aAAa;UAC3G,IAAI,CAAC3B,IAAI,CAACY,UAAU,CAAC;YAAEP;UAAI,CAAE,EAAE;YAAES,SAAS,EAAE;UAAK,CAAE,CAAC;UACpDX,KAAK,CAACE,IAAI,GAAGA,IAAI;QACnB;MACF;MACA,IAAI,CAACW,cAAc,CAACiB,IAAI,CAAC;QACvB5B,IAAI,EAAEF,KAAK,CAACE,IAAI;QAChBE,KAAK,EAAE,EAAE;QACT,IAAID,YAAY,CAACO,EAAE,IAAI,EAAE,CAAC;QAC1BP;OACD,CAAC;IACJ,CAAC,CAAC;IAEF,IAAI,CAACN,IAAI,CAACkC,aAAa,CAACV,IAAI,CAC1BvC,SAAS,CAAC,IAAI,CAACoC,UAAU,CAAC,CAC3B,CAACI,SAAS,CAACU,MAAM,IAAG;MACnB,IAAI,CAAClB,iBAAiB,CAACgB,IAAI,CAACE,MAAM,KAAK,OAAO,CAAC;IACjD,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACf,UAAU,CAACgB,IAAI,EAAE;IACtB,IAAI,CAAChB,UAAU,CAACiB,QAAQ,EAAE;EAC5B;EAEA,IAAIC,WAAWA,CAAA;IACb,OAAO,IAAI,CAACvC,IAAI,CAAC6B,GAAG,CAAC,MAAM,CAAgB;EAC7C;EAEA,IAAIW,mBAAmBA,CAAA;IACrB,OAAO,IAAI,CAACxC,IAAI,CAAC6B,GAAG,CAAC,cAAc,CAAgB;EACrD;;;uCA3FWjC,8BAA8B;IAAA;EAAA;;;YAA9BA,8BAA8B;MAAA6C,SAAA;MAAAC,MAAA;QAAAf,aAAA;QAAAT,OAAA;QAAAC,gBAAA;QAAAwB,QAAA;QAAA9C,YAAA;QAAAK,WAAA;MAAA;MAAA0C,OAAA;QAAA5B,cAAA;QAAAC,iBAAA;MAAA;MAAA4B,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCb3C9D,EAAA,CAAAC,cAAA,cAAyB;UACvBD,EAAA,CAAAgE,UAAA,IAAAC,wDAAA,4BAAsD;UAKtDjE,EAAA,CAAAC,cAAA,uCAImC;UAEjCD,EAAA,CAAAI,SAAA,kCAKwB;UAE5BJ,EADE,CAAAG,YAAA,EAAgC,EAC3B;;;;UAnBDH,EAAA,CAAAkE,UAAA,cAAAH,GAAA,CAAAnD,IAAA,CAAkB;UACgBZ,EAAA,CAAAK,SAAA,EAAc;UAAdL,EAAA,CAAAkE,UAAA,UAAAH,GAAA,CAAAjC,OAAA,CAAc;UAMlD9B,EAAA,CAAAK,SAAA,EAAuB;UAGvBL,EAHA,CAAAkE,UAAA,cAAAH,GAAA,CAAApD,SAAA,CAAuB,gBAAAoD,GAAA,CAAAX,mBAAA,CACY,cAAAW,GAAA,CAAA/B,kBAAA,CACH,cAAAmC,qBAAA,CACA;UAI9BnE,EAAA,CAAAK,SAAA,EAA0B;UAE1BL,EAFA,CAAAkE,UAAA,mBAAAH,GAAA,CAAAjC,OAAA,CAA0B,qBAAAiC,GAAA,CAAAhC,gBAAA,CACW,cAAAgC,GAAA,CAAApD,SAAA,CACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}