{"ast": null, "code": "import { BehaviorSubject, iif, of, Subject } from 'rxjs';\nimport { mergeMap, takeUntil } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../common/services/cdn.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/card\";\nimport * as i5 from \"@angular/material/chips\";\nimport * as i6 from \"@angular/material/tooltip\";\nconst _c0 = [\"game-store-category-item\", \"\"];\nconst _c1 = a0 => [\"/pages/marketing-materials/game-details\", a0];\nfunction GameStoreCategoryItemComponent_img_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 7);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"src\", ctx_r0.image, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r0.game == null ? null : ctx_r0.game.title);\n  }\n}\nexport class GameStoreCategoryItemComponent {\n  set setItem(value) {\n    const game = value ? value.game : null;\n    this.game = game;\n    this.game$.next(game);\n  }\n  constructor(cdnService, cdRef) {\n    this.cdnService = cdnService;\n    this.cdRef = cdRef;\n    this.game$ = new BehaviorSubject(null);\n    this.destroyed$ = new Subject();\n  }\n  ngOnInit() {\n    this.game$.pipe(mergeMap(game => iif(() => game === null, of(null), this.cdnService.getImageUrl(game.code))), takeUntil(this.destroyed$)).subscribe(url => {\n      this.image = url;\n      this.cdRef.markForCheck();\n    });\n  }\n  ngOnDestroy() {\n    this.destroyed$.next();\n    this.destroyed$.complete();\n  }\n  static {\n    this.ɵfac = function GameStoreCategoryItemComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || GameStoreCategoryItemComponent)(i0.ɵɵdirectiveInject(i1.CdnService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: GameStoreCategoryItemComponent,\n      selectors: [[\"\", \"game-store-category-item\", \"\"]],\n      inputs: {\n        setItem: [0, \"game-store-category-item\", \"setItem\"]\n      },\n      attrs: _c0,\n      decls: 12,\n      vars: 9,\n      consts: [[1, \"mat-elevation-z0\", \"thumb\", 3, \"routerLink\"], [\"mat-card-image\", \"\", 1, \"thumb__image\"], [1, \"thumb__image-wrapper\"], [3, \"src\", \"alt\", 4, \"ngIf\"], [1, \"thumb__title\"], [1, \"thumb__type\", 3, \"matTooltip\"], [\"color\", \"accent\", \"selected\", \"\", 3, \"matTooltip\"], [3, \"src\", \"alt\"]],\n      template: function GameStoreCategoryItemComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"mat-card\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵtemplate(3, GameStoreCategoryItemComponent_img_3_Template, 1, 2, \"img\", 3);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"mat-card-title\", 4);\n          i0.ɵɵtext(5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"mat-card-content\")(7, \"mat-chip-set\")(8, \"mat-chip\", 5);\n          i0.ɵɵtext(9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"mat-chip\", 6);\n          i0.ɵɵtext(11);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(7, _c1, ctx.game == null ? null : ctx.game.code));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.image);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.game == null ? null : ctx.game.title, \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵpropertyInterpolate(\"matTooltip\", \"Game type: \" + (ctx.game == null ? null : ctx.game.type));\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate(ctx.game == null ? null : ctx.game.type);\n          i0.ɵɵadvance();\n          i0.ɵɵpropertyInterpolate(\"matTooltip\", \"Game provider: \" + (ctx.game == null ? null : ctx.game.gameProvider == null ? null : ctx.game.gameProvider.title));\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", ctx.game == null ? null : ctx.game.gameProvider == null ? null : ctx.game.gameProvider.code, \" \");\n        }\n      },\n      dependencies: [i2.NgIf, i3.RouterLink, i4.MatCard, i4.MatCardContent, i4.MatCardImage, i4.MatCardTitle, i5.MatChip, i5.MatChipSet, i6.MatTooltip],\n      styles: [\".game-thumb__img-wrapper[_ngcontent-%COMP%] {\\n  position: relative;\\n  padding-top: 62.75%;\\n  border-top-right-radius: 3px;\\n  border-top-left-radius: 3px;\\n  overflow: hidden;\\n}\\n.game-thumb__image[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 100%;\\n  height: 100%;\\n  overflow: hidden;\\n  background-color: #393939;\\n  background-image: url(\\\"data:image/png;base64,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\\\");\\n  background-repeat: no-repeat;\\n  background-size: contain;\\n  background-position: center;\\n}\\n.game-thumb__image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  max-width: 100%;\\n}\\n.game-thumb__flag[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 7px;\\n  right: -20px;\\n  width: 75px;\\n  font-size: 14px;\\n  color: #fefefe;\\n  text-align: center;\\n  text-transform: uppercase;\\n  transform: rotate(45deg);\\n}\\n.game-thumb__flag--hot[_ngcontent-%COMP%] {\\n  background-color: #ff3600;\\n}\\n.game-thumb__flag--new[_ngcontent-%COMP%] {\\n  background-color: #ff00ae;\\n}\\n.game-thumb__caption[_ngcontent-%COMP%] {\\n  position: relative;\\n  height: 80px;\\n  box-sizing: border-box;\\n  padding: 10px 12px;\\n  border: 1px solid #ddd;\\n  border-top: none;\\n  border-bottom-left-radius: 3px;\\n  border-bottom-right-radius: 3px;\\n}\\n.game-thumb__bottom[_ngcontent-%COMP%] {\\n  position: relative;\\n  bottom: -4px;\\n  height: 20px;\\n}\\n.game-thumb__title[_ngcontent-%COMP%] {\\n  display: block;\\n  height: 14px;\\n  overflow: hidden;\\n  margin: 0;\\n  font-weight: 500;\\n  font-size: 14px;\\n  line-height: 14px;\\n  color: #000;\\n  white-space: nowrap;\\n  text-overflow: ellipsis;\\n}\\n.game-thumb__mark[_ngcontent-%COMP%] {\\n  position: absolute !important;\\n  left: 0;\\n  bottom: 0;\\n}\\n.game-thumb__rate[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 0;\\n  bottom: 0;\\n}\\n\\n.mark[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 46px;\\n  height: 20px;\\n  padding: 0;\\n  box-sizing: border-box;\\n  font-size: 12px;\\n  line-height: 12px;\\n  color: #fefefe;\\n  text-transform: uppercase;\\n  text-align: center;\\n}\\n.mark__inner[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  z-index: 5;\\n  display: block;\\n  padding: 4px 0;\\n  width: 100%;\\n  background-color: #fafafa;\\n}\\n.mark__collapse[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: calc(100% - 1px);\\n  display: inline-block;\\n  padding: 3px 10px;\\n  color: #333;\\n  white-space: nowrap;\\n  background-color: #fff;\\n  border: 1px solid #ddd;\\n  border-left: none;\\n  transform: translateX(-100%);\\n  transition: transform 0.2s linear, opacity 0.6s ease-in-out;\\n  opacity: 0;\\n  z-index: 1;\\n  visibility: hidden;\\n}\\n.mark[_ngcontent-%COMP%]:hover {\\n  cursor: pointer;\\n}\\n.mark[_ngcontent-%COMP%]:hover   .mark__collapse[_ngcontent-%COMP%] {\\n  display: block;\\n  transform: translateX(0);\\n  opacity: 1;\\n  visibility: visible;\\n}\\n.mark--mgs[_ngcontent-%COMP%]   .mark__inner[_ngcontent-%COMP%] {\\n  background-color: #06a93c;\\n}\\n.mark--mgs[_ngcontent-%COMP%]   .mark__collapse[_ngcontent-%COMP%] {\\n  border-color: #06a93c;\\n}\\n.mark--sw[_ngcontent-%COMP%]   .mark__inner[_ngcontent-%COMP%] {\\n  background-color: #7fc100;\\n}\\n.mark--sw[_ngcontent-%COMP%]   .mark__collapse[_ngcontent-%COMP%] {\\n  border-color: #7fc100;\\n}\\n.mark--bsg[_ngcontent-%COMP%]   .mark__inner[_ngcontent-%COMP%] {\\n  background-color: #d8a501;\\n}\\n.mark--bsg[_ngcontent-%COMP%]   .mark__collapse[_ngcontent-%COMP%] {\\n  border-color: #d8a501;\\n}\\n.mark--lx[_ngcontent-%COMP%]   .mark__inner[_ngcontent-%COMP%] {\\n  background-color: #b201d8;\\n}\\n.mark--lx[_ngcontent-%COMP%]   .mark__collapse[_ngcontent-%COMP%] {\\n  border-color: #b201d8;\\n}\\n\\n.thumb[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n}\\n.thumb__title[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  margin-bottom: 16px;\\n}\\n.thumb__image[_ngcontent-%COMP%] {\\n  overflow: hidden;\\n}\\n.thumb__image-wrapper[_ngcontent-%COMP%] {\\n  position: relative;\\n  padding-top: 62.616822429%;\\n  background: url(\\\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAUAAAADwCAMAAABG801ZAAAAaVBMVEU5OTl+fX03NzeBf381NTWCgYGEg4MyMjI8PDx7enqGhYU/Pz9KSkp4d3dxcHB1dXVEREROTk5dXV1ubW1qamovLy9hYGBnZ2dkZGRYV1dRUVFBQUFHR0eIh4dbWlpUVFRzcnKKiYknJyfoGzpqAAANb0lEQVR42uzBgQAAAACAoP2pF6kCAAAAAAAAAAAAAAAAAACYXTo2AQAEgiAocqD9Vyz/HWhkMBNeuBwAAAAAAAD8aLbBY7xklUh4fbuUft+uRcLDnpntOAoDUZTyhg3YZt+X9Pz/R46roBklClLPa5MjJSgvPBzVpa7Jf3EI1Hmx9tXD15uORfThR94IptMpz7aydh7UsqjHkH6ehD9Rt1+Ynsey9gYAeEByqcygPyH+gcBIp2HuupBaA1wpKTkHRH25XHxG8NocTpfW6YyPPA/JkqigDvVxIOSXGT8Cn2An2FQEhrZ7WEqtDPCTXWACpf4IfFUoEJ1n49rW1cMGTTR58A8SSFKhzz8Cnzoeo9TmFFpO5iRyCMQfmOHDYfhUmbh5k2GIYIJC22BoseNZQ+uCfOHGRdRuM1Hc17WFXaDf7i7wO7MRLouh7GtnQSWJfA1tEBjUmUfdlUMxz4OjIeRQ3rhLU2oZEuk8G9raQ5BEIT0zuwOEdd06zimLA1ELGGWpWnG7JsgOKLMY2mJoO9wWwDGmR2Txiy5hHBW3VV9u2TzpiAkkbkajOASBvb5dkUF1RJSGVRvkOcuTEFoO/CWzKjiy3tX9umW5Fg2+fyFbLG4yuwus5zud5vaFQegp2zC0NHEU1VPdDnAKbTkGdTHuZ+KwFce5kyTQjfdpgmdo82ILHc9ZA3z3d0IxTpZFGV+FbTFmk2YijlHgkyYh0o7jzEp7oyodJOhpLrZyD606zT0lF7xzQd6WTVHcUGbf3kqve4Z5l95FoBBRVlYWMJ3IO3+S+34I6s41cyGQMT06JfE05/J7NMFgI12dOQ9mb+GJ3dIIc44LY+dCYJRXCwpc7D3OIkwIXXJctdegDl98PypJ4tUI4hbqFgXAF9hu0WNYLEYv1eHvcgKVCV25yPKUxQ39a/TCebtIiJ4EJryMbiGwES2n+Tv68nuBHIx3Vd31bbkOWzA5pdidY0Ig7CAWJUhUDv3EbpDhILAja9hcwFq4gp8Y+6i6FrtMnmqtzxopBAptmsIB9pibvJBBgZwESgXVOtSGXws83r3QwBpjrfWkkkbymMjmTzN3hkqkH0T8+08jf9k7s93GdRgMWxIleV9iJ96X6fs/5BEp2U7bBKc9y43tH4MBpikG6AdSpEiK5ZpPzNbwgrrwozYjPALdWUmSoI+fAdr7XBiO42gvdWXdGIssYqM8n0tGCRFrOMWbY8cSA7DL0ARl0Az6DgVlcQ4pSeJfikQ4UcT4WQyZ0kFZNWngTtSli6x3H9kOOfC4wlNfBa13v3OXBrOmy4dinm6Pukqx5yY2Y/wusk1H2Lj29mWWlCbqTF1/5MqWAdg/mEKAU6TvXpsZCEItMdzv4EVRPsQdXZDTJcuShPAIdN/Q1qWdJGr7CnPCUk6QpE3sHZcgZr4zG9Ho6lh/+I8Ay6Fh5d8/58p+1BuUWJkuM6RIIFFSWUnUl4ugcF/JigMPyxhIMfuDh9gyw0dUM7TAsYI7f5oa0ppqfr7BaEwyjou5vT0MS+psSmkt7234DlU5HNiLue6TkRK3mwFYCQQYVqA3vrtgE/fRuYvZdjiNaxtZWnucERtCJW6ePjDAqMTURanGAEwlApQN6PdF/62U4BtFaJRFZyyyLtOESaz07xkQc6MelX9ggOA3gcCfsvQ++kUhQHb7u0vESlJThQY8ms+Ki458u67KNEsCGTqC4dEBUvasxjT66DNqXAYThx83oXaWJDDebUi2kzHJYAVYHxigx724lJJJtRQQZ0pIbItz+H1bwEFcZ48iP78FQlDa0xz4DPQ4jxqJBZSgetQBhZO0APjXzT3j3HktFI3KHDmIYE31JtWa19HPWxYc/mV/GZCjPzECGLQHTqWxptoyJWwOYgFWA4d/anuuwMrJtAmgFFl3bIC6C5Ac6d/UQjm6rQb+5ZYoRVkcGiDAkLJPAB8R8H84jWTHahzAvGFUmqiOfBsmS6mZfAY4/ZN2BmhvmKep6zmsAIeKSYG1nUPPeXDg/hQosQEcWQuc/97+/LkKhMia2CNcXPM4FWSBj2PPq3LuFckngAUA/317tE1w1FKNS+EBEMAiE2iB4nbsKQXOoc9CwVaNQaH5++9+F8rjRYZYmR1VlWsLcA7IAkV78DkZrv30yQLDLNb8faB9YZ1E6ybc/6FYd9fUr5qYBdgd+Qi0ABu2A5Tp8A4gtYJfAgS/lnItRE8O4ENKAhgfOAaTwJuSHaCocuDvDrq4oxmj7x95jVIOoGodwEZJhUEkPvBFzgEsFrESlKzpgb8LFAvLXsVUnEiQoSQDHtmsDUC4+40iC0yGowPkvC93gAEGzTf8AqFGMb2ICaC7RNFkYKjSGAC/0lfUhRdLfniA4DVCrkdg8jJo2toA5ikq6Ljm3z6NbkE4hubPMtNrV4ChslXp8vAAPYCJqRVgNhPA74Qehp+NMt+JcI9Htww7Kqnlx8ErSnqPE9T98QHqIlgtUC0FAnzFTwl3Sj584C/qinHXtt0QeQ7gnEphCAaP6OhRGN1tjSJCpfHXMMuf+dEczd7p3ccDqVvnc/P32ipYCGByO/7zawCsJwgLsMy/AQTI6ZB0krLqiRNHAdqbxWz/5QBGtwTHakR2gvfrwKMpURZgWPkv+eHHTobl5GuKtNqLPNi+35F3AB/YIFAHL6du0/VFFjqADQf+xX/zmvjtkkmsNYCGvpsoaLxq2NcM26UyjQ882bFB4nk6SuvCDcAXfnEl5NeHD48e7poXVcCSyQd4kVrnKTLHwY7D8zMCv/wjHRoNnwI0xKXc7W+LI4agP5dyHMOkA+29AJhIBDhW/SkAQh1agGzS8Nn+SinF98H95FZMix0OrCLg/BvAIQipq94cP4ZQGLnZyVwRtABP1RfepVK+nLpKloQJYsleJCpcx4IAjofuqm8CA0qgWNYBPPlvsSjJXkpKsVnj9xo2QIEARahu/AwAOY8xE5RiH0ugxK57x+/5WY5SVf4ldBvNMiTzbI8fhF3eSwDd+w66jGl/znZ+7yUke3j6C0B/IguUSXcSgDiHgcWAOncAQfMWs+ufaHPiHWD0ENRVT4884PsZYIAATXqCAK39JVKwH0mIdFg9f+uqC3X8rvpnFzYAVXDzbVNSRzfsFv9QUjw82EFx8Gz2rVh96K76E0Caw5AjTVLZwZbgh/bnspqO62eARUoAg0d+EoAYhXHxWsHp/BsqvH78AqBI+08Au0wIPByn/gz8POBFKRBgEmua7evkH4UEfiwcowS+94nbgAAG88G76hvAORXCAMwGC7AIxtBISWH0MxvMCoAd4I0JSeH5FA+vDcA2Q4BjmmuDgUN/SwOpwvUJ0g8wyrDensXxO2+EpDPw8F317S4c0MYcgmDk5Xbn2LI+knMvCgnnG4AmAOkVoFcrC/D4LTkS4BwGXSlcZcX+1L7f4+uPCVFm66trsWm1TufDMos1kMdyHVUWYHL8ltxTOUtgV331QtgGYeyDpHyI8fkHoqTlRk5Pvq1kNQB5Mei8RIBCLNFJAPJqRIBbB2jfdIwimHdtN0Z3862hx0j2lRwj70b/Hv+sxyDwOFUCnb08BUCOFWkDUMqyMwBJa4eIOwHJwSCrxDfZ7gUs7bFkQo3UAKE0cJEEsD5FOdXw6VMEKCoC8FYOpSYBeO4FbGFITvhKrp56C9BvM9dVPwvAYRkFvUiAuwbgTm+X0sJulhamRy9gt7GEaEoQIEumkwD0qK0phAkiGpF8Ov82mpzAEaKdp5PFvQYgc5MWCDCbT1GLAe7PSSjoOtFMbVcMbjcR6C94+N+s/eVrS662XfW08E6wfgcnE9ogFG6BUZDhPp25G/roy0Nrvus1wQ3gUDIEKMv4DAugcLJqDlxjneJpEARJtqTluploiHy7us09aIXdc1Gbb3sWIeh4oaawrI79RGQH6MfJH/m858nuJgoV7spKaQFMO3eG5JBH/jptTlq57aAIYKAQoGqOP9pmBVEZYuZL2jY9bcuK3AqEZCHnLobeJyPklh989Wy4d9QUHsNzZDFG1MAclfwuIQmfQqFzJ8m2Cc+YJBokf97LiIL7vVXYkhvD2ymOQDefVWeJvZgpFLJDvVjiRovHQokGWa2HpI3blOnojw/+kAoByukMO/C2y0jcunU6SGnzZyYsy13bnqdNLEmNc7ddPOS+p+9A5W0mQnaeX0pgALp9Op27llWpQYn323G0u7K+a907FlrnzsgijW8/ysCST7rTAPQ4CbQRLR7rsVRA69uMFqxgIa5Pa/BeOLdybQDX61yK8wDcEJL4826igfbAWOfebO9ZCJA9r3ETDupJ8uhXeoLp1o65nWNNVTqDlOFo9Mq5nQUqUZ8jj34LcJersNISN0RJleklyxa7VPClc8uQHfyp9a99m4RIyLWNVdJSQUK5Ffn3raDhmManmEr4N0elXc1IbacafXuhXFKid4ciO/TCnf/Iu59/k8vu3MYgWVJ3lwP/Ktasa8d66uDFcX6OoY7/7bA89Prj/0l8Fxx8A/elS5cuXbp06dKlS5cuXbp06dKlS5cuXbp06dKlS3+1BwcCAAAAAIL8rQe5AgAAAAAAAAAAAAAAAAAAYChc2sNZyURpzQAAAABJRU5ErkJggg==\\\");\\n  background-position: center;\\n  background-size: cover;\\n  overflow: hidden;\\n}\\n.thumb__image-wrapper[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  display: block;\\n  width: 100%;\\n}\\n.thumb__type[_ngcontent-%COMP%] {\\n  text-transform: capitalize;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      changeDetection: 0\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "iif", "of", "Subject", "mergeMap", "takeUntil", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r0", "image", "ɵɵsanitizeUrl", "game", "title", "GameStoreCategoryItemComponent", "setItem", "value", "game$", "next", "constructor", "cdnService", "cdRef", "destroyed$", "ngOnInit", "pipe", "getImageUrl", "code", "subscribe", "url", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ngOnDestroy", "complete", "ɵɵdirectiveInject", "i1", "CdnService", "ChangeDetectorRef", "selectors", "inputs", "attrs", "_c0", "decls", "vars", "consts", "template", "GameStoreCategoryItemComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtemplate", "GameStoreCategoryItemComponent_img_3_Template", "ɵɵelementEnd", "ɵɵtext", "ɵɵpureFunction1", "_c1", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵpropertyInterpolate", "type", "ɵɵtextInterpolate", "gameProvider"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/game-store/game-store-category/game-store-category-item/game-store-category-item.component.ts", "/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/game-store/game-store-category/game-store-category-item/game-store-category-item.component.html"], "sourcesContent": ["import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnDestroy, OnInit } from '@angular/core';\nimport { BehaviorSubject, iif, of, Subject } from 'rxjs';\nimport { mergeMap, takeUntil } from 'rxjs/operators';\n\nimport { CdnService } from '../../../../common/services/cdn.service';\nimport { EntityGame, Game } from '../../../../common/typings';\n\n\n@Component({\n  selector: '[game-store-category-item]',\n  templateUrl: 'game-store-category-item.component.html',\n  styleUrls: ['game-store-category-item.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class GameStoreCategoryItemComponent implements OnInit, OnDestroy {\n  image: string | null;\n  game: Game | null;\n\n  @Input('game-store-category-item')\n  set setItem( value: EntityGame | null ) {\n    const game = value ? value.game : null;\n    this.game = game;\n    this.game$.next(game);\n  }\n\n  private readonly game$ = new BehaviorSubject<Game | null>(null);\n  private readonly destroyed$ = new Subject<void>();\n\n  constructor(\n    private readonly cdnService: CdnService,\n    private readonly cdRef: ChangeDetectorRef\n  ) {\n  }\n\n  ngOnInit(): void {\n    this.game$.pipe(\n      mergeMap(game => iif(() => game === null, of(null), this.cdnService.getImageUrl(game.code))),\n      takeUntil(this.destroyed$)\n    ).subscribe(url => {\n      this.image = url;\n      this.cdRef.markForCheck();\n    });\n  }\n\n  ngOnDestroy(): void {\n    this.destroyed$.next();\n    this.destroyed$.complete();\n  }\n}\n", "<mat-card class=\"mat-elevation-z0 thumb\" [routerLink]=\"['/pages/marketing-materials/game-details', game?.code]\">\n  <div mat-card-image class=\"thumb__image\">\n    <div class=\"thumb__image-wrapper\">\n      <img *ngIf=\"image\" [src]=\"image\" [alt]=\"game?.title\">\n    </div>\n  </div>\n  <mat-card-title class=\"thumb__title\">\n    {{game?.title}}\n  </mat-card-title>\n  <mat-card-content>\n    <mat-chip-set>\n      <mat-chip matTooltip=\"{{'Game type: ' + game?.type}}\" class=\"thumb__type\">{{game?.type}}</mat-chip>\n      <mat-chip color=\"accent\" selected matTooltip=\"{{'Game provider: ' + game?.gameProvider?.title}}\">\n        {{game?.gameProvider?.code}}\n      </mat-chip>\n    </mat-chip-set>\n  </mat-card-content>\n</mat-card>\n"], "mappings": "AACA,SAASA,eAAe,EAAEC,GAAG,EAAEC,EAAE,EAAEC,OAAO,QAAQ,MAAM;AACxD,SAASC,QAAQ,EAAEC,SAAS,QAAQ,gBAAgB;;;;;;;;;;;;ICC9CC,EAAA,CAAAC,SAAA,aAAqD;;;;IAApBD,EAAd,CAAAE,UAAA,QAAAC,MAAA,CAAAC,KAAA,EAAAJ,EAAA,CAAAK,aAAA,CAAa,QAAAF,MAAA,CAAAG,IAAA,kBAAAH,MAAA,CAAAG,IAAA,CAAAC,KAAA,CAAoB;;;ADW1D,OAAM,MAAOC,8BAA8B;EAIzC,IACIC,OAAOA,CAAEC,KAAwB;IACnC,MAAMJ,IAAI,GAAGI,KAAK,GAAGA,KAAK,CAACJ,IAAI,GAAG,IAAI;IACtC,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACK,KAAK,CAACC,IAAI,CAACN,IAAI,CAAC;EACvB;EAKAO,YACmBC,UAAsB,EACtBC,KAAwB;IADxB,KAAAD,UAAU,GAAVA,UAAU;IACV,KAAAC,KAAK,GAALA,KAAK;IALP,KAAAJ,KAAK,GAAG,IAAIjB,eAAe,CAAc,IAAI,CAAC;IAC9C,KAAAsB,UAAU,GAAG,IAAInB,OAAO,EAAQ;EAMjD;EAEAoB,QAAQA,CAAA;IACN,IAAI,CAACN,KAAK,CAACO,IAAI,CACbpB,QAAQ,CAACQ,IAAI,IAAIX,GAAG,CAAC,MAAMW,IAAI,KAAK,IAAI,EAAEV,EAAE,CAAC,IAAI,CAAC,EAAE,IAAI,CAACkB,UAAU,CAACK,WAAW,CAACb,IAAI,CAACc,IAAI,CAAC,CAAC,CAAC,EAC5FrB,SAAS,CAAC,IAAI,CAACiB,UAAU,CAAC,CAC3B,CAACK,SAAS,CAACC,GAAG,IAAG;MAChB,IAAI,CAAClB,KAAK,GAAGkB,GAAG;MAChB,IAAI,CAACP,KAAK,CAACQ,YAAY,EAAE;IAC3B,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACR,UAAU,CAACJ,IAAI,EAAE;IACtB,IAAI,CAACI,UAAU,CAACS,QAAQ,EAAE;EAC5B;;;uCAjCWjB,8BAA8B,EAAAR,EAAA,CAAA0B,iBAAA,CAAAC,EAAA,CAAAC,UAAA,GAAA5B,EAAA,CAAA0B,iBAAA,CAAA1B,EAAA,CAAA6B,iBAAA;IAAA;EAAA;;;YAA9BrB,8BAA8B;MAAAsB,SAAA;MAAAC,MAAA;QAAAtB,OAAA;MAAA;MAAAuB,KAAA,EAAAC,GAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZvCvC,EAFJ,CAAAyC,cAAA,kBAAgH,aACrE,aACL;UAChCzC,EAAA,CAAA0C,UAAA,IAAAC,6CAAA,iBAAqD;UAEzD3C,EADE,CAAA4C,YAAA,EAAM,EACF;UACN5C,EAAA,CAAAyC,cAAA,wBAAqC;UACnCzC,EAAA,CAAA6C,MAAA,GACF;UAAA7C,EAAA,CAAA4C,YAAA,EAAiB;UAGb5C,EAFJ,CAAAyC,cAAA,uBAAkB,mBACF,kBAC8D;UAAAzC,EAAA,CAAA6C,MAAA,GAAc;UAAA7C,EAAA,CAAA4C,YAAA,EAAW;UACnG5C,EAAA,CAAAyC,cAAA,mBAAiG;UAC/FzC,EAAA,CAAA6C,MAAA,IACF;UAGN7C,EAHM,CAAA4C,YAAA,EAAW,EACE,EACE,EACV;;;UAjB8B5C,EAAA,CAAAE,UAAA,eAAAF,EAAA,CAAA8C,eAAA,IAAAC,GAAA,EAAAP,GAAA,CAAAlC,IAAA,kBAAAkC,GAAA,CAAAlC,IAAA,CAAAc,IAAA,EAAsE;UAGnGpB,EAAA,CAAAgD,SAAA,GAAW;UAAXhD,EAAA,CAAAE,UAAA,SAAAsC,GAAA,CAAApC,KAAA,CAAW;UAInBJ,EAAA,CAAAgD,SAAA,GACF;UADEhD,EAAA,CAAAiD,kBAAA,MAAAT,GAAA,CAAAlC,IAAA,kBAAAkC,GAAA,CAAAlC,IAAA,CAAAC,KAAA,MACF;UAGcP,EAAA,CAAAgD,SAAA,GAA2C;UAA3ChD,EAAA,CAAAkD,qBAAA,gCAAAV,GAAA,CAAAlC,IAAA,kBAAAkC,GAAA,CAAAlC,IAAA,CAAA6C,IAAA,EAA2C;UAAqBnD,EAAA,CAAAgD,SAAA,EAAc;UAAdhD,EAAA,CAAAoD,iBAAA,CAAAZ,GAAA,CAAAlC,IAAA,kBAAAkC,GAAA,CAAAlC,IAAA,CAAA6C,IAAA,CAAc;UACtDnD,EAAA,CAAAgD,SAAA,EAA8D;UAA9DhD,EAAA,CAAAkD,qBAAA,oCAAAV,GAAA,CAAAlC,IAAA,kBAAAkC,GAAA,CAAAlC,IAAA,CAAA+C,YAAA,kBAAAb,GAAA,CAAAlC,IAAA,CAAA+C,YAAA,CAAA9C,KAAA,EAA8D;UAC9FP,EAAA,CAAAgD,SAAA,EACF;UADEhD,EAAA,CAAAiD,kBAAA,MAAAT,GAAA,CAAAlC,IAAA,kBAAAkC,GAAA,CAAAlC,IAAA,CAAA+C,YAAA,kBAAAb,GAAA,CAAAlC,IAAA,CAAA+C,YAAA,CAAAjC,IAAA,MACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}