{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FlexModule } from '@angular/flex-layout';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { ClipboardModule } from '../../../../../../../common/components/clipboard/clipboard.module';\nimport { ViewDetailsComponent } from './view-details.component';\nimport * as i0 from \"@angular/core\";\nexport class ViewDetailsModule {\n  static {\n    this.ɵfac = function ViewDetailsModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ViewDetailsModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ViewDetailsModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, MatCardModule, ReactiveFormsModule, ClipboardModule, TranslateModule, FlexModule, MatButtonModule, MatDialogModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ViewDetailsModule, {\n    declarations: [ViewDetailsComponent],\n    imports: [CommonModule, MatCardModule, ReactiveFormsModule, ClipboardModule, TranslateModule, FlexModule, MatButtonModule, MatDialogModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FlexModule", "ReactiveFormsModule", "MatButtonModule", "MatCardModule", "MatDialogModule", "TranslateModule", "ClipboardModule", "ViewDetailsComponent", "ViewDetailsModule", "declarations", "imports"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-games/jp-games-info/modals/view-details.module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { FlexModule } from '@angular/flex-layout';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { ClipboardModule } from '../../../../../../../common/components/clipboard/clipboard.module';\nimport { ViewDetailsComponent } from './view-details.component';\n\n@NgModule({\n  declarations: [\n    ViewDetailsComponent\n  ],\n  entryComponents: [\n    ViewDetailsComponent\n  ],\n  imports: [\n    CommonModule,\n    MatCardModule,\n    ReactiveFormsModule,\n    ClipboardModule,\n    TranslateModule,\n    FlexModule,\n    MatButtonModule,\n    MatDialogModule\n  ]\n})\nexport class ViewDetailsModule {\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,UAAU,QAAQ,sBAAsB;AACjD,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,eAAe,QAAQ,mEAAmE;AACnG,SAASC,oBAAoB,QAAQ,0BAA0B;;AAoB/D,OAAM,MAAOC,iBAAiB;;;uCAAjBA,iBAAiB;IAAA;EAAA;;;YAAjBA;IAAiB;EAAA;;;gBAV1BT,YAAY,EACZI,aAAa,EACbF,mBAAmB,EACnBK,eAAe,EACfD,eAAe,EACfL,UAAU,EACVE,eAAe,EACfE,eAAe;IAAA;EAAA;;;2EAGNI,iBAAiB;IAAAC,YAAA,GAhB1BF,oBAAoB;IAAAG,OAAA,GAMpBX,YAAY,EACZI,aAAa,EACbF,mBAAmB,EACnBK,eAAe,EACfD,eAAe,EACfL,UAAU,EACVE,eAAe,EACfE,eAAe;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}