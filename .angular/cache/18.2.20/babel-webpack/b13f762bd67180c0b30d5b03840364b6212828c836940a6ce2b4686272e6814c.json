{"ast": null, "code": "import { codeArrayToObjectReducer } from '../../../../../../common/services/entity.service';\nimport { MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport { MatTableDataSource } from '@angular/material/table';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/material/table\";\nimport * as i5 from \"@angular/material/form-field\";\nimport * as i6 from \"@angular/material/input\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/checkbox\";\nimport * as i9 from \"../../../../../../common/directives/trim-input-value/trim-input-value.component\";\nimport * as i10 from \"@ngx-translate/core\";\nconst _c0 = () => [\"amount\"];\nfunction MatCurrencyDialogComponent_th_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 17)(1, \"mat-checkbox\", 18);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function MatCurrencyDialogComponent_th_9_Template_mat_checkbox_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.allSelected, $event) || (ctx_r1.allSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function MatCurrencyDialogComponent_th_9_Template_mat_checkbox_change_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.allSelectedChanged($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.allSelected);\n    i0.ɵɵproperty(\"disabled\", !!ctx_r1.nameFilter);\n  }\n}\nfunction MatCurrencyDialogComponent_td_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\", 19)(1, \"mat-checkbox\", 20);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function MatCurrencyDialogComponent_td_10_Template_mat_checkbox_ngModelChange_1_listener($event) {\n      const currency_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      i0.ɵɵtwoWayBindingSet(currency_r4.selected, $event) || (currency_r4.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const currency_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", currency_r4.selected);\n    i0.ɵɵproperty(\"disabled\", (ctx_r1.entity == null ? null : ctx_r1.entity.defaultCountry) === currency_r4.code);\n  }\n}\nfunction MatCurrencyDialogComponent_th_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, ctx_r1.allSelected ? \"ENTITY_SETUP.REGIONAL.unSelectAll\" : \"ENTITY_SETUP.REGIONAL.selectAll\"), \" \");\n  }\n}\nfunction MatCurrencyDialogComponent_td_13_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 22);\n    i0.ɵɵtext(1, \"Social\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MatCurrencyDialogComponent_td_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, MatCurrencyDialogComponent_td_13_span_2_Template, 2, 0, \"span\", 21);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const currency_r5 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", currency_r5.displayName, \" ( \", currency_r5.label, \" ) \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", currency_r5.isSocial);\n  }\n}\nfunction MatCurrencyDialogComponent_td_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 23);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementStart(3, \"span\")(4, \"strong\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, \"ENTITY_SETUP.REGIONAL.MODALS.selected\"), \": \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.selectedItems.length);\n  }\n}\nfunction MatCurrencyDialogComponent_tr_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 24);\n  }\n}\nfunction MatCurrencyDialogComponent_tr_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 25);\n  }\n}\nfunction MatCurrencyDialogComponent_tr_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 26);\n  }\n}\nexport class CurrencyItem {\n  constructor(data) {\n    this.code = data.code || '';\n    this.displayName = data.displayName || '';\n    this.label = data.label;\n    this.isSocial = data.isSocial;\n  }\n}\nexport class MatCurrencyDialogComponent {\n  get selectedItems() {\n    return this.dataSource.data.filter(item => item.selected);\n  }\n  constructor(data, dialogRef) {\n    this.data = data;\n    this.dialogRef = dialogRef;\n    this.displayedColumns = ['name', 'code'];\n    this.allSelected = false;\n    this.currencies = data.currencies;\n    this.entity = data.entity;\n  }\n  ngOnInit() {\n    this.buildHash();\n    this.initAvailableCurrencies();\n  }\n  applyFilter(filterValue) {\n    this.nameFilter = filterValue;\n    this.dataSource.filter = filterValue.trim().toLowerCase();\n    this.allSelected = this.dataSource.filteredData.every(item => item.selected);\n  }\n  applyChanges() {\n    this.dialogRef.close({\n      entity: this.entity,\n      selected: this.selectedItems.map(item => item.code)\n    });\n  }\n  allSelectedChanged(changeEvent) {\n    this.dataSource.data.map(item => item.selected = false);\n    if (changeEvent.checked) {\n      this.dataSource.filteredData.map(item => item.selected = true);\n    } else {\n      this.defaultCurrency.selected = true;\n    }\n  }\n  get availableCurrencyCodes() {\n    let currencyCodes = this.entity.entityParent.currencies;\n    const rootParent = this.entity.entityParent.isRoot();\n    const rootCurrenciesMismatch = currencyCodes.length !== this.currencies.length;\n    if (rootParent && rootCurrenciesMismatch) {\n      currencyCodes = Object.keys(this.hash);\n    }\n    return currencyCodes;\n  }\n  initAvailableCurrencies() {\n    const items = this.availableCurrencyCodes.map(code => {\n      const item = new CurrencyItem(this.hash[code]);\n      item.selected = this.entity.currencies.indexOf(code) > -1;\n      if (code === this.entity.defaultCurrency) {\n        this.defaultCurrency = item;\n      }\n      return item;\n    });\n    this.dataSource = new MatTableDataSource(items);\n  }\n  buildHash() {\n    this.hash = this.currencies.reduce(codeArrayToObjectReducer, {});\n  }\n  static {\n    this.ɵfac = function MatCurrencyDialogComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatCurrencyDialogComponent)(i0.ɵɵdirectiveInject(MAT_DIALOG_DATA), i0.ɵɵdirectiveInject(i1.MatDialogRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: MatCurrencyDialogComponent,\n      selectors: [[\"mat-currency-dialog\"]],\n      decls: 26,\n      vars: 19,\n      consts: [[\"mat-dialog-title\", \"\"], [\"appearance\", \"outline\", 1, \"sw-dialog-search\", \"no-field-padding\"], [\"matInput\", \"\", \"trimValue\", \"\", 3, \"keyup\", \"placeholder\"], [1, \"sw-dialog-content\"], [\"mat-table\", \"\", 3, \"dataSource\"], [\"matColumnDef\", \"name\"], [\"mat-header-cell\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"code\"], [\"matColumnDef\", \"amount\"], [\"mat-footer-cell\", \"\", \"colspan\", \"2\", 4, \"matFooterCellDef\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\", \"matHeaderRowDefSticky\"], [\"mat-row\", \"\", 4, \"matRowDef\", \"matRowDefColumns\"], [\"mat-footer-row\", \"\", 4, \"matFooterRowDef\", \"matFooterRowDefSticky\"], [\"align\", \"end\"], [\"mat-button\", \"\", \"color\", \"primary\", \"mat-dialog-close\", \"\", 1, \"mat-button-md\"], [\"mat-flat-button\", \"\", \"color\", \"primary\", \"cdkFocusInitial\", \"\", 1, \"mat-button-md\", 3, \"click\"], [\"mat-header-cell\", \"\"], [3, \"ngModelChange\", \"change\", \"ngModel\", \"disabled\"], [\"mat-cell\", \"\"], [3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"class\", \"sw-chip sw-chip-blue\", \"style\", \"margin-left: 8px;\", 4, \"ngIf\"], [1, \"sw-chip\", \"sw-chip-blue\", 2, \"margin-left\", \"8px\"], [\"mat-footer-cell\", \"\", \"colspan\", \"2\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\"], [\"mat-footer-row\", \"\"]],\n      template: function MatCurrencyDialogComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"h2\", 0);\n          i0.ɵɵtext(1);\n          i0.ɵɵpipe(2, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"mat-form-field\", 1)(4, \"input\", 2);\n          i0.ɵɵpipe(5, \"translate\");\n          i0.ɵɵlistener(\"keyup\", function MatCurrencyDialogComponent_Template_input_keyup_4_listener($event) {\n            return ctx.applyFilter($event.target[\"value\"]);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"mat-dialog-content\", 3)(7, \"table\", 4);\n          i0.ɵɵelementContainerStart(8, 5);\n          i0.ɵɵtemplate(9, MatCurrencyDialogComponent_th_9_Template, 2, 2, \"th\", 6)(10, MatCurrencyDialogComponent_td_10_Template, 2, 2, \"td\", 7);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(11, 8);\n          i0.ɵɵtemplate(12, MatCurrencyDialogComponent_th_12_Template, 3, 3, \"th\", 6)(13, MatCurrencyDialogComponent_td_13_Template, 3, 3, \"td\", 7);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(14, 9);\n          i0.ɵɵtemplate(15, MatCurrencyDialogComponent_td_15_Template, 6, 4, \"td\", 10);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵtemplate(16, MatCurrencyDialogComponent_tr_16_Template, 1, 0, \"tr\", 11)(17, MatCurrencyDialogComponent_tr_17_Template, 1, 0, \"tr\", 12)(18, MatCurrencyDialogComponent_tr_18_Template, 1, 0, \"tr\", 13);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(19, \"mat-dialog-actions\", 14)(20, \"button\", 15);\n          i0.ɵɵtext(21);\n          i0.ɵɵpipe(22, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"button\", 16);\n          i0.ɵɵlistener(\"click\", function MatCurrencyDialogComponent_Template_button_click_23_listener() {\n            return ctx.applyChanges();\n          });\n          i0.ɵɵtext(24);\n          i0.ɵɵpipe(25, \"translate\");\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 10, \"ENTITY_SETUP.REGIONAL.MODALS.manageCurrency\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"placeholder\", i0.ɵɵpipeBind1(5, 12, \"ENTITY_SETUP.REGIONAL.MODALS.placeholderSearch\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"dataSource\", ctx.dataSource);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"matHeaderRowDef\", ctx.displayedColumns)(\"matHeaderRowDefSticky\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"matRowDefColumns\", ctx.displayedColumns);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"matFooterRowDef\", i0.ɵɵpureFunction0(18, _c0))(\"matFooterRowDefSticky\", true);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(22, 14, \"DIALOG.cancel\"), \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(25, 16, \"DIALOG.save\"), \" \");\n        }\n      },\n      dependencies: [i2.NgIf, i3.NgControlStatus, i3.NgModel, i4.MatTable, i4.MatHeaderCellDef, i4.MatHeaderRowDef, i4.MatColumnDef, i4.MatCellDef, i4.MatRowDef, i4.MatFooterCellDef, i4.MatFooterRowDef, i4.MatHeaderCell, i4.MatCell, i4.MatFooterCell, i4.MatHeaderRow, i4.MatRow, i4.MatFooterRow, i5.MatFormField, i6.MatInput, i7.MatButton, i1.MatDialogClose, i1.MatDialogTitle, i1.MatDialogActions, i1.MatDialogContent, i8.MatCheckbox, i9.TrimInputValueComponent, i10.TranslatePipe],\n      styles: [\".sw-dialog-search[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin-bottom: 16px;\\n}\\n\\n.sw-dialog-content[_ngcontent-%COMP%] {\\n  padding: 0;\\n  max-height: 45vh;\\n}\\n.sw-dialog-content[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.sw-dialog-content[_ngcontent-%COMP%]   table[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-height: 400px;\\n  overflow: auto;\\n}\\n.sw-dialog-content[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%] {\\n  position: relative;\\n  bottom: -2px;\\n}\\n.sw-dialog-content[_ngcontent-%COMP%]   .mat-row[_ngcontent-%COMP%] {\\n  min-height: 44px;\\n}\\n.sw-dialog-content[_ngcontent-%COMP%]   .mat-cell[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #2a2c44;\\n  height: 44px !important;\\n}\\n.sw-dialog-content[_ngcontent-%COMP%]   .mat-cell[_ngcontent-%COMP%]:first-child {\\n  width: 56px;\\n  padding-right: 16px;\\n}\\n.sw-dialog-content[_ngcontent-%COMP%]   .mat-header-row[_ngcontent-%COMP%] {\\n  min-height: 42px;\\n}\\n.sw-dialog-content[_ngcontent-%COMP%]   .mat-header-cell[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: #2a2c44;\\n  height: 42px !important;\\n}\\n.sw-dialog-content[_ngcontent-%COMP%]   .mat-header-cell[_ngcontent-%COMP%]:first-child {\\n  width: 56px;\\n  padding-right: 16px;\\n}\\n.sw-dialog-content[_ngcontent-%COMP%]   .mat-footer-cell[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: rgba(0, 0, 0, 0.54);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvYnVzaW5lc3MtbWFuYWdlbWVudC9jb21wb25lbnRzL21hdC1idXNpbmVzcy1zdHJ1Y3R1cmUvZGlhbG9ncy9tYXQtY3VycmVuY3ktZGlhbG9nL21hdC1jdXJyZW5jeS1kaWFsb2cuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxXQUFBO0VBQ0EsbUJBQUE7QUFDRjs7QUFDQTtFQUNFLFVBQUE7RUFDQSxnQkFBQTtBQUVGO0FBREU7RUFDRSxXQUFBO0FBR0o7QUFBRTtFQUNFLFdBQUE7RUFDQSxpQkFBQTtFQUNBLGNBQUE7QUFFSjtBQUNFO0VBQ0Usa0JBQUE7RUFDQSxZQUFBO0FBQ0o7QUFFRTtFQUNFLGdCQUFBO0FBQUo7QUFHRTtFQUNFLGVBQUE7RUFDQSxjQUFBO0VBQ0EsdUJBQUE7QUFESjtBQUVJO0VBQ0UsV0FBQTtFQUNBLG1CQUFBO0FBQU47QUFJRTtFQUNFLGdCQUFBO0FBRko7QUFJRTtFQUNFLGVBQUE7RUFDQSxjQUFBO0VBQ0EsdUJBQUE7QUFGSjtBQUdJO0VBQ0UsV0FBQTtFQUNBLG1CQUFBO0FBRE47QUFLRTtFQUNFLGVBQUE7RUFDQSwwQkFBQTtBQUhKIiwic291cmNlc0NvbnRlbnQiOlsiLnN3LWRpYWxvZy1zZWFyY2gge1xuICB3aWR0aDogMTAwJTtcbiAgbWFyZ2luLWJvdHRvbTogMTZweDtcbn1cbi5zdy1kaWFsb2ctY29udGVudHtcbiAgcGFkZGluZzogMDtcbiAgbWF4LWhlaWdodDogNDV2aDtcbiAgbWF0LWZvcm0tZmllbGQge1xuICAgIHdpZHRoOiAxMDAlO1xuICB9XG5cbiAgdGFibGUge1xuICAgIHdpZHRoOiAxMDAlO1xuICAgIG1heC1oZWlnaHQ6IDQwMHB4O1xuICAgIG92ZXJmbG93OiBhdXRvO1xuICB9XG5cbiAgbWF0LWNoZWNrYm94IHtcbiAgICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gICAgYm90dG9tOiAtMnB4O1xuICB9XG5cbiAgLm1hdC1yb3cge1xuICAgIG1pbi1oZWlnaHQ6IDQ0cHg7XG4gIH1cblxuICAubWF0LWNlbGwge1xuICAgIGZvbnQtc2l6ZTogMTRweDtcbiAgICBjb2xvcjogIzJhMmM0NDtcbiAgICBoZWlnaHQ6IDQ0cHggIWltcG9ydGFudDtcbiAgICAmOmZpcnN0LWNoaWxkIHtcbiAgICAgIHdpZHRoOiA1NnB4O1xuICAgICAgcGFkZGluZy1yaWdodDogMTZweDtcbiAgICB9XG4gIH1cblxuICAubWF0LWhlYWRlci1yb3cge1xuICAgIG1pbi1oZWlnaHQ6IDQycHg7XG4gIH1cbiAgLm1hdC1oZWFkZXItY2VsbCB7XG4gICAgZm9udC1zaXplOiAxNnB4O1xuICAgIGNvbG9yOiAjMmEyYzQ0O1xuICAgIGhlaWdodDogNDJweCAhaW1wb3J0YW50O1xuICAgICY6Zmlyc3QtY2hpbGQge1xuICAgICAgd2lkdGg6IDU2cHg7XG4gICAgICBwYWRkaW5nLXJpZ2h0OiAxNnB4O1xuICAgIH1cbiAgfVxuXG4gIC5tYXQtZm9vdGVyLWNlbGwge1xuICAgIGZvbnQtc2l6ZTogMTRweDtcbiAgICBjb2xvcjogcmdiYSgwLDAsMCwuNTQpOztcbiAgfVxufVxuXG5cbiJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["codeArrayToObjectReducer", "MAT_DIALOG_DATA", "MatTableDataSource", "i0", "ɵɵelementStart", "ɵɵtwoWayListener", "MatCurrencyDialogComponent_th_9_Template_mat_checkbox_ngModelChange_1_listener", "$event", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵtwoWayBindingSet", "allSelected", "ɵɵresetView", "ɵɵlistener", "MatCurrencyDialogComponent_th_9_Template_mat_checkbox_change_1_listener", "allSelectedChanged", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtwoWayProperty", "ɵɵproperty", "nameFilter", "MatCurrencyDialogComponent_td_10_Template_mat_checkbox_ngModelChange_1_listener", "currency_r4", "_r3", "$implicit", "selected", "entity", "defaultCountry", "code", "ɵɵtext", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "ɵɵtemplate", "MatCurrencyDialogComponent_td_13_span_2_Template", "ɵɵtextInterpolate2", "currency_r5", "displayName", "label", "isSocial", "ɵɵtextInterpolate", "selectedItems", "length", "ɵɵelement", "CurrencyItem", "constructor", "data", "MatCurrencyDialogComponent", "dataSource", "filter", "item", "dialogRef", "displayedColumns", "currencies", "ngOnInit", "buildHash", "initAvailableCurrencies", "applyFilter", "filterValue", "trim", "toLowerCase", "filteredData", "every", "applyChanges", "close", "map", "changeEvent", "checked", "defaultCurrency", "availableCurrencyCodes", "currencyCodes", "entityParent", "rootParent", "isRoot", "rootCurrenciesMismatch", "Object", "keys", "hash", "items", "indexOf", "reduce", "ɵɵdirectiveInject", "i1", "MatDialogRef", "selectors", "decls", "vars", "consts", "template", "MatCurrencyDialogComponent_Template", "rf", "ctx", "MatCurrencyDialogComponent_Template_input_keyup_4_listener", "target", "ɵɵelementContainerStart", "MatCurrencyDialogComponent_th_9_Template", "MatCurrencyDialogComponent_td_10_Template", "MatCurrencyDialogComponent_th_12_Template", "MatCurrencyDialogComponent_td_13_Template", "MatCurrencyDialogComponent_td_15_Template", "MatCurrencyDialogComponent_tr_16_Template", "MatCurrencyDialogComponent_tr_17_Template", "MatCurrencyDialogComponent_tr_18_Template", "MatCurrencyDialogComponent_Template_button_click_23_listener", "ɵɵpureFunction0", "_c0"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/mat-business-structure/dialogs/mat-currency-dialog/mat-currency-dialog.component.ts", "/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/mat-business-structure/dialogs/mat-currency-dialog/mat-currency-dialog.component.html"], "sourcesContent": ["import { Component, Inject, OnInit } from '@angular/core';\nimport { Currency } from '../../../../../../common/typings';\nimport { Entity } from '../../../../../../common/models/entity.model';\nimport { codeArrayToObjectReducer } from '../../../../../../common/services/entity.service';\nimport { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';\nimport { MatCheckboxChange } from '@angular/material/checkbox';\nimport { MatTableDataSource } from '@angular/material/table';\n\n\nexport interface MatCurrencyDialogData {\n  entity: Entity;\n  currencies: Currency[];\n}\n\nexport class CurrencyItem implements Currency {\n  code: string;\n  displayName: string;\n  label?: string;\n  isSocial?: true;\n  selected?: boolean;\n\n  constructor( data: Currency ) {\n    this.code = data.code || '';\n    this.displayName = data.displayName || '';\n    this.label = data.label;\n    this.isSocial = data.isSocial;\n  }\n}\n\n@Component({\n  selector: 'mat-currency-dialog',\n  templateUrl: 'mat-currency-dialog.component.html',\n  styleUrls: [\n    './mat-currency-dialog.component.scss'\n  ]\n})\n\nexport class MatCurrencyDialogComponent implements OnInit {\n\n  entity: Entity;\n  currencies: Currency[];\n  loading: boolean;\n\n  nameFilter: string;\n  displayedColumns: string[] = ['name', 'code'];\n  dataSource: MatTableDataSource<CurrencyItem>;\n  allSelected = false;\n\n  private hash: { [code: string]: Currency };\n  private defaultCurrency: CurrencyItem;\n\n  get selectedItems(): CurrencyItem[] {\n    return this.dataSource.data.filter(item => item.selected);\n  }\n\n  constructor(\n    @Inject(MAT_DIALOG_DATA) public data: MatCurrencyDialogData,\n    public dialogRef: MatDialogRef<MatCurrencyDialogComponent>,\n  ) {\n    this.currencies = data.currencies;\n    this.entity = data.entity;\n  }\n\n  ngOnInit() {\n    this.buildHash();\n    this.initAvailableCurrencies();\n  }\n\n  applyFilter( filterValue: string ) {\n    this.nameFilter = filterValue;\n    this.dataSource.filter = filterValue.trim().toLowerCase();\n    this.allSelected = this.dataSource.filteredData.every(item => item.selected);\n  }\n\n  applyChanges() {\n    this.dialogRef.close({\n      entity: this.entity,\n      selected: this.selectedItems.map(item => item.code)\n    });\n  }\n\n  allSelectedChanged( changeEvent: MatCheckboxChange ) {\n    this.dataSource.data.map(item => item.selected = false);\n\n    if (changeEvent.checked) {\n      this.dataSource.filteredData.map(item => item.selected = true);\n    } else {\n      this.defaultCurrency.selected = true;\n    }\n  }\n\n  get availableCurrencyCodes(): string[] {\n    let currencyCodes = this.entity.entityParent.currencies;\n    const rootParent = this.entity.entityParent.isRoot();\n    const rootCurrenciesMismatch = currencyCodes.length !== this.currencies.length;\n\n    if (rootParent && rootCurrenciesMismatch) {\n      currencyCodes = Object.keys(this.hash);\n    }\n\n    return currencyCodes;\n  }\n\n  private initAvailableCurrencies() {\n    const items = this.availableCurrencyCodes\n      .map(( code ) => {\n        const item = new CurrencyItem(this.hash[code]);\n        item.selected = this.entity.currencies.indexOf(code) > -1;\n\n        if (code === this.entity.defaultCurrency) {\n          this.defaultCurrency = item;\n        }\n\n        return item;\n      });\n    this.dataSource = new MatTableDataSource(items);\n  }\n\n  private buildHash() {\n    this.hash = this.currencies.reduce(codeArrayToObjectReducer, {});\n  }\n}\n", "<h2 mat-dialog-title>{{ 'ENTITY_SETUP.REGIONAL.MODALS.manageCurrency' | translate }}</h2>\n\n<mat-form-field class=\"sw-dialog-search no-field-padding\" appearance=\"outline\">\n  <input\n    matInput trimValue\n    (keyup)=\"applyFilter($event.target['value'])\"\n    [placeholder]=\"'ENTITY_SETUP.REGIONAL.MODALS.placeholderSearch' | translate\">\n</mat-form-field>\n\n<mat-dialog-content class=\"sw-dialog-content\">\n  <table mat-table [dataSource]=\"dataSource\">\n    <ng-container matColumnDef=\"name\">\n      <th mat-header-cell *matHeaderCellDef>\n        <mat-checkbox\n          [(ngModel)]=\"allSelected\"\n          (change)=\"allSelectedChanged($event)\"\n          [disabled]=\"!!nameFilter\">\n        </mat-checkbox>\n      </th>\n      <td mat-cell *matCellDef=\"let currency\">\n        <mat-checkbox\n          [(ngModel)]=\"currency.selected\"\n          [disabled]=\"entity?.defaultCountry === currency.code\">\n        </mat-checkbox>\n      </td>\n    </ng-container>\n\n    <ng-container matColumnDef=\"code\">\n      <th mat-header-cell *matHeaderCellDef>\n        {{ (allSelected ? 'ENTITY_SETUP.REGIONAL.unSelectAll' : 'ENTITY_SETUP.REGIONAL.selectAll') | translate }}\n      </th>\n      <td mat-cell *matCellDef=\"let currency\">\n        {{ currency.displayName }} ( {{ currency.label }} )\n        <span *ngIf=\"currency.isSocial\" class=\"sw-chip sw-chip-blue\" style=\"margin-left: 8px;\">Social</span>\n      </td>\n    </ng-container>\n\n    <ng-container matColumnDef=\"amount\">\n      <td mat-footer-cell *matFooterCellDef colspan=\"2\">\n        {{ 'ENTITY_SETUP.REGIONAL.MODALS.selected' | translate }}:\n        <span>\n          <strong>{{ selectedItems.length }}</strong>\n        </span>\n      </td>\n    </ng-container>\n\n    <tr mat-header-row *matHeaderRowDef=\"displayedColumns; sticky: true\"></tr>\n    <tr mat-row *matRowDef=\"let row; columns: displayedColumns;\"></tr>\n    <tr mat-footer-row *matFooterRowDef=\"['amount']; sticky: true\"></tr>\n  </table>\n</mat-dialog-content>\n\n<mat-dialog-actions align=\"end\">\n  <button\n    mat-button\n    color=\"primary\"\n    class=\"mat-button-md\"\n    mat-dialog-close>\n    {{ 'DIALOG.cancel' | translate }}\n  </button>\n  <button\n    mat-flat-button\n    color=\"primary\"\n    class=\"mat-button-md\"\n    cdkFocusInitial\n    (click)=\"applyChanges()\">\n    {{ 'DIALOG.save' | translate }}\n  </button>\n</mat-dialog-actions>\n"], "mappings": "AAGA,SAASA,wBAAwB,QAAQ,kDAAkD;AAC3F,SAASC,eAAe,QAAsB,0BAA0B;AAExE,SAASC,kBAAkB,QAAQ,yBAAyB;;;;;;;;;;;;;;;;ICOpDC,EADF,CAAAC,cAAA,aAAsC,uBAIR;IAF1BD,EAAA,CAAAE,gBAAA,2BAAAC,+EAAAC,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAAS,kBAAA,CAAAF,MAAA,CAAAG,WAAA,EAAAN,MAAA,MAAAG,MAAA,CAAAG,WAAA,GAAAN,MAAA;MAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;IAAA,EAAyB;IACzBJ,EAAA,CAAAY,UAAA,oBAAAC,wEAAAT,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAW,WAAA,CAAUJ,MAAA,CAAAO,kBAAA,CAAAV,MAAA,CAA0B;IAAA,EAAC;IAGzCJ,EADE,CAAAe,YAAA,EAAe,EACZ;;;;IAJDf,EAAA,CAAAgB,SAAA,EAAyB;IAAzBhB,EAAA,CAAAiB,gBAAA,YAAAV,MAAA,CAAAG,WAAA,CAAyB;IAEzBV,EAAA,CAAAkB,UAAA,eAAAX,MAAA,CAAAY,UAAA,CAAyB;;;;;;IAI3BnB,EADF,CAAAC,cAAA,aAAwC,uBAGkB;IADtDD,EAAA,CAAAE,gBAAA,2BAAAkB,gFAAAhB,MAAA;MAAA,MAAAiB,WAAA,GAAArB,EAAA,CAAAK,aAAA,CAAAiB,GAAA,EAAAC,SAAA;MAAAvB,EAAA,CAAAS,kBAAA,CAAAY,WAAA,CAAAG,QAAA,EAAApB,MAAA,MAAAiB,WAAA,CAAAG,QAAA,GAAApB,MAAA;MAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;IAAA,EAA+B;IAGnCJ,EADE,CAAAe,YAAA,EAAe,EACZ;;;;;IAHDf,EAAA,CAAAgB,SAAA,EAA+B;IAA/BhB,EAAA,CAAAiB,gBAAA,YAAAI,WAAA,CAAAG,QAAA,CAA+B;IAC/BxB,EAAA,CAAAkB,UAAA,cAAAX,MAAA,CAAAkB,MAAA,kBAAAlB,MAAA,CAAAkB,MAAA,CAAAC,cAAA,MAAAL,WAAA,CAAAM,IAAA,CAAqD;;;;;IAMzD3B,EAAA,CAAAC,cAAA,aAAsC;IACpCD,EAAA,CAAA4B,MAAA,GACF;;IAAA5B,EAAA,CAAAe,YAAA,EAAK;;;;IADHf,EAAA,CAAAgB,SAAA,EACF;IADEhB,EAAA,CAAA6B,kBAAA,MAAA7B,EAAA,CAAA8B,WAAA,OAAAvB,MAAA,CAAAG,WAAA,iFACF;;;;;IAGEV,EAAA,CAAAC,cAAA,eAAuF;IAAAD,EAAA,CAAA4B,MAAA,aAAM;IAAA5B,EAAA,CAAAe,YAAA,EAAO;;;;;IAFtGf,EAAA,CAAAC,cAAA,aAAwC;IACtCD,EAAA,CAAA4B,MAAA,GACA;IAAA5B,EAAA,CAAA+B,UAAA,IAAAC,gDAAA,mBAAuF;IACzFhC,EAAA,CAAAe,YAAA,EAAK;;;;IAFHf,EAAA,CAAAgB,SAAA,EACA;IADAhB,EAAA,CAAAiC,kBAAA,MAAAC,WAAA,CAAAC,WAAA,SAAAD,WAAA,CAAAE,KAAA,QACA;IAAOpC,EAAA,CAAAgB,SAAA,EAAuB;IAAvBhB,EAAA,CAAAkB,UAAA,SAAAgB,WAAA,CAAAG,QAAA,CAAuB;;;;;IAKhCrC,EAAA,CAAAC,cAAA,aAAkD;IAChDD,EAAA,CAAA4B,MAAA,GACA;;IACE5B,EADF,CAAAC,cAAA,WAAM,aACI;IAAAD,EAAA,CAAA4B,MAAA,GAA0B;IAEtC5B,EAFsC,CAAAe,YAAA,EAAS,EACtC,EACJ;;;;IAJHf,EAAA,CAAAgB,SAAA,EACA;IADAhB,EAAA,CAAA6B,kBAAA,MAAA7B,EAAA,CAAA8B,WAAA,sDACA;IACU9B,EAAA,CAAAgB,SAAA,GAA0B;IAA1BhB,EAAA,CAAAsC,iBAAA,CAAA/B,MAAA,CAAAgC,aAAA,CAAAC,MAAA,CAA0B;;;;;IAKxCxC,EAAA,CAAAyC,SAAA,aAA0E;;;;;IAC1EzC,EAAA,CAAAyC,SAAA,aAAkE;;;;;IAClEzC,EAAA,CAAAyC,SAAA,aAAoE;;;ADlCxE,OAAM,MAAOC,YAAY;EAOvBC,YAAaC,IAAc;IACzB,IAAI,CAACjB,IAAI,GAAGiB,IAAI,CAACjB,IAAI,IAAI,EAAE;IAC3B,IAAI,CAACQ,WAAW,GAAGS,IAAI,CAACT,WAAW,IAAI,EAAE;IACzC,IAAI,CAACC,KAAK,GAAGQ,IAAI,CAACR,KAAK;IACvB,IAAI,CAACC,QAAQ,GAAGO,IAAI,CAACP,QAAQ;EAC/B;;AAWF,OAAM,MAAOQ,0BAA0B;EAcrC,IAAIN,aAAaA,CAAA;IACf,OAAO,IAAI,CAACO,UAAU,CAACF,IAAI,CAACG,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACxB,QAAQ,CAAC;EAC3D;EAEAmB,YACkCC,IAA2B,EACpDK,SAAmD;IAD1B,KAAAL,IAAI,GAAJA,IAAI;IAC7B,KAAAK,SAAS,GAATA,SAAS;IAblB,KAAAC,gBAAgB,GAAa,CAAC,MAAM,EAAE,MAAM,CAAC;IAE7C,KAAAxC,WAAW,GAAG,KAAK;IAajB,IAAI,CAACyC,UAAU,GAAGP,IAAI,CAACO,UAAU;IACjC,IAAI,CAAC1B,MAAM,GAAGmB,IAAI,CAACnB,MAAM;EAC3B;EAEA2B,QAAQA,CAAA;IACN,IAAI,CAACC,SAAS,EAAE;IAChB,IAAI,CAACC,uBAAuB,EAAE;EAChC;EAEAC,WAAWA,CAAEC,WAAmB;IAC9B,IAAI,CAACrC,UAAU,GAAGqC,WAAW;IAC7B,IAAI,CAACV,UAAU,CAACC,MAAM,GAAGS,WAAW,CAACC,IAAI,EAAE,CAACC,WAAW,EAAE;IACzD,IAAI,CAAChD,WAAW,GAAG,IAAI,CAACoC,UAAU,CAACa,YAAY,CAACC,KAAK,CAACZ,IAAI,IAAIA,IAAI,CAACxB,QAAQ,CAAC;EAC9E;EAEAqC,YAAYA,CAAA;IACV,IAAI,CAACZ,SAAS,CAACa,KAAK,CAAC;MACnBrC,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBD,QAAQ,EAAE,IAAI,CAACe,aAAa,CAACwB,GAAG,CAACf,IAAI,IAAIA,IAAI,CAACrB,IAAI;KACnD,CAAC;EACJ;EAEAb,kBAAkBA,CAAEkD,WAA8B;IAChD,IAAI,CAAClB,UAAU,CAACF,IAAI,CAACmB,GAAG,CAACf,IAAI,IAAIA,IAAI,CAACxB,QAAQ,GAAG,KAAK,CAAC;IAEvD,IAAIwC,WAAW,CAACC,OAAO,EAAE;MACvB,IAAI,CAACnB,UAAU,CAACa,YAAY,CAACI,GAAG,CAACf,IAAI,IAAIA,IAAI,CAACxB,QAAQ,GAAG,IAAI,CAAC;IAChE,CAAC,MAAM;MACL,IAAI,CAAC0C,eAAe,CAAC1C,QAAQ,GAAG,IAAI;IACtC;EACF;EAEA,IAAI2C,sBAAsBA,CAAA;IACxB,IAAIC,aAAa,GAAG,IAAI,CAAC3C,MAAM,CAAC4C,YAAY,CAAClB,UAAU;IACvD,MAAMmB,UAAU,GAAG,IAAI,CAAC7C,MAAM,CAAC4C,YAAY,CAACE,MAAM,EAAE;IACpD,MAAMC,sBAAsB,GAAGJ,aAAa,CAAC5B,MAAM,KAAK,IAAI,CAACW,UAAU,CAACX,MAAM;IAE9E,IAAI8B,UAAU,IAAIE,sBAAsB,EAAE;MACxCJ,aAAa,GAAGK,MAAM,CAACC,IAAI,CAAC,IAAI,CAACC,IAAI,CAAC;IACxC;IAEA,OAAOP,aAAa;EACtB;EAEQd,uBAAuBA,CAAA;IAC7B,MAAMsB,KAAK,GAAG,IAAI,CAACT,sBAAsB,CACtCJ,GAAG,CAAGpC,IAAI,IAAK;MACd,MAAMqB,IAAI,GAAG,IAAIN,YAAY,CAAC,IAAI,CAACiC,IAAI,CAAChD,IAAI,CAAC,CAAC;MAC9CqB,IAAI,CAACxB,QAAQ,GAAG,IAAI,CAACC,MAAM,CAAC0B,UAAU,CAAC0B,OAAO,CAAClD,IAAI,CAAC,GAAG,CAAC,CAAC;MAEzD,IAAIA,IAAI,KAAK,IAAI,CAACF,MAAM,CAACyC,eAAe,EAAE;QACxC,IAAI,CAACA,eAAe,GAAGlB,IAAI;MAC7B;MAEA,OAAOA,IAAI;IACb,CAAC,CAAC;IACJ,IAAI,CAACF,UAAU,GAAG,IAAI/C,kBAAkB,CAAC6E,KAAK,CAAC;EACjD;EAEQvB,SAASA,CAAA;IACf,IAAI,CAACsB,IAAI,GAAG,IAAI,CAACxB,UAAU,CAAC2B,MAAM,CAACjF,wBAAwB,EAAE,EAAE,CAAC;EAClE;;;uCAnFWgD,0BAA0B,EAAA7C,EAAA,CAAA+E,iBAAA,CAmB3BjF,eAAe,GAAAE,EAAA,CAAA+E,iBAAA,CAAAC,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAnBdpC,0BAA0B;MAAAqC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCrCvCxF,EAAA,CAAAC,cAAA,YAAqB;UAAAD,EAAA,CAAA4B,MAAA,GAA+D;;UAAA5B,EAAA,CAAAe,YAAA,EAAK;UAGvFf,EADF,CAAAC,cAAA,wBAA+E,eAIE;;UAD7ED,EAAA,CAAAY,UAAA,mBAAA8E,2DAAAtF,MAAA;YAAA,OAASqF,GAAA,CAAAlC,WAAA,CAAAnD,MAAA,CAAAuF,MAAA,CAA0B,OAAO,EAAE;UAAA,EAAC;UAEjD3F,EAJE,CAAAe,YAAA,EAG+E,EAChE;UAGff,EADF,CAAAC,cAAA,4BAA8C,eACD;UACzCD,EAAA,CAAA4F,uBAAA,MAAkC;UAQhC5F,EAPA,CAAA+B,UAAA,IAAA8D,wCAAA,gBAAsC,KAAAC,yCAAA,gBAOE;;UAQ1C9F,EAAA,CAAA4F,uBAAA,OAAkC;UAIhC5F,EAHA,CAAA+B,UAAA,KAAAgE,yCAAA,gBAAsC,KAAAC,yCAAA,gBAGE;;UAM1ChG,EAAA,CAAA4F,uBAAA,OAAoC;UAClC5F,EAAA,CAAA+B,UAAA,KAAAkE,yCAAA,iBAAkD;;UAUpDjG,EAFA,CAAA+B,UAAA,KAAAmE,yCAAA,iBAAqE,KAAAC,yCAAA,iBACR,KAAAC,yCAAA,iBACE;UAEnEpG,EADE,CAAAe,YAAA,EAAQ,EACW;UAGnBf,EADF,CAAAC,cAAA,8BAAgC,kBAKX;UACjBD,EAAA,CAAA4B,MAAA,IACF;;UAAA5B,EAAA,CAAAe,YAAA,EAAS;UACTf,EAAA,CAAAC,cAAA,kBAK2B;UAAzBD,EAAA,CAAAY,UAAA,mBAAAyF,6DAAA;YAAA,OAASZ,GAAA,CAAA5B,YAAA,EAAc;UAAA,EAAC;UACxB7D,EAAA,CAAA4B,MAAA,IACF;;UACF5B,EADE,CAAAe,YAAA,EAAS,EACU;;;UApEAf,EAAA,CAAAgB,SAAA,EAA+D;UAA/DhB,EAAA,CAAAsC,iBAAA,CAAAtC,EAAA,CAAA8B,WAAA,uDAA+D;UAMhF9B,EAAA,CAAAgB,SAAA,GAA4E;UAA5EhB,EAAA,CAAAkB,UAAA,gBAAAlB,EAAA,CAAA8B,WAAA,0DAA4E;UAI7D9B,EAAA,CAAAgB,SAAA,GAAyB;UAAzBhB,EAAA,CAAAkB,UAAA,eAAAuE,GAAA,CAAA3C,UAAA,CAAyB;UAoCpB9C,EAAA,CAAAgB,SAAA,GAAmC;UAAAhB,EAAnC,CAAAkB,UAAA,oBAAAuE,GAAA,CAAAvC,gBAAA,CAAmC,+BAAY;UAClClD,EAAA,CAAAgB,SAAA,EAA0B;UAA1BhB,EAAA,CAAAkB,UAAA,qBAAAuE,GAAA,CAAAvC,gBAAA,CAA0B;UACvClD,EAAA,CAAAgB,SAAA,EAA6B;UAAAhB,EAA7B,CAAAkB,UAAA,oBAAAlB,EAAA,CAAAsG,eAAA,KAAAC,GAAA,EAA6B,+BAAY;UAU7DvG,EAAA,CAAAgB,SAAA,GACF;UADEhB,EAAA,CAAA6B,kBAAA,MAAA7B,EAAA,CAAA8B,WAAA,+BACF;UAOE9B,EAAA,CAAAgB,SAAA,GACF;UADEhB,EAAA,CAAA6B,kBAAA,MAAA7B,EAAA,CAAA8B,WAAA,6BACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}