{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatListModule } from '@angular/material/list';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { DeleteUserDialogComponent } from './delete-user-dialog.component';\nimport { TwofaResetDialogComponent } from './twofa-reset-dialog.component';\nimport { UnblockUserDialogComponent } from './unblock-user-dialog.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nexport class TabUsersDialogsModule {\n  static {\n    this.ɵfac = function TabUsersDialogsModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TabUsersDialogsModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: TabUsersDialogsModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, TranslateModule.forChild(), MatDialogModule, MatCheckboxModule, MatListModule, MatButtonModule, FormsModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(TabUsersDialogsModule, {\n    declarations: [DeleteUserDialogComponent, TwofaResetDialogComponent, UnblockUserDialogComponent],\n    imports: [CommonModule, i1.TranslateModule, MatDialogModule, MatCheckboxModule, MatListModule, MatButtonModule, FormsModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "MatButtonModule", "MatCheckboxModule", "MatDialogModule", "MatListModule", "TranslateModule", "DeleteUserDialogComponent", "TwofaResetDialogComponent", "UnblockUserDialogComponent", "TabUsersDialogsModule", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "imports", "i1"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/pages/business-management/components/entities/tab-users/dialogs/tab-users-dialogs.module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { FormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatListModule } from '@angular/material/list';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { DeleteUserDialogComponent } from './delete-user-dialog.component';\nimport { TwofaResetDialogComponent } from './twofa-reset-dialog.component';\nimport { UnblockUserDialogComponent } from './unblock-user-dialog.component';\n\n@NgModule({\n  imports: [\n    CommonModule,\n    TranslateModule.forChild(),\n    MatDialogModule,\n    MatCheckboxModule,\n    MatListModule,\n    MatButtonModule,\n    FormsModule,\n  ],\n  exports: [],\n  declarations: [\n    DeleteUserDialogComponent,\n    TwofaResetDialogComponent,\n    UnblockUserDialogComponent,\n  ],\n  providers: [],\n})\nexport class TabUsersDialogsModule {\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,yBAAyB,QAAQ,gCAAgC;AAC1E,SAASC,yBAAyB,QAAQ,gCAAgC;AAC1E,SAASC,0BAA0B,QAAQ,iCAAiC;;;AAoB5E,OAAM,MAAOC,qBAAqB;;;uCAArBA,qBAAqB;IAAA;EAAA;;;YAArBA;IAAqB;EAAA;;;gBAhB9BV,YAAY,EACZM,eAAe,CAACK,QAAQ,EAAE,EAC1BP,eAAe,EACfD,iBAAiB,EACjBE,aAAa,EACbH,eAAe,EACfD,WAAW;IAAA;EAAA;;;2EAUFS,qBAAqB;IAAAE,YAAA,GAN9BL,yBAAyB,EACzBC,yBAAyB,EACzBC,0BAA0B;IAAAI,OAAA,GAZ1Bb,YAAY,EAAAc,EAAA,CAAAR,eAAA,EAEZF,eAAe,EACfD,iBAAiB,EACjBE,aAAa,EACbH,eAAe,EACfD,WAAW;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}