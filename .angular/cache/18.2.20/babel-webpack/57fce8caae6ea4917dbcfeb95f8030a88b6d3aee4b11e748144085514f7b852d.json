{"ast": null, "code": "import { Http<PERSON>arams, HttpUrlEncodingCodec } from '@angular/common/http';\nimport { of } from 'rxjs';\nimport { first, tap } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@skywind-group/lib-swui\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../../services/entity.service\";\nimport * as i4 from \"@ngx-translate/core\";\nimport * as i5 from \"@angular/common/http\";\nimport * as i6 from \"../../directives/baIfAllowed/baIfAllowed.directive\";\nimport * as i7 from \"@angular/common\";\nconst _c0 = () => [\"live-chat\"];\nfunction LiveChatComponent_div_0_ng_container_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵlistener(\"click\", function LiveChatComponent_div_0_ng_container_1_div_1_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onClick());\n    });\n    i0.ɵɵelementStart(1, \"b\");\n    i0.ɵɵelement(2, \"i\", 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Live chat \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LiveChatComponent_div_0_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, LiveChatComponent_div_0_ng_container_1_div_1_Template, 4, 0, \"div\", 3);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.licence);\n  }\n}\nfunction LiveChatComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵtemplate(1, LiveChatComponent_div_0_ng_container_1_Template, 2, 1, \"ng-container\", 2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ifAllowed\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nclass UrlEncoder extends HttpUrlEncodingCodec {\n  encodeValue(value) {\n    return encodeURIComponent(value);\n  }\n}\nexport class LiveChatComponent {\n  constructor(authService, router, entityService, translateService, http) {\n    this.authService = authService;\n    this.router = router;\n    this.entityService = entityService;\n    this.translateService = translateService;\n    this.http = http;\n    this.isAuthorized = false;\n  }\n  ngOnInit() {\n    this.http.get('/api/config').subscribe(value => this.licence = value.liveChatLicence);\n    this.isAuthorized = this.authService.isLogged();\n  }\n  onClick() {\n    this.getBrief().subscribe(brief => this.open(brief), () => this.open());\n  }\n  open(brief) {\n    const username = this.authService.username || '';\n    // const { username = '' } = profile;\n    const {\n      key = '',\n      name = ''\n    } = brief || {};\n    const query = new HttpParams({\n      encoder: new UrlEncoder()\n    }).set('name', username).set('groups', '0').set('lang', this.translateService.currentLang).set('params', new HttpParams({\n      encoder: new UrlEncoder()\n    }).set('url', this.router.url).set('brief.key', key).set('brief.name', name).set('username', username).toString()).toString();\n    const url = `https://secure.livechatinc.com/licence/${this.licence}/open_chat.cgi?${query}`;\n    window.open(url, `LiveChat_${username}`, 'width=530,height=520,resizable=yes,scrollbars=no');\n  }\n  getBrief() {\n    if (!this.authService.isLogged()) {\n      return of(null);\n    }\n    if (this.brief) {\n      return of(this.brief);\n    } else {\n      return this.entityService.getBrief().pipe(first(), tap(brief => this.brief = brief));\n    }\n  }\n  static {\n    this.ɵfac = function LiveChatComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LiveChatComponent)(i0.ɵɵdirectiveInject(i1.SwHubAuthService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.EntityService), i0.ɵɵdirectiveInject(i4.TranslateService), i0.ɵɵdirectiveInject(i5.HttpClient));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LiveChatComponent,\n      selectors: [[\"live-chat\"]],\n      decls: 1,\n      vars: 1,\n      consts: [[\"class\", \"no-print\", 4, \"ngIf\"], [1, \"no-print\"], [4, \"ifAllowed\"], [\"type\", \"button\", \"class\", \"btn btn-labeled button-chat mr-20\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"btn\", \"btn-labeled\", \"button-chat\", \"mr-20\", 3, \"click\"], [1, \"icon-bubbles9\"]],\n      template: function LiveChatComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, LiveChatComponent_div_0_Template, 2, 2, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.isAuthorized);\n        }\n      },\n      dependencies: [i6.BaIfAllowedDirective, i7.NgIf],\n      styles: [\".button-chat[_ngcontent-%COMP%] {\\n  position: fixed;\\n  right: 10px;\\n  bottom: 10px;\\n  color: #fff;\\n  background-color: #FF7043;\\n  opacity: 0.95;\\n  z-index: 99;\\n}\\n.button-chat[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #fff;\\n}\\n.button-chat[_ngcontent-%COMP%]:hover, .button-chat[_ngcontent-%COMP%]:active, .button-chat[_ngcontent-%COMP%]:focus {\\n  color: #fff;\\n  opacity: 1;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY29tbW9uL2NvbXBvbmVudHMvbGl2ZWNoYXQvbGl2ZS1jaGF0LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxlQUFBO0VBQ0EsV0FBQTtFQUNBLFlBQUE7RUFDQSxXQUFBO0VBQ0EseUJBQUE7RUFDQSxhQUFBO0VBQ0EsV0FBQTtBQUNGO0FBQ0U7RUFDRSxXQUFBO0FBQ0o7QUFFRTtFQUdFLFdBQUE7RUFDQSxVQUFBO0FBRkoiLCJzb3VyY2VzQ29udGVudCI6WyIuYnV0dG9uLWNoYXQge1xuICBwb3NpdGlvbjogZml4ZWQ7XG4gIHJpZ2h0OiAxMHB4O1xuICBib3R0b206IDEwcHg7XG4gIGNvbG9yOiAjZmZmO1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjRkY3MDQzO1xuICBvcGFjaXR5OiAwLjk1O1xuICB6LWluZGV4OiA5OTtcblxuICBpIHtcbiAgICBjb2xvcjogI2ZmZjtcbiAgfVxuXG4gICY6aG92ZXIsXG4gICY6YWN0aXZlLFxuICAmOmZvY3VzIHtcbiAgICBjb2xvcjogI2ZmZjtcbiAgICBvcGFjaXR5OiAxO1xuICB9XG59XG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "HttpUrlEncodingCodec", "of", "first", "tap", "i0", "ɵɵelementStart", "ɵɵlistener", "LiveChatComponent_div_0_ng_container_1_div_1_Template_div_click_0_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onClick", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵelementContainerStart", "ɵɵtemplate", "LiveChatComponent_div_0_ng_container_1_div_1_Template", "ɵɵadvance", "ɵɵproperty", "licence", "LiveChatComponent_div_0_ng_container_1_Template", "ɵɵpureFunction0", "_c0", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "encodeValue", "value", "encodeURIComponent", "LiveChatComponent", "constructor", "authService", "router", "entityService", "translateService", "http", "isAuthorized", "ngOnInit", "get", "subscribe", "liveChatLicence", "isLogged", "getBrief", "brief", "open", "username", "key", "name", "query", "encoder", "set", "currentLang", "url", "toString", "window", "pipe", "ɵɵdirectiveInject", "i1", "SwHubAuthService", "i2", "Router", "i3", "EntityService", "i4", "TranslateService", "i5", "HttpClient", "selectors", "decls", "vars", "consts", "template", "LiveChatComponent_Template", "rf", "ctx", "LiveChatComponent_div_0_Template"], "sources": ["/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/common/components/livechat/live-chat.component.ts", "/Users/<USER>/Projects/sw-ubo-hub-casino/src/app/common/components/livechat/live-chat.html"], "sourcesContent": ["import { HttpClient, HttpParams, HttpUrlEncodingCodec } from '@angular/common/http';\nimport { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { TranslateService } from '@ngx-translate/core';\nimport { Observable, of } from 'rxjs';\nimport { first, tap } from 'rxjs/operators';\n\n// import { AuthService } from '../../auth/auth.service';\nimport { SwHubAuthService } from '@skywind-group/lib-swui';\nimport { EntityService } from '../../services/entity.service';\nimport { Entity } from '../../typings';\nimport { ServerConfig } from '../../typings/server-config';\n\nclass UrlEncoder extends HttpUrlEncodingCodec {\n  encodeValue(value: string): string {\n    return encodeURIComponent(value);\n  }\n}\n\n@Component({\n  selector: 'live-chat',\n  styleUrls: ['./live-chat.scss'],\n  templateUrl: 'live-chat.html'\n})\nexport class LiveChatComponent implements OnInit {\n  public licence: string;\n  public isAuthorized: Boolean = false;\n  private brief: Entity | null;\n\n  constructor(\n    private authService: SwHubAuthService,\n    private router: Router,\n    private entityService: EntityService<Entity>,\n    private translateService: TranslateService,\n    private http: HttpClient,\n  ) {\n  }\n\n  ngOnInit(): void {\n    this.http.get<ServerConfig>('/api/config').subscribe(value => this.licence = value.liveChatLicence);\n    this.isAuthorized = this.authService.isLogged();\n  }\n\n  public onClick() {\n    this.getBrief().subscribe(brief => this.open(brief), () => this.open());\n  }\n\n  private open(brief?: Entity | null) {\n    const username = this.authService.username || '';\n    // const { username = '' } = profile;\n    const { key = '', name = '' } = brief || {};\n\n    const query = new HttpParams({ encoder: new UrlEncoder() })\n      .set('name', username)\n      .set('groups', '0')\n      .set('lang', this.translateService.currentLang)\n      .set('params', new HttpParams({ encoder: new UrlEncoder() })\n        .set('url', this.router.url)\n        .set('brief.key', key)\n        .set('brief.name', name)\n        .set('username', username)\n        .toString())\n      .toString();\n\n    const url = `https://secure.livechatinc.com/licence/${this.licence}/open_chat.cgi?${query}`;\n    window.open(url, `LiveChat_${username}`, 'width=530,height=520,resizable=yes,scrollbars=no');\n  }\n\n  private getBrief(): Observable<Entity | null> {\n    if (!this.authService.isLogged()) {\n      return of(null);\n    }\n    if (this.brief) {\n      return of(this.brief);\n    } else {\n      return this.entityService.getBrief().pipe(\n        first(),\n        tap(brief => this.brief = brief)\n      );\n    }\n  }\n}\n", "<div class=\"no-print\" *ngIf=\"isAuthorized\">\n  <ng-container *ifAllowed=\"['live-chat']\">\n    <div *ngIf=\"licence\" type=\"button\" class=\"btn btn-labeled button-chat mr-20\" (click)=\"onClick()\">\n      <b><i class=\"icon-bubbles9\"></i></b>\n      Live chat\n    </div>\n  </ng-container>\n</div>\n"], "mappings": "AAAA,SAAqBA,UAAU,EAAEC,oBAAoB,QAAQ,sBAAsB;AAInF,SAAqBC,EAAE,QAAQ,MAAM;AACrC,SAASC,KAAK,EAAEC,GAAG,QAAQ,gBAAgB;;;;;;;;;;;;;ICHvCC,EAAA,CAAAC,cAAA,aAAiG;IAApBD,EAAA,CAAAE,UAAA,mBAAAC,2EAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,OAAA,EAAS;IAAA,EAAC;IAC9FT,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAU,SAAA,WAA6B;IAAAV,EAAA,CAAAW,YAAA,EAAI;IACpCX,EAAA,CAAAY,MAAA,kBACF;IAAAZ,EAAA,CAAAW,YAAA,EAAM;;;;;IAJRX,EAAA,CAAAa,uBAAA,GAAyC;IACvCb,EAAA,CAAAc,UAAA,IAAAC,qDAAA,iBAAiG;;;;;IAA3Ff,EAAA,CAAAgB,SAAA,EAAa;IAAbhB,EAAA,CAAAiB,UAAA,SAAAX,MAAA,CAAAY,OAAA,CAAa;;;;;IAFvBlB,EAAA,CAAAC,cAAA,aAA2C;IACzCD,EAAA,CAAAc,UAAA,IAAAK,+CAAA,0BAAyC;IAM3CnB,EAAA,CAAAW,YAAA,EAAM;;;IANWX,EAAA,CAAAgB,SAAA,EAAwB;IAAxBhB,EAAA,CAAAiB,UAAA,cAAAjB,EAAA,CAAAoB,eAAA,IAAAC,GAAA,EAAwB;;;ADYzC,MAAMC,UAAW,SAAQ1B,oBAAoB;EAC3C2B,WAAWA,CAACC,KAAa;IACvB,OAAOC,kBAAkB,CAACD,KAAK,CAAC;EAClC;;AAQF,OAAM,MAAOE,iBAAiB;EAK5BC,YACUC,WAA6B,EAC7BC,MAAc,EACdC,aAAoC,EACpCC,gBAAkC,EAClCC,IAAgB;IAJhB,KAAAJ,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,IAAI,GAAJA,IAAI;IARP,KAAAC,YAAY,GAAY,KAAK;EAUpC;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACF,IAAI,CAACG,GAAG,CAAe,aAAa,CAAC,CAACC,SAAS,CAACZ,KAAK,IAAI,IAAI,CAACN,OAAO,GAAGM,KAAK,CAACa,eAAe,CAAC;IACnG,IAAI,CAACJ,YAAY,GAAG,IAAI,CAACL,WAAW,CAACU,QAAQ,EAAE;EACjD;EAEO7B,OAAOA,CAAA;IACZ,IAAI,CAAC8B,QAAQ,EAAE,CAACH,SAAS,CAACI,KAAK,IAAI,IAAI,CAACC,IAAI,CAACD,KAAK,CAAC,EAAE,MAAM,IAAI,CAACC,IAAI,EAAE,CAAC;EACzE;EAEQA,IAAIA,CAACD,KAAqB;IAChC,MAAME,QAAQ,GAAG,IAAI,CAACd,WAAW,CAACc,QAAQ,IAAI,EAAE;IAChD;IACA,MAAM;MAAEC,GAAG,GAAG,EAAE;MAAEC,IAAI,GAAG;IAAE,CAAE,GAAGJ,KAAK,IAAI,EAAE;IAE3C,MAAMK,KAAK,GAAG,IAAIlD,UAAU,CAAC;MAAEmD,OAAO,EAAE,IAAIxB,UAAU;IAAE,CAAE,CAAC,CACxDyB,GAAG,CAAC,MAAM,EAAEL,QAAQ,CAAC,CACrBK,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,CAClBA,GAAG,CAAC,MAAM,EAAE,IAAI,CAAChB,gBAAgB,CAACiB,WAAW,CAAC,CAC9CD,GAAG,CAAC,QAAQ,EAAE,IAAIpD,UAAU,CAAC;MAAEmD,OAAO,EAAE,IAAIxB,UAAU;IAAE,CAAE,CAAC,CACzDyB,GAAG,CAAC,KAAK,EAAE,IAAI,CAAClB,MAAM,CAACoB,GAAG,CAAC,CAC3BF,GAAG,CAAC,WAAW,EAAEJ,GAAG,CAAC,CACrBI,GAAG,CAAC,YAAY,EAAEH,IAAI,CAAC,CACvBG,GAAG,CAAC,UAAU,EAAEL,QAAQ,CAAC,CACzBQ,QAAQ,EAAE,CAAC,CACbA,QAAQ,EAAE;IAEb,MAAMD,GAAG,GAAG,0CAA0C,IAAI,CAAC/B,OAAO,kBAAkB2B,KAAK,EAAE;IAC3FM,MAAM,CAACV,IAAI,CAACQ,GAAG,EAAE,YAAYP,QAAQ,EAAE,EAAE,kDAAkD,CAAC;EAC9F;EAEQH,QAAQA,CAAA;IACd,IAAI,CAAC,IAAI,CAACX,WAAW,CAACU,QAAQ,EAAE,EAAE;MAChC,OAAOzC,EAAE,CAAC,IAAI,CAAC;IACjB;IACA,IAAI,IAAI,CAAC2C,KAAK,EAAE;MACd,OAAO3C,EAAE,CAAC,IAAI,CAAC2C,KAAK,CAAC;IACvB,CAAC,MAAM;MACL,OAAO,IAAI,CAACV,aAAa,CAACS,QAAQ,EAAE,CAACa,IAAI,CACvCtD,KAAK,EAAE,EACPC,GAAG,CAACyC,KAAK,IAAI,IAAI,CAACA,KAAK,GAAGA,KAAK,CAAC,CACjC;IACH;EACF;;;uCAxDWd,iBAAiB,EAAA1B,EAAA,CAAAqD,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAvD,EAAA,CAAAqD,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAzD,EAAA,CAAAqD,iBAAA,CAAAK,EAAA,CAAAC,aAAA,GAAA3D,EAAA,CAAAqD,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAA7D,EAAA,CAAAqD,iBAAA,CAAAS,EAAA,CAAAC,UAAA;IAAA;EAAA;;;YAAjBrC,iBAAiB;MAAAsC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCxB9BtE,EAAA,CAAAc,UAAA,IAAA0D,gCAAA,iBAA2C;;;UAApBxE,EAAA,CAAAiB,UAAA,SAAAsD,GAAA,CAAAtC,YAAA,CAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}